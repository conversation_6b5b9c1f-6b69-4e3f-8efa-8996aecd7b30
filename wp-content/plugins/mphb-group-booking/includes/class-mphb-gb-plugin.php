<?php
/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since      1.0.0
 * @package    MPHB_Group_Booking
 * @subpackage MPHB_Group_Booking/includes
 * <AUTHOR> <<EMAIL>>
 */

if (!defined('WPINC')) {
	die;
}

class MPHB_GB_Plugin {

	/**
	 * The loader that's responsible for maintaining and registering all hooks that power
	 * the plugin.
	 *
	 * @since    1.0.0
	 * @access   protected
	 * @var      MPHB_GB_Loader    $loader    Maintains and registers all hooks for the plugin.
	 */
	protected $loader;

	/**
	 * The unique identifier of this plugin.
	 *
	 * @since    1.0.0
	 * @access   protected
	 * @var      string    $plugin_name    The string used to uniquely identify this plugin.
	 */
	protected $plugin_name;

	/**
	 * The current version of the plugin.
	 *
	 * @since    1.0.0
	 * @access   protected
	 * @var      string    $version    The current version of the plugin.
	 */
	protected $version;

	/**
	 * Define the core functionality of the plugin.
	 *
	 * Set the plugin name and the plugin version that can be used throughout the plugin.
	 * Load the dependencies, define the locale, and set the hooks for the admin area and
	 * the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function __construct() {
		$this->version = MPHB_GB_VERSION;
		$this->plugin_name = 'mphb-group-booking';

		$this->load_dependencies();
		$this->set_locale();
		$this->define_admin_hooks();
		$this->define_public_hooks();
		$this->define_rule_hooks();
		$this->initialize_patches();
	}

	/**
	 * Load the required dependencies for this plugin.
	 *
	 * Include the following files that make up the plugin:
	 *
	 * - MPHB_GB_Loader. Orchestrates the hooks of the plugin.
	 * - MPHB_GB_i18n. Defines internationalization functionality.
	 * - MPHB_GB_Admin. Defines all hooks for the admin area.
	 * - MPHB_GB_Public. Defines all hooks for the public side of the site.
	 * - MPHB_GB_Rule. Defines the group booking rule class.
	 *
	 * Create an instance of the loader which will be used to register the hooks
	 * with WordPress.
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function load_dependencies() {
		/**
		 * The class responsible for orchestrating the actions and filters of the
		 * core plugin.
		 */
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/class-mphb-gb-loader.php';

		/**
		 * The class responsible for defining internationalization functionality
		 * of the plugin.
		 */
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/class-mphb-gb-i18n.php';

		/**
		 * The class responsible for defining all actions that occur in the admin area.
		 */
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/admin/class-mphb-gb-admin.php';

		/**
		 * The class responsible for defining all actions that occur in the public-facing
		 * side of the site.
		 */
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/public/class-mphb-gb-public.php';

		/**
		 * The class responsible for defining the group booking rule.
		 */
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/rules/class-mphb-gb-rule.php';
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/rules/class-mphb-gb-rule-factory.php';
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/rules/class-mphb-gb-validator.php';

		/**
		 * The classes responsible for patching core MPHB functionality.
		 */
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/patches/class-mphb-gb-search-parameters-storage-patch.php';
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/patches/class-mphb-gb-booking-rules-data-patch.php';
		require_once MPHB_GB_PLUGIN_DIR_PATH . 'includes/patches/class-mphb-gb-room-persistence-patch.php';

		$this->loader = new MPHB_GB_Loader();
	}

	/**
	 * Define the locale for this plugin for internationalization.
	 *
	 * Uses the MPHB_GB_i18n class in order to set the domain and to register the hook
	 * with WordPress.
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function set_locale() {
		$plugin_i18n = new MPHB_GB_i18n();
		$this->loader->add_action('plugins_loaded', $plugin_i18n, 'load_plugin_textdomain');
	}

	/**
	 * Register all of the hooks related to the admin area functionality
	 * of the plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function define_admin_hooks() {
		$plugin_admin = new MPHB_GB_Admin($this->get_plugin_name(), $this->get_version());

		$this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_styles');
		$this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts');

		// Initialize meta boxes
		$this->loader->add_action('admin_init', $plugin_admin, 'init_meta_boxes');

		// Initialize admin menu
		$this->loader->add_action('admin_menu', $plugin_admin->admin_menu, 'add_admin_menu', 20);
	}

	/**
	 * Register all of the hooks related to the public-facing functionality
	 * of the plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function define_public_hooks() {
		$plugin_public = new MPHB_GB_Public($this->get_plugin_name(), $this->get_version());

		$this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_styles');
		$this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_scripts');

		// Initialize booking form modifications
		$this->loader->add_action('init', $plugin_public, 'init_booking_form');

		// Initialize search form modifications
		$this->loader->add_action('init', $plugin_public, 'init_search_form');
	}

	/**
	 * Register all of the hooks related to the rule functionality
	 * of the plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function define_rule_hooks() {
		// Log that we're registering hooks
		$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Registering hooks in define_rule_hooks\n";
		file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

		// Register custom rule type
		$this->loader->add_filter('mphb_custom_rule_types', $this, 'register_group_rule_type');

		// Filter room availability based on group code for booking
		$this->loader->add_filter('mphb_check_room_availability_for_booking', $this, 'check_room_availability_for_group', 10, 4);

		// Add filter for search results
		$this->loader->add_filter('mphb_search_available_rooms', $this, 'add_group_code_to_search_args', 10, 1);

		// Add filter for checking if rooms exist
		$this->loader->add_filter('mphb_is_rooms_exist_query_atts', $this, 'add_group_code_to_search_args', 10, 1);
		$this->loader->add_filter('mphb_is_rooms_free_query_atts', $this, 'add_group_code_to_search_args', 10, 1);

		// Add a test hook to verify our plugin is working
		$this->loader->add_action('wp_head', $this, 'test_hook_working');

		// Preserve group booking rules when admin block rules are saved
		$this->loader->add_filter('pre_update_option_mphb_booking_rules_custom', $this, 'preserve_group_booking_rules', 10, 2);

		$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Finished registering hooks\n";
		file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);
	}

	/**
	 * Initialize patches for core MPHB functionality
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function initialize_patches() {
		// Debug log
		error_log('MPHB_GB: Initializing patches');

		// Initialize search parameters storage patch
		$search_parameters_patch = new MPHB_GB_Search_Parameters_Storage_Patch();
		$search_parameters_patch->init();

		// Initialize booking rules data patch
		$booking_rules_patch = new MPHB_GB_Booking_Rules_Data_Patch();
		$booking_rules_patch->init();

		// Initialize room persistence patch
		$room_persistence_patch = new MPHB_GB_Room_Persistence_Patch();
		$room_persistence_patch->init();

		// Debug log
		error_log('MPHB_GB: Patches initialized');
	}

	/**
	 * Register group booking rule type
	 *
	 * @since    1.0.0
	 * @param    array    $rule_types    Array of rule types.
	 * @return   array    Modified array of rule types.
	 */
	public function register_group_rule_type($rule_types) {
		if (!is_array($rule_types)) {
			$rule_types = array();
		}
		$rule_types['group'] = __('Group Booking', 'mphb-group-booking');
		return $rule_types;
	}

	/**
	 * Check room availability based on group code
	 *
	 * @since    1.0.0
	 * @param    bool      $is_available    Current availability status.
	 * @param    int       $room_id         Room ID.
	 * @param    DateTime  $check_in        Check-in date.
	 * @param    DateTime  $check_out       Check-out date.
	 * @return   bool      Updated availability status.
	 */
	public function check_room_availability_for_group($is_available, $room_id, $check_in, $check_out) {
		// Get group code from request
		$group_code = isset($_REQUEST['mphb_group_code']) ? sanitize_text_field($_REQUEST['mphb_group_code']) : '';

		// Debug log
		error_log('MPHB_GB: check_room_availability_for_group called');
		error_log('MPHB_GB: room_id: ' . $room_id);
		error_log('MPHB_GB: group_code: ' . $group_code);
		error_log('MPHB_GB: initial is_available: ' . ($is_available ? 'true' : 'false'));

		if (empty($group_code)) {
			error_log('MPHB_GB: No group code provided, returning original availability: ' . ($is_available ? 'true' : 'false'));
			return $is_available;
		}

		// Make sure we have valid DateTime objects
		if (!($check_in instanceof DateTime) || !($check_out instanceof DateTime)) {
			error_log('MPHB_GB: Invalid DateTime objects, returning original availability: ' . ($is_available ? 'true' : 'false'));
			return $is_available;
		}

		error_log('MPHB_GB: check_in: ' . $check_in->format('Y-m-d') . ', check_out: ' . $check_out->format('Y-m-d'));

		// Create validator instance
		$validator = new MPHB_GB_Validator();

		// Check if room is available for this group
		$group_availability = $validator->is_room_available_for_group($room_id, $group_code, $check_in, $check_out);

		error_log('MPHB_GB: group_availability result: ' . ($group_availability ? 'true' : 'false'));

		return $group_availability;
	}

	/**
	 * Add group code to search available rooms filter
	 *
	 * @since    1.0.0
	 * @param    array    $args    Search arguments.
	 * @return   array    Updated search arguments.
	 */
	// Static flag to prevent recursive calls
	private static $processing_group_search = false;

	public function add_group_code_to_search_args($args) {
		// Prevent recursive calls
		if (self::$processing_group_search) {
			return $args;
		}

		$group_code = isset($_REQUEST['mphb_group_code']) ? sanitize_text_field($_REQUEST['mphb_group_code']) : '';

		// Write to our custom log file for easier debugging
		$log_message = date('Y-m-d H:i:s') . " MPHB_GB: add_group_code_to_search_args called\n";
		$log_message .= date('Y-m-d H:i:s') . " MPHB_GB: group_code: " . $group_code . "\n";
		$log_message .= date('Y-m-d H:i:s') . " MPHB_GB: filter: " . current_filter() . "\n";
		$log_message .= date('Y-m-d H:i:s') . " MPHB_GB: args: " . print_r($args, true) . "\n";
		file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

		error_log('MPHB_GB: add_group_code_to_search_args called');
		error_log('MPHB_GB: group_code: ' . $group_code);
		error_log('MPHB_GB: filter: ' . current_filter());
		error_log('MPHB_GB: args: ' . print_r($args, true));

		if (!empty($group_code)) {
			$args['group_code'] = $group_code;
			error_log('MPHB_GB: Added group code to search args');
		}

		// If we have search dates and a group code, modify the availability check
		if (isset($args['from_date']) && isset($args['to_date']) && !empty($group_code)) {
			// Set flag to prevent recursive calls
			self::$processing_group_search = true;

			$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Processing group code: " . $group_code . "\n";
			file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

			// Get room type ID if available
			$room_type_id = isset($args['room_type_id']) ? $args['room_type_id'] : 0;
			$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Room type ID: " . $room_type_id . "\n";
			file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

			// Process both specific room types and general searches (room_type_id = 0)
			if (!empty($room_type_id)) {
				// Handle specific room type search
				try {
					// Get all rooms for this room type
					$room_ids = MPHB()->getRoomPersistence()->findAllIdsByType($room_type_id);
					$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Found " . count($room_ids) . " rooms for room type " . $room_type_id . "\n";
					file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

					// Check which rooms are available for the group
					$available_room_ids = array();
					foreach ($room_ids as $room_id) {
						// Use direct method to check availability (avoid recursive calls)
						$is_available = $this->check_room_availability_for_group_direct($room_id, $group_code, $args['from_date'], $args['to_date']);
						if ($is_available) {
							$available_room_ids[] = $room_id;
						}
					}

					$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Found " . count($available_room_ids) . " available rooms for group: " . implode(',', $available_room_ids) . "\n";
					file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

					// If we're searching for locked rooms (which is what the search results shortcode does),
					// we want to exclude the available rooms so they don't appear as locked
					if (isset($args['availability']) && $args['availability'] === 'locked') {
						$args['exclude_rooms'] = isset($args['exclude_rooms']) ? array_merge($args['exclude_rooms'], $available_room_ids) : $available_room_ids;
						$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Excluded " . count($available_room_ids) . " available rooms from locked search\n";
						file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);
					}

				} catch (Exception $e) {
					$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Error processing group code: " . $e->getMessage() . "\n";
					file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);
				}
			} else {
				// Handle general search across all room types (room_type_id = 0)
				$log_message = date('Y-m-d H:i:s') . " MPHB_GB: General search across all room types\n";
				file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

				try {
					// Get all room type IDs
					$all_room_type_ids = mphb_get_room_type_ids('original');
					$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Found " . count($all_room_type_ids) . " room types: " . implode(',', $all_room_type_ids) . "\n";
					file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

					$all_available_room_ids = array();

					// Check each room type
					foreach ($all_room_type_ids as $rt_id) {
						$room_ids = MPHB()->getRoomPersistence()->findAllIdsByType($rt_id);
						$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Checking " . count($room_ids) . " rooms for room type " . $rt_id . "\n";
						file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

						// Check each room in this room type
						foreach ($room_ids as $room_id) {
							$is_available = $this->check_room_availability_for_group_direct($room_id, $group_code, $args['from_date'], $args['to_date']);
							if ($is_available) {
								$all_available_room_ids[] = $room_id;
							}
						}
					}

					$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Found " . count($all_available_room_ids) . " total available rooms for group: " . implode(',', $all_available_room_ids) . "\n";
					file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);

					// If we're searching for locked rooms, exclude the available rooms
					if (isset($args['availability']) && $args['availability'] === 'locked') {
						$args['exclude_rooms'] = isset($args['exclude_rooms']) ? array_merge($args['exclude_rooms'], $all_available_room_ids) : $all_available_room_ids;
						$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Excluded " . count($all_available_room_ids) . " available rooms from locked search\n";
						file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);
					}

				} catch (Exception $e) {
					$log_message = date('Y-m-d H:i:s') . " MPHB_GB: Error processing all room types: " . $e->getMessage() . "\n";
					file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);
				}
			}
		}

		// Reset flag
		self::$processing_group_search = false;

		return $args;
	}

	/**
	 * Check room availability for group directly without using validator (to avoid recursion)
	 *
	 * @since    1.0.0
	 * @param    int       $room_id      Room ID.
	 * @param    string    $group_code   Group code.
	 * @param    DateTime  $check_in     Check-in date.
	 * @param    DateTime  $check_out    Check-out date.
	 * @return   bool      Is room available.
	 */
	private function check_room_availability_for_group_direct($room_id, $group_code, $check_in, $check_out) {
		// Get room type ID
		$room_type_id = get_post_meta($room_id, 'mphb_room_type_id', true);

		// Format dates for query
		$date_from = $check_in->format('Y-m-d');
		$date_to = $check_out->format('Y-m-d');

		// Set up the post type
		$post_type = 'mphb_custom_rule'; // Default post type

		// Use MPHB post type if available
		if (function_exists('MPHB') && MPHB()->postTypes() && method_exists(MPHB()->postTypes(), 'customRule')) {
			$post_type = MPHB()->postTypes()->customRule()->getPostType();
		}

		// Query for custom rules that might apply
		$args = array(
			'post_type'      => $post_type,
			'posts_per_page' => -1,
			'meta_query'     => array(
				'relation' => 'AND',
			),
		);

		// Add room/room type criteria
		$room_criteria = array('relation' => 'OR');

		// Add room ID criteria
		$room_criteria[] = array(
			'key'     => 'mphb_room_id',
			'value'   => $room_id,
			'compare' => '=',
		);

		// Add room type criteria if available
		if ($room_type_id) {
			$room_criteria[] = array(
				'relation' => 'AND',
				array(
					'key'     => 'mphb_room_type_id',
					'value'   => $room_type_id,
					'compare' => '=',
				),
				array(
					'key'     => 'mphb_room_id',
					'value'   => '',
					'compare' => '=',
				),
			);
		}

		$args['meta_query'][] = $room_criteria;

		// Add date criteria
		$date_criteria = array('relation' => 'OR');

		// Date range overlaps with booking
		$date_criteria[] = array(
			'relation' => 'AND',
			array(
				'key'     => 'mphb_date_from',
				'value'   => $date_to,
				'compare' => '<=',
				'type'    => 'DATE',
			),
			array(
				'key'     => 'mphb_date_to',
				'value'   => $date_from,
				'compare' => '>=',
				'type'    => 'DATE',
			),
		);

		// Indefinite end date
		$date_criteria[] = array(
			'relation' => 'AND',
			array(
				'key'     => 'mphb_date_from',
				'value'   => $date_from,
				'compare' => '<=',
				'type'    => 'DATE',
			),
			array(
				'key'     => 'mphb_date_to',
				'value'   => '',
				'compare' => '=',
			),
		);

		$args['meta_query'][] = $date_criteria;

		// Add group code criteria
		$args['meta_query'][] = array(
			'key'     => '_mphb_group_code',
			'compare' => 'EXISTS',
		);

		$query = new WP_Query($args);

		if ($query->have_posts()) {
			// Check if any rule matches the group code
			foreach ($query->posts as $post) {
				$rule_group_code = get_post_meta($post->ID, '_mphb_group_code', true);

				// If room is blocked by a rule with matching group code, it's available
				if ($rule_group_code === $group_code) {
					return true;
				}

				// If room is blocked by a different group code, it's not available
				if (!empty($rule_group_code)) {
					return false;
				}
			}

			// If we get here, there are group rules but none match the provided code
			return false;
		}

		// If no group rules apply, the room should be available for the group code
		return true;
	}

	/**
	 * Test method to verify our plugin hooks are working
	 *
	 * @since    1.0.0
	 */
	public function test_hook_working() {
		$log_message = date('Y-m-d H:i:s') . " MPHB_GB: test_hook_working called - plugin is active\n";
		file_put_contents(ABSPATH . 'wp-content/mphb-gb-debug.log', $log_message, FILE_APPEND | LOCK_EX);
	}

	/**
	 * Preserve group booking rules when admin block rules are saved
	 *
	 * @since    1.3.2
	 * @param    mixed     $new_value    New value for the option.
	 * @param    mixed     $old_value    Old value for the option.
	 * @return   mixed     Modified new value.
	 */
	public function preserve_group_booking_rules($new_value, $old_value) {
		// Debug log the values
		error_log('MPHB Group Booking: preserve_group_booking_rules called');
		error_log('MPHB Group Booking: New value count: ' . (is_array($new_value) ? count($new_value) : 'not an array'));
		error_log('MPHB Group Booking: Old value count: ' . (is_array($old_value) ? count($old_value) : 'not an array'));

		// If there's no old value or it's not an array, return the new value
		if (empty($old_value) || !is_array($old_value)) {
			error_log('MPHB Group Booking: No old value or not an array, returning new value');
			return $new_value;
		}

		// If there's no new value or it's not an array, return the new value
		if (empty($new_value) || !is_array($new_value)) {
			error_log('MPHB Group Booking: No new value or not an array, returning new value');
			return $new_value;
		}

		// Find all group booking rules in the old value
		$group_booking_rules = array();
		foreach ($old_value as $rule) {
			if (isset($rule['_mphb_group_code']) && !empty($rule['_mphb_group_code'])) {
				$group_booking_rules[] = $rule;
				error_log('MPHB Group Booking: Found group booking rule with code: ' . $rule['_mphb_group_code']);
			}
		}

		// If there are no group booking rules, return the new value
		if (empty($group_booking_rules)) {
			error_log('MPHB Group Booking: No group booking rules found, returning new value');
			return $new_value;
		}

		// Check if this is a group booking operation (new or update)
		$is_group_booking_operation = false;
		$new_group_codes = array();
		$new_group_rules = array();

		// Get all group codes and rules from the new value
		foreach ($new_value as $rule) {
			if (isset($rule['_mphb_group_code']) && !empty($rule['_mphb_group_code'])) {
				$new_group_codes[] = $rule['_mphb_group_code'];
				$new_group_rules[] = $rule;
				error_log('MPHB Group Booking: Found group code in new value: ' . $rule['_mphb_group_code']);
			}
		}

		// If there are group codes in the new value, this is a group booking operation
		if (!empty($new_group_codes)) {
			$is_group_booking_operation = true;
			error_log('MPHB Group Booking: Detected group booking operation');
		}

		// If this is a group booking operation, return the new value as is
		if ($is_group_booking_operation) {
			error_log('MPHB Group Booking: Group booking operation detected, returning new value as is');
			return $new_value;
		}

		// Otherwise, this is an admin block rule update, so preserve group booking rules
		$admin_block_rules = array();
		foreach ($new_value as $rule) {
			if (!isset($rule['_mphb_group_code']) || empty($rule['_mphb_group_code'])) {
				$admin_block_rules[] = $rule;
			} else {
				error_log('MPHB Group Booking: Removing existing group booking rule with code: ' . $rule['_mphb_group_code']);
			}
		}

		// Merge the admin block rules with the group booking rules
		$merged_rules = array_merge($admin_block_rules, $group_booking_rules);

		error_log('MPHB Group Booking: Final merged rules count: ' . count($merged_rules));
		return $merged_rules;
	}

	/**
	 * Run the loader to execute all of the hooks with WordPress.
	 *
	 * @since    1.0.0
	 */
	public function run() {
		$this->loader->run();
	}

	/**
	 * The name of the plugin used to uniquely identify it within the context of
	 * WordPress and to define internationalization functionality.
	 *
	 * @since     1.0.0
	 * @return    string    The name of the plugin.
	 */
	public function get_plugin_name() {
		return $this->plugin_name;
	}

	/**
	 * The reference to the class that orchestrates the hooks with the plugin.
	 *
	 * @since     1.0.0
	 * @return    MPHB_GB_Loader    Orchestrates the hooks of the plugin.
	 */
	public function get_loader() {
		return $this->loader;
	}

	/**
	 * Retrieve the version number of the plugin.
	 *
	 * @since     1.0.0
	 * @return    string    The version number of the plugin.
	 */
	public function get_version() {
		return $this->version;
	}
}
