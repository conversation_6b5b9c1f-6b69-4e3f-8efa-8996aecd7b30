<?php
/**
 * The search form functionality of the plugin.
 *
 * @since      1.0.0
 * @package    MPHB_Group_Booking
 * @subpackage MPHB_Group_Booking/public
 * <AUTHOR> <<EMAIL>>
 */

if (!defined('WPINC')) {
	die;
}

class MPHB_GB_Search_Form {

	/**
	 * Initialize search form modifications
	 *
	 * @since    1.0.0
	 */
	public function init() {
		// Add group code field to search forms
		add_action('mphb_sc_search_form_before_submit_btn', array($this, 'add_group_code_field'));
		add_action('mphb_widget_search_form_before_submit_btn', array($this, 'add_group_code_field'));

		// Filter search parameters to include group code
		add_filter('mphb_search_parameters', array($this, 'add_group_code_to_search_parameters'));

		// Add group code to search available rooms filter
		add_filter('mphb_search_available_rooms', array($this, 'add_group_code_to_search_args'));

		// Save group code to search parameters
		add_action('mphb_save_search_parameters', array($this, 'save_group_code_to_search_parameters'));
	}

	/**
	 * Add group code field to search form
	 *
	 * @since    1.0.0
	 */
	public function add_group_code_field() {
		$group_code = isset($_REQUEST['mphb_group_code']) ? sanitize_text_field($_REQUEST['mphb_group_code']) : '';
		?>
		<p class="mphb-search-group-code-wrapper">
			<label for="mphb_group_code">
				<?php esc_html_e('Group Code (optional)', 'mphb-group-booking'); ?>
			</label>
			<br />
			<input type="text" id="mphb_group_code" name="mphb_group_code" value="<?php echo esc_attr($group_code); ?>" class="mphb-group-code">
			<span class="mphb-group-code-help">
				<?php esc_html_e('If you have a group code, enter it here to search for rooms within that group.', 'mphb-group-booking'); ?>
			</span>
		</p>
		<?php
	}

	/**
	 * Add group code to search parameters
	 *
	 * @since    1.0.0
	 * @param    array    $parameters    Search parameters.
	 * @return   array    Updated search parameters.
	 */
	public function add_group_code_to_search_parameters($parameters) {
		$group_code = isset($_REQUEST['mphb_group_code']) ? sanitize_text_field($_REQUEST['mphb_group_code']) : '';

		if (!empty($group_code)) {
			$parameters['mphb_group_code'] = $group_code;
		} elseif (isset($parameters['mphb_group_code'])) {
			// Keep existing group code if present
		}

		return $parameters;
	}

	/**
	 * Save group code to search parameters
	 *
	 * @since    1.0.0
	 * @param    array    $parameters    Search parameters to save.
	 * @return   array    Updated search parameters.
	 */
	public function save_group_code_to_search_parameters($parameters) {
		$group_code = isset($_REQUEST['mphb_group_code']) ? sanitize_text_field($_REQUEST['mphb_group_code']) : '';

		if (!empty($group_code)) {
			$parameters['mphb_group_code'] = $group_code;
		}

		return $parameters;
	}



	/**
	 * Add group code to search available rooms filter
	 *
	 * @since    1.0.0
	 * @param    array    $args    Search arguments.
	 * @return   array    Updated search arguments.
	 */
	public function add_group_code_to_search_args($args) {
		$group_code = isset($_REQUEST['mphb_group_code']) ? sanitize_text_field($_REQUEST['mphb_group_code']) : '';

		error_log('MPHB_GB_Search_Form: add_group_code_to_search_args called');
		error_log('MPHB_GB_Search_Form: group_code: ' . $group_code);

		if (!empty($group_code)) {
			$args['group_code'] = $group_code;
			error_log('MPHB_GB_Search_Form: Added group code to search args');

			// Log the search arguments
			error_log('MPHB_GB_Search_Form: Search args: ' . print_r($args, true));
		}

		return $args;
	}
}
