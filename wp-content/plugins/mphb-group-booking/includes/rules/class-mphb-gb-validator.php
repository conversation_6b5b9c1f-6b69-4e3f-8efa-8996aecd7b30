<?php
/**
 * Group Booking Validator class.
 *
 * @since      1.0.0
 * @package    MPHB_Group_Booking
 * @subpackage MPHB_Group_Booking/includes/rules
 * <AUTHOR> <<EMAIL>>
 */

if (!defined('WPINC')) {
	die;
}

class MPHB_GB_Validator {

	/**
	 * Validate if room is available for booking with group code
	 *
	 * @since    1.0.0
	 * @param    int       $room_id      Room ID.
	 * @param    string    $group_code   Group code.
	 * @param    DateTime  $check_in     Check-in date.
	 * @param    DateTime  $check_out    Check-out date.
	 * @return   bool      Is room available.
	 */
	public function is_room_available_for_group($room_id, $group_code, $check_in, $check_out) {
		// Debug log
		error_log('MPHB_GB: is_room_available_for_group called for room ' . $room_id);
		error_log('MPHB_GB: group_code: ' . $group_code);
		error_log('MPHB_GB: check_in: ' . $check_in->format('Y-m-d') . ', check_out: ' . $check_out->format('Y-m-d'));

		// Check if room is blocked by a group rule
		$rules = $this->get_group_rules_for_room($room_id, $check_in, $check_out);

		// If there are group rules for this room
		if (!empty($rules)) {
			error_log('MPHB_GB: Found ' . count($rules) . ' group rules for this room');

			// If no group code is provided, room is not available
			if (empty($group_code)) {
				error_log('MPHB_GB: No group code provided, room is not available');
				return false;
			}

			foreach ($rules as $index => $rule) {
				$rule_code = $rule->get_group_code();
				error_log('MPHB_GB: Checking rule ' . $index . ' with code: ' . $rule_code);

				// If room is blocked by a rule with matching group code, it's available
				if ($rule_code === $group_code) {
					error_log('MPHB_GB: Found matching group code, room is available');
					return true;
				}

				// If room is blocked by a different group code, it's not available
				if (!empty($rule_code)) {
					error_log('MPHB_GB: Room is blocked by a different group code: ' . $rule_code);
					return false;
				}
			}

			// If we get here, there are group rules but none match the provided code
			error_log('MPHB_GB: No matching group code found in rules, room is not available');
			return false;
		}

		// If no group rules apply and a group code is provided, the room should be available
		// This is the key change - if a valid group code is provided but there are no group rules,
		// we should still check standard availability
		if (!empty($group_code)) {
			error_log('MPHB_GB: No group rules apply but group code provided, checking standard availability');

			// Use the filter to check availability
			$is_available = apply_filters('mphb_check_room_availability', true, $room_id, $check_in, $check_out);

			error_log('MPHB_GB: Standard availability check result: ' . ($is_available ? 'true' : 'false'));
			return $is_available;
		}

		// If no group code is provided and no group rules apply, check standard availability
		error_log('MPHB_GB: No group rules apply and no group code provided, checking standard availability');

		// Use the filter to check availability
		$is_available = apply_filters('mphb_check_room_availability', true, $room_id, $check_in, $check_out);

		error_log('MPHB_GB: Standard availability check result: ' . ($is_available ? 'true' : 'false'));
		return $is_available;
	}

	/**
	 * Get group rules that apply to a room for given dates
	 *
	 * @since    1.0.0
	 * @param    int       $room_id      Room ID.
	 * @param    DateTime  $check_in     Check-in date.
	 * @param    DateTime  $check_out    Check-out date.
	 * @return   array     Array of MPHB_GB_Rule objects.
	 */
	public function get_group_rules_for_room($room_id, $check_in, $check_out) {
		// Debug log
		error_log('MPHB_GB: get_group_rules_for_room called for room ' . $room_id);
		error_log('MPHB_GB: check_in: ' . $check_in->format('Y-m-d') . ', check_out: ' . $check_out->format('Y-m-d'));
		$rules = array();

		// Get room type ID
		$room_type_id = get_post_meta($room_id, 'mphb_room_type_id', true);

		// Format dates for query
		$date_from = $check_in->format('Y-m-d');
		$date_to = $check_out->format('Y-m-d');

		// Set up the post type
		$post_type = 'mphb_custom_rule'; // Default post type

		// Use MPHB post type if available
		if (function_exists('MPHB') && MPHB()->postTypes() && method_exists(MPHB()->postTypes(), 'customRule')) {
			$post_type = MPHB()->postTypes()->customRule()->getPostType();
		}

		// Query for custom rules that might apply
		$args = array(
			'post_type'      => $post_type,
			'posts_per_page' => -1,
			'meta_query'     => array(
				'relation' => 'AND',
			),
		);

		// Add room/room type criteria
		$room_criteria = array('relation' => 'OR');

		// Add room ID criteria
		$room_criteria[] = array(
			'key'     => 'mphb_room_id',
			'value'   => $room_id,
			'compare' => '=',
		);

		// Add room type criteria if available
		if ($room_type_id) {
			$room_criteria[] = array(
				'relation' => 'AND',
				array(
					'key'     => 'mphb_room_type_id',
					'value'   => $room_type_id,
					'compare' => '=',
				),
				array(
					'key'     => 'mphb_room_id',
					'value'   => '',
					'compare' => '=',
				),
			);
		}

		$args['meta_query'][] = $room_criteria;

		// Add date criteria
		$date_criteria = array('relation' => 'OR');

		// Date range overlaps with booking
		$date_criteria[] = array(
			'relation' => 'AND',
			array(
				'key'     => 'mphb_date_from',
				'value'   => $date_to,
				'compare' => '<=',
				'type'    => 'DATE',
			),
			array(
				'key'     => 'mphb_date_to',
				'value'   => $date_from,
				'compare' => '>=',
				'type'    => 'DATE',
			),
		);

		// Indefinite end date
		$date_criteria[] = array(
			'relation' => 'AND',
			array(
				'key'     => 'mphb_date_from',
				'value'   => $date_from,
				'compare' => '<=',
				'type'    => 'DATE',
			),
			array(
				'key'     => 'mphb_date_to',
				'value'   => '',
				'compare' => '=',
			),
		);

		$args['meta_query'][] = $date_criteria;

		// Add group code criteria
		$args['meta_query'][] = array(
			'key'     => '_mphb_group_code',
			'compare' => 'EXISTS',
		);

		$query = new WP_Query($args);

		if ($query->have_posts()) {
			$factory = new MPHB_GB_Rule_Factory();

			foreach ($query->posts as $post) {
				$rule = $factory->create_from_post($post->ID);

				if ($rule) {
					$rules[] = $rule;
				}
			}
		}

		// Debug log
		error_log('MPHB_GB: Found ' . count($rules) . ' group rules for room ' . $room_id);
		foreach ($rules as $index => $rule) {
			error_log('MPHB_GB: Rule ' . $index . ' group code: ' . $rule->get_group_code());
		}

		return $rules;
	}
}
