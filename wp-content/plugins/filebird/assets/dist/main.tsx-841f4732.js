import{r as c,u as U,j as R,a as u,_ as P,F as Ne,c as qr,b as Qs,d as an,R as Ve,i as Js,e as On,f as En,g as Qr,h as Jr,k as eu,l as Di,m as tu,n as nu,o as ru,p as ou,q as au,s as iu,T as rr,t as su,v as cu,S as ec,w as Ia,x as tc,y as lu,z as dt,A as ve,B as dn,M as du,C as uu,D as fu,E as pu,G as hu,H as nc,I as rc,J as oc,K as vu,L as mu,N as bu,O as gu,P as ko,Q as yu,U as Cu,V as oe,W as wu,X as or,Y as Fa,Z as _u,$ as cn,a0 as je,a1 as Me,a2 as fe,a3 as Kt,a4 as Ut,a5 as X,a6 as Zn,a7 as q,a8 as Ha,a9 as un,aa as It,ab as Ka,ac as Su,ad as xu,ae as Yt,af as Eu,ag as $u,ah as Ot,ai as <PERSON>,aj as Mu,ak as <PERSON>,al as eo,am as Ua,an as ac,ao as ic,ap as sc,aq as cc,ar as Ou,as as Gn,at as Ru,au as Nu,av as ku,aw as Pu,ax as Bn,ay as Lu,az as Au,aA as Iu,aB as Fu,aC as Hu,aD as Ku,aE as Uu,aF as zu,aG as Wu,aH as Bu,aI as Vu,aJ as Oi,aK as ju}from"./ProBadge-60c93dc8.js";const lc=(e,n)=>e.flatMap(r=>{const t="-".repeat(n),o={...r,text:t+r.text};if(r.children.length){n++;const a=lc(r.children,n);return n--,[o,...a]}return o}),za=c.forwardRef(({fetchOnLoad:e=!1,onValueChange:n,onFetched:r,...t},o)=>{const a=U(p=>p.getTreeData),s=U(p=>lc(p.treeData,0)),i=U(p=>p.selectedKeys[0]),d=U(p=>p.setSelectedKeys);async function l(){const p=await a();r&&r(p)}return c.useEffect(()=>{e&&l(),setTimeout((function(){const{uploader:p}=window;p&&p.bind("BeforeUpload",function(f){const v=f.settings.multipart_params;v.fbv=U.getState().selectedKeys[0]})}).bind(void 0),500)},[]),R("select",{ref:o,...t,defaultValue:i.toString(),onChange:p=>{d([parseInt(p.target.value)]),n&&n(parseInt(p.target.value))},children:[u("option",{value:"-1",children:P("all_files")},"-1"),u("option",{value:"0",children:P("uncategorized")},"0"),s.map(p=>u("option",{value:p.id,children:p.text},p.id))]})});function Gu({attachmentsBrowser:e}){function n(t){e.$el.val(t).trigger("change")}function r(t){qr(t)}return R(Ne,{children:[u("h2",{className:"media-attachments-filter-heading",children:"FileBird"}),u(za,{onValueChange:n,className:"fb-bg-white fb-text-black fb-max-w-28",fetchOnLoad:!0,onFetched:r})]})}const dc=()=>!(Zu()||Yu()),Yu=()=>document.body.classList.contains("upload-php")&&window.fbv_data.is_upload_screen==="1",Zu=()=>!!(window.wp&&window.wp.blocks&&window.wp.blocks.registerBlockType&&document.body.classList.contains("block-editor-page")),Xu=function(){if(window.elementor&&window.elementorAppConfig)return!0},{user_settings:qu}=window.fbv_data,uc=(e,n)=>e.flatMap(r=>{const t="-".repeat(n),o={...r,text:t+r.text};if(r.children.length){n++;const a=uc(r.children,n);return n--,[o,...a]}return o});let kr=!1;const Qu=({attachmentId:e,folderId:n,attachmentBrowser:r})=>{const t=U(s=>uc(s.treeData,0)),[o,a]=c.useState(n);return R("select",{name:`attachments[${e}][fbv]`,defaultValue:o,onChange:s=>{a(s.target.value),Qs(n,r,!0),kr=!0},children:[u("option",{value:"-1",children:P("all_files")},"-1"),u("option",{value:"0",children:P("uncategorized")},"0"),t.map(s=>u("option",{value:s.id,children:s.text},s.id))]})};jQuery(document).ajaxComplete((e,n,r)=>{try{r.data.indexOf("action=save-attachment-compat")>-1&&kr&&(U.getState().setFolderCounter(qu.FOLDER_COUNTER_TYPE),kr=!1)}catch{kr=!1}});const fc=e=>{jQuery(document).on("click",".fbv-attachment-edit-wrapper input",function(n){const r=n.target.parentNode;r&&an.createRoot(r).render(u(Ve.StrictMode,{children:u(Qu,{attachmentBrowser:e,attachmentId:r.dataset.attachmentId,folderId:r.dataset.folderId})}))})};const pc=c.createContext(void 0),Ju=e=>{const{dir:n,children:r}=e;return c.createElement(pc.Provider,{value:n},r)};function Wa(e){const n=c.useContext(pc);return e||n||"ltr"}var ef=function(e){return function(n,r){var t=c.useRef(!1);e(function(){return function(){t.current=!1}},[]),e(function(){if(!t.current)t.current=!0;else return n()},r)}};function hc(e){Js&&(On(e)||console.error("useMemoizedFn expected parameter is a function, got ".concat(typeof e)));var n=c.useRef(e);n.current=c.useMemo(function(){return e},[e]);var r=c.useRef();return r.current||(r.current=function(){for(var t=[],o=0;o<arguments.length;o++)t[o]=arguments[o];return n.current.apply(this,t)}),r.current}const vc=ef(c.useEffect);function tf(e){var n=typeof e;return e!=null&&(n=="object"||n=="function")}var mc=tf,nf=typeof En=="object"&&En&&En.Object===Object&&En,rf=nf,of=rf,af=typeof self=="object"&&self&&self.Object===Object&&self,sf=of||af||Function("return this")(),bc=sf,cf=bc,lf=function(){return cf.Date.now()},df=lf,uf=/\s/;function ff(e){for(var n=e.length;n--&&uf.test(e.charAt(n)););return n}var pf=ff,hf=pf,vf=/^\s+/;function mf(e){return e&&e.slice(0,hf(e)+1).replace(vf,"")}var bf=mf,gf=bc,yf=gf.Symbol,gc=yf,Ri=gc,yc=Object.prototype,Cf=yc.hasOwnProperty,wf=yc.toString,Ln=Ri?Ri.toStringTag:void 0;function _f(e){var n=Cf.call(e,Ln),r=e[Ln];try{e[Ln]=void 0;var t=!0}catch{}var o=wf.call(e);return t&&(n?e[Ln]=r:delete e[Ln]),o}var Sf=_f,xf=Object.prototype,Ef=xf.toString;function $f(e){return Ef.call(e)}var Tf=$f,Ni=gc,Mf=Sf,Df=Tf,Of="[object Null]",Rf="[object Undefined]",ki=Ni?Ni.toStringTag:void 0;function Nf(e){return e==null?e===void 0?Rf:Of:ki&&ki in Object(e)?Mf(e):Df(e)}var kf=Nf;function Pf(e){return e!=null&&typeof e=="object"}var Lf=Pf,Af=kf,If=Lf,Ff="[object Symbol]";function Hf(e){return typeof e=="symbol"||If(e)&&Af(e)==Ff}var Kf=Hf,Uf=bf,Pi=mc,zf=Kf,Li=0/0,Wf=/^[-+]0x[0-9a-f]+$/i,Bf=/^0b[01]+$/i,Vf=/^0o[0-7]+$/i,jf=parseInt;function Gf(e){if(typeof e=="number")return e;if(zf(e))return Li;if(Pi(e)){var n=typeof e.valueOf=="function"?e.valueOf():e;e=Pi(n)?n+"":n}if(typeof e!="string")return e===0?e:+e;e=Uf(e);var r=Bf.test(e);return r||Vf.test(e)?jf(e.slice(2),r?2:8):Wf.test(e)?Li:+e}var Yf=Gf,Zf=mc,Po=df,Ai=Yf,Xf="Expected a function",qf=Math.max,Qf=Math.min;function Jf(e,n,r){var t,o,a,s,i,d,l=0,p=!1,f=!1,v=!0;if(typeof e!="function")throw new TypeError(Xf);n=Ai(n)||0,Zf(r)&&(p=!!r.leading,f="maxWait"in r,a=f?qf(Ai(r.maxWait)||0,n):a,v="trailing"in r?!!r.trailing:v);function h(M){var D=t,F=o;return t=o=void 0,l=M,s=e.apply(F,D),s}function m(M){return l=M,i=setTimeout(g,n),p?h(M):s}function b(M){var D=M-d,F=M-l,K=n-D;return f?Qf(K,a-F):K}function y(M){var D=M-d,F=M-l;return d===void 0||D>=n||D<0||f&&F>=a}function g(){var M=Po();if(y(M))return w(M);i=setTimeout(g,b(M))}function w(M){return i=void 0,v&&t?h(M):(t=o=void 0,s)}function x(){i!==void 0&&clearTimeout(i),l=0,t=d=o=i=void 0}function C(){return i===void 0?s:w(Po())}function $(){var M=Po(),D=y(M);if(t=arguments,o=this,d=M,D){if(i===void 0)return m(d);if(f)return clearTimeout(i),i=setTimeout(g,n),h(d)}return i===void 0&&(i=setTimeout(g,n)),s}return $.cancel=x,$.flush=C,$}var e1=Jf;const t1=Qr(e1);var Cc={exports:{}};/*!
 * JavaScript Cookie v2.2.1
 * https://github.com/js-cookie/js-cookie
 *
 * Copyright 2006, 2015 Klaus Hartl & Fagner Brack
 * Released under the MIT license
 */(function(e,n){(function(r){var t;if(e.exports=r(),t=!0,!t){var o=window.Cookies,a=window.Cookies=r();a.noConflict=function(){return window.Cookies=o,a}}})(function(){function r(){for(var a=0,s={};a<arguments.length;a++){var i=arguments[a];for(var d in i)s[d]=i[d]}return s}function t(a){return a.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent)}function o(a){function s(){}function i(l,p,f){if(!(typeof document>"u")){f=r({path:"/"},s.defaults,f),typeof f.expires=="number"&&(f.expires=new Date(new Date*1+f.expires*864e5)),f.expires=f.expires?f.expires.toUTCString():"";try{var v=JSON.stringify(p);/^[\{\[]/.test(v)&&(p=v)}catch{}p=a.write?a.write(p,l):encodeURIComponent(String(p)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),l=encodeURIComponent(String(l)).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/[\(\)]/g,escape);var h="";for(var m in f)f[m]&&(h+="; "+m,f[m]!==!0&&(h+="="+f[m].split(";")[0]));return document.cookie=l+"="+p+h}}function d(l,p){if(!(typeof document>"u")){for(var f={},v=document.cookie?document.cookie.split("; "):[],h=0;h<v.length;h++){var m=v[h].split("="),b=m.slice(1).join("=");!p&&b.charAt(0)==='"'&&(b=b.slice(1,-1));try{var y=t(m[0]);if(b=(a.read||a)(b,y)||t(b),p)try{b=JSON.parse(b)}catch{}if(f[y]=b,l===y)break}catch{}}return l?f[l]:f}}return s.set=i,s.get=function(l){return d(l,!1)},s.getJSON=function(l){return d(l,!0)},s.remove=function(l,p){i(l,"",r(p,{expires:-1}))},s.defaults={},s.withConverter=o,s}return o(function(){})})})(Cc);var n1=Cc.exports;const Lo=Qr(n1);function r1(e,n){n===void 0&&(n={});var r=Jr(c.useState(function(){var s=Lo.get(e);return eu(s)?s:On(n.defaultValue)?n.defaultValue():n.defaultValue}),2),t=r[0],o=r[1],a=hc(function(s,i){i===void 0&&(i={});var d=Di(Di({},n),i);d.defaultValue;var l=tu(d,["defaultValue"]),p=On(s)?s(t):s;o(p),p===void 0?Lo.remove(e):Lo.set(e,p,l)});return[t,a]}function o1(e,n){var r;Js&&(On(e)||console.error("useDebounceFn expected parameter is a function, got ".concat(typeof e)));var t=nu(e),o=(r=n==null?void 0:n.wait)!==null&&r!==void 0?r:1e3,a=c.useMemo(function(){return t1(function(){for(var s=[],i=0;i<arguments.length;i++)s[i]=arguments[i];return t.current.apply(t,ru([],Jr(s),!1))},o,n)},[]);return ou(function(){a.cancel()}),{run:a,cancel:a.cancel,flush:a.flush}}function a1(e,n,r){var t=Jr(c.useState({}),2),o=t[0],a=t[1],s=o1(function(){a({})},r).run;c.useEffect(function(){return s()},n),vc(e,[o])}function i1(e){function n(r,t){t===void 0&&(t={});var o,a=t.onError,s=a===void 0?function(m){console.error(m)}:a;try{o=e()}catch(m){s(m)}var i=function(m){return t!=null&&t.serializer?t==null?void 0:t.serializer(m):JSON.stringify(m)},d=function(m){return t!=null&&t.deserializer?t==null?void 0:t.deserializer(m):JSON.parse(m)};function l(){try{var m=o==null?void 0:o.getItem(r);if(m)return d(m)}catch(b){s(b)}return On(t==null?void 0:t.defaultValue)?t==null?void 0:t.defaultValue():t==null?void 0:t.defaultValue}var p=Jr(c.useState(function(){return l()}),2),f=p[0],v=p[1];vc(function(){v(l())},[r]);var h=function(m){var b=On(m)?m(f):m;if(v(b),au(b))o==null||o.removeItem(r);else try{o==null||o.setItem(r,i(b))}catch(y){console.error(y)}};return[f,hc(h)]}return n}var s1=i1(function(){return iu?localStorage:void 0});const c1=s1,l1=()=>{const[e,n]=r1("fbv_close_buy_pro_dialog",{defaultValue:"0"});if(!(e==="0"||new Date(e)<new Date))return null;const t=()=>{const o=new Date,a=o.setDate(o.getDate()+7);n(a.toString())};return R("div",{className:"fb-mt-2 fb-relative fb-mr-[10px] fb-bg-white fb-p-3 fb-rounded fb-shadow-md",children:[R("h3",{className:"fb-m-0 fb-flex fb-items-center fb-justify-between",children:[u("span",{className:"fb-text-lg fb-font-semibold",children:P("unlock_new_features_title")}),u(rr,{sideOffset:5,content:P("turn_off_for_7_days"),children:()=>u("button",{onClick:t,type:"button",className:"fb-border-none fb-bg-gray-100 hover:fb-bg-gray-200 fb-w-5 fb-h-5 fb-flex fb-justify-center fb-items-center fb-cursor-pointer fb-rounded-full",children:u("span",{className:"dashicons dashicons-no-alt"})})})]}),u("p",{className:"fb-my-3",children:P("unlock_new_features_desc")}),u("div",{children:u("a",{rel:"noopener noreferrer",target:"_blank",href:"https://1.envato.market/Go-Pro-FileBird",className:"button button-primary",children:u("strong",{children:P("go_pro")})})})]})},{user_settings:d1}=window.fbv_data,wc=d1.THEME.name,_c=c.createContext({theme:wc,attachmentsBrowser:{},modal:!1,setTheme:()=>{}}),u1=({children:e,attachmentsBrowser:n,modal:r})=>{const[t,o]=c.useState(wc);return u(_c.Provider,{value:{theme:t,setTheme:o,attachmentsBrowser:n,modal:r},children:u(Ne,{children:e})})},ke=()=>c.useContext(_c),{tree_mode:f1}=window.fbv_data;function Nn(){const e=U(i=>i.deleteFolders),n=U(i=>i.setSelectedKeys),r=U(i=>i.selectedKeys),t=U(i=>i.checkedKeys),{attachmentsBrowser:o}=ke();function a(i){!i.length||i[0]==="#"||(f1==="post_type"?su(i[0]):o?Qs(i[0],o):cu(i[0]),n(i.map(Number)))}async function s(i){try{await e(i),(r[0]===i||t.indexOf(r[0])!==-1)&&a([ec.UN_CATEGORIZED])}catch(d){console.log(d)}}return{selectFolder:a,deleteFolder:s}}const{fbv_data:pa}=window,p1={tree:".fb-tree-treenode, .fb-uncategorized"},h1=pa.tree_mode==="attachment"?Ia:tc,Ao=(e,n)=>{jQuery(e).droppable({greedy:!0,accept:'li.attachment, tr[id^="post-"], tr[id^="attachment-"]',hoverClass:"fbv-drophover",drop:(r,t)=>{var l,p;const o=jQuery(r.target),a=o.closest(".fb-tree-treenode"),s=o.attr("data-id")||a.attr("data-id"),i=U.getState(),d=i.selectedKeys[0];if(s){U.setState({isProcessing:!0});const f=((l=jQuery(t.helper[0]).attr("data-id"))==null?void 0:l.split(","))||[];if((p=n==null?void 0:n.controller)!=null&&p.isModeActive("grid")&&(n.controller.state().get("selection").reset(),n.controller.trigger("selection:toggle")),s===void 0||s==i.selectedKeys[0])return;h1.assignFolder({folderId:s,ids:f}).then(v=>{i.calculateAttachmentsCount(v),d!=-1&&lu(f,n),dt.success(pa.i18n.move_done)}).catch(v=>{console.log(v),dt.error(pa.i18n.move_error)}).done(()=>{U.setState({isProcessing:!1})})}}})},Ba={forTree:e=>{Ao(p1.tree,e)},forExpandNode:(e,n)=>{e&&setTimeout(()=>{Ao(e.querySelectorAll(`.catf-tree-treenode[data-parent="${n.key}"] .catf-tree-node-content-wrapper`))},300)},forNode:(e,n)=>{Ao(e,n)}},Ii=e=>typeof e=="boolean"?"".concat(e):e===0?"0":e,Fi=ve,fn=(e,n)=>r=>{var t;if((n==null?void 0:n.variants)==null)return Fi(e,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:o,defaultVariants:a}=n,s=Object.keys(o).map(l=>{const p=r==null?void 0:r[l],f=a==null?void 0:a[l];if(p===null)return null;const v=Ii(p)||Ii(f);return o[l][v]}),i=r&&Object.entries(r).reduce((l,p)=>{let[f,v]=p;return v===void 0||(l[f]=v),l},{}),d=n==null||(t=n.compoundVariants)===null||t===void 0?void 0:t.reduce((l,p)=>{let{class:f,className:v,...h}=p;return Object.entries(h).every(m=>{let[b,y]=m;return Array.isArray(y)?y.includes({...a,...i}[b]):{...a,...i}[b]===y})?[...l,f,v]:l},[]);return Fi(e,s,d,r==null?void 0:r.class,r==null?void 0:r.className)},v1=c.memo(()=>u("svg",{viewBox:"0 0 22 24",fill:"currentColor",children:u("path",{d:"M2.25538 24C1.40735 24 1.97271 24 1.12468 24C0.606441 24 0.182428 23.6147 0.04109 23.1332C-0.100248 22.6035 0.135315 22.0256 0.559328 21.7848C0.794891 21.6885 1.07757 21.6403 1.31313 21.6403C8.23868 21.6403 13.7508 21.6403 20.6764 21.6403C20.7706 21.6403 20.8177 21.6403 20.912 21.6403C21.4773 21.6885 21.9484 22.1219 21.9955 22.6516C22.0427 23.2776 21.7129 23.8074 21.1475 23.9518C20.9591 24 20.7706 24 20.5822 24C17.143 24 13.7037 24 10.2645 24C7.62621 24 4.9408 24 2.25538 24ZM18.6506 6.18201C17.143 7.72302 15.6354 9.26404 14.1277 10.8051C11.5366 13.4537 8.94537 16.1023 6.35417 18.7991C6.0715 19.088 5.74171 19.2325 5.3177 19.2325C4.84657 19.2325 4.32833 19.2325 3.8101 19.2325C3.29186 19.2325 2.72651 19.2325 2.20827 19.2325C1.45447 19.2325 0.936229 18.7509 0.936229 17.9804C0.936229 16.8728 0.936229 15.8134 0.936229 14.7058C0.936229 14.3205 1.07757 14.0316 1.31313 13.7426C1.69003 13.3574 2.06693 12.924 2.49094 12.5387C6.21284 8.73431 9.93473 4.88177 13.6566 1.07739C14.6931 0.0179429 15.9651 -0.270998 17.3785 0.258727C19.7341 1.12555 20.4408 4.30389 18.6506 6.18201ZM16.9545 2.76288C16.4363 2.23315 15.7767 2.28131 15.2585 2.81103C11.301 6.90436 7.39065 10.9495 3.43319 14.9947C3.33897 15.091 3.29186 15.2355 3.29186 15.3318C3.29186 15.8134 3.29186 16.2949 3.29186 16.8246C3.8101 16.8246 4.32833 16.8246 4.84657 16.8246C4.9408 16.8246 5.08214 16.7283 5.12925 16.632C8.61558 13.0684 12.0548 9.50482 15.5411 5.94122C16.0123 5.45965 16.4834 4.97809 16.9074 4.54468C17.4256 3.9668 17.4727 3.2926 16.9545 2.76288Z"})})),m1=c.memo(()=>u("svg",{viewBox:"0 0 22 24",fill:"currentColor",children:u("path",{d:"M20.8147 4.66533H18.2069H16.4526L16.3103 3.31864C16.1207 1.39479 14.556 0 12.7069 0H8.91379C7.25431 0 5.8319 1.2986 5.68965 2.98196L5.5 4.66533H3.7931H1.18534C0.521552 4.66533 0 5.19439 0 5.86774C0 6.54108 0.521552 7.07014 1.18534 7.07014H2.70259L3.84052 20.2966C3.98276 22.3647 5.73707 24 7.77586 24H14.2241C16.2629 24 17.9698 22.3647 18.1595 20.2966L19.2974 7.07014H20.8147C21.4784 7.07014 22 6.54108 22 5.86774C22 5.19439 21.4784 4.66533 20.8147 4.66533ZM8.01293 3.22244C8.06034 2.74148 8.43965 2.40481 8.91379 2.40481H12.7069C13.3233 2.40481 13.8922 2.88577 13.9397 3.55912L14.0345 4.66533H7.87069L8.01293 3.22244ZM15.7888 20.1042C15.7414 20.9218 15.0302 21.5952 14.2241 21.5952H7.77586C6.96983 21.5952 6.25862 20.9699 6.21121 20.1042L5.07328 7.07014H6.5431H15.3621H16.9267L15.7888 20.1042Z"})}));c.memo(()=>u("svg",{viewBox:"0 0 22 24",fill:"currentColor",children:u("path",{d:"M20.8147 4.66533H18.2069H16.4526L16.3103 3.31864C16.1207 1.39479 14.556 0 12.7069 0H8.91379C7.25431 0 5.8319 1.2986 5.68965 2.98196L5.5 4.66533H3.7931H1.18534C0.521552 4.66533 0 5.19439 0 5.86774C0 6.54108 0.521552 7.07014 1.18534 7.07014H2.70259L3.84052 20.2966C3.98276 22.3647 5.73707 24 7.77586 24H14.2241C16.2629 24 17.9698 22.3647 18.1595 20.2966L19.2974 7.07014H20.8147C21.4784 7.07014 22 6.54108 22 5.86774C22 5.19439 21.4784 4.66533 20.8147 4.66533ZM8.01293 3.22244C8.06034 2.74148 8.43965 2.40481 8.91379 2.40481H12.7069C13.3233 2.40481 13.8922 2.88577 13.9397 3.55912L14.0345 4.66533H7.87069L8.01293 3.22244ZM15.7888 20.1042C15.7414 20.9218 15.0302 21.5952 14.2241 21.5952H7.77586C6.96983 21.5952 6.25862 20.9699 6.21121 20.1042L5.07328 7.07014H6.5431H15.3621H16.9267L15.7888 20.1042Z"})}));const b1=c.memo(()=>R("svg",{viewBox:"0 0 35 27",fill:"currentColor",children:[u("path",{d:"M31.5595 27H3.44045C1.5879 27 0 25.4927 0 23.5922V3.40777C0 1.50728 1.52174 0 3.44045 0H10.3875C11.38 0 12.3062 0.393204 12.9017 1.11408L13.828 2.16262C14.3573 2.75243 15.1512 3.0801 15.9452 3.0801H31.5595C33.4121 3.0801 35 4.58738 35 6.48786V23.6578C35 25.4927 33.4783 27 31.5595 27Z",fill:"#94CEFF"}),u("path",{d:"M20.6973 7.92969C20.8335 7.92969 20.9697 7.92969 21.1059 7.92969C21.4465 7.92969 21.7189 8.12629 21.9913 8.38843C23.9665 10.2889 25.9416 12.1894 27.9168 14.0899C28.1892 14.352 28.3935 14.6142 28.3935 14.9418C28.3935 15.0729 28.3935 15.204 28.3935 15.335C28.3254 15.6627 28.1892 15.9904 27.9168 16.187C27.6443 16.3836 27.3719 16.4491 27.0314 16.4491C26.827 16.4491 26.827 16.4491 26.827 16.6457C26.827 18.0219 26.827 19.3981 26.827 20.7088C26.827 21.2986 26.5546 21.7574 26.0097 22.085C25.8054 22.2161 25.6011 22.2816 25.3287 22.2816C24.4432 22.2816 23.5578 22.2816 22.6043 22.2816C22.3319 22.2161 22.1957 22.085 22.1957 21.7574C22.1957 20.7088 22.1957 19.5947 22.1957 18.5462C22.1957 18.4151 22.1957 18.2841 22.1276 18.153C21.9913 17.9564 21.787 17.8253 21.5146 17.8253C21.0378 17.8253 20.5611 17.8253 20.1524 17.8253C19.7438 17.8253 19.4713 18.153 19.4713 18.5462C19.4713 19.5947 19.4713 20.6433 19.4713 21.6918C19.4713 21.7574 19.4713 21.8229 19.4713 21.8884C19.4713 22.1506 19.3351 22.2816 19.0627 22.3472C18.1092 22.3472 17.2237 22.3472 16.2702 22.3472C15.521 22.2816 14.9762 21.6263 14.9081 21.0365C14.9081 20.9709 14.9081 20.8399 14.9081 20.7743C14.9081 19.4637 14.9081 18.0875 14.9081 16.7768C14.9081 16.7112 14.9081 16.7112 14.9081 16.6457C14.9081 16.5146 14.8399 16.5146 14.7718 16.5146C14.4313 16.5146 14.1589 16.4491 13.9545 16.2525C13.614 16.0559 13.4097 15.7282 13.4097 15.4006C13.4097 15.204 13.4097 15.0729 13.4097 14.8763C13.4778 14.5486 13.614 14.2865 13.8864 14.0899C15.8616 12.1894 17.9048 10.2889 19.88 8.32289C20.0843 8.12629 20.3567 7.92969 20.6973 7.92969Z",fill:"white"})]})),g1=c.memo(()=>R("svg",{viewBox:"0 0 36 31",fill:"currentColor",children:[u("path",{d:"M24.5539 29.0463C29.9918 29.0463 34.4001 24.638 34.4001 19.2002C34.4001 13.7623 29.9918 9.354 24.5539 9.354C19.116 9.354 14.7078 13.7623 14.7078 19.2002C14.7078 24.638 19.116 29.0463 24.5539 29.0463Z",fill:"white"}),u("path",{d:"M32.5538 11.0154V6.09231C32.5538 4.36923 31.1385 2.89231 29.3538 2.89231H14.8308C14.0923 2.89231 13.3538 2.58462 12.8615 2.03077L12 1.04615C11.3846 0.369231 10.5231 0 9.66154 0H3.2C1.41538 0 0 1.41538 0 3.2V22.2154C0 23.9385 1.41538 25.4154 3.2 25.4154H15.0769C17.1077 28.5538 20.6154 30.5846 24.6154 30.5846C30.8923 30.5846 36 25.4769 36 19.2C35.9385 16 34.6462 13.1077 32.5538 11.0154ZM24.5538 27.4462C20 27.4462 16.2462 23.6923 16.2462 19.1385C16.2462 14.5846 20 10.8308 24.5538 10.8308C29.1077 10.8308 32.8615 14.5846 32.8615 19.1385C32.8615 23.7538 29.1692 27.4462 24.5538 27.4462ZM24.5538 20.9846C23.6923 20.9846 23.0154 20.3077 23.0154 19.4462V14.2769C23.0154 13.4154 23.6923 12.7385 24.5538 12.7385C25.4154 12.7385 26.0923 13.4154 26.0923 14.2769V19.4462C26.0923 20.3077 25.4154 20.9846 24.5538 20.9846ZM24.5538 22.5231C23.6923 22.5231 23.0154 23.2 23.0154 24.0615C23.0154 24.9231 23.6923 25.6 24.5538 25.6C25.4154 25.6 26.0923 24.9231 26.0923 24.0615C26.0923 23.2 25.4154 22.5231 24.5538 22.5231Z",fill:"#94CEFF"})]})),Pr=c.memo(()=>u("svg",{viewBox:"0 0 35 27",fill:"currentColor",children:u("path",{d:"M31.5 27H3.5C1.63333 27 0 25.3846 0 23.5385V3.46154C0 1.61538 1.63333 0 3.5 0H10.5C11.4333 0 12.3667 0.461538 13.0667 1.15385L14 2.30769C14.4667 3 15.4 3.23077 16.1 3.23077H31.7333C33.3667 3 35 4.61538 35 6.46154V23.5385C35 25.3846 33.3667 27 31.5 27Z"})})),y1=c.memo(()=>R(Ne,{children:[u("svg",{viewBox:"0 0 15 11",fill:"#33302F",className:"fb-arrow",children:u("path",{d:"M2.12944e-08 2.28571C1.49061e-08 1.75 0.172414 1.39286 0.517241 1.03571C1.2069 0.321429 2.24138 0.321429 2.93103 1.03571L7.41379 6.03571L12.069 1.03571C12.7586 0.321429 13.7931 0.321429 14.4828 1.03571C15.1724 1.75 15.1724 2.82143 14.4828 3.53571L8.7931 9.96429C8.44828 10.3214 7.93103 10.5 7.58621 10.5C7.24138 10.5 6.55172 10.1429 6.2069 9.78571L0.517242 3.53571C0.172414 3.17857 2.55533e-08 2.64286 2.12944e-08 2.28571Z"})}),R("svg",{viewBox:"0 0 38 27",fill:"currentColor",children:[u("path",{d:"M6.9486 7.4859H33.5913V6.31544C33.5913 4.50223 32.1258 3.03266 30.3176 3.03266H15.3213C14.5463 3.03266 13.8076 2.70089 13.2918 2.12049L12.3851 1.09921C11.7633 0.400049 10.8744 0 9.94032 0H3.27372C1.46551 0 0 1.46957 0 3.28278V19.6507C0 19.9936 0.0525527 20.3239 0.149513 20.6348L3.3729 10.3211C3.7674 8.65191 5.23736 7.4859 6.9486 7.4859Z",fill:"currentColor"}),u("path",{d:"M31.5524 27.0003H3.77647C2.19549 27.0003 1.029 25.5211 1.39316 23.9788L5.30416 11.3465C5.56543 10.2383 6.55206 9.45605 7.68746 9.45605H35.5492C37.1346 9.45605 38.3026 10.9442 37.9296 12.4895L33.9327 25.1218C33.667 26.224 32.6833 27.0003 31.5524 27.0003Z",fill:"currentColor"})]})]})),C1=c.memo(()=>R(Ne,{children:[u("svg",{viewBox:"0 0 10 15",className:"fb-arrow",children:u("path",{d:"M1.78571 15C1.25 15 0.892857 14.8276 0.535714 14.4828C-0.178571 13.7931 -0.178571 12.7586 0.535714 12.069L5.53571 7.58621L0.535714 2.93103C-0.178571 2.24138 -0.178571 1.2069 0.535714 0.517241C1.25 -0.172414 2.32143 -0.172414 3.03571 0.517241L9.46429 6.2069C9.82143 6.55172 10 7.06897 10 7.41379C10 7.75862 9.64286 8.44828 9.28571 8.7931L3.03571 14.4828C2.67857 14.8276 2.14286 15 1.78571 15Z",fill:"#8A8A8A"})}),u("svg",{viewBox:"0 0 35 27",fill:"currentColor",children:u("path",{d:"M31.5 27H3.5C1.63333 27 0 25.3846 0 23.5385V3.46154C0 1.61538 1.63333 0 3.5 0H10.5C11.4333 0 12.3667 0.461538 13.0667 1.15385L14 2.30769C14.4667 3 15.4 3.23077 16.1 3.23077H31.7333C33.3667 3 35 4.61538 35 6.46154V23.5385C35 25.3846 33.3667 27 31.5 27Z"})})]})),w1=c.memo(()=>u("svg",{viewBox:"0 0 36 30",fill:"currentColor",children:u("path",{d:"M16.1985 24.8692H3.20274C1.47819 24.8692 0 23.4809 0 21.7304V3.13883C0 1.38833 1.4166 0 3.20274 0H9.6698C10.5937 0 11.4559 0.362173 12.0103 1.02616L12.8725 1.99195C13.3653 2.53521 14.1044 2.83702 14.8435 2.83702H29.379C31.1035 2.83702 32.5817 4.22535 32.5817 5.97585V11.7706C30.734 10.2012 28.2703 9.23541 25.6219 9.23541C19.7092 9.23541 14.905 13.9437 14.905 19.7384C14.8435 21.6097 15.3362 23.3602 16.1985 24.8692ZM35.5381 29.5775C35.2301 29.8793 34.8606 30 34.4294 30C34.0599 30 33.6287 29.8793 33.3208 29.5775L29.6869 26.0161C28.5167 26.8008 27.1001 27.2233 25.5603 27.2233C21.3721 27.2233 17.923 23.9034 17.923 19.7384C17.923 15.5734 21.3105 12.2535 25.5603 12.2535C29.8101 12.2535 33.1976 15.6942 33.1976 19.7988C33.1976 21.3078 32.7049 22.7565 31.9042 23.9638L35.5381 27.4648C36.154 28.008 36.154 28.9738 35.5381 29.5775ZM25.6219 24.2052C28.1471 24.2052 30.1796 22.2133 30.1796 19.7384C30.1796 17.2636 28.1471 15.2716 25.6219 15.2716C23.0967 15.2716 21.0642 17.2636 21.0642 19.7384C21.0642 22.2133 23.0967 24.2052 25.6219 24.2052Z"})})),_1=c.memo(()=>u("svg",{viewBox:"0 0 21 21",fill:"currentColor",children:u("path",{d:"M19.8487 9.34868H11.6513V1.15132C11.6513 0.506579 11.1447 0 10.5 0C9.85526 0 9.34868 0.506579 9.34868 1.15132V9.34868H1.15132C0.506579 9.34868 0 9.85526 0 10.5C0 11.1447 0.506579 11.6513 1.15132 11.6513H9.34868V19.8487C9.34868 20.4934 9.85526 21 10.5 21C11.1447 21 11.6513 20.4934 11.6513 19.8487V11.6513H19.8487C20.4934 11.6513 21 11.1447 21 10.5C21 9.85526 20.4934 9.34868 19.8487 9.34868Z"})})),S1=c.memo(()=>u("svg",{viewBox:"0 0 22 24",children:u("path",{d:"M2.25538 24C1.40735 24 1.97271 24 1.12468 24C0.606441 24 0.182428 23.6147 0.04109 23.1332C-0.100248 22.6035 0.135315 22.0256 0.559328 21.7848C0.794891 21.6885 1.07757 21.6403 1.31313 21.6403C8.23868 21.6403 13.7508 21.6403 20.6764 21.6403C20.7706 21.6403 20.8177 21.6403 20.912 21.6403C21.4773 21.6885 21.9484 22.1219 21.9955 22.6516C22.0427 23.2776 21.7129 23.8074 21.1475 23.9518C20.9591 24 20.7706 24 20.5822 24C17.143 24 13.7037 24 10.2645 24C7.62621 24 4.9408 24 2.25538 24ZM18.6506 6.18201C17.143 7.72302 15.6354 9.26404 14.1277 10.8051C11.5366 13.4537 8.94537 16.1023 6.35417 18.7991C6.0715 19.088 5.74171 19.2325 5.3177 19.2325C4.84657 19.2325 4.32833 19.2325 3.8101 19.2325C3.29186 19.2325 2.72651 19.2325 2.20827 19.2325C1.45447 19.2325 0.936229 18.7509 0.936229 17.9804C0.936229 16.8728 0.936229 15.8134 0.936229 14.7058C0.936229 14.3205 1.07757 14.0316 1.31313 13.7426C1.69003 13.3574 2.06693 12.924 2.49094 12.5387C6.21284 8.73431 9.93473 4.88177 13.6566 1.07739C14.6931 0.0179429 15.9651 -0.270998 17.3785 0.258727C19.7341 1.12555 20.4408 4.30389 18.6506 6.18201ZM16.9545 2.76288C16.4363 2.23315 15.7767 2.28131 15.2585 2.81103C11.301 6.90436 7.39065 10.9495 3.43319 14.9947C3.33897 15.091 3.29186 15.2355 3.29186 15.3318C3.29186 15.8134 3.29186 16.2949 3.29186 16.8246C3.8101 16.8246 4.32833 16.8246 4.84657 16.8246C4.9408 16.8246 5.08214 16.7283 5.12925 16.632C8.61558 13.0684 12.0548 9.50482 15.5411 5.94122C16.0123 5.45965 16.4834 4.97809 16.9074 4.54468C17.4256 3.9668 17.4727 3.2926 16.9545 2.76288Z",fill:"currentColor"})})),x1=c.memo(()=>u("svg",{viewBox:"0 0 22 24",children:u("path",{d:"M20.8147 4.66533H18.2069H16.4526L16.3103 3.31864C16.1207 1.39479 14.556 0 12.7069 0H8.91379C7.25431 0 5.8319 1.2986 5.68965 2.98196L5.5 4.66533H3.7931H1.18534C0.521552 4.66533 0 5.19439 0 5.86774C0 6.54108 0.521552 7.07014 1.18534 7.07014H2.70259L3.84052 20.2966C3.98276 22.3647 5.73707 24 7.77586 24H14.2241C16.2629 24 17.9698 22.3647 18.1595 20.2966L19.2974 7.07014H20.8147C21.4784 7.07014 22 6.54108 22 5.86774C22 5.19439 21.4784 4.66533 20.8147 4.66533ZM8.01293 3.22244C8.06034 2.74148 8.43965 2.40481 8.91379 2.40481H12.7069C13.3233 2.40481 13.8922 2.88577 13.9397 3.55912L14.0345 4.66533H7.87069L8.01293 3.22244ZM15.7888 20.1042C15.7414 20.9218 15.0302 21.5952 14.2241 21.5952H7.77586C6.96983 21.5952 6.25862 20.9699 6.21121 20.1042L5.07328 7.07014H6.5431H15.3621H16.9267L15.7888 20.1042Z",fill:"currentColor"})})),Hi=c.memo(()=>u("svg",{viewBox:"0 0 29 28",fill:"currentColor",children:u("path",{d:"M11.3275 9.99237L8.30683 1.87023C7.82137 0.587786 7.01226 0 5.71769 0C4.47706 0 3.66795 0.587786 3.18249 1.87023L0.215763 9.99237C0.0539413 10.3664 0 10.7405 0 11.0076C0 11.9695 0.647285 12.6107 1.67215 12.6107C2.5352 12.6107 3.07461 12.1298 3.39825 11.1145L3.77583 9.93893H7.71348L8.09107 11.1679C8.36077 12.1832 8.90017 12.6641 9.81716 12.6641C10.7881 12.6641 11.4893 11.9695 11.4893 11.0611C11.5433 10.687 11.4354 10.3664 11.3275 9.99237ZM6.95832 7.32061H4.47706L5.71769 3.20611L6.95832 7.32061ZM10.7881 26.6107C10.7881 27.4122 10.1947 27.9466 9.27776 27.9466H2.48126C1.51033 27.9466 0.809107 27.3588 0.809107 26.4504C0.809107 26.0229 0.916988 25.6489 1.24063 25.2214L6.41891 18.4351H2.21156C1.29457 18.4351 0.701226 17.9008 0.701226 17.0992C0.701226 16.2977 1.29457 15.7634 2.21156 15.7634H8.68441C9.70928 15.7634 10.3566 16.3511 10.3566 17.313C10.3566 17.8473 10.0869 18.1679 9.8711 18.4885L4.74676 25.2748H9.22382C10.1947 25.2214 10.7881 25.7557 10.7881 26.6107ZM27.7093 22.4962L22.8546 27.5725C22.5849 27.8397 22.2613 28 21.8837 28C21.5061 28 21.1825 27.8397 20.9128 27.5725L16.0581 22.4962C15.5187 21.9618 15.5727 21.1069 16.1121 20.626C16.6515 20.0916 17.5145 20.145 18 20.6794L20.5352 23.3511V1.38931C20.5352 0.641222 21.1285 0.0534353 21.8837 0.0534353C22.6389 0.0534353 23.2322 0.641222 23.2322 1.38931V23.3511L25.7674 20.6794C26.3068 20.145 27.1159 20.145 27.6553 20.626C28.1947 21.1603 28.1947 21.9618 27.7093 22.4962Z"})})),E1=c.memo(()=>R("svg",{viewBox:"0 0 35 27",fill:"currentColor",children:[u("path",{d:"M33.1333 27H1.86667C0.84 27 0 26.1669 0 25.1486V1.85143C0 0.833143 0.84 0 1.86667 0H11.6511C12.1956 0 12.7244 0.231429 13.0667 0.648L15.6022 3.61029H33.1333C34.16 3.61029 35 4.44343 35 5.46171V25.1486C35 26.1823 34.1756 27 33.1333 27Z",fill:"url(#paint0_linear_986_3918)"}),u("path",{opacity:"0.35",d:"M33.1333 26.5063H1.86667C0.84 26.5063 0 25.6732 0 24.6549V7.83772C0 6.81944 0.84 5.98629 1.86667 5.98629H11.9C12.7556 5.98629 13.58 5.70858 14.2489 5.19944L15.6022 4.18115H33.1333C34.16 4.18115 35 5.01429 35 6.03258V24.6549C35 25.6732 34.1756 26.5063 33.1333 26.5063Z",fill:"url(#paint1_linear_986_3918)"}),u("path",{d:"M21.4821 8.11523C21.6221 8.11523 21.7465 8.11523 21.8865 8.11523C22.2443 8.14609 22.5087 8.31581 22.7421 8.56266C24.6554 10.4604 26.5843 12.3735 28.4976 14.2712C28.7465 14.5181 28.9332 14.7804 28.9643 15.1352C28.9643 15.2741 28.9643 15.4129 28.9643 15.5518C28.9176 15.9067 28.7465 16.1844 28.4665 16.4004C28.2176 16.5855 27.9376 16.6781 27.6421 16.6935C27.4399 16.6935 27.4399 16.6935 27.4399 16.9095C27.4399 18.2672 27.4399 19.6249 27.4399 20.9827C27.4399 21.5844 27.1599 22.0472 26.6621 22.3712C26.4754 22.4947 26.2576 22.5409 26.0399 22.5564C25.1687 22.5564 24.2976 22.5564 23.4265 22.5564C23.1465 22.4947 23.0376 22.3404 23.0376 22.0318C23.0376 20.9518 23.0376 19.8872 23.0376 18.8072C23.0376 18.6529 23.0221 18.5141 22.9443 18.3907C22.8043 18.1747 22.6021 18.0667 22.3687 18.0667C21.9176 18.0512 21.4665 18.0512 21.0154 18.0667C20.6265 18.0821 20.331 18.3598 20.331 18.7918C20.3465 19.8255 20.331 20.8747 20.331 21.9084C20.331 21.9701 20.331 22.0472 20.331 22.1089C20.3154 22.3712 20.2065 22.4947 19.9421 22.5564C19.0554 22.5564 18.1532 22.5564 17.2665 22.5564C16.5199 22.4792 16.0065 21.8158 15.9287 21.2449C15.9132 21.1524 15.9132 21.0598 15.9132 20.9827C15.9132 19.6558 15.9132 18.3135 15.9132 16.9867C15.9132 16.9404 15.9132 16.9095 15.9132 16.8632C15.9287 16.7398 15.8665 16.7089 15.7576 16.7089C15.4621 16.7244 15.1821 16.6318 14.9487 16.4621C14.6376 16.2461 14.451 15.9684 14.4043 15.5981C14.4043 15.4284 14.4043 15.2587 14.4043 15.0889C14.451 14.7649 14.6221 14.5181 14.8554 14.3021C16.7843 12.3889 18.7132 10.4912 20.6265 8.57809C20.8599 8.33123 21.1243 8.14609 21.4821 8.11523Z",fill:"white"}),R("defs",{children:[R("linearGradient",{id:"paint0_linear_986_3918",x1:"17.5",y1:"0",x2:"17.5",y2:"27",gradientUnits:"userSpaceOnUse",children:[u("stop",{stopColor:"#28AFEA"}),u("stop",{offset:"0.1685",stopColor:"#26A6E1"}),u("stop",{offset:"0.4486",stopColor:"#208CCA"}),u("stop",{offset:"0.8037",stopColor:"#1663A3"}),u("stop",{offset:"0.9944",stopColor:"#104A8C"})]}),R("linearGradient",{id:"paint1_linear_986_3918",x1:"13.1166",y1:"2.19783",x2:"22.2898",y2:"30.1632",gradientUnits:"userSpaceOnUse",children:[u("stop",{stopColor:"white"}),u("stop",{offset:"0.4173",stopColor:"#E2E2E1",stopOpacity:"0.5827"}),u("stop",{offset:"1",stopColor:"#B4B4B3",stopOpacity:"0"})]})]})]})),$1=c.memo(()=>R("svg",{fill:"currentColor",viewBox:"0 0 36 30",children:[u("path",{d:"M32.0031 10.1712V4.98438C32.0031 4.04981 31.2322 3.30215 30.3039 3.30215H14.2708L11.9422 0.591895C11.6275 0.218067 11.1397 0 10.652 0H1.69928C0.77097 0 0 0.763233 0 1.68223V22.9437C0 23.8783 0.77097 24.6259 1.69928 24.6259H14.9159C16.9299 27.8502 20.5172 29.9842 24.6081 29.9842C30.886 29.9842 35.9839 24.9375 35.9839 18.7226C35.9996 15.3114 34.4419 12.2273 32.0031 10.1712Z",fill:"url(#paint0_linear_986_3922)"}),u("path",{opacity:"0.35",d:"M35.9996 18.7384C35.9996 24.9533 30.9017 30 24.6238 30C24.6238 30 17.8739 30.109 14.6327 24.1901H1.69928C0.755236 24.1901 0 23.4268 0 22.5078V7.14971C0 6.21514 0.77097 5.46748 1.69928 5.46748H10.8723C11.659 5.46748 12.4142 5.21826 13.0121 4.75098L14.2551 3.81641H30.2881C31.2322 3.81641 31.9874 4.57964 31.9874 5.49863V10.2026C36.1255 13.4892 35.9996 18.7384 35.9996 18.7384Z",fill:"url(#paint1_linear_986_3922)"}),u("path",{d:"M24.6237 9.98438C19.7461 9.98438 15.7969 13.9096 15.7969 18.7226C15.7969 23.5357 19.7619 27.4609 24.6237 27.4609C29.4855 27.4609 33.4505 23.5357 33.4505 18.7226C33.4505 13.9096 29.4855 9.98438 24.6237 9.98438ZM24.6237 25.0621C23.7741 25.0621 23.0975 24.3768 23.0975 23.5512C23.0975 22.7101 23.7898 22.0403 24.6237 22.0403C25.4733 22.0403 26.1499 22.7257 26.1499 23.5512C26.1499 24.3768 25.4733 25.0621 24.6237 25.0621ZM26.1499 19.0186C26.1499 19.8597 25.4576 20.545 24.608 20.545C23.7583 20.545 23.066 19.8597 23.066 19.0186V13.9252C23.066 13.084 23.7583 12.3987 24.608 12.3987C25.4576 12.3987 26.1499 13.084 26.1499 13.9252V19.0186Z",fill:"white"}),R("defs",{children:[R("linearGradient",{id:"paint0_linear_986_3922",x1:"18.0004",y1:"0",x2:"18.0004",y2:"30.0022",gradientUnits:"userSpaceOnUse",children:[u("stop",{stopColor:"#29C797"}),u("stop",{offset:"1",stopColor:"#115D5D"})]}),R("linearGradient",{id:"paint1_linear_986_3922",x1:"12.0217",y1:"1.99563",x2:"21.7015",y2:"31.5614",gradientUnits:"userSpaceOnUse",children:[u("stop",{stopColor:"white"}),u("stop",{offset:"0.4173",stopColor:"#E3E3E2",stopOpacity:"0.58"}),u("stop",{offset:"1",stopColor:"#B5B5B4",stopOpacity:"0"})]})]})]})),Lr=c.memo(()=>R("svg",{viewBox:"0 0 35 27",children:[u("path",{d:"M33.1333 27.0001H1.86667C0.84 27.0001 0 26.1669 0 25.1486V1.85143C0 0.833145 0.84 0 1.86667 0H11.6511C12.1956 0 12.7244 0.231429 13.0667 0.648001L15.6022 3.61029H33.1333C34.16 3.61029 35 4.44344 35 5.46173V25.1486C35 26.1823 34.1756 27.0001 33.1333 27.0001Z",fill:"currentColor"}),u("path",{opacity:"0.35",d:"M33.1333 26.5063H1.86667C0.84 26.5063 0 25.6732 0 24.6549V7.83773C0 6.81944 0.84 5.9863 1.86667 5.9863H11.9C12.7556 5.9863 13.58 5.70858 14.2489 5.19944L15.6022 4.18115H33.1333C34.16 4.18115 35 5.0143 35 6.03258V24.6549C35 25.6732 34.1756 26.5063 33.1333 26.5063Z",fill:"url(#paint0_linear_1705_1693)"}),u("defs",{children:R("linearGradient",{id:"paint0_linear_1705_1693",x1:"13.1166",y1:"2.19787",x2:"22.2899",y2:"30.1634",gradientUnits:"userSpaceOnUse",children:[u("stop",{stopColor:"white"}),u("stop",{offset:"0.4173",stopColor:"#E3E3E2",stopOpacity:"0.5827"}),u("stop",{offset:"1",stopColor:"#B5B5B4",stopOpacity:"0"})]})})]})),T1=c.memo(()=>R("svg",{viewBox:"0 0 36 31",fill:"currentColor",children:[u("path",{d:"M35.5374 28.3452L31.9327 24.7029C32.7461 23.4726 33.2087 22.0157 33.2087 20.4292C33.2087 16.188 29.8113 12.7399 25.6323 12.7399C21.4533 12.7399 18.0558 16.188 18.0558 20.4292C18.0558 24.6705 21.4533 28.1185 25.6323 28.1185C27.1475 28.1185 28.5671 27.6653 29.7475 26.8721L33.3841 30.5306C33.6872 30.8381 34.07 30.9838 34.4688 30.9838C34.8675 30.9838 35.2503 30.8381 35.5534 30.5306C36.1436 29.9316 36.1436 28.9441 35.5374 28.3452ZM25.6163 25.0104C23.128 25.0104 21.1023 22.9546 21.1023 20.4292C21.1023 17.9039 23.128 15.848 25.6163 15.848C28.1046 15.848 30.1303 17.9039 30.1303 20.4292C30.1303 22.9546 28.1205 25.0104 25.6163 25.0104ZM0 7.43029V1.7483C0 0.793212 0.781568 0 1.72264 0H10.7825C11.2929 0 11.7714 0.226632 12.0904 0.615144L14.4351 3.43185H30.6885C31.6455 3.43185 32.4112 4.22507 32.4112 5.18016V5.71436C32.4112 4.74308 31.6296 3.96606 30.6885 3.96606H14.467L13.2069 4.93734C12.5848 5.42298 11.8192 5.68198 11.0377 5.68198H1.72264C0.781568 5.68198 0 6.45901 0 7.43029ZM15.9982 25.1399C16.078 25.3018 16.1577 25.4475 16.2375 25.6094H1.72264C0.765618 25.6094 0 24.8162 0 23.8611V23.3916C0 24.3629 0.781568 25.1399 1.72264 25.1399H15.9982Z",fill:"#F1C54C"}),u("path",{d:"M1.72264 25.1397H15.9982C15.3283 23.7152 14.9455 22.1287 14.9455 20.429C14.9455 14.4394 19.7306 9.58305 25.6323 9.58305C28.2162 9.58305 30.5928 10.522 32.4431 12.076V5.71412C32.4431 4.74284 31.6615 3.96582 30.7204 3.96582H14.467L13.2069 4.9371C12.5848 5.42274 11.8192 5.68175 11.0377 5.68175H1.72264C0.781569 5.68175 0 6.45877 0 7.43005V23.3752C0 24.3465 0.781569 25.1397 1.72264 25.1397Z",fill:"#F1C54C"}),u("path",{opacity:"0.35",d:"M35.5374 28.3449L31.9327 24.7026C32.7461 23.4723 33.2087 22.0154 33.2087 20.429C33.2087 16.1878 29.8113 12.7397 25.6323 12.7397C21.4533 12.7397 18.0558 16.1878 18.0558 20.429C18.0558 24.6703 21.4533 28.1183 25.6323 28.1183C27.1475 28.1183 28.5671 27.665 29.7475 26.8718L33.3841 30.5303C33.6872 30.8379 34.07 30.9836 34.4688 30.9836C34.8675 30.9836 35.2503 30.8379 35.5534 30.5303C36.1436 29.9314 36.1436 28.9439 35.5374 28.3449ZM25.6163 25.0102C23.128 25.0102 21.1023 22.9543 21.1023 20.429C21.1023 17.9037 23.128 15.8478 25.6163 15.8478C28.1046 15.8478 30.1303 17.9037 30.1303 20.429C30.1303 22.9543 28.1205 25.0102 25.6163 25.0102ZM0 23.3752V7.43005C0 6.45877 0.781568 5.68175 1.72264 5.68175H11.0217C11.8192 5.68175 12.5848 5.42274 13.191 4.9371L14.451 3.96582H30.7045C31.6615 3.96582 32.4271 4.75903 32.4271 5.71412V12.076C30.5769 10.522 28.2003 9.58305 25.6163 9.58305C19.7147 9.58305 14.9296 14.4394 14.9296 20.429C14.9296 22.1126 15.3124 23.7152 15.9823 25.1397H1.72264C0.781568 25.1397 0 24.3465 0 23.3752Z",fill:"url(#paint0_linear_1712_3822)"}),u("defs",{children:R("linearGradient",{id:"paint0_linear_1712_3822",x1:"12.1558",y1:"2.08393",x2:"23.1625",y2:"34.8766",gradientUnits:"userSpaceOnUse",children:[u("stop",{stopColor:"white"}),u("stop",{offset:"0.4173",stopColor:"#E2E2E1",stopOpacity:"0.5827"}),u("stop",{offset:"1",stopColor:"#B4B4B3",stopOpacity:"0"})]})})]})),M1=c.memo(()=>R("svg",{viewBox:"0 0 37 29",children:[u("path",{d:"M31.6151 29H4.6924C2.11158 29 0 26.8866 0 24.3036V4.75506C0 2.11336 2.11158 0 4.6924 0H11.3204C12.6695 0 13.9599 0.587045 14.8397 1.58502L15.7195 2.583C15.9542 2.81781 16.3061 2.99393 16.658 2.99393H31.5564C34.1372 2.99393 36.2488 5.10729 36.2488 7.69028V24.2449C36.3075 26.8866 34.1959 29 31.6151 29ZM4.6924 2.93522C3.75392 2.93522 2.93275 3.75709 2.93275 4.75506V24.3036C2.93275 25.3016 3.75392 26.0648 4.6924 26.0648H31.5564C32.5535 26.0648 33.3161 25.2429 33.3161 24.3036V7.74899C33.3161 6.75101 32.4949 5.98785 31.5564 5.98785H16.7167C15.5436 5.98785 14.3705 5.45951 13.608 4.57895L12.6695 3.52227C12.3176 3.17004 11.8483 2.93522 11.3204 2.93522H4.6924Z",fill:"currentColor"}),u("path",{d:"M28.5044 14.5223H25.8705V11.8885C25.8705 11.0309 25.1968 10.3572 24.3393 10.3572C23.4817 10.3572 22.808 11.0309 22.808 11.8885V14.5223H20.1741C19.3166 14.5223 18.6428 15.1961 18.6428 16.0536C18.6428 16.9111 19.3166 17.5849 20.1741 17.5849H22.808V20.2187C22.808 21.0763 23.4817 21.75 24.3393 21.75C25.1968 21.75 25.8705 21.0763 25.8705 20.2187V17.5849H28.5044C29.3619 17.5849 30.0357 16.9111 30.0357 16.0536C30.0357 15.1961 29.3007 14.5223 28.5044 14.5223Z",fill:"currentColor"})]})),Ki=c.memo(()=>u("svg",{viewBox:"0 0 35 31",children:R("g",{children:[u("path",{id:"Vector",d:"M6.14369 12.5975C5.52312 11.9769 5.58518 10.984 6.20575 10.4255C6.82631 9.80496 7.81922 9.86702 8.37773 10.4876L16.5692 19.0514V1.55142C16.5692 0.682624 17.2518 0 18.1206 0C18.9894 0 19.6721 0.682624 19.6721 1.55142V18.9894L27.8635 10.4255C28.4841 9.80496 29.415 9.80497 30.0355 10.3635C30.6561 10.984 30.6561 11.9149 30.0976 12.5355L19.1756 23.9539C18.8653 24.2642 18.493 24.4504 18.0586 24.4504C17.6242 24.4504 17.2518 24.2642 16.9416 23.9539L6.14369 12.5975Z",fill:"currentColor"}),u("path",{id:"Vector_2",d:"M33.4486 27.8015H1.55142C0.682624 27.8015 0 28.4841 0 29.3529C0 30.2217 0.682624 30.9044 1.55142 30.9044H33.4486C34.3174 30.9044 35 30.2217 35 29.3529C35 28.4841 34.3174 27.8015 33.4486 27.8015Z",fill:"currentColor"})]})})),Ui=c.memo(()=>u("svg",{viewBox:"0 0 35 36",children:R("g",{children:[u("g",{children:u("path",{id:"Vector",d:"M16.8101 35.1979C16.3749 35.1979 16.0485 35.1435 15.9397 35.1435C7.01783 34.3275 0 26.6025 0 17.5718C0 7.88826 7.99706 0 17.7894 0C23.1207 0 28.1257 2.33928 31.553 6.47381C33.6203 8.97629 34.8171 11.9684 34.9803 15.1237C35.2523 20.2919 32.6955 24.1544 27.7449 25.9496C27.3097 26.1128 26.004 26.6569 21.4343 26.4393C21.0535 26.3849 20.7815 26.6569 20.7271 26.7657C20.6183 26.8745 20.5095 27.1465 20.6183 27.4729C20.6183 27.5817 20.6727 27.6361 20.7271 27.6905C21.5431 28.5609 22.7399 30.9002 21.1079 33.2939C19.9654 34.9259 18.007 35.1979 16.8101 35.1979ZM16.3749 31.6618C17.0278 31.7706 17.9526 31.6074 18.1158 31.3354C18.4966 30.737 18.279 30.3018 18.0614 30.0298C17.6262 29.5946 17.3542 29.0505 17.191 28.5065C16.7557 27.2009 17.0278 25.732 17.8982 24.644C18.7686 23.5016 20.1287 22.9031 21.5975 22.9575C24.372 23.1208 25.9496 22.9031 26.4393 22.7399C29.9754 21.4343 31.6074 19.0406 31.4442 15.3957C31.3354 13.002 30.4106 10.7171 28.8329 8.81309C26.1128 5.49458 22.0871 3.59052 17.7894 3.59052C9.90112 3.53611 3.53611 9.84672 3.53611 17.5718C3.53611 24.8072 9.1395 30.9546 16.3205 31.6074C16.3205 31.6618 16.3205 31.6618 16.3749 31.6618Z",fill:"currentColor"})}),u("path",{id:"Vector_2",d:"M10.4452 16.6469C10.4452 18.0614 9.30279 19.2582 7.83394 19.2582C6.36509 19.2582 5.22266 18.1158 5.22266 16.6469C5.22266 15.1781 6.36509 14.0357 7.83394 14.0357C9.30279 14.0357 10.4452 15.1781 10.4452 16.6469ZM13.1109 7.01782C11.6965 7.01782 10.4996 8.16026 10.4996 9.62911C10.4996 11.098 11.6421 12.2404 13.1109 12.2404C14.5798 12.2404 15.7222 11.098 15.7222 9.62911C15.7222 8.16026 14.5798 7.01782 13.1109 7.01782ZM21.8152 7.07222C20.4007 7.07222 19.2039 8.21466 19.2039 9.68351C19.2039 11.1524 20.3463 12.2948 21.8152 12.2948C23.2296 12.2948 24.4265 11.1524 24.4265 9.68351C24.4265 8.21466 23.2296 7.07222 21.8152 7.07222ZM26.9834 13.9268C25.5689 13.9268 24.3721 15.0693 24.3721 16.5381C24.3721 18.007 25.5145 19.1494 26.9834 19.1494C28.3978 19.1494 29.5947 18.007 29.5947 16.5381C29.5947 15.0693 28.4522 13.9268 26.9834 13.9268Z",fill:"currentColor"})]})})),D1=c.memo(()=>R(Ne,{children:[u("svg",{viewBox:"0 0 15 11",fill:"#33302F",className:"fb-arrow",children:u("path",{d:"M2.12944e-08 2.28571C1.49061e-08 1.75 0.172414 1.39286 0.517241 1.03571C1.2069 0.321429 2.24138 0.321429 2.93103 1.03571L7.41379 6.03571L12.069 1.03571C12.7586 0.321429 13.7931 0.321429 14.4828 1.03571C15.1724 1.75 15.1724 2.82143 14.4828 3.53571L8.7931 9.96429C8.44828 10.3214 7.93103 10.5 7.58621 10.5C7.24138 10.5 6.55172 10.1429 6.2069 9.78571L0.517242 3.53571C0.172414 3.17857 2.55533e-08 2.64286 2.12944e-08 2.28571Z"})}),R("svg",{viewBox:"0 0 37 27",children:[R("g",{clipPath:"url(#clip0_2480_1280)",children:[u("path",{d:"M33.8093 26.9684H3.2245C2.82578 26.9684 2.45578 26.8375 2.15485 26.6153C1.69731 26.2789 1.39844 25.7326 1.39844 25.1199V1.86116C1.39844 1.42085 1.55232 1.01516 1.80879 0.696695C2.14459 0.279926 2.6548 0.0126953 3.22382 0.0126953H12.7946C12.9984 0.0126953 13.1995 0.0466184 13.3896 0.111695C13.696 0.216926 13.9723 0.403157 14.1788 0.660003L16.6594 3.61754H16.8482H33.8093C34.2395 3.61754 34.6362 3.77054 34.9494 4.02462C35.3673 4.36385 35.6354 4.88446 35.6354 5.466V25.1199C35.6354 25.7008 35.3796 26.2138 34.9741 26.5509C34.6595 26.8126 34.2546 26.9684 33.8093 26.9684Z",fill:"currentColor"}),u("path",{d:"M31.3859 22H5.6147C5.27517 22 5 21.6639 5 21.2492V7.75079C5 7.33609 5.27517 7 5.6147 7H31.3853C31.7248 7 32 7.33609 32 7.75079V21.2492C32 21.6639 31.7248 22 31.3859 22Z",fill:"white"}),u("path",{d:"M33.809 26.9684H3.22417C2.7509 26.9684 2.31867 26.7793 1.99312 26.4713C1.62791 26.1251 1.39811 25.6287 1.39811 25.0784L0.0302734 14.8412C0.0302734 14.322 0.235449 13.8491 0.566466 13.5064C0.897482 13.1637 1.35366 12.9519 1.85634 12.9519H13.0384C13.5671 12.9519 14.0827 12.8384 14.5567 12.6251C14.833 12.4998 15.095 12.3406 15.3364 12.1488L16.6604 11.1104H16.8492H35.1769C35.5776 11.1104 35.949 11.2454 36.2513 11.4738C36.7061 11.8186 37.0029 12.3745 37.0029 12.999L35.6351 25.0784C35.6351 25.6883 35.3567 26.2338 34.9204 26.58C34.614 26.8237 34.2296 26.9684 33.809 26.9684Z",fill:"currentColor"}),u("path",{opacity:"0.35",d:"M33.809 26.9684H3.22417C2.7509 26.9684 2.31867 26.7793 1.99312 26.4713C1.62791 26.1251 1.39811 25.6287 1.39811 25.0784L0.0302734 14.8412C0.0302734 14.322 0.235449 13.8491 0.566466 13.5064C0.897482 13.1637 1.35366 12.9519 1.85634 12.9519H13.0384C13.5671 12.9519 14.0827 12.8384 14.5567 12.6251C14.833 12.4998 15.095 12.3406 15.3364 12.1488L16.6604 11.1104H16.8492H35.1769C35.5776 11.1104 35.949 11.2454 36.2513 11.4738C36.7061 11.8186 37.0029 12.3745 37.0029 12.999L35.6351 25.0784C35.6351 25.6883 35.3567 26.2338 34.9204 26.58C34.614 26.8237 34.2296 26.9684 33.809 26.9684Z",fill:"url(#paint0_linear_2480_1280)"})]}),R("defs",{children:[R("linearGradient",{id:"paint0_linear_2480_1280",x1:"15.6699",y1:"8.23176",x2:"23.1678",y2:"30.6287",gradientUnits:"userSpaceOnUse",children:[u("stop",{stopColor:"white"}),u("stop",{offset:"0.4173",stopColor:"#DFDFDE",stopOpacity:"0.58"}),u("stop",{offset:"1",stopColor:"#B1B1B0",stopOpacity:"0"})]}),u("clipPath",{id:"clip0_2480_1280",children:u("rect",{width:"37",height:"27",fill:"white"})})]})]})]})),O1=c.memo(()=>R(Ne,{children:[u("svg",{viewBox:"0 0 10 15",className:"fb-arrow",children:u("path",{d:"M1.78571 15C1.25 15 0.892857 14.8276 0.535714 14.4828C-0.178571 13.7931 -0.178571 12.7586 0.535714 12.069L5.53571 7.58621L0.535714 2.93103C-0.178571 2.24138 -0.178571 1.2069 0.535714 0.517241C1.25 -0.172414 2.32143 -0.172414 3.03571 0.517241L9.46429 6.2069C9.82143 6.55172 10 7.06897 10 7.41379C10 7.75862 9.64286 8.44828 9.28571 8.7931L3.03571 14.4828C2.67857 14.8276 2.14286 15 1.78571 15Z",fill:"#8A8A8A"})}),R("svg",{viewBox:"0 0 35 27",children:[u("path",{d:"M33.1333 27.0001H1.86667C0.84 27.0001 0 26.1669 0 25.1486V1.85143C0 0.833145 0.84 0 1.86667 0H11.6511C12.1956 0 12.7244 0.231429 13.0667 0.648001L15.6022 3.61029H33.1333C34.16 3.61029 35 4.44344 35 5.46173V25.1486C35 26.1823 34.1756 27.0001 33.1333 27.0001Z",fill:"currentColor"}),u("path",{opacity:"0.35",d:"M33.1333 26.5063H1.86667C0.84 26.5063 0 25.6732 0 24.6549V7.83773C0 6.81944 0.84 5.9863 1.86667 5.9863H11.9C12.7556 5.9863 13.58 5.70858 14.2489 5.19944L15.6022 4.18115H33.1333C34.16 4.18115 35 5.0143 35 6.03258V24.6549C35 25.6732 34.1756 26.5063 33.1333 26.5063Z",fill:"url(#paint0_linear_1705_1693)"}),u("defs",{children:R("linearGradient",{id:"paint0_linear_1705_1693",x1:"13.1166",y1:"2.19787",x2:"22.2899",y2:"30.1634",gradientUnits:"userSpaceOnUse",children:[u("stop",{stopColor:"white"}),u("stop",{offset:"0.4173",stopColor:"#E3E3E2",stopOpacity:"0.5827"}),u("stop",{offset:"1",stopColor:"#B5B5B4",stopOpacity:"0"})]})})]})]})),zi=c.memo(()=>R("svg",{viewBox:"0 0 35 28",fill:"none",children:[u("path",{d:"M30.3594 2.85144H16.1022C15.8227 2.85144 15.4872 2.73962 15.3195 2.51597L14.4808 1.5655C13.5304 0.559105 12.3003 0 10.9585 0H4.64058C2.06869 0 0 2.06869 0 4.64058V23.3706C0 25.8866 2.06869 27.9553 4.64058 27.9553H30.3594C32.9313 27.9553 35 25.8866 35 23.3147V7.49201C35 4.97604 32.9313 2.85144 30.3594 2.85144ZM31.9808 23.3147C31.9808 24.2093 31.254 24.9361 30.3594 24.9361H4.64058C3.74601 24.9361 3.01917 24.2093 3.01917 23.3147V4.64058C3.01917 3.74601 3.74601 3.01917 4.64058 3.01917H10.9585C11.4058 3.01917 11.853 3.1869 12.1326 3.57827L12.9712 4.52875C13.754 5.42332 14.8722 5.92652 16.0463 5.92652H30.3035C31.1981 5.92652 31.9249 6.65336 31.9249 7.54792V23.3147H31.9808Z",fill:"currentColor"}),u("path",{d:"M28.5704 15.3753C28.5704 15.7667 28.4026 16.1022 28.1231 16.3817L22.8116 21.4137C22.532 21.6932 22.1966 21.805 21.8611 21.805C21.4697 21.805 21.1343 21.6373 20.8547 21.3578C20.2956 20.7987 20.3515 19.9041 20.9106 19.4009L23.7061 16.7731H17.2205C16.4378 16.7731 15.8228 16.1581 15.8228 15.3753C15.8228 14.5926 16.4378 13.9776 17.2205 13.9776H23.7061L20.9106 11.3498C20.3515 10.7907 20.3515 9.95201 20.8547 9.3929C21.4138 8.8338 22.2525 8.8338 22.8116 9.33699L28.1231 14.3689C28.4026 14.6485 28.5704 15.0399 28.5704 15.3753Z",fill:"currentColor"})]})),Wi=c.memo(()=>R("svg",{viewBox:"0 0 27 31",fill:"none",children:[u("path",{d:"M21.6896 0H5.31043C2.3673 0 0 2.30321 0 5.16667V25.8333C0 28.6968 2.3673 31 5.31043 31H21.6896C24.6327 31 27 28.6968 27 25.8333V5.16667C27 2.30321 24.6327 0 21.6896 0ZM23.8009 25.8333C23.8009 26.9538 22.8412 27.8876 21.6896 27.8876H5.31043C4.15877 27.8876 3.19905 26.9538 3.19905 25.8333V5.16667C3.19905 4.04619 4.15877 3.11245 5.31043 3.11245H8.63744V3.92169C8.63744 4.91767 9.46919 5.72691 10.4929 5.72691H16.5071C17.5308 5.72691 18.3626 4.91767 18.3626 3.92169V3.11245H21.6896C22.8412 3.11245 23.8009 4.04619 23.8009 5.16667V25.8333Z",fill:"currentColor"}),u("path",{d:"M18.3626 15.0641H8.82938C7.93365 15.0641 7.22986 14.3794 7.22986 13.5079C7.22986 12.6364 7.93365 11.9517 8.82938 11.9517H18.3626C19.2583 11.9517 19.9621 12.6364 19.9621 13.5079C19.9621 14.3794 19.2583 15.0641 18.3626 15.0641ZM19.5142 20.1685C19.5142 19.297 18.8104 18.6123 17.9147 18.6123H9.27725C8.38152 18.6123 7.67773 19.297 7.67773 20.1685C7.67773 21.04 8.38152 21.7248 9.27725 21.7248H17.9147C18.8104 21.7248 19.5142 20.9778 19.5142 20.1685Z",fill:"currentColor"})]})),R1={default:{editFolderIcon:u(du,{}),deleteFolderIcon:u(uu,{}),sortIcon:u(fu,{}),allFoldersIcon:u(pu,{}),unFoldersIcon:u(hu,{}),singleFolderIcon:u(nc,{}),expandedFolderIcon:u(rc,{}),collapsedFolderIcon:u(oc,{}),searchFolderIcon:u(vu,{}),newFolderIcon:u(mu,{}),downloadFolderIcon:u(bu,{}),folderColorIcon:u(gu,{}),folderColorResetIcon:u(ko,{}),cutFolder:u(yu,{}),pasteFolder:u(Cu,{})},windows:{editFolderIcon:u(S1,{}),deleteFolderIcon:u(x1,{}),sortIcon:u(Hi,{}),allFoldersIcon:u(E1,{}),unFoldersIcon:u($1,{}),singleFolderIcon:u(Lr,{}),expandedFolderIcon:u(Lr,{}),collapsedFolderIcon:u(Lr,{}),searchFolderIcon:u(T1,{}),newFolderIcon:u(M1,{}),downloadFolderIcon:u(Ki,{}),folderColorIcon:u(Ui,{}),folderColorResetIcon:u(ko,{}),cutFolder:u(zi,{}),pasteFolder:u(Wi,{})},dropbox:{editFolderIcon:u(v1,{}),deleteFolderIcon:u(m1,{}),sortIcon:u(Hi,{}),allFoldersIcon:u(b1,{}),unFoldersIcon:u(g1,{}),singleFolderIcon:u(Pr,{}),expandedFolderIcon:u(Pr,{}),collapsedFolderIcon:u(Pr,{}),searchFolderIcon:u(w1,{}),downloadFolderIcon:u(Ki,{}),newFolderIcon:u(_1,{}),folderColorIcon:u(Ui,{}),folderColorResetIcon:u(ko,{}),cutFolder:u(zi,{}),pasteFolder:u(Wi,{})}},N1=fn(["fb-flex fb-items-center fb-justify-center"],{variants:{$theme:{default:"[&_svg]:fb-h-5 [&_svg]:fb-w-5",windows:"[&_svg]:fb-h-[17px] [&_svg]:fb-w-[17px]",dropbox:"[&_svg]:fb-h-[17px] [&_svg]:fb-w-[17px]"},$color:{"":"",default:"fb-text-default",windows:"fb-text-windows",dropbox:"fb-text-dropbox"}}}),k1=dn.i(({$theme:e,$color:n})=>N1({$theme:e,$color:n}));function qe({icon:e,...n}){const{theme:r}=ke();return u(k1,{$theme:r,$color:r,icon:e,...n,children:R1[r][e]})}const P1=fn(["fb-relative fb-flex fb-h-8 [&.fbv-drophover]:fb-bg-default-lighter [&:not(.fbv-drophover)]:fb-cursor-pointer fb-items-center fb-rounded-sm fb-px-8 fb-py-0 fb-leading-8 fb-transition-all fb-duration-200"],{variants:{$theme:{default:"hover:fb-bg-default-lighter aria-selected:fb-bg-default-lighter aria-selected:fb-text-primary [&_svg]:aria-selected:fb-text-primary",windows:"hover:fb-bg-windows",dropbox:"hover:fb-bg-dropbox"}}}),L1=oe.li(({$theme:e})=>P1({$theme:e})),A1=c.forwardRef(({$selected:e,onClick:n,dataId:r,count:t,title:o,className:a,icon:s},i)=>{const{theme:d}=ke();return R(L1,{ref:i,onClick:n,"data-id":r,role:"treeitem","aria-selected":e,className:a,$theme:d,children:[u("span",{className:"fb-absolute fb-left-[7px] fb-flex",children:u(qe,{icon:s})}),u("span",{className:"fb-flex-1 fb-overflow-hidden fb-text-ellipsis fb-whitespace-nowrap",children:o}),u("span",{className:"fb-text-xxs fb-pointer-events-none fb-absolute fb-right-[7px] fb-overflow-hidden fb-text-ellipsis fb-rounded fb-bg-white fb-p-[5px_6px] fb-leading-none fb-text-zinc-800",children:t})]})}),I1=[{id:"-1",title:P("all_files"),icon:"allFoldersIcon",className:"fb-all-files"},{id:"0",title:P("uncategorized"),icon:"unFoldersIcon",className:"fb-uncategorized"}],F1=()=>{const{selectFolder:e}=Nn(),n=c.useRef(null),r=U(a=>a.selectedKeys[0].toString()),t=U(a=>({"-1":a.allAttachmentsCount,0:a.unAttachmentsCount})),{attachmentsBrowser:o}=ke();return c.useEffect(()=>{Ba.forNode(n.current,o)},[]),u("ul",{className:"fb-base-folders fb-m-[8px_16px_8px_0] fb-p-0",children:I1.map(a=>u(A1,{ref:a.id==="0"?n:null,onClick:()=>e([a.id]),dataId:a.id,$selected:a.id===r,className:a.className,count:t[a.id],title:a.title,icon:a.icon},a.id))})},H1=c.memo(F1);/*! @license DOMPurify 3.2.4 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE */const{entries:Sc,setPrototypeOf:Bi,isFrozen:K1,getPrototypeOf:U1,getOwnPropertyDescriptor:z1}=Object;let{freeze:Je,seal:pt,create:xc}=Object,{apply:ha,construct:va}=typeof Reflect<"u"&&Reflect;Je||(Je=function(n){return n});pt||(pt=function(n){return n});ha||(ha=function(n,r,t){return n.apply(r,t)});va||(va=function(n,r){return new n(...r)});const br=et(Array.prototype.forEach),W1=et(Array.prototype.lastIndexOf),Vi=et(Array.prototype.pop),An=et(Array.prototype.push),B1=et(Array.prototype.splice),Ar=et(String.prototype.toLowerCase),Io=et(String.prototype.toString),ji=et(String.prototype.match),In=et(String.prototype.replace),V1=et(String.prototype.indexOf),j1=et(String.prototype.trim),bt=et(Object.prototype.hasOwnProperty),Ze=et(RegExp.prototype.test),Fn=G1(TypeError);function et(e){return function(n){for(var r=arguments.length,t=new Array(r>1?r-1:0),o=1;o<r;o++)t[o-1]=arguments[o];return ha(e,n,t)}}function G1(e){return function(){for(var n=arguments.length,r=new Array(n),t=0;t<n;t++)r[t]=arguments[t];return va(e,r)}}function re(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Ar;Bi&&Bi(e,null);let t=n.length;for(;t--;){let o=n[t];if(typeof o=="string"){const a=r(o);a!==o&&(K1(n)||(n[t]=a),o=a)}e[o]=!0}return e}function Y1(e){for(let n=0;n<e.length;n++)bt(e,n)||(e[n]=null);return e}function nn(e){const n=xc(null);for(const[r,t]of Sc(e))bt(e,r)&&(Array.isArray(t)?n[r]=Y1(t):t&&typeof t=="object"&&t.constructor===Object?n[r]=nn(t):n[r]=t);return n}function Hn(e,n){for(;e!==null;){const t=z1(e,n);if(t){if(t.get)return et(t.get);if(typeof t.value=="function")return et(t.value)}e=U1(e)}function r(){return null}return r}const Gi=Je(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Fo=Je(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ho=Je(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Z1=Je(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ko=Je(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),X1=Je(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Yi=Je(["#text"]),Zi=Je(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Uo=Je(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Xi=Je(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),gr=Je(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),q1=pt(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Q1=pt(/<%[\w\W]*|[\w\W]*%>/gm),J1=pt(/\$\{[\w\W]*/gm),ep=pt(/^data-[\-\w.\u00B7-\uFFFF]+$/),tp=pt(/^aria-[\-\w]+$/),Ec=pt(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),np=pt(/^(?:\w+script|data):/i),rp=pt(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),$c=pt(/^html$/i),op=pt(/^[a-z][.\w]*(-[.\w]+)+$/i);var qi=Object.freeze({__proto__:null,ARIA_ATTR:tp,ATTR_WHITESPACE:rp,CUSTOM_ELEMENT:op,DATA_ATTR:ep,DOCTYPE_NAME:$c,ERB_EXPR:Q1,IS_ALLOWED_URI:Ec,IS_SCRIPT_OR_DATA:np,MUSTACHE_EXPR:q1,TMPLIT_EXPR:J1});const Kn={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},ap=function(){return typeof window>"u"?null:window},ip=function(n,r){if(typeof n!="object"||typeof n.createPolicy!="function")return null;let t=null;const o="data-tt-policy-suffix";r&&r.hasAttribute(o)&&(t=r.getAttribute(o));const a="dompurify"+(t?"#"+t:"");try{return n.createPolicy(a,{createHTML(s){return s},createScriptURL(s){return s}})}catch{return console.warn("TrustedTypes policy "+a+" could not be created."),null}},Qi=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Tc(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ap();const n=Y=>Tc(Y);if(n.version="3.2.4",n.removed=[],!e||!e.document||e.document.nodeType!==Kn.document||!e.Element)return n.isSupported=!1,n;let{document:r}=e;const t=r,o=t.currentScript,{DocumentFragment:a,HTMLTemplateElement:s,Node:i,Element:d,NodeFilter:l,NamedNodeMap:p=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:f,DOMParser:v,trustedTypes:h}=e,m=d.prototype,b=Hn(m,"cloneNode"),y=Hn(m,"remove"),g=Hn(m,"nextSibling"),w=Hn(m,"childNodes"),x=Hn(m,"parentNode");if(typeof s=="function"){const Y=r.createElement("template");Y.content&&Y.content.ownerDocument&&(r=Y.content.ownerDocument)}let C,$="";const{implementation:M,createNodeIterator:D,createDocumentFragment:F,getElementsByTagName:K}=r,{importNode:O}=t;let z=Qi();n.isSupported=typeof Sc=="function"&&typeof x=="function"&&M&&M.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:I,ERB_EXPR:A,TMPLIT_EXPR:N,DATA_ATTR:T,ARIA_ATTR:k,IS_SCRIPT_OR_DATA:L,ATTR_WHITESPACE:V,CUSTOM_ELEMENT:E}=qi;let{IS_ALLOWED_URI:W}=qi,B=null;const Q=re({},[...Gi,...Fo,...Ho,...Ko,...Yi]);let J=null;const he=re({},[...Zi,...Uo,...Xi,...gr]);let te=Object.seal(xc(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ce=null,le=null,we=!0,ae=!0,De=!1,me=!0,Le=!1,Oe=!0,xe=!1,ge=!1,Ke=!1,Se=!1,nt=!1,se=!1,G=!0,de=!1;const ye="user-content-";let Te=!0,Ge=!1,Ue={},at=null;const _t=re({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let vt=null;const Qt=re({},["audio","video","img","source","image","track"]);let bn=null;const hr=re({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),gn="http://www.w3.org/1998/Math/MathML",kt="http://www.w3.org/2000/svg",ee="http://www.w3.org/1999/xhtml";let ue=ee,ze=!1,St=null;const xt=re({},[gn,kt,ee],Io);let yn=re({},["mi","mo","mn","ms","mtext"]),Et=re({},["annotation-xml"]);const Oo=re({},["title","style","font","a","script"]);let Jt=null;const vr=["application/xhtml+xml","text/html"],kn="text/html";let Re=null,Cn=null;const qd=r.createElement("form"),bi=function(S){return S instanceof RegExp||S instanceof Function},Ro=function(){let S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(Cn&&Cn===S)){if((!S||typeof S!="object")&&(S={}),S=nn(S),Jt=vr.indexOf(S.PARSER_MEDIA_TYPE)===-1?kn:S.PARSER_MEDIA_TYPE,Re=Jt==="application/xhtml+xml"?Io:Ar,B=bt(S,"ALLOWED_TAGS")?re({},S.ALLOWED_TAGS,Re):Q,J=bt(S,"ALLOWED_ATTR")?re({},S.ALLOWED_ATTR,Re):he,St=bt(S,"ALLOWED_NAMESPACES")?re({},S.ALLOWED_NAMESPACES,Io):xt,bn=bt(S,"ADD_URI_SAFE_ATTR")?re(nn(hr),S.ADD_URI_SAFE_ATTR,Re):hr,vt=bt(S,"ADD_DATA_URI_TAGS")?re(nn(Qt),S.ADD_DATA_URI_TAGS,Re):Qt,at=bt(S,"FORBID_CONTENTS")?re({},S.FORBID_CONTENTS,Re):_t,ce=bt(S,"FORBID_TAGS")?re({},S.FORBID_TAGS,Re):{},le=bt(S,"FORBID_ATTR")?re({},S.FORBID_ATTR,Re):{},Ue=bt(S,"USE_PROFILES")?S.USE_PROFILES:!1,we=S.ALLOW_ARIA_ATTR!==!1,ae=S.ALLOW_DATA_ATTR!==!1,De=S.ALLOW_UNKNOWN_PROTOCOLS||!1,me=S.ALLOW_SELF_CLOSE_IN_ATTR!==!1,Le=S.SAFE_FOR_TEMPLATES||!1,Oe=S.SAFE_FOR_XML!==!1,xe=S.WHOLE_DOCUMENT||!1,Se=S.RETURN_DOM||!1,nt=S.RETURN_DOM_FRAGMENT||!1,se=S.RETURN_TRUSTED_TYPE||!1,Ke=S.FORCE_BODY||!1,G=S.SANITIZE_DOM!==!1,de=S.SANITIZE_NAMED_PROPS||!1,Te=S.KEEP_CONTENT!==!1,Ge=S.IN_PLACE||!1,W=S.ALLOWED_URI_REGEXP||Ec,ue=S.NAMESPACE||ee,yn=S.MATHML_TEXT_INTEGRATION_POINTS||yn,Et=S.HTML_INTEGRATION_POINTS||Et,te=S.CUSTOM_ELEMENT_HANDLING||{},S.CUSTOM_ELEMENT_HANDLING&&bi(S.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(te.tagNameCheck=S.CUSTOM_ELEMENT_HANDLING.tagNameCheck),S.CUSTOM_ELEMENT_HANDLING&&bi(S.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(te.attributeNameCheck=S.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),S.CUSTOM_ELEMENT_HANDLING&&typeof S.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(te.allowCustomizedBuiltInElements=S.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Le&&(ae=!1),nt&&(Se=!0),Ue&&(B=re({},Yi),J=[],Ue.html===!0&&(re(B,Gi),re(J,Zi)),Ue.svg===!0&&(re(B,Fo),re(J,Uo),re(J,gr)),Ue.svgFilters===!0&&(re(B,Ho),re(J,Uo),re(J,gr)),Ue.mathMl===!0&&(re(B,Ko),re(J,Xi),re(J,gr))),S.ADD_TAGS&&(B===Q&&(B=nn(B)),re(B,S.ADD_TAGS,Re)),S.ADD_ATTR&&(J===he&&(J=nn(J)),re(J,S.ADD_ATTR,Re)),S.ADD_URI_SAFE_ATTR&&re(bn,S.ADD_URI_SAFE_ATTR,Re),S.FORBID_CONTENTS&&(at===_t&&(at=nn(at)),re(at,S.FORBID_CONTENTS,Re)),Te&&(B["#text"]=!0),xe&&re(B,["html","head","body"]),B.table&&(re(B,["tbody"]),delete ce.tbody),S.TRUSTED_TYPES_POLICY){if(typeof S.TRUSTED_TYPES_POLICY.createHTML!="function")throw Fn('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof S.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Fn('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');C=S.TRUSTED_TYPES_POLICY,$=C.createHTML("")}else C===void 0&&(C=ip(h,o)),C!==null&&typeof $=="string"&&($=C.createHTML(""));Je&&Je(S),Cn=S}},gi=re({},[...Fo,...Ho,...Z1]),yi=re({},[...Ko,...X1]),Qd=function(S){let H=x(S);(!H||!H.tagName)&&(H={namespaceURI:ue,tagName:"template"});const j=Ar(S.tagName),be=Ar(H.tagName);return St[S.namespaceURI]?S.namespaceURI===kt?H.namespaceURI===ee?j==="svg":H.namespaceURI===gn?j==="svg"&&(be==="annotation-xml"||yn[be]):!!gi[j]:S.namespaceURI===gn?H.namespaceURI===ee?j==="math":H.namespaceURI===kt?j==="math"&&Et[be]:!!yi[j]:S.namespaceURI===ee?H.namespaceURI===kt&&!Et[be]||H.namespaceURI===gn&&!yn[be]?!1:!yi[j]&&(Oo[j]||!gi[j]):!!(Jt==="application/xhtml+xml"&&St[S.namespaceURI]):!1},$t=function(S){An(n.removed,{element:S});try{x(S).removeChild(S)}catch{y(S)}},mr=function(S,H){try{An(n.removed,{attribute:H.getAttributeNode(S),from:H})}catch{An(n.removed,{attribute:null,from:H})}if(H.removeAttribute(S),S==="is")if(Se||nt)try{$t(H)}catch{}else try{H.setAttribute(S,"")}catch{}},Ci=function(S){let H=null,j=null;if(Ke)S="<remove></remove>"+S;else{const Ae=ji(S,/^[\r\n\t ]+/);j=Ae&&Ae[0]}Jt==="application/xhtml+xml"&&ue===ee&&(S='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+S+"</body></html>");const be=C?C.createHTML(S):S;if(ue===ee)try{H=new v().parseFromString(be,Jt)}catch{}if(!H||!H.documentElement){H=M.createDocument(ue,"template",null);try{H.documentElement.innerHTML=ze?$:be}catch{}}const We=H.body||H.documentElement;return S&&j&&We.insertBefore(r.createTextNode(j),We.childNodes[0]||null),ue===ee?K.call(H,xe?"html":"body")[0]:xe?H.documentElement:We},wi=function(S){return D.call(S.ownerDocument||S,S,l.SHOW_ELEMENT|l.SHOW_COMMENT|l.SHOW_TEXT|l.SHOW_PROCESSING_INSTRUCTION|l.SHOW_CDATA_SECTION,null)},No=function(S){return S instanceof f&&(typeof S.nodeName!="string"||typeof S.textContent!="string"||typeof S.removeChild!="function"||!(S.attributes instanceof p)||typeof S.removeAttribute!="function"||typeof S.setAttribute!="function"||typeof S.namespaceURI!="string"||typeof S.insertBefore!="function"||typeof S.hasChildNodes!="function")},_i=function(S){return typeof i=="function"&&S instanceof i};function Pt(Y,S,H){br(Y,j=>{j.call(n,S,H,Cn)})}const Si=function(S){let H=null;if(Pt(z.beforeSanitizeElements,S,null),No(S))return $t(S),!0;const j=Re(S.nodeName);if(Pt(z.uponSanitizeElement,S,{tagName:j,allowedTags:B}),S.hasChildNodes()&&!_i(S.firstElementChild)&&Ze(/<[/\w]/g,S.innerHTML)&&Ze(/<[/\w]/g,S.textContent)||S.nodeType===Kn.progressingInstruction||Oe&&S.nodeType===Kn.comment&&Ze(/<[/\w]/g,S.data))return $t(S),!0;if(!B[j]||ce[j]){if(!ce[j]&&Ei(j)&&(te.tagNameCheck instanceof RegExp&&Ze(te.tagNameCheck,j)||te.tagNameCheck instanceof Function&&te.tagNameCheck(j)))return!1;if(Te&&!at[j]){const be=x(S)||S.parentNode,We=w(S)||S.childNodes;if(We&&be){const Ae=We.length;for(let rt=Ae-1;rt>=0;--rt){const Tt=b(We[rt],!0);Tt.__removalCount=(S.__removalCount||0)+1,be.insertBefore(Tt,g(S))}}}return $t(S),!0}return S instanceof d&&!Qd(S)||(j==="noscript"||j==="noembed"||j==="noframes")&&Ze(/<\/no(script|embed|frames)/i,S.innerHTML)?($t(S),!0):(Le&&S.nodeType===Kn.text&&(H=S.textContent,br([I,A,N],be=>{H=In(H,be," ")}),S.textContent!==H&&(An(n.removed,{element:S.cloneNode()}),S.textContent=H)),Pt(z.afterSanitizeElements,S,null),!1)},xi=function(S,H,j){if(G&&(H==="id"||H==="name")&&(j in r||j in qd))return!1;if(!(ae&&!le[H]&&Ze(T,H))){if(!(we&&Ze(k,H))){if(!J[H]||le[H]){if(!(Ei(S)&&(te.tagNameCheck instanceof RegExp&&Ze(te.tagNameCheck,S)||te.tagNameCheck instanceof Function&&te.tagNameCheck(S))&&(te.attributeNameCheck instanceof RegExp&&Ze(te.attributeNameCheck,H)||te.attributeNameCheck instanceof Function&&te.attributeNameCheck(H))||H==="is"&&te.allowCustomizedBuiltInElements&&(te.tagNameCheck instanceof RegExp&&Ze(te.tagNameCheck,j)||te.tagNameCheck instanceof Function&&te.tagNameCheck(j))))return!1}else if(!bn[H]){if(!Ze(W,In(j,V,""))){if(!((H==="src"||H==="xlink:href"||H==="href")&&S!=="script"&&V1(j,"data:")===0&&vt[S])){if(!(De&&!Ze(L,In(j,V,"")))){if(j)return!1}}}}}}return!0},Ei=function(S){return S!=="annotation-xml"&&ji(S,E)},$i=function(S){Pt(z.beforeSanitizeAttributes,S,null);const{attributes:H}=S;if(!H||No(S))return;const j={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:J,forceKeepAttr:void 0};let be=H.length;for(;be--;){const We=H[be],{name:Ae,namespaceURI:rt,value:Tt}=We,Pn=Re(Ae);let Ye=Ae==="value"?Tt:j1(Tt);if(j.attrName=Pn,j.attrValue=Ye,j.keepAttr=!0,j.forceKeepAttr=void 0,Pt(z.uponSanitizeAttribute,S,j),Ye=j.attrValue,de&&(Pn==="id"||Pn==="name")&&(mr(Ae,S),Ye=ye+Ye),Oe&&Ze(/((--!?|])>)|<\/(style|title)/i,Ye)){mr(Ae,S);continue}if(j.forceKeepAttr||(mr(Ae,S),!j.keepAttr))continue;if(!me&&Ze(/\/>/i,Ye)){mr(Ae,S);continue}Le&&br([I,A,N],Mi=>{Ye=In(Ye,Mi," ")});const Ti=Re(S.nodeName);if(xi(Ti,Pn,Ye)){if(C&&typeof h=="object"&&typeof h.getAttributeType=="function"&&!rt)switch(h.getAttributeType(Ti,Pn)){case"TrustedHTML":{Ye=C.createHTML(Ye);break}case"TrustedScriptURL":{Ye=C.createScriptURL(Ye);break}}try{rt?S.setAttributeNS(rt,Ae,Ye):S.setAttribute(Ae,Ye),No(S)?$t(S):Vi(n.removed)}catch{}}}Pt(z.afterSanitizeAttributes,S,null)},Jd=function Y(S){let H=null;const j=wi(S);for(Pt(z.beforeSanitizeShadowDOM,S,null);H=j.nextNode();)Pt(z.uponSanitizeShadowNode,H,null),Si(H),$i(H),H.content instanceof a&&Y(H.content);Pt(z.afterSanitizeShadowDOM,S,null)};return n.sanitize=function(Y){let S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=null,j=null,be=null,We=null;if(ze=!Y,ze&&(Y="<!-->"),typeof Y!="string"&&!_i(Y))if(typeof Y.toString=="function"){if(Y=Y.toString(),typeof Y!="string")throw Fn("dirty is not a string, aborting")}else throw Fn("toString is not a function");if(!n.isSupported)return Y;if(ge||Ro(S),n.removed=[],typeof Y=="string"&&(Ge=!1),Ge){if(Y.nodeName){const Tt=Re(Y.nodeName);if(!B[Tt]||ce[Tt])throw Fn("root node is forbidden and cannot be sanitized in-place")}}else if(Y instanceof i)H=Ci("<!---->"),j=H.ownerDocument.importNode(Y,!0),j.nodeType===Kn.element&&j.nodeName==="BODY"||j.nodeName==="HTML"?H=j:H.appendChild(j);else{if(!Se&&!Le&&!xe&&Y.indexOf("<")===-1)return C&&se?C.createHTML(Y):Y;if(H=Ci(Y),!H)return Se?null:se?$:""}H&&Ke&&$t(H.firstChild);const Ae=wi(Ge?Y:H);for(;be=Ae.nextNode();)Si(be),$i(be),be.content instanceof a&&Jd(be.content);if(Ge)return Y;if(Se){if(nt)for(We=F.call(H.ownerDocument);H.firstChild;)We.appendChild(H.firstChild);else We=H;return(J.shadowroot||J.shadowrootmode)&&(We=O.call(t,We,!0)),We}let rt=xe?H.outerHTML:H.innerHTML;return xe&&B["!doctype"]&&H.ownerDocument&&H.ownerDocument.doctype&&H.ownerDocument.doctype.name&&Ze($c,H.ownerDocument.doctype.name)&&(rt="<!DOCTYPE "+H.ownerDocument.doctype.name+`>
`+rt),Le&&br([I,A,N],Tt=>{rt=In(rt,Tt," ")}),C&&se?C.createHTML(rt):rt},n.setConfig=function(){let Y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Ro(Y),ge=!0},n.clearConfig=function(){Cn=null,ge=!1},n.isValidAttribute=function(Y,S,H){Cn||Ro({});const j=Re(Y),be=Re(S);return xi(j,be,H)},n.addHook=function(Y,S){typeof S=="function"&&An(z[Y],S)},n.removeHook=function(Y,S){if(S!==void 0){const H=W1(z[Y],S);return H===-1?void 0:B1(z[Y],H,1)[0]}return Vi(z[Y])},n.removeHooks=function(Y){z[Y]=[]},n.removeAllHooks=function(){z=Qi()},n}var Va=Tc();const sp=({items:e})=>{const{selectFolder:n}=Nn(),r=t=>{t.preventDefault(),t.stopPropagation(),n([t.target.closest("a").dataset.id])};return u(Ne,{children:e.map(t=>u(c.Fragment,{children:u("li",{className:"before:fb-text-default last:fb-text-default fb-m-0 fb-text-primary before:fb-content-['/'] last:fb-pointer-events-none",children:u("a",{className:"fb-cursor-pointer fb-px-[10px] fb-py-0 fb-leading-none !fb-text-inherit fb-no-underline focus:fb-shadow-none focus:fb-outline-none","data-id":t.id,onClick:r,children:u("span",{dangerouslySetInnerHTML:{__html:Va.sanitize(t.title)}})})})},t.id))})},{jQuery:zo,fbv_data:Ji}=window,cp=()=>{const[e,n]=Ve.useState(null),r=U(t=>wu(t.treeData,t.selectedKeys[0]));return c.useEffect(()=>{document.body.classList.contains("upload-php")&&Ji.is_upload_screen==="1"&&Ji.user_settings.SHOW_BREAD_CRUMB&&!document.getElementById("fbv-breadcrumb")&&(zo(`<div id="fbv-breadcrumb" dir="${window.fbv_data.is_rtl?"rtl":"ltr"}"></div>`).insertAfter(".media-toolbar.wp-filter"),n(document.getElementById("fbv-breadcrumb")),zo(document).trigger("filebird-breadcrumb-mounted",[zo("#fbv-breadcrumb")]))},[]),e?or.createPortal(R("ul",{className:"last-chil fb-m-0 fb-flex fb-flex-wrap fb-items-center fb-justify-start fb-border fb-border-solid fb-border-[#c3c4c7] fb-bg-white fb-p-2 fb-shadow-[0_1px_1px_rgba(0,0,0,0.04)]",children:[u("li",{className:"peer fb-text-default fb-m-0 fb-flex fb-items-center fb-p-[0_10px_0_7px] fb-text-[18px]",children:u("span",{className:"dashicons dashicons-admin-home"})},"home"),r?u(sp,{items:r}):null]}),e):null},lp=()=>window.typenow==="post"?P("posts"):window.typenow==="page"?P("pages"):P("files"),dp=window.fbv_data.asset_url+"img/empty.svg",up=()=>{const e=U(r=>!r.treeData.length&&!r.editFolderId&&!r.isLoading&&!r.folderQuery.search),n=U(r=>r.createChildFolder);return e?R("div",{className:"fb-mr-[10px] fb-px-0 fb-py-[30px] fb-text-center",children:[u("img",{src:dp,alt:"Empty",className:"fb-w-full fb-max-w-[300px]"}),u("h2",{className:"fb-mb-[10px] fb-text-xl fb-font-medium fb-text-[#2d2d2d]",children:P("add_your_first_folder")}),u("p",{className:"fb-text-sm fb-text-[#5a677a]",children:P("add_your_first_folder_description")+" "+lp()+"."}),u("button",{className:"button button-primary fb-px-5 fb-py-0 fb-text-sm fb-min-h-8",onClick:()=>n(),children:P("add_folder")})]}):null},{user_settings:fp}=window.fbv_data,pp=dn.li`
  fb-relative fb-group
  fb-m-[10px] fb-flex fb-select-none fb-items-center fb-whitespace-nowrap fb-rounded-[3px]
  fb-bg-white fb-py-[15px] fb-pl-[10px] fb-pr-10 fb-text-[14px] fb-shadow-[0_2px_5px_0_rgba(0,0,0,0.06)] fb-transition-colors fb-duration-200
  after:fb-absolute after:fb-right-[15px] after:fb-select-none after:fb-rounded-sm
  after:fb-border after:fb-border-solid after:fb-border-[#c3c4c7] after:fb-p-[5px_6px] after:fb-align-middle after:fb-text-[10px] after:fb-leading-none after:fb-shadow-[0_1px_1px_rgba(0,0,0,0.04)] after:fb-content-[attr(data-count)] hover:fb-bg-primary hover:fb-text-white
  `,hp=({node:e,count:n})=>{const{theme:r}=ke(),{selectFolder:t}=Nn(),o=c.useRef(null),{attachmentsBrowser:a}=ke();return c.useEffect(()=>{o.current&&(jQuery(o.current).hasClass("ui-droppable")||Ba.forNode(o.current,a))},[]),R(pp,{ref:o,onClick:()=>t([e.id]),"data-id":e.id,"data-count":n,children:[u(qe,{$color:"",icon:"singleFolderIcon",className:ve("fb-mr-[10px] fb-flex-[0_0_20px] group-hover:fb-text-white"),style:{color:e.color||fp.THEME.colors[r]}}),u(rr,{sideOffset:20,content:Fa(e.title),requiresOverflowToShow:!0,children:s=>u("div",{ref:s,className:"fb-tree-title-inside",children:u("span",{dangerouslySetInnerHTML:{__html:Va.sanitize(e.title)}})})})]},e.id)},vp=oe.ul`fb-relative fb-m-0 fb-grid fb-grid-cols-right-folder fb-gap-0 fb-p-0 fb-pb-[10px]`,mp=()=>{const[e,n]=c.useState(null),r=U(o=>_u(o.treeData,o.selectedKeys[0])),t=U(o=>o.attachmentsCount.display);return c.useEffect(()=>{document.body.classList.contains("upload-php")&&!document.getElementById("fbv-grid-folders")&&(jQuery(`<div id="fbv-grid-folders" dir="${window.fbv_data.is_rtl?"rtl":"ltr"}"></div>`).insertBefore("ul.attachments"),n(document.getElementById("fbv-grid-folders")))},[]),e&&r&&or.createPortal(u(Ne,{children:u(vp,{children:r.children.map(o=>u(hp,{node:o,count:t[o.id]??0},o.id))})}),e)},bp=fn(["button fb-flex fb-items-center fb-leading-normal fb-text-white [&_i]:fb-mr-2 fb-m-0 fb-min-h-8"],{variants:{theme:{default:"button-primary",windows:"button-primary",dropbox:["fb-rounded-none fb-border fb-border-solid fb-border-transparent fb-bg-[#0360fe] fb-text-white","hocus:fb-bg-[#0057e5] hocus:fb-text-white hocus:fb-shadow-none hocus:fb-outline-none"]}},defaultVariants:{theme:"default"}}),gp=oe.button(({theme:e})=>bp({theme:e})),{i18n:yp}=window.fbv_data,Cp=()=>{const e=U(t=>t.checkable||!!t.editFolderId||t.isLoading),{theme:n}=ke(),r=U(t=>t.createChildFolder);return R("div",{className:"fb-header fb-flex fb-items-center fb-justify-between fb-pb-[16px] fb-pl-0 fb-pr-[16px] fb-pt-[19px]",children:[u("h2",{className:"fb-m-0 fb-p-0",children:"FileBird"}),R(gp,{theme:n,disabled:e,onClick:()=>r(),children:[u(qe,{className:ve("fb-text-white",{"fb-text-disabled":e},{"[&_svg]:fb-h-3 [&_svg]:fb-w-3":n==="dropbox"}),icon:"newFolderIcon"}),u("span",{children:yp.new_folder})]})]})},wp=c.memo(Cp),Mc=(e,n)=>{let r=[];return e.forEach(t=>{const o="-".repeat(n);r.push({...t,title:o+t.title}),t.children&&t.children.length&&(n++,r=r.concat(Mc(t.children,n)),n--)}),[...r]},_p=(e,n=null)=>{const r=e+"=",t=document.cookie.split(";");for(let o=0;o<t.length;o++){let a=t[o];for(;a.charAt(0)==" ";)a=a.substring(1,a.length);if(a.indexOf(r)==0)return a.substring(r.length,a.length)}return n},{i18n:Wo,tree_mode:Sp}=window.fbv_data,xp=Sp==="attachment"?Ia:tc,Ep=({disabled:e,onChange:n,value:r})=>{const[t,o]=c.useState(!1),[a,s]=c.useState([]);c.useEffect(()=>{o(!0),xp.getTree().then(d=>{s(d.tree),o(!1)}).catch(()=>{o(!1)})},[]);const i=Mc(a,0);return R("select",{value:r,disabled:e||t,onChange:n,className:"form-control",style:{width:"100%",maxWidth:"100%"},children:[u("option",{value:"-1",children:Wo.all_files},"-1"),u("option",{value:"0",children:Wo.uncategorized},"0"),u("option",{value:"-2",children:Wo.most_recent_folder},"-2"),i.map(d=>u("option",{value:d.id,children:d.title},d.id))]})},Bo="focusScope.autoFocusOnMount",Vo="focusScope.autoFocusOnUnmount",es={bubbles:!1,cancelable:!0},ja=c.forwardRef((e,n)=>{const{loop:r=!1,trapped:t=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...s}=e,[i,d]=c.useState(null),l=cn(o),p=cn(a),f=c.useRef(null),v=je(n,b=>d(b)),h=c.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;c.useEffect(()=>{if(t){let b=function(x){if(h.paused||!i)return;const C=x.target;i.contains(C)?f.current=C:jt(f.current,{select:!0})},y=function(x){if(h.paused||!i)return;const C=x.relatedTarget;C!==null&&(i.contains(C)||jt(f.current,{select:!0}))},g=function(x){if(document.activeElement===document.body)for(const $ of x)$.removedNodes.length>0&&jt(i)};document.addEventListener("focusin",b),document.addEventListener("focusout",y);const w=new MutationObserver(g);return i&&w.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",b),document.removeEventListener("focusout",y),w.disconnect()}}},[t,i,h.paused]),c.useEffect(()=>{if(i){ns.add(h);const b=document.activeElement;if(!i.contains(b)){const g=new CustomEvent(Bo,es);i.addEventListener(Bo,l),i.dispatchEvent(g),g.defaultPrevented||($p(Rp(Dc(i)),{select:!0}),document.activeElement===b&&jt(i))}return()=>{i.removeEventListener(Bo,l),setTimeout(()=>{const g=new CustomEvent(Vo,es);i.addEventListener(Vo,p),i.dispatchEvent(g),g.defaultPrevented||jt(b??document.body,{select:!0}),i.removeEventListener(Vo,p),ns.remove(h)},0)}}},[i,l,p,h]);const m=c.useCallback(b=>{if(!r&&!t||h.paused)return;const y=b.key==="Tab"&&!b.altKey&&!b.ctrlKey&&!b.metaKey,g=document.activeElement;if(y&&g){const w=b.currentTarget,[x,C]=Tp(w);x&&C?!b.shiftKey&&g===C?(b.preventDefault(),r&&jt(x,{select:!0})):b.shiftKey&&g===x&&(b.preventDefault(),r&&jt(C,{select:!0})):g===w&&b.preventDefault()}},[r,t,h.paused]);return c.createElement(Me.div,fe({tabIndex:-1},s,{ref:v,onKeyDown:m}))});function $p(e,{select:n=!1}={}){const r=document.activeElement;for(const t of e)if(jt(t,{select:n}),document.activeElement!==r)return}function Tp(e){const n=Dc(e),r=ts(n,e),t=ts(n.reverse(),e);return[r,t]}function Dc(e){const n=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:t=>{const o=t.tagName==="INPUT"&&t.type==="hidden";return t.disabled||t.hidden||o?NodeFilter.FILTER_SKIP:t.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)n.push(r.currentNode);return n}function ts(e,n){for(const r of e)if(!Mp(r,{upTo:n}))return r}function Mp(e,{upTo:n}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(n!==void 0&&e===n)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Dp(e){return e instanceof HTMLInputElement&&"select"in e}function jt(e,{select:n=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&Dp(e)&&n&&e.select()}}const ns=Op();function Op(){let e=[];return{add(n){const r=e[0];n!==r&&(r==null||r.pause()),e=rs(e,n),e.unshift(n)},remove(n){var r;e=rs(e,n),(r=e[0])===null||r===void 0||r.resume()}}}function rs(e,n){const r=[...e],t=r.indexOf(n);return t!==-1&&r.splice(t,1),r}function Rp(e){return e.filter(n=>n.tagName!=="A")}let jo=0;function Ga(){c.useEffect(()=>{var e,n;const r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(e=r[0])!==null&&e!==void 0?e:os()),document.body.insertAdjacentElement("beforeend",(n=r[1])!==null&&n!==void 0?n:os()),jo++,()=>{jo===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),jo--}},[])}function os(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var Dt=function(){return Dt=Object.assign||function(n){for(var r,t=1,o=arguments.length;t<o;t++){r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(n[a]=r[a])}return n},Dt.apply(this,arguments)};function Oc(e,n){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)n.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(r[t[o]]=e[t[o]]);return r}function Np(e,n,r){if(r||arguments.length===2)for(var t=0,o=n.length,a;t<o;t++)(a||!(t in n))&&(a||(a=Array.prototype.slice.call(n,0,t)),a[t]=n[t]);return e.concat(a||Array.prototype.slice.call(n))}var Ir="right-scroll-bar-position",Fr="width-before-scroll-bar",kp="with-scroll-bars-hidden",Pp="--removed-body-scroll-bar-size";function Lp(e,n){return typeof e=="function"?e(n):e&&(e.current=n),e}function Ap(e,n){var r=c.useState(function(){return{value:e,callback:n,facade:{get current(){return r.value},set current(t){var o=r.value;o!==t&&(r.value=t,r.callback(t,o))}}}})[0];return r.callback=n,r.facade}function Ip(e,n){return Ap(n||null,function(r){return e.forEach(function(t){return Lp(t,r)})})}function Fp(e){return e}function Hp(e,n){n===void 0&&(n=Fp);var r=[],t=!1,o={read:function(){if(t)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(a){var s=n(a,t);return r.push(s),function(){r=r.filter(function(i){return i!==s})}},assignSyncMedium:function(a){for(t=!0;r.length;){var s=r;r=[],s.forEach(a)}r={push:function(i){return a(i)},filter:function(){return r}}},assignMedium:function(a){t=!0;var s=[];if(r.length){var i=r;r=[],i.forEach(a),s=r}var d=function(){var p=s;s=[],p.forEach(a)},l=function(){return Promise.resolve().then(d)};l(),r={push:function(p){s.push(p),l()},filter:function(p){return s=s.filter(p),r}}}};return o}function Kp(e){e===void 0&&(e={});var n=Hp(null);return n.options=Dt({async:!0,ssr:!1},e),n}var Rc=function(e){var n=e.sideCar,r=Oc(e,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var t=n.read();if(!t)throw new Error("Sidecar medium not found");return c.createElement(t,Dt({},r))};Rc.isSideCarExport=!0;function Up(e,n){return e.useMedium(n),Rc}var Nc=Kp(),Go=function(){},to=c.forwardRef(function(e,n){var r=c.useRef(null),t=c.useState({onScrollCapture:Go,onWheelCapture:Go,onTouchMoveCapture:Go}),o=t[0],a=t[1],s=e.forwardProps,i=e.children,d=e.className,l=e.removeScrollBar,p=e.enabled,f=e.shards,v=e.sideCar,h=e.noIsolation,m=e.inert,b=e.allowPinchZoom,y=e.as,g=y===void 0?"div":y,w=Oc(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),x=v,C=Ip([r,n]),$=Dt(Dt({},w),o);return c.createElement(c.Fragment,null,p&&c.createElement(x,{sideCar:Nc,removeScrollBar:l,shards:f,noIsolation:h,inert:m,setCallbacks:a,allowPinchZoom:!!b,lockRef:r}),s?c.cloneElement(c.Children.only(i),Dt(Dt({},$),{ref:C})):c.createElement(g,Dt({},$,{className:d,ref:C}),i))});to.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};to.classNames={fullWidth:Fr,zeroRight:Ir};var as,zp=function(){if(as)return as;if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Wp(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var n=zp();return n&&e.setAttribute("nonce",n),e}function Bp(e,n){e.styleSheet?e.styleSheet.cssText=n:e.appendChild(document.createTextNode(n))}function Vp(e){var n=document.head||document.getElementsByTagName("head")[0];n.appendChild(e)}var jp=function(){var e=0,n=null;return{add:function(r){e==0&&(n=Wp())&&(Bp(n,r),Vp(n)),e++},remove:function(){e--,!e&&n&&(n.parentNode&&n.parentNode.removeChild(n),n=null)}}},Gp=function(){var e=jp();return function(n,r){c.useEffect(function(){return e.add(n),function(){e.remove()}},[n&&r])}},kc=function(){var e=Gp(),n=function(r){var t=r.styles,o=r.dynamic;return e(t,o),null};return n},Yp={left:0,top:0,right:0,gap:0},Yo=function(e){return parseInt(e||"",10)||0},Zp=function(e){var n=window.getComputedStyle(document.body),r=n[e==="padding"?"paddingLeft":"marginLeft"],t=n[e==="padding"?"paddingTop":"marginTop"],o=n[e==="padding"?"paddingRight":"marginRight"];return[Yo(r),Yo(t),Yo(o)]},Xp=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Yp;var n=Zp(e),r=document.documentElement.clientWidth,t=window.innerWidth;return{left:n[0],top:n[1],right:n[2],gap:Math.max(0,t-r+n[2]-n[0])}},qp=kc(),Qp=function(e,n,r,t){var o=e.left,a=e.top,s=e.right,i=e.gap;return r===void 0&&(r="margin"),`
  .`.concat(kp,` {
   overflow: hidden `).concat(t,`;
   padding-right: `).concat(i,"px ").concat(t,`;
  }
  body {
    overflow: hidden `).concat(t,`;
    overscroll-behavior: contain;
    `).concat([n&&"position: relative ".concat(t,";"),r==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(i,"px ").concat(t,`;
    `),r==="padding"&&"padding-right: ".concat(i,"px ").concat(t,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ir,` {
    right: `).concat(i,"px ").concat(t,`;
  }
  
  .`).concat(Fr,` {
    margin-right: `).concat(i,"px ").concat(t,`;
  }
  
  .`).concat(Ir," .").concat(Ir,` {
    right: 0 `).concat(t,`;
  }
  
  .`).concat(Fr," .").concat(Fr,` {
    margin-right: 0 `).concat(t,`;
  }
  
  body {
    `).concat(Pp,": ").concat(i,`px;
  }
`)},Jp=function(e){var n=e.noRelative,r=e.noImportant,t=e.gapMode,o=t===void 0?"margin":t,a=c.useMemo(function(){return Xp(o)},[o]);return c.createElement(qp,{styles:Qp(a,!n,o,r?"":"!important")})},ma=!1;if(typeof window<"u")try{var yr=Object.defineProperty({},"passive",{get:function(){return ma=!0,!0}});window.addEventListener("test",yr,yr),window.removeEventListener("test",yr,yr)}catch{ma=!1}var wn=ma?{passive:!1}:!1,e2=function(e){return e.tagName==="TEXTAREA"},Pc=function(e,n){var r=window.getComputedStyle(e);return r[n]!=="hidden"&&!(r.overflowY===r.overflowX&&!e2(e)&&r[n]==="visible")},t2=function(e){return Pc(e,"overflowY")},n2=function(e){return Pc(e,"overflowX")},is=function(e,n){var r=n;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var t=Lc(e,r);if(t){var o=Ac(e,r),a=o[1],s=o[2];if(a>s)return!0}r=r.parentNode}while(r&&r!==document.body);return!1},r2=function(e){var n=e.scrollTop,r=e.scrollHeight,t=e.clientHeight;return[n,r,t]},o2=function(e){var n=e.scrollLeft,r=e.scrollWidth,t=e.clientWidth;return[n,r,t]},Lc=function(e,n){return e==="v"?t2(n):n2(n)},Ac=function(e,n){return e==="v"?r2(n):o2(n)},a2=function(e,n){return e==="h"&&n==="rtl"?-1:1},i2=function(e,n,r,t,o){var a=a2(e,window.getComputedStyle(n).direction),s=a*t,i=r.target,d=n.contains(i),l=!1,p=s>0,f=0,v=0;do{var h=Ac(e,i),m=h[0],b=h[1],y=h[2],g=b-y-a*m;(m||g)&&Lc(e,i)&&(f+=g,v+=m),i=i.parentNode}while(!d&&i!==document.body||d&&(n.contains(i)||n===i));return(p&&(o&&f===0||!o&&s>f)||!p&&(o&&v===0||!o&&-s>v))&&(l=!0),l},Cr=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ss=function(e){return[e.deltaX,e.deltaY]},cs=function(e){return e&&"current"in e?e.current:e},s2=function(e,n){return e[0]===n[0]&&e[1]===n[1]},c2=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},l2=0,_n=[];function d2(e){var n=c.useRef([]),r=c.useRef([0,0]),t=c.useRef(),o=c.useState(l2++)[0],a=c.useState(function(){return kc()})[0],s=c.useRef(e);c.useEffect(function(){s.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var b=Np([e.lockRef.current],(e.shards||[]).map(cs),!0).filter(Boolean);return b.forEach(function(y){return y.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),b.forEach(function(y){return y.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=c.useCallback(function(b,y){if("touches"in b&&b.touches.length===2)return!s.current.allowPinchZoom;var g=Cr(b),w=r.current,x="deltaX"in b?b.deltaX:w[0]-g[0],C="deltaY"in b?b.deltaY:w[1]-g[1],$,M=b.target,D=Math.abs(x)>Math.abs(C)?"h":"v";if("touches"in b&&D==="h"&&M.type==="range")return!1;var F=is(D,M);if(!F)return!0;if(F?$=D:($=D==="v"?"h":"v",F=is(D,M)),!F)return!1;if(!t.current&&"changedTouches"in b&&(x||C)&&(t.current=$),!$)return!0;var K=t.current||$;return i2(K,y,b,K==="h"?x:C,!0)},[]),d=c.useCallback(function(b){var y=b;if(!(!_n.length||_n[_n.length-1]!==a)){var g="deltaY"in y?ss(y):Cr(y),w=n.current.filter(function($){return $.name===y.type&&$.target===y.target&&s2($.delta,g)})[0];if(w&&w.should){y.cancelable&&y.preventDefault();return}if(!w){var x=(s.current.shards||[]).map(cs).filter(Boolean).filter(function($){return $.contains(y.target)}),C=x.length>0?i(y,x[0]):!s.current.noIsolation;C&&y.cancelable&&y.preventDefault()}}},[]),l=c.useCallback(function(b,y,g,w){var x={name:b,delta:y,target:g,should:w};n.current.push(x),setTimeout(function(){n.current=n.current.filter(function(C){return C!==x})},1)},[]),p=c.useCallback(function(b){r.current=Cr(b),t.current=void 0},[]),f=c.useCallback(function(b){l(b.type,ss(b),b.target,i(b,e.lockRef.current))},[]),v=c.useCallback(function(b){l(b.type,Cr(b),b.target,i(b,e.lockRef.current))},[]);c.useEffect(function(){return _n.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",d,wn),document.addEventListener("touchmove",d,wn),document.addEventListener("touchstart",p,wn),function(){_n=_n.filter(function(b){return b!==a}),document.removeEventListener("wheel",d,wn),document.removeEventListener("touchmove",d,wn),document.removeEventListener("touchstart",p,wn)}},[]);var h=e.removeScrollBar,m=e.inert;return c.createElement(c.Fragment,null,m?c.createElement(a,{styles:c2(o)}):null,h?c.createElement(Jp,{gapMode:"margin"}):null)}const u2=Up(Nc,d2);var Ic=c.forwardRef(function(e,n){return c.createElement(to,Dt({},e,{ref:n,sideCar:u2}))});Ic.classNames=to.classNames;const Ya=Ic;var f2=function(e){if(typeof document>"u")return null;var n=Array.isArray(e)?e[0]:e;return n.ownerDocument.body},Sn=new WeakMap,wr=new WeakMap,_r={},Zo=0,Fc=function(e){return e&&(e.host||Fc(e.parentNode))},p2=function(e,n){return n.map(function(r){if(e.contains(r))return r;var t=Fc(r);return t&&e.contains(t)?t:(console.error("aria-hidden",r,"in not contained inside",e,". Doing nothing"),null)}).filter(function(r){return!!r})},h2=function(e,n,r,t){var o=p2(n,Array.isArray(e)?e:[e]);_r[r]||(_r[r]=new WeakMap);var a=_r[r],s=[],i=new Set,d=new Set(o),l=function(f){!f||i.has(f)||(i.add(f),l(f.parentNode))};o.forEach(l);var p=function(f){!f||d.has(f)||Array.prototype.forEach.call(f.children,function(v){if(i.has(v))p(v);else{var h=v.getAttribute(t),m=h!==null&&h!=="false",b=(Sn.get(v)||0)+1,y=(a.get(v)||0)+1;Sn.set(v,b),a.set(v,y),s.push(v),b===1&&m&&wr.set(v,!0),y===1&&v.setAttribute(r,"true"),m||v.setAttribute(t,"true")}})};return p(n),i.clear(),Zo++,function(){s.forEach(function(f){var v=Sn.get(f)-1,h=a.get(f)-1;Sn.set(f,v),a.set(f,h),v||(wr.has(f)||f.removeAttribute(t),wr.delete(f)),h||f.removeAttribute(r)}),Zo--,Zo||(Sn=new WeakMap,Sn=new WeakMap,wr=new WeakMap,_r={})}},Za=function(e,n,r){r===void 0&&(r="data-aria-hidden");var t=Array.from(Array.isArray(e)?e:[e]),o=n||f2(e);return o?(t.push.apply(t,Array.from(o.querySelectorAll("[aria-live]"))),h2(t,o,r,"aria-hidden")):function(){return null}};const Hc="Dialog",[Kc,t5]=Kt(Hc),[v2,wt]=Kc(Hc),m2=e=>{const{__scopeDialog:n,children:r,open:t,defaultOpen:o,onOpenChange:a,modal:s=!0}=e,i=c.useRef(null),d=c.useRef(null),[l=!1,p]=un({prop:t,defaultProp:o,onChange:a});return c.createElement(v2,{scope:n,triggerRef:i,contentRef:d,contentId:It(),titleId:It(),descriptionId:It(),open:l,onOpenChange:p,onOpenToggle:c.useCallback(()=>p(f=>!f),[p]),modal:s},r)},b2="DialogTrigger",g2=c.forwardRef((e,n)=>{const{__scopeDialog:r,...t}=e,o=wt(b2,r),a=je(n,o.triggerRef);return c.createElement(Me.button,X({type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Xa(o.open)},t,{ref:a,onClick:q(e.onClick,o.onOpenToggle)}))}),Uc="DialogPortal",[y2,zc]=Kc(Uc,{forceMount:void 0}),C2=e=>{const{__scopeDialog:n,forceMount:r,children:t,container:o}=e,a=wt(Uc,n);return c.createElement(y2,{scope:n,forceMount:r},c.Children.map(t,s=>c.createElement(Ut,{present:r||a.open},c.createElement(Ka,{asChild:!0,container:o},s))))},ba="DialogOverlay",w2=c.forwardRef((e,n)=>{const r=zc(ba,e.__scopeDialog),{forceMount:t=r.forceMount,...o}=e,a=wt(ba,e.__scopeDialog);return a.modal?c.createElement(Ut,{present:t||a.open},c.createElement(_2,X({},o,{ref:n}))):null}),_2=c.forwardRef((e,n)=>{const{__scopeDialog:r,...t}=e,o=wt(ba,r);return c.createElement(Ya,{as:Zn,allowPinchZoom:!0,shards:[o.contentRef]},c.createElement(Me.div,X({"data-state":Xa(o.open)},t,{ref:n,style:{pointerEvents:"auto",...t.style}})))}),Xn="DialogContent",S2=c.forwardRef((e,n)=>{const r=zc(Xn,e.__scopeDialog),{forceMount:t=r.forceMount,...o}=e,a=wt(Xn,e.__scopeDialog);return c.createElement(Ut,{present:t||a.open},a.modal?c.createElement(x2,X({},o,{ref:n})):c.createElement(E2,X({},o,{ref:n})))}),x2=c.forwardRef((e,n)=>{const r=wt(Xn,e.__scopeDialog),t=c.useRef(null),o=je(n,r.contentRef,t);return c.useEffect(()=>{const a=t.current;if(a)return Za(a)},[]),c.createElement(Wc,X({},e,{ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:q(e.onCloseAutoFocus,a=>{var s;a.preventDefault(),(s=r.triggerRef.current)===null||s===void 0||s.focus()}),onPointerDownOutside:q(e.onPointerDownOutside,a=>{const s=a.detail.originalEvent,i=s.button===0&&s.ctrlKey===!0;(s.button===2||i)&&a.preventDefault()}),onFocusOutside:q(e.onFocusOutside,a=>a.preventDefault())}))}),E2=c.forwardRef((e,n)=>{const r=wt(Xn,e.__scopeDialog),t=c.useRef(!1),o=c.useRef(!1);return c.createElement(Wc,X({},e,{ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var s;if((s=e.onCloseAutoFocus)===null||s===void 0||s.call(e,a),!a.defaultPrevented){var i;t.current||(i=r.triggerRef.current)===null||i===void 0||i.focus(),a.preventDefault()}t.current=!1,o.current=!1},onInteractOutside:a=>{var s,i;(s=e.onInteractOutside)===null||s===void 0||s.call(e,a),a.defaultPrevented||(t.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const d=a.target;((i=r.triggerRef.current)===null||i===void 0?void 0:i.contains(d))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}}))}),Wc=c.forwardRef((e,n)=>{const{__scopeDialog:r,trapFocus:t,onOpenAutoFocus:o,onCloseAutoFocus:a,...s}=e,i=wt(Xn,r),d=c.useRef(null),l=je(n,d);return Ga(),c.createElement(c.Fragment,null,c.createElement(ja,{asChild:!0,loop:!0,trapped:t,onMountAutoFocus:o,onUnmountAutoFocus:a},c.createElement(Ha,X({role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":Xa(i.open)},s,{ref:l,onDismiss:()=>i.onOpenChange(!1)}))),!1)}),$2="DialogTitle",T2=c.forwardRef((e,n)=>{const{__scopeDialog:r,...t}=e,o=wt($2,r);return c.createElement(Me.h2,X({id:o.titleId},t,{ref:n}))}),M2="DialogDescription",D2=c.forwardRef((e,n)=>{const{__scopeDialog:r,...t}=e,o=wt(M2,r);return c.createElement(Me.p,X({id:o.descriptionId},t,{ref:n}))}),O2="DialogClose",R2=c.forwardRef((e,n)=>{const{__scopeDialog:r,...t}=e,o=wt(O2,r);return c.createElement(Me.button,X({type:"button"},t,{ref:n,onClick:q(e.onClick,()=>o.onOpenChange(!1))}))});function Xa(e){return e?"open":"closed"}const N2=m2,k2=g2,P2=C2,L2=w2,A2=S2,I2=T2,F2=D2,H2=R2,K2=oe(I2)`fb-m-0 fb-font-medium fb-text-black fb-text-[17px] fb-p-[20px] fb-border-b fb-border-[#e3e3e3] fb-leading-6`,U2=oe(F2)`fb-mt-[10px] fb-mb-5 fb-mx-0 fb-text-black fb-text-[15px] fb-leading-[1.5]`,z2=oe(A2)`fb-bg-white fb-rounded fb-shadow-[0_10px_38px_-10px_rgba(14,28,22,0.35),0_10px_20px_-15px_rgba(14,28,22,0.2)]
  fb-fixed fb-top-1/2 fb-left-1/2 -fb-translate-x-1/2 rtl:fb-translate-x-1/2 -fb-translate-y-1/2 fb-w-[90vw] fb-max-w-[440px] fb-max-h-[85vh] fb-z-biggest
  focus:fb-outline-none fb-animate-modal-fade
`,W2=oe(L2)`fb-bg-[rgba(0,0,0,0.4)] fb-fixed fb-inset-0 fb-z-biggest fb-animate-modal-overlay-fade`,B2=oe.button`
  fb-rounded fb-h-[25px] fb-w-[25px] fb-inline-flex fb-items-center fb-justify-center fb-text-[#777] fb-absolute fb-top-5 fb-right-5 fb-bg-[#f3f5f6] fb-border-1 fb-border-solid fb-border-[#f3f5f6] fb-cursor-pointer
  hover:fb-bg-[#f1f1f1] hover:fb-border-primary hover:fb-text-primary [&_svg]:fb-w-[inherit] [&_svg]:fb-h-[inherit]
`,V2=({title:e,trigger:n,description:r,children:t,open:o,onClose:a})=>R(N2,{open:o,children:[n&&u(k2,{asChild:!0,children:n}),R(P2,{children:[u(W2,{}),R(z2,{onPointerDownOutside:()=>a(),dir:window.fbv_data.is_rtl?"rtl":"ltr",children:[u(K2,{children:e}),r&&u(U2,{children:r}),u("div",{className:"fb-flex fb-flex-col fb-pl-5 fb-pr-5 fb-pb-5",children:t}),u(H2,{asChild:!0,children:u(B2,{"aria-label":"Close",onClick:()=>a(),children:u("svg",{"data-v-1fde3d8e":"",viewBox:"0 0 320 512",children:u("path",{"data-v-1fde3d8e":"",fill:"currentColor",d:"M193.94 256L296.5 153.44l21.15-21.15c3.12-3.12 3.12-8.19 0-11.31l-22.63-22.63c-3.12-3.12-8.19-3.12-11.31 0L160 222.06 36.29 98.34c-3.12-3.12-8.19-3.12-11.31 0L2.34 120.97c-3.12 3.12-3.12 8.19 0 11.31L126.06 256 2.34 379.71c-3.12 3.12-3.12 8.19 0 11.31l22.63 22.63c3.12 3.12 8.19 3.12 11.31 0L160 289.94 262.56 392.5l21.15 21.15c3.12 3.12 8.19 3.12 11.31 0l22.63-22.63c3.12-3.12 3.12-8.19 0-11.31L193.94 256z"})})})})]})]})]});function Bc(e){const n=e+"CollectionProvider",[r,t]=Kt(n),[o,a]=r(n,{collectionRef:{current:null},itemMap:new Map}),s=h=>{const{scope:m,children:b}=h,y=Ve.useRef(null),g=Ve.useRef(new Map).current;return Ve.createElement(o,{scope:m,itemMap:g,collectionRef:y},b)},i=e+"CollectionSlot",d=Ve.forwardRef((h,m)=>{const{scope:b,children:y}=h,g=a(i,b),w=je(m,g.collectionRef);return Ve.createElement(Zn,{ref:w},y)}),l=e+"CollectionItemSlot",p="data-radix-collection-item",f=Ve.forwardRef((h,m)=>{const{scope:b,children:y,...g}=h,w=Ve.useRef(null),x=je(m,w),C=a(l,b);return Ve.useEffect(()=>(C.itemMap.set(w,{ref:w,...g}),()=>void C.itemMap.delete(w))),Ve.createElement(Zn,{[p]:"",ref:x},y)});function v(h){const m=a(e+"CollectionConsumer",h);return Ve.useCallback(()=>{const y=m.collectionRef.current;if(!y)return[];const g=Array.from(y.querySelectorAll(`[${p}]`));return Array.from(m.itemMap.values()).sort((C,$)=>g.indexOf(C.ref.current)-g.indexOf($.ref.current))},[m.collectionRef,m.itemMap])}return[{Provider:s,Slot:d,ItemSlot:f},v,t]}const Xo="rovingFocusGroup.onEntryFocus",j2={bubbles:!1,cancelable:!0},qa="RovingFocusGroup",[ga,Vc,G2]=Bc(qa),[Y2,no]=Kt(qa,[G2]),[Z2,X2]=Y2(qa),q2=c.forwardRef((e,n)=>c.createElement(ga.Provider,{scope:e.__scopeRovingFocusGroup},c.createElement(ga.Slot,{scope:e.__scopeRovingFocusGroup},c.createElement(Q2,fe({},e,{ref:n}))))),Q2=c.forwardRef((e,n)=>{const{__scopeRovingFocusGroup:r,orientation:t,loop:o=!1,dir:a,currentTabStopId:s,defaultCurrentTabStopId:i,onCurrentTabStopIdChange:d,onEntryFocus:l,...p}=e,f=c.useRef(null),v=je(n,f),h=Wa(a),[m=null,b]=un({prop:s,defaultProp:i,onChange:d}),[y,g]=c.useState(!1),w=cn(l),x=Vc(r),C=c.useRef(!1),[$,M]=c.useState(0);return c.useEffect(()=>{const D=f.current;if(D)return D.addEventListener(Xo,w),()=>D.removeEventListener(Xo,w)},[w]),c.createElement(Z2,{scope:r,orientation:t,dir:h,loop:o,currentTabStopId:m,onItemFocus:c.useCallback(D=>b(D),[b]),onItemShiftTab:c.useCallback(()=>g(!0),[]),onFocusableItemAdd:c.useCallback(()=>M(D=>D+1),[]),onFocusableItemRemove:c.useCallback(()=>M(D=>D-1),[])},c.createElement(Me.div,fe({tabIndex:y||$===0?-1:0,"data-orientation":t},p,{ref:v,style:{outline:"none",...e.style},onMouseDown:q(e.onMouseDown,()=>{C.current=!0}),onFocus:q(e.onFocus,D=>{const F=!C.current;if(D.target===D.currentTarget&&F&&!y){const K=new CustomEvent(Xo,j2);if(D.currentTarget.dispatchEvent(K),!K.defaultPrevented){const O=x().filter(T=>T.focusable),z=O.find(T=>T.active),I=O.find(T=>T.id===m),N=[z,I,...O].filter(Boolean).map(T=>T.ref.current);jc(N)}}C.current=!1}),onBlur:q(e.onBlur,()=>g(!1))})))}),J2="RovingFocusGroupItem",eh=c.forwardRef((e,n)=>{const{__scopeRovingFocusGroup:r,focusable:t=!0,active:o=!1,tabStopId:a,...s}=e,i=It(),d=a||i,l=X2(J2,r),p=l.currentTabStopId===d,f=Vc(r),{onFocusableItemAdd:v,onFocusableItemRemove:h}=l;return c.useEffect(()=>{if(t)return v(),()=>h()},[t,v,h]),c.createElement(ga.ItemSlot,{scope:r,id:d,focusable:t,active:o},c.createElement(Me.span,fe({tabIndex:p?0:-1,"data-orientation":l.orientation},s,{ref:n,onMouseDown:q(e.onMouseDown,m=>{t?l.onItemFocus(d):m.preventDefault()}),onFocus:q(e.onFocus,()=>l.onItemFocus(d)),onKeyDown:q(e.onKeyDown,m=>{if(m.key==="Tab"&&m.shiftKey){l.onItemShiftTab();return}if(m.target!==m.currentTarget)return;const b=rh(m,l.orientation,l.dir);if(b!==void 0){m.preventDefault();let g=f().filter(w=>w.focusable).map(w=>w.ref.current);if(b==="last")g.reverse();else if(b==="prev"||b==="next"){b==="prev"&&g.reverse();const w=g.indexOf(m.currentTarget);g=l.loop?oh(g,w+1):g.slice(w+1)}setTimeout(()=>jc(g))}})})))}),th={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function nh(e,n){return n!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function rh(e,n,r){const t=nh(e.key,r);if(!(n==="vertical"&&["ArrowLeft","ArrowRight"].includes(t))&&!(n==="horizontal"&&["ArrowUp","ArrowDown"].includes(t)))return th[t]}function jc(e){const n=document.activeElement;for(const r of e)if(r===n||(r.focus(),document.activeElement!==n))return}function oh(e,n){return e.map((r,t)=>e[(n+t)%e.length])}const Gc=q2,Yc=eh,Zc="Radio",[ah,Xc]=Kt(Zc),[ih,n5]=ah(Zc),sh=c.forwardRef((e,n)=>{const{__scopeRadio:r,name:t,checked:o=!1,required:a,disabled:s,value:i="on",onCheck:d,...l}=e,[p,f]=c.useState(null),v=je(n,b=>f(b)),h=c.useRef(!1),m=p?!!p.closest("form"):!0;return c.createElement(ih,{scope:r,checked:o,disabled:s},c.createElement(Me.button,X({type:"button",role:"radio","aria-checked":o,"data-state":lh(o),"data-disabled":s?"":void 0,disabled:s,value:i},l,{ref:v,onClick:q(e.onClick,b=>{o||d==null||d(),m&&(h.current=b.isPropagationStopped(),h.current||b.stopPropagation())})})),m&&c.createElement(ch,{control:p,bubbles:!h.current,name:t,value:i,checked:o,required:a,disabled:s,style:{transform:"translateX(-100%)"}}))}),ch=e=>{const{control:n,checked:r,bubbles:t=!0,...o}=e,a=c.useRef(null),s=Su(r),i=xu(n);return c.useEffect(()=>{const d=a.current,l=window.HTMLInputElement.prototype,f=Object.getOwnPropertyDescriptor(l,"checked").set;if(s!==r&&f){const v=new Event("click",{bubbles:t});f.call(d,r),d.dispatchEvent(v)}},[s,r,t]),c.createElement("input",X({type:"radio","aria-hidden":!0,defaultChecked:r},o,{tabIndex:-1,ref:a,style:{...e.style,...i,position:"absolute",pointerEvents:"none",opacity:0,margin:0}}))};function lh(e){return e?"checked":"unchecked"}const dh=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],qc="RadioGroup",[uh,r5]=Kt(qc,[no,Xc]),Qc=no(),fh=Xc(),[ph,hh]=uh(qc),vh=c.forwardRef((e,n)=>{const{__scopeRadioGroup:r,name:t,defaultValue:o,value:a,required:s=!1,disabled:i=!1,orientation:d,dir:l,loop:p=!0,onValueChange:f,...v}=e,h=Qc(r),m=Wa(l),[b,y]=un({prop:a,defaultProp:o,onChange:f});return c.createElement(ph,{scope:r,name:t,required:s,disabled:i,value:b,onValueChange:y},c.createElement(Gc,X({asChild:!0},h,{orientation:d,dir:m,loop:p}),c.createElement(Me.div,X({role:"radiogroup","aria-required":s,"aria-orientation":d,"data-disabled":i?"":void 0,dir:m},v,{ref:n}))))}),mh="RadioGroupItem",bh=c.forwardRef((e,n)=>{const{__scopeRadioGroup:r,disabled:t,...o}=e,a=hh(mh,r),s=a.disabled||t,i=Qc(r),d=fh(r),l=c.useRef(null),p=je(n,l),f=a.value===o.value,v=c.useRef(!1);return c.useEffect(()=>{const h=b=>{dh.includes(b.key)&&(v.current=!0)},m=()=>v.current=!1;return document.addEventListener("keydown",h),document.addEventListener("keyup",m),()=>{document.removeEventListener("keydown",h),document.removeEventListener("keyup",m)}},[]),c.createElement(Yc,X({asChild:!0},i,{focusable:!s,active:f}),c.createElement(sh,X({disabled:s,required:a.required,checked:f},d,o,{name:a.name,ref:p,onCheck:()=>a.onValueChange(o.value),onKeyDown:q(h=>{h.key==="Enter"&&h.preventDefault()}),onFocus:q(o.onFocus,()=>{var h;v.current&&((h=l.current)===null||h===void 0||h.click())})})))}),gh=vh,yh=bh,Ch=oe(gh)`fb-inline-flex fb-rounded fb-border fb-border-solid fb-border-[#d9d9d9] fb-bg-[#d9d9d9]`,ls=oe(yh)`
  fb-all-unset fb-bg-white fb-text-black fb-h-[35px] fb-w-1/3 fb-flex fb-text-[15px] fb-leading-none fb-items-center fb-ml-[1px] fb-justify-center fb-cursor-pointer
  first:fb-ml-0 first:fb-rounded-tl-[4px] first:fb-rounded-bl-[4px]
  last:fb-rounded-tr-[4px] last:fb-rounded-br-[4px]
  hover:fb-text-primary
  data-[state="checked"]:fb-bg-primary data-[state="checked"]:fb-text-white data-[state="checked"]:fb-shadow-[0_0_0_1px_#2271b1]
  focus:fb-relative
`,wh={default:{title:"Default",needActive:!1},windows:{title:"Windows",needActive:!0},dropbox:{title:"Dropbox",needActive:!0}},{user_settings:_h,license:ds}=window.fbv_data,Sh=()=>{const e=Yt(f=>f.modalOpen),n=Yt(f=>f.setModalOpen),r=Yt(f=>f.isFetching),t=Yt(f=>f.updateUserSetting),{theme:o,setTheme:a}=ke(),[s,i]=c.useState(_h),d=f=>{i({...s,THEME:{...s.THEME,name:f}}),a(f||"default")},l=f=>{i({...s,DEFAULT_FOLDER:parseInt(f.target.value)})},p=!ds.status&&s.THEME.name!=="default";return R(V2,{open:e,onClose:()=>n(!1),title:P("fb_settings"),children:[R("div",{className:"fb-flex fb-flex-col fb-gap-3",children:[R("div",{className:"fb-flex fb-flex-col fb-gap-3",children:[u("div",{className:"fb-font-medium",children:P("select_default_startup_folder")}),u(Ep,{value:s.DEFAULT_FOLDER,disabled:!1,onChange:l})]}),R("div",{className:"fb-flex fb-flex-col fb-gap-3",children:[R("div",{className:"fb-font-medium",children:[P("theme"),":"]}),u(Ch,{defaultValue:o,"aria-label":"Theme",onValueChange:d,children:Object.entries(wh).map(([f,v])=>u(c.Fragment,{children:v.needActive&&!ds.status?u(rr,{content:P("active_to_update"),sideOffset:5,children:()=>u(ls,{value:f,children:v.title})}):u(ls,{value:f,children:v.title})},f))})]}),u("div",{className:"fb-flex fb-flex-col fb-gap-3",children:R("div",{className:"fb-flex fb-items-center fb-gap-3",children:[u("input",{type:"checkbox",id:"show-breadcrumb",checked:!!s.SHOW_BREAD_CRUMB,onChange:f=>{i({...s,SHOW_BREAD_CRUMB:f.target.checked})}}),u("label",{htmlFor:"show-breadcrumb",children:P("show_breadcrumb")})]})})]}),u("div",{className:"fb-clear-both fb-mx-0 fb-my-4 fb-flex fb-w-full fb-min-w-full fb-border-0 fb-border-t fb-border-solid fb-border-[rgba(5,5,5,0.06)]"}),p&&u("div",{className:"notice inline notice-warning notice-alt fb-mb-4 fb-mt-0 fb-mx-0",children:u("p",{dangerouslySetInnerHTML:{__html:P("theme_alert")}})}),R("div",{className:"fb-flex fb-justify-end fb-gap-3",children:[u("button",{className:"button",onClick:()=>n(!1),children:P("cancel")}),u("button",{className:ve("button button-primary",{"updating-message":r}),onClick:()=>t(s),disabled:p,children:P("save")})]})]})},{data_import:xh,data_import_url:Eh}=window.fbv_data,$h=()=>{const[e,n]=c.useState(!1),[r,t]=c.useState(!1),[o,a]=c.useState([]);c.useEffect(()=>{const i=Object.values(xh.plugins).filter(d=>!d.completed&&d.counter>3&&!d.noThanks);a(i)},[]);const s=()=>{const i=o.length>1?"all":o[0].prefix,d={method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":window.fbv_data.rest_nonce},body:JSON.stringify({site:i})};n(!0),fetch(`${window.fbv_data.json_url}/fb-no-thanks`,d).then(l=>{if(l.ok)return l.json();throw new Error("Failed to cancel")}).then(()=>{setTimeout(()=>{n(!1),t(!0)},500)}).catch(()=>{setTimeout(()=>{n(!1),t(!0)},500)})};return u(Ne,{children:o.length>0&&!r&&R("div",{className:"notice fb-mt-[8px] fb-mr-4 fb-mb-0 fb-ml-0 fb-border-l",children:[u("div",{children:u("h3",{children:P("import_folder_to_filebird")})}),o.length===1&&u("div",{children:u("p",{dangerouslySetInnerHTML:{__html:o[0].description}})}),o.length>1&&u("div",{children:u("p",{children:P("import_some_folders")})}),u("div",{children:R("p",{children:[u("a",{rel:"noopener noreferrer",target:"_blank",href:Eh,className:"button button-primary",children:u("strong",{children:P("update_noti_btn")})})," ",u("a",{rel:"noopener noreferrer",className:ve("button",{"updating-message":e}),onClick:s,children:u("strong",{children:P("no_thanks")})})]})})]})})};var Qa={exports:{}},ar={},ro={exports:{}},Jc={},el={exports:{}},Th="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Mh=Th,Dh=Mh;function tl(){}function nl(){}nl.resetWarningCache=tl;var Oh=function(){function e(t,o,a,s,i,d){if(d!==Dh){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}e.isRequired=e;function n(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:n,element:e,elementType:e,instanceOf:n,node:e,objectOf:n,oneOf:n,oneOfType:n,shape:n,exact:n,checkPropTypes:nl,resetWarningCache:tl};return r.PropTypes=r,r};el.exports=Oh();var ir=el.exports;const Rh=Eu($u);var _e={},Rt={};Object.defineProperty(Rt,"__esModule",{value:!0});Rt.dontSetMe=Ah;Rt.findInArray=Nh;Rt.int=Lh;Rt.isFunction=kh;Rt.isNum=Ph;function Nh(e,n){for(let r=0,t=e.length;r<t;r++)if(n.apply(n,[e[r],r,e]))return e[r]}function kh(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Function]"}function Ph(e){return typeof e=="number"&&!isNaN(e)}function Lh(e){return parseInt(e,10)}function Ah(e,n,r){if(e[n])return new Error("Invalid prop ".concat(n," passed to ").concat(r," - do not set this, set it on the child."))}var pn={};Object.defineProperty(pn,"__esModule",{value:!0});pn.browserPrefixToKey=ol;pn.browserPrefixToStyle=Ih;pn.default=void 0;pn.getPrefix=rl;const qo=["Moz","Webkit","O","ms"];function rl(){var e;let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"transform";if(typeof window>"u")return"";const r=(e=window.document)===null||e===void 0||(e=e.documentElement)===null||e===void 0?void 0:e.style;if(!r||n in r)return"";for(let t=0;t<qo.length;t++)if(ol(n,qo[t])in r)return qo[t];return""}function ol(e,n){return n?"".concat(n).concat(Fh(e)):e}function Ih(e,n){return n?"-".concat(n.toLowerCase(),"-").concat(e):e}function Fh(e){let n="",r=!0;for(let t=0;t<e.length;t++)r?(n+=e[t].toUpperCase(),r=!1):e[t]==="-"?r=!0:n+=e[t];return n}pn.default=rl();Object.defineProperty(_e,"__esModule",{value:!0});_e.addClassName=sl;_e.addEvent=Uh;_e.addUserSelectStyles=Qh;_e.createCSSTransform=Yh;_e.createSVGTransform=Zh;_e.getTouch=Xh;_e.getTouchIdentifier=qh;_e.getTranslation=Ja;_e.innerHeight=Vh;_e.innerWidth=jh;_e.matchesSelector=il;_e.matchesSelectorAndParentsTo=Kh;_e.offsetXYFromParent=Gh;_e.outerHeight=Wh;_e.outerWidth=Bh;_e.removeClassName=cl;_e.removeEvent=zh;_e.removeUserSelectStyles=Jh;var st=Rt,us=Hh(pn);function al(e){if(typeof WeakMap!="function")return null;var n=new WeakMap,r=new WeakMap;return(al=function(t){return t?r:n})(e)}function Hh(e,n){if(!n&&e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=al(n);if(r&&r.has(e))return r.get(e);var t={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if(a!=="default"&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(t,a,s):t[a]=e[a]}return t.default=e,r&&r.set(e,t),t}let Sr="";function il(e,n){return Sr||(Sr=(0,st.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],function(r){return(0,st.isFunction)(e[r])})),(0,st.isFunction)(e[Sr])?e[Sr](n):!1}function Kh(e,n,r){let t=e;do{if(il(t,n))return!0;if(t===r)return!1;t=t.parentNode}while(t);return!1}function Uh(e,n,r,t){if(!e)return;const o={capture:!0,...t};e.addEventListener?e.addEventListener(n,r,o):e.attachEvent?e.attachEvent("on"+n,r):e["on"+n]=r}function zh(e,n,r,t){if(!e)return;const o={capture:!0,...t};e.removeEventListener?e.removeEventListener(n,r,o):e.detachEvent?e.detachEvent("on"+n,r):e["on"+n]=null}function Wh(e){let n=e.clientHeight;const r=e.ownerDocument.defaultView.getComputedStyle(e);return n+=(0,st.int)(r.borderTopWidth),n+=(0,st.int)(r.borderBottomWidth),n}function Bh(e){let n=e.clientWidth;const r=e.ownerDocument.defaultView.getComputedStyle(e);return n+=(0,st.int)(r.borderLeftWidth),n+=(0,st.int)(r.borderRightWidth),n}function Vh(e){let n=e.clientHeight;const r=e.ownerDocument.defaultView.getComputedStyle(e);return n-=(0,st.int)(r.paddingTop),n-=(0,st.int)(r.paddingBottom),n}function jh(e){let n=e.clientWidth;const r=e.ownerDocument.defaultView.getComputedStyle(e);return n-=(0,st.int)(r.paddingLeft),n-=(0,st.int)(r.paddingRight),n}function Gh(e,n,r){const o=n===n.ownerDocument.body?{left:0,top:0}:n.getBoundingClientRect(),a=(e.clientX+n.scrollLeft-o.left)/r,s=(e.clientY+n.scrollTop-o.top)/r;return{x:a,y:s}}function Yh(e,n){const r=Ja(e,n,"px");return{[(0,us.browserPrefixToKey)("transform",us.default)]:r}}function Zh(e,n){return Ja(e,n,"")}function Ja(e,n,r){let{x:t,y:o}=e,a="translate(".concat(t).concat(r,",").concat(o).concat(r,")");if(n){const s="".concat(typeof n.x=="string"?n.x:n.x+r),i="".concat(typeof n.y=="string"?n.y:n.y+r);a="translate(".concat(s,", ").concat(i,")")+a}return a}function Xh(e,n){return e.targetTouches&&(0,st.findInArray)(e.targetTouches,r=>n===r.identifier)||e.changedTouches&&(0,st.findInArray)(e.changedTouches,r=>n===r.identifier)}function qh(e){if(e.targetTouches&&e.targetTouches[0])return e.targetTouches[0].identifier;if(e.changedTouches&&e.changedTouches[0])return e.changedTouches[0].identifier}function Qh(e){if(!e)return;let n=e.getElementById("react-draggable-style-el");n||(n=e.createElement("style"),n.type="text/css",n.id="react-draggable-style-el",n.innerHTML=`.react-draggable-transparent-selection *::-moz-selection {all: inherit;}
`,n.innerHTML+=`.react-draggable-transparent-selection *::selection {all: inherit;}
`,e.getElementsByTagName("head")[0].appendChild(n)),e.body&&sl(e.body,"react-draggable-transparent-selection")}function Jh(e){if(e)try{if(e.body&&cl(e.body,"react-draggable-transparent-selection"),e.selection)e.selection.empty();else{const n=(e.defaultView||window).getSelection();n&&n.type!=="Caret"&&n.removeAllRanges()}}catch{}}function sl(e,n){e.classList?e.classList.add(n):e.className.match(new RegExp("(?:^|\\s)".concat(n,"(?!\\S)")))||(e.className+=" ".concat(n))}function cl(e,n){e.classList?e.classList.remove(n):e.className=e.className.replace(new RegExp("(?:^|\\s)".concat(n,"(?!\\S)"),"g"),"")}var Nt={};Object.defineProperty(Nt,"__esModule",{value:!0});Nt.canDragX=n0;Nt.canDragY=r0;Nt.createCoreData=a0;Nt.createDraggableData=i0;Nt.getBoundPosition=e0;Nt.getControlPosition=o0;Nt.snapToGrid=t0;var it=Rt,$n=_e;function e0(e,n,r){if(!e.props.bounds)return[n,r];let{bounds:t}=e.props;t=typeof t=="string"?t:s0(t);const o=ei(e);if(typeof t=="string"){const{ownerDocument:a}=o,s=a.defaultView;let i;if(t==="parent"?i=o.parentNode:i=a.querySelector(t),!(i instanceof s.HTMLElement))throw new Error('Bounds selector "'+t+'" could not find an element.');const d=i,l=s.getComputedStyle(o),p=s.getComputedStyle(d);t={left:-o.offsetLeft+(0,it.int)(p.paddingLeft)+(0,it.int)(l.marginLeft),top:-o.offsetTop+(0,it.int)(p.paddingTop)+(0,it.int)(l.marginTop),right:(0,$n.innerWidth)(d)-(0,$n.outerWidth)(o)-o.offsetLeft+(0,it.int)(p.paddingRight)-(0,it.int)(l.marginRight),bottom:(0,$n.innerHeight)(d)-(0,$n.outerHeight)(o)-o.offsetTop+(0,it.int)(p.paddingBottom)-(0,it.int)(l.marginBottom)}}return(0,it.isNum)(t.right)&&(n=Math.min(n,t.right)),(0,it.isNum)(t.bottom)&&(r=Math.min(r,t.bottom)),(0,it.isNum)(t.left)&&(n=Math.max(n,t.left)),(0,it.isNum)(t.top)&&(r=Math.max(r,t.top)),[n,r]}function t0(e,n,r){const t=Math.round(n/e[0])*e[0],o=Math.round(r/e[1])*e[1];return[t,o]}function n0(e){return e.props.axis==="both"||e.props.axis==="x"}function r0(e){return e.props.axis==="both"||e.props.axis==="y"}function o0(e,n,r){const t=typeof n=="number"?(0,$n.getTouch)(e,n):null;if(typeof n=="number"&&!t)return null;const o=ei(r),a=r.props.offsetParent||o.offsetParent||o.ownerDocument.body;return(0,$n.offsetXYFromParent)(t||e,a,r.props.scale)}function a0(e,n,r){const t=!(0,it.isNum)(e.lastX),o=ei(e);return t?{node:o,deltaX:0,deltaY:0,lastX:n,lastY:r,x:n,y:r}:{node:o,deltaX:n-e.lastX,deltaY:r-e.lastY,lastX:e.lastX,lastY:e.lastY,x:n,y:r}}function i0(e,n){const r=e.props.scale;return{node:n.node,x:e.state.x+n.deltaX/r,y:e.state.y+n.deltaY/r,deltaX:n.deltaX/r,deltaY:n.deltaY/r,lastX:e.state.x,lastY:e.state.y}}function s0(e){return{left:e.left,top:e.top,right:e.right,bottom:e.bottom}}function ei(e){const n=e.findDOMNode();if(!n)throw new Error("<DraggableCore>: Unmounted during event!");return n}var oo={},ao={};Object.defineProperty(ao,"__esModule",{value:!0});ao.default=c0;function c0(){}Object.defineProperty(oo,"__esModule",{value:!0});oo.default=void 0;var Qo=d0(c),ot=ti(ir),l0=ti(or),Be=_e,Wt=Nt,Jo=Rt,Un=ti(ao);function ti(e){return e&&e.__esModule?e:{default:e}}function ll(e){if(typeof WeakMap!="function")return null;var n=new WeakMap,r=new WeakMap;return(ll=function(t){return t?r:n})(e)}function d0(e,n){if(!n&&e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=ll(n);if(r&&r.has(e))return r.get(e);var t={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if(a!=="default"&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(t,a,s):t[a]=e[a]}return t.default=e,r&&r.set(e,t),t}function Xe(e,n,r){return n=u0(n),n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function u0(e){var n=f0(e,"string");return typeof n=="symbol"?n:String(n)}function f0(e,n){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var t=r.call(e,n||"default");if(typeof t!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}const mt={touch:{start:"touchstart",move:"touchmove",stop:"touchend"},mouse:{start:"mousedown",move:"mousemove",stop:"mouseup"}};let Bt=mt.mouse,io=class extends Qo.Component{constructor(){super(...arguments),Xe(this,"dragging",!1),Xe(this,"lastX",NaN),Xe(this,"lastY",NaN),Xe(this,"touchIdentifier",null),Xe(this,"mounted",!1),Xe(this,"handleDragStart",n=>{if(this.props.onMouseDown(n),!this.props.allowAnyClick&&typeof n.button=="number"&&n.button!==0)return!1;const r=this.findDOMNode();if(!r||!r.ownerDocument||!r.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");const{ownerDocument:t}=r;if(this.props.disabled||!(n.target instanceof t.defaultView.Node)||this.props.handle&&!(0,Be.matchesSelectorAndParentsTo)(n.target,this.props.handle,r)||this.props.cancel&&(0,Be.matchesSelectorAndParentsTo)(n.target,this.props.cancel,r))return;n.type==="touchstart"&&n.preventDefault();const o=(0,Be.getTouchIdentifier)(n);this.touchIdentifier=o;const a=(0,Wt.getControlPosition)(n,o,this);if(a==null)return;const{x:s,y:i}=a,d=(0,Wt.createCoreData)(this,s,i);(0,Un.default)("DraggableCore: handleDragStart: %j",d),(0,Un.default)("calling",this.props.onStart),!(this.props.onStart(n,d)===!1||this.mounted===!1)&&(this.props.enableUserSelectHack&&(0,Be.addUserSelectStyles)(t),this.dragging=!0,this.lastX=s,this.lastY=i,(0,Be.addEvent)(t,Bt.move,this.handleDrag),(0,Be.addEvent)(t,Bt.stop,this.handleDragStop))}),Xe(this,"handleDrag",n=>{const r=(0,Wt.getControlPosition)(n,this.touchIdentifier,this);if(r==null)return;let{x:t,y:o}=r;if(Array.isArray(this.props.grid)){let i=t-this.lastX,d=o-this.lastY;if([i,d]=(0,Wt.snapToGrid)(this.props.grid,i,d),!i&&!d)return;t=this.lastX+i,o=this.lastY+d}const a=(0,Wt.createCoreData)(this,t,o);if((0,Un.default)("DraggableCore: handleDrag: %j",a),this.props.onDrag(n,a)===!1||this.mounted===!1){try{this.handleDragStop(new MouseEvent("mouseup"))}catch{const d=document.createEvent("MouseEvents");d.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),this.handleDragStop(d)}return}this.lastX=t,this.lastY=o}),Xe(this,"handleDragStop",n=>{if(!this.dragging)return;const r=(0,Wt.getControlPosition)(n,this.touchIdentifier,this);if(r==null)return;let{x:t,y:o}=r;if(Array.isArray(this.props.grid)){let d=t-this.lastX||0,l=o-this.lastY||0;[d,l]=(0,Wt.snapToGrid)(this.props.grid,d,l),t=this.lastX+d,o=this.lastY+l}const a=(0,Wt.createCoreData)(this,t,o);if(this.props.onStop(n,a)===!1||this.mounted===!1)return!1;const i=this.findDOMNode();i&&this.props.enableUserSelectHack&&(0,Be.removeUserSelectStyles)(i.ownerDocument),(0,Un.default)("DraggableCore: handleDragStop: %j",a),this.dragging=!1,this.lastX=NaN,this.lastY=NaN,i&&((0,Un.default)("DraggableCore: Removing handlers"),(0,Be.removeEvent)(i.ownerDocument,Bt.move,this.handleDrag),(0,Be.removeEvent)(i.ownerDocument,Bt.stop,this.handleDragStop))}),Xe(this,"onMouseDown",n=>(Bt=mt.mouse,this.handleDragStart(n))),Xe(this,"onMouseUp",n=>(Bt=mt.mouse,this.handleDragStop(n))),Xe(this,"onTouchStart",n=>(Bt=mt.touch,this.handleDragStart(n))),Xe(this,"onTouchEnd",n=>(Bt=mt.touch,this.handleDragStop(n)))}componentDidMount(){this.mounted=!0;const n=this.findDOMNode();n&&(0,Be.addEvent)(n,mt.touch.start,this.onTouchStart,{passive:!1})}componentWillUnmount(){this.mounted=!1;const n=this.findDOMNode();if(n){const{ownerDocument:r}=n;(0,Be.removeEvent)(r,mt.mouse.move,this.handleDrag),(0,Be.removeEvent)(r,mt.touch.move,this.handleDrag),(0,Be.removeEvent)(r,mt.mouse.stop,this.handleDragStop),(0,Be.removeEvent)(r,mt.touch.stop,this.handleDragStop),(0,Be.removeEvent)(n,mt.touch.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,Be.removeUserSelectStyles)(r)}}findDOMNode(){var n,r;return(n=this.props)!==null&&n!==void 0&&n.nodeRef?(r=this.props)===null||r===void 0||(r=r.nodeRef)===null||r===void 0?void 0:r.current:l0.default.findDOMNode(this)}render(){return Qo.cloneElement(Qo.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}};oo.default=io;Xe(io,"displayName","DraggableCore");Xe(io,"propTypes",{allowAnyClick:ot.default.bool,children:ot.default.node.isRequired,disabled:ot.default.bool,enableUserSelectHack:ot.default.bool,offsetParent:function(e,n){if(e[n]&&e[n].nodeType!==1)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:ot.default.arrayOf(ot.default.number),handle:ot.default.string,cancel:ot.default.string,nodeRef:ot.default.object,onStart:ot.default.func,onDrag:ot.default.func,onStop:ot.default.func,onMouseDown:ot.default.func,scale:ot.default.number,className:Jo.dontSetMe,style:Jo.dontSetMe,transform:Jo.dontSetMe});Xe(io,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1});(function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"DraggableCore",{enumerable:!0,get:function(){return d.default}}),e.default=void 0;var n=v(c),r=p(ir),t=p(or),o=p(Rh),a=_e,s=Nt,i=Rt,d=p(oo),l=p(ao);function p(w){return w&&w.__esModule?w:{default:w}}function f(w){if(typeof WeakMap!="function")return null;var x=new WeakMap,C=new WeakMap;return(f=function($){return $?C:x})(w)}function v(w,x){if(!x&&w&&w.__esModule)return w;if(w===null||typeof w!="object"&&typeof w!="function")return{default:w};var C=f(x);if(C&&C.has(w))return C.get(w);var $={},M=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var D in w)if(D!=="default"&&Object.prototype.hasOwnProperty.call(w,D)){var F=M?Object.getOwnPropertyDescriptor(w,D):null;F&&(F.get||F.set)?Object.defineProperty($,D,F):$[D]=w[D]}return $.default=w,C&&C.set(w,$),$}function h(){return h=Object.assign?Object.assign.bind():function(w){for(var x=1;x<arguments.length;x++){var C=arguments[x];for(var $ in C)Object.prototype.hasOwnProperty.call(C,$)&&(w[$]=C[$])}return w},h.apply(this,arguments)}function m(w,x,C){return x=b(x),x in w?Object.defineProperty(w,x,{value:C,enumerable:!0,configurable:!0,writable:!0}):w[x]=C,w}function b(w){var x=y(w,"string");return typeof x=="symbol"?x:String(x)}function y(w,x){if(typeof w!="object"||w===null)return w;var C=w[Symbol.toPrimitive];if(C!==void 0){var $=C.call(w,x||"default");if(typeof $!="object")return $;throw new TypeError("@@toPrimitive must return a primitive value.")}return(x==="string"?String:Number)(w)}class g extends n.Component{static getDerivedStateFromProps(x,C){let{position:$}=x,{prevPropsPosition:M}=C;return $&&(!M||$.x!==M.x||$.y!==M.y)?((0,l.default)("Draggable: getDerivedStateFromProps %j",{position:$,prevPropsPosition:M}),{x:$.x,y:$.y,prevPropsPosition:{...$}}):null}constructor(x){super(x),m(this,"onDragStart",(C,$)=>{if((0,l.default)("Draggable: onDragStart: %j",$),this.props.onStart(C,(0,s.createDraggableData)(this,$))===!1)return!1;this.setState({dragging:!0,dragged:!0})}),m(this,"onDrag",(C,$)=>{if(!this.state.dragging)return!1;(0,l.default)("Draggable: onDrag: %j",$);const M=(0,s.createDraggableData)(this,$),D={x:M.x,y:M.y,slackX:0,slackY:0};if(this.props.bounds){const{x:K,y:O}=D;D.x+=this.state.slackX,D.y+=this.state.slackY;const[z,I]=(0,s.getBoundPosition)(this,D.x,D.y);D.x=z,D.y=I,D.slackX=this.state.slackX+(K-D.x),D.slackY=this.state.slackY+(O-D.y),M.x=D.x,M.y=D.y,M.deltaX=D.x-this.state.x,M.deltaY=D.y-this.state.y}if(this.props.onDrag(C,M)===!1)return!1;this.setState(D)}),m(this,"onDragStop",(C,$)=>{if(!this.state.dragging||this.props.onStop(C,(0,s.createDraggableData)(this,$))===!1)return!1;(0,l.default)("Draggable: onDragStop: %j",$);const D={dragging:!1,slackX:0,slackY:0};if(!!this.props.position){const{x:K,y:O}=this.props.position;D.x=K,D.y=O}this.setState(D)}),this.state={dragging:!1,dragged:!1,x:x.position?x.position.x:x.defaultPosition.x,y:x.position?x.position.y:x.defaultPosition.y,prevPropsPosition:{...x.position},slackX:0,slackY:0,isElementSVG:!1},x.position&&!(x.onDrag||x.onStop)&&console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element.")}componentDidMount(){typeof window.SVGElement<"u"&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}componentWillUnmount(){this.setState({dragging:!1})}findDOMNode(){var x,C;return(x=(C=this.props)===null||C===void 0||(C=C.nodeRef)===null||C===void 0?void 0:C.current)!==null&&x!==void 0?x:t.default.findDOMNode(this)}render(){const{axis:x,bounds:C,children:$,defaultPosition:M,defaultClassName:D,defaultClassNameDragging:F,defaultClassNameDragged:K,position:O,positionOffset:z,scale:I,...A}=this.props;let N={},T=null;const L=!!!O||this.state.dragging,V=O||M,E={x:(0,s.canDragX)(this)&&L?this.state.x:V.x,y:(0,s.canDragY)(this)&&L?this.state.y:V.y};this.state.isElementSVG?T=(0,a.createSVGTransform)(E,z):N=(0,a.createCSSTransform)(E,z);const W=(0,o.default)($.props.className||"",D,{[F]:this.state.dragging,[K]:this.state.dragged});return n.createElement(d.default,h({},A,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),n.cloneElement(n.Children.only($),{className:W,style:{...$.props.style,...N},transform:T}))}}e.default=g,m(g,"displayName","Draggable"),m(g,"propTypes",{...d.default.propTypes,axis:r.default.oneOf(["both","x","y","none"]),bounds:r.default.oneOfType([r.default.shape({left:r.default.number,right:r.default.number,top:r.default.number,bottom:r.default.number}),r.default.string,r.default.oneOf([!1])]),defaultClassName:r.default.string,defaultClassNameDragging:r.default.string,defaultClassNameDragged:r.default.string,defaultPosition:r.default.shape({x:r.default.number,y:r.default.number}),positionOffset:r.default.shape({x:r.default.oneOfType([r.default.number,r.default.string]),y:r.default.oneOfType([r.default.number,r.default.string])}),position:r.default.shape({x:r.default.number,y:r.default.number}),className:i.dontSetMe,style:i.dontSetMe,transform:i.dontSetMe}),m(g,"defaultProps",{...d.default.defaultProps,axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1})})(Jc);const{default:dl,DraggableCore:p0}=Jc;ro.exports=dl;ro.exports.default=dl;ro.exports.DraggableCore=p0;var h0=ro.exports,ni={};ni.__esModule=!0;ni.cloneElement=C0;var v0=m0(c);function m0(e){return e&&e.__esModule?e:{default:e}}function fs(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function ps(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?fs(Object(r),!0).forEach(function(t){b0(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fs(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b0(e,n,r){return n=g0(n),n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function g0(e){var n=y0(e,"string");return typeof n=="symbol"?n:String(n)}function y0(e,n){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var t=r.call(e,n||"default");if(typeof t!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}function C0(e,n){return n.style&&e.props.style&&(n.style=ps(ps({},e.props.style),n.style)),n.className&&e.props.className&&(n.className=e.props.className+" "+n.className),v0.default.cloneElement(e,n)}var sr={};sr.__esModule=!0;sr.resizableProps=void 0;var ne=w0(ir);function w0(e){return e&&e.__esModule?e:{default:e}}var _0={axis:ne.default.oneOf(["both","x","y","none"]),className:ne.default.string,children:ne.default.element.isRequired,draggableOpts:ne.default.shape({allowAnyClick:ne.default.bool,cancel:ne.default.string,children:ne.default.node,disabled:ne.default.bool,enableUserSelectHack:ne.default.bool,offsetParent:ne.default.node,grid:ne.default.arrayOf(ne.default.number),handle:ne.default.string,nodeRef:ne.default.object,onStart:ne.default.func,onDrag:ne.default.func,onStop:ne.default.func,onMouseDown:ne.default.func,scale:ne.default.number}),height:function(){for(var n=arguments.length,r=new Array(n),t=0;t<n;t++)r[t]=arguments[t];var o=r[0];if(o.axis==="both"||o.axis==="y"){var a;return(a=ne.default.number).isRequired.apply(a,r)}return ne.default.number.apply(ne.default,r)},handle:ne.default.oneOfType([ne.default.node,ne.default.func]),handleSize:ne.default.arrayOf(ne.default.number),lockAspectRatio:ne.default.bool,maxConstraints:ne.default.arrayOf(ne.default.number),minConstraints:ne.default.arrayOf(ne.default.number),onResizeStop:ne.default.func,onResizeStart:ne.default.func,onResize:ne.default.func,resizeHandles:ne.default.arrayOf(ne.default.oneOf(["s","w","e","n","sw","nw","se","ne"])),transformScale:ne.default.number,width:function(){for(var n=arguments.length,r=new Array(n),t=0;t<n;t++)r[t]=arguments[t];var o=r[0];if(o.axis==="both"||o.axis==="x"){var a;return(a=ne.default.number).isRequired.apply(a,r)}return ne.default.number.apply(ne.default,r)}};sr.resizableProps=_0;ar.__esModule=!0;ar.default=void 0;var zn=T0(c),S0=h0,x0=ni,E0=sr,$0=["children","className","draggableOpts","width","height","handle","handleSize","lockAspectRatio","axis","minConstraints","maxConstraints","onResize","onResizeStop","onResizeStart","resizeHandles","transformScale"];function ul(e){if(typeof WeakMap!="function")return null;var n=new WeakMap,r=new WeakMap;return(ul=function(o){return o?r:n})(e)}function T0(e,n){if(!n&&e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=ul(n);if(r&&r.has(e))return r.get(e);var t={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if(a!=="default"&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(t,a,s):t[a]=e[a]}return t.default=e,r&&r.set(e,t),t}function ya(){return ya=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},ya.apply(this,arguments)}function M0(e,n){if(e==null)return{};var r={},t=Object.keys(e),o,a;for(a=0;a<t.length;a++)o=t[a],!(n.indexOf(o)>=0)&&(r[o]=e[o]);return r}function hs(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function ea(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?hs(Object(r),!0).forEach(function(t){D0(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hs(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function D0(e,n,r){return n=O0(n),n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function O0(e){var n=R0(e,"string");return typeof n=="symbol"?n:String(n)}function R0(e,n){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var t=r.call(e,n||"default");if(typeof t!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}function N0(e,n){e.prototype=Object.create(n.prototype),e.prototype.constructor=e,Ca(e,n)}function Ca(e,n){return Ca=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,o){return t.__proto__=o,t},Ca(e,n)}var ri=function(e){N0(n,e);function n(){for(var t,o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];return t=e.call.apply(e,[this].concat(a))||this,t.handleRefs={},t.lastHandleRect=null,t.slack=null,t}var r=n.prototype;return r.componentWillUnmount=function(){this.resetData()},r.resetData=function(){this.lastHandleRect=this.slack=null},r.runConstraints=function(o,a){var s=this.props,i=s.minConstraints,d=s.maxConstraints,l=s.lockAspectRatio;if(!i&&!d&&!l)return[o,a];if(l){var p=this.props.width/this.props.height,f=o-this.props.width,v=a-this.props.height;Math.abs(f)>Math.abs(v*p)?a=o/p:o=a*p}var h=o,m=a,b=this.slack||[0,0],y=b[0],g=b[1];return o+=y,a+=g,i&&(o=Math.max(i[0],o),a=Math.max(i[1],a)),d&&(o=Math.min(d[0],o),a=Math.min(d[1],a)),this.slack=[y+(h-o),g+(m-a)],[o,a]},r.resizeHandler=function(o,a){var s=this;return function(i,d){var l=d.node,p=d.deltaX,f=d.deltaY;o==="onResizeStart"&&s.resetData();var v=(s.props.axis==="both"||s.props.axis==="x")&&a!=="n"&&a!=="s",h=(s.props.axis==="both"||s.props.axis==="y")&&a!=="e"&&a!=="w";if(!(!v&&!h)){var m=a[0],b=a[a.length-1],y=l.getBoundingClientRect();if(s.lastHandleRect!=null){if(b==="w"){var g=y.left-s.lastHandleRect.left;p+=g}if(m==="n"){var w=y.top-s.lastHandleRect.top;f+=w}}s.lastHandleRect=y,b==="w"&&(p=-p),m==="n"&&(f=-f);var x=s.props.width+(v?p/s.props.transformScale:0),C=s.props.height+(h?f/s.props.transformScale:0),$=s.runConstraints(x,C);x=$[0],C=$[1];var M=x!==s.props.width||C!==s.props.height,D=typeof s.props[o]=="function"?s.props[o]:null,F=o==="onResize"&&!M;D&&!F&&(i.persist==null||i.persist(),D(i,{node:l,size:{width:x,height:C},handle:a})),o==="onResizeStop"&&s.resetData()}}},r.renderResizeHandle=function(o,a){var s=this.props.handle;if(!s)return zn.createElement("span",{className:"react-resizable-handle react-resizable-handle-"+o,ref:a});if(typeof s=="function")return s(o,a);var i=typeof s.type=="string",d=ea({ref:a},i?{}:{handleAxis:o});return zn.cloneElement(s,d)},r.render=function(){var o=this,a=this.props,s=a.children,i=a.className,d=a.draggableOpts;a.width,a.height,a.handle,a.handleSize,a.lockAspectRatio,a.axis,a.minConstraints,a.maxConstraints,a.onResize,a.onResizeStop,a.onResizeStart;var l=a.resizeHandles;a.transformScale;var p=M0(a,$0);return(0,x0.cloneElement)(s,ea(ea({},p),{},{className:(i?i+" ":"")+"react-resizable",children:[].concat(s.props.children,l.map(function(f){var v,h=(v=o.handleRefs[f])!=null?v:o.handleRefs[f]=zn.createRef();return zn.createElement(S0.DraggableCore,ya({},d,{nodeRef:h,key:"resizableHandle-"+f,onStop:o.resizeHandler("onResizeStop",f),onStart:o.resizeHandler("onResizeStart",f),onDrag:o.resizeHandler("onResize",f)}),o.renderResizeHandle(f,h))}))}))},n}(zn.Component);ar.default=ri;ri.propTypes=E0.resizableProps;ri.defaultProps={axis:"both",handleSize:[20,20],lockAspectRatio:!1,minConstraints:[20,20],maxConstraints:[1/0,1/0],resizeHandles:["se"],transformScale:1};var so={};so.__esModule=!0;so.default=void 0;var ta=I0(c),k0=fl(ir),P0=fl(ar),L0=sr,A0=["handle","handleSize","onResize","onResizeStart","onResizeStop","draggableOpts","minConstraints","maxConstraints","lockAspectRatio","axis","width","height","resizeHandles","style","transformScale"];function fl(e){return e&&e.__esModule?e:{default:e}}function pl(e){if(typeof WeakMap!="function")return null;var n=new WeakMap,r=new WeakMap;return(pl=function(o){return o?r:n})(e)}function I0(e,n){if(!n&&e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=pl(n);if(r&&r.has(e))return r.get(e);var t={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if(a!=="default"&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(t,a,s):t[a]=e[a]}return t.default=e,r&&r.set(e,t),t}function wa(){return wa=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},wa.apply(this,arguments)}function vs(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function zr(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?vs(Object(r),!0).forEach(function(t){F0(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vs(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function F0(e,n,r){return n=H0(n),n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function H0(e){var n=K0(e,"string");return typeof n=="symbol"?n:String(n)}function K0(e,n){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var t=r.call(e,n||"default");if(typeof t!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}function U0(e,n){if(e==null)return{};var r={},t=Object.keys(e),o,a;for(a=0;a<t.length;a++)o=t[a],!(n.indexOf(o)>=0)&&(r[o]=e[o]);return r}function z0(e,n){e.prototype=Object.create(n.prototype),e.prototype.constructor=e,_a(e,n)}function _a(e,n){return _a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,o){return t.__proto__=o,t},_a(e,n)}var hl=function(e){z0(n,e);function n(){for(var t,o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];return t=e.call.apply(e,[this].concat(a))||this,t.state={width:t.props.width,height:t.props.height,propsWidth:t.props.width,propsHeight:t.props.height},t.onResize=function(i,d){var l=d.size;t.props.onResize?(i.persist==null||i.persist(),t.setState(l,function(){return t.props.onResize&&t.props.onResize(i,d)})):t.setState(l)},t}n.getDerivedStateFromProps=function(o,a){return a.propsWidth!==o.width||a.propsHeight!==o.height?{width:o.width,height:o.height,propsWidth:o.width,propsHeight:o.height}:null};var r=n.prototype;return r.render=function(){var o=this.props,a=o.handle,s=o.handleSize;o.onResize;var i=o.onResizeStart,d=o.onResizeStop,l=o.draggableOpts,p=o.minConstraints,f=o.maxConstraints,v=o.lockAspectRatio,h=o.axis;o.width,o.height;var m=o.resizeHandles,b=o.style,y=o.transformScale,g=U0(o,A0);return ta.createElement(P0.default,{axis:h,draggableOpts:l,handle:a,handleSize:s,height:this.state.height,lockAspectRatio:v,maxConstraints:f,minConstraints:p,onResizeStart:i,onResize:this.onResize,onResizeStop:d,resizeHandles:m,transformScale:y,width:this.state.width},ta.createElement("div",wa({},g,{style:zr(zr({},b),{},{width:this.state.width+"px",height:this.state.height+"px"})})))},n}(ta.Component);so.default=hl;hl.propTypes=zr(zr({},L0.resizableProps),{},{children:k0.default.element});Qa.exports=function(){throw new Error("Don't instantiate Resizable directly! Use require('react-resizable').Resizable")};var W0=Qa.exports.Resizable=ar.default;Qa.exports.ResizableBox=so.default;const ms=()=>{window.dispatchEvent(new Event("resize"))},B0=oe.span(({$collapsed:e})=>["fb-absolute fb-left-[3px] fb-top-[79px] fb-z-[999] fb-h-6 fb-w-6 fb-cursor-pointer fb-rounded-full","fb-bg-white fb-text-[#6b778c] fb-shadow-[0_0_0_1px_#c3c4c7,0_2px_5px_rgba(51,51,51,0.1)]","-fb-translate-x-1/2 rtl:fb-translate-x-1/2","hover:fb-bg-primary hover:fb-text-white hover:fb-shadow-[0_0_0_1px_#2271b1,0_2px_5px_rgba(51,51,51,0.1)]",e&&"-fb-scale-x-100"]),V0=oe.div`
  fb-relative fb-mr-4 fb-w-[3px] fb-cursor-col-resize fb-resize-x fb-justify-self-end
  before:fb-absolute before:fb-right-0 before:fb-h-full before:fb-w-[1px] before:fb-bg-[#c1c3c5b4] before:fb-transition-all before:fb-duration-200 before:fb-content-['']
  hover:before:fb-bg-primary hover:before:fb-shadow-[1px_0_0_0_#2271b1]
`,vl=oe.div(({$modal:e})=>["fb-flex fb-grow fb-flex-col fb-pl-[1px]",e&&!Ot&&"fb-max-w-[300px]"]),ml=oe.div(({$modal:e})=>["fb-flex",e?"fb-h-full":"fb-h-[calc(100vh-72px)]"]),j0=({minWidth:e,maxWidth:n,children:r,stopCallback:t})=>{const{modal:o,theme:a}=ke();return o||Ot?u(ml,{id:"filebird-tree","data-theme":a,$modal:o,children:u(vl,{$modal:o,children:r})}):G0({minWidth:e,maxWidth:n,children:r,stopCallback:t})},G0=({minWidth:e,maxWidth:n,children:r,stopCallback:t})=>{const[o,a]=c.useState(!1),[s,i]=c1("filebird-sidebar-width",{defaultValue:e}),{theme:d}=ke(),l=(b,{node:y,size:g,handle:w})=>{i(g.width),a(!0)},p=(b,{node:y,size:g,handle:w})=>{a(!1),ms(),t&&t()},f=()=>{i(s===0?e:0)},v=`${o?"none":"width"} 0.2s ease 0s`,h=c.useRef(null),m=c.useMemo(()=>s?P("collapse"):P("expand"),[s]);return c.useEffect(()=>ms(),[]),u(W0,{minConstraints:[e,e],maxConstraints:[n,n],axis:"x",width:s,onResize:l,onResizeStop:p,handle:(b,y)=>u(V0,{ref:y,className:`handle-${b}`,children:u(rr,{arrow:!1,content:m,sideOffset:5,side:"right",children:()=>u(B0,{$collapsed:s!==0,onClick:f,children:u(Tu,{})})})}),children:u(ml,{id:"filebird-tree","data-theme":d,children:u(vl,{ref:h,style:{width:s+"px",transition:v,overflow:"hidden"},children:r})})})},Y0=fn(["fb-m-0 fb-h-8 fb-w-full fb-rounded-[3px] fb-bg-white fb-px-8 fb-py-0 fb-transition-all fb-duration-200 fb-ease-in-out fb-text-black placeholder:fb-text-[rgba(0,0,0,0.6)]"],{variants:{$theme:{default:`fb-border fb-border-solid fb-border-[#ccd0d4] 
          hover:fb-border-primary focusin:fb-border-primary focusin:fb-shadow-[0_0_0_1px_#2271b1] 
          [&+i]:focus:fb-text-primary`,windows:"fb-border-none fb-shadow-[0_2px_0_0_#d3d3d3] focusin:fb-shadow-[0_2px_0_0_#2271b1]",dropbox:`fb-border fb-border-solid fb-border-[#ccd0d4] fb-bg-white 
          hover:fb-border-[#0360fe] focusin:fb-border-[#0360fe] focusin:fb-shadow-[0_0_0_1px_#0360fe] 
          [&+i]:fb-text-[#94ceff] [&-i]:fb-text-[#94ceff]`}}}),Z0=oe.input(({$theme:e})=>Y0({$theme:e})),X0=c.memo(()=>{const e=U(i=>i.folderQuery.search),[n,r]=c.useState(e??""),t=U(i=>i.setSearch),{theme:o}=ke(),a=i=>{r(i.target.value),i.target.value||t("")},s=()=>{t(""),r("")};return a1(()=>{n&&t(n)},[n],{wait:1e3}),c.useEffect(()=>{r(e??"")},[e]),R(Ne,{children:[R("div",{className:"fb-search-bar fb-relative fb-mb-2 fb-mr-4",children:[u(Z0,{$theme:o,value:n,onChange:a,placeholder:P("enter_folder_name_placeholder")}),u(qe,{className:"fb-absolute fb-left-[7px] fb-top-1/2 -fb-translate-y-1/2",icon:"searchFolderIcon"}),e&&u("i",{className:"fb-absolute fb-left-auto fb-right-[9px] fb-top-1/2 fb-flex fb-w-[14px] -fb-translate-y-1/2 fb-cursor-pointer fb-items-center fb-text-default",onClick:s,children:u(Mu,{})})]}),u("hr",{className:"fb-mx-0 fb-mb-1 fb-mt-0 fb-w-[calc(100%-16px)] fb-border-0 fb-border-b fb-border-t fb-border-solid fb-border-b-[#f6f7f7] fb-border-t-[#dcdcde]"})]})}),na="fb-toaster",q0=()=>{const[e,n]=c.useState(null);return c.useEffect(()=>{document.body&&!document.getElementById(na)&&(jQuery(document.body).append(`<div id=${na}></div>`),n(document.getElementById(na)))},[]),e?or.createPortal(u(Du,{toastOptions:{style:{maxWidth:"inherit"}},containerStyle:{top:40}}),e):null},Sa=["Enter"," "],Q0=["ArrowDown","PageUp","Home"],bl=["ArrowUp","PageDown","End"],J0=[...Q0,...bl],ev={ltr:[...Sa,"ArrowRight"],rtl:[...Sa,"ArrowLeft"]},tv={ltr:["ArrowLeft"],rtl:["ArrowRight"]},co="Menu",[qn,nv,rv]=Bc(co),[hn,lo]=Kt(co,[rv,eo,no]),cr=eo(),gl=no(),[yl,qt]=hn(co),[ov,lr]=hn(co),av=e=>{const{__scopeMenu:n,open:r=!1,children:t,dir:o,onOpenChange:a,modal:s=!0}=e,i=cr(n),[d,l]=c.useState(null),p=c.useRef(!1),f=cn(a),v=Wa(o);return c.useEffect(()=>{const h=()=>{p.current=!0,document.addEventListener("pointerdown",m,{capture:!0,once:!0}),document.addEventListener("pointermove",m,{capture:!0,once:!0})},m=()=>p.current=!1;return document.addEventListener("keydown",h,{capture:!0}),()=>{document.removeEventListener("keydown",h,{capture:!0}),document.removeEventListener("pointerdown",m,{capture:!0}),document.removeEventListener("pointermove",m,{capture:!0})}},[]),c.createElement(Ua,i,c.createElement(yl,{scope:n,open:r,onOpenChange:f,content:d,onContentChange:l},c.createElement(ov,{scope:n,onClose:c.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:s},t)))},Cl=c.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e,o=cr(r);return c.createElement(ac,fe({},o,t,{ref:n}))}),wl="MenuPortal",[iv,_l]=hn(wl,{forceMount:void 0}),sv=e=>{const{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=qt(wl,n);return c.createElement(iv,{scope:n,forceMount:r},c.createElement(Ut,{present:r||a.open},c.createElement(Ka,{asChild:!0,container:o},t)))},Ct="MenuContent",[cv,oi]=hn(Ct),lv=c.forwardRef((e,n)=>{const r=_l(Ct,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=qt(Ct,e.__scopeMenu),s=lr(Ct,e.__scopeMenu);return c.createElement(qn.Provider,{scope:e.__scopeMenu},c.createElement(Ut,{present:t||a.open},c.createElement(qn.Slot,{scope:e.__scopeMenu},s.modal?c.createElement(dv,fe({},o,{ref:n})):c.createElement(uv,fe({},o,{ref:n})))))}),dv=c.forwardRef((e,n)=>{const r=qt(Ct,e.__scopeMenu),t=c.useRef(null),o=je(n,t);return c.useEffect(()=>{const a=t.current;if(a)return Za(a)},[]),c.createElement(ai,fe({},e,{ref:o,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:q(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)}))}),uv=c.forwardRef((e,n)=>{const r=qt(Ct,e.__scopeMenu);return c.createElement(ai,fe({},e,{ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)}))}),ai=c.forwardRef((e,n)=>{const{__scopeMenu:r,loop:t=!1,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:s,disableOutsidePointerEvents:i,onEntryFocus:d,onEscapeKeyDown:l,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:v,onDismiss:h,disableOutsideScroll:m,...b}=e,y=qt(Ct,r),g=lr(Ct,r),w=cr(r),x=gl(r),C=nv(r),[$,M]=c.useState(null),D=c.useRef(null),F=je(n,D,y.onContentChange),K=c.useRef(0),O=c.useRef(""),z=c.useRef(0),I=c.useRef(null),A=c.useRef("right"),N=c.useRef(0),T=m?Ya:c.Fragment,k=m?{as:Zn,allowPinchZoom:!0}:void 0,L=E=>{var W,B;const Q=O.current+E,J=C().filter(ae=>!ae.disabled),he=document.activeElement,te=(W=J.find(ae=>ae.ref.current===he))===null||W===void 0?void 0:W.textValue,ce=J.map(ae=>ae.textValue),le=Nv(ce,Q,te),we=(B=J.find(ae=>ae.textValue===le))===null||B===void 0?void 0:B.ref.current;(function ae(De){O.current=De,window.clearTimeout(K.current),De!==""&&(K.current=window.setTimeout(()=>ae(""),1e3))})(Q),we&&setTimeout(()=>we.focus())};c.useEffect(()=>()=>window.clearTimeout(K.current),[]),Ga();const V=c.useCallback(E=>{var W,B;return A.current===((W=I.current)===null||W===void 0?void 0:W.side)&&Pv(E,(B=I.current)===null||B===void 0?void 0:B.area)},[]);return c.createElement(cv,{scope:r,searchRef:O,onItemEnter:c.useCallback(E=>{V(E)&&E.preventDefault()},[V]),onItemLeave:c.useCallback(E=>{var W;V(E)||((W=D.current)===null||W===void 0||W.focus(),M(null))},[V]),onTriggerLeave:c.useCallback(E=>{V(E)&&E.preventDefault()},[V]),pointerGraceTimerRef:z,onPointerGraceIntentChange:c.useCallback(E=>{I.current=E},[])},c.createElement(T,k,c.createElement(ja,{asChild:!0,trapped:o,onMountAutoFocus:q(a,E=>{var W;E.preventDefault(),(W=D.current)===null||W===void 0||W.focus()}),onUnmountAutoFocus:s},c.createElement(Ha,{asChild:!0,disableOutsidePointerEvents:i,onEscapeKeyDown:l,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:v,onDismiss:h},c.createElement(Gc,fe({asChild:!0},x,{dir:g.dir,orientation:"vertical",loop:t,currentTabStopId:$,onCurrentTabStopIdChange:M,onEntryFocus:q(d,E=>{g.isUsingKeyboardRef.current||E.preventDefault()})}),c.createElement(ic,fe({role:"menu","aria-orientation":"vertical","data-state":Ml(y.open),"data-radix-menu-content":"",dir:g.dir},w,b,{ref:F,style:{outline:"none",...b.style},onKeyDown:q(b.onKeyDown,E=>{const B=E.target.closest("[data-radix-menu-content]")===E.currentTarget,Q=E.ctrlKey||E.altKey||E.metaKey,J=E.key.length===1;B&&(E.key==="Tab"&&E.preventDefault(),!Q&&J&&L(E.key));const he=D.current;if(E.target!==he||!J0.includes(E.key))return;E.preventDefault();const ce=C().filter(le=>!le.disabled).map(le=>le.ref.current);bl.includes(E.key)&&ce.reverse(),Ov(ce)}),onBlur:q(e.onBlur,E=>{E.currentTarget.contains(E.target)||(window.clearTimeout(K.current),O.current="")}),onPointerMove:q(e.onPointerMove,Qn(E=>{const W=E.target,B=N.current!==E.clientX;if(E.currentTarget.contains(W)&&B){const Q=E.clientX>N.current?"right":"left";A.current=Q,N.current=E.clientX}}))})))))))}),fv=c.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e;return c.createElement(Me.div,fe({role:"group"},t,{ref:n}))}),pv=c.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e;return c.createElement(Me.div,fe({},t,{ref:n}))}),xa="MenuItem",bs="menu.itemSelect",ii=c.forwardRef((e,n)=>{const{disabled:r=!1,onSelect:t,...o}=e,a=c.useRef(null),s=lr(xa,e.__scopeMenu),i=oi(xa,e.__scopeMenu),d=je(n,a),l=c.useRef(!1),p=()=>{const f=a.current;if(!r&&f){const v=new CustomEvent(bs,{bubbles:!0,cancelable:!0});f.addEventListener(bs,h=>t==null?void 0:t(h),{once:!0}),Ou(f,v),v.defaultPrevented?l.current=!1:s.onClose()}};return c.createElement(Sl,fe({},o,{ref:d,disabled:r,onClick:q(e.onClick,p),onPointerDown:f=>{var v;(v=e.onPointerDown)===null||v===void 0||v.call(e,f),l.current=!0},onPointerUp:q(e.onPointerUp,f=>{var v;l.current||(v=f.currentTarget)===null||v===void 0||v.click()}),onKeyDown:q(e.onKeyDown,f=>{const v=i.searchRef.current!=="";r||v&&f.key===" "||Sa.includes(f.key)&&(f.currentTarget.click(),f.preventDefault())})}))}),Sl=c.forwardRef((e,n)=>{const{__scopeMenu:r,disabled:t=!1,textValue:o,...a}=e,s=oi(xa,r),i=gl(r),d=c.useRef(null),l=je(n,d),[p,f]=c.useState(!1),[v,h]=c.useState("");return c.useEffect(()=>{const m=d.current;if(m){var b;h(((b=m.textContent)!==null&&b!==void 0?b:"").trim())}},[a.children]),c.createElement(qn.ItemSlot,{scope:r,disabled:t,textValue:o??v},c.createElement(Yc,fe({asChild:!0},i,{focusable:!t}),c.createElement(Me.div,fe({role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":t||void 0,"data-disabled":t?"":void 0},a,{ref:l,onPointerMove:q(e.onPointerMove,Qn(m=>{t?s.onItemLeave(m):(s.onItemEnter(m),m.defaultPrevented||m.currentTarget.focus())})),onPointerLeave:q(e.onPointerLeave,Qn(m=>s.onItemLeave(m))),onFocus:q(e.onFocus,()=>f(!0)),onBlur:q(e.onBlur,()=>f(!1))}))))}),hv=c.forwardRef((e,n)=>{const{checked:r=!1,onCheckedChange:t,...o}=e;return c.createElement(El,{scope:e.__scopeMenu,checked:r},c.createElement(ii,fe({role:"menuitemcheckbox","aria-checked":Wr(r)?"mixed":r},o,{ref:n,"data-state":si(r),onSelect:q(o.onSelect,()=>t==null?void 0:t(Wr(r)?!0:!r),{checkForDefaultPrevented:!1})})))}),vv="MenuRadioGroup",[mv,bv]=hn(vv,{value:void 0,onValueChange:()=>{}}),gv=c.forwardRef((e,n)=>{const{value:r,onValueChange:t,...o}=e,a=cn(t);return c.createElement(mv,{scope:e.__scopeMenu,value:r,onValueChange:a},c.createElement(fv,fe({},o,{ref:n})))}),yv="MenuRadioItem",Cv=c.forwardRef((e,n)=>{const{value:r,...t}=e,o=bv(yv,e.__scopeMenu),a=r===o.value;return c.createElement(El,{scope:e.__scopeMenu,checked:a},c.createElement(ii,fe({role:"menuitemradio","aria-checked":a},t,{ref:n,"data-state":si(a),onSelect:q(t.onSelect,()=>{var s;return(s=o.onValueChange)===null||s===void 0?void 0:s.call(o,r)},{checkForDefaultPrevented:!1})})))}),xl="MenuItemIndicator",[El,wv]=hn(xl,{checked:!1}),_v=c.forwardRef((e,n)=>{const{__scopeMenu:r,forceMount:t,...o}=e,a=wv(xl,r);return c.createElement(Ut,{present:t||Wr(a.checked)||a.checked===!0},c.createElement(Me.span,fe({},o,{ref:n,"data-state":si(a.checked)})))}),Sv=c.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e;return c.createElement(Me.div,fe({role:"separator","aria-orientation":"horizontal"},t,{ref:n}))}),xv=c.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e,o=cr(r);return c.createElement(sc,fe({},o,t,{ref:n}))}),$l="MenuSub",[Ev,Tl]=hn($l),$v=e=>{const{__scopeMenu:n,children:r,open:t=!1,onOpenChange:o}=e,a=qt($l,n),s=cr(n),[i,d]=c.useState(null),[l,p]=c.useState(null),f=cn(o);return c.useEffect(()=>(a.open===!1&&f(!1),()=>f(!1)),[a.open,f]),c.createElement(Ua,s,c.createElement(yl,{scope:n,open:t,onOpenChange:f,content:l,onContentChange:p},c.createElement(Ev,{scope:n,contentId:It(),triggerId:It(),trigger:i,onTriggerChange:d},r)))},xr="MenuSubTrigger",Tv=c.forwardRef((e,n)=>{const r=qt(xr,e.__scopeMenu),t=lr(xr,e.__scopeMenu),o=Tl(xr,e.__scopeMenu),a=oi(xr,e.__scopeMenu),s=c.useRef(null),{pointerGraceTimerRef:i,onPointerGraceIntentChange:d}=a,l={__scopeMenu:e.__scopeMenu},p=c.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return c.useEffect(()=>p,[p]),c.useEffect(()=>{const f=i.current;return()=>{window.clearTimeout(f),d(null)}},[i,d]),c.createElement(Cl,fe({asChild:!0},l),c.createElement(Sl,fe({id:o.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":o.contentId,"data-state":Ml(r.open)},e,{ref:cc(n,o.onTriggerChange),onClick:f=>{var v;(v=e.onClick)===null||v===void 0||v.call(e,f),!(e.disabled||f.defaultPrevented)&&(f.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:q(e.onPointerMove,Qn(f=>{a.onItemEnter(f),!f.defaultPrevented&&!e.disabled&&!r.open&&!s.current&&(a.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:q(e.onPointerLeave,Qn(f=>{var v;p();const h=(v=r.content)===null||v===void 0?void 0:v.getBoundingClientRect();if(h){var m;const b=(m=r.content)===null||m===void 0?void 0:m.dataset.side,y=b==="right",g=y?-5:5,w=h[y?"left":"right"],x=h[y?"right":"left"];a.onPointerGraceIntentChange({area:[{x:f.clientX+g,y:f.clientY},{x:w,y:h.top},{x,y:h.top},{x,y:h.bottom},{x:w,y:h.bottom}],side:b}),window.clearTimeout(i.current),i.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(f),f.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:q(e.onKeyDown,f=>{const v=a.searchRef.current!=="";if(!(e.disabled||v&&f.key===" ")&&ev[t.dir].includes(f.key)){var h;r.onOpenChange(!0),(h=r.content)===null||h===void 0||h.focus(),f.preventDefault()}})})))}),Mv="MenuSubContent",Dv=c.forwardRef((e,n)=>{const r=_l(Ct,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=qt(Ct,e.__scopeMenu),s=lr(Ct,e.__scopeMenu),i=Tl(Mv,e.__scopeMenu),d=c.useRef(null),l=je(n,d);return c.createElement(qn.Provider,{scope:e.__scopeMenu},c.createElement(Ut,{present:t||a.open},c.createElement(qn.Slot,{scope:e.__scopeMenu},c.createElement(ai,fe({id:i.contentId,"aria-labelledby":i.triggerId},o,{ref:l,align:"start",side:s.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:p=>{var f;s.isUsingKeyboardRef.current&&((f=d.current)===null||f===void 0||f.focus()),p.preventDefault()},onCloseAutoFocus:p=>p.preventDefault(),onFocusOutside:q(e.onFocusOutside,p=>{p.target!==i.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:q(e.onEscapeKeyDown,p=>{s.onClose(),p.preventDefault()}),onKeyDown:q(e.onKeyDown,p=>{const f=p.currentTarget.contains(p.target),v=tv[s.dir].includes(p.key);if(f&&v){var h;a.onOpenChange(!1),(h=i.trigger)===null||h===void 0||h.focus(),p.preventDefault()}})})))))});function Ml(e){return e?"open":"closed"}function Wr(e){return e==="indeterminate"}function si(e){return Wr(e)?"indeterminate":e?"checked":"unchecked"}function Ov(e){const n=document.activeElement;for(const r of e)if(r===n||(r.focus(),document.activeElement!==n))return}function Rv(e,n){return e.map((r,t)=>e[(n+t)%e.length])}function Nv(e,n,r){const o=n.length>1&&Array.from(n).every(l=>l===n[0])?n[0]:n,a=r?e.indexOf(r):-1;let s=Rv(e,Math.max(a,0));o.length===1&&(s=s.filter(l=>l!==r));const d=s.find(l=>l.toLowerCase().startsWith(o.toLowerCase()));return d!==r?d:void 0}function kv(e,n){const{x:r,y:t}=e;let o=!1;for(let a=0,s=n.length-1;a<n.length;s=a++){const i=n[a].x,d=n[a].y,l=n[s].x,p=n[s].y;d>t!=p>t&&r<(l-i)*(t-d)/(p-d)+i&&(o=!o)}return o}function Pv(e,n){if(!n)return!1;const r={x:e.clientX,y:e.clientY};return kv(r,n)}function Qn(e){return n=>n.pointerType==="mouse"?e(n):void 0}const Dl=av,Ol=Cl,Rl=sv,Nl=lv,Lv=pv,kl=ii,Av=hv,Iv=gv,Fv=Cv,Hv=_v,Pl=Sv,Kv=xv,Ll=$v,Al=Tv,Il=Dv,Fl="DropdownMenu",[Uv,a5]=Kt(Fl,[lo]),tt=lo(),[zv,Hl]=Uv(Fl),Wv=e=>{const{__scopeDropdownMenu:n,children:r,dir:t,open:o,defaultOpen:a,onOpenChange:s,modal:i=!0}=e,d=tt(n),l=c.useRef(null),[p=!1,f]=un({prop:o,defaultProp:a,onChange:s});return c.createElement(zv,{scope:n,triggerId:It(),triggerRef:l,contentId:It(),open:p,onOpenChange:f,onOpenToggle:c.useCallback(()=>f(v=>!v),[f]),modal:i},c.createElement(Dl,X({},d,{open:p,onOpenChange:f,dir:t,modal:i}),r))},Bv="DropdownMenuTrigger",Vv=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,disabled:t=!1,...o}=e,a=Hl(Bv,r),s=tt(r);return c.createElement(Ol,X({asChild:!0},s),c.createElement(Me.button,X({type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":t?"":void 0,disabled:t},o,{ref:cc(n,a.triggerRef),onPointerDown:q(e.onPointerDown,i=>{!t&&i.button===0&&i.ctrlKey===!1&&(a.onOpenToggle(),a.open||i.preventDefault())}),onKeyDown:q(e.onKeyDown,i=>{t||(["Enter"," "].includes(i.key)&&a.onOpenToggle(),i.key==="ArrowDown"&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(i.key)&&i.preventDefault())})})))}),jv=e=>{const{__scopeDropdownMenu:n,...r}=e,t=tt(n);return c.createElement(Rl,X({},t,r))},Gv="DropdownMenuContent",Yv=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Hl(Gv,r),a=tt(r),s=c.useRef(!1);return c.createElement(Nl,X({id:o.contentId,"aria-labelledby":o.triggerId},a,t,{ref:n,onCloseAutoFocus:q(e.onCloseAutoFocus,i=>{var d;s.current||(d=o.triggerRef.current)===null||d===void 0||d.focus(),s.current=!1,i.preventDefault()}),onInteractOutside:q(e.onInteractOutside,i=>{const d=i.detail.originalEvent,l=d.button===0&&d.ctrlKey===!0,p=d.button===2||l;(!o.modal||p)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))}),Zv=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(Lv,X({},o,t,{ref:n}))}),Xv=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(kl,X({},o,t,{ref:n}))}),qv=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(Av,X({},o,t,{ref:n}))}),Qv=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(Iv,X({},o,t,{ref:n}))}),Jv=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(Fv,X({},o,t,{ref:n}))}),em=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(Hv,X({},o,t,{ref:n}))}),tm=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(Pl,X({},o,t,{ref:n}))}),nm=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(Kv,X({},o,t,{ref:n}))}),rm=e=>{const{__scopeDropdownMenu:n,children:r,open:t,onOpenChange:o,defaultOpen:a}=e,s=tt(n),[i=!1,d]=un({prop:t,defaultProp:a,onChange:o});return c.createElement(Ll,X({},s,{open:i,onOpenChange:d}),r)},om=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(Al,X({},o,t,{ref:n}))}),am=c.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=tt(r);return c.createElement(Il,X({},o,t,{ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))}),im=Wv,sm=Vv,Kl=jv,cm=Yv,lm=Zv,dm=Xv,um=qv,fm=Qv,pm=Jv,hm=em,vm=tm,mm=nm,bm=rm,gm=om,ym=am,Ul=`fb-p-1 fb-min-w-60 fb-bg-white fb-will-change-transform-opacity fb-rounded-md fb-shadow-[0px_2px_8px_2px_rgba(28,39,60,0.2)] fb-z-biggest
  dropdown-top:fb-animate-dropdown-top-and-fade
  dropdown-down:fb-animate-dropdown-down-and-fade
  dropdown-left:fb-animate-dropdown-left-and-fade
  dropdown-right:fb-animate-dropdown-right-and-fade
`,uo=`
  fb-all-unset fb-text-[13px] fb-text-[rgba(0,0,0,0.65)] fb-flex fb-items-center fb-relative fb-p-[0_25px] fb-select-none fb-rounded fb-h-9 fb-leading-9 fb-cursor-pointer
  data-[disabled]:fb-text-default data-[disabled]:fb-pointer-events-none
  data-[state="checked"]:fb-text-primary data-[state="checked"]:fb-font-semibold
  data-[highlighted]:fb-bg-[rgba(0,0,0,0.06)]
`,Cm=oe(im)`fb-z-[9999]`,wm=dn(cm)`${Ul}${Ot&&"fb-min-w-fit"}`,_m=oe(ym)`${Ul}${Ot&&"fb-min-w-fit"}`,Sm=oe(mm)`fb-fill-white`,xm=dn(dm)`${uo}`;oe(um)`${uo}`;const Em=oe(pm)`${uo}`,$m=oe(gm)`${uo} data-[state="open"]:fb-bg-[rgba(0,0,0,0.06)]`,Tm=oe(lm)`fb-pl-[25px] fb-text-sm fn-leading-7 fb-text-[rgba(0,0,0,0.65)] fb-font-semibold`,Mm=oe(vm)`fb-h-[1px] fb-bg-[rgba(72,94,144,0.12)] fb-my-1 fb-m-0`,Dm=oe(hm)`fb-absolute fb-left-1 fb-w-[18px] fb-inline-flex fb-flex fb-items-center fb-justify-center [&_svg]:fb-w-[inherit] [&_svg]:fb-h-[inherit]`,Om=oe.div`fb-ml-auto fb-pl-5 fn-text-default data-[highlighted]:fb-text-default data-[disabled]:fb-text-primary`,Rm=oe.span`fb-absolute fb-right-0 fb-top-1/2 -fb-translate-y-1/2 fb-flex fb-items-center fb-ml-[10px] fb-text-[10px]
  [&>svg]:fb-fill-default [&>svg]:fb-w-6 [&>svg]:fb-h-6
`,Nm=(e,n)=>n.some(r=>r.includes(e)),zl=(e,n,r,t,o)=>e.map((a,s)=>{if(a.type==="label")return u(Tm,{children:a.label},s);if(a.type==="separator")return u(Mm,{},s);if(a.type==="item")return R(xm,{onSelect:i=>n({event:i,key:a.key,value:a.key}),className:a.className,children:[a.icon&&a.icon,a.label]},s);if(a.type==="radio")return u(fm,{value:r[a.key],onValueChange:i=>n({event:null,key:a.key,value:i}),children:a.options.map(i=>R(Em,{disabled:i.disabled,value:i.value,children:[u(Dm,{children:u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",children:u("path",{d:"M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"})})}),i.label]},i.value))},a.key||s);if(a.type==="sub")return R(bm,{children:[R($m,{disabled:a.disabled,className:ve({"fb-text-primary fb-font-semibold":Nm(a.key,t)}),children:[a.label,u(Om,{children:u(Rm,{"aria-hidden":!0,children:u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:u("path",{d:"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"})})})})]}),u(Kl,{children:u(_m,{sideOffset:2,alignOffset:-5,children:zl(a.children,n,r,t)})})]},s)});function Wl(e,n,r=[]){const t=[];for(const o of e){if(n.includes(o.key)&&t.push([...r,o.key]),o.type==="radio"){const a=o.options.find(s=>n.includes(s.value));a&&t.push([...r,o.key,a.value])}if(o.children){const a=Wl(o.children,n,[...r,o.key]);a.length>0&&t.push(...a)}}return t}const km=({selectedKeys:e,menu:n,children:r,onClick:t,arrow:o,noSelection:a})=>{const s=Object.values(e),i=c.useMemo(()=>Wl(n,s),[s]);return R(Cm,{children:[u(sm,{asChild:!0,children:r}),u(Kl,{children:R(wm,{sideOffset:5,side:"bottom",align:Ot?"end":"start",children:[zl(n,t,e,i),o&&u(Sm,{})]})})]})},gs=c.memo(km),Bl="Popover",[Vl,i5]=Kt(Bl,[eo]),fo=eo(),[Pm,vn]=Vl(Bl),Lm=e=>{const{__scopePopover:n,children:r,open:t,defaultOpen:o,onOpenChange:a,modal:s=!1}=e,i=fo(n),d=c.useRef(null),[l,p]=c.useState(!1),[f=!1,v]=un({prop:t,defaultProp:o,onChange:a});return c.createElement(Ua,i,c.createElement(Pm,{scope:n,contentId:It(),triggerRef:d,open:f,onOpenChange:v,onOpenToggle:c.useCallback(()=>v(h=>!h),[v]),hasCustomAnchor:l,onCustomAnchorAdd:c.useCallback(()=>p(!0),[]),onCustomAnchorRemove:c.useCallback(()=>p(!1),[]),modal:s},r))},Am="PopoverTrigger",Im=c.forwardRef((e,n)=>{const{__scopePopover:r,...t}=e,o=vn(Am,r),a=fo(r),s=je(n,o.triggerRef),i=c.createElement(Me.button,X({type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Yl(o.open)},t,{ref:s,onClick:q(e.onClick,o.onOpenToggle)}));return o.hasCustomAnchor?i:c.createElement(ac,X({asChild:!0},a),i)}),jl="PopoverPortal",[Fm,Hm]=Vl(jl,{forceMount:void 0}),Km=e=>{const{__scopePopover:n,forceMount:r,children:t,container:o}=e,a=vn(jl,n);return c.createElement(Fm,{scope:n,forceMount:r},c.createElement(Ut,{present:r||a.open},c.createElement(Ka,{asChild:!0,container:o},t)))},Jn="PopoverContent",Um=c.forwardRef((e,n)=>{const r=Hm(Jn,e.__scopePopover),{forceMount:t=r.forceMount,...o}=e,a=vn(Jn,e.__scopePopover);return c.createElement(Ut,{present:t||a.open},a.modal?c.createElement(zm,X({},o,{ref:n})):c.createElement(Wm,X({},o,{ref:n})))}),zm=c.forwardRef((e,n)=>{const r=vn(Jn,e.__scopePopover),t=c.useRef(null),o=je(n,t),a=c.useRef(!1);return c.useEffect(()=>{const s=t.current;if(s)return Za(s)},[]),c.createElement(Ya,{as:Zn,allowPinchZoom:!0},c.createElement(Gl,X({},e,{ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:q(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),a.current||(i=r.triggerRef.current)===null||i===void 0||i.focus()}),onPointerDownOutside:q(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,d=i.button===0&&i.ctrlKey===!0,l=i.button===2||d;a.current=l},{checkForDefaultPrevented:!1}),onFocusOutside:q(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1})})))}),Wm=c.forwardRef((e,n)=>{const r=vn(Jn,e.__scopePopover),t=c.useRef(!1),o=c.useRef(!1);return c.createElement(Gl,X({},e,{ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var s;if((s=e.onCloseAutoFocus)===null||s===void 0||s.call(e,a),!a.defaultPrevented){var i;t.current||(i=r.triggerRef.current)===null||i===void 0||i.focus(),a.preventDefault()}t.current=!1,o.current=!1},onInteractOutside:a=>{var s,i;(s=e.onInteractOutside)===null||s===void 0||s.call(e,a),a.defaultPrevented||(t.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const d=a.target;((i=r.triggerRef.current)===null||i===void 0?void 0:i.contains(d))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}}))}),Gl=c.forwardRef((e,n)=>{const{__scopePopover:r,trapFocus:t,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:s,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:l,onInteractOutside:p,...f}=e,v=vn(Jn,r),h=fo(r);return Ga(),c.createElement(ja,{asChild:!0,loop:!0,trapped:t,onMountAutoFocus:o,onUnmountAutoFocus:a},c.createElement(Ha,{asChild:!0,disableOutsidePointerEvents:s,onInteractOutside:p,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:l,onDismiss:()=>v.onOpenChange(!1)},c.createElement(ic,X({"data-state":Yl(v.open),role:"dialog",id:v.contentId},h,f,{ref:n,style:{...f.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}}))))}),Bm="PopoverClose",Vm=c.forwardRef((e,n)=>{const{__scopePopover:r,...t}=e,o=vn(Bm,r);return c.createElement(Me.button,X({type:"button"},t,{ref:n,onClick:q(e.onClick,()=>o.onOpenChange(!1))}))}),jm=c.forwardRef((e,n)=>{const{__scopePopover:r,...t}=e,o=fo(r);return c.createElement(sc,X({},o,t,{ref:n}))});function Yl(e){return e?"open":"closed"}const Gm=Lm,Ym=Im,Zm=Km,Xm=Um,qm=Vm,Qm=jm,Zl=Gm,Xl=Ym,Jm=qm,ql=c.forwardRef(({children:e,...n},r)=>u(Zm,{children:R(Xm,{...n,ref:r,children:[e,u(Qm,{style:{fill:"white"}})]})})),Ql=({title:e,onConfirm:n,content:r,confirmText:t,cancelText:o,cancelButton:a,confirmButtonClassName:s})=>R("div",{className:"fb-box-border fb-max-w-[350px] fb-rounded fb-bg-white fb-p-6 fb-text-[rgba(0,0,0,0.65)] fb-shadow-[0_2px_8px_rgba(0,0,0,0.15)]",children:[R("div",{className:"fb-mb-6 fb-border-0 fb-border-b fb-border-solid fb-border-b-[rgba(72,94,144,0.16)] fb-px-0 fb-pb-5 fb-pt-0 fb-text-inherit",children:[u("div",{className:"fb-mb-[10px] fb-block fb-text-[16px] fb-font-semibold",children:e}),u("div",{className:"fb-leading-6",children:r})]}),R("div",{className:"fb-fle fb-flex-wrap fb-items-center",children:[u("button",{className:ve("button button-primary fb-mr-[10px]",s),onClick:n,children:t}),a??u(Jm,{className:"button",children:o??P("cancel")})]})]}),ys=({isDisabledAction:e,className:n,iconColors:r})=>{const[t,o]=c.useState(!1),a=U(l=>l.checkable&&l.checkedKeys.length?!1:l.checkable&&!l.checkedKeys.length||e),{theme:s}=ke(),{deleteFolder:i}=Nn(),d=()=>{i(U.getState().selectedKeys[0]).then(()=>{dt.success(P("delete_done")),o(!1)}).catch(l=>{dt.error(l.responseJSON.message)})};return R(Zl,{open:t,onOpenChange:l=>!l&&o(!1),children:[u(Xl,{asChild:!0,children:R(Vn,{$theme:s,disabled:a,className:ve("button",n),onClick:()=>o(!0),children:[u(qe,{icon:"deleteFolderIcon",className:ve("fb-mr-[5px]",r[s],a&&"fb-text-disabled")}),u("span",{children:P("delete")})]})}),u(ql,{align:Ot?"center":"start",sideOffset:5,children:u(Ql,{title:u("span",{children:P("delete_folder")}),content:u("span",{dangerouslySetInnerHTML:{__html:P("are_you_sure_delete_this_folder")}}),confirmText:P("delete"),onConfirm:d})})]})},{tree_mode:eb}=window.fbv_data,tb=[{key:"name",type:"sub",label:P("by_name"),children:[{key:"file",type:"radio",options:[{value:"file-name-ASC",label:P("ascending")},{value:"file-name-DESC",label:P("descending")}]}]},{key:"date",type:"sub",label:P("by_date"),children:[{key:"file",type:"radio",options:[{value:"file-date-ASC",label:P("ascending")},{value:"file-date-DESC",label:P("descending")}]}]},{key:"modified",type:"sub",label:P("by_modified"),children:[{key:"file",type:"radio",options:[{value:"file-modified-ASC",label:P("ascending")},{value:"file-modified-DESC",label:P("descending")}]}]}],nb=[{type:"separator"},{key:"file-reset",value:"file-reset",type:"item",label:P("default")}],rb=[{key:"author",type:"sub",label:P("by_author"),children:[{key:"file",type:"radio",options:[{value:"file-author-ASC",label:P("ascending")},{value:"file-author-DESC",label:P("descending")}]}]},{key:"size",type:"sub",label:P("by_size"),children:[{key:"file",type:"radio",options:[{value:"file-fb_filesize-ASC",label:P("ascending")},{value:"file-fb_filesize-DESC",label:P("descending")}]}]}],ob=[{key:"sort-folders",type:"sub",label:R(Ne,{children:[P("sort_folders")," ",u(Gn,{})]}),disabled:!0,children:[{key:"folder",type:"radio",options:[{value:"folder-name-asc",label:P("ascending")},{value:"folder-name-desc",label:P("descending")},{value:"folder-name-reset",label:P("default")}]}]},{key:"sort-files",type:"sub",disabled:!0,label:R(Ne,{children:[P("sort_files")," ",u(Gn,{})]}),children:[...tb,...eb==="attachment"?rb:[],...nb]},{key:"counter",type:"sub",label:P("count"),children:[{key:"counter",type:"radio",options:[{value:"counter_file_in_folder",label:P("count_in_folder")},{value:"counter_file_in_folder_and_sub",label:R(Ne,{children:[P("count_nested")," ",u(Gn,{})]}),disabled:!0}]}]}],ab=(e,n)=>[{key:"bulk",type:"item",label:P("bulk_select"),className:"fb-p-[0_10px]",icon:u("i",{className:ve("fb-mr-2 fb-flex fb-h-[17px] fb-w-[17px] fb-items-center [&_svg]:fb-w-[inherit] [&_svg]:fb-h-[inherit]",Gt[e]),children:u(Ru,{})})},{key:"setting",type:"item",label:P("settings"),className:"fb-p-[0_10px]",icon:u("i",{className:ve("fb-mr-2 fb-flex fb-h-[17px] fb-w-[17px] fb-items-center [&_svg]:fb-w-[inherit] [&_svg]:fb-h-[inherit]",Gt[e]),children:u(Nu,{})})},{type:"separator"},{key:"display_folder_id",type:"item",className:"fb-p-[0_10px]",label:n?P("hide_folder_id"):P("display_folder_id"),icon:u("i",{className:ve("fb-mr-2 fb-flex fb-h-[17px] fb-w-[17px] fb-items-center [&_svg]:fb-w-[inherit] [&_svg]:fb-h-[inherit]",Gt[e]),children:u(ku,{})})}],{FOLDER_COUNTER_TYPE:ib}=window.fbv_data.user_settings,sb=fn([`fb-m-0 fb-flex fb-h-[30px] fb-min-h-[30px] fb-items-center fb-overflow-hidden fb-font-medium
    [&_span]:fb-overflow-hidden [&_span]:fb-text-ellipsis`],{variants:{$theme:{default:["[&_i]:fb-h-5 [&_i]:fb-w-5"],windows:["[&_i]:fb-h-[14px] [&_i]:fb-w-[14px]","fb-rounded-[3px] fb-border fb-border-[#d3d3d3] fb-bg-white fb-text-[#1a1a1a]","focus:fb-shadow-none active:fb-border-[#d3d3d3_#d3d3d3_#2576b3] hocus:fb-border-[#d3d3d3_#d3d3d3_#2576b3]"],dropbox:["fb-rounded-none fb-border-transparent fb-bg-[#e3e3e3] fb-text-[#1a1a1a] [&_i]:fb-h-[14px] [&_i]:fb-w-[14px]","focus:fb-shadow-none active:fb-bg-[#e5e2db] hocus:fb-bg-[#e5e2db]"]},$icon:{textAndIcon:"",only:"fb-w-[30px] fb-justify-center fb-px-2 fb-py-0"}},compoundVariants:[{$theme:"default",$icon:"only",className:"fb-border-none"}],defaultVariants:{$theme:"default",$icon:"textAndIcon"}}),cb=fn(["fb-toolbar fb-flex fb-justify-between"],{variants:{$theme:{default:"fb-my-0 fb-ml-0 fb-mr-[16px] fb-border fb-border-solid fb-border-[#ccd0d4] fb-bg-white fb-px-[10px] fb-py-[11px] fb-shadow-[0_1px_1px_rgba(0,0,0,0.04)]",windows:"fb-ml-0 fb-mr-4",dropbox:"fb-mr-4"}}}),Gt={default:"fb-text-primary",windows:"fb-text-[#33302F]",dropbox:"fb-text-[#33302F]"},Vn=dn.button(({$theme:e,$icon:n})=>sb({$theme:e,$icon:n})),lb=dn.div(({$theme:e})=>cb({$theme:e})),db=e=>{const n=new URLSearchParams(window.location.search);return e&&!Bn(e)?{orderby:e.collection.mirroring.args.orderby,order:e.collection.mirroring.args.order}:{orderby:n.get("orderby"),order:n.get("order")}},ub=()=>{const e=Yt(C=>C.setModalOpen),n=U(C=>C.setCheckable),r=U(C=>C.setFolderQuery),t=U(C=>C.setFileQuery),o=U(C=>+C.selectedKeys[0]<1||!!C.editFolderId||C.isLoading),a=U(C=>C.selectedKeys),s=U(C=>C.checkable),i=U(C=>C.editFolder),d=Yt(C=>C.setDisplayFolderId),l=U(C=>C.setFolderCounter),p=Yt(C=>C.displayFolderId),{theme:f,attachmentsBrowser:v}=ke(),[h,m]=c.useState(ib),b=C=>{const $=C.value.split("-"),M=$[0],D=$[1],F=$[2];M==="folder"&&r({orderby:D,order:F}),M==="file"&&(t({orderby:D,order:F}),v?Lu(a[0],v,{order:F,orderby:D}):Au(D,F)),C.key==="counter"&&C.value!==h&&(l(C.value),m(C.value))},y=C=>{C.key==="bulk"&&n(!0),C.key==="setting"&&e(!0),C.key==="display_folder_id"&&d()},{order:g,orderby:w}=db(v),x=U(C=>({folder:`folder-${C.folderQuery.orderby}-${C.folderQuery.order}`,file:`file-${w}-${g}`,counter:h}));return u(Ne,{children:u(lb,{$theme:f,children:s?R(Ne,{children:[u(ys,{iconColors:Gt,className:"fb-mr-[5px]",isDisabledAction:o}),u(Vn,{$theme:f,onClick:()=>n(!1),className:"button fb-mr-auto",children:u("span",{className:"fb-overflow-hidden fb-text-ellipsis",children:P("cancel")})})]}):R(Ne,{children:[R(Vn,{$theme:f,disabled:o,onClick:()=>i(),className:"button fb-mr-[5px]",children:[u(qe,{className:ve("fb-mr-[5px]",Gt[f],o&&"fb-text-disabled"),icon:"editFolderIcon"}),u("span",{className:"fb-overflow-hidden fb-text-ellipsis",children:P("rename")})]}),u(ys,{iconColors:Gt,className:"fb-mr-[5px]",isDisabledAction:o}),u(gs,{selectedKeys:x,menu:ob,onClick:b,children:u(Vn,{$icon:"only",$theme:f,className:"button fb-ml-auto fb-mr-[5px]",children:u(qe,{className:Gt[f],icon:"sortIcon"})})}),u(gs,{selectedKeys:x,menu:ab(f,p),onClick:y,noSelection:!0,children:u(Vn,{$icon:"only",$theme:f,className:"button",children:u("i",{className:ve("fb-flex fb-items-center [&>svg]:fb-h-5 [&>svg]:fb-w-5",Gt[f],f!=="default"&&"fb-rotate-90"),children:u(Pu,{})})})})]})})})},fb=c.memo(ub);function ut(){return ut=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},ut.apply(this,arguments)}function Ht(e){"@babel/helpers - typeof";return Ht=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Ht(e)}function pb(e,n){if(Ht(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var t=r.call(e,n||"default");if(Ht(t)!=="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}function Jl(e){var n=pb(e,"string");return Ht(n)==="symbol"?n:String(n)}function Ce(e,n,r){return n=Jl(n),n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function Cs(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function Fe(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?Cs(Object(r),!0).forEach(function(t){Ce(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cs(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Ea(e,n){(n==null||n>e.length)&&(n=e.length);for(var r=0,t=new Array(n);r<n;r++)t[r]=e[r];return t}function hb(e){if(Array.isArray(e))return Ea(e)}function vb(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ed(e,n){if(e){if(typeof e=="string")return Ea(e,n);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ea(e,n)}}function mb(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sn(e){return hb(e)||vb(e)||ed(e)||mb()}function td(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function ws(e,n){for(var r=0;r<n.length;r++){var t=n[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,Jl(t.key),t)}}function nd(e,n,r){return n&&ws(e.prototype,n),r&&ws(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function At(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $a(e,n){return $a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,o){return t.__proto__=o,t},$a(e,n)}function rd(e,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),n&&$a(e,n)}function Br(e){return Br=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Br(e)}function bb(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function gb(e,n){if(n&&(Ht(n)==="object"||typeof n=="function"))return n;if(n!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return At(e)}function od(e){var n=bb();return function(){var t=Br(e),o;if(n){var a=Br(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return gb(this,o)}}var Z={MAC_ENTER:3,BACKSPACE:8,TAB:9,NUM_CENTER:12,ENTER:13,SHIFT:16,CTRL:17,ALT:18,PAUSE:19,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,PRINT_SCREEN:44,INSERT:45,DELETE:46,ZERO:48,ONE:49,TWO:50,THREE:51,FOUR:52,FIVE:53,SIX:54,SEVEN:55,EIGHT:56,NINE:57,QUESTION_MARK:63,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,NUM_ZERO:96,NUM_ONE:97,NUM_TWO:98,NUM_THREE:99,NUM_FOUR:100,NUM_FIVE:101,NUM_SIX:102,NUM_SEVEN:103,NUM_EIGHT:104,NUM_NINE:105,NUM_MULTIPLY:106,NUM_PLUS:107,NUM_MINUS:109,NUM_PERIOD:110,NUM_DIVISION:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,NUMLOCK:144,SEMICOLON:186,DASH:189,EQUALS:187,COMMA:188,PERIOD:190,SLASH:191,APOSTROPHE:192,SINGLE_QUOTE:222,OPEN_SQUARE_BRACKET:219,BACKSLASH:220,CLOSE_SQUARE_BRACKET:221,WIN_KEY:224,MAC_FF_META:224,WIN_IME:229,isTextModifyingKeyEvent:function(n){var r=n.keyCode;if(n.altKey&&!n.ctrlKey||n.metaKey||r>=Z.F1&&r<=Z.F12)return!1;switch(r){case Z.ALT:case Z.CAPS_LOCK:case Z.CONTEXT_MENU:case Z.CTRL:case Z.DOWN:case Z.END:case Z.ESC:case Z.HOME:case Z.INSERT:case Z.LEFT:case Z.MAC_FF_META:case Z.META:case Z.NUMLOCK:case Z.NUM_CENTER:case Z.PAGE_DOWN:case Z.PAGE_UP:case Z.PAUSE:case Z.PRINT_SCREEN:case Z.RIGHT:case Z.SHIFT:case Z.UP:case Z.WIN_KEY:case Z.WIN_KEY_RIGHT:return!1;default:return!0}},isCharacterKey:function(n){if(n>=Z.ZERO&&n<=Z.NINE||n>=Z.NUM_ZERO&&n<=Z.NUM_MULTIPLY||n>=Z.A&&n<=Z.Z||window.navigator.userAgent.indexOf("WebKit")!==-1&&n===0)return!0;switch(n){case Z.SPACE:case Z.QUESTION_MARK:case Z.NUM_PLUS:case Z.NUM_MINUS:case Z.NUM_PERIOD:case Z.NUM_DIVISION:case Z.SEMICOLON:case Z.DASH:case Z.EQUALS:case Z.COMMA:case Z.PERIOD:case Z.SLASH:case Z.APOSTROPHE:case Z.SINGLE_QUOTE:case Z.OPEN_SQUARE_BRACKET:case Z.BACKSLASH:case Z.CLOSE_SQUARE_BRACKET:return!0;default:return!1}}},Ta={},yb=function(n){};function Cb(e,n){}function wb(e,n){}function _b(){Ta={}}function ad(e,n,r){!n&&!Ta[r]&&(e(!1,r),Ta[r]=!0)}function yt(e,n){ad(Cb,e,n)}function Sb(e,n){ad(wb,e,n)}yt.preMessage=yb;yt.resetWarned=_b;yt.noteOnce=Sb;function ht(e){"@babel/helpers - typeof";return ht=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},ht(e)}function xb(e,n){if(ht(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var t=r.call(e,n||"default");if(ht(t)!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}function id(e){var n=xb(e,"string");return ht(n)=="symbol"?n:String(n)}function $e(e,n,r){return n=id(n),n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function _s(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function ie(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?_s(Object(r),!0).forEach(function(t){$e(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var Eb=`accept acceptCharset accessKey action allowFullScreen allowTransparency
    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge
    charSet checked classID className colSpan cols content contentEditable contextMenu
    controls coords crossOrigin data dateTime default defer dir disabled download draggable
    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder
    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity
    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media
    mediaGroup method min minLength multiple muted name noValidate nonce open
    optimum pattern placeholder poster preload radioGroup readOnly rel required
    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected
    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style
    summary tabIndex target title type useMap value width wmode wrap`,$b=`onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown
    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick
    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown
    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel
    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough
    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata
    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError`,Tb="".concat(Eb," ").concat($b).split(/[\s\n]+/),Mb="aria-",Db="data-";function Ss(e,n){return e.indexOf(n)===0}function sd(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r;n===!1?r={aria:!0,data:!0,attr:!0}:n===!0?r={aria:!0}:r=ie({},n);var t={};return Object.keys(e).forEach(function(o){(r.aria&&(o==="role"||Ss(o,Mb))||r.data&&Ss(o,Db)||r.attr&&Tb.includes(o))&&(t[o]=e[o])}),t}var cd={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(e){(function(){var n={}.hasOwnProperty;function r(){for(var t=[],o=0;o<arguments.length;o++){var a=arguments[o];if(a){var s=typeof a;if(s==="string"||s==="number")t.push(a);else if(Array.isArray(a)){if(a.length){var i=r.apply(null,a);i&&t.push(i)}}else if(s==="object"){if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]")){t.push(a.toString());continue}for(var d in a)n.call(a,d)&&a[d]&&t.push(d)}}}return t.join(" ")}e.exports?(r.default=r,e.exports=r):window.classNames=r})()})(cd);var Ob=cd.exports;const Qe=Qr(Ob);var ci=c.createContext(null);function Rb(e,n){if(e==null)return{};var r={},t=Object.keys(e),o,a;for(a=0;a<t.length;a++)o=t[a],!(n.indexOf(o)>=0)&&(r[o]=e[o]);return r}function po(e,n){if(e==null)return{};var r=Rb(e,n),t,o;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)t=a[o],!(n.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(r[t]=e[t])}return r}var Nb=function(n){for(var r=n.prefixCls,t=n.level,o=n.isStart,a=n.isEnd,s="".concat(r,"-indent-unit"),i=[],d=0;d<t;d+=1){var l;i.push(c.createElement("span",{key:d,className:Qe(s,(l={},Ce(l,"".concat(s,"-start"),o[d]),Ce(l,"".concat(s,"-end"),a[d]),l))}))}return c.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},i)};const kb=c.memo(Nb);var ld={exports:{}},pe={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pe=typeof Symbol=="function"&&Symbol.for,li=Pe?Symbol.for("react.element"):60103,di=Pe?Symbol.for("react.portal"):60106,ho=Pe?Symbol.for("react.fragment"):60107,vo=Pe?Symbol.for("react.strict_mode"):60108,mo=Pe?Symbol.for("react.profiler"):60114,bo=Pe?Symbol.for("react.provider"):60109,go=Pe?Symbol.for("react.context"):60110,ui=Pe?Symbol.for("react.async_mode"):60111,yo=Pe?Symbol.for("react.concurrent_mode"):60111,Co=Pe?Symbol.for("react.forward_ref"):60112,wo=Pe?Symbol.for("react.suspense"):60113,Pb=Pe?Symbol.for("react.suspense_list"):60120,_o=Pe?Symbol.for("react.memo"):60115,So=Pe?Symbol.for("react.lazy"):60116,Lb=Pe?Symbol.for("react.block"):60121,Ab=Pe?Symbol.for("react.fundamental"):60117,Ib=Pe?Symbol.for("react.responder"):60118,Fb=Pe?Symbol.for("react.scope"):60119;function ct(e){if(typeof e=="object"&&e!==null){var n=e.$$typeof;switch(n){case li:switch(e=e.type,e){case ui:case yo:case ho:case mo:case vo:case wo:return e;default:switch(e=e&&e.$$typeof,e){case go:case Co:case So:case _o:case bo:return e;default:return n}}case di:return n}}}function dd(e){return ct(e)===yo}pe.AsyncMode=ui;pe.ConcurrentMode=yo;pe.ContextConsumer=go;pe.ContextProvider=bo;pe.Element=li;pe.ForwardRef=Co;pe.Fragment=ho;pe.Lazy=So;pe.Memo=_o;pe.Portal=di;pe.Profiler=mo;pe.StrictMode=vo;pe.Suspense=wo;pe.isAsyncMode=function(e){return dd(e)||ct(e)===ui};pe.isConcurrentMode=dd;pe.isContextConsumer=function(e){return ct(e)===go};pe.isContextProvider=function(e){return ct(e)===bo};pe.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===li};pe.isForwardRef=function(e){return ct(e)===Co};pe.isFragment=function(e){return ct(e)===ho};pe.isLazy=function(e){return ct(e)===So};pe.isMemo=function(e){return ct(e)===_o};pe.isPortal=function(e){return ct(e)===di};pe.isProfiler=function(e){return ct(e)===mo};pe.isStrictMode=function(e){return ct(e)===vo};pe.isSuspense=function(e){return ct(e)===wo};pe.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===ho||e===yo||e===mo||e===vo||e===wo||e===Pb||typeof e=="object"&&e!==null&&(e.$$typeof===So||e.$$typeof===_o||e.$$typeof===bo||e.$$typeof===go||e.$$typeof===Co||e.$$typeof===Ab||e.$$typeof===Ib||e.$$typeof===Fb||e.$$typeof===Lb)};pe.typeOf=ct;ld.exports=pe;var ud=ld.exports;function Vr(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=[];return Ve.Children.forEach(e,function(t){t==null&&!n.keepEmpty||(Array.isArray(t)?r=r.concat(Vr(t)):ud.isFragment(t)&&t.props?r=r.concat(Vr(t.props.children,n)):r.push(t))}),r}function Hb(e,n){var r=ie({},e);return Array.isArray(n)&&n.forEach(function(t){delete r[t]}),r}var Kb=["children"];function fd(e,n){return"".concat(e,"-").concat(n)}function Ub(e){return e&&e.type&&e.type.isTreeNode}function dr(e,n){return e??n}function jr(e){var n=e||{},r=n.title,t=n._title,o=n.key,a=n.children,s=r||"title";return{title:s,_title:t||[s],key:o||"key",children:a||"children"}}function zb(e){function n(r){var t=Vr(r);return t.map(function(o){if(!Ub(o))return yt(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var a=o.key,s=o.props,i=s.children,d=po(s,Kb),l=Fe({key:a},d),p=n(i);return p.length&&(l.children=p),l}).filter(function(o){return o})}return n(e)}function ra(e,n,r){var t=jr(r),o=t._title,a=t.key,s=t.children,i=new Set(n===!0?[]:n),d=[];function l(p){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return p.map(function(v,h){for(var m=fd(f?f.pos:"0",h),b=dr(v[a],m),y,g=0;g<o.length;g+=1){var w=o[g];if(v[w]!==void 0){y=v[w];break}}var x=Fe(Fe({},Hb(v,[].concat(sn(o),[a,s]))),{},{title:y,key:b,parent:f,pos:m,children:null,data:v,isStart:[].concat(sn(f?f.isStart:[]),[h===0]),isEnd:[].concat(sn(f?f.isEnd:[]),[h===p.length-1])});return d.push(x),n===!0||i.has(b)?x.children=l(v[s]||[],x):x.children=[],x})}return l(e),d}function Wb(e,n,r){var t={};Ht(r)==="object"?t=r:t={externalGetKey:r},t=t||{};var o=t,a=o.childrenPropName,s=o.externalGetKey,i=o.fieldNames,d=jr(i),l=d.key,p=d.children,f=a||p,v;s?typeof s=="string"?v=function(b){return b[s]}:typeof s=="function"&&(v=function(b){return s(b)}):v=function(b,y){return dr(b[l],y)};function h(m,b,y,g){var w=m?m[f]:e,x=m?fd(y.pos,b):"0",C=m?[].concat(sn(g),[m]):[];if(m){var $=v(m,x),M={node:m,index:b,pos:x,key:$,parentPos:y.node?y.pos:null,level:y.level+1,nodes:C};n(M)}w&&w.forEach(function(D,F){h(D,F,{node:m,pos:x,level:y?y.level+1:-1},C)})}h(null)}function Bb(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.initWrapper,t=n.processEntity,o=n.onProcessFinished,a=n.externalGetKey,s=n.childrenPropName,i=n.fieldNames,d=arguments.length>2?arguments[2]:void 0,l=a||d,p={},f={},v={posEntities:p,keyEntities:f};return r&&(v=r(v)||v),Wb(e,function(h){var m=h.node,b=h.index,y=h.pos,g=h.key,w=h.parentPos,x=h.level,C=h.nodes,$={node:m,nodes:C,index:b,key:g,pos:y,level:x},M=dr(g,y);p[y]=$,f[M]=$,$.parent=p[w],$.parent&&($.parent.children=$.parent.children||[],$.parent.children.push($)),t&&t($,v)},{externalGetKey:l,childrenPropName:s,fieldNames:i}),o&&o(v),v}function Yn(e,n){var r=n.expandedKeys,t=n.selectedKeys,o=n.loadedKeys,a=n.loadingKeys,s=n.checkedKeys,i=n.halfCheckedKeys,d=n.dragOverNodeKey,l=n.dropPosition,p=n.keyEntities,f=p[e],v={eventKey:e,expanded:r.indexOf(e)!==-1,selected:t.indexOf(e)!==-1,loaded:o.indexOf(e)!==-1,loading:a.indexOf(e)!==-1,checked:s.indexOf(e)!==-1,halfChecked:i.indexOf(e)!==-1,pos:String(f?f.pos:""),dragOver:d===e&&l===0,dragOverGapTop:d===e&&l===-1,dragOverGapBottom:d===e&&l===1};return v}function Ee(e){var n=e.data,r=e.expanded,t=e.selected,o=e.checked,a=e.loaded,s=e.loading,i=e.halfChecked,d=e.dragOver,l=e.dragOverGapTop,p=e.dragOverGapBottom,f=e.pos,v=e.active,h=e.eventKey,m=Fe(Fe({},n),{},{expanded:r,selected:t,checked:o,loaded:a,loading:s,halfChecked:i,dragOver:d,dragOverGapTop:l,dragOverGapBottom:p,pos:f,active:v,key:h});return"props"in m||Object.defineProperty(m,"props",{get:function(){return yt(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),m}var Vb=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],xs="open",Es="close",jb="---",Gb=function(e){rd(r,e);var n=od(r);function r(){var t;td(this,r);for(var o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];return t=n.call.apply(n,[this].concat(a)),t.state={dragNodeHighlight:!1},t.selectHandle=void 0,t.cacheIndent=void 0,t.onSelectorClick=function(i){var d=t.props.context.onNodeClick;d(i,Ee(t.props)),t.isSelectable()?t.onSelect(i):t.onCheck(i)},t.onSelectorDoubleClick=function(i){var d=t.props.context.onNodeDoubleClick;d(i,Ee(t.props))},t.onSelect=function(i){if(!t.isDisabled()){var d=t.props.context.onNodeSelect;i.preventDefault(),d(i,Ee(t.props))}},t.onCheck=function(i){if(!t.isDisabled()){var d=t.props,l=d.disableCheckbox,p=d.checked,f=t.props.context.onNodeCheck;if(!(!t.isCheckable()||l)){i.preventDefault();var v=!p;f(i,Ee(t.props),v)}}},t.onMouseEnter=function(i){var d=t.props.context.onNodeMouseEnter;d(i,Ee(t.props))},t.onMouseLeave=function(i){var d=t.props.context.onNodeMouseLeave;d(i,Ee(t.props))},t.onContextMenu=function(i){var d=t.props.context.onNodeContextMenu;d(i,Ee(t.props))},t.onDragStart=function(i){var d=t.props.context.onNodeDragStart;i.stopPropagation(),t.setState({dragNodeHighlight:!0}),d(i,At(t));try{i.dataTransfer.setData("text/plain","")}catch{}},t.onDragEnter=function(i){var d=t.props.context.onNodeDragEnter;i.preventDefault(),i.stopPropagation(),d(i,At(t))},t.onDragOver=function(i){var d=t.props.context.onNodeDragOver;i.preventDefault(),i.stopPropagation(),d(i,At(t))},t.onDragLeave=function(i){var d=t.props.context.onNodeDragLeave;i.stopPropagation(),d(i,At(t))},t.onDragEnd=function(i){var d=t.props.context.onNodeDragEnd;i.stopPropagation(),t.setState({dragNodeHighlight:!1}),d(i,At(t))},t.onDrop=function(i){var d=t.props.context.onNodeDrop;i.preventDefault(),i.stopPropagation(),t.setState({dragNodeHighlight:!1}),d(i,At(t))},t.onExpand=function(i){var d=t.props,l=d.loading,p=d.context.onNodeExpand;l||p(i,Ee(t.props))},t.setSelectHandle=function(i){t.selectHandle=i},t.getNodeState=function(){var i=t.props.expanded;return t.isLeaf()?null:i?xs:Es},t.hasChildren=function(){var i=t.props.eventKey,d=t.props.context.keyEntities,l=d[i]||{},p=l.children;return!!(p||[]).length},t.isLeaf=function(){var i=t.props,d=i.isLeaf,l=i.loaded,p=t.props.context.loadData,f=t.hasChildren();return d===!1?!1:d||!p&&!f||p&&l&&!f},t.isDisabled=function(){var i=t.props.disabled,d=t.props.context.disabled;return!!(d||i)},t.isCheckable=function(){var i=t.props.checkable,d=t.props.context.checkable;return!d||i===!1?!1:d},t.syncLoadData=function(i){var d=i.expanded,l=i.loading,p=i.loaded,f=t.props.context,v=f.loadData,h=f.onNodeLoad;l||v&&d&&!t.isLeaf()&&!t.hasChildren()&&!p&&h(Ee(t.props))},t.isDraggable=function(){var i=t.props,d=i.data,l=i.context.draggable;return!!(l&&(!l.nodeDraggable||l.nodeDraggable(d)))},t.renderDragHandler=function(){var i=t.props.context,d=i.draggable,l=i.prefixCls;return d!=null&&d.icon?c.createElement("span",{className:"".concat(l,"-draggable-icon")},d.icon):null},t.renderSwitcherIconDom=function(i){var d=t.props.switcherIcon,l=t.props.context.switcherIcon,p=d||l;return typeof p=="function"?p(Fe(Fe({},t.props),{},{isLeaf:i})):p},t.renderSwitcher=function(){var i=t.props.expanded,d=t.props.context.prefixCls;if(t.isLeaf()){var l=t.renderSwitcherIconDom(!0);return l!==!1?c.createElement("span",{className:Qe("".concat(d,"-switcher"),"".concat(d,"-switcher-noop"))},l):null}var p=Qe("".concat(d,"-switcher"),"".concat(d,"-switcher_").concat(i?xs:Es)),f=t.renderSwitcherIconDom(!1);return f!==!1?c.createElement("span",{onClick:t.onExpand,className:p},f):null},t.renderCheckbox=function(){var i=t.props,d=i.checked,l=i.halfChecked,p=i.disableCheckbox,f=t.props.context.prefixCls,v=t.isDisabled(),h=t.isCheckable();if(!h)return null;var m=typeof h!="boolean"?h:null;return c.createElement("span",{className:Qe("".concat(f,"-checkbox"),d&&"".concat(f,"-checkbox-checked"),!d&&l&&"".concat(f,"-checkbox-indeterminate"),(v||p)&&"".concat(f,"-checkbox-disabled")),onClick:t.onCheck},m)},t.renderIcon=function(){var i=t.props.loading,d=t.props.context.prefixCls;return c.createElement("span",{className:Qe("".concat(d,"-iconEle"),"".concat(d,"-icon__").concat(t.getNodeState()||"docu"),i&&"".concat(d,"-icon_loading"))})},t.renderSelector=function(){var i=t.state.dragNodeHighlight,d=t.props,l=d.title,p=l===void 0?jb:l,f=d.selected,v=d.icon,h=d.loading,m=d.data,b=t.props.context,y=b.prefixCls,g=b.showIcon,w=b.icon,x=b.loadData,C=b.titleRender,$=t.isDisabled(),M="".concat(y,"-node-content-wrapper"),D;if(g){var F=v||w;D=F?c.createElement("span",{className:Qe("".concat(y,"-iconEle"),"".concat(y,"-icon__customize"))},typeof F=="function"?F(t.props):F):t.renderIcon()}else x&&h&&(D=t.renderIcon());var K;typeof p=="function"?K=p(m):C?K=C(m):K=p;var O=c.createElement("span",{className:"".concat(y,"-title")},K);return c.createElement("span",{ref:t.setSelectHandle,title:typeof p=="string"?p:"",className:Qe("".concat(M),"".concat(M,"-").concat(t.getNodeState()||"normal"),!$&&(f||i)&&"".concat(y,"-node-selected")),onMouseEnter:t.onMouseEnter,onMouseLeave:t.onMouseLeave,onContextMenu:t.onContextMenu,onClick:t.onSelectorClick,onDoubleClick:t.onSelectorDoubleClick},D,O,t.renderDropIndicator())},t.renderDropIndicator=function(){var i=t.props,d=i.disabled,l=i.eventKey,p=t.props.context,f=p.draggable,v=p.dropLevelOffset,h=p.dropPosition,m=p.prefixCls,b=p.indent,y=p.dropIndicatorRender,g=p.dragOverNodeKey,w=p.direction,x=!!f,C=!d&&x&&g===l,$=b??t.cacheIndent;return t.cacheIndent=b,C?y({dropPosition:h,dropLevelOffset:v,indent:$,prefixCls:m,direction:w}):null},t}return nd(r,[{key:"componentDidMount",value:function(){this.syncLoadData(this.props)}},{key:"componentDidUpdate",value:function(){this.syncLoadData(this.props)}},{key:"isSelectable",value:function(){var o=this.props.selectable,a=this.props.context.selectable;return typeof o=="boolean"?o:a}},{key:"render",value:function(){var o,a=this.props,s=a.eventKey,i=a.className,d=a.style,l=a.dragOver,p=a.dragOverGapTop,f=a.dragOverGapBottom,v=a.isLeaf,h=a.isStart,m=a.isEnd,b=a.expanded,y=a.selected,g=a.checked,w=a.halfChecked,x=a.loading,C=a.domRef,$=a.active;a.data;var M=a.onMouseMove,D=a.selectable,F=po(a,Vb),K=this.props.context,O=K.prefixCls,z=K.filterTreeNode,I=K.keyEntities,A=K.dropContainerKey,N=K.dropTargetKey,T=K.draggingNodeKey,k=this.isDisabled(),L=sd(F,{aria:!0,data:!0}),V=I[s]||{},E=V.level,W=m[m.length-1],B=this.isDraggable(),Q=!k&&B,J=T===s,he=D!==void 0?{"aria-selected":!!D}:void 0;return c.createElement("div",ut({ref:C,className:Qe(i,"".concat(O,"-treenode"),(o={},Ce(o,"".concat(O,"-treenode-disabled"),k),Ce(o,"".concat(O,"-treenode-switcher-").concat(b?"open":"close"),!v),Ce(o,"".concat(O,"-treenode-checkbox-checked"),g),Ce(o,"".concat(O,"-treenode-checkbox-indeterminate"),w),Ce(o,"".concat(O,"-treenode-selected"),y),Ce(o,"".concat(O,"-treenode-loading"),x),Ce(o,"".concat(O,"-treenode-active"),$),Ce(o,"".concat(O,"-treenode-leaf-last"),W),Ce(o,"".concat(O,"-treenode-draggable"),B),Ce(o,"dragging",J),Ce(o,"drop-target",N===s),Ce(o,"drop-container",A===s),Ce(o,"drag-over",!k&&l),Ce(o,"drag-over-gap-top",!k&&p),Ce(o,"drag-over-gap-bottom",!k&&f),Ce(o,"filter-node",z&&z(Ee(this.props))),o)),style:d,draggable:Q,"aria-grabbed":J,onDragStart:Q?this.onDragStart:void 0,onDragEnter:B?this.onDragEnter:void 0,onDragOver:B?this.onDragOver:void 0,onDragLeave:B?this.onDragLeave:void 0,onDrop:B?this.onDrop:void 0,onDragEnd:B?this.onDragEnd:void 0,onMouseMove:M},he,L),c.createElement(kb,{prefixCls:O,level:E,isStart:h,isEnd:m}),this.renderDragHandler(),this.renderSwitcher(),this.renderCheckbox(),this.renderSelector())}}]),r}(c.Component),er=function(n){return c.createElement(ci.Consumer,null,function(r){return c.createElement(Gb,ut({},n,{context:r}))})};er.displayName="TreeNode";er.isTreeNode=1;function Lt(e,n){if(!e)return[];var r=e.slice(),t=r.indexOf(n);return t>=0&&r.splice(t,1),r}function Vt(e,n){var r=(e||[]).slice();return r.indexOf(n)===-1&&r.push(n),r}function fi(e){return e.split("-")}function Yb(e,n){var r=[],t=n[e];function o(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];a.forEach(function(s){var i=s.key,d=s.children;r.push(i),o(d)})}return o(t.children),r}function Zb(e){if(e.parent){var n=fi(e.pos);return Number(n[n.length-1])===e.parent.children.length-1}return!1}function Xb(e){var n=fi(e.pos);return Number(n[n.length-1])===0}function $s(e,n,r,t,o,a,s,i,d,l){var p,f=e.clientX,v=e.clientY,h=e.target.getBoundingClientRect(),m=h.top,b=h.height,y=(l==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-f),g=(y-12)/t,w=i[r.props.eventKey];if(v<m+b/2){var x=s.findIndex(function(T){return T.key===w.key}),C=x<=0?0:x-1,$=s[C].key;w=i[$]}var M=w.key,D=w,F=w.key,K=0,O=0;if(!d.includes(M))for(var z=0;z<g&&Zb(w);z+=1)w=w.parent,O+=1;var I=n.props.data,A=w.node,N=!0;return Xb(w)&&w.level===0&&v<m+b/2&&a({dragNode:I,dropNode:A,dropPosition:-1})&&w.key===r.props.eventKey?K=-1:(D.children||[]).length&&d.includes(F)?a({dragNode:I,dropNode:A,dropPosition:0})?K=0:N=!1:O===0?g>-1.5?a({dragNode:I,dropNode:A,dropPosition:1})?K=1:N=!1:a({dragNode:I,dropNode:A,dropPosition:0})?K=0:a({dragNode:I,dropNode:A,dropPosition:1})?K=1:N=!1:a({dragNode:I,dropNode:A,dropPosition:1})?K=1:N=!1,{dropPosition:K,dropLevelOffset:O,dropTargetKey:w.key,dropTargetPos:w.pos,dragOverNodeKey:F,dropContainerKey:K===0?null:((p=w.parent)===null||p===void 0?void 0:p.key)||null,dropAllowed:N}}function Ts(e,n){if(e){var r=n.multiple;return r?e.slice():e.length?[e[0]]:e}}function oa(e){if(!e)return null;var n;if(Array.isArray(e))n={checkedKeys:e,halfCheckedKeys:void 0};else if(Ht(e)==="object")n={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return yt(!1,"`checkedKeys` is not an array or an object"),null;return n}function Ms(e,n){var r=new Set;function t(o){if(!r.has(o)){var a=n[o];if(a){r.add(o);var s=a.parent,i=a.node;i.disabled||s&&t(s.key)}}}return(e||[]).forEach(function(o){t(o)}),sn(r)}function pd(e){if(e==null)throw new TypeError("Cannot destructure "+e)}function qb(e){if(Array.isArray(e))return e}function Qb(e,n){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var t,o,a,s,i=[],d=!0,l=!1;try{if(a=(r=r.call(e)).next,n===0){if(Object(r)!==r)return;d=!1}else for(;!(d=(t=a.call(r)).done)&&(i.push(t.value),i.length!==n);d=!0);}catch(p){l=!0,o=p}finally{try{if(!d&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(l)throw o}}return i}}function Jb(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function on(e,n){return qb(e)||Qb(e,n)||ed(e,n)||Jb()}function xo(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}var Ds=xo()?c.useLayoutEffect:c.useEffect,tr=function(n,r){var t=c.useRef(!0);Ds(function(){return n(t.current)},r),Ds(function(){return t.current=!1,function(){t.current=!0}},[])};function eg(e){if(Array.isArray(e))return e}function tg(e,n){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var t,o,a,s,i=[],d=!0,l=!1;try{if(a=(r=r.call(e)).next,n===0){if(Object(r)!==r)return;d=!1}else for(;!(d=(t=a.call(r)).done)&&(i.push(t.value),i.length!==n);d=!0);}catch(p){l=!0,o=p}finally{try{if(!d&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(l)throw o}}return i}}function Os(e,n){(n==null||n>e.length)&&(n=e.length);for(var r=0,t=new Array(n);r<n;r++)t[r]=e[r];return t}function ng(e,n){if(e){if(typeof e=="string")return Os(e,n);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Os(e,n)}}function rg(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function He(e,n){return eg(e)||tg(e,n)||ng(e,n)||rg()}function og(e,n){if(e==null)return{};var r={},t=Object.keys(e),o,a;for(a=0;a<t.length;a++)o=t[a],!(n.indexOf(o)>=0)&&(r[o]=e[o]);return r}function Ma(e,n){if(e==null)return{};var r=og(e,n),t,o;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)t=a[o],!(n.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(r[t]=e[t])}return r}function hd(e,n){typeof e=="function"?e(n):ht(e)==="object"&&e&&"current"in e&&(e.current=n)}function ag(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var t=n.filter(function(o){return o});return t.length<=1?t[0]:function(o){n.forEach(function(a){hd(a,o)})}}function vd(e){var n,r,t=ud.isMemo(e)?e.type.type:e.type;return!(typeof t=="function"&&!((n=t.prototype)!==null&&n!==void 0&&n.render)||typeof e=="function"&&!((r=e.prototype)!==null&&r!==void 0&&r.render))}function ig(e){return e instanceof HTMLElement||e instanceof SVGElement}function Gr(e){return ig(e)?e:e instanceof Ve.Component?Iu.findDOMNode(e):null}var Zt=new Map;function sg(e){e.forEach(function(n){var r,t=n.target;(r=Zt.get(t))===null||r===void 0||r.forEach(function(o){return o(t)})})}var md=new Fu(sg);function cg(e,n){Zt.has(e)||(Zt.set(e,new Set),md.observe(e)),Zt.get(e).add(n)}function lg(e,n){Zt.has(e)&&(Zt.get(e).delete(n),Zt.get(e).size||(md.unobserve(e),Zt.delete(e)))}function ur(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function Rs(e,n){for(var r=0;r<n.length;r++){var t=n[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,id(t.key),t)}}function fr(e,n,r){return n&&Rs(e.prototype,n),r&&Rs(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Da(e,n){return Da=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,o){return t.__proto__=o,t},Da(e,n)}function Eo(e,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),n&&Da(e,n)}function Yr(e){return Yr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Yr(e)}function dg(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Oa(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ug(e,n){if(n&&(ht(n)==="object"||typeof n=="function"))return n;if(n!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Oa(e)}function $o(e){var n=dg();return function(){var t=Yr(e),o;if(n){var a=Yr(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return ug(this,o)}}var fg=function(e){Eo(r,e);var n=$o(r);function r(){return ur(this,r),n.apply(this,arguments)}return fr(r,[{key:"render",value:function(){return this.props.children}}]),r}(c.Component),Ra=c.createContext(null);function pg(e){var n=e.children,r=e.onBatchResize,t=c.useRef(0),o=c.useRef([]),a=c.useContext(Ra),s=c.useCallback(function(i,d,l){t.current+=1;var p=t.current;o.current.push({size:i,element:d,data:l}),Promise.resolve().then(function(){p===t.current&&(r==null||r(o.current),o.current=[])}),a==null||a(i,d,l)},[r,a]);return c.createElement(Ra.Provider,{value:s},n)}function hg(e,n){var r=e.children,t=e.disabled,o=c.useRef(null),a=c.useRef(null),s=c.useContext(Ra),i=typeof r=="function",d=i?r(o):r,l=c.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),p=!i&&c.isValidElement(d)&&vd(d),f=p?d.ref:null,v=c.useMemo(function(){return ag(f,o)},[f,o]),h=function(){return Gr(o.current)||Gr(a.current)};c.useImperativeHandle(n,function(){return h()});var m=c.useRef(e);m.current=e;var b=c.useCallback(function(y){var g=m.current,w=g.onResize,x=g.data,C=y.getBoundingClientRect(),$=C.width,M=C.height,D=y.offsetWidth,F=y.offsetHeight,K=Math.floor($),O=Math.floor(M);if(l.current.width!==K||l.current.height!==O||l.current.offsetWidth!==D||l.current.offsetHeight!==F){var z={width:K,height:O,offsetWidth:D,offsetHeight:F};l.current=z;var I=D===Math.round($)?$:D,A=F===Math.round(M)?M:F,N=ie(ie({},z),{},{offsetWidth:I,offsetHeight:A});s==null||s(N,y,x),w&&Promise.resolve().then(function(){w(N,y)})}},[]);return c.useEffect(function(){var y=h();return y&&!t&&cg(y,b),function(){return lg(y,b)}},[o.current,t]),c.createElement(fg,{ref:a},p?c.cloneElement(d,{ref:v}):d)}var vg=c.forwardRef(hg),mg="rc-observer-key";function bg(e,n){var r=e.children,t=typeof r=="function"?[r]:Vr(r);return t.map(function(o,a){var s=(o==null?void 0:o.key)||"".concat(mg,"-").concat(a);return c.createElement(vg,fe({},e,{key:s,ref:a===0?n:void 0}),o)})}var bd=c.forwardRef(bg);bd.Collection=pg;var gd=c.forwardRef(function(e,n){var r=e.height,t=e.offset,o=e.children,a=e.prefixCls,s=e.onInnerResize,i=e.innerProps,d={},l={display:"flex",flexDirection:"column"};return t!==void 0&&(d={height:r,position:"relative",overflow:"hidden"},l=ie(ie({},l),{},{transform:"translateY(".concat(t,"px)"),position:"absolute",left:0,right:0,top:0})),c.createElement("div",{style:d},c.createElement(bd,{onResize:function(f){var v=f.offsetHeight;v&&s&&s()}},c.createElement("div",fe({style:l,className:Qe($e({},"".concat(a,"-holder-inner"),a)),ref:n},i),o)))});gd.displayName="Filler";var yd=function(n){return+setTimeout(n,16)},Cd=function(n){return clearTimeout(n)};typeof window<"u"&&"requestAnimationFrame"in window&&(yd=function(n){return window.requestAnimationFrame(n)},Cd=function(n){return window.cancelAnimationFrame(n)});var Ns=0,pi=new Map;function wd(e){pi.delete(e)}var ft=function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;Ns+=1;var t=Ns;function o(a){if(a===0)wd(t),n();else{var s=yd(function(){o(a-1)});pi.set(t,s)}}return o(r),t};ft.cancel=function(e){var n=pi.get(e);return wd(n),Cd(n)};var gg=20;function ks(e){return"touches"in e?e.touches[0].pageY:e.pageY}var yg=function(e){Eo(r,e);var n=$o(r);function r(){var t;ur(this,r);for(var o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];return t=n.call.apply(n,[this].concat(a)),t.moveRaf=null,t.scrollbarRef=c.createRef(),t.thumbRef=c.createRef(),t.visibleTimeout=null,t.state={dragging:!1,pageY:null,startTop:null,visible:!1},t.delayHidden=function(){clearTimeout(t.visibleTimeout),t.setState({visible:!0}),t.visibleTimeout=setTimeout(function(){t.setState({visible:!1})},2e3)},t.onScrollbarTouchStart=function(i){i.preventDefault()},t.onContainerMouseDown=function(i){i.stopPropagation(),i.preventDefault()},t.patchEvents=function(){window.addEventListener("mousemove",t.onMouseMove),window.addEventListener("mouseup",t.onMouseUp),t.thumbRef.current.addEventListener("touchmove",t.onMouseMove),t.thumbRef.current.addEventListener("touchend",t.onMouseUp)},t.removeEvents=function(){window.removeEventListener("mousemove",t.onMouseMove),window.removeEventListener("mouseup",t.onMouseUp),t.thumbRef.current&&(t.thumbRef.current.removeEventListener("touchmove",t.onMouseMove),t.thumbRef.current.removeEventListener("touchend",t.onMouseUp)),ft.cancel(t.moveRaf)},t.onMouseDown=function(i){var d=t.props.onStartMove;t.setState({dragging:!0,pageY:ks(i),startTop:t.getTop()}),d(),t.patchEvents(),i.stopPropagation(),i.preventDefault()},t.onMouseMove=function(i){var d=t.state,l=d.dragging,p=d.pageY,f=d.startTop,v=t.props.onScroll;if(ft.cancel(t.moveRaf),l){var h=ks(i)-p,m=f+h,b=t.getEnableScrollRange(),y=t.getEnableHeightRange(),g=y?m/y:0,w=Math.ceil(g*b);t.moveRaf=ft(function(){v(w)})}},t.onMouseUp=function(){var i=t.props.onStopMove;t.setState({dragging:!1}),i(),t.removeEvents()},t.getSpinHeight=function(){var i=t.props,d=i.height,l=i.count,p=d/l*10;return p=Math.max(p,gg),p=Math.min(p,d/2),Math.floor(p)},t.getEnableScrollRange=function(){var i=t.props,d=i.scrollHeight,l=i.height;return d-l||0},t.getEnableHeightRange=function(){var i=t.props.height,d=t.getSpinHeight();return i-d||0},t.getTop=function(){var i=t.props.scrollTop,d=t.getEnableScrollRange(),l=t.getEnableHeightRange();if(i===0||d===0)return 0;var p=i/d;return p*l},t.showScroll=function(){var i=t.props,d=i.height,l=i.scrollHeight;return l>d},t}return fr(r,[{key:"componentDidMount",value:function(){this.scrollbarRef.current.addEventListener("touchstart",this.onScrollbarTouchStart),this.thumbRef.current.addEventListener("touchstart",this.onMouseDown)}},{key:"componentDidUpdate",value:function(o){o.scrollTop!==this.props.scrollTop&&this.delayHidden()}},{key:"componentWillUnmount",value:function(){var o,a;this.removeEvents(),(o=this.scrollbarRef.current)===null||o===void 0||o.removeEventListener("touchstart",this.onScrollbarTouchStart),(a=this.thumbRef.current)===null||a===void 0||a.removeEventListener("touchstart",this.onMouseDown),clearTimeout(this.visibleTimeout)}},{key:"render",value:function(){var o=this.state,a=o.dragging,s=o.visible,i=this.props,d=i.prefixCls,l=i.direction,p=this.getSpinHeight(),f=this.getTop(),v=this.showScroll(),h=v&&s,m=l==="rtl"?{left:0}:{right:0};return c.createElement("div",{ref:this.scrollbarRef,className:Qe("".concat(d,"-scrollbar"),$e({},"".concat(d,"-scrollbar-show"),v)),style:ie(ie({width:8,top:0,bottom:0},m),{},{position:"absolute",display:h?null:"none"}),onMouseDown:this.onContainerMouseDown,onMouseMove:this.delayHidden},c.createElement("div",{ref:this.thumbRef,className:Qe("".concat(d,"-scrollbar-thumb"),$e({},"".concat(d,"-scrollbar-thumb-moving"),a)),style:{width:"100%",height:p,top:f,left:0,position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"},onMouseDown:this.onMouseDown}))}}]),r}(c.Component);function Cg(e){var n=e.children,r=e.setRef,t=c.useCallback(function(o){r(o)},[]);return c.cloneElement(n,{ref:t})}function wg(e,n,r,t,o,a){var s=a.getKey;return e.slice(n,r+1).map(function(i,d){var l=n+d,p=o(i,l,{}),f=s(i);return c.createElement(Cg,{key:f,setRef:function(h){return t(i,h)}},p)})}var _g=function(){function e(){ur(this,e),this.maps=void 0,this.maps=Object.create(null)}return fr(e,[{key:"set",value:function(r,t){this.maps[r]=t}},{key:"get",value:function(r){return this.maps[r]}}]),e}();function Sg(e,n,r){var t=c.useState(0),o=He(t,2),a=o[0],s=o[1],i=c.useRef(new Map),d=c.useRef(new _g),l=c.useRef();function p(){ft.cancel(l.current)}function f(){p(),l.current=ft(function(){i.current.forEach(function(h,m){if(h&&h.offsetParent){var b=Gr(h),y=b.offsetHeight;d.current.get(m)!==y&&d.current.set(m,b.offsetHeight)}}),s(function(h){return h+1})})}function v(h,m){var b=e(h),y=i.current.get(b);m?(i.current.set(b,m),f()):i.current.delete(b),!y!=!m&&(m?n==null||n(h):r==null||r(h))}return c.useEffect(function(){return p},[]),[v,f,d.current,a]}function xg(e,n,r,t,o,a,s,i){var d=c.useRef();return function(l){if(l==null){i();return}if(ft.cancel(d.current),typeof l=="number")s(l);else if(l&&ht(l)==="object"){var p,f=l.align;"index"in l?p=l.index:p=n.findIndex(function(b){return o(b)===l.key});var v=l.offset,h=v===void 0?0:v,m=function b(y,g){if(!(y<0||!e.current)){var w=e.current.clientHeight,x=!1,C=g;if(w){for(var $=g||f,M=0,D=0,F=0,K=Math.min(n.length,p),O=0;O<=K;O+=1){var z=o(n[O]);D=M;var I=r.get(z);F=D+(I===void 0?t:I),M=F,O===p&&I===void 0&&(x=!0)}var A=null;switch($){case"top":A=D-h;break;case"bottom":A=F-w+h;break;default:{var N=e.current.scrollTop,T=N+w;D<N?C="top":F>T&&(C="bottom")}}A!==null&&A!==e.current.scrollTop&&s(A)}d.current=ft(function(){x&&a(),b(y-1,C)},2)}};m(3)}}}function Eg(e,n,r){var t=e.length,o=n.length,a,s;if(t===0&&o===0)return null;t<o?(a=e,s=n):(a=n,s=e);var i={__EMPTY_ITEM__:!0};function d(m){return m!==void 0?r(m):i}for(var l=null,p=Math.abs(t-o)!==1,f=0;f<s.length;f+=1){var v=d(a[f]),h=d(s[f]);if(v!==h){l=f,p=p||v!==d(s[f+1]);break}}return l===null?null:{index:l,multiple:p}}function $g(e,n,r){var t=c.useState(e),o=He(t,2),a=o[0],s=o[1],i=c.useState(null),d=He(i,2),l=d[0],p=d[1];return c.useEffect(function(){var f=Eg(a||[],e||[],n);(f==null?void 0:f.index)!==void 0&&(r==null||r(f.index),p(e[f.index])),s(e)},[e]),[l]}var Tg=(typeof navigator>"u"?"undefined":ht(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);const _d=function(e,n){var r=c.useRef(!1),t=c.useRef(null);function o(){clearTimeout(t.current),r.current=!0,t.current=setTimeout(function(){r.current=!1},50)}var a=c.useRef({top:e,bottom:n});return a.current.top=e,a.current.bottom=n,function(s){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,d=s<0&&a.current.top||s>0&&a.current.bottom;return i&&d?(clearTimeout(t.current),r.current=!1):(!d||r.current)&&o(),!r.current&&d}};function Mg(e,n,r,t){var o=c.useRef(0),a=c.useRef(null),s=c.useRef(null),i=c.useRef(!1),d=_d(n,r);function l(f){if(e){ft.cancel(a.current);var v=f.deltaY;o.current+=v,s.current=v,!d(v)&&(Tg||f.preventDefault(),a.current=ft(function(){var h=i.current?10:1;t(o.current*h),o.current=0}))}}function p(f){e&&(i.current=f.detail===s.current)}return[l,p]}var Dg=14/15;function Og(e,n,r){var t=c.useRef(!1),o=c.useRef(0),a=c.useRef(null),s=c.useRef(null),i,d=function(v){if(t.current){var h=Math.ceil(v.touches[0].pageY),m=o.current-h;o.current=h,r(m)&&v.preventDefault(),clearInterval(s.current),s.current=setInterval(function(){m*=Dg,(!r(m,!0)||Math.abs(m)<=.1)&&clearInterval(s.current)},16)}},l=function(){t.current=!1,i()},p=function(v){i(),v.touches.length===1&&!t.current&&(t.current=!0,o.current=Math.ceil(v.touches[0].pageY),a.current=v.target,a.current.addEventListener("touchmove",d),a.current.addEventListener("touchend",l))};i=function(){a.current&&(a.current.removeEventListener("touchmove",d),a.current.removeEventListener("touchend",l))},tr(function(){return e&&n.current.addEventListener("touchstart",p),function(){var f;(f=n.current)===null||f===void 0||f.removeEventListener("touchstart",p),i(),clearInterval(s.current)}},[e])}var Rg=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","component","onScroll","onVisibleChange","innerProps"],Ng=[],kg={overflowY:"auto",overflowAnchor:"none"};function Pg(e,n){var r=e.prefixCls,t=r===void 0?"rc-virtual-list":r,o=e.className,a=e.height,s=e.itemHeight,i=e.fullHeight,d=i===void 0?!0:i,l=e.style,p=e.data,f=e.children,v=e.itemKey,h=e.virtual,m=e.direction,b=e.component,y=b===void 0?"div":b,g=e.onScroll,w=e.onVisibleChange,x=e.innerProps,C=Ma(e,Rg),$=!!(h!==!1&&a&&s),M=$&&p&&s*p.length>a,D=c.useState(0),F=He(D,2),K=F[0],O=F[1],z=c.useState(!1),I=He(z,2),A=I[0],N=I[1],T=Qe(t,$e({},"".concat(t,"-rtl"),m==="rtl"),o),k=p||Ng,L=c.useRef(),V=c.useRef(),E=c.useRef(),W=c.useCallback(function(ee){return typeof v=="function"?v(ee):ee==null?void 0:ee[v]},[v]),B={getKey:W};function Q(ee){O(function(ue){var ze;typeof ee=="function"?ze=ee(ue):ze=ee;var St=de(ze);return L.current.scrollTop=St,St})}var J=c.useRef({start:0,end:k.length}),he=c.useRef(),te=$g(k,W),ce=He(te,1),le=ce[0];he.current=le;var we=Sg(W,null,null),ae=He(we,4),De=ae[0],me=ae[1],Le=ae[2],Oe=ae[3],xe=c.useMemo(function(){if(!$)return{scrollHeight:void 0,start:0,end:k.length-1,offset:void 0};if(!M){var ee;return{scrollHeight:((ee=V.current)===null||ee===void 0?void 0:ee.offsetHeight)||0,start:0,end:k.length-1,offset:void 0}}for(var ue=0,ze,St,xt,yn=k.length,Et=0;Et<yn;Et+=1){var Oo=k[Et],Jt=W(Oo),vr=Le.get(Jt),kn=ue+(vr===void 0?s:vr);kn>=K&&ze===void 0&&(ze=Et,St=ue),kn>K+a&&xt===void 0&&(xt=Et),ue=kn}return ze===void 0&&(ze=0,St=0,xt=Math.ceil(a/s)),xt===void 0&&(xt=k.length-1),xt=Math.min(xt+1,k.length),{scrollHeight:ue,start:ze,end:xt,offset:St}},[M,$,K,k,Oe,a]),ge=xe.scrollHeight,Ke=xe.start,Se=xe.end,nt=xe.offset;J.current.start=Ke,J.current.end=Se;var se=ge-a,G=c.useRef(se);G.current=se;function de(ee){var ue=ee;return Number.isNaN(G.current)||(ue=Math.min(ue,G.current)),ue=Math.max(ue,0),ue}var ye=K<=0,Te=K>=se,Ge=_d(ye,Te);function Ue(ee){var ue=ee;Q(ue)}function at(ee){var ue=ee.currentTarget.scrollTop;ue!==K&&Q(ue),g==null||g(ee)}var _t=Mg($,ye,Te,function(ee){Q(function(ue){var ze=ue+ee;return ze})}),vt=He(_t,2),Qt=vt[0],bn=vt[1];Og($,L,function(ee,ue){return Ge(ee,ue)?!1:(Qt({preventDefault:function(){},deltaY:ee}),!0)}),tr(function(){function ee(ue){$&&ue.preventDefault()}return L.current.addEventListener("wheel",Qt),L.current.addEventListener("DOMMouseScroll",bn),L.current.addEventListener("MozMousePixelScroll",ee),function(){L.current&&(L.current.removeEventListener("wheel",Qt),L.current.removeEventListener("DOMMouseScroll",bn),L.current.removeEventListener("MozMousePixelScroll",ee))}},[$]);var hr=xg(L,k,Le,s,W,me,Q,function(){var ee;(ee=E.current)===null||ee===void 0||ee.delayHidden()});c.useImperativeHandle(n,function(){return{scrollTo:hr}}),tr(function(){if(w){var ee=k.slice(Ke,Se+1);w(ee,k)}},[Ke,Se,k]);var gn=wg(k,Ke,Se,De,f,B),kt=null;return a&&(kt=ie($e({},d?"height":"maxHeight",a),kg),$&&(kt.overflowY="hidden",A&&(kt.pointerEvents="none"))),c.createElement("div",fe({style:ie(ie({},l),{},{position:"relative"}),className:T},C),c.createElement(y,{className:"".concat(t,"-holder"),style:kt,ref:L,onScroll:at},c.createElement(gd,{prefixCls:t,height:ge,offset:nt,onInnerResize:me,ref:V,innerProps:x},gn)),$&&c.createElement(yg,{ref:E,prefixCls:t,scrollTop:K,height:a,scrollHeight:ge,count:k.length,direction:m,onScroll:Ue,onStartMove:function(){N(!0)},onStopMove:function(){N(!1)}}))}var Sd=c.forwardRef(Pg);Sd.displayName="List";var Lg=c.createContext({}),Ag=function(e){Eo(r,e);var n=$o(r);function r(){return ur(this,r),n.apply(this,arguments)}return fr(r,[{key:"render",value:function(){return this.props.children}}]),r}(c.Component);function Hr(e){var n=c.useRef(!1),r=c.useState(e),t=He(r,2),o=t[0],a=t[1];c.useEffect(function(){return n.current=!1,function(){n.current=!0}},[]);function s(i,d){d&&n.current||a(i)}return[o,s]}var rn="none",Er="appear",$r="enter",Tr="leave",Ps="none",gt="prepare",Tn="start",Mn="active",hi="end",xd="prepared";function Ls(e,n){var r={};return r[e.toLowerCase()]=n.toLowerCase(),r["Webkit".concat(e)]="webkit".concat(n),r["Moz".concat(e)]="moz".concat(n),r["ms".concat(e)]="MS".concat(n),r["O".concat(e)]="o".concat(n.toLowerCase()),r}function Ig(e,n){var r={animationend:Ls("Animation","AnimationEnd"),transitionend:Ls("Transition","TransitionEnd")};return e&&("AnimationEvent"in n||delete r.animationend.animation,"TransitionEvent"in n||delete r.transitionend.transition),r}var Fg=Ig(xo(),typeof window<"u"?window:{}),Ed={};if(xo()){var Hg=document.createElement("div");Ed=Hg.style}var Mr={};function $d(e){if(Mr[e])return Mr[e];var n=Fg[e];if(n)for(var r=Object.keys(n),t=r.length,o=0;o<t;o+=1){var a=r[o];if(Object.prototype.hasOwnProperty.call(n,a)&&a in Ed)return Mr[e]=n[a],Mr[e]}return""}var Td=$d("animationend"),Md=$d("transitionend"),Dd=!!(Td&&Md),As=Td||"animationend",Is=Md||"transitionend";function Fs(e,n){if(!e)return null;if(ht(e)==="object"){var r=n.replace(/-\w/g,function(t){return t[1].toUpperCase()});return e[r]}return"".concat(e,"-").concat(n)}const Kg=function(e){var n=c.useRef(),r=c.useRef(e);r.current=e;var t=c.useCallback(function(s){r.current(s)},[]);function o(s){s&&(s.removeEventListener(Is,t),s.removeEventListener(As,t))}function a(s){n.current&&n.current!==s&&o(n.current),s&&s!==n.current&&(s.addEventListener(Is,t),s.addEventListener(As,t),n.current=s)}return c.useEffect(function(){return function(){o(n.current)}},[]),[a,o]};var Od=xo()?c.useLayoutEffect:c.useEffect;const Ug=function(){var e=c.useRef(null);function n(){ft.cancel(e.current)}function r(t){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:2;n();var a=ft(function(){o<=1?t({isCanceled:function(){return a!==e.current}}):r(t,o-1)});e.current=a}return c.useEffect(function(){return function(){n()}},[]),[r,n]};var zg=[gt,Tn,Mn,hi],Wg=[gt,xd],Rd=!1,Bg=!0;function Nd(e){return e===Mn||e===hi}const Vg=function(e,n,r){var t=Hr(Ps),o=He(t,2),a=o[0],s=o[1],i=Ug(),d=He(i,2),l=d[0],p=d[1];function f(){s(gt,!0)}var v=n?Wg:zg;return Od(function(){if(a!==Ps&&a!==hi){var h=v.indexOf(a),m=v[h+1],b=r(a);b===Rd?s(m,!0):m&&l(function(y){function g(){y.isCanceled()||s(m,!0)}b===!0?g():Promise.resolve(b).then(g)})}},[e,a]),c.useEffect(function(){return function(){p()}},[]),[f,a]};function jg(e,n,r,t){var o=t.motionEnter,a=o===void 0?!0:o,s=t.motionAppear,i=s===void 0?!0:s,d=t.motionLeave,l=d===void 0?!0:d,p=t.motionDeadline,f=t.motionLeaveImmediately,v=t.onAppearPrepare,h=t.onEnterPrepare,m=t.onLeavePrepare,b=t.onAppearStart,y=t.onEnterStart,g=t.onLeaveStart,w=t.onAppearActive,x=t.onEnterActive,C=t.onLeaveActive,$=t.onAppearEnd,M=t.onEnterEnd,D=t.onLeaveEnd,F=t.onVisibleChanged,K=Hr(),O=He(K,2),z=O[0],I=O[1],A=Hr(rn),N=He(A,2),T=N[0],k=N[1],L=Hr(null),V=He(L,2),E=V[0],W=V[1],B=c.useRef(!1),Q=c.useRef(null);function J(){return r()}var he=c.useRef(!1);function te(){k(rn,!0),W(null,!0)}function ce(se){var G=J();if(!(se&&!se.deadline&&se.target!==G)){var de=he.current,ye;T===Er&&de?ye=$==null?void 0:$(G,se):T===$r&&de?ye=M==null?void 0:M(G,se):T===Tr&&de&&(ye=D==null?void 0:D(G,se)),T!==rn&&de&&ye!==!1&&te()}}var le=Kg(ce),we=He(le,1),ae=we[0],De=function(G){var de,ye,Te;switch(G){case Er:return de={},$e(de,gt,v),$e(de,Tn,b),$e(de,Mn,w),de;case $r:return ye={},$e(ye,gt,h),$e(ye,Tn,y),$e(ye,Mn,x),ye;case Tr:return Te={},$e(Te,gt,m),$e(Te,Tn,g),$e(Te,Mn,C),Te;default:return{}}},me=c.useMemo(function(){return De(T)},[T]),Le=Vg(T,!e,function(se){if(se===gt){var G=me[gt];return G?G(J()):Rd}if(ge in me){var de;W(((de=me[ge])===null||de===void 0?void 0:de.call(me,J(),null))||null)}return ge===Mn&&(ae(J()),p>0&&(clearTimeout(Q.current),Q.current=setTimeout(function(){ce({deadline:!0})},p))),ge===xd&&te(),Bg}),Oe=He(Le,2),xe=Oe[0],ge=Oe[1],Ke=Nd(ge);he.current=Ke,Od(function(){I(n);var se=B.current;B.current=!0;var G;!se&&n&&i&&(G=Er),se&&n&&a&&(G=$r),(se&&!n&&l||!se&&f&&!n&&l)&&(G=Tr);var de=De(G);G&&(e||de[gt])?(k(G),xe()):k(rn)},[n]),c.useEffect(function(){(T===Er&&!i||T===$r&&!a||T===Tr&&!l)&&k(rn)},[i,a,l]),c.useEffect(function(){return function(){B.current=!1,clearTimeout(Q.current)}},[]);var Se=c.useRef(!1);c.useEffect(function(){z&&(Se.current=!0),z!==void 0&&T===rn&&((Se.current||z)&&(F==null||F(z)),Se.current=!0)},[z,T]);var nt=E;return me[gt]&&ge===Tn&&(nt=ie({transition:"none"},nt)),[T,ge,nt,z??n]}function Gg(e){var n=e;ht(e)==="object"&&(n=e.transitionSupport);function r(o,a){return!!(o.motionName&&n&&a!==!1)}var t=c.forwardRef(function(o,a){var s=o.visible,i=s===void 0?!0:s,d=o.removeOnLeave,l=d===void 0?!0:d,p=o.forceRender,f=o.children,v=o.motionName,h=o.leavedClassName,m=o.eventProps,b=c.useContext(Lg),y=b.motion,g=r(o,y),w=c.useRef(),x=c.useRef();function C(){try{return w.current instanceof HTMLElement?w.current:Gr(x.current)}catch{return null}}var $=jg(g,i,C,o),M=He($,4),D=M[0],F=M[1],K=M[2],O=M[3],z=c.useRef(O);O&&(z.current=!0);var I=c.useCallback(function(W){w.current=W,hd(a,W)},[a]),A,N=ie(ie({},m),{},{visible:i});if(!f)A=null;else if(D===rn)O?A=f(ie({},N),I):!l&&z.current&&h?A=f(ie(ie({},N),{},{className:h}),I):p||!l&&!h?A=f(ie(ie({},N),{},{style:{display:"none"}}),I):A=null;else{var T,k;F===gt?k="prepare":Nd(F)?k="active":F===Tn&&(k="start");var L=Fs(v,"".concat(D,"-").concat(k));A=f(ie(ie({},N),{},{className:Qe(Fs(v,D),(T={},$e(T,L,L&&k),$e(T,v,typeof v=="string"),T)),style:K}),I)}if(c.isValidElement(A)&&vd(A)){var V=A,E=V.ref;E||(A=c.cloneElement(A,{ref:I}))}return c.createElement(Ag,{ref:x},A)});return t.displayName="CSSMotion",t}const kd=Gg(Dd);var Na="add",ka="keep",Pa="remove",aa="removed";function Yg(e){var n;return e&&ht(e)==="object"&&"key"in e?n=e:n={key:e},ie(ie({},n),{},{key:String(n.key)})}function La(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(Yg)}function Zg(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=[],t=0,o=n.length,a=La(e),s=La(n);a.forEach(function(l){for(var p=!1,f=t;f<o;f+=1){var v=s[f];if(v.key===l.key){t<f&&(r=r.concat(s.slice(t,f).map(function(h){return ie(ie({},h),{},{status:Na})})),t=f),r.push(ie(ie({},v),{},{status:ka})),t+=1,p=!0;break}}p||r.push(ie(ie({},l),{},{status:Pa}))}),t<o&&(r=r.concat(s.slice(t).map(function(l){return ie(ie({},l),{},{status:Na})})));var i={};r.forEach(function(l){var p=l.key;i[p]=(i[p]||0)+1});var d=Object.keys(i).filter(function(l){return i[l]>1});return d.forEach(function(l){r=r.filter(function(p){var f=p.key,v=p.status;return f!==l||v!==Pa}),r.forEach(function(p){p.key===l&&(p.status=ka)})}),r}var Xg=["component","children","onVisibleChanged","onAllRemoved"],qg=["status"],Qg=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];function Jg(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kd,r=function(t){Eo(a,t);var o=$o(a);function a(){var s;ur(this,a);for(var i=arguments.length,d=new Array(i),l=0;l<i;l++)d[l]=arguments[l];return s=o.call.apply(o,[this].concat(d)),$e(Oa(s),"state",{keyEntities:[]}),$e(Oa(s),"removeKey",function(p){var f=s.state.keyEntities,v=f.map(function(h){return h.key!==p?h:ie(ie({},h),{},{status:aa})});return s.setState({keyEntities:v}),v.filter(function(h){var m=h.status;return m!==aa}).length}),s}return fr(a,[{key:"render",value:function(){var i=this,d=this.state.keyEntities,l=this.props,p=l.component,f=l.children,v=l.onVisibleChanged,h=l.onAllRemoved,m=Ma(l,Xg),b=p||c.Fragment,y={};return Qg.forEach(function(g){y[g]=m[g],delete m[g]}),delete m.keys,c.createElement(b,m,d.map(function(g){var w=g.status,x=Ma(g,qg),C=w===Na||w===ka;return c.createElement(n,fe({},y,{key:x.key,visible:C,eventProps:x,onVisibleChanged:function(M){if(v==null||v(M,{key:x.key}),!M){var D=i.removeKey(x.key);D===0&&h&&h()}}}),f)}))}}],[{key:"getDerivedStateFromProps",value:function(i,d){var l=i.keys,p=d.keyEntities,f=La(l),v=Zg(p,f);return{keyEntities:v.filter(function(h){var m=p.find(function(b){var y=b.key;return h.key===y});return!(m&&m.status===aa&&h.status===Pa)})}}}]),a}(c.Component);return $e(r,"defaultProps",{component:"div"}),r}Jg(Dd);function e3(e,n){var r=c.useState(!1),t=on(r,2),o=t[0],a=t[1];c.useLayoutEffect(function(){if(o)return e(),function(){n()}},[o]),c.useLayoutEffect(function(){return a(!0),function(){a(!1)}},[])}var t3=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],Pd=function(n,r){var t=n.className,o=n.style,a=n.motion,s=n.motionNodes,i=n.motionType,d=n.onMotionStart,l=n.onMotionEnd,p=n.active,f=n.treeNodeRequiredProps,v=po(n,t3),h=c.useState(!0),m=on(h,2),b=m[0],y=m[1],g=c.useContext(ci),w=g.prefixCls,x=s&&i!=="hide";tr(function(){s&&x!==b&&y(x)},[s]);var C=function(){s&&d()},$=c.useRef(!1),M=function(){s&&!$.current&&($.current=!0,l())};e3(C,M);var D=function(K){x===K&&M()};return s?c.createElement(kd,ut({ref:r,visible:b},a,{motionAppear:i==="show",onVisibleChanged:D}),function(F,K){var O=F.className,z=F.style;return c.createElement("div",{ref:K,className:Qe("".concat(w,"-treenode-motion"),O),style:z},s.map(function(I){var A=ut({},(pd(I.data),I.data)),N=I.title,T=I.key,k=I.isStart,L=I.isEnd;delete A.children;var V=Yn(T,f);return c.createElement(er,ut({},A,V,{title:N,active:p,data:I.data,key:T,isStart:k,isEnd:L}))}))}):c.createElement(er,ut({domRef:r,className:t,style:o},v,{active:p}))};Pd.displayName="MotionTreeNode";var n3=c.forwardRef(Pd);function r3(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=e.length,t=n.length;if(Math.abs(r-t)!==1)return{add:!1,key:null};function o(a,s){var i=new Map;a.forEach(function(l){i.set(l,!0)});var d=s.filter(function(l){return!i.has(l)});return d.length===1?d[0]:null}return r<t?{add:!0,key:o(e,n)}:{add:!1,key:o(n,e)}}function Hs(e,n,r){var t=e.findIndex(function(i){return i.key===r}),o=e[t+1],a=n.findIndex(function(i){return i.key===r});if(o){var s=n.findIndex(function(i){return i.key===o.key});return n.slice(a+1,s)}return n.slice(a+1)}var o3=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],Ks={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},a3=function(){},ln="RC_TREE_MOTION_".concat(Math.random()),Aa={key:ln},Ld={key:ln,level:0,index:0,pos:"0",node:Aa,nodes:[Aa]},Us={parent:null,children:[],pos:Ld.pos,data:Aa,title:null,key:ln,isStart:[],isEnd:[]};function zs(e,n,r,t){return n===!1||!r?e:e.slice(0,Math.ceil(r/t)+1)}function Ws(e){var n=e.key,r=e.pos;return dr(n,r)}function i3(e){for(var n=String(e.data.key),r=e;r.parent;)r=r.parent,n="".concat(r.data.key," > ").concat(n);return n}var Ad=c.forwardRef(function(e,n){var r=e.prefixCls,t=e.data;e.selectable,e.checkable;var o=e.expandedKeys,a=e.selectedKeys,s=e.checkedKeys,i=e.loadedKeys,d=e.loadingKeys,l=e.halfCheckedKeys,p=e.keyEntities,f=e.disabled,v=e.dragging,h=e.dragOverNodeKey,m=e.dropPosition,b=e.motion,y=e.height,g=e.itemHeight,w=e.virtual,x=e.focusable,C=e.activeItem,$=e.focused,M=e.tabIndex,D=e.onKeyDown,F=e.onFocus,K=e.onBlur,O=e.onActiveChange,z=e.onListChangeStart,I=e.onListChangeEnd,A=po(e,o3),N=c.useRef(null),T=c.useRef(null);c.useImperativeHandle(n,function(){return{scrollTo:function(de){N.current.scrollTo(de)},getIndentWidth:function(){return T.current.offsetWidth}}});var k=c.useState(o),L=on(k,2),V=L[0],E=L[1],W=c.useState(t),B=on(W,2),Q=B[0],J=B[1],he=c.useState(t),te=on(he,2),ce=te[0],le=te[1],we=c.useState([]),ae=on(we,2),De=ae[0],me=ae[1],Le=c.useState(null),Oe=on(Le,2),xe=Oe[0],ge=Oe[1],Ke=c.useRef(t);Ke.current=t;function Se(){var G=Ke.current;J(G),le(G),me([]),ge(null),I()}tr(function(){E(o);var G=r3(V,o);if(G.key!==null)if(G.add){var de=Q.findIndex(function(_t){var vt=_t.key;return vt===G.key}),ye=zs(Hs(Q,t,G.key),w,y,g),Te=Q.slice();Te.splice(de+1,0,Us),le(Te),me(ye),ge("show")}else{var Ge=t.findIndex(function(_t){var vt=_t.key;return vt===G.key}),Ue=zs(Hs(t,Q,G.key),w,y,g),at=t.slice();at.splice(Ge+1,0,Us),le(at),me(Ue),ge("hide")}else Q!==t&&(J(t),le(t))},[o,t]),c.useEffect(function(){v||Se()},[v]);var nt=b?ce:t,se={expandedKeys:o,selectedKeys:a,loadedKeys:i,loadingKeys:d,checkedKeys:s,halfCheckedKeys:l,dragOverNodeKey:h,dropPosition:m,keyEntities:p};return c.createElement(c.Fragment,null,$&&C&&c.createElement("span",{style:Ks,"aria-live":"assertive"},i3(C)),c.createElement("div",null,c.createElement("input",{style:Ks,disabled:x===!1||f,tabIndex:x!==!1?M:null,onKeyDown:D,onFocus:F,onBlur:K,value:"",onChange:a3,"aria-label":"for screen reader"})),c.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},c.createElement("div",{className:"".concat(r,"-indent")},c.createElement("div",{ref:T,className:"".concat(r,"-indent-unit")}))),c.createElement(Sd,ut({},A,{data:nt,itemKey:Ws,height:y,fullHeight:!1,virtual:w,itemHeight:g,prefixCls:"".concat(r,"-list"),ref:N,onVisibleChange:function(de,ye){var Te=new Set(de),Ge=ye.filter(function(Ue){return!Te.has(Ue)});Ge.some(function(Ue){return Ws(Ue)===ln})&&Se()}}),function(G){var de=G.pos,ye=ut({},(pd(G.data),G.data)),Te=G.title,Ge=G.key,Ue=G.isStart,at=G.isEnd,_t=dr(Ge,de);delete ye.key,delete ye.children;var vt=Yn(_t,se);return c.createElement(n3,ut({},ye,vt,{title:Te,active:!!C&&Ge===C.key,pos:de,data:G.data,isStart:Ue,isEnd:at,motion:b,motionNodes:Ge===ln?De:null,motionType:xe,onMotionStart:z,onMotionEnd:Se,treeNodeRequiredProps:se,onMouseMove:function(){O(null)}}))}))});Ad.displayName="NodeList";function Id(e,n){var r=new Set;return e.forEach(function(t){n.has(t)||r.add(t)}),r}function s3(e){var n=e||{},r=n.disabled,t=n.disableCheckbox,o=n.checkable;return!!(r||t)||o===!1}function c3(e,n,r,t){for(var o=new Set(e),a=new Set,s=0;s<=r;s+=1){var i=n.get(s)||new Set;i.forEach(function(f){var v=f.key,h=f.node,m=f.children,b=m===void 0?[]:m;o.has(v)&&!t(h)&&b.filter(function(y){return!t(y.node)}).forEach(function(y){o.add(y.key)})})}for(var d=new Set,l=r;l>=0;l-=1){var p=n.get(l)||new Set;p.forEach(function(f){var v=f.parent,h=f.node;if(!(t(h)||!f.parent||d.has(f.parent.key))){if(t(f.parent.node)){d.add(v.key);return}var m=!0,b=!1;(v.children||[]).filter(function(y){return!t(y.node)}).forEach(function(y){var g=y.key,w=o.has(g);m&&!w&&(m=!1),!b&&(w||a.has(g))&&(b=!0)}),m&&o.add(v.key),b&&a.add(v.key),d.add(v.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(Id(a,o))}}function l3(e,n,r,t,o){for(var a=new Set(e),s=new Set(n),i=0;i<=t;i+=1){var d=r.get(i)||new Set;d.forEach(function(v){var h=v.key,m=v.node,b=v.children,y=b===void 0?[]:b;!a.has(h)&&!s.has(h)&&!o(m)&&y.filter(function(g){return!o(g.node)}).forEach(function(g){a.delete(g.key)})})}s=new Set;for(var l=new Set,p=t;p>=0;p-=1){var f=r.get(p)||new Set;f.forEach(function(v){var h=v.parent,m=v.node;if(!(o(m)||!v.parent||l.has(v.parent.key))){if(o(v.parent.node)){l.add(h.key);return}var b=!0,y=!1;(h.children||[]).filter(function(g){return!o(g.node)}).forEach(function(g){var w=g.key,x=a.has(w);b&&!x&&(b=!1),!y&&(x||s.has(w))&&(y=!0)}),b||a.delete(h.key),y&&s.add(h.key),l.add(h.key)}})}return{checkedKeys:Array.from(a),halfCheckedKeys:Array.from(Id(s,a))}}function ia(e,n,r,t){var o=[],a;t?a=t:a=s3;var s=new Set(e.filter(function(p){var f=!!r[p];return f||o.push(p),f})),i=new Map,d=0;Object.keys(r).forEach(function(p){var f=r[p],v=f.level,h=i.get(v);h||(h=new Set,i.set(v,h)),h.add(f),d=Math.max(d,v)}),yt(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(p){return"'".concat(p,"'")}).join(", ")));var l;return n===!0?l=c3(s,i,d,a):l=l3(s,n.halfCheckedKeys,i,d,a),l}function d3(e){var n=e.dropPosition,r=e.dropLevelOffset,t=e.indent,o={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(n){case-1:o.top=0,o.left=-r*t;break;case 1:o.bottom=0,o.left=-r*t;break;case 0:o.bottom=0,o.left=t;break}return c.createElement("div",{style:o})}var u3=10,vi=function(e){rd(r,e);var n=od(r);function r(){var t;td(this,r);for(var o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];return t=n.call.apply(n,[this].concat(a)),t.destroyed=!1,t.delayedDragEnterLogic=void 0,t.loadingRetryTimes={},t.state={keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:jr()},t.dragStartMousePosition=null,t.dragNode=void 0,t.currentMouseOverDroppableNodeKey=null,t.listRef=c.createRef(),t.onNodeDragStart=function(i,d){var l=t.state,p=l.expandedKeys,f=l.keyEntities,v=t.props.onDragStart,h=d.props.eventKey;t.dragNode=d,t.dragStartMousePosition={x:i.clientX,y:i.clientY};var m=Lt(p,h);t.setState({draggingNodeKey:h,dragChildrenKeys:Yb(h,f),indent:t.listRef.current.getIndentWidth()}),t.setExpandedKeys(m),window.addEventListener("dragend",t.onWindowDragEnd),v==null||v({event:i,node:Ee(d.props)})},t.onNodeDragEnter=function(i,d){var l=t.state,p=l.expandedKeys,f=l.keyEntities,v=l.dragChildrenKeys,h=l.flattenNodes,m=l.indent,b=t.props,y=b.onDragEnter,g=b.onExpand,w=b.allowDrop,x=b.direction,C=d.props,$=C.pos,M=C.eventKey,D=At(t),F=D.dragNode;if(t.currentMouseOverDroppableNodeKey!==M&&(t.currentMouseOverDroppableNodeKey=M),!F){t.resetDragState();return}var K=$s(i,F,d,m,t.dragStartMousePosition,w,h,f,p,x),O=K.dropPosition,z=K.dropLevelOffset,I=K.dropTargetKey,A=K.dropContainerKey,N=K.dropTargetPos,T=K.dropAllowed,k=K.dragOverNodeKey;if(v.indexOf(I)!==-1||!T){t.resetDragState();return}if(t.delayedDragEnterLogic||(t.delayedDragEnterLogic={}),Object.keys(t.delayedDragEnterLogic).forEach(function(L){clearTimeout(t.delayedDragEnterLogic[L])}),F.props.eventKey!==d.props.eventKey&&(i.persist(),t.delayedDragEnterLogic[$]=window.setTimeout(function(){if(t.state.draggingNodeKey!==null){var L=sn(p),V=f[d.props.eventKey];V&&(V.children||[]).length&&(L=Vt(p,d.props.eventKey)),"expandedKeys"in t.props||t.setExpandedKeys(L),g==null||g(L,{node:Ee(d.props),expanded:!0,nativeEvent:i.nativeEvent})}},800)),F.props.eventKey===I&&z===0){t.resetDragState();return}t.setState({dragOverNodeKey:k,dropPosition:O,dropLevelOffset:z,dropTargetKey:I,dropContainerKey:A,dropTargetPos:N,dropAllowed:T}),y==null||y({event:i,node:Ee(d.props),expandedKeys:p})},t.onNodeDragOver=function(i,d){var l=t.state,p=l.dragChildrenKeys,f=l.flattenNodes,v=l.keyEntities,h=l.expandedKeys,m=l.indent,b=t.props,y=b.onDragOver,g=b.allowDrop,w=b.direction,x=At(t),C=x.dragNode;if(C){var $=$s(i,C,d,m,t.dragStartMousePosition,g,f,v,h,w),M=$.dropPosition,D=$.dropLevelOffset,F=$.dropTargetKey,K=$.dropContainerKey,O=$.dropAllowed,z=$.dropTargetPos,I=$.dragOverNodeKey;p.indexOf(F)!==-1||!O||(C.props.eventKey===F&&D===0?t.state.dropPosition===null&&t.state.dropLevelOffset===null&&t.state.dropTargetKey===null&&t.state.dropContainerKey===null&&t.state.dropTargetPos===null&&t.state.dropAllowed===!1&&t.state.dragOverNodeKey===null||t.resetDragState():M===t.state.dropPosition&&D===t.state.dropLevelOffset&&F===t.state.dropTargetKey&&K===t.state.dropContainerKey&&z===t.state.dropTargetPos&&O===t.state.dropAllowed&&I===t.state.dragOverNodeKey||t.setState({dropPosition:M,dropLevelOffset:D,dropTargetKey:F,dropContainerKey:K,dropTargetPos:z,dropAllowed:O,dragOverNodeKey:I}),y==null||y({event:i,node:Ee(d.props)}))}},t.onNodeDragLeave=function(i,d){t.currentMouseOverDroppableNodeKey===d.props.eventKey&&!i.currentTarget.contains(i.relatedTarget)&&(t.resetDragState(),t.currentMouseOverDroppableNodeKey=null);var l=t.props.onDragLeave;l==null||l({event:i,node:Ee(d.props)})},t.onWindowDragEnd=function(i){t.onNodeDragEnd(i,null,!0),window.removeEventListener("dragend",t.onWindowDragEnd)},t.onNodeDragEnd=function(i,d){var l=t.props.onDragEnd;t.setState({dragOverNodeKey:null}),t.cleanDragState(),l==null||l({event:i,node:Ee(d.props)}),t.dragNode=null,window.removeEventListener("dragend",t.onWindowDragEnd)},t.onNodeDrop=function(i,d){var l,p=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,f=t.state,v=f.dragChildrenKeys,h=f.dropPosition,m=f.dropTargetKey,b=f.dropTargetPos,y=f.dropAllowed;if(y){var g=t.props.onDrop;if(t.setState({dragOverNodeKey:null}),t.cleanDragState(),m!==null){var w=Fe(Fe({},Yn(m,t.getTreeNodeRequiredProps())),{},{active:((l=t.getActiveItem())===null||l===void 0?void 0:l.key)===m,data:t.state.keyEntities[m].node}),x=v.indexOf(m)!==-1;yt(!x,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var C=fi(b),$={event:i,node:Ee(w),dragNode:t.dragNode?Ee(t.dragNode.props):null,dragNodesKeys:[t.dragNode.props.eventKey].concat(v),dropToGap:h!==0,dropPosition:h+Number(C[C.length-1])};p||g==null||g($),t.dragNode=null}}},t.cleanDragState=function(){var i=t.state.draggingNodeKey;i!==null&&t.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),t.dragStartMousePosition=null,t.currentMouseOverDroppableNodeKey=null},t.triggerExpandActionExpand=function(i,d){var l=t.state,p=l.expandedKeys,f=l.flattenNodes,v=d.expanded,h=d.key,m=d.isLeaf;if(!(m||i.shiftKey||i.metaKey||i.ctrlKey)){var b=f.filter(function(g){return g.key===h})[0],y=Ee(Fe(Fe({},Yn(h,t.getTreeNodeRequiredProps())),{},{data:b.data}));t.setExpandedKeys(v?Lt(p,h):Vt(p,h)),t.onNodeExpand(i,y)}},t.onNodeClick=function(i,d){var l=t.props,p=l.onClick,f=l.expandAction;f==="click"&&t.triggerExpandActionExpand(i,d),p==null||p(i,d)},t.onNodeDoubleClick=function(i,d){var l=t.props,p=l.onDoubleClick,f=l.expandAction;f==="doubleClick"&&t.triggerExpandActionExpand(i,d),p==null||p(i,d)},t.onNodeSelect=function(i,d){var l=t.state.selectedKeys,p=t.state,f=p.keyEntities,v=p.fieldNames,h=t.props,m=h.onSelect,b=h.multiple,y=d.selected,g=d[v.key],w=!y;w?b?l=Vt(l,g):l=[g]:l=Lt(l,g);var x=l.map(function(C){var $=f[C];return $?$.node:null}).filter(function(C){return C});t.setUncontrolledState({selectedKeys:l}),m==null||m(l,{event:"select",selected:w,node:d,selectedNodes:x,nativeEvent:i.nativeEvent})},t.onNodeCheck=function(i,d,l){var p=t.state,f=p.keyEntities,v=p.checkedKeys,h=p.halfCheckedKeys,m=t.props,b=m.checkStrictly,y=m.onCheck,g=d.key,w,x={event:"check",node:d,checked:l,nativeEvent:i.nativeEvent};if(b){var C=l?Vt(v,g):Lt(v,g),$=Lt(h,g);w={checked:C,halfChecked:$},x.checkedNodes=C.map(function(z){return f[z]}).filter(function(z){return z}).map(function(z){return z.node}),t.setUncontrolledState({checkedKeys:C})}else{var M=ia([].concat(sn(v),[g]),!0,f),D=M.checkedKeys,F=M.halfCheckedKeys;if(!l){var K=new Set(D);K.delete(g);var O=ia(Array.from(K),{checked:!1,halfCheckedKeys:F},f);D=O.checkedKeys,F=O.halfCheckedKeys}w=D,x.checkedNodes=[],x.checkedNodesPositions=[],x.halfCheckedKeys=F,D.forEach(function(z){var I=f[z];if(I){var A=I.node,N=I.pos;x.checkedNodes.push(A),x.checkedNodesPositions.push({node:A,pos:N})}}),t.setUncontrolledState({checkedKeys:D},!1,{halfCheckedKeys:F})}y==null||y(w,x)},t.onNodeLoad=function(i){var d=i.key,l=new Promise(function(p,f){t.setState(function(v){var h=v.loadedKeys,m=h===void 0?[]:h,b=v.loadingKeys,y=b===void 0?[]:b,g=t.props,w=g.loadData,x=g.onLoad;if(!w||m.indexOf(d)!==-1||y.indexOf(d)!==-1)return null;var C=w(i);return C.then(function(){var $=t.state.loadedKeys,M=Vt($,d);x==null||x(M,{event:"load",node:i}),t.setUncontrolledState({loadedKeys:M}),t.setState(function(D){return{loadingKeys:Lt(D.loadingKeys,d)}}),p()}).catch(function($){if(t.setState(function(D){return{loadingKeys:Lt(D.loadingKeys,d)}}),t.loadingRetryTimes[d]=(t.loadingRetryTimes[d]||0)+1,t.loadingRetryTimes[d]>=u3){var M=t.state.loadedKeys;yt(!1,"Retry for `loadData` many times but still failed. No more retry."),t.setUncontrolledState({loadedKeys:Vt(M,d)}),p()}f($)}),{loadingKeys:Vt(y,d)}})});return l.catch(function(){}),l},t.onNodeMouseEnter=function(i,d){var l=t.props.onMouseEnter;l==null||l({event:i,node:d})},t.onNodeMouseLeave=function(i,d){var l=t.props.onMouseLeave;l==null||l({event:i,node:d})},t.onNodeContextMenu=function(i,d){var l=t.props.onRightClick;l&&(i.preventDefault(),l({event:i,node:d}))},t.onFocus=function(){var i=t.props.onFocus;t.setState({focused:!0});for(var d=arguments.length,l=new Array(d),p=0;p<d;p++)l[p]=arguments[p];i==null||i.apply(void 0,l)},t.onBlur=function(){var i=t.props.onBlur;t.setState({focused:!1}),t.onActiveChange(null);for(var d=arguments.length,l=new Array(d),p=0;p<d;p++)l[p]=arguments[p];i==null||i.apply(void 0,l)},t.getTreeNodeRequiredProps=function(){var i=t.state,d=i.expandedKeys,l=i.selectedKeys,p=i.loadedKeys,f=i.loadingKeys,v=i.checkedKeys,h=i.halfCheckedKeys,m=i.dragOverNodeKey,b=i.dropPosition,y=i.keyEntities;return{expandedKeys:d||[],selectedKeys:l||[],loadedKeys:p||[],loadingKeys:f||[],checkedKeys:v||[],halfCheckedKeys:h||[],dragOverNodeKey:m,dropPosition:b,keyEntities:y}},t.setExpandedKeys=function(i){var d=t.state,l=d.treeData,p=d.fieldNames,f=ra(l,i,p);t.setUncontrolledState({expandedKeys:i,flattenNodes:f},!0)},t.onNodeExpand=function(i,d){var l=t.state.expandedKeys,p=t.state,f=p.listChanging,v=p.fieldNames,h=t.props,m=h.onExpand,b=h.loadData,y=d.expanded,g=d[v.key];if(!f){var w=l.indexOf(g),x=!y;if(yt(y&&w!==-1||!y&&w===-1,"Expand state not sync with index check"),x?l=Vt(l,g):l=Lt(l,g),t.setExpandedKeys(l),m==null||m(l,{node:d,expanded:x,nativeEvent:i.nativeEvent}),x&&b){var C=t.onNodeLoad(d);C&&C.then(function(){var $=ra(t.state.treeData,l,v);t.setUncontrolledState({flattenNodes:$})}).catch(function(){var $=t.state.expandedKeys,M=Lt($,g);t.setExpandedKeys(M)})}}},t.onListChangeStart=function(){t.setUncontrolledState({listChanging:!0})},t.onListChangeEnd=function(){setTimeout(function(){t.setUncontrolledState({listChanging:!1})})},t.onActiveChange=function(i){var d=t.state.activeKey,l=t.props.onActiveChange;d!==i&&(t.setState({activeKey:i}),i!==null&&t.scrollTo({key:i}),l==null||l(i))},t.getActiveItem=function(){var i=t.state,d=i.activeKey,l=i.flattenNodes;return d===null?null:l.find(function(p){var f=p.key;return f===d})||null},t.offsetActiveKey=function(i){var d=t.state,l=d.flattenNodes,p=d.activeKey,f=l.findIndex(function(m){var b=m.key;return b===p});f===-1&&i<0&&(f=l.length),f=(f+i+l.length)%l.length;var v=l[f];if(v){var h=v.key;t.onActiveChange(h)}else t.onActiveChange(null)},t.onKeyDown=function(i){var d=t.state,l=d.activeKey,p=d.expandedKeys,f=d.checkedKeys,v=d.fieldNames,h=t.props,m=h.onKeyDown,b=h.checkable,y=h.selectable;switch(i.which){case Z.UP:{t.offsetActiveKey(-1),i.preventDefault();break}case Z.DOWN:{t.offsetActiveKey(1),i.preventDefault();break}}var g=t.getActiveItem();if(g&&g.data){var w=t.getTreeNodeRequiredProps(),x=g.data.isLeaf===!1||!!(g.data[v.children]||[]).length,C=Ee(Fe(Fe({},Yn(l,w)),{},{data:g.data,active:!0}));switch(i.which){case Z.LEFT:{x&&p.includes(l)?t.onNodeExpand({},C):g.parent&&t.onActiveChange(g.parent.key),i.preventDefault();break}case Z.RIGHT:{x&&!p.includes(l)?t.onNodeExpand({},C):g.children&&g.children.length&&t.onActiveChange(g.children[0].key),i.preventDefault();break}case Z.ENTER:case Z.SPACE:{b&&!C.disabled&&C.checkable!==!1&&!C.disableCheckbox?t.onNodeCheck({},C,!f.includes(l)):!b&&y&&!C.disabled&&C.selectable!==!1&&t.onNodeSelect({},C);break}}}m==null||m(i)},t.setUncontrolledState=function(i){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!t.destroyed){var p=!1,f=!0,v={};Object.keys(i).forEach(function(h){if(h in t.props){f=!1;return}p=!0,v[h]=i[h]}),p&&(!d||f)&&t.setState(Fe(Fe({},v),l))}},t.scrollTo=function(i){t.listRef.current.scrollTo(i)},t}return nd(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props.activeKey;o!==void 0&&o!==this.state.activeKey&&(this.setState({activeKey:o}),o!==null&&this.scrollTo({key:o}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o,a=this.state,s=a.focused,i=a.flattenNodes,d=a.keyEntities,l=a.draggingNodeKey,p=a.activeKey,f=a.dropLevelOffset,v=a.dropContainerKey,h=a.dropTargetKey,m=a.dropPosition,b=a.dragOverNodeKey,y=a.indent,g=this.props,w=g.prefixCls,x=g.className,C=g.style,$=g.showLine,M=g.focusable,D=g.tabIndex,F=D===void 0?0:D,K=g.selectable,O=g.showIcon,z=g.icon,I=g.switcherIcon,A=g.draggable,N=g.checkable,T=g.checkStrictly,k=g.disabled,L=g.motion,V=g.loadData,E=g.filterTreeNode,W=g.height,B=g.itemHeight,Q=g.virtual,J=g.titleRender,he=g.dropIndicatorRender,te=g.onContextMenu,ce=g.onScroll,le=g.direction,we=g.rootClassName,ae=g.rootStyle,De=sd(this.props,{aria:!0,data:!0}),me;return A&&(Ht(A)==="object"?me=A:typeof A=="function"?me={nodeDraggable:A}:me={}),c.createElement(ci.Provider,{value:{prefixCls:w,selectable:K,showIcon:O,icon:z,switcherIcon:I,draggable:me,draggingNodeKey:l,checkable:N,checkStrictly:T,disabled:k,keyEntities:d,dropLevelOffset:f,dropContainerKey:v,dropTargetKey:h,dropPosition:m,dragOverNodeKey:b,indent:y,direction:le,dropIndicatorRender:he,loadData:V,filterTreeNode:E,titleRender:J,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop}},c.createElement("div",{role:"tree",className:Qe(w,x,we,(o={},Ce(o,"".concat(w,"-show-line"),$),Ce(o,"".concat(w,"-focused"),s),Ce(o,"".concat(w,"-active-focused"),p!==null),o)),style:ae},c.createElement(Ad,ut({ref:this.listRef,prefixCls:w,style:C,data:i,disabled:k,selectable:K,checkable:!!N,motion:L,dragging:l!==null,height:W,itemHeight:B,virtual:Q,focusable:M,focused:s,tabIndex:F,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:te,onScroll:ce},this.getTreeNodeRequiredProps(),De))))}}],[{key:"getDerivedStateFromProps",value:function(o,a){var s=a.prevProps,i={prevProps:o};function d(M){return!s&&M in o||s&&s[M]!==o[M]}var l,p=a.fieldNames;if(d("fieldNames")&&(p=jr(o.fieldNames),i.fieldNames=p),d("treeData")?l=o.treeData:d("children")&&(yt(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),l=zb(o.children)),l){i.treeData=l;var f=Bb(l,{fieldNames:p});i.keyEntities=Fe(Ce({},ln,Ld),f.keyEntities)}var v=i.keyEntities||a.keyEntities;if(d("expandedKeys")||s&&d("autoExpandParent"))i.expandedKeys=o.autoExpandParent||!s&&o.defaultExpandParent?Ms(o.expandedKeys,v):o.expandedKeys;else if(!s&&o.defaultExpandAll){var h=Fe({},v);delete h[ln],i.expandedKeys=Object.keys(h).map(function(M){return h[M].key})}else!s&&o.defaultExpandedKeys&&(i.expandedKeys=o.autoExpandParent||o.defaultExpandParent?Ms(o.defaultExpandedKeys,v):o.defaultExpandedKeys);if(i.expandedKeys||delete i.expandedKeys,l||i.expandedKeys){var m=ra(l||a.treeData,i.expandedKeys||a.expandedKeys,p);i.flattenNodes=m}if(o.selectable&&(d("selectedKeys")?i.selectedKeys=Ts(o.selectedKeys,o):!s&&o.defaultSelectedKeys&&(i.selectedKeys=Ts(o.defaultSelectedKeys,o))),o.checkable){var b;if(d("checkedKeys")?b=oa(o.checkedKeys)||{}:!s&&o.defaultCheckedKeys?b=oa(o.defaultCheckedKeys)||{}:l&&(b=oa(o.checkedKeys)||{checkedKeys:a.checkedKeys,halfCheckedKeys:a.halfCheckedKeys}),b){var y=b,g=y.checkedKeys,w=g===void 0?[]:g,x=y.halfCheckedKeys,C=x===void 0?[]:x;if(!o.checkStrictly){var $=ia(w,!0,v);w=$.checkedKeys,C=$.halfCheckedKeys}i.checkedKeys=w,i.halfCheckedKeys=C}}return d("loadedKeys")&&(i.loadedKeys=o.loadedKeys),i}}]),r}(c.Component);vi.defaultProps={prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:d3,allowDrop:function(){return!0},expandAction:!1};vi.TreeNode=er;var Fd={},Hd={},To={exports:{}},jn={exports:{}};(function(){var e,n,r,t,o,a;typeof performance<"u"&&performance!==null&&performance.now?jn.exports=function(){return performance.now()}:typeof process<"u"&&process!==null&&process.hrtime?(jn.exports=function(){return(e()-o)/1e6},n=process.hrtime,e=function(){var s;return s=n(),s[0]*1e9+s[1]},t=e(),a=process.uptime()*1e9,o=t-a):Date.now?(jn.exports=function(){return Date.now()-r},r=Date.now()):(jn.exports=function(){return new Date().getTime()-r},r=new Date().getTime())}).call(En);var f3=jn.exports,p3=f3,Ft=typeof window>"u"?En:window,Dr=["moz","webkit"],Dn="AnimationFrame",Rn=Ft["request"+Dn],nr=Ft["cancel"+Dn]||Ft["cancelRequest"+Dn];for(var Wn=0;!Rn&&Wn<Dr.length;Wn++)Rn=Ft[Dr[Wn]+"Request"+Dn],nr=Ft[Dr[Wn]+"Cancel"+Dn]||Ft[Dr[Wn]+"CancelRequest"+Dn];if(!Rn||!nr){var sa=0,Bs=0,en=[],h3=1e3/60;Rn=function(e){if(en.length===0){var n=p3(),r=Math.max(0,h3-(n-sa));sa=r+n,setTimeout(function(){var t=en.slice(0);en.length=0;for(var o=0;o<t.length;o++)if(!t[o].cancelled)try{t[o].callback(sa)}catch(a){setTimeout(function(){throw a},0)}},Math.round(r))}return en.push({handle:++Bs,callback:e,cancelled:!1}),Bs},nr=function(e){for(var n=0;n<en.length;n++)en[n].handle===e&&(en[n].cancelled=!0)}}To.exports=function(e){return Rn.call(Ft,e)};To.exports.cancel=function(){nr.apply(Ft,arguments)};To.exports.polyfill=function(e){e||(e=Ft),e.requestAnimationFrame=Rn,e.cancelAnimationFrame=nr};var v3=To.exports,Mo={exports:{}},ca=null,Vs=["Webkit","Moz","O","ms"],m3=function(n){ca||(ca=document.createElement("div"));var r=ca.style;if(n in r)return n;for(var t=n.charAt(0).toUpperCase()+n.slice(1),o=Vs.length;o>=0;o--){var a=Vs[o]+t;if(a in r)return a}return!1},b3=w3,g3=/\s/,y3=/(_|-|\.|:)/,C3=/([a-z][A-Z]|[A-Z][a-z])/;function w3(e){return g3.test(e)?e.toLowerCase():y3.test(e)?(S3(e)||e).toLowerCase():C3.test(e)?E3(e).toLowerCase():e.toLowerCase()}var _3=/[\W_]+(.|$)/g;function S3(e){return e.replace(_3,function(n,r){return r?" "+r:""})}var x3=/(.)([A-Z]+)/g;function E3(e){return e.replace(x3,function(n,r,t){return r+" "+t.toLowerCase().split("").join(" ")})}var $3=b3,T3=M3;function M3(e){return $3(e).replace(/[\W_]+(.|$)/g,function(n,r){return r?" "+r:""}).trim()}var D3=T3,O3=R3;function R3(e){return D3(e).replace(/\s(\w)/g,function(n,r){return r.toUpperCase()})}var N3={animationIterationCount:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridColumn:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,stopOpacity:!0,strokeDashoffset:!0,strokeOpacity:!0,strokeWidth:!0},k3=function(e,n){return typeof n=="number"&&!N3[e]?n+"px":n},P3=m3,L3=O3,Kr={float:"cssFloat"},A3=k3;function Zr(e,n,r){var t=Kr[n];if(typeof t>"u"&&(t=F3(n)),t){if(r===void 0)return e.style[t];e.style[t]=A3(t,r)}}function I3(e,n){for(var r in n)n.hasOwnProperty(r)&&Zr(e,r,n[r])}function F3(e){var n=L3(e),r=P3(n);return Kr[n]=Kr[e]=Kr[r]=r,r}function Kd(){arguments.length===2?typeof arguments[1]=="string"?arguments[0].style.cssText=arguments[1]:I3(arguments[0],arguments[1]):Zr(arguments[0],arguments[1],arguments[2])}Mo.exports=Kd;Mo.exports.set=Kd;Mo.exports.get=function(e,n){return Array.isArray(n)?n.reduce(function(r,t){return r[t]=Zr(e,t||""),r},{}):Zr(e,n||"")};var Ud=Mo.exports,zd={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=n;function n(r){return typeof r=="string"}})(zd);var Wd={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=a;var n=Ud,r=t(n);function t(s){return s&&s.__esModule?s:{default:s}}var o=!1;function a(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;if(s&&o!==!1)return o;if(typeof document<"u"){var i=document.createElement("div");(0,r.default)(i,{width:100,height:100,position:"absolute",top:-9999,overflow:"scroll",MsOverflowStyle:"scrollbar"}),document.body.appendChild(i),o=i.offsetWidth-i.clientWidth,document.body.removeChild(i)}else o=0;return o||0}})(Wd);var Bd={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=n;function n(){return!1}})(Bd);var Vd={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=n;function n(r){var t=r.clientWidth,o=getComputedStyle(r),a=o.paddingLeft,s=o.paddingRight;return t-parseFloat(a)-parseFloat(s)}})(Vd);var jd={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=n;function n(r){var t=r.clientHeight,o=getComputedStyle(r),a=o.paddingTop,s=o.paddingBottom;return t-parseFloat(a)-parseFloat(s)}})(jd);var lt={};Object.defineProperty(lt,"__esModule",{value:!0});lt.containerStyleDefault={position:"relative",overflow:"hidden",width:"100%",height:"100%"};lt.containerStyleAutoHeight={height:"auto"};lt.viewStyleDefault={position:"absolute",top:0,left:0,right:0,bottom:0,overflow:"scroll",WebkitOverflowScrolling:"touch"};lt.viewStyleAutoHeight={position:"relative",top:void 0,left:void 0,right:void 0,bottom:void 0};lt.viewStyleUniversalInitial={overflow:"hidden",marginRight:0,marginBottom:0};lt.trackHorizontalStyleDefault={position:"absolute",height:6};lt.trackVerticalStyleDefault={position:"absolute",width:6};lt.thumbHorizontalStyleDefault={position:"relative",display:"block",height:"100%"};lt.thumbVerticalStyleDefault={position:"relative",display:"block",width:"100%"};lt.disableSelectStyle={userSelect:"none"};lt.disableSelectStyleReset={userSelect:""};var mn={};Object.defineProperty(mn,"__esModule",{value:!0});var Xt=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e};mn.renderViewDefault=U3;mn.renderTrackHorizontalDefault=z3;mn.renderTrackVerticalDefault=W3;mn.renderThumbHorizontalDefault=B3;mn.renderThumbVerticalDefault=V3;var H3=c,pr=K3(H3);function K3(e){return e&&e.__esModule?e:{default:e}}function Do(e,n){var r={};for(var t in e)n.indexOf(t)>=0||Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t]);return r}function U3(e){return pr.default.createElement("div",e)}function z3(e){var n=e.style,r=Do(e,["style"]),t=Xt({},n,{right:2,bottom:2,left:2,borderRadius:3});return pr.default.createElement("div",Xt({style:t},r))}function W3(e){var n=e.style,r=Do(e,["style"]),t=Xt({},n,{right:2,bottom:2,top:2,borderRadius:3});return pr.default.createElement("div",Xt({style:t},r))}function B3(e){var n=e.style,r=Do(e,["style"]),t=Xt({},n,{cursor:"pointer",borderRadius:"inherit",backgroundColor:"rgba(0,0,0,.2)"});return pr.default.createElement("div",Xt({style:t},r))}function V3(e){var n=e.style,r=Do(e,["style"]),t=Xt({},n,{cursor:"pointer",borderRadius:"inherit",backgroundColor:"rgba(0,0,0,.2)"});return pr.default.createElement("div",Xt({style:t},r))}(function(e){Object.defineProperty(e,"__esModule",{value:!0});var n=Object.assign||function(I){for(var A=1;A<arguments.length;A++){var N=arguments[A];for(var T in N)Object.prototype.hasOwnProperty.call(N,T)&&(I[T]=N[T])}return I},r=function(){function I(A,N){for(var T=0;T<N.length;T++){var k=N[T];k.enumerable=k.enumerable||!1,k.configurable=!0,"value"in k&&(k.writable=!0),Object.defineProperty(A,k.key,k)}}return function(A,N,T){return N&&I(A.prototype,N),T&&I(A,T),A}}(),t=v3,o=M(t),a=Ud,s=M(a),i=c,d=ir,l=M(d),p=zd,f=M(p),v=Wd,h=M(v),m=Bd,b=M(m),y=Vd,g=M(y),w=jd,x=M(w),C=lt,$=mn;function M(I){return I&&I.__esModule?I:{default:I}}function D(I,A){var N={};for(var T in I)A.indexOf(T)>=0||Object.prototype.hasOwnProperty.call(I,T)&&(N[T]=I[T]);return N}function F(I,A){if(!(I instanceof A))throw new TypeError("Cannot call a class as a function")}function K(I,A){if(!I)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A&&(typeof A=="object"||typeof A=="function")?A:I}function O(I,A){if(typeof A!="function"&&A!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof A);I.prototype=Object.create(A&&A.prototype,{constructor:{value:I,enumerable:!1,writable:!0,configurable:!0}}),A&&(Object.setPrototypeOf?Object.setPrototypeOf(I,A):I.__proto__=A)}var z=function(I){O(A,I);function A(N){var T;F(this,A);for(var k=arguments.length,L=Array(k>1?k-1:0),V=1;V<k;V++)L[V-1]=arguments[V];var E=K(this,(T=A.__proto__||Object.getPrototypeOf(A)).call.apply(T,[this,N].concat(L)));return E.getScrollLeft=E.getScrollLeft.bind(E),E.getScrollTop=E.getScrollTop.bind(E),E.getScrollWidth=E.getScrollWidth.bind(E),E.getScrollHeight=E.getScrollHeight.bind(E),E.getClientWidth=E.getClientWidth.bind(E),E.getClientHeight=E.getClientHeight.bind(E),E.getValues=E.getValues.bind(E),E.getThumbHorizontalWidth=E.getThumbHorizontalWidth.bind(E),E.getThumbVerticalHeight=E.getThumbVerticalHeight.bind(E),E.getScrollLeftForOffset=E.getScrollLeftForOffset.bind(E),E.getScrollTopForOffset=E.getScrollTopForOffset.bind(E),E.scrollLeft=E.scrollLeft.bind(E),E.scrollTop=E.scrollTop.bind(E),E.scrollToLeft=E.scrollToLeft.bind(E),E.scrollToTop=E.scrollToTop.bind(E),E.scrollToRight=E.scrollToRight.bind(E),E.scrollToBottom=E.scrollToBottom.bind(E),E.handleTrackMouseEnter=E.handleTrackMouseEnter.bind(E),E.handleTrackMouseLeave=E.handleTrackMouseLeave.bind(E),E.handleHorizontalTrackMouseDown=E.handleHorizontalTrackMouseDown.bind(E),E.handleVerticalTrackMouseDown=E.handleVerticalTrackMouseDown.bind(E),E.handleHorizontalThumbMouseDown=E.handleHorizontalThumbMouseDown.bind(E),E.handleVerticalThumbMouseDown=E.handleVerticalThumbMouseDown.bind(E),E.handleWindowResize=E.handleWindowResize.bind(E),E.handleScroll=E.handleScroll.bind(E),E.handleDrag=E.handleDrag.bind(E),E.handleDragEnd=E.handleDragEnd.bind(E),E.state={didMountUniversal:!1},E}return r(A,[{key:"componentDidMount",value:function(){this.addListeners(),this.update(),this.componentDidMountUniversal()}},{key:"componentDidMountUniversal",value:function(){var T=this.props.universal;T&&this.setState({didMountUniversal:!0})}},{key:"componentDidUpdate",value:function(){this.update()}},{key:"componentWillUnmount",value:function(){this.removeListeners(),(0,t.cancel)(this.requestFrame),clearTimeout(this.hideTracksTimeout),clearInterval(this.detectScrollingInterval)}},{key:"getScrollLeft",value:function(){return this.view?this.view.scrollLeft:0}},{key:"getScrollTop",value:function(){return this.view?this.view.scrollTop:0}},{key:"getScrollWidth",value:function(){return this.view?this.view.scrollWidth:0}},{key:"getScrollHeight",value:function(){return this.view?this.view.scrollHeight:0}},{key:"getClientWidth",value:function(){return this.view?this.view.clientWidth:0}},{key:"getClientHeight",value:function(){return this.view?this.view.clientHeight:0}},{key:"getValues",value:function(){var T=this.view||{},k=T.scrollLeft,L=k===void 0?0:k,V=T.scrollTop,E=V===void 0?0:V,W=T.scrollWidth,B=W===void 0?0:W,Q=T.scrollHeight,J=Q===void 0?0:Q,he=T.clientWidth,te=he===void 0?0:he,ce=T.clientHeight,le=ce===void 0?0:ce;return{left:L/(B-te)||0,top:E/(J-le)||0,scrollLeft:L,scrollTop:E,scrollWidth:B,scrollHeight:J,clientWidth:te,clientHeight:le}}},{key:"getThumbHorizontalWidth",value:function(){var T=this.props,k=T.thumbSize,L=T.thumbMinSize,V=this.view,E=V.scrollWidth,W=V.clientWidth,B=(0,g.default)(this.trackHorizontal),Q=Math.ceil(W/E*B);return B<=Q?0:k||Math.max(Q,L)}},{key:"getThumbVerticalHeight",value:function(){var T=this.props,k=T.thumbSize,L=T.thumbMinSize,V=this.view,E=V.scrollHeight,W=V.clientHeight,B=(0,x.default)(this.trackVertical),Q=Math.ceil(W/E*B);return B<=Q?0:k||Math.max(Q,L)}},{key:"getScrollLeftForOffset",value:function(T){var k=this.view,L=k.scrollWidth,V=k.clientWidth,E=(0,g.default)(this.trackHorizontal),W=this.getThumbHorizontalWidth();return T/(E-W)*(L-V)}},{key:"getScrollTopForOffset",value:function(T){var k=this.view,L=k.scrollHeight,V=k.clientHeight,E=(0,x.default)(this.trackVertical),W=this.getThumbVerticalHeight();return T/(E-W)*(L-V)}},{key:"scrollLeft",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;this.view&&(this.view.scrollLeft=T)}},{key:"scrollTop",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;this.view&&(this.view.scrollTop=T)}},{key:"scrollToLeft",value:function(){this.view&&(this.view.scrollLeft=0)}},{key:"scrollToTop",value:function(){this.view&&(this.view.scrollTop=0)}},{key:"scrollToRight",value:function(){this.view&&(this.view.scrollLeft=this.view.scrollWidth)}},{key:"scrollToBottom",value:function(){this.view&&(this.view.scrollTop=this.view.scrollHeight)}},{key:"addListeners",value:function(){if(!(typeof document>"u"||!this.view)){var T=this.view,k=this.trackHorizontal,L=this.trackVertical,V=this.thumbHorizontal,E=this.thumbVertical;T.addEventListener("scroll",this.handleScroll),(0,h.default)()&&(k.addEventListener("mouseenter",this.handleTrackMouseEnter),k.addEventListener("mouseleave",this.handleTrackMouseLeave),k.addEventListener("mousedown",this.handleHorizontalTrackMouseDown),L.addEventListener("mouseenter",this.handleTrackMouseEnter),L.addEventListener("mouseleave",this.handleTrackMouseLeave),L.addEventListener("mousedown",this.handleVerticalTrackMouseDown),V.addEventListener("mousedown",this.handleHorizontalThumbMouseDown),E.addEventListener("mousedown",this.handleVerticalThumbMouseDown),window.addEventListener("resize",this.handleWindowResize))}}},{key:"removeListeners",value:function(){if(!(typeof document>"u"||!this.view)){var T=this.view,k=this.trackHorizontal,L=this.trackVertical,V=this.thumbHorizontal,E=this.thumbVertical;T.removeEventListener("scroll",this.handleScroll),(0,h.default)()&&(k.removeEventListener("mouseenter",this.handleTrackMouseEnter),k.removeEventListener("mouseleave",this.handleTrackMouseLeave),k.removeEventListener("mousedown",this.handleHorizontalTrackMouseDown),L.removeEventListener("mouseenter",this.handleTrackMouseEnter),L.removeEventListener("mouseleave",this.handleTrackMouseLeave),L.removeEventListener("mousedown",this.handleVerticalTrackMouseDown),V.removeEventListener("mousedown",this.handleHorizontalThumbMouseDown),E.removeEventListener("mousedown",this.handleVerticalThumbMouseDown),window.removeEventListener("resize",this.handleWindowResize),this.teardownDragging())}}},{key:"handleScroll",value:function(T){var k=this,L=this.props,V=L.onScroll,E=L.onScrollFrame;V&&V(T),this.update(function(W){var B=W.scrollLeft,Q=W.scrollTop;k.viewScrollLeft=B,k.viewScrollTop=Q,E&&E(W)}),this.detectScrolling()}},{key:"handleScrollStart",value:function(){var T=this.props.onScrollStart;T&&T(),this.handleScrollStartAutoHide()}},{key:"handleScrollStartAutoHide",value:function(){var T=this.props.autoHide;T&&this.showTracks()}},{key:"handleScrollStop",value:function(){var T=this.props.onScrollStop;T&&T(),this.handleScrollStopAutoHide()}},{key:"handleScrollStopAutoHide",value:function(){var T=this.props.autoHide;T&&this.hideTracks()}},{key:"handleWindowResize",value:function(){(0,h.default)(!1),this.forceUpdate()}},{key:"handleHorizontalTrackMouseDown",value:function(T){T.preventDefault();var k=T.target,L=T.clientX,V=k.getBoundingClientRect(),E=V.left,W=this.getThumbHorizontalWidth(),B=Math.abs(E-L)-W/2;this.view.scrollLeft=this.getScrollLeftForOffset(B)}},{key:"handleVerticalTrackMouseDown",value:function(T){T.preventDefault();var k=T.target,L=T.clientY,V=k.getBoundingClientRect(),E=V.top,W=this.getThumbVerticalHeight(),B=Math.abs(E-L)-W/2;this.view.scrollTop=this.getScrollTopForOffset(B)}},{key:"handleHorizontalThumbMouseDown",value:function(T){T.preventDefault(),this.handleDragStart(T);var k=T.target,L=T.clientX,V=k.offsetWidth,E=k.getBoundingClientRect(),W=E.left;this.prevPageX=V-(L-W)}},{key:"handleVerticalThumbMouseDown",value:function(T){T.preventDefault(),this.handleDragStart(T);var k=T.target,L=T.clientY,V=k.offsetHeight,E=k.getBoundingClientRect(),W=E.top;this.prevPageY=V-(L-W)}},{key:"setupDragging",value:function(){(0,s.default)(document.body,C.disableSelectStyle),document.addEventListener("mousemove",this.handleDrag),document.addEventListener("mouseup",this.handleDragEnd),document.onselectstart=b.default}},{key:"teardownDragging",value:function(){(0,s.default)(document.body,C.disableSelectStyleReset),document.removeEventListener("mousemove",this.handleDrag),document.removeEventListener("mouseup",this.handleDragEnd),document.onselectstart=void 0}},{key:"handleDragStart",value:function(T){this.dragging=!0,T.stopImmediatePropagation(),this.setupDragging()}},{key:"handleDrag",value:function(T){if(this.prevPageX){var k=T.clientX,L=this.trackHorizontal.getBoundingClientRect(),V=L.left,E=this.getThumbHorizontalWidth(),W=E-this.prevPageX,B=-V+k-W;this.view.scrollLeft=this.getScrollLeftForOffset(B)}if(this.prevPageY){var Q=T.clientY,J=this.trackVertical.getBoundingClientRect(),he=J.top,te=this.getThumbVerticalHeight(),ce=te-this.prevPageY,le=-he+Q-ce;this.view.scrollTop=this.getScrollTopForOffset(le)}return!1}},{key:"handleDragEnd",value:function(){this.dragging=!1,this.prevPageX=this.prevPageY=0,this.teardownDragging(),this.handleDragEndAutoHide()}},{key:"handleDragEndAutoHide",value:function(){var T=this.props.autoHide;T&&this.hideTracks()}},{key:"handleTrackMouseEnter",value:function(){this.trackMouseOver=!0,this.handleTrackMouseEnterAutoHide()}},{key:"handleTrackMouseEnterAutoHide",value:function(){var T=this.props.autoHide;T&&this.showTracks()}},{key:"handleTrackMouseLeave",value:function(){this.trackMouseOver=!1,this.handleTrackMouseLeaveAutoHide()}},{key:"handleTrackMouseLeaveAutoHide",value:function(){var T=this.props.autoHide;T&&this.hideTracks()}},{key:"showTracks",value:function(){clearTimeout(this.hideTracksTimeout),(0,s.default)(this.trackHorizontal,{opacity:1}),(0,s.default)(this.trackVertical,{opacity:1})}},{key:"hideTracks",value:function(){var T=this;if(!this.dragging&&!this.scrolling&&!this.trackMouseOver){var k=this.props.autoHideTimeout;clearTimeout(this.hideTracksTimeout),this.hideTracksTimeout=setTimeout(function(){(0,s.default)(T.trackHorizontal,{opacity:0}),(0,s.default)(T.trackVertical,{opacity:0})},k)}}},{key:"detectScrolling",value:function(){var T=this;this.scrolling||(this.scrolling=!0,this.handleScrollStart(),this.detectScrollingInterval=setInterval(function(){T.lastViewScrollLeft===T.viewScrollLeft&&T.lastViewScrollTop===T.viewScrollTop&&(clearInterval(T.detectScrollingInterval),T.scrolling=!1,T.handleScrollStop()),T.lastViewScrollLeft=T.viewScrollLeft,T.lastViewScrollTop=T.viewScrollTop},100))}},{key:"raf",value:function(T){var k=this;this.requestFrame&&o.default.cancel(this.requestFrame),this.requestFrame=(0,o.default)(function(){k.requestFrame=void 0,T()})}},{key:"update",value:function(T){var k=this;this.raf(function(){return k._update(T)})}},{key:"_update",value:function(T){var k=this.props,L=k.onUpdate,V=k.hideTracksWhenNotNeeded,E=this.getValues();if((0,h.default)()){var W=E.scrollLeft,B=E.clientWidth,Q=E.scrollWidth,J=(0,g.default)(this.trackHorizontal),he=this.getThumbHorizontalWidth(),te=W/(Q-B)*(J-he),ce={width:he,transform:"translateX("+te+"px)"},le=E.scrollTop,we=E.clientHeight,ae=E.scrollHeight,De=(0,x.default)(this.trackVertical),me=this.getThumbVerticalHeight(),Le=le/(ae-we)*(De-me),Oe={height:me,transform:"translateY("+Le+"px)"};if(V){var xe={visibility:Q>B?"visible":"hidden"},ge={visibility:ae>we?"visible":"hidden"};(0,s.default)(this.trackHorizontal,xe),(0,s.default)(this.trackVertical,ge)}(0,s.default)(this.thumbHorizontal,ce),(0,s.default)(this.thumbVertical,Oe)}L&&L(E),typeof T=="function"&&T(E)}},{key:"render",value:function(){var T=this,k=(0,h.default)(),L=this.props;L.onScroll,L.onScrollFrame,L.onScrollStart,L.onScrollStop,L.onUpdate;var V=L.renderView,E=L.renderTrackHorizontal,W=L.renderTrackVertical,B=L.renderThumbHorizontal,Q=L.renderThumbVertical,J=L.tagName;L.hideTracksWhenNotNeeded;var he=L.autoHide;L.autoHideTimeout;var te=L.autoHideDuration;L.thumbSize,L.thumbMinSize;var ce=L.universal,le=L.autoHeight,we=L.autoHeightMin,ae=L.autoHeightMax,De=L.style,me=L.children,Le=D(L,["onScroll","onScrollFrame","onScrollStart","onScrollStop","onUpdate","renderView","renderTrackHorizontal","renderTrackVertical","renderThumbHorizontal","renderThumbVertical","tagName","hideTracksWhenNotNeeded","autoHide","autoHideTimeout","autoHideDuration","thumbSize","thumbMinSize","universal","autoHeight","autoHeightMin","autoHeightMax","style","children"]),Oe=this.state.didMountUniversal,xe=n({},C.containerStyleDefault,le&&n({},C.containerStyleAutoHeight,{minHeight:we,maxHeight:ae}),De),ge=n({},C.viewStyleDefault,{marginRight:k?-k:0,marginBottom:k?-k:0},le&&n({},C.viewStyleAutoHeight,{minHeight:(0,f.default)(we)?"calc("+we+" + "+k+"px)":we+k,maxHeight:(0,f.default)(ae)?"calc("+ae+" + "+k+"px)":ae+k}),le&&ce&&!Oe&&{minHeight:we,maxHeight:ae},ce&&!Oe&&C.viewStyleUniversalInitial),Ke={transition:"opacity "+te+"ms",opacity:0},Se=n({},C.trackHorizontalStyleDefault,he&&Ke,(!k||ce&&!Oe)&&{display:"none"}),nt=n({},C.trackVerticalStyleDefault,he&&Ke,(!k||ce&&!Oe)&&{display:"none"});return(0,i.createElement)(J,n({},Le,{style:xe,ref:function(G){T.container=G}}),[(0,i.cloneElement)(V({style:ge}),{key:"view",ref:function(G){T.view=G}},me),(0,i.cloneElement)(E({style:Se}),{key:"trackHorizontal",ref:function(G){T.trackHorizontal=G}},(0,i.cloneElement)(B({style:C.thumbHorizontalStyleDefault}),{ref:function(G){T.thumbHorizontal=G}})),(0,i.cloneElement)(W({style:nt}),{key:"trackVertical",ref:function(G){T.trackVertical=G}},(0,i.cloneElement)(Q({style:C.thumbVerticalStyleDefault}),{ref:function(G){T.thumbVertical=G}}))])}}]),A}(i.Component);e.default=z,z.propTypes={onScroll:l.default.func,onScrollFrame:l.default.func,onScrollStart:l.default.func,onScrollStop:l.default.func,onUpdate:l.default.func,renderView:l.default.func,renderTrackHorizontal:l.default.func,renderTrackVertical:l.default.func,renderThumbHorizontal:l.default.func,renderThumbVertical:l.default.func,tagName:l.default.string,thumbSize:l.default.number,thumbMinSize:l.default.number,hideTracksWhenNotNeeded:l.default.bool,autoHide:l.default.bool,autoHideTimeout:l.default.number,autoHideDuration:l.default.number,autoHeight:l.default.bool,autoHeightMin:l.default.oneOfType([l.default.number,l.default.string]),autoHeightMax:l.default.oneOfType([l.default.number,l.default.string]),universal:l.default.bool,style:l.default.object,children:l.default.node},z.defaultProps={renderView:$.renderViewDefault,renderTrackHorizontal:$.renderTrackHorizontalDefault,renderTrackVertical:$.renderTrackVerticalDefault,renderThumbHorizontal:$.renderThumbHorizontalDefault,renderThumbVertical:$.renderThumbVerticalDefault,tagName:"div",thumbMinSize:30,hideTracksWhenNotNeeded:!1,autoHide:!1,autoHideTimeout:1e3,autoHideDuration:200,autoHeight:!1,autoHeightMin:0,autoHeightMax:200,universal:!1}})(Hd);(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Scrollbars=void 0;var n=Hd,r=t(n);function t(o){return o&&o.__esModule?o:{default:o}}e.default=r.default,e.Scrollbars=r.default})(Fd);const j3=Qr(Fd),mi=({hasText:e})=>R("div",{className:"fb-flex fb-items-center fb-justify-start fb-gap-[9px] fb-p-2",children:[R("svg",{className:"fb-h-[18px] fb-w-[18px] fb-animate-spin fb-text-primary",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[u("circle",{className:"fb-opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),u("path",{className:"fb-opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e?P("loading"):null]});mi.defaultProps={hasText:!0};const G3=`fb-relative fb-clear-both fb-opacity-50 fb-user-select-none fb-pointer-events-none fb-transition-opacity fb-duration-300
  after:fb-opacity-40 after:fb-absolute after:fb-top-0 after:fb-w-full after:fb-h-full after:fb-start-0 after:fb-end-0 after:fb-z-[100] after:fb-bg-white after:fb-transition-all after:fb-duration-300
`,Y3=({children:e,isLoading:n})=>R("div",{className:"fb-relative",children:[n&&u("div",{children:u("div",{className:"fb-absolute fb-top-0 fb-block fb-w-full fb-h-full z-[99] fb-max-w-[400px] fb-text-center fb-start-0",children:u("div",{className:"fb-absolute fb-top-1/2 fb-start-1/2 -fb-m-[17px]",children:u(mi,{hasText:!1})})})}),u("div",{className:n?G3:void 0,children:e})]}),Z3=oe.div`
  fb-absolute fb-z-[1] fb-h-[2px] fb-bg-primary fb-rounded-[1px] fb-pointer-events-none
  after:fb-absolute after:-fb-top-[3px] after:-fb-left-[6px] after:fb-w-[8px] after:fb-h-[8px] 
  after:fb-bg-transparent after:fb-border-[2px] after:fb-border-solid after:fb-border-primary after:fb-rounded-full after:fb-content-[""]
`,js=4,X3=({dropPosition:e,dropLevelOffset:n,indent:r,direction:t})=>{const o=t==="ltr"?"left":"right",a=t==="ltr"?"right":"left",s={[o]:-n*r+js-30,[a]:0};switch(e){case-1:s.top=-3;break;case 1:s.bottom=-3;break;case 0:s.bottom=-3,s[o]=r+js-30;break}return u(Z3,{style:s})},{fbv_data:q3}=window,la={default:{singleFolderIcon:u(nc,{}),expandedFolderIcon:u(rc,{}),collapsedFolderIcon:u(oc,{})},windows:{singleFolderIcon:u(Lr,{}),expandedFolderIcon:u(O1,{}),collapsedFolderIcon:u(D1,{})},dropbox:{singleFolderIcon:u(Pr,{}),expandedFolderIcon:u(C1,{}),collapsedFolderIcon:u(y1,{})}},Q3=fn(["fb-flex fb-items-center fb-justify-center"],{variants:{$theme:{default:"fb-w-5 fb-h-5 [&_svg]:fb-w-[inherit] [&_svg]:fb-h-[inherit]",windows:"fb-gap-[17px] [&_svg.fb-arrow]:fb-w-[8px] [&_svg.fb-arrow]:fb-h-[8px] [&:not(:has(.fb-arrow))_svg]:fb-ml-[25px] [&_svg:not(.fb-arrow)]:fb-h-[17px] [&_svg:not(.fb-arrow)]:fb-w-[17px]",dropbox:"fb-gap-[17px] [&_svg.fb-arrow]:fb-w-[8px] [&_svg.fb-arrow]:fb-h-[8px] [&:not(:has(.fb-arrow))_svg]:fb-ml-[25px] [&_svg:not(.fb-arrow)]:fb-h-[17px] [&_svg:not(.fb-arrow)]:fb-w-[17px]"}}}),da=dn.i(({$theme:e})=>Q3({$theme:e})),J3=e=>{const{theme:n}=ke(),r=e.selected&&n==="default"?"#2271b1":e.color||q3.user_settings.THEME.colors[n];return e.isLeaf?u(da,{$theme:n,style:{color:r},children:la[n].singleFolderIcon}):e.expanded?u(da,{$theme:n,style:{color:r},children:la[n].collapsedFolderIcon}):u(da,{$theme:n,style:{color:r},children:la[n].expandedFolderIcon})},e6=c.memo(J3),Gd="ContextMenu",[t6,s5]=Kt(Gd,[lo]),zt=lo(),[n6,Yd]=t6(Gd),r6=e=>{const{__scopeContextMenu:n,children:r,onOpenChange:t,dir:o,modal:a=!0}=e,[s,i]=c.useState(!1),d=zt(n),l=cn(t),p=c.useCallback(f=>{i(f),l(f)},[l]);return c.createElement(n6,{scope:n,open:s,onOpenChange:p,modal:a},c.createElement(Dl,X({},d,{dir:o,open:s,onOpenChange:p,modal:a}),r))},o6="ContextMenuTrigger",a6=c.forwardRef((e,n)=>{const{__scopeContextMenu:r,disabled:t=!1,...o}=e,a=Yd(o6,r),s=zt(r),i=c.useRef({x:0,y:0}),d=c.useRef({getBoundingClientRect:()=>DOMRect.fromRect({width:0,height:0,...i.current})}),l=c.useRef(0),p=c.useCallback(()=>window.clearTimeout(l.current),[]),f=v=>{i.current={x:v.clientX,y:v.clientY},a.onOpenChange(!0)};return c.useEffect(()=>p,[p]),c.useEffect(()=>void(t&&p()),[t,p]),c.createElement(c.Fragment,null,c.createElement(Ol,X({},s,{virtualRef:d})),c.createElement(Me.span,X({"data-state":a.open?"open":"closed","data-disabled":t?"":void 0},o,{ref:n,style:{WebkitTouchCallout:"none",...e.style},onContextMenu:t?e.onContextMenu:q(e.onContextMenu,v=>{p(),f(v),v.preventDefault()}),onPointerDown:t?e.onPointerDown:q(e.onPointerDown,Or(v=>{p(),l.current=window.setTimeout(()=>f(v),700)})),onPointerMove:t?e.onPointerMove:q(e.onPointerMove,Or(p)),onPointerCancel:t?e.onPointerCancel:q(e.onPointerCancel,Or(p)),onPointerUp:t?e.onPointerUp:q(e.onPointerUp,Or(p))})))}),i6=e=>{const{__scopeContextMenu:n,...r}=e,t=zt(n);return c.createElement(Rl,X({},t,r))},s6="ContextMenuContent",c6=c.forwardRef((e,n)=>{const{__scopeContextMenu:r,...t}=e,o=Yd(s6,r),a=zt(r),s=c.useRef(!1);return c.createElement(Nl,X({},a,t,{ref:n,side:"right",sideOffset:2,align:"start",onCloseAutoFocus:i=>{var d;(d=e.onCloseAutoFocus)===null||d===void 0||d.call(e,i),!i.defaultPrevented&&s.current&&i.preventDefault(),s.current=!1},onInteractOutside:i=>{var d;(d=e.onInteractOutside)===null||d===void 0||d.call(e,i),!i.defaultPrevented&&!o.modal&&(s.current=!0)},style:{...e.style,"--radix-context-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-context-menu-content-available-width":"var(--radix-popper-available-width)","--radix-context-menu-content-available-height":"var(--radix-popper-available-height)","--radix-context-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-context-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))}),l6=c.forwardRef((e,n)=>{const{__scopeContextMenu:r,...t}=e,o=zt(r);return c.createElement(kl,X({},o,t,{ref:n}))}),d6=c.forwardRef((e,n)=>{const{__scopeContextMenu:r,...t}=e,o=zt(r);return c.createElement(Pl,X({},o,t,{ref:n}))}),u6=e=>{const{__scopeContextMenu:n,children:r,onOpenChange:t,open:o,defaultOpen:a}=e,s=zt(n),[i,d]=un({prop:o,defaultProp:a,onChange:t});return c.createElement(Ll,X({},s,{open:i,onOpenChange:d}),r)},f6=c.forwardRef((e,n)=>{const{__scopeContextMenu:r,...t}=e,o=zt(r);return c.createElement(Al,X({},o,t,{ref:n}))}),p6=c.forwardRef((e,n)=>{const{__scopeContextMenu:r,...t}=e,o=zt(r);return c.createElement(Il,X({},o,t,{ref:n,style:{...e.style,"--radix-context-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-context-menu-content-available-width":"var(--radix-popper-available-width)","--radix-context-menu-content-available-height":"var(--radix-popper-available-height)","--radix-context-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-context-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))});function Or(e){return n=>n.pointerType!=="mouse"?e(n):void 0}const h6=r6,v6=a6,Gs=i6,m6=c6,b6=l6,g6=d6,y6=u6,C6=f6,w6=p6;function _6(e){return n=>{e.forEach(r=>{typeof r=="function"?r(n):r!=null&&(r.current=n)})}}const S6=["#F73A21","#FA573C","#FF7537","#FFAD45","#F9D165","#FADD55","#3C78D8","#6D9EEB","#D7E2FA","#17A765","#7AD149","#B3DC6B","#674EA7","#8E7CC3","#B4A7D6","#CF51AC","#EE76BF","#959DA1"],x6={default:"#8f8f8f",windows:"#F3C73E",dropbox:"#88C1FC"},E6=oe.span`fb-flex fb-h-6 fb-w-6 fb-cursor-pointer fb-items-center fb-justify-center fb-transition-all fb-duration-200`,$6=({color:e,changeColor:n})=>{const{theme:r}=ke(),[t,o]=c.useState(e),a=i=>{n(i),o(i)},s=i=>{o(i.target.value)};return R("div",{className:"fb-rounded fb-bg-white fb-p-3 fb-shadow-[0_2px_8px_2px_rgba(28,39,60,0.2)]",children:[u("div",{className:"fb-grid fb-grid-cols-6 fb-gap-3",children:S6.map((i,d)=>u(E6,{className:ve(i!==t&&"hover:before:fb-content-[' 543'] hover:fb-scale-125 hover:before:fb-font-['dashicons'] hover:before:fb-text-white"),onClick:()=>a(i),style:{backgroundColor:i},children:i===t&&u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"#fff",children:u("path",{d:"M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"})})},d))}),u("div",{className:"fb-my-[10px] fb-h-[1px] fb-bg-[rgb(229,229,229)]"}),R("div",{className:"fb-flex fb-items-center",children:[u("span",{className:"fb-relative fb-my-1 fb-mr-2 fb-inline-block fb-h-7 fb-w-7 fb-cursor-pointer fb-rounded hover:fb-shadow-[0_3px_8px_rgba(0,0,0,0.24)]",onClick:()=>n(t),style:{backgroundColor:t},children:e===t&&u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"#fff",children:u("path",{d:"M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"})})}),u("input",{className:"fb-h-7 fb-w-20 fb-rounded-md fb-border fb-border-solid fb-border-default fb-text-left",type:"text",value:t,onChange:s}),u("span",{className:"fb-ml-auto fb-flex fb-cursor-pointer fb-items-center fb-justify-center",onClick:()=>a(x6[r]),children:u(qe,{icon:"folderColorResetIcon",className:"fb-mr-2 fb-h-5 fb-w-5 fb-text-default"})})]})]})},T6=200,M6=oe.input`fb-m-0 fb-h-8 fb-min-h-8 fb-w-full fb-border-2 fb-border-solid fb-border-primary fb-text-[13px] fb-leading-8 fb-shadow-none fb-outline-none`,Zd=oe.button`fb-inline-flex fb-leading-normal fb-h-auto fb-cursor-pointer fb-touch-manipulation fb-select-none fb-items-center fb-justify-center fb-whitespace-nowrap fb-rounded-[3px] fb-border-none fb-px-2 fb-py-1 fb-text-[13px] fb-font-medium fb-shadow-none fb-transition-all fb-duration-200`,D6=oe(Zd)`fb-bg-[#dddbdb] fb-text-[#5f5f5f]`,O6=oe(Zd)`fb-ml-[5px] fb-bg-primary fb-text-white hover:fb-bg-[#2987d3]`,R6=({node:e})=>{const[n,r]=c.useState(""),t=U(h=>h.updateFolder),o=U(h=>h.createFolder),a=U(h=>h.clearEditFolder),s=()=>{U.getState().editFolderId==="#"?d():l()},i=h=>{h.stopPropagation(),a()},d=()=>{o({title:n,parent:e.parent}).then(h=>{qr(h),dt.success(P("success"))}).catch(h=>{console.log({error:h}),dt.error(h.responseJSON.message)})},l=()=>{e&&t({...e,title:n}).then(()=>{dt.success(P("update_done"))}).catch(h=>{dt.error(h.responseJSON.message)})},p=h=>{h.key==="Escape"&&a(),h.key==="Enter"&&s()},f=c.useRef(null);c.useEffect(()=>{var h;e&&typeof e.title=="string"&&r(Fa(e.title)),(h=f.current)==null||h.focus()},[]);const v=h=>{if(h.target.value.length>=T6){dt.error(P("max_input_length"));return}r(h.target.value)};return R("span",{children:[u(M6,{value:n,ref:f,type:"text",className:"filebird-edit-folder",placeholder:P("enter_folder_name_placeholder"),onKeyDown:p,onChange:v}),R("div",{children:[u(D6,{onClick:i,children:P("cancel")}),u(O6,{onClick:s,children:P("save")})]})]})},{user_settings:N6,tree_mode:k6}=window.fbv_data,P6=oe(m6)`fb-shadow-[0_2px_8px_2px_rgba(28,39,60,0.2)] fb-z-biggest fb-m-0 fb-block fb-min-w-[234px] fb-list-none fb-rounded fb-bg-white fb-p-1 fb-outline-none`,xn=oe(b6)`fb-relative fb-m-0 fb-flex fb-h-9 fb-cursor-pointer fb-items-center fb-justify-start fb-leading-9 fb-text-[#444] fb-outline-none hover:fb-rounded-sm hover:fb-bg-[rgba(0,0,0,0.06)] data-[disabled]:fb-opacity-50 data-[disabled]:fb-pointer-events-none`,L6=oe(C6)`fb-relative fb-m-0 fb-flex fb-h-9 fb-cursor-pointer fb-items-center fb-justify-start fb-leading-9 fb-text-[#444] fb-outline-none hover:fb-rounded-sm hover:fb-bg-[rgba(0,0,0,0.06)] data-[disabled]:fb-opacity-50 data-[disabled]:fb-pointer-events-none`,tn={default:"fb-text-default",windows:"fb-text-[#757469]",dropbox:"fb-text-[#757469]"},A6=oe.span`fb-absolute fb-right-[7px] fb-top-1/2 -fb-translate-y-1/2 fb-rounded-[3px] fb-bg-white fb-px-[6px] fb-py-[5px] fb-text-[10px] fb-leading-none fb-text-[#333]`,I6=c.forwardRef(({node:e,setShowConfirm:n,...r},t)=>{const o=Yt(g=>g.displayFolderId),a=U(g=>g.updateFolderColor),s=U(g=>g.createChildFolder),i=U(g=>g.checkable),d=U(g=>g.editFolder),l=U(g=>g.cuttingNode),p=U(g=>g.setCuttingNode),f=U(g=>g.dnd),{theme:v}=ke(),h=async g=>{e.key&&await a(e.key,g)},m=async()=>{e.key&&dt.promise(Ia.downloadFolder(e.key),{loading:P("generate_download"),success:g=>(g.success&&window.open(g.data.link,"_blank"),P("success")),error:g=>(console.log(g),P("please_try_again"))})},b=(g,w)=>{w==="create"&&s(e.id),w==="edit"&&d(e.id),w==="delete"&&n(!0),w==="cut"&&p(e),w==="paste"&&l&&f(e.id,l.id,0,0).then(()=>p(null)),w==="download"&&m()},y=c.useMemo(()=>!l||l.id===e.id,[l]);return R(h6,{children:[u(v6,{className:"fb-context-menu",disabled:i,children:u(rr,{sideOffset:5,content:Fa(e.title),requiresOverflowToShow:!0,children:g=>R("span",{ref:_6([g,t]),className:"fb-tree-title-inside",...r,children:[o&&R(Ne,{children:[R("span",{children:["#",e.id]})," "]}),u("span",{dangerouslySetInnerHTML:{__html:Va.sanitize(e.title)}})]})})}),u(Gs,{children:R(P6,{sideOffset:5,align:"end",children:[R(xn,{onClick:g=>b(g,"create"),children:[u("span",{children:u(qe,{icon:"newFolderIcon",className:ve("fb-mx-[10px]",tn[v])})}),P("new_folder")]}),u(g6,{className:"fb-mx-0 fb-my-1 fb-h-[1px] fb-w-full fb-bg-[rgba(72,94,144,0.16)]"}),R(xn,{onClick:g=>b(g,"edit"),children:[u("span",{children:u(qe,{icon:"editFolderIcon",className:ve("fb-mx-[10px]",tn[v])})}),P("rename")]}),R(xn,{onClick:g=>b(g,"cut"),children:[u("span",{children:u(qe,{icon:"cutFolder",className:ve("fb-mx-[10px]",tn[v])})}),P("cut")]}),R(xn,{disabled:y,onClick:g=>b(g,"paste"),children:[u("span",{children:u(qe,{icon:"pasteFolder",className:ve("fb-mx-[10px]",tn[v])})}),P("paste")]}),R(xn,{onSelect:g=>b(g,"delete"),children:[u("span",{children:u(qe,{icon:"deleteFolderIcon",className:ve("fb-mx-[10px]",tn[v])})}),P("delete")]}),R(y6,{children:[R(L6,{disabled:!0,children:[u("span",{children:u(qe,{icon:"folderColorIcon",className:ve("fb-mx-[10px]",tn[v])})}),P("change_color")," ",u(Gn,{}),u("div",{className:"fb-ml-auto fb-flex fb-items-center",children:u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"fb-h-6 fb-w-6 fb-fill-[#8f8f8f]",children:u("path",{d:"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"})})})]}),u(Gs,{children:u(w6,{className:"ContextMenuSubContent",sideOffset:2,alignOffset:0,children:u($6,{color:e.color||N6.THEME.colors[v],changeColor:h})})})]}),k6==="attachment"&&R(xn,{disabled:!0,onClick:g=>b(g,"download"),children:[u("span",{children:u(qe,{icon:"downloadFolderIcon",className:ve("fb-mx-[10px]",tn[v])})}),P("download")," ",u(Gn,{})]})]})})]})}),F6=({node:e,isEdit:n,count:r})=>{const t=c.useRef(null),{attachmentsBrowser:o}=ke(),{deleteFolder:a}=Nn(),[s,i]=c.useState(!1);c.useEffect(()=>{if(t.current){const l=t.current.closest("[data-id]");l&&(jQuery(l).hasClass("ui-droppable")||Ba.forNode(l,o))}},[]);const d=l=>{l.stopPropagation(),s&&a(e.key).then(()=>{dt.success(P("delete_done")),i(!1)}).catch(p=>{dt.error(p.responseJSON.message)})};return n?u(R6,{node:e}):R(Ne,{children:[R(Zl,{open:s,children:[u(Xl,{asChild:!0,children:u(I6,{ref:t,node:e,setShowConfirm:i})}),u(ql,{sideOffset:5,onPointerDownOutside:()=>i(!1),children:u(Ql,{title:u("span",{children:P("delete_folder")}),content:u("span",{dangerouslySetInnerHTML:{__html:P("are_you_sure_delete_this_folder")}}),confirmText:P("delete"),onConfirm:d,cancelButton:u("button",{className:"button",onClick:l=>{l.stopPropagation(),i(!1)},children:P("cancel")})})})]}),e.id!="#"&&u(A6,{children:r})]})},H6=c.memo(F6),{is_rtl:Ys,folder_search_api:ua}=window.fbv_data,K6={motionName:"node-motion",motionAppear:!1,onAppearStart:()=>({height:0}),onAppearActive:e=>({height:e.scrollHeight}),onLeaveStart:e=>({height:e.offsetHeight}),onLeaveActive:()=>({height:0})},U6=c.memo(()=>{const{selectFolder:e}=Nn(),n=U(O=>O.folderQuery.search&&!ua?Hu(O.treeData):O.expandedKeys),r=U(O=>O.setExpandedKeys),t=U(O=>O.editFolderId),o=U(O=>O.treeData),a=U(O=>O.setCheckedKeys),s=U(O=>O.checkable),i=U(O=>O.checkedKeys),d=U(O=>O.getTreeData),l=U(O=>O.isLoading),p=U(O=>O.isProcessing),f=U(O=>O.folderQuery.search),v=U(O=>O.selectedKeys),h=U(O=>O.attachmentsCount.display),m=U(O=>O.dnd),b=U(O=>!!O.editFolderId),y=c.useRef(null),w=ke().theme==="default",x=(O,z)=>{const I=z.nativeEvent.target.closest("[data-radix-popper-content-wrapper]");b||I||e(O)},C=O=>{Array.isArray(O)?a(O):a(O.checked)},$=(O,z)=>{r(O)},M=({event:O,node:z})=>{O.dataTransfer.effectAllowed="copyMove";let I=document.getElementById("filebird-ghost-dragging");if(!I){const A=jQuery.parseHTML(`<div id="filebird-ghost-dragging" dir="${Ys?"rtl":"ltr"}"><i></i><span>${z.title}</span></div>`);document.body.appendChild(A[0]),I=A[0]}I.children[1].innerHTML=z.title,O.dataTransfer.setDragImage(I,0,0),document.body.classList.add("filebird-sortable-cover")},D=O=>{const z=O.node.key,I=O.dragNode.key,A=O.node.pos.split("-"),N=O.node.parent,T=O.dropPosition-Number(A[A.length-1]);document.body.classList.remove("filebird-sortable-cover"),m(z,I,N,T)};c.useEffect(()=>{d().then(O=>{qr(O)})},[]);const F=c.useMemo(()=>!(b||s),[b,s]),K=O=>ua||!f||!f.trim()?!1:O.title.toLowerCase().includes(f.toLowerCase());return u(j3,{className:"filebird-scrollbar",autoHide:!0,children:u("div",{className:"inner-filebird-tree",children:l?u(mi,{}):u(Y3,{isLoading:p,children:u(vi,{defaultExpandParent:!1,prefixCls:"fb-tree",className:ve("filebird-tree-wrapper",{searching:f&&!ua}),direction:Ys?"rtl":"ltr",ref:y,filterTreeNode:K,showLine:w,selectedKeys:v,checkable:s,checkedKeys:i,checkStrictly:!0,draggable:F,onSelect:x,onCheck:C,expandedKeys:n,onExpand:$,onDrop:D,motion:K6,switcherIcon:O=>u(e6,{...O}),onDragStart:M,titleRender:O=>u(H6,{node:O,isEdit:t===O.key,count:h[O.key]||0}),treeData:o,dropIndicatorRender:X3})})})})}),{i18n:Rr}=window.fbv_data,z6=()=>{const e=U(l=>l.uploadedAttachments),n=U(l=>l.uploadingFiles),r=U(l=>l.maximizeUploadModal),t=U(l=>l.totalUploadingSize),o=U(l=>l.currentUploadingFile),a=U(l=>l.progressPercent),s=U(l=>l.toggleModal),i=U(l=>l.closeModal),d=U(l=>l.showUploader);return R("div",{className:ve("fbv-uploader",{__maximize:r}),style:{display:d?"block":"none"},children:[R("div",{className:"fbv-u-title-bar-button",children:[R("div",{className:"fbv-u-process",children:[u("div",{className:"fbv-u-sign-sync",children:u("i",{className:"fbf-sync-circle njn-i",children:u("svg",{width:20,height:20,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",children:u("path",{d:"M2 12A10 10 0 1 0 12 2A10 10 0 0 0 2 12M15.6 13.72A4 4 0 0 0 16 12A4 4 0 0 0 12 8V10L8.88 7L12 4V6A6 6 0 0 1 18 12A5.9 5.9 0 0 1 17.07 15.19M6 12A5.9 5.9 0 0 1 6.93 8.81L8.4 10.28A4 4 0 0 0 8 12A4 4 0 0 0 12 16V14L15 17L12 20V18A6 6 0 0 1 6 12Z"})})})}),R("div",{className:"fbv-u-process-title",children:[R("span",{className:"_txt",children:[Rr.uploaded,": ",e.length," / ",n.length," ",!r&&u("span",{className:"_time_remaining",children:Rr.lessThanAMin})]}),r&&u("span",{className:"_time_remaining",children:Rr.lessThanAMin})]})]}),R("div",{className:"fbv-u-bar-button-wrapper",children:[u("button",{id:"fbv-u-toggle",className:ve("fbv-u-toggle",{"fbv-u-minimize":r,"fbv-u-maximize":!r}),type:"button",onClick:s,children:u("span",{className:"njn-i _ChevronUpDown",children:u("svg",{viewBox:"0 0 448 512",width:"1em",height:"1em",children:u("path",{fill:"currentColor",d:"M441.9 167.3l-19.8-19.8c-4.7-4.7-12.3-4.7-17 0L224 328.2 42.9 147.5c-4.7-4.7-12.3-4.7-17 0L6.1 167.3c-4.7 4.7-4.7 12.3 0 17l209.4 209.4c4.7 4.7 12.3 4.7 17 0l209.4-209.4c4.7-4.7 4.7-12.3 0-17z"})})})}),u("button",{id:"fbv-u-close",className:"fbv-u-close",type:"button",onClick:i,children:u("span",{className:"njn-i _Close",children:u("svg",{viewBox:"0 0 320 512",width:"1em",height:"1em",children:u("path",{fill:"currentColor",d:"M193.94 256L296.5 153.44l21.15-21.15c3.12-3.12 3.12-8.19 0-11.31l-22.63-22.63c-3.12-3.12-8.19-3.12-11.31 0L160 222.06 36.29 98.34c-3.12-3.12-8.19-3.12-11.31 0L2.34 120.97c-3.12 3.12-3.12 8.19 0 11.31L126.06 256 2.34 379.71c-3.12 3.12-3.12 8.19 0 11.31l22.63 22.63c3.12 3.12 8.19 3.12 11.31 0L160 289.94 262.56 392.5l21.15 21.15c3.12 3.12 8.19 3.12 11.31 0l22.63-22.63c3.12-3.12 3.12-8.19 0-11.31L193.94 256z"})})})})]})]}),u("div",{id:"fbv-u-progress-bar",className:"fbv-u-progress-bar",style:{"--progress-status":a==0?!1:`${a}%`},children:u("div",{className:"_progress-bar-wrapper",children:u("span",{className:"_progress-status"})})}),u("div",{className:"fbv-u-files-processing",children:R("div",{className:"inner--fbv-u-p-wrapper",children:[u("div",{className:"_list-files-processing",children:u("ul",{className:"_list-files",children:n.map(l=>R("li",{id:`file_${l.id}`,className:ve("_file-uploading",{uploading:o!=null&&l.id==o.id}),children:[R("div",{className:"_file-info",children:[u("i",{className:"fbf-image njn-i",children:u("svg",{fill:"currentColor",width:24,height:24,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:u("path",{d:"M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"})})}),u("span",{className:"_file-name",children:l.name})]}),u("div",{className:ve("_file-status",[{"l-waiting":o!=null&&l.id!=o.id},{"l-loading":o!=null&&l.id==o.id},{"l-success":e.indexOf(l.id)>-1}]),children:e.indexOf(l.id)>-1?u("i",{className:"njn-i fbf-check-circle",children:u("svg",{width:24,height:24,fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:u("path",{d:"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"})})}):u("i",{className:"fbf-loading njn-i",children:R("svg",{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:24,height:24,children:[u("title",{children:P("loading")}),u("path",{d:"M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"})]})})})]},l.id))})}),u("div",{className:"display-total-size",children:R("span",{className:"_total-size",children:[Rr.totalSize,": ",t]})})]})})]})};function Xr({attachmentsBrowser:e,modal:n}){return dc()?u(l1,{}):(c.useEffect(()=>Ku(e,n),[]),u(Ju,{dir:window.fbv_data.is_rtl?"rtl":"ltr",children:R(u1,{attachmentsBrowser:e,modal:n,children:[R(j0,{minWidth:300,maxWidth:650,stopCallback:()=>{setTimeout(()=>{e&&e.attachments.setColumns()},200)},children:[u(wp,{}),u(fb,{}),u($h,{}),u(H1,{}),u(X0,{}),u(up,{}),u(U6,{})]}),u(Sh,{}),u(z6,{}),u(cp,{}),u(mp,{}),u(q0,{})]})}))}const Nr="fbv-drag-selected fbv-disabled-pointer",fa='#the-list tr:has(input[name="media[]"]:input:checked),.attachments .selected:not(.selection,:hidden), #the-list tr:has(input[name="post[]"]:input:checked)',W6="th.check-column",{jQuery:Ie,fbv_data:Ur}=window,{tree_mode:Zs}=Ur,Mt={$mediaList:Ie("#wpbody-content .wp-list-table tbody tr:not(.no-items)"),$postTypeList:Ie(".filebird-post-type #wpbody-content .wp-list-table tbody tr:not(.no-items)"),$bulked:()=>{Ie(fa)},register:(e,n)=>{e.draggable({appendTo:"body",helper:r=>Mt.dragHelper(r,n),cursor:"move",cursorAt:{top:0,left:0},handle:n==="list"?W6:!1,start:function(r){const t=Ie(fa);t.length?t.addClass(Nr):Ie(r.target).addClass(Nr)},stop:function(r){const t=Ie(fa);t.length?t.removeClass(Nr):Ie(r.target).removeClass(Nr)}})},registerMediaList:()=>{Mt.register(Mt.$mediaList,"list")},preparePostTypeRegister:()=>{const e=Mt.$postTypeList.closest("table.widefat tbody");return!Ot&&!e.data("ui-sortable")},registerPostTypeList:()=>{Mt.preparePostTypeRegister()&&Mt.register(Mt.$postTypeList,"list")},dragHelper:(e,n)=>{let r=1;const t=[];if(Zs==="attachment")if(n=="grid"||n=="modal"){const a=Ie(".attachments .selected:not(.selection,:hidden)");if(n=="grid"&&Uu()&&a.length||n=="modal"&&a.length)r=a.length,a.each((s,i)=>{t.push(Ie(i).data("id"))});else{const s=Ie(e.currentTarget);t.push(s.data("id"))}}else{const a=Ie("input[name='media[]']:input:checked, input[name='cb_attachment[]']:input:checked");if(a.length)r=a.length,a.each((s,i)=>{t.push(Ie(i).val())});else{const s=Ie(e.currentTarget).attr("id"),i=s==null?void 0:s.match(/\d+/);i&&t.push(i[0])}}if(Zs==="post_type"){const a=Ie("input[name='post[]']:input:checked");if(a.length)r=a.length,a.each((s,i)=>{t.push(Ie(i).val())});else{const s=Ie(e.currentTarget).attr("id"),i=s==null?void 0:s.match(/\d+/);i&&t.push(i[0])}}const o=`<span data-id=${t.join(",")} class="fb-v-dragging" data-title="${Mt.getTooltip(r)}"></span>`;return Ie(o)},getTooltip(e){return Ur.i18n.move+" "+e+" "+(e>1?Ur.i18n.items:Ur.i18n.item)}},{user_settings:B6,is_rtl:Xs}=window.fbv_data,Xd=window.jQuery.Deferred(),V6=function(){const e=new Date,n=_p("fbv_close_buy_pro_dialog");return!(n!=null&&e<n)},j6=()=>{const{wp:e,fbv_data:n,jQuery:r}=window,t=e.media;let o="grid",a=null;if(t){const s=t.view.AttachmentsBrowser,i=t.view.Attachment.Library,d=t.view.Modal,l=t.view.UploaderInline,p=t.view.Attachments,f=dc();e.media.model.Query.orderby.allowed.push("fb_filesize"),e.media.model.Query.orderby.allowed.push("fb_filename");const v=t.view.AttachmentFilters.FBV=t.view.AttachmentFilters.extend({tagName:"select",className:"fbv-filter attachment-filters fbv",id:"filter-by-fbv",initialize:function(){Xu()&&(this.fbButton=r('<div class="filebird-select-modal" />')[0],this.$el.parent().prepend(this.fbButton),an.createRoot(this.fbButton).render(u(Gu,{attachmentsBrowser:this})))},events:{change:"change"},change:function(h){h.stopPropagation();const m=this.filters[this.el.value];m&&(o==="modal"?(m.props.ignore=+new Date,this.model.set(m.props)):this.model.set(m.props))},select:function(){const m=this.model.toJSON();let b="-1";_.find(this.filters,function(y,g){if(_.all(y.props,function(x,C){return x===(_.isUndefined(m[C])?null:m[C])}))return b=g}),this.$el.val(b)},createFilters:function(h=!1){const m={};_.each(n.folders||{},(b,y)=>{const g=b.term_id,w=b.term_name;m[g]={text:w,props:{fbv:g},priority:y+2}}),h&&this.$el.html(n.folders.map(function(b){return r("<option></option>").val(b.term_id).html(b.term_name)[0].outerHTML}).join("")),this.filters=m,this.listenTo(this.model,"change",this.select),this.select()}});t.view.UploaderInline=t.view.UploaderInline.extend({initialize:function(){l.prototype.initialize.apply(this,arguments),setTimeout(()=>{const h=this.controller.content._mode==="upload",m=this.$el.find(".post-upload-ui #fbv-folder-selector")[0];if(!m)return;an.createRoot(m).render(u(za,{fetchOnLoad:h}))},50)}}),t.view.AttachmentsBrowser=t.view.AttachmentsBrowser.extend({initialize:function(){s.prototype.initialize.apply(this,arguments),!(Bn(this)||f)&&this.collection.on("remove",function(h){a=h.id})},createToolbar:function(){this.$el.data("backboneView",this),s.prototype.createToolbar.apply(this,arguments);const h=new v({controller:this.controller,model:this.collection.props,priority:-81}).render();this.toolbar.set("fbv-filter",h),h.initialize();const{modal:m}=this.controller.options;m?setTimeout(()=>{if(Bn(this))return;const y=this.views.parent;if(y&&y.views){const w=y.views.get(".media-frame-menu"),x=this.$el.find("ul.attachments").is(":visible");if(w&&x){const C=w[0];if(y.$el.find(".filebird-app-wrapper").length||C.views.add(new e.media.View({className:"fb-treeview",el:`<div class="fbv-sidebar"><div class="filebird-app-wrapper"><div id="filebird-root" dir="${Xs?"rtl":"ltr"}"></div></div></div>`})),o="modal",Ot){const $=this.toolbar.get("fbv-filter").$el;if($){$.before('<div id="filebird-modal-mobile"></div>');const M=this.controller.$el.find("#filebird-modal-mobile")[0];an.createRoot(M).render(u(zu,{modal:!0,children:u(Xr,{attachmentsBrowser:this,modal:!0})}))}}else{const $=this.controller.$el.find("#filebird-root")[0];$!=null&&$.fbMounted||($.fbMounted=an.createRoot($)),$.fbMounted.render(u(Xr,{attachmentsBrowser:this,modal:!0})),fc(this),y.$el.removeClass("hide-menu")}}}},50):(o="grid",Xd.resolve(this))},remove:function(){var b;s.prototype.remove.apply(this,arguments);const{$el:h}=this.controller,m=(b=h.find("#filebird-root")[0])==null?void 0:b.fbMounted;m&&(m.unmount(),h.find("#filebird-root")[0].closest(".fbv-sidebar").remove())},updateContent:function(){s.prototype.updateContent.apply(this,arguments),this.collection.length===0&&this.$el.find(".fbv-upload-inline").hide()}}),t.view.Attachments=t.view.Attachments.extend({initialize:function(){var C,$;if(p.prototype.initialize.apply(this,arguments),Bn(this)||f)return;let m=this,b=null,y=!1;const g=m.collection,w=g.mirroring;if(m.controller._state=="gallery-edit"||m.controller._state=="replace-image"||($=(C=g==null?void 0:g.props)==null?void 0:C.attributes)!=null&&$.post__in)return;B6.DEFAULT_FOLDER==ec.PREVIOUS||m.controller.fbvCreated,b=U.getState().selectedKeys[0],typeof m.controller.fbvCreated>"u"?(m.controller.fbvCreated=!0,y=!1):y=!0;const x={fbv:b,ignore:+new Date};if(typeof this.controller.acf<"u"){const M=this.controller.acf;typeof M.get=="function"&&M.get("field")&&(x._acfuploader=M.get("field")),typeof M.get!="function"&&(M!=null&&M.field)&&(x._acfuploader=M.field)}w.args={...w.args,...x},g.props.set(x,{silent:y})}}),!Ot&&!f&&(t.view.Attachment.Library=t.view.Attachment.Library.extend({initialize:function(){i.prototype.initialize.apply(this,arguments),!Bn(this)&&this.on("ready",function(){Mt.register(this.$el,o)})}})),V6()&&(t.view.Modal=t.view.Modal.extend({attributes:{dir:Xs?"rtl":"ltr"},className:Ot?"":"fbv-modal",initialize:function(){d.prototype.initialize.apply(this,arguments)},open:function(){d.prototype.open.apply(this,arguments),(window.mlaModal||window.eml)&&setTimeout(()=>{const m=this.$el.find(".attachments-browser .media-toolbar");this.$el.find(".attachments-browser .attachments").css("top",m.height())},500),o==="modal"&&setTimeout(()=>{const m=parseInt(this.$el.find("#filter-by-fbv").val());isNaN(m)||U.setState({selectedKeys:[m]})},300)},close:function(){d.prototype.close.apply(this,arguments),U.getState().setSearch("")}})),r(document).ajaxComplete((h,m,b)=>{try{b.data.indexOf("action=delete-post")>-1&&(a!==null&&U.getState().updateAttachmentCount(),a=null)}catch{a=null}}),plupload&&plupload.addFileFilter("mime_types",function(h,m,b){h.length&&!h.regexp.test(m.name)?(m.name==="desktop.ini"||m.name===".DS_Store"||this.trigger("Error",{code:plupload.FILE_EXTENSION_ERROR,message:plupload.translate("File extension error."),file:m}),b(!1)):b(!0)})}},G6=(e,n=2)=>{if(e===0)return"0 Bytes";const r=1024,t=n<0?0:n,o=["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"],a=Math.floor(Math.log(e)/Math.log(r));return parseFloat((e/Math.pow(r,a)).toFixed(t))+" "+o[a]},Y6=()=>{const{wp:e}=window;typeof e<"u"&&typeof e.Uploader=="function"&&jQuery.extend(e.Uploader.prototype,{uploader:function(){},progress:function(){},init:function(){this.uploader&&(this.uploader.bind("BeforeUpload",function(n,r){const t=U.getState();t.isUploading||U.setState({isUploading:!0,showUploader:!0}),typeof r.fbv>"u"&&(r.fbv=t.selectedKeys[0]);const o=n.settings.multipart_params,a=r.getNative();a?(a.path=r.getSource().relativePath?r.getSource().relativePath:"/"+r.getSource().name,a.path=a.path.split("/").slice(1,-1).join("/"),a.path.trim()!=""&&(r.path=a.path),o.fbv=r.fbv+(typeof r.path<"u"?"/"+r.path:"")):o.fbv=0}),this.uploader.bind("UploadProgress",function(n,r){U.getState().uploadedAttachments.length==0&&U.setState({bytesPerSec:n.total.bytesPerSec}),U.setState({currentUploadingFile:r,progressPercent:n.total.percent})}),this.uploader.bind("FilesAdded",function(n,r){const t=U.getState();let o=[...t.uploadingFiles];o=o.concat(r),U.setState({uploadingFiles:o});const a=o.reduce((s,i)=>s+i.size,0);U.setState({totalUploadingSize:G6(a,2)}),r.forEach(function(s){s.fbv=t.selectedKeys[0]})}),this.uploader.bind("FileUploaded",function(n,r,t){const a=[...U.getState().uploadedAttachments];a.push(r.id),U.setState({uploadedAttachments:a})}),this.uploader.bind("UploadComplete",function(){const n=U.getState();n.resetUploader();const r=jQuery(this.settings.container).find(".attachments-browser");n.getTreeData().then(t=>{qr(t);const o=r.data("backboneView");window.mlaModal?o.$el.find("#mla-search-submit").click():o.collection.props.set({ignore:+new Date})})}))}}),jQuery("body.wp-admin").hasClass("media-new-php")&&typeof window.uploader<"u"&&typeof window.uploader=="object"&&window.uploader.bind("BeforeUpload",function(n){const r=n.settings.multipart_params;r.fbv=jQuery("select.fbv").val()})},{fbv_data:qs}=window;class Z6{constructor(){this.isActivated()&&this.init()}isActivated(){const n=document.body.classList;return n.contains("eml-grid")&&n.contains("upload-php")&&qs.is_upload_screen==="1"&&!!qs.user_settings.SHOW_BREAD_CRUMB}init(){setTimeout(()=>{jQuery(document).on("filebird-breadcrumb-mounted",(n,r)=>{jQuery(".attachments-wrapper").prepend(r)})},0)}}const X6=()=>{new Z6},q6=()=>{const[e,n]=c.useState("none"),[r,t]=c.useState(""),[o,a]=c.useState(!1),[s,i]=c.useState(!1),d=()=>jQuery('[data-plugin*="filebird.php"] span.deactivate a').attr("href"),l=()=>{window.open("https://ninjateam.org/support/","_blank")},p=()=>{const h=d();window.location.href=h},f=()=>{const h=atob("aHR0cHM6Ly9wcmV2aWV3Lm5pbmphdGVhbS5vcmcvZmlsZWJpcmQvd3AtanNvbi9maWxlYmlyZC92NC9hZGRGZWVkYmFjaw=="),m=d();let b="";["found_better_plugin","no_features","other"].includes(e)&&(b=r),a(!0),jQuery.ajax({method:"POST",contentType:"application/json",url:h,beforeSend:()=>{},data:JSON.stringify({key:e,feed:b}),dataType:"json",cache:!1}).done(function(y){a(!1),window.location.href=m}).fail(function(y){a(!1),console.log("Feedback can't submit"),console.log(y.responseText),window.location.href=m})},v=()=>{jQuery("#fbv-feedback").hide(),jQuery(".filebird-app-wrapper").removeClass("zIndex")};return u("div",{id:"#fbv-feedback",children:R("div",{className:"fbv-feedback-dialog-wrapper",children:[u("div",{className:"inner--fbv-fd-wrapper",children:R("div",{className:"fbv-dialog-card feedback_custom",children:[R("div",{className:"fbv-dialog-card-content",children:[R("div",{className:"fbv-dialog-header",children:[u("i",{className:"njn-i feedback-icon",children:u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 640 512",children:R("g",{children:[u("path",{fill:"currentColor",d:"M640 192v288a32 32 0 0 1-32 32H32a32 32 0 0 1-32-32V192a32 32 0 0 1 32-32h171.06L331 288H104a8 8 0 0 0-8 8v16a8 8 0 0 0 8 8h259l64 64H104a8 8 0 0 0-8 8v16a8 8 0 0 0 8 8h456a16 16 0 0 0 16-16v-74.46a64 64 0 0 0-18.74-45.26L437 160h171a32 32 0 0 1 32 32z",className:"fill-secondary"}),u("path",{fill:"currentColor",d:"M534.64 302.91L303.18 71.47l-71.7 71.7 231.39 231.45a32 32 0 0 0 22.64 9.38H528a16 16 0 0 0 16-16v-42.46a32 32 0 0 0-9.36-22.63zM238.78 7a24.1 24.1 0 0 0-33.9 0L167 44.87a24 24 0 0 0 0 33.8l41.9 41.9 71.7-71.8z",className:"fill-primary"})]})})}),u("span",{className:"fbv-in_title",children:P("quick_feedback")})]}),u("div",{className:"fbv-dialog-body",children:R("div",{className:"fbv-in_content",children:[u("div",{className:"__e01",children:u("span",{children:P("deactivate_sadly")})}),Object.entries(P("feedback")).map(([h,m])=>R("div",{children:[u("input",{id:h,type:"radio",value:h,name:"reasons",checked:e===h,onChange:b=>{n(b.target.value),t(""),i(!0)}}),u("label",{htmlFor:h,children:m}),e==="filebird_pro"&&h==="filebird_pro"&&u("div",{className:"_reason_form",children:P("thank_you_so_much")}),e==="no_features"&&h==="no_features"&&u("div",{className:"_reason_form",children:u("textarea",{value:r,onChange:b=>t(b.target.value),rows:3,placeholder:P("which_features")})}),e==="not_working"&&h==="not_working"&&u("div",{className:"_reason_form",children:u("span",{dangerouslySetInnerHTML:{__html:P("not_working_support")}})}),e==="found_better_plugin"&&h==="found_better_plugin"&&u("div",{className:"_reason_form",children:u("textarea",{value:r,onChange:b=>t(b.target.value),rows:3,placeholder:P("found_better_plugin_placeholder")})}),e==="not_know_using"&&h==="not_know_using"&&u("div",{className:"_reason_form",children:u("span",{dangerouslySetInnerHTML:{__html:P("not_know_using_document")}})}),e==="other"&&h==="other"&&u("div",{className:"_reason_form",children:u("textarea",{value:r,onChange:b=>t(b.target.value),rows:3,placeholder:P("other_placeholder")})})]},h))]})}),R("div",{className:"fbv-dialog-footer",children:[u("div",{className:"left-side"}),R("div",{className:"right-side",children:[u("button",{className:"button",onClick:l,children:P("contact_support")}),s&&u("button",{className:`button button-primary ${o?"updating-message":""}`,onClick:f,children:P("deactivate")}),!s&&u("button",{className:"button",onClick:p,children:P("skip_and_deactivate")})]})]})]}),u("button",{className:"dialog-close-btn",type:"button",onClick:v,children:u("span",{className:"njn-i _Close",children:u("svg",{viewBox:"0 0 320 512",children:u("path",{fill:"currentColor",d:"M193.94 256L296.5 153.44l21.15-21.15c3.12-3.12 3.12-8.19 0-11.31l-22.63-22.63c-3.12-3.12-8.19-3.12-11.31 0L160 222.06 36.29 98.34c-3.12-3.12-8.19-3.12-11.31 0L2.34 120.97c-3.12 3.12-3.12 8.19 0 11.31L126.06 256 2.34 379.71c-3.12 3.12-3.12 8.19 0 11.31l22.63 22.63c3.12 3.12 8.19 3.12 11.31 0L160 289.94 262.56 392.5l21.15 21.15c3.12 3.12 8.19 3.12 11.31 0l22.63-22.63c3.12-3.12 3.12-8.19 0-11.31L193.94 256z"})})})})]})}),u("div",{className:"fbv-dialog-overlay",onClick:v})]})})};function Q6(){if(jQuery("#fbv-feedback").length){jQuery('[data-plugin*="filebird.php"] span.deactivate a').on("click",function(n){n.preventDefault(),jQuery("#fbv-feedback").show()});const e=document.getElementById("fbv-feedback");an.createRoot(e).render(u(Ve.StrictMode,{children:u(q6,{})}))}}function J6(){const e=document.querySelector("body.media-new-php #fbv-folder-selector");e&&an.createRoot(e).render(u(Ve.StrictMode,{children:u(za,{fetchOnLoad:!0})}))}window.wp.domReady(()=>{const e=ju(),n=Wu();j6(),Y6(),Q6(),J6(),Bu(),X6(),setTimeout(()=>{if(n===Vu.MEDIA_PAGE)switch(e){case"grid":case"":Xd.done(r=>{Oi(()=>u(Xr,{attachmentsBrowser:r})),fc(r)});break;case"list":Mt.registerMediaList(),Oi(()=>u(Xr,{}));break}},0)});
