function H1(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var Hw=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ca(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Vw(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}),n}var uf={exports:{}},Zi={},cf={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ho=Symbol.for("react.element"),V1=Symbol.for("react.portal"),U1=Symbol.for("react.fragment"),B1=Symbol.for("react.strict_mode"),W1=Symbol.for("react.profiler"),Q1=Symbol.for("react.provider"),K1=Symbol.for("react.context"),G1=Symbol.for("react.forward_ref"),Z1=Symbol.for("react.suspense"),Y1=Symbol.for("react.memo"),X1=Symbol.for("react.lazy"),_u=Symbol.iterator;function J1(e){return e===null||typeof e!="object"?null:(e=_u&&e[_u]||e["@@iterator"],typeof e=="function"?e:null)}var ff={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},df=Object.assign,pf={};function fr(e,t,n){this.props=e,this.context=t,this.refs=pf,this.updater=n||ff}fr.prototype.isReactComponent={};fr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};fr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function mf(){}mf.prototype=fr.prototype;function fa(e,t,n){this.props=e,this.context=t,this.refs=pf,this.updater=n||ff}var da=fa.prototype=new mf;da.constructor=fa;df(da,fr.prototype);da.isPureReactComponent=!0;var Eu=Array.isArray,hf=Object.prototype.hasOwnProperty,pa={current:null},vf={key:!0,ref:!0,__self:!0,__source:!0};function gf(e,t,n){var r,o={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)hf.call(t,r)&&!vf.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:ho,type:e,key:i,ref:l,props:o,_owner:pa.current}}function q1(e,t){return{$$typeof:ho,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ma(e){return typeof e=="object"&&e!==null&&e.$$typeof===ho}function e0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ku=/\/+/g;function Sl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?e0(""+e.key):t.toString(36)}function Zo(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case ho:case V1:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+Sl(l,0):r,Eu(o)?(n="",e!=null&&(n=e.replace(ku,"$&/")+"/"),Zo(o,t,n,"",function(u){return u})):o!=null&&(ma(o)&&(o=q1(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(ku,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",Eu(e))for(var s=0;s<e.length;s++){i=e[s];var a=r+Sl(i,s);l+=Zo(i,t,n,a,o)}else if(a=J1(e),typeof a=="function")for(e=a.call(e),s=0;!(i=e.next()).done;)i=i.value,a=r+Sl(i,s++),l+=Zo(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function ko(e,t,n){if(e==null)return e;var r=[],o=0;return Zo(e,r,"","",function(i){return t.call(n,i,o++)}),r}function t0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ye={current:null},Yo={transition:null},n0={ReactCurrentDispatcher:ye,ReactCurrentBatchConfig:Yo,ReactCurrentOwner:pa};F.Children={map:ko,forEach:function(e,t,n){ko(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ko(e,function(){t++}),t},toArray:function(e){return ko(e,function(t){return t})||[]},only:function(e){if(!ma(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};F.Component=fr;F.Fragment=U1;F.Profiler=W1;F.PureComponent=fa;F.StrictMode=B1;F.Suspense=Z1;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=n0;F.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=df({},e.props),o=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=pa.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)hf.call(t,a)&&!vf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:ho,type:e.type,key:o,ref:i,props:r,_owner:l}};F.createContext=function(e){return e={$$typeof:K1,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Q1,_context:e},e.Consumer=e};F.createElement=gf;F.createFactory=function(e){var t=gf.bind(null,e);return t.type=e,t};F.createRef=function(){return{current:null}};F.forwardRef=function(e){return{$$typeof:G1,render:e}};F.isValidElement=ma;F.lazy=function(e){return{$$typeof:X1,_payload:{_status:-1,_result:e},_init:t0}};F.memo=function(e,t){return{$$typeof:Y1,type:e,compare:t===void 0?null:t}};F.startTransition=function(e){var t=Yo.transition;Yo.transition={};try{e()}finally{Yo.transition=t}};F.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};F.useCallback=function(e,t){return ye.current.useCallback(e,t)};F.useContext=function(e){return ye.current.useContext(e)};F.useDebugValue=function(){};F.useDeferredValue=function(e){return ye.current.useDeferredValue(e)};F.useEffect=function(e,t){return ye.current.useEffect(e,t)};F.useId=function(){return ye.current.useId()};F.useImperativeHandle=function(e,t,n){return ye.current.useImperativeHandle(e,t,n)};F.useInsertionEffect=function(e,t){return ye.current.useInsertionEffect(e,t)};F.useLayoutEffect=function(e,t){return ye.current.useLayoutEffect(e,t)};F.useMemo=function(e,t){return ye.current.useMemo(e,t)};F.useReducer=function(e,t,n){return ye.current.useReducer(e,t,n)};F.useRef=function(e){return ye.current.useRef(e)};F.useState=function(e){return ye.current.useState(e)};F.useSyncExternalStore=function(e,t,n){return ye.current.useSyncExternalStore(e,t,n)};F.useTransition=function(){return ye.current.useTransition()};F.version="18.2.0";cf.exports=F;var g=cf.exports;const ha=ca(g),r0=H1({__proto__:null,default:ha},[g]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var o0=g,i0=Symbol.for("react.element"),l0=Symbol.for("react.fragment"),s0=Object.prototype.hasOwnProperty,a0=o0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u0={key:!0,ref:!0,__self:!0,__source:!0};function yf(e,t,n){var r,o={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)s0.call(t,r)&&!u0.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:i0,type:e,key:i,ref:l,props:o,_owner:a0.current}}Zi.Fragment=l0;Zi.jsx=yf;Zi.jsxs=yf;uf.exports=Zi;var va=uf.exports;const ga=va.Fragment,$=va.jsx,vn=va.jsxs;var pi={},wf={exports:{}},ze={},xf={exports:{}},Cf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,A){var L=P.length;P.push(A);e:for(;0<L;){var H=L-1>>>1,W=P[H];if(0<o(W,A))P[H]=A,P[L]=W,L=H;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var A=P[0],L=P.pop();if(L!==A){P[0]=L;e:for(var H=0,W=P.length,nn=W>>>1;H<nn;){var st=2*(H+1)-1,Pt=P[st],xe=st+1,rn=P[xe];if(0>o(Pt,L))xe<W&&0>o(rn,Pt)?(P[H]=rn,P[xe]=L,H=xe):(P[H]=Pt,P[st]=L,H=st);else if(xe<W&&0>o(rn,L))P[H]=rn,P[xe]=L,H=xe;else break e}}return A}function o(P,A){var L=P.sortIndex-A.sortIndex;return L!==0?L:P.id-A.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],c=1,d=null,m=3,h=!1,w=!1,y=!1,C=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,f=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(P){for(var A=n(u);A!==null;){if(A.callback===null)r(u);else if(A.startTime<=P)r(u),A.sortIndex=A.expirationTime,t(a,A);else break;A=n(u)}}function x(P){if(y=!1,v(P),!w)if(n(a)!==null)w=!0,N(S);else{var A=n(u);A!==null&&he(x,A.startTime-P)}}function S(P,A){w=!1,y&&(y=!1,p(k),k=-1),h=!0;var L=m;try{for(v(A),d=n(a);d!==null&&(!(d.expirationTime>A)||P&&!z());){var H=d.callback;if(typeof H=="function"){d.callback=null,m=d.priorityLevel;var W=H(d.expirationTime<=A);A=e.unstable_now(),typeof W=="function"?d.callback=W:d===n(a)&&r(a),v(A)}else r(a);d=n(a)}if(d!==null)var nn=!0;else{var st=n(u);st!==null&&he(x,st.startTime-A),nn=!1}return nn}finally{d=null,m=L,h=!1}}var _=!1,b=null,k=-1,M=5,T=-1;function z(){return!(e.unstable_now()-T<M)}function R(){if(b!==null){var P=e.unstable_now();T=P;var A=!0;try{A=b(!0,P)}finally{A?U():(_=!1,b=null)}}else _=!1}var U;if(typeof f=="function")U=function(){f(R)};else if(typeof MessageChannel<"u"){var I=new MessageChannel,se=I.port2;I.port1.onmessage=R,U=function(){se.postMessage(null)}}else U=function(){C(R,0)};function N(P){b=P,_||(_=!0,U())}function he(P,A){k=C(function(){P(e.unstable_now())},A)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){w||h||(w=!0,N(S))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(P){switch(m){case 1:case 2:case 3:var A=3;break;default:A=m}var L=m;m=A;try{return P()}finally{m=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,A){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var L=m;m=P;try{return A()}finally{m=L}},e.unstable_scheduleCallback=function(P,A,L){var H=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?H+L:H):L=H,P){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=L+W,P={id:c++,callback:A,priorityLevel:P,startTime:L,expirationTime:W,sortIndex:-1},L>H?(P.sortIndex=L,t(u,P),n(a)===null&&P===n(u)&&(y?(p(k),k=-1):y=!0,he(x,L-H))):(P.sortIndex=W,t(a,P),w||h||(w=!0,N(S))),P},e.unstable_shouldYield=z,e.unstable_wrapCallback=function(P){var A=m;return function(){var L=m;m=A;try{return P.apply(this,arguments)}finally{m=L}}}})(Cf);xf.exports=Cf;var c0=xf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sf=g,Me=c0;function E(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var _f=new Set,Wr={};function bn(e,t){Jn(e,t),Jn(e+"Capture",t)}function Jn(e,t){for(Wr[e]=t,e=0;e<t.length;e++)_f.add(t[e])}var vt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Jl=Object.prototype.hasOwnProperty,f0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,bu={},Pu={};function d0(e){return Jl.call(Pu,e)?!0:Jl.call(bu,e)?!1:f0.test(e)?Pu[e]=!0:(bu[e]=!0,!1)}function p0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function m0(e,t,n,r){if(t===null||typeof t>"u"||p0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function we(e,t,n,r,o,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new we(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new we(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new we(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new we(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new we(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new we(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new we(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new we(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new we(e,5,!1,e.toLowerCase(),null,!1,!1)});var ya=/[\-:]([a-z])/g;function wa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ya,wa);ce[t]=new we(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ya,wa);ce[t]=new we(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ya,wa);ce[t]=new we(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new we("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!0,!0)});function xa(e,t,n,r){var o=ce.hasOwnProperty(t)?ce[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(m0(t,n,o,r)&&(n=null),r||o===null?d0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var kt=Sf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,bo=Symbol.for("react.element"),Rn=Symbol.for("react.portal"),Ln=Symbol.for("react.fragment"),Ca=Symbol.for("react.strict_mode"),ql=Symbol.for("react.profiler"),Ef=Symbol.for("react.provider"),kf=Symbol.for("react.context"),Sa=Symbol.for("react.forward_ref"),es=Symbol.for("react.suspense"),ts=Symbol.for("react.suspense_list"),_a=Symbol.for("react.memo"),At=Symbol.for("react.lazy"),bf=Symbol.for("react.offscreen"),$u=Symbol.iterator;function Cr(e){return e===null||typeof e!="object"?null:(e=$u&&e[$u]||e["@@iterator"],typeof e=="function"?e:null)}var J=Object.assign,_l;function Mr(e){if(_l===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);_l=t&&t[1]||""}return`
`+_l+e}var El=!1;function kl(e,t){if(!e||El)return"";El=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),l=o.length-1,s=i.length-1;1<=l&&0<=s&&o[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(o[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||o[l]!==i[s]){var a=`
`+o[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{El=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Mr(e):""}function h0(e){switch(e.tag){case 5:return Mr(e.type);case 16:return Mr("Lazy");case 13:return Mr("Suspense");case 19:return Mr("SuspenseList");case 0:case 2:case 15:return e=kl(e.type,!1),e;case 11:return e=kl(e.type.render,!1),e;case 1:return e=kl(e.type,!0),e;default:return""}}function ns(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ln:return"Fragment";case Rn:return"Portal";case ql:return"Profiler";case Ca:return"StrictMode";case es:return"Suspense";case ts:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case kf:return(e.displayName||"Context")+".Consumer";case Ef:return(e._context.displayName||"Context")+".Provider";case Sa:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case _a:return t=e.displayName||null,t!==null?t:ns(e.type)||"Memo";case At:t=e._payload,e=e._init;try{return ns(e(t))}catch{}}return null}function v0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ns(t);case 8:return t===Ca?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Kt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Pf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function g0(e){var t=Pf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Po(e){e._valueTracker||(e._valueTracker=g0(e))}function $f(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Pf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function mi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function rs(e,t){var n=t.checked;return J({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ou(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Kt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Of(e,t){t=t.checked,t!=null&&xa(e,"checked",t,!1)}function os(e,t){Of(e,t);var n=Kt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?is(e,t.type,n):t.hasOwnProperty("defaultValue")&&is(e,t.type,Kt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Tu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function is(e,t,n){(t!=="number"||mi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Rr=Array.isArray;function Wn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Kt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ls(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(E(91));return J({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Au(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(E(92));if(Rr(n)){if(1<n.length)throw Error(E(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Kt(n)}}function Tf(e,t){var n=Kt(t.value),r=Kt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Mu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Af(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ss(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Af(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var $o,Mf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for($o=$o||document.createElement("div"),$o.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=$o.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Qr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Dr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},y0=["Webkit","ms","Moz","O"];Object.keys(Dr).forEach(function(e){y0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Dr[t]=Dr[e]})});function Rf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Dr.hasOwnProperty(e)&&Dr[e]?(""+t).trim():t+"px"}function Lf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Rf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var w0=J({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function as(e,t){if(t){if(w0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(E(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(E(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(E(61))}if(t.style!=null&&typeof t.style!="object")throw Error(E(62))}}function us(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var cs=null;function Ea(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var fs=null,Qn=null,Kn=null;function Ru(e){if(e=yo(e)){if(typeof fs!="function")throw Error(E(280));var t=e.stateNode;t&&(t=el(t),fs(e.stateNode,e.type,t))}}function zf(e){Qn?Kn?Kn.push(e):Kn=[e]:Qn=e}function Df(){if(Qn){var e=Qn,t=Kn;if(Kn=Qn=null,Ru(e),t)for(e=0;e<t.length;e++)Ru(t[e])}}function Nf(e,t){return e(t)}function Ff(){}var bl=!1;function If(e,t,n){if(bl)return e(t,n);bl=!0;try{return Nf(e,t,n)}finally{bl=!1,(Qn!==null||Kn!==null)&&(Ff(),Df())}}function Kr(e,t){var n=e.stateNode;if(n===null)return null;var r=el(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(E(231,t,typeof n));return n}var ds=!1;if(vt)try{var Sr={};Object.defineProperty(Sr,"passive",{get:function(){ds=!0}}),window.addEventListener("test",Sr,Sr),window.removeEventListener("test",Sr,Sr)}catch{ds=!1}function x0(e,t,n,r,o,i,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Nr=!1,hi=null,vi=!1,ps=null,C0={onError:function(e){Nr=!0,hi=e}};function S0(e,t,n,r,o,i,l,s,a){Nr=!1,hi=null,x0.apply(C0,arguments)}function _0(e,t,n,r,o,i,l,s,a){if(S0.apply(this,arguments),Nr){if(Nr){var u=hi;Nr=!1,hi=null}else throw Error(E(198));vi||(vi=!0,ps=u)}}function Pn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function jf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Lu(e){if(Pn(e)!==e)throw Error(E(188))}function E0(e){var t=e.alternate;if(!t){if(t=Pn(e),t===null)throw Error(E(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Lu(o),e;if(i===r)return Lu(o),t;i=i.sibling}throw Error(E(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(E(189))}}if(n.alternate!==r)throw Error(E(190))}if(n.tag!==3)throw Error(E(188));return n.stateNode.current===n?e:t}function Hf(e){return e=E0(e),e!==null?Vf(e):null}function Vf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Vf(e);if(t!==null)return t;e=e.sibling}return null}var Uf=Me.unstable_scheduleCallback,zu=Me.unstable_cancelCallback,k0=Me.unstable_shouldYield,b0=Me.unstable_requestPaint,ee=Me.unstable_now,P0=Me.unstable_getCurrentPriorityLevel,ka=Me.unstable_ImmediatePriority,Bf=Me.unstable_UserBlockingPriority,gi=Me.unstable_NormalPriority,$0=Me.unstable_LowPriority,Wf=Me.unstable_IdlePriority,Yi=null,ot=null;function O0(e){if(ot&&typeof ot.onCommitFiberRoot=="function")try{ot.onCommitFiberRoot(Yi,e,void 0,(e.current.flags&128)===128)}catch{}}var Je=Math.clz32?Math.clz32:M0,T0=Math.log,A0=Math.LN2;function M0(e){return e>>>=0,e===0?32:31-(T0(e)/A0|0)|0}var Oo=64,To=4194304;function Lr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function yi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~o;s!==0?r=Lr(s):(i&=l,i!==0&&(r=Lr(i)))}else l=n&~o,l!==0?r=Lr(l):i!==0&&(r=Lr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Je(t),o=1<<n,r|=e[n],t&=~o;return r}function R0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function L0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-Je(i),s=1<<l,a=o[l];a===-1?(!(s&n)||s&r)&&(o[l]=R0(s,t)):a<=t&&(e.expiredLanes|=s),i&=~s}}function ms(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Qf(){var e=Oo;return Oo<<=1,!(Oo&4194240)&&(Oo=64),e}function Pl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Je(t),e[t]=n}function z0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Je(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function ba(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Je(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var V=0;function Kf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Gf,Pa,Zf,Yf,Xf,hs=!1,Ao=[],It=null,jt=null,Ht=null,Gr=new Map,Zr=new Map,Lt=[],D0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Du(e,t){switch(e){case"focusin":case"focusout":It=null;break;case"dragenter":case"dragleave":jt=null;break;case"mouseover":case"mouseout":Ht=null;break;case"pointerover":case"pointerout":Gr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zr.delete(t.pointerId)}}function _r(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=yo(t),t!==null&&Pa(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function N0(e,t,n,r,o){switch(t){case"focusin":return It=_r(It,e,t,n,r,o),!0;case"dragenter":return jt=_r(jt,e,t,n,r,o),!0;case"mouseover":return Ht=_r(Ht,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Gr.set(i,_r(Gr.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Zr.set(i,_r(Zr.get(i)||null,e,t,n,r,o)),!0}return!1}function Jf(e){var t=cn(e.target);if(t!==null){var n=Pn(t);if(n!==null){if(t=n.tag,t===13){if(t=jf(n),t!==null){e.blockedOn=t,Xf(e.priority,function(){Zf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Xo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=vs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);cs=r,n.target.dispatchEvent(r),cs=null}else return t=yo(n),t!==null&&Pa(t),e.blockedOn=n,!1;t.shift()}return!0}function Nu(e,t,n){Xo(e)&&n.delete(t)}function F0(){hs=!1,It!==null&&Xo(It)&&(It=null),jt!==null&&Xo(jt)&&(jt=null),Ht!==null&&Xo(Ht)&&(Ht=null),Gr.forEach(Nu),Zr.forEach(Nu)}function Er(e,t){e.blockedOn===t&&(e.blockedOn=null,hs||(hs=!0,Me.unstable_scheduleCallback(Me.unstable_NormalPriority,F0)))}function Yr(e){function t(o){return Er(o,e)}if(0<Ao.length){Er(Ao[0],e);for(var n=1;n<Ao.length;n++){var r=Ao[n];r.blockedOn===e&&(r.blockedOn=null)}}for(It!==null&&Er(It,e),jt!==null&&Er(jt,e),Ht!==null&&Er(Ht,e),Gr.forEach(t),Zr.forEach(t),n=0;n<Lt.length;n++)r=Lt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&(n=Lt[0],n.blockedOn===null);)Jf(n),n.blockedOn===null&&Lt.shift()}var Gn=kt.ReactCurrentBatchConfig,wi=!0;function I0(e,t,n,r){var o=V,i=Gn.transition;Gn.transition=null;try{V=1,$a(e,t,n,r)}finally{V=o,Gn.transition=i}}function j0(e,t,n,r){var o=V,i=Gn.transition;Gn.transition=null;try{V=4,$a(e,t,n,r)}finally{V=o,Gn.transition=i}}function $a(e,t,n,r){if(wi){var o=vs(e,t,n,r);if(o===null)Nl(e,t,r,xi,n),Du(e,r);else if(N0(o,e,t,n,r))r.stopPropagation();else if(Du(e,r),t&4&&-1<D0.indexOf(e)){for(;o!==null;){var i=yo(o);if(i!==null&&Gf(i),i=vs(e,t,n,r),i===null&&Nl(e,t,r,xi,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Nl(e,t,r,null,n)}}var xi=null;function vs(e,t,n,r){if(xi=null,e=Ea(r),e=cn(e),e!==null)if(t=Pn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=jf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return xi=e,null}function qf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(P0()){case ka:return 1;case Bf:return 4;case gi:case $0:return 16;case Wf:return 536870912;default:return 16}default:return 16}}var Nt=null,Oa=null,Jo=null;function ed(){if(Jo)return Jo;var e,t=Oa,n=t.length,r,o="value"in Nt?Nt.value:Nt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[i-r];r++);return Jo=o.slice(e,1<r?1-r:void 0)}function qo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Mo(){return!0}function Fu(){return!1}function De(e){function t(n,r,o,i,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Mo:Fu,this.isPropagationStopped=Fu,this}return J(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Mo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Mo)},persist:function(){},isPersistent:Mo}),t}var dr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ta=De(dr),go=J({},dr,{view:0,detail:0}),H0=De(go),$l,Ol,kr,Xi=J({},go,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Aa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==kr&&(kr&&e.type==="mousemove"?($l=e.screenX-kr.screenX,Ol=e.screenY-kr.screenY):Ol=$l=0,kr=e),$l)},movementY:function(e){return"movementY"in e?e.movementY:Ol}}),Iu=De(Xi),V0=J({},Xi,{dataTransfer:0}),U0=De(V0),B0=J({},go,{relatedTarget:0}),Tl=De(B0),W0=J({},dr,{animationName:0,elapsedTime:0,pseudoElement:0}),Q0=De(W0),K0=J({},dr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),G0=De(K0),Z0=J({},dr,{data:0}),ju=De(Z0),Y0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},X0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},J0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function q0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=J0[e])?!!t[e]:!1}function Aa(){return q0}var em=J({},go,{key:function(e){if(e.key){var t=Y0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=qo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?X0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Aa,charCode:function(e){return e.type==="keypress"?qo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?qo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),tm=De(em),nm=J({},Xi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Hu=De(nm),rm=J({},go,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Aa}),om=De(rm),im=J({},dr,{propertyName:0,elapsedTime:0,pseudoElement:0}),lm=De(im),sm=J({},Xi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),am=De(sm),um=[9,13,27,32],Ma=vt&&"CompositionEvent"in window,Fr=null;vt&&"documentMode"in document&&(Fr=document.documentMode);var cm=vt&&"TextEvent"in window&&!Fr,td=vt&&(!Ma||Fr&&8<Fr&&11>=Fr),Vu=String.fromCharCode(32),Uu=!1;function nd(e,t){switch(e){case"keyup":return um.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function rd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var zn=!1;function fm(e,t){switch(e){case"compositionend":return rd(t);case"keypress":return t.which!==32?null:(Uu=!0,Vu);case"textInput":return e=t.data,e===Vu&&Uu?null:e;default:return null}}function dm(e,t){if(zn)return e==="compositionend"||!Ma&&nd(e,t)?(e=ed(),Jo=Oa=Nt=null,zn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return td&&t.locale!=="ko"?null:t.data;default:return null}}var pm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!pm[e.type]:t==="textarea"}function od(e,t,n,r){zf(r),t=Ci(t,"onChange"),0<t.length&&(n=new Ta("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ir=null,Xr=null;function mm(e){hd(e,0)}function Ji(e){var t=Fn(e);if($f(t))return e}function hm(e,t){if(e==="change")return t}var id=!1;if(vt){var Al;if(vt){var Ml="oninput"in document;if(!Ml){var Wu=document.createElement("div");Wu.setAttribute("oninput","return;"),Ml=typeof Wu.oninput=="function"}Al=Ml}else Al=!1;id=Al&&(!document.documentMode||9<document.documentMode)}function Qu(){Ir&&(Ir.detachEvent("onpropertychange",ld),Xr=Ir=null)}function ld(e){if(e.propertyName==="value"&&Ji(Xr)){var t=[];od(t,Xr,e,Ea(e)),If(mm,t)}}function vm(e,t,n){e==="focusin"?(Qu(),Ir=t,Xr=n,Ir.attachEvent("onpropertychange",ld)):e==="focusout"&&Qu()}function gm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ji(Xr)}function ym(e,t){if(e==="click")return Ji(t)}function wm(e,t){if(e==="input"||e==="change")return Ji(t)}function xm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var et=typeof Object.is=="function"?Object.is:xm;function Jr(e,t){if(et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Jl.call(t,o)||!et(e[o],t[o]))return!1}return!0}function Ku(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Gu(e,t){var n=Ku(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ku(n)}}function sd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ad(){for(var e=window,t=mi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=mi(e.document)}return t}function Ra(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Cm(e){var t=ad(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&sd(n.ownerDocument.documentElement,n)){if(r!==null&&Ra(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Gu(n,i);var l=Gu(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Sm=vt&&"documentMode"in document&&11>=document.documentMode,Dn=null,gs=null,jr=null,ys=!1;function Zu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ys||Dn==null||Dn!==mi(r)||(r=Dn,"selectionStart"in r&&Ra(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),jr&&Jr(jr,r)||(jr=r,r=Ci(gs,"onSelect"),0<r.length&&(t=new Ta("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Dn)))}function Ro(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Nn={animationend:Ro("Animation","AnimationEnd"),animationiteration:Ro("Animation","AnimationIteration"),animationstart:Ro("Animation","AnimationStart"),transitionend:Ro("Transition","TransitionEnd")},Rl={},ud={};vt&&(ud=document.createElement("div").style,"AnimationEvent"in window||(delete Nn.animationend.animation,delete Nn.animationiteration.animation,delete Nn.animationstart.animation),"TransitionEvent"in window||delete Nn.transitionend.transition);function qi(e){if(Rl[e])return Rl[e];if(!Nn[e])return e;var t=Nn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ud)return Rl[e]=t[n];return e}var cd=qi("animationend"),fd=qi("animationiteration"),dd=qi("animationstart"),pd=qi("transitionend"),md=new Map,Yu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Jt(e,t){md.set(e,t),bn(t,[e])}for(var Ll=0;Ll<Yu.length;Ll++){var zl=Yu[Ll],_m=zl.toLowerCase(),Em=zl[0].toUpperCase()+zl.slice(1);Jt(_m,"on"+Em)}Jt(cd,"onAnimationEnd");Jt(fd,"onAnimationIteration");Jt(dd,"onAnimationStart");Jt("dblclick","onDoubleClick");Jt("focusin","onFocus");Jt("focusout","onBlur");Jt(pd,"onTransitionEnd");Jn("onMouseEnter",["mouseout","mouseover"]);Jn("onMouseLeave",["mouseout","mouseover"]);Jn("onPointerEnter",["pointerout","pointerover"]);Jn("onPointerLeave",["pointerout","pointerover"]);bn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));bn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));bn("onBeforeInput",["compositionend","keypress","textInput","paste"]);bn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));bn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));bn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),km=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function Xu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,_0(r,t,void 0,e),e.currentTarget=null}function hd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==i&&o.isPropagationStopped())break e;Xu(o,s,u),i=a}else for(l=0;l<r.length;l++){if(s=r[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==i&&o.isPropagationStopped())break e;Xu(o,s,u),i=a}}}if(vi)throw e=ps,vi=!1,ps=null,e}function K(e,t){var n=t[_s];n===void 0&&(n=t[_s]=new Set);var r=e+"__bubble";n.has(r)||(vd(t,e,2,!1),n.add(r))}function Dl(e,t,n){var r=0;t&&(r|=4),vd(n,e,r,t)}var Lo="_reactListening"+Math.random().toString(36).slice(2);function qr(e){if(!e[Lo]){e[Lo]=!0,_f.forEach(function(n){n!=="selectionchange"&&(km.has(n)||Dl(n,!1,e),Dl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Lo]||(t[Lo]=!0,Dl("selectionchange",!1,t))}}function vd(e,t,n,r){switch(qf(t)){case 1:var o=I0;break;case 4:o=j0;break;default:o=$a}n=o.bind(null,t,n,e),o=void 0,!ds||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Nl(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;l=l.return}for(;s!==null;){if(l=cn(s),l===null)return;if(a=l.tag,a===5||a===6){r=i=l;continue e}s=s.parentNode}}r=r.return}If(function(){var u=i,c=Ea(n),d=[];e:{var m=md.get(e);if(m!==void 0){var h=Ta,w=e;switch(e){case"keypress":if(qo(n)===0)break e;case"keydown":case"keyup":h=tm;break;case"focusin":w="focus",h=Tl;break;case"focusout":w="blur",h=Tl;break;case"beforeblur":case"afterblur":h=Tl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":h=Iu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":h=U0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":h=om;break;case cd:case fd:case dd:h=Q0;break;case pd:h=lm;break;case"scroll":h=H0;break;case"wheel":h=am;break;case"copy":case"cut":case"paste":h=G0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":h=Hu}var y=(t&4)!==0,C=!y&&e==="scroll",p=y?m!==null?m+"Capture":null:m;y=[];for(var f=u,v;f!==null;){v=f;var x=v.stateNode;if(v.tag===5&&x!==null&&(v=x,p!==null&&(x=Kr(f,p),x!=null&&y.push(eo(f,x,v)))),C)break;f=f.return}0<y.length&&(m=new h(m,w,null,n,c),d.push({event:m,listeners:y}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",h=e==="mouseout"||e==="pointerout",m&&n!==cs&&(w=n.relatedTarget||n.fromElement)&&(cn(w)||w[gt]))break e;if((h||m)&&(m=c.window===c?c:(m=c.ownerDocument)?m.defaultView||m.parentWindow:window,h?(w=n.relatedTarget||n.toElement,h=u,w=w?cn(w):null,w!==null&&(C=Pn(w),w!==C||w.tag!==5&&w.tag!==6)&&(w=null)):(h=null,w=u),h!==w)){if(y=Iu,x="onMouseLeave",p="onMouseEnter",f="mouse",(e==="pointerout"||e==="pointerover")&&(y=Hu,x="onPointerLeave",p="onPointerEnter",f="pointer"),C=h==null?m:Fn(h),v=w==null?m:Fn(w),m=new y(x,f+"leave",h,n,c),m.target=C,m.relatedTarget=v,x=null,cn(c)===u&&(y=new y(p,f+"enter",w,n,c),y.target=v,y.relatedTarget=C,x=y),C=x,h&&w)t:{for(y=h,p=w,f=0,v=y;v;v=Tn(v))f++;for(v=0,x=p;x;x=Tn(x))v++;for(;0<f-v;)y=Tn(y),f--;for(;0<v-f;)p=Tn(p),v--;for(;f--;){if(y===p||p!==null&&y===p.alternate)break t;y=Tn(y),p=Tn(p)}y=null}else y=null;h!==null&&Ju(d,m,h,y,!1),w!==null&&C!==null&&Ju(d,C,w,y,!0)}}e:{if(m=u?Fn(u):window,h=m.nodeName&&m.nodeName.toLowerCase(),h==="select"||h==="input"&&m.type==="file")var S=hm;else if(Bu(m))if(id)S=wm;else{S=gm;var _=vm}else(h=m.nodeName)&&h.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(S=ym);if(S&&(S=S(e,u))){od(d,S,n,c);break e}_&&_(e,m,u),e==="focusout"&&(_=m._wrapperState)&&_.controlled&&m.type==="number"&&is(m,"number",m.value)}switch(_=u?Fn(u):window,e){case"focusin":(Bu(_)||_.contentEditable==="true")&&(Dn=_,gs=u,jr=null);break;case"focusout":jr=gs=Dn=null;break;case"mousedown":ys=!0;break;case"contextmenu":case"mouseup":case"dragend":ys=!1,Zu(d,n,c);break;case"selectionchange":if(Sm)break;case"keydown":case"keyup":Zu(d,n,c)}var b;if(Ma)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else zn?nd(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(td&&n.locale!=="ko"&&(zn||k!=="onCompositionStart"?k==="onCompositionEnd"&&zn&&(b=ed()):(Nt=c,Oa="value"in Nt?Nt.value:Nt.textContent,zn=!0)),_=Ci(u,k),0<_.length&&(k=new ju(k,e,null,n,c),d.push({event:k,listeners:_}),b?k.data=b:(b=rd(n),b!==null&&(k.data=b)))),(b=cm?fm(e,n):dm(e,n))&&(u=Ci(u,"onBeforeInput"),0<u.length&&(c=new ju("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=b))}hd(d,t)})}function eo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ci(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Kr(e,n),i!=null&&r.unshift(eo(e,i,o)),i=Kr(e,t),i!=null&&r.push(eo(e,i,o))),e=e.return}return r}function Tn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ju(e,t,n,r,o){for(var i=t._reactName,l=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,o?(a=Kr(n,i),a!=null&&l.unshift(eo(n,a,s))):o||(a=Kr(n,i),a!=null&&l.push(eo(n,a,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var bm=/\r\n?/g,Pm=/\u0000|\uFFFD/g;function qu(e){return(typeof e=="string"?e:""+e).replace(bm,`
`).replace(Pm,"")}function zo(e,t,n){if(t=qu(t),qu(e)!==t&&n)throw Error(E(425))}function Si(){}var ws=null,xs=null;function Cs(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ss=typeof setTimeout=="function"?setTimeout:void 0,$m=typeof clearTimeout=="function"?clearTimeout:void 0,ec=typeof Promise=="function"?Promise:void 0,Om=typeof queueMicrotask=="function"?queueMicrotask:typeof ec<"u"?function(e){return ec.resolve(null).then(e).catch(Tm)}:Ss;function Tm(e){setTimeout(function(){throw e})}function Fl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Yr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Yr(t)}function Vt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function tc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var pr=Math.random().toString(36).slice(2),rt="__reactFiber$"+pr,to="__reactProps$"+pr,gt="__reactContainer$"+pr,_s="__reactEvents$"+pr,Am="__reactListeners$"+pr,Mm="__reactHandles$"+pr;function cn(e){var t=e[rt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[gt]||n[rt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=tc(e);e!==null;){if(n=e[rt])return n;e=tc(e)}return t}e=n,n=e.parentNode}return null}function yo(e){return e=e[rt]||e[gt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Fn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(E(33))}function el(e){return e[to]||null}var Es=[],In=-1;function qt(e){return{current:e}}function G(e){0>In||(e.current=Es[In],Es[In]=null,In--)}function B(e,t){In++,Es[In]=e.current,e.current=t}var Gt={},me=qt(Gt),_e=qt(!1),gn=Gt;function qn(e,t){var n=e.type.contextTypes;if(!n)return Gt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ee(e){return e=e.childContextTypes,e!=null}function _i(){G(_e),G(me)}function nc(e,t,n){if(me.current!==Gt)throw Error(E(168));B(me,t),B(_e,n)}function gd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(E(108,v0(e)||"Unknown",o));return J({},n,r)}function Ei(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Gt,gn=me.current,B(me,e),B(_e,_e.current),!0}function rc(e,t,n){var r=e.stateNode;if(!r)throw Error(E(169));n?(e=gd(e,t,gn),r.__reactInternalMemoizedMergedChildContext=e,G(_e),G(me),B(me,e)):G(_e),B(_e,n)}var ct=null,tl=!1,Il=!1;function yd(e){ct===null?ct=[e]:ct.push(e)}function Rm(e){tl=!0,yd(e)}function en(){if(!Il&&ct!==null){Il=!0;var e=0,t=V;try{var n=ct;for(V=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}ct=null,tl=!1}catch(o){throw ct!==null&&(ct=ct.slice(e+1)),Uf(ka,en),o}finally{V=t,Il=!1}}return null}var jn=[],Hn=0,ki=null,bi=0,Ne=[],Fe=0,yn=null,pt=1,mt="";function sn(e,t){jn[Hn++]=bi,jn[Hn++]=ki,ki=e,bi=t}function wd(e,t,n){Ne[Fe++]=pt,Ne[Fe++]=mt,Ne[Fe++]=yn,yn=e;var r=pt;e=mt;var o=32-Je(r)-1;r&=~(1<<o),n+=1;var i=32-Je(t)+o;if(30<i){var l=o-o%5;i=(r&(1<<l)-1).toString(32),r>>=l,o-=l,pt=1<<32-Je(t)+o|n<<o|r,mt=i+e}else pt=1<<i|n<<o|r,mt=e}function La(e){e.return!==null&&(sn(e,1),wd(e,1,0))}function za(e){for(;e===ki;)ki=jn[--Hn],jn[Hn]=null,bi=jn[--Hn],jn[Hn]=null;for(;e===yn;)yn=Ne[--Fe],Ne[Fe]=null,mt=Ne[--Fe],Ne[Fe]=null,pt=Ne[--Fe],Ne[Fe]=null}var Te=null,$e=null,Z=!1,Ye=null;function xd(e,t){var n=Ie(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function oc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Te=e,$e=Vt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Te=e,$e=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=yn!==null?{id:pt,overflow:mt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ie(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Te=e,$e=null,!0):!1;default:return!1}}function ks(e){return(e.mode&1)!==0&&(e.flags&128)===0}function bs(e){if(Z){var t=$e;if(t){var n=t;if(!oc(e,t)){if(ks(e))throw Error(E(418));t=Vt(n.nextSibling);var r=Te;t&&oc(e,t)?xd(r,n):(e.flags=e.flags&-4097|2,Z=!1,Te=e)}}else{if(ks(e))throw Error(E(418));e.flags=e.flags&-4097|2,Z=!1,Te=e}}}function ic(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Te=e}function Do(e){if(e!==Te)return!1;if(!Z)return ic(e),Z=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Cs(e.type,e.memoizedProps)),t&&(t=$e)){if(ks(e))throw Cd(),Error(E(418));for(;t;)xd(e,t),t=Vt(t.nextSibling)}if(ic(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(E(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){$e=Vt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}$e=null}}else $e=Te?Vt(e.stateNode.nextSibling):null;return!0}function Cd(){for(var e=$e;e;)e=Vt(e.nextSibling)}function er(){$e=Te=null,Z=!1}function Da(e){Ye===null?Ye=[e]:Ye.push(e)}var Lm=kt.ReactCurrentBatchConfig;function Ge(e,t){if(e&&e.defaultProps){t=J({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}var Pi=qt(null),$i=null,Vn=null,Na=null;function Fa(){Na=Vn=$i=null}function Ia(e){var t=Pi.current;G(Pi),e._currentValue=t}function Ps(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Zn(e,t){$i=e,Na=Vn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Se=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(Na!==e)if(e={context:e,memoizedValue:t,next:null},Vn===null){if($i===null)throw Error(E(308));Vn=e,$i.dependencies={lanes:0,firstContext:e}}else Vn=Vn.next=e;return t}var fn=null;function ja(e){fn===null?fn=[e]:fn.push(e)}function Sd(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,ja(t)):(n.next=o.next,o.next=n),t.interleaved=n,yt(e,r)}function yt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Mt=!1;function Ha(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function _d(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ht(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ut(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,j&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,yt(e,n)}return o=r.interleaved,o===null?(t.next=t,ja(r)):(t.next=o.next,o.next=t),r.interleaved=t,yt(e,n)}function ei(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ba(e,n)}}function lc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Oi(e,t,n,r){var o=e.updateQueue;Mt=!1;var i=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var c=e.alternate;c!==null&&(c=c.updateQueue,s=c.lastBaseUpdate,s!==l&&(s===null?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=a))}if(i!==null){var d=o.baseState;l=0,c=u=a=null,s=i;do{var m=s.lane,h=s.eventTime;if((r&m)===m){c!==null&&(c=c.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var w=e,y=s;switch(m=t,h=n,y.tag){case 1:if(w=y.payload,typeof w=="function"){d=w.call(h,d,m);break e}d=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=y.payload,m=typeof w=="function"?w.call(h,d,m):w,m==null)break e;d=J({},d,m);break e;case 2:Mt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,m=o.effects,m===null?o.effects=[s]:m.push(s))}else h={eventTime:h,lane:m,tag:s.tag,payload:s.payload,callback:s.callback,next:null},c===null?(u=c=h,a=d):c=c.next=h,l|=m;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;m=s,s=m.next,m.next=null,o.lastBaseUpdate=m,o.shared.pending=null}}while(1);if(c===null&&(a=d),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);xn|=l,e.lanes=l,e.memoizedState=d}}function sc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(E(191,o));o.call(r)}}}var Ed=new Sf.Component().refs;function $s(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:J({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var nl={isMounted:function(e){return(e=e._reactInternals)?Pn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ge(),o=Wt(e),i=ht(r,o);i.payload=t,n!=null&&(i.callback=n),t=Ut(e,i,o),t!==null&&(qe(t,e,o,r),ei(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ge(),o=Wt(e),i=ht(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Ut(e,i,o),t!==null&&(qe(t,e,o,r),ei(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ge(),r=Wt(e),o=ht(n,r);o.tag=2,t!=null&&(o.callback=t),t=Ut(e,o,r),t!==null&&(qe(t,e,r,n),ei(t,e,r))}};function ac(e,t,n,r,o,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!Jr(n,r)||!Jr(o,i):!0}function kd(e,t,n){var r=!1,o=Gt,i=t.contextType;return typeof i=="object"&&i!==null?i=He(i):(o=Ee(t)?gn:me.current,r=t.contextTypes,i=(r=r!=null)?qn(e,o):Gt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=nl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function uc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&nl.enqueueReplaceState(t,t.state,null)}function Os(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=Ed,Ha(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=He(i):(i=Ee(t)?gn:me.current,o.context=qn(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&($s(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&nl.enqueueReplaceState(o,o.state,null),Oi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function br(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(E(309));var r=n.stateNode}if(!r)throw Error(E(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=o.refs;s===Ed&&(s=o.refs={}),l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(E(284));if(!n._owner)throw Error(E(290,e))}return e}function No(e,t){throw e=Object.prototype.toString.call(t),Error(E(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function cc(e){var t=e._init;return t(e._payload)}function bd(e){function t(p,f){if(e){var v=p.deletions;v===null?(p.deletions=[f],p.flags|=16):v.push(f)}}function n(p,f){if(!e)return null;for(;f!==null;)t(p,f),f=f.sibling;return null}function r(p,f){for(p=new Map;f!==null;)f.key!==null?p.set(f.key,f):p.set(f.index,f),f=f.sibling;return p}function o(p,f){return p=Qt(p,f),p.index=0,p.sibling=null,p}function i(p,f,v){return p.index=v,e?(v=p.alternate,v!==null?(v=v.index,v<f?(p.flags|=2,f):v):(p.flags|=2,f)):(p.flags|=1048576,f)}function l(p){return e&&p.alternate===null&&(p.flags|=2),p}function s(p,f,v,x){return f===null||f.tag!==6?(f=Ql(v,p.mode,x),f.return=p,f):(f=o(f,v),f.return=p,f)}function a(p,f,v,x){var S=v.type;return S===Ln?c(p,f,v.props.children,x,v.key):f!==null&&(f.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===At&&cc(S)===f.type)?(x=o(f,v.props),x.ref=br(p,f,v),x.return=p,x):(x=li(v.type,v.key,v.props,null,p.mode,x),x.ref=br(p,f,v),x.return=p,x)}function u(p,f,v,x){return f===null||f.tag!==4||f.stateNode.containerInfo!==v.containerInfo||f.stateNode.implementation!==v.implementation?(f=Kl(v,p.mode,x),f.return=p,f):(f=o(f,v.children||[]),f.return=p,f)}function c(p,f,v,x,S){return f===null||f.tag!==7?(f=hn(v,p.mode,x,S),f.return=p,f):(f=o(f,v),f.return=p,f)}function d(p,f,v){if(typeof f=="string"&&f!==""||typeof f=="number")return f=Ql(""+f,p.mode,v),f.return=p,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case bo:return v=li(f.type,f.key,f.props,null,p.mode,v),v.ref=br(p,null,f),v.return=p,v;case Rn:return f=Kl(f,p.mode,v),f.return=p,f;case At:var x=f._init;return d(p,x(f._payload),v)}if(Rr(f)||Cr(f))return f=hn(f,p.mode,v,null),f.return=p,f;No(p,f)}return null}function m(p,f,v,x){var S=f!==null?f.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return S!==null?null:s(p,f,""+v,x);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case bo:return v.key===S?a(p,f,v,x):null;case Rn:return v.key===S?u(p,f,v,x):null;case At:return S=v._init,m(p,f,S(v._payload),x)}if(Rr(v)||Cr(v))return S!==null?null:c(p,f,v,x,null);No(p,v)}return null}function h(p,f,v,x,S){if(typeof x=="string"&&x!==""||typeof x=="number")return p=p.get(v)||null,s(f,p,""+x,S);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case bo:return p=p.get(x.key===null?v:x.key)||null,a(f,p,x,S);case Rn:return p=p.get(x.key===null?v:x.key)||null,u(f,p,x,S);case At:var _=x._init;return h(p,f,v,_(x._payload),S)}if(Rr(x)||Cr(x))return p=p.get(v)||null,c(f,p,x,S,null);No(f,x)}return null}function w(p,f,v,x){for(var S=null,_=null,b=f,k=f=0,M=null;b!==null&&k<v.length;k++){b.index>k?(M=b,b=null):M=b.sibling;var T=m(p,b,v[k],x);if(T===null){b===null&&(b=M);break}e&&b&&T.alternate===null&&t(p,b),f=i(T,f,k),_===null?S=T:_.sibling=T,_=T,b=M}if(k===v.length)return n(p,b),Z&&sn(p,k),S;if(b===null){for(;k<v.length;k++)b=d(p,v[k],x),b!==null&&(f=i(b,f,k),_===null?S=b:_.sibling=b,_=b);return Z&&sn(p,k),S}for(b=r(p,b);k<v.length;k++)M=h(b,p,k,v[k],x),M!==null&&(e&&M.alternate!==null&&b.delete(M.key===null?k:M.key),f=i(M,f,k),_===null?S=M:_.sibling=M,_=M);return e&&b.forEach(function(z){return t(p,z)}),Z&&sn(p,k),S}function y(p,f,v,x){var S=Cr(v);if(typeof S!="function")throw Error(E(150));if(v=S.call(v),v==null)throw Error(E(151));for(var _=S=null,b=f,k=f=0,M=null,T=v.next();b!==null&&!T.done;k++,T=v.next()){b.index>k?(M=b,b=null):M=b.sibling;var z=m(p,b,T.value,x);if(z===null){b===null&&(b=M);break}e&&b&&z.alternate===null&&t(p,b),f=i(z,f,k),_===null?S=z:_.sibling=z,_=z,b=M}if(T.done)return n(p,b),Z&&sn(p,k),S;if(b===null){for(;!T.done;k++,T=v.next())T=d(p,T.value,x),T!==null&&(f=i(T,f,k),_===null?S=T:_.sibling=T,_=T);return Z&&sn(p,k),S}for(b=r(p,b);!T.done;k++,T=v.next())T=h(b,p,k,T.value,x),T!==null&&(e&&T.alternate!==null&&b.delete(T.key===null?k:T.key),f=i(T,f,k),_===null?S=T:_.sibling=T,_=T);return e&&b.forEach(function(R){return t(p,R)}),Z&&sn(p,k),S}function C(p,f,v,x){if(typeof v=="object"&&v!==null&&v.type===Ln&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case bo:e:{for(var S=v.key,_=f;_!==null;){if(_.key===S){if(S=v.type,S===Ln){if(_.tag===7){n(p,_.sibling),f=o(_,v.props.children),f.return=p,p=f;break e}}else if(_.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===At&&cc(S)===_.type){n(p,_.sibling),f=o(_,v.props),f.ref=br(p,_,v),f.return=p,p=f;break e}n(p,_);break}else t(p,_);_=_.sibling}v.type===Ln?(f=hn(v.props.children,p.mode,x,v.key),f.return=p,p=f):(x=li(v.type,v.key,v.props,null,p.mode,x),x.ref=br(p,f,v),x.return=p,p=x)}return l(p);case Rn:e:{for(_=v.key;f!==null;){if(f.key===_)if(f.tag===4&&f.stateNode.containerInfo===v.containerInfo&&f.stateNode.implementation===v.implementation){n(p,f.sibling),f=o(f,v.children||[]),f.return=p,p=f;break e}else{n(p,f);break}else t(p,f);f=f.sibling}f=Kl(v,p.mode,x),f.return=p,p=f}return l(p);case At:return _=v._init,C(p,f,_(v._payload),x)}if(Rr(v))return w(p,f,v,x);if(Cr(v))return y(p,f,v,x);No(p,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,f!==null&&f.tag===6?(n(p,f.sibling),f=o(f,v),f.return=p,p=f):(n(p,f),f=Ql(v,p.mode,x),f.return=p,p=f),l(p)):n(p,f)}return C}var tr=bd(!0),Pd=bd(!1),wo={},it=qt(wo),no=qt(wo),ro=qt(wo);function dn(e){if(e===wo)throw Error(E(174));return e}function Va(e,t){switch(B(ro,t),B(no,e),B(it,wo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ss(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ss(t,e)}G(it),B(it,t)}function nr(){G(it),G(no),G(ro)}function $d(e){dn(ro.current);var t=dn(it.current),n=ss(t,e.type);t!==n&&(B(no,e),B(it,n))}function Ua(e){no.current===e&&(G(it),G(no))}var Y=qt(0);function Ti(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var jl=[];function Ba(){for(var e=0;e<jl.length;e++)jl[e]._workInProgressVersionPrimary=null;jl.length=0}var ti=kt.ReactCurrentDispatcher,Hl=kt.ReactCurrentBatchConfig,wn=0,X=null,ne=null,oe=null,Ai=!1,Hr=!1,oo=0,zm=0;function fe(){throw Error(E(321))}function Wa(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!et(e[n],t[n]))return!1;return!0}function Qa(e,t,n,r,o,i){if(wn=i,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ti.current=e===null||e.memoizedState===null?Im:jm,e=n(r,o),Hr){i=0;do{if(Hr=!1,oo=0,25<=i)throw Error(E(301));i+=1,oe=ne=null,t.updateQueue=null,ti.current=Hm,e=n(r,o)}while(Hr)}if(ti.current=Mi,t=ne!==null&&ne.next!==null,wn=0,oe=ne=X=null,Ai=!1,t)throw Error(E(300));return e}function Ka(){var e=oo!==0;return oo=0,e}function nt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?X.memoizedState=oe=e:oe=oe.next=e,oe}function Ve(){if(ne===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=ne.next;var t=oe===null?X.memoizedState:oe.next;if(t!==null)oe=t,ne=e;else{if(e===null)throw Error(E(310));ne=e,e={memoizedState:ne.memoizedState,baseState:ne.baseState,baseQueue:ne.baseQueue,queue:ne.queue,next:null},oe===null?X.memoizedState=oe=e:oe=oe.next=e}return oe}function io(e,t){return typeof t=="function"?t(e):t}function Vl(e){var t=Ve(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=ne,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=l=null,a=null,u=i;do{var c=u.lane;if((wn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=d,l=r):a=a.next=d,X.lanes|=c,xn|=c}u=u.next}while(u!==null&&u!==i);a===null?l=r:a.next=s,et(r,t.memoizedState)||(Se=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,X.lanes|=i,xn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ul(e){var t=Ve(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do i=e(i,l.action),l=l.next;while(l!==o);et(i,t.memoizedState)||(Se=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Od(){}function Td(e,t){var n=X,r=Ve(),o=t(),i=!et(r.memoizedState,o);if(i&&(r.memoizedState=o,Se=!0),r=r.queue,Ga(Rd.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,lo(9,Md.bind(null,n,r,o,t),void 0,null),le===null)throw Error(E(349));wn&30||Ad(n,t,o)}return o}function Ad(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Md(e,t,n,r){t.value=n,t.getSnapshot=r,Ld(t)&&zd(e)}function Rd(e,t,n){return n(function(){Ld(t)&&zd(e)})}function Ld(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!et(e,n)}catch{return!0}}function zd(e){var t=yt(e,1);t!==null&&qe(t,e,1,-1)}function fc(e){var t=nt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:io,lastRenderedState:e},t.queue=e,e=e.dispatch=Fm.bind(null,X,e),[t.memoizedState,e]}function lo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Dd(){return Ve().memoizedState}function ni(e,t,n,r){var o=nt();X.flags|=e,o.memoizedState=lo(1|t,n,void 0,r===void 0?null:r)}function rl(e,t,n,r){var o=Ve();r=r===void 0?null:r;var i=void 0;if(ne!==null){var l=ne.memoizedState;if(i=l.destroy,r!==null&&Wa(r,l.deps)){o.memoizedState=lo(t,n,i,r);return}}X.flags|=e,o.memoizedState=lo(1|t,n,i,r)}function dc(e,t){return ni(8390656,8,e,t)}function Ga(e,t){return rl(2048,8,e,t)}function Nd(e,t){return rl(4,2,e,t)}function Fd(e,t){return rl(4,4,e,t)}function Id(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function jd(e,t,n){return n=n!=null?n.concat([e]):null,rl(4,4,Id.bind(null,t,e),n)}function Za(){}function Hd(e,t){var n=Ve();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Wa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vd(e,t){var n=Ve();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Wa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ud(e,t,n){return wn&21?(et(n,t)||(n=Qf(),X.lanes|=n,xn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Se=!0),e.memoizedState=n)}function Dm(e,t){var n=V;V=n!==0&&4>n?n:4,e(!0);var r=Hl.transition;Hl.transition={};try{e(!1),t()}finally{V=n,Hl.transition=r}}function Bd(){return Ve().memoizedState}function Nm(e,t,n){var r=Wt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Wd(e))Qd(t,n);else if(n=Sd(e,t,n,r),n!==null){var o=ge();qe(n,e,r,o),Kd(n,t,r)}}function Fm(e,t,n){var r=Wt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Wd(e))Qd(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,n);if(o.hasEagerState=!0,o.eagerState=s,et(s,l)){var a=t.interleaved;a===null?(o.next=o,ja(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Sd(e,t,o,r),n!==null&&(o=ge(),qe(n,e,r,o),Kd(n,t,r))}}function Wd(e){var t=e.alternate;return e===X||t!==null&&t===X}function Qd(e,t){Hr=Ai=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Kd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ba(e,n)}}var Mi={readContext:He,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},Im={readContext:He,useCallback:function(e,t){return nt().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:dc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ni(4194308,4,Id.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ni(4194308,4,e,t)},useInsertionEffect:function(e,t){return ni(4,2,e,t)},useMemo:function(e,t){var n=nt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=nt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Nm.bind(null,X,e),[r.memoizedState,e]},useRef:function(e){var t=nt();return e={current:e},t.memoizedState=e},useState:fc,useDebugValue:Za,useDeferredValue:function(e){return nt().memoizedState=e},useTransition:function(){var e=fc(!1),t=e[0];return e=Dm.bind(null,e[1]),nt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=X,o=nt();if(Z){if(n===void 0)throw Error(E(407));n=n()}else{if(n=t(),le===null)throw Error(E(349));wn&30||Ad(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,dc(Rd.bind(null,r,i,e),[e]),r.flags|=2048,lo(9,Md.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=nt(),t=le.identifierPrefix;if(Z){var n=mt,r=pt;n=(r&~(1<<32-Je(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=oo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=zm++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},jm={readContext:He,useCallback:Hd,useContext:He,useEffect:Ga,useImperativeHandle:jd,useInsertionEffect:Nd,useLayoutEffect:Fd,useMemo:Vd,useReducer:Vl,useRef:Dd,useState:function(){return Vl(io)},useDebugValue:Za,useDeferredValue:function(e){var t=Ve();return Ud(t,ne.memoizedState,e)},useTransition:function(){var e=Vl(io)[0],t=Ve().memoizedState;return[e,t]},useMutableSource:Od,useSyncExternalStore:Td,useId:Bd,unstable_isNewReconciler:!1},Hm={readContext:He,useCallback:Hd,useContext:He,useEffect:Ga,useImperativeHandle:jd,useInsertionEffect:Nd,useLayoutEffect:Fd,useMemo:Vd,useReducer:Ul,useRef:Dd,useState:function(){return Ul(io)},useDebugValue:Za,useDeferredValue:function(e){var t=Ve();return ne===null?t.memoizedState=e:Ud(t,ne.memoizedState,e)},useTransition:function(){var e=Ul(io)[0],t=Ve().memoizedState;return[e,t]},useMutableSource:Od,useSyncExternalStore:Td,useId:Bd,unstable_isNewReconciler:!1};function rr(e,t){try{var n="",r=t;do n+=h0(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Bl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ts(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Vm=typeof WeakMap=="function"?WeakMap:Map;function Gd(e,t,n){n=ht(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Li||(Li=!0,js=r),Ts(e,t)},n}function Zd(e,t,n){n=ht(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ts(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Ts(e,t),typeof r!="function"&&(Bt===null?Bt=new Set([this]):Bt.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function pc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Vm;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=nh.bind(null,e,t,n),t.then(e,e))}function mc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function hc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ht(-1,1),t.tag=2,Ut(n,t,1))),n.lanes|=1),e)}var Um=kt.ReactCurrentOwner,Se=!1;function ve(e,t,n,r){t.child=e===null?Pd(t,null,n,r):tr(t,e.child,n,r)}function vc(e,t,n,r,o){n=n.render;var i=t.ref;return Zn(t,o),r=Qa(e,t,n,r,i,o),n=Ka(),e!==null&&!Se?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,wt(e,t,o)):(Z&&n&&La(t),t.flags|=1,ve(e,t,r,o),t.child)}function gc(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!ru(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Yd(e,t,i,r,o)):(e=li(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:Jr,n(l,r)&&e.ref===t.ref)return wt(e,t,o)}return t.flags|=1,e=Qt(i,r),e.ref=t.ref,e.return=t,t.child=e}function Yd(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Jr(i,r)&&e.ref===t.ref)if(Se=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Se=!0);else return t.lanes=e.lanes,wt(e,t,o)}return As(e,t,n,r,o)}function Xd(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(Bn,be),be|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(Bn,be),be|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,B(Bn,be),be|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,B(Bn,be),be|=r;return ve(e,t,o,n),t.child}function Jd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function As(e,t,n,r,o){var i=Ee(n)?gn:me.current;return i=qn(t,i),Zn(t,o),n=Qa(e,t,n,r,i,o),r=Ka(),e!==null&&!Se?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,wt(e,t,o)):(Z&&r&&La(t),t.flags|=1,ve(e,t,n,o),t.child)}function yc(e,t,n,r,o){if(Ee(n)){var i=!0;Ei(t)}else i=!1;if(Zn(t,o),t.stateNode===null)ri(e,t),kd(t,n,r),Os(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=He(u):(u=Ee(n)?gn:me.current,u=qn(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof l.getSnapshotBeforeUpdate=="function";d||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||a!==u)&&uc(t,l,r,u),Mt=!1;var m=t.memoizedState;l.state=m,Oi(t,r,l,o),a=t.memoizedState,s!==r||m!==a||_e.current||Mt?(typeof c=="function"&&($s(t,n,c,r),a=t.memoizedState),(s=Mt||ac(t,n,s,r,m,a,u))?(d||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,_d(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:Ge(t.type,s),l.props=u,d=t.pendingProps,m=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=He(a):(a=Ee(n)?gn:me.current,a=qn(t,a));var h=n.getDerivedStateFromProps;(c=typeof h=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==d||m!==a)&&uc(t,l,r,a),Mt=!1,m=t.memoizedState,l.state=m,Oi(t,r,l,o);var w=t.memoizedState;s!==d||m!==w||_e.current||Mt?(typeof h=="function"&&($s(t,n,h,r),w=t.memoizedState),(u=Mt||ac(t,n,u,r,m,w,a)||!1)?(c||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,w,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,w,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),l.props=r,l.state=w,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return Ms(e,t,n,r,i,o)}function Ms(e,t,n,r,o,i){Jd(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&rc(t,n,!1),wt(e,t,i);r=t.stateNode,Um.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=tr(t,e.child,null,i),t.child=tr(t,null,s,i)):ve(e,t,s,i),t.memoizedState=r.state,o&&rc(t,n,!0),t.child}function qd(e){var t=e.stateNode;t.pendingContext?nc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&nc(e,t.context,!1),Va(e,t.containerInfo)}function wc(e,t,n,r,o){return er(),Da(o),t.flags|=256,ve(e,t,n,r),t.child}var Rs={dehydrated:null,treeContext:null,retryLane:0};function Ls(e){return{baseLanes:e,cachePool:null,transitions:null}}function ep(e,t,n){var r=t.pendingProps,o=Y.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),B(Y,o&1),e===null)return bs(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=ll(l,r,0,null),e=hn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ls(n),t.memoizedState=Rs,e):Ya(t,l));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return Bm(e,t,l,r,s,o,n);if(i){i=r.fallback,l=t.mode,o=e.child,s=o.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Qt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=Qt(s,i):(i=hn(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?Ls(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=Rs,r}return i=e.child,e=i.sibling,r=Qt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ya(e,t){return t=ll({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Fo(e,t,n,r){return r!==null&&Da(r),tr(t,e.child,null,n),e=Ya(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Bm(e,t,n,r,o,i,l){if(n)return t.flags&256?(t.flags&=-257,r=Bl(Error(E(422))),Fo(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=ll({mode:"visible",children:r.children},o,0,null),i=hn(i,o,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&tr(t,e.child,null,l),t.child.memoizedState=Ls(l),t.memoizedState=Rs,i);if(!(t.mode&1))return Fo(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(E(419)),r=Bl(i,r,void 0),Fo(e,t,l,r)}if(s=(l&e.childLanes)!==0,Se||s){if(r=le,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,yt(e,o),qe(r,e,o,-1))}return nu(),r=Bl(Error(E(421))),Fo(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=rh.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,$e=Vt(o.nextSibling),Te=t,Z=!0,Ye=null,e!==null&&(Ne[Fe++]=pt,Ne[Fe++]=mt,Ne[Fe++]=yn,pt=e.id,mt=e.overflow,yn=t),t=Ya(t,r.children),t.flags|=4096,t)}function xc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ps(e.return,t,n)}function Wl(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function tp(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(ve(e,t,r.children,n),r=Y.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xc(e,n,t);else if(e.tag===19)xc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(Y,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ti(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Wl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ti(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Wl(t,!0,n,null,i);break;case"together":Wl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ri(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function wt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),xn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(E(153));if(t.child!==null){for(e=t.child,n=Qt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Qt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Wm(e,t,n){switch(t.tag){case 3:qd(t),er();break;case 5:$d(t);break;case 1:Ee(t.type)&&Ei(t);break;case 4:Va(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;B(Pi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(Y,Y.current&1),t.flags|=128,null):n&t.child.childLanes?ep(e,t,n):(B(Y,Y.current&1),e=wt(e,t,n),e!==null?e.sibling:null);B(Y,Y.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return tp(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),B(Y,Y.current),r)break;return null;case 22:case 23:return t.lanes=0,Xd(e,t,n)}return wt(e,t,n)}var np,zs,rp,op;np=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};zs=function(){};rp=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,dn(it.current);var i=null;switch(n){case"input":o=rs(e,o),r=rs(e,r),i=[];break;case"select":o=J({},o,{value:void 0}),r=J({},r,{value:void 0}),i=[];break;case"textarea":o=ls(e,o),r=ls(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Si)}as(n,r);var l;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Wr.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Wr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&K("scroll",e),i||s===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};op=function(e,t,n,r){n!==r&&(t.flags|=4)};function Pr(e,t){if(!Z)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qm(e,t,n){var r=t.pendingProps;switch(za(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Ee(t.type)&&_i(),de(t),null;case 3:return r=t.stateNode,nr(),G(_e),G(me),Ba(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Do(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ye!==null&&(Us(Ye),Ye=null))),zs(e,t),de(t),null;case 5:Ua(t);var o=dn(ro.current);if(n=t.type,e!==null&&t.stateNode!=null)rp(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(E(166));return de(t),null}if(e=dn(it.current),Do(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[rt]=t,r[to]=i,e=(t.mode&1)!==0,n){case"dialog":K("cancel",r),K("close",r);break;case"iframe":case"object":case"embed":K("load",r);break;case"video":case"audio":for(o=0;o<zr.length;o++)K(zr[o],r);break;case"source":K("error",r);break;case"img":case"image":case"link":K("error",r),K("load",r);break;case"details":K("toggle",r);break;case"input":Ou(r,i),K("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},K("invalid",r);break;case"textarea":Au(r,i),K("invalid",r)}as(n,i),o=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&zo(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&zo(r.textContent,s,e),o=["children",""+s]):Wr.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&K("scroll",r)}switch(n){case"input":Po(r),Tu(r,i,!0);break;case"textarea":Po(r),Mu(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Si)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Af(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[rt]=t,e[to]=r,np(e,t,!1,!1),t.stateNode=e;e:{switch(l=us(n,r),n){case"dialog":K("cancel",e),K("close",e),o=r;break;case"iframe":case"object":case"embed":K("load",e),o=r;break;case"video":case"audio":for(o=0;o<zr.length;o++)K(zr[o],e);o=r;break;case"source":K("error",e),o=r;break;case"img":case"image":case"link":K("error",e),K("load",e),o=r;break;case"details":K("toggle",e),o=r;break;case"input":Ou(e,r),o=rs(e,r),K("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=J({},r,{value:void 0}),K("invalid",e);break;case"textarea":Au(e,r),o=ls(e,r),K("invalid",e);break;default:o=r}as(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="style"?Lf(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Mf(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Qr(e,a):typeof a=="number"&&Qr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Wr.hasOwnProperty(i)?a!=null&&i==="onScroll"&&K("scroll",e):a!=null&&xa(e,i,a,l))}switch(n){case"input":Po(e),Tu(e,r,!1);break;case"textarea":Po(e),Mu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Kt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Wn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Wn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Si)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)op(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(E(166));if(n=dn(ro.current),dn(it.current),Do(t)){if(r=t.stateNode,n=t.memoizedProps,r[rt]=t,(i=r.nodeValue!==n)&&(e=Te,e!==null))switch(e.tag){case 3:zo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&zo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[rt]=t,t.stateNode=r}return de(t),null;case 13:if(G(Y),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Z&&$e!==null&&t.mode&1&&!(t.flags&128))Cd(),er(),t.flags|=98560,i=!1;else if(i=Do(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(E(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(E(317));i[rt]=t}else er(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),i=!1}else Ye!==null&&(Us(Ye),Ye=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Y.current&1?re===0&&(re=3):nu())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return nr(),zs(e,t),e===null&&qr(t.stateNode.containerInfo),de(t),null;case 10:return Ia(t.type._context),de(t),null;case 17:return Ee(t.type)&&_i(),de(t),null;case 19:if(G(Y),i=t.memoizedState,i===null)return de(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)Pr(i,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Ti(e),l!==null){for(t.flags|=128,Pr(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(Y,Y.current&1|2),t.child}e=e.sibling}i.tail!==null&&ee()>or&&(t.flags|=128,r=!0,Pr(i,!1),t.lanes=4194304)}else{if(!r)if(e=Ti(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Pr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!Z)return de(t),null}else 2*ee()-i.renderingStartTime>or&&n!==1073741824&&(t.flags|=128,r=!0,Pr(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ee(),t.sibling=null,n=Y.current,B(Y,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return tu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?be&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(E(156,t.tag))}function Km(e,t){switch(za(t),t.tag){case 1:return Ee(t.type)&&_i(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return nr(),G(_e),G(me),Ba(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ua(t),null;case 13:if(G(Y),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(E(340));er()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(Y),null;case 4:return nr(),null;case 10:return Ia(t.type._context),null;case 22:case 23:return tu(),null;case 24:return null;default:return null}}var Io=!1,pe=!1,Gm=typeof WeakSet=="function"?WeakSet:Set,O=null;function Un(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){q(e,t,r)}else n.current=null}function Ds(e,t,n){try{n()}catch(r){q(e,t,r)}}var Cc=!1;function Zm(e,t){if(ws=wi,e=ad(),Ra(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,s=-1,a=-1,u=0,c=0,d=e,m=null;t:for(;;){for(var h;d!==n||o!==0&&d.nodeType!==3||(s=l+o),d!==i||r!==0&&d.nodeType!==3||(a=l+r),d.nodeType===3&&(l+=d.nodeValue.length),(h=d.firstChild)!==null;)m=d,d=h;for(;;){if(d===e)break t;if(m===n&&++u===o&&(s=l),m===i&&++c===r&&(a=l),(h=d.nextSibling)!==null)break;d=m,m=d.parentNode}d=h}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(xs={focusedElem:e,selectionRange:n},wi=!1,O=t;O!==null;)if(t=O,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,O=e;else for(;O!==null;){t=O;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var y=w.memoizedProps,C=w.memoizedState,p=t.stateNode,f=p.getSnapshotBeforeUpdate(t.elementType===t.type?y:Ge(t.type,y),C);p.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(E(163))}}catch(x){q(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,O=e;break}O=t.return}return w=Cc,Cc=!1,w}function Vr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Ds(t,n,i)}o=o.next}while(o!==r)}}function ol(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ns(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function ip(e){var t=e.alternate;t!==null&&(e.alternate=null,ip(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[rt],delete t[to],delete t[_s],delete t[Am],delete t[Mm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function lp(e){return e.tag===5||e.tag===3||e.tag===4}function Sc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||lp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Fs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Si));else if(r!==4&&(e=e.child,e!==null))for(Fs(e,t,n),e=e.sibling;e!==null;)Fs(e,t,n),e=e.sibling}function Is(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Is(e,t,n),e=e.sibling;e!==null;)Is(e,t,n),e=e.sibling}var ae=null,Ze=!1;function $t(e,t,n){for(n=n.child;n!==null;)sp(e,t,n),n=n.sibling}function sp(e,t,n){if(ot&&typeof ot.onCommitFiberUnmount=="function")try{ot.onCommitFiberUnmount(Yi,n)}catch{}switch(n.tag){case 5:pe||Un(n,t);case 6:var r=ae,o=Ze;ae=null,$t(e,t,n),ae=r,Ze=o,ae!==null&&(Ze?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Ze?(e=ae,n=n.stateNode,e.nodeType===8?Fl(e.parentNode,n):e.nodeType===1&&Fl(e,n),Yr(e)):Fl(ae,n.stateNode));break;case 4:r=ae,o=Ze,ae=n.stateNode.containerInfo,Ze=!0,$t(e,t,n),ae=r,Ze=o;break;case 0:case 11:case 14:case 15:if(!pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&Ds(n,t,l),o=o.next}while(o!==r)}$t(e,t,n);break;case 1:if(!pe&&(Un(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){q(n,t,s)}$t(e,t,n);break;case 21:$t(e,t,n);break;case 22:n.mode&1?(pe=(r=pe)||n.memoizedState!==null,$t(e,t,n),pe=r):$t(e,t,n);break;default:$t(e,t,n)}}function _c(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Gm),t.forEach(function(r){var o=oh.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function We(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:ae=s.stateNode,Ze=!1;break e;case 3:ae=s.stateNode.containerInfo,Ze=!0;break e;case 4:ae=s.stateNode.containerInfo,Ze=!0;break e}s=s.return}if(ae===null)throw Error(E(160));sp(i,l,o),ae=null,Ze=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){q(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)ap(t,e),t=t.sibling}function ap(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(We(t,e),tt(e),r&4){try{Vr(3,e,e.return),ol(3,e)}catch(y){q(e,e.return,y)}try{Vr(5,e,e.return)}catch(y){q(e,e.return,y)}}break;case 1:We(t,e),tt(e),r&512&&n!==null&&Un(n,n.return);break;case 5:if(We(t,e),tt(e),r&512&&n!==null&&Un(n,n.return),e.flags&32){var o=e.stateNode;try{Qr(o,"")}catch(y){q(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&Of(o,i),us(s,l);var u=us(s,i);for(l=0;l<a.length;l+=2){var c=a[l],d=a[l+1];c==="style"?Lf(o,d):c==="dangerouslySetInnerHTML"?Mf(o,d):c==="children"?Qr(o,d):xa(o,c,d,u)}switch(s){case"input":os(o,i);break;case"textarea":Tf(o,i);break;case"select":var m=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;h!=null?Wn(o,!!i.multiple,h,!1):m!==!!i.multiple&&(i.defaultValue!=null?Wn(o,!!i.multiple,i.defaultValue,!0):Wn(o,!!i.multiple,i.multiple?[]:"",!1))}o[to]=i}catch(y){q(e,e.return,y)}}break;case 6:if(We(t,e),tt(e),r&4){if(e.stateNode===null)throw Error(E(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){q(e,e.return,y)}}break;case 3:if(We(t,e),tt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Yr(t.containerInfo)}catch(y){q(e,e.return,y)}break;case 4:We(t,e),tt(e);break;case 13:We(t,e),tt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(qa=ee())),r&4&&_c(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(pe=(u=pe)||c,We(t,e),pe=u):We(t,e),tt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(O=e,c=e.child;c!==null;){for(d=O=c;O!==null;){switch(m=O,h=m.child,m.tag){case 0:case 11:case 14:case 15:Vr(4,m,m.return);break;case 1:Un(m,m.return);var w=m.stateNode;if(typeof w.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(y){q(r,n,y)}}break;case 5:Un(m,m.return);break;case 22:if(m.memoizedState!==null){kc(d);continue}}h!==null?(h.return=m,O=h):kc(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=d.stateNode,a=d.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=Rf("display",l))}catch(y){q(e,e.return,y)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(y){q(e,e.return,y)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:We(t,e),tt(e),r&4&&_c(e);break;case 21:break;default:We(t,e),tt(e)}}function tt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(lp(n)){var r=n;break e}n=n.return}throw Error(E(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Qr(o,""),r.flags&=-33);var i=Sc(e);Is(e,i,o);break;case 3:case 4:var l=r.stateNode.containerInfo,s=Sc(e);Fs(e,s,l);break;default:throw Error(E(161))}}catch(a){q(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ym(e,t,n){O=e,up(e)}function up(e,t,n){for(var r=(e.mode&1)!==0;O!==null;){var o=O,i=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||Io;if(!l){var s=o.alternate,a=s!==null&&s.memoizedState!==null||pe;s=Io;var u=pe;if(Io=l,(pe=a)&&!u)for(O=o;O!==null;)l=O,a=l.child,l.tag===22&&l.memoizedState!==null?bc(o):a!==null?(a.return=l,O=a):bc(o);for(;i!==null;)O=i,up(i),i=i.sibling;O=o,Io=s,pe=u}Ec(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,O=i):Ec(e)}}function Ec(e){for(;O!==null;){var t=O;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:pe||ol(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!pe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ge(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&sc(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}sc(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Yr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(E(163))}pe||t.flags&512&&Ns(t)}catch(m){q(t,t.return,m)}}if(t===e){O=null;break}if(n=t.sibling,n!==null){n.return=t.return,O=n;break}O=t.return}}function kc(e){for(;O!==null;){var t=O;if(t===e){O=null;break}var n=t.sibling;if(n!==null){n.return=t.return,O=n;break}O=t.return}}function bc(e){for(;O!==null;){var t=O;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ol(4,t)}catch(a){q(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){q(t,o,a)}}var i=t.return;try{Ns(t)}catch(a){q(t,i,a)}break;case 5:var l=t.return;try{Ns(t)}catch(a){q(t,l,a)}}}catch(a){q(t,t.return,a)}if(t===e){O=null;break}var s=t.sibling;if(s!==null){s.return=t.return,O=s;break}O=t.return}}var Xm=Math.ceil,Ri=kt.ReactCurrentDispatcher,Xa=kt.ReactCurrentOwner,je=kt.ReactCurrentBatchConfig,j=0,le=null,te=null,ue=0,be=0,Bn=qt(0),re=0,so=null,xn=0,il=0,Ja=0,Ur=null,Ce=null,qa=0,or=1/0,ut=null,Li=!1,js=null,Bt=null,jo=!1,Ft=null,zi=0,Br=0,Hs=null,oi=-1,ii=0;function ge(){return j&6?ee():oi!==-1?oi:oi=ee()}function Wt(e){return e.mode&1?j&2&&ue!==0?ue&-ue:Lm.transition!==null?(ii===0&&(ii=Qf()),ii):(e=V,e!==0||(e=window.event,e=e===void 0?16:qf(e.type)),e):1}function qe(e,t,n,r){if(50<Br)throw Br=0,Hs=null,Error(E(185));vo(e,n,r),(!(j&2)||e!==le)&&(e===le&&(!(j&2)&&(il|=n),re===4&&zt(e,ue)),ke(e,r),n===1&&j===0&&!(t.mode&1)&&(or=ee()+500,tl&&en()))}function ke(e,t){var n=e.callbackNode;L0(e,t);var r=yi(e,e===le?ue:0);if(r===0)n!==null&&zu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&zu(n),t===1)e.tag===0?Rm(Pc.bind(null,e)):yd(Pc.bind(null,e)),Om(function(){!(j&6)&&en()}),n=null;else{switch(Kf(r)){case 1:n=ka;break;case 4:n=Bf;break;case 16:n=gi;break;case 536870912:n=Wf;break;default:n=gi}n=gp(n,cp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function cp(e,t){if(oi=-1,ii=0,j&6)throw Error(E(327));var n=e.callbackNode;if(Yn()&&e.callbackNode!==n)return null;var r=yi(e,e===le?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Di(e,r);else{t=r;var o=j;j|=2;var i=dp();(le!==e||ue!==t)&&(ut=null,or=ee()+500,mn(e,t));do try{eh();break}catch(s){fp(e,s)}while(1);Fa(),Ri.current=i,j=o,te!==null?t=0:(le=null,ue=0,t=re)}if(t!==0){if(t===2&&(o=ms(e),o!==0&&(r=o,t=Vs(e,o))),t===1)throw n=so,mn(e,0),zt(e,r),ke(e,ee()),n;if(t===6)zt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Jm(o)&&(t=Di(e,r),t===2&&(i=ms(e),i!==0&&(r=i,t=Vs(e,i))),t===1))throw n=so,mn(e,0),zt(e,r),ke(e,ee()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(E(345));case 2:an(e,Ce,ut);break;case 3:if(zt(e,r),(r&130023424)===r&&(t=qa+500-ee(),10<t)){if(yi(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){ge(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Ss(an.bind(null,e,Ce,ut),t);break}an(e,Ce,ut);break;case 4:if(zt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-Je(r);i=1<<l,l=t[l],l>o&&(o=l),r&=~i}if(r=o,r=ee()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Xm(r/1960))-r,10<r){e.timeoutHandle=Ss(an.bind(null,e,Ce,ut),r);break}an(e,Ce,ut);break;case 5:an(e,Ce,ut);break;default:throw Error(E(329))}}}return ke(e,ee()),e.callbackNode===n?cp.bind(null,e):null}function Vs(e,t){var n=Ur;return e.current.memoizedState.isDehydrated&&(mn(e,t).flags|=256),e=Di(e,t),e!==2&&(t=Ce,Ce=n,t!==null&&Us(t)),e}function Us(e){Ce===null?Ce=e:Ce.push.apply(Ce,e)}function Jm(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!et(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function zt(e,t){for(t&=~Ja,t&=~il,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Je(t),r=1<<n;e[n]=-1,t&=~r}}function Pc(e){if(j&6)throw Error(E(327));Yn();var t=yi(e,0);if(!(t&1))return ke(e,ee()),null;var n=Di(e,t);if(e.tag!==0&&n===2){var r=ms(e);r!==0&&(t=r,n=Vs(e,r))}if(n===1)throw n=so,mn(e,0),zt(e,t),ke(e,ee()),n;if(n===6)throw Error(E(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,an(e,Ce,ut),ke(e,ee()),null}function eu(e,t){var n=j;j|=1;try{return e(t)}finally{j=n,j===0&&(or=ee()+500,tl&&en())}}function Cn(e){Ft!==null&&Ft.tag===0&&!(j&6)&&Yn();var t=j;j|=1;var n=je.transition,r=V;try{if(je.transition=null,V=1,e)return e()}finally{V=r,je.transition=n,j=t,!(j&6)&&en()}}function tu(){be=Bn.current,G(Bn)}function mn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,$m(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(za(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&_i();break;case 3:nr(),G(_e),G(me),Ba();break;case 5:Ua(r);break;case 4:nr();break;case 13:G(Y);break;case 19:G(Y);break;case 10:Ia(r.type._context);break;case 22:case 23:tu()}n=n.return}if(le=e,te=e=Qt(e.current,null),ue=be=t,re=0,so=null,Ja=il=xn=0,Ce=Ur=null,fn!==null){for(t=0;t<fn.length;t++)if(n=fn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=o,r.next=l}n.pending=r}fn=null}return e}function fp(e,t){do{var n=te;try{if(Fa(),ti.current=Mi,Ai){for(var r=X.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ai=!1}if(wn=0,oe=ne=X=null,Hr=!1,oo=0,Xa.current=null,n===null||n.return===null){re=1,so=t,te=null;break}e:{var i=e,l=n.return,s=n,a=t;if(t=ue,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=s,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var m=c.alternate;m?(c.updateQueue=m.updateQueue,c.memoizedState=m.memoizedState,c.lanes=m.lanes):(c.updateQueue=null,c.memoizedState=null)}var h=mc(l);if(h!==null){h.flags&=-257,hc(h,l,s,i,t),h.mode&1&&pc(i,u,t),t=h,a=u;var w=t.updateQueue;if(w===null){var y=new Set;y.add(a),t.updateQueue=y}else w.add(a);break e}else{if(!(t&1)){pc(i,u,t),nu();break e}a=Error(E(426))}}else if(Z&&s.mode&1){var C=mc(l);if(C!==null){!(C.flags&65536)&&(C.flags|=256),hc(C,l,s,i,t),Da(rr(a,s));break e}}i=a=rr(a,s),re!==4&&(re=2),Ur===null?Ur=[i]:Ur.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=Gd(i,a,t);lc(i,p);break e;case 1:s=a;var f=i.type,v=i.stateNode;if(!(i.flags&128)&&(typeof f.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(Bt===null||!Bt.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=Zd(i,s,t);lc(i,x);break e}}i=i.return}while(i!==null)}mp(n)}catch(S){t=S,te===n&&n!==null&&(te=n=n.return);continue}break}while(1)}function dp(){var e=Ri.current;return Ri.current=Mi,e===null?Mi:e}function nu(){(re===0||re===3||re===2)&&(re=4),le===null||!(xn&268435455)&&!(il&268435455)||zt(le,ue)}function Di(e,t){var n=j;j|=2;var r=dp();(le!==e||ue!==t)&&(ut=null,mn(e,t));do try{qm();break}catch(o){fp(e,o)}while(1);if(Fa(),j=n,Ri.current=r,te!==null)throw Error(E(261));return le=null,ue=0,re}function qm(){for(;te!==null;)pp(te)}function eh(){for(;te!==null&&!k0();)pp(te)}function pp(e){var t=vp(e.alternate,e,be);e.memoizedProps=e.pendingProps,t===null?mp(e):te=t,Xa.current=null}function mp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Km(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,te=null;return}}else if(n=Qm(n,t,be),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);re===0&&(re=5)}function an(e,t,n){var r=V,o=je.transition;try{je.transition=null,V=1,th(e,t,n,r)}finally{je.transition=o,V=r}return null}function th(e,t,n,r){do Yn();while(Ft!==null);if(j&6)throw Error(E(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(E(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(z0(e,i),e===le&&(te=le=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||jo||(jo=!0,gp(gi,function(){return Yn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=je.transition,je.transition=null;var l=V;V=1;var s=j;j|=4,Xa.current=null,Zm(e,n),ap(n,e),Cm(xs),wi=!!ws,xs=ws=null,e.current=n,Ym(n),b0(),j=s,V=l,je.transition=i}else e.current=n;if(jo&&(jo=!1,Ft=e,zi=o),i=e.pendingLanes,i===0&&(Bt=null),O0(n.stateNode),ke(e,ee()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Li)throw Li=!1,e=js,js=null,e;return zi&1&&e.tag!==0&&Yn(),i=e.pendingLanes,i&1?e===Hs?Br++:(Br=0,Hs=e):Br=0,en(),null}function Yn(){if(Ft!==null){var e=Kf(zi),t=je.transition,n=V;try{if(je.transition=null,V=16>e?16:e,Ft===null)var r=!1;else{if(e=Ft,Ft=null,zi=0,j&6)throw Error(E(331));var o=j;for(j|=4,O=e.current;O!==null;){var i=O,l=i.child;if(O.flags&16){var s=i.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(O=u;O!==null;){var c=O;switch(c.tag){case 0:case 11:case 15:Vr(8,c,i)}var d=c.child;if(d!==null)d.return=c,O=d;else for(;O!==null;){c=O;var m=c.sibling,h=c.return;if(ip(c),c===u){O=null;break}if(m!==null){m.return=h,O=m;break}O=h}}}var w=i.alternate;if(w!==null){var y=w.child;if(y!==null){w.child=null;do{var C=y.sibling;y.sibling=null,y=C}while(y!==null)}}O=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,O=l;else e:for(;O!==null;){if(i=O,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Vr(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,O=p;break e}O=i.return}}var f=e.current;for(O=f;O!==null;){l=O;var v=l.child;if(l.subtreeFlags&2064&&v!==null)v.return=l,O=v;else e:for(l=f;O!==null;){if(s=O,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:ol(9,s)}}catch(S){q(s,s.return,S)}if(s===l){O=null;break e}var x=s.sibling;if(x!==null){x.return=s.return,O=x;break e}O=s.return}}if(j=o,en(),ot&&typeof ot.onPostCommitFiberRoot=="function")try{ot.onPostCommitFiberRoot(Yi,e)}catch{}r=!0}return r}finally{V=n,je.transition=t}}return!1}function $c(e,t,n){t=rr(n,t),t=Gd(e,t,1),e=Ut(e,t,1),t=ge(),e!==null&&(vo(e,1,t),ke(e,t))}function q(e,t,n){if(e.tag===3)$c(e,e,n);else for(;t!==null;){if(t.tag===3){$c(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Bt===null||!Bt.has(r))){e=rr(n,e),e=Zd(t,e,1),t=Ut(t,e,1),e=ge(),t!==null&&(vo(t,1,e),ke(t,e));break}}t=t.return}}function nh(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ge(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(ue&n)===n&&(re===4||re===3&&(ue&130023424)===ue&&500>ee()-qa?mn(e,0):Ja|=n),ke(e,t)}function hp(e,t){t===0&&(e.mode&1?(t=To,To<<=1,!(To&130023424)&&(To=4194304)):t=1);var n=ge();e=yt(e,t),e!==null&&(vo(e,t,n),ke(e,n))}function rh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),hp(e,n)}function oh(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(E(314))}r!==null&&r.delete(t),hp(e,n)}var vp;vp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||_e.current)Se=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Se=!1,Wm(e,t,n);Se=!!(e.flags&131072)}else Se=!1,Z&&t.flags&1048576&&wd(t,bi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ri(e,t),e=t.pendingProps;var o=qn(t,me.current);Zn(t,n),o=Qa(null,t,r,e,o,n);var i=Ka();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ee(r)?(i=!0,Ei(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Ha(t),o.updater=nl,t.stateNode=o,o._reactInternals=t,Os(t,r,e,n),t=Ms(null,t,r,!0,i,n)):(t.tag=0,Z&&i&&La(t),ve(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ri(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=lh(r),e=Ge(r,e),o){case 0:t=As(null,t,r,e,n);break e;case 1:t=yc(null,t,r,e,n);break e;case 11:t=vc(null,t,r,e,n);break e;case 14:t=gc(null,t,r,Ge(r.type,e),n);break e}throw Error(E(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ge(r,o),As(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ge(r,o),yc(e,t,r,o,n);case 3:e:{if(qd(t),e===null)throw Error(E(387));r=t.pendingProps,i=t.memoizedState,o=i.element,_d(e,t),Oi(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=rr(Error(E(423)),t),t=wc(e,t,r,n,o);break e}else if(r!==o){o=rr(Error(E(424)),t),t=wc(e,t,r,n,o);break e}else for($e=Vt(t.stateNode.containerInfo.firstChild),Te=t,Z=!0,Ye=null,n=Pd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(er(),r===o){t=wt(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return $d(t),e===null&&bs(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,l=o.children,Cs(r,o)?l=null:i!==null&&Cs(r,i)&&(t.flags|=32),Jd(e,t),ve(e,t,l,n),t.child;case 6:return e===null&&bs(t),null;case 13:return ep(e,t,n);case 4:return Va(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=tr(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ge(r,o),vc(e,t,r,o,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,B(Pi,r._currentValue),r._currentValue=l,i!==null)if(et(i.value,l)){if(i.children===o.children&&!_e.current){t=wt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=ht(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Ps(i.return,n,t),s.lanes|=n;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(E(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),Ps(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}ve(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Zn(t,n),o=He(o),r=r(o),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,o=Ge(r,t.pendingProps),o=Ge(r.type,o),gc(e,t,r,o,n);case 15:return Yd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ge(r,o),ri(e,t),t.tag=1,Ee(r)?(e=!0,Ei(t)):e=!1,Zn(t,n),kd(t,r,o),Os(t,r,o,n),Ms(null,t,r,!0,e,n);case 19:return tp(e,t,n);case 22:return Xd(e,t,n)}throw Error(E(156,t.tag))};function gp(e,t){return Uf(e,t)}function ih(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ie(e,t,n,r){return new ih(e,t,n,r)}function ru(e){return e=e.prototype,!(!e||!e.isReactComponent)}function lh(e){if(typeof e=="function")return ru(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Sa)return 11;if(e===_a)return 14}return 2}function Qt(e,t){var n=e.alternate;return n===null?(n=Ie(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function li(e,t,n,r,o,i){var l=2;if(r=e,typeof e=="function")ru(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case Ln:return hn(n.children,o,i,t);case Ca:l=8,o|=8;break;case ql:return e=Ie(12,n,t,o|2),e.elementType=ql,e.lanes=i,e;case es:return e=Ie(13,n,t,o),e.elementType=es,e.lanes=i,e;case ts:return e=Ie(19,n,t,o),e.elementType=ts,e.lanes=i,e;case bf:return ll(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ef:l=10;break e;case kf:l=9;break e;case Sa:l=11;break e;case _a:l=14;break e;case At:l=16,r=null;break e}throw Error(E(130,e==null?e:typeof e,""))}return t=Ie(l,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function hn(e,t,n,r){return e=Ie(7,e,r,t),e.lanes=n,e}function ll(e,t,n,r){return e=Ie(22,e,r,t),e.elementType=bf,e.lanes=n,e.stateNode={isHidden:!1},e}function Ql(e,t,n){return e=Ie(6,e,null,t),e.lanes=n,e}function Kl(e,t,n){return t=Ie(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function sh(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Pl(0),this.expirationTimes=Pl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Pl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function ou(e,t,n,r,o,i,l,s,a){return e=new sh(e,t,n,s,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ie(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ha(i),e}function ah(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Rn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function yp(e){if(!e)return Gt;e=e._reactInternals;e:{if(Pn(e)!==e||e.tag!==1)throw Error(E(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ee(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(E(171))}if(e.tag===1){var n=e.type;if(Ee(n))return gd(e,n,t)}return t}function wp(e,t,n,r,o,i,l,s,a){return e=ou(n,r,!0,e,o,i,l,s,a),e.context=yp(null),n=e.current,r=ge(),o=Wt(n),i=ht(r,o),i.callback=t??null,Ut(n,i,o),e.current.lanes=o,vo(e,o,r),ke(e,r),e}function sl(e,t,n,r){var o=t.current,i=ge(),l=Wt(o);return n=yp(n),t.context===null?t.context=n:t.pendingContext=n,t=ht(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ut(o,t,l),e!==null&&(qe(e,o,l,i),ei(e,o,l)),l}function Ni(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Oc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function iu(e,t){Oc(e,t),(e=e.alternate)&&Oc(e,t)}function uh(){return null}var xp=typeof reportError=="function"?reportError:function(e){console.error(e)};function lu(e){this._internalRoot=e}al.prototype.render=lu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(E(409));sl(e,t,null,null)};al.prototype.unmount=lu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Cn(function(){sl(null,e,null,null)}),t[gt]=null}};function al(e){this._internalRoot=e}al.prototype.unstable_scheduleHydration=function(e){if(e){var t=Yf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&t!==0&&t<Lt[n].priority;n++);Lt.splice(n,0,e),n===0&&Jf(e)}};function su(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ul(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Tc(){}function ch(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Ni(l);i.call(u)}}var l=wp(t,r,e,0,null,!1,!1,"",Tc);return e._reactRootContainer=l,e[gt]=l.current,qr(e.nodeType===8?e.parentNode:e),Cn(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=Ni(a);s.call(u)}}var a=ou(e,0,!1,null,null,!1,!1,"",Tc);return e._reactRootContainer=a,e[gt]=a.current,qr(e.nodeType===8?e.parentNode:e),Cn(function(){sl(t,a,n,r)}),a}function cl(e,t,n,r,o){var i=n._reactRootContainer;if(i){var l=i;if(typeof o=="function"){var s=o;o=function(){var a=Ni(l);s.call(a)}}sl(t,l,e,o)}else l=ch(n,t,e,o,r);return Ni(l)}Gf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Lr(t.pendingLanes);n!==0&&(ba(t,n|1),ke(t,ee()),!(j&6)&&(or=ee()+500,en()))}break;case 13:Cn(function(){var r=yt(e,1);if(r!==null){var o=ge();qe(r,e,1,o)}}),iu(e,1)}};Pa=function(e){if(e.tag===13){var t=yt(e,134217728);if(t!==null){var n=ge();qe(t,e,134217728,n)}iu(e,134217728)}};Zf=function(e){if(e.tag===13){var t=Wt(e),n=yt(e,t);if(n!==null){var r=ge();qe(n,e,t,r)}iu(e,t)}};Yf=function(){return V};Xf=function(e,t){var n=V;try{return V=e,t()}finally{V=n}};fs=function(e,t,n){switch(t){case"input":if(os(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=el(r);if(!o)throw Error(E(90));$f(r),os(r,o)}}}break;case"textarea":Tf(e,n);break;case"select":t=n.value,t!=null&&Wn(e,!!n.multiple,t,!1)}};Nf=eu;Ff=Cn;var fh={usingClientEntryPoint:!1,Events:[yo,Fn,el,zf,Df,eu]},$r={findFiberByHostInstance:cn,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},dh={bundleType:$r.bundleType,version:$r.version,rendererPackageName:$r.rendererPackageName,rendererConfig:$r.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:kt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Hf(e),e===null?null:e.stateNode},findFiberByHostInstance:$r.findFiberByHostInstance||uh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ho=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ho.isDisabled&&Ho.supportsFiber)try{Yi=Ho.inject(dh),ot=Ho}catch{}}ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fh;ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!su(t))throw Error(E(200));return ah(e,t,null,n)};ze.createRoot=function(e,t){if(!su(e))throw Error(E(299));var n=!1,r="",o=xp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=ou(e,1,!1,null,null,n,!1,r,o),e[gt]=t.current,qr(e.nodeType===8?e.parentNode:e),new lu(t)};ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(E(188)):(e=Object.keys(e).join(","),Error(E(268,e)));return e=Hf(t),e=e===null?null:e.stateNode,e};ze.flushSync=function(e){return Cn(e)};ze.hydrate=function(e,t,n){if(!ul(t))throw Error(E(200));return cl(null,e,t,!0,n)};ze.hydrateRoot=function(e,t,n){if(!su(e))throw Error(E(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",l=xp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=wp(t,null,e,1,n??null,o,!1,i,l),e[gt]=t.current,qr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new al(t)};ze.render=function(e,t,n){if(!ul(t))throw Error(E(200));return cl(null,e,t,!1,n)};ze.unmountComponentAtNode=function(e){if(!ul(e))throw Error(E(40));return e._reactRootContainer?(Cn(function(){cl(null,null,e,!1,function(){e._reactRootContainer=null,e[gt]=null})}),!0):!1};ze.unstable_batchedUpdates=eu;ze.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ul(n))throw Error(E(200));if(e==null||e._reactInternals===void 0)throw Error(E(38));return cl(e,t,n,!1,r)};ze.version="18.2.0-next-9e3b772b8-20220608";function Cp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Cp)}catch(e){console.error(e)}}Cp(),wf.exports=ze;var mr=wf.exports;const ph=ca(mr);var Ac=mr;pi.createRoot=Ac.createRoot,pi.hydrateRoot=Ac.hydrateRoot;const Mc=e=>{let t;const n=new Set,r=(a,u)=>{const c=typeof a=="function"?a(t):a;if(!Object.is(c,t)){const d=t;t=u??typeof c!="object"?c:Object.assign({},t,c),n.forEach(m=>m(t,d))}},o=()=>t,s={setState:r,getState:o,subscribe:a=>(n.add(a),()=>n.delete(a)),destroy:()=>{n.clear()}};return t=e(r,o,s),s},mh=e=>e?Mc(e):Mc;var Sp={exports:{}},_p={},Ep={exports:{}},kp={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ir=g;function hh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var vh=typeof Object.is=="function"?Object.is:hh,gh=ir.useState,yh=ir.useEffect,wh=ir.useLayoutEffect,xh=ir.useDebugValue;function Ch(e,t){var n=t(),r=gh({inst:{value:n,getSnapshot:t}}),o=r[0].inst,i=r[1];return wh(function(){o.value=n,o.getSnapshot=t,Gl(o)&&i({inst:o})},[e,n,t]),yh(function(){return Gl(o)&&i({inst:o}),e(function(){Gl(o)&&i({inst:o})})},[e]),xh(n),n}function Gl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!vh(e,n)}catch{return!0}}function Sh(e,t){return t()}var _h=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Sh:Ch;kp.useSyncExternalStore=ir.useSyncExternalStore!==void 0?ir.useSyncExternalStore:_h;Ep.exports=kp;var Eh=Ep.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fl=g,kh=Eh;function bh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ph=typeof Object.is=="function"?Object.is:bh,$h=kh.useSyncExternalStore,Oh=fl.useRef,Th=fl.useEffect,Ah=fl.useMemo,Mh=fl.useDebugValue;_p.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var i=Oh(null);if(i.current===null){var l={hasValue:!1,value:null};i.current=l}else l=i.current;i=Ah(function(){function a(h){if(!u){if(u=!0,c=h,h=r(h),o!==void 0&&l.hasValue){var w=l.value;if(o(w,h))return d=w}return d=h}if(w=d,Ph(c,h))return w;var y=r(h);return o!==void 0&&o(w,y)?w:(c=h,d=y)}var u=!1,c,d,m=n===void 0?null:n;return[function(){return a(t())},m===null?void 0:function(){return a(m())}]},[t,n,r,o]);var s=$h(e,i[0],i[1]);return Th(function(){l.hasValue=!0,l.value=s},[s]),Mh(s),s};Sp.exports=_p;var Rh=Sp.exports;const Lh=ca(Rh),{useSyncExternalStoreWithSelector:zh}=Lh;function Dh(e,t=e.getState,n){const r=zh(e.subscribe,e.getState,e.getServerState||e.getState,t,n);return g.useDebugValue(r),r}const Rc=e=>{const t=typeof e=="function"?mh(e):e,n=(r,o)=>Dh(t,r,o);return Object.assign(n,t),n},bp=e=>e?Rc(e):Rc,Bs=new Map,Vo=e=>{const t=Bs.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([n,r])=>[n,r.getState()])):{}},Nh=(e,t,n)=>{if(e===void 0)return{type:"untracked",connection:t.connect(n)};const r=Bs.get(n.name);if(r)return{type:"tracked",store:e,...r};const o={connection:t.connect(n),stores:{}};return Bs.set(n.name,o),{type:"tracked",store:e,...o}},Fh=(e,t={})=>(n,r,o)=>{const{enabled:i,anonymousActionType:l,store:s,...a}=t;let u;try{u=(i??!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!u)return e(n,r,o);const{connection:c,...d}=Nh(s,u,a);let m=!0;o.setState=(y,C,p)=>{const f=n(y,C);if(!m)return f;const v=p===void 0?{type:l||"anonymous"}:typeof p=="string"?{type:p}:p;return s===void 0?(c==null||c.send(v,r()),f):(c==null||c.send({...v,type:`${s}/${v.type}`},{...Vo(a.name),[s]:o.getState()}),f)};const h=(...y)=>{const C=m;m=!1,n(...y),m=C},w=e(o.setState,r,o);if(d.type==="untracked"?c==null||c.init(w):(d.stores[d.store]=o,c==null||c.init(Object.fromEntries(Object.entries(d.stores).map(([y,C])=>[y,y===d.store?w:C.getState()])))),o.dispatchFromDevtools&&typeof o.dispatch=="function"){let y=!1;const C=o.dispatch;o.dispatch=(...p)=>{C(...p)}}return c.subscribe(y=>{var C;switch(y.type){case"ACTION":if(typeof y.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return Zl(y.payload,p=>{if(p.type==="__setState"){if(s===void 0){h(p.state);return}Object.keys(p.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const f=p.state[s];if(f==null)return;JSON.stringify(o.getState())!==JSON.stringify(f)&&h(f);return}o.dispatchFromDevtools&&typeof o.dispatch=="function"&&o.dispatch(p)});case"DISPATCH":switch(y.payload.type){case"RESET":return h(w),s===void 0?c==null?void 0:c.init(o.getState()):c==null?void 0:c.init(Vo(a.name));case"COMMIT":if(s===void 0){c==null||c.init(o.getState());return}return c==null?void 0:c.init(Vo(a.name));case"ROLLBACK":return Zl(y.state,p=>{if(s===void 0){h(p),c==null||c.init(o.getState());return}h(p[s]),c==null||c.init(Vo(a.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return Zl(y.state,p=>{if(s===void 0){h(p);return}JSON.stringify(o.getState())!==JSON.stringify(p[s])&&h(p[s])});case"IMPORT_STATE":{const{nextLiftedState:p}=y.payload,f=(C=p.computedStates.slice(-1)[0])==null?void 0:C.state;if(!f)return;h(s===void 0?f:f[s]),c==null||c.send(null,p);return}case"PAUSE_RECORDING":return m=!m}return}}),w},Ih=Fh,Zl=(e,t)=>{let n;try{n=JSON.parse(e)}catch(r){console.error("[zustand devtools middleware] Could not parse the received json",r)}n!==void 0&&t(n)},jh=e=>(t,n,r)=>{const o=r.subscribe;return r.subscribe=(l,s,a)=>{let u=l;if(s){const c=(a==null?void 0:a.equalityFn)||Object.is;let d=l(r.getState());u=m=>{const h=l(m);if(!c(d,h)){const w=d;s(d=h,w)}},a!=null&&a.fireImmediately&&s(d,d)}return o(u)},e(t,n,r)},Hh=jh;function Vh(e,t){let n;try{n=e()}catch{return}return{getItem:o=>{var i;const l=a=>a===null?null:JSON.parse(a,t==null?void 0:t.reviver),s=(i=n.getItem(o))!=null?i:null;return s instanceof Promise?s.then(l):l(s)},setItem:(o,i)=>n.setItem(o,JSON.stringify(i,t==null?void 0:t.replacer)),removeItem:o=>n.removeItem(o)}}const ao=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return ao(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return ao(r)(n)}}}},Uh=(e,t)=>(n,r,o)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:C=>C,version:0,merge:(C,p)=>({...p,...C}),...t},l=!1;const s=new Set,a=new Set;let u;try{u=i.getStorage()}catch{}if(!u)return e((...C)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...C)},r,o);const c=ao(i.serialize),d=()=>{const C=i.partialize({...r()});let p;const f=c({state:C,version:i.version}).then(v=>u.setItem(i.name,v)).catch(v=>{p=v});if(p)throw p;return f},m=o.setState;o.setState=(C,p)=>{m(C,p),d()};const h=e((...C)=>{n(...C),d()},r,o);let w;const y=()=>{var C;if(!u)return;l=!1,s.forEach(f=>f(r()));const p=((C=i.onRehydrateStorage)==null?void 0:C.call(i,r()))||void 0;return ao(u.getItem.bind(u))(i.name).then(f=>{if(f)return i.deserialize(f)}).then(f=>{if(f)if(typeof f.version=="number"&&f.version!==i.version){if(i.migrate)return i.migrate(f.state,f.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return f.state}).then(f=>{var v;return w=i.merge(f,(v=r())!=null?v:h),n(w,!0),d()}).then(()=>{p==null||p(w,void 0),l=!0,a.forEach(f=>f(w))}).catch(f=>{p==null||p(void 0,f)})};return o.persist={setOptions:C=>{i={...i,...C},C.getStorage&&(u=C.getStorage())},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>y(),hasHydrated:()=>l,onHydrate:C=>(s.add(C),()=>{s.delete(C)}),onFinishHydration:C=>(a.add(C),()=>{a.delete(C)})},y(),w||h},Bh=(e,t)=>(n,r,o)=>{let i={storage:Vh(()=>localStorage),partialize:y=>y,version:0,merge:(y,C)=>({...C,...y}),...t},l=!1;const s=new Set,a=new Set;let u=i.storage;if(!u)return e((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...y)},r,o);const c=()=>{const y=i.partialize({...r()});return u.setItem(i.name,{state:y,version:i.version})},d=o.setState;o.setState=(y,C)=>{d(y,C),c()};const m=e((...y)=>{n(...y),c()},r,o);let h;const w=()=>{var y,C;if(!u)return;l=!1,s.forEach(f=>{var v;return f((v=r())!=null?v:m)});const p=((C=i.onRehydrateStorage)==null?void 0:C.call(i,(y=r())!=null?y:m))||void 0;return ao(u.getItem.bind(u))(i.name).then(f=>{if(f)if(typeof f.version=="number"&&f.version!==i.version){if(i.migrate)return i.migrate(f.state,f.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return f.state}).then(f=>{var v;return h=i.merge(f,(v=r())!=null?v:m),n(h,!0),c()}).then(()=>{p==null||p(h,void 0),h=r(),l=!0,a.forEach(f=>f(h))}).catch(f=>{p==null||p(void 0,f)})};return o.persist={setOptions:y=>{i={...i,...y},y.storage&&(u=y.storage)},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>w(),hasHydrated:()=>l,onHydrate:y=>(s.add(y),()=>{s.delete(y)}),onFinishHydration:y=>(a.add(y),()=>{a.delete(y)})},i.skipHydration||w(),h||m},Wh=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?Uh(e,t):Bh(e,t),Qh=Wh;var Pp=Symbol.for("immer-nothing"),Lc=Symbol.for("immer-draftable"),Re=Symbol.for("immer-state");function Xe(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var lr=Object.getPrototypeOf;function sr(e){return!!e&&!!e[Re]}function Sn(e){var t;return e?$p(e)||Array.isArray(e)||!!e[Lc]||!!((t=e.constructor)!=null&&t[Lc])||pl(e)||ml(e):!1}var Kh=Object.prototype.constructor.toString();function $p(e){if(!e||typeof e!="object")return!1;const t=lr(e);if(t===null)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object?!0:typeof n=="function"&&Function.toString.call(n)===Kh}function uo(e,t){dl(e)===0?Object.entries(e).forEach(([n,r])=>{t(n,r,e)}):e.forEach((n,r)=>t(r,n,e))}function dl(e){const t=e[Re];return t?t.type_:Array.isArray(e)?1:pl(e)?2:ml(e)?3:0}function Ws(e,t){return dl(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function Op(e,t,n){const r=dl(e);r===2?e.set(t,n):r===3?e.add(n):e[t]=n}function Gh(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function pl(e){return e instanceof Map}function ml(e){return e instanceof Set}function un(e){return e.copy_||e.base_}function Qs(e,t){if(pl(e))return new Map(e);if(ml(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);if(!t&&$p(e))return lr(e)?{...e}:Object.assign(Object.create(null),e);const n=Object.getOwnPropertyDescriptors(e);delete n[Re];let r=Reflect.ownKeys(n);for(let o=0;o<r.length;o++){const i=r[o],l=n[i];l.writable===!1&&(l.writable=!0,l.configurable=!0),(l.get||l.set)&&(n[i]={configurable:!0,writable:!0,enumerable:l.enumerable,value:e[i]})}return Object.create(lr(e),n)}function au(e,t=!1){return hl(e)||sr(e)||!Sn(e)||(dl(e)>1&&(e.set=e.add=e.clear=e.delete=Zh),Object.freeze(e),t&&uo(e,(n,r)=>au(r,!0))),e}function Zh(){Xe(2)}function hl(e){return Object.isFrozen(e)}var Yh={};function _n(e){const t=Yh[e];return t||Xe(0,e),t}var co;function Tp(){return co}function Xh(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function zc(e,t){t&&(_n("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Ks(e){Gs(e),e.drafts_.forEach(Jh),e.drafts_=null}function Gs(e){e===co&&(co=e.parent_)}function Dc(e){return co=Xh(co,e)}function Jh(e){const t=e[Re];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function Nc(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return e!==void 0&&e!==n?(n[Re].modified_&&(Ks(t),Xe(4)),Sn(e)&&(e=Fi(t,e),t.parent_||Ii(t,e)),t.patches_&&_n("Patches").generateReplacementPatches_(n[Re].base_,e,t.patches_,t.inversePatches_)):e=Fi(t,n,[]),Ks(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==Pp?e:void 0}function Fi(e,t,n){if(hl(t))return t;const r=t[Re];if(!r)return uo(t,(o,i)=>Fc(e,r,t,o,i,n)),t;if(r.scope_!==e)return t;if(!r.modified_)return Ii(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const o=r.copy_;let i=o,l=!1;r.type_===3&&(i=new Set(o),o.clear(),l=!0),uo(i,(s,a)=>Fc(e,r,o,s,a,n,l)),Ii(e,o,!1),n&&e.patches_&&_n("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function Fc(e,t,n,r,o,i,l){if(sr(o)){const s=i&&t&&t.type_!==3&&!Ws(t.assigned_,r)?i.concat(r):void 0,a=Fi(e,o,s);if(Op(n,r,a),sr(a))e.canAutoFreeze_=!1;else return}else l&&n.add(o);if(Sn(o)&&!hl(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Fi(e,o),(!t||!t.scope_.parent_)&&Ii(e,o)}}function Ii(e,t,n=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&au(t,n)}function qh(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:Tp(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=r,i=uu;n&&(o=[r],i=fo);const{revoke:l,proxy:s}=Proxy.revocable(o,i);return r.draft_=s,r.revoke_=l,s}var uu={get(e,t){if(t===Re)return e;const n=un(e);if(!Ws(n,t))return ev(e,n,t);const r=n[t];return e.finalized_||!Sn(r)?r:r===Yl(e.base_,t)?(Xl(e),e.copy_[t]=Ys(r,e)):r},has(e,t){return t in un(e)},ownKeys(e){return Reflect.ownKeys(un(e))},set(e,t,n){const r=Ap(un(e),t);if(r!=null&&r.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const o=Yl(un(e),t),i=o==null?void 0:o[Re];if(i&&i.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(Gh(n,o)&&(n!==void 0||Ws(e.base_,t)))return!0;Xl(e),Zs(e)}return e.copy_[t]===n&&(n!==void 0||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty(e,t){return Yl(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,Xl(e),Zs(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const n=un(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty(){Xe(11)},getPrototypeOf(e){return lr(e.base_)},setPrototypeOf(){Xe(12)}},fo={};uo(uu,(e,t)=>{fo[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});fo.deleteProperty=function(e,t){return fo.set.call(this,e,t,void 0)};fo.set=function(e,t,n){return uu.set.call(this,e[0],t,n,e[0])};function Yl(e,t){const n=e[Re];return(n?un(n):e)[t]}function ev(e,t,n){var o;const r=Ap(t,n);return r?"value"in r?r.value:(o=r.get)==null?void 0:o.call(e.draft_):void 0}function Ap(e,t){if(!(t in e))return;let n=lr(e);for(;n;){const r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=lr(n)}}function Zs(e){e.modified_||(e.modified_=!0,e.parent_&&Zs(e.parent_))}function Xl(e){e.copy_||(e.copy_=Qs(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var tv=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,n,r)=>{if(typeof t=="function"&&typeof n!="function"){const i=n;n=t;const l=this;return function(a=i,...u){return l.produce(a,c=>n.call(this,c,...u))}}typeof n!="function"&&Xe(6),r!==void 0&&typeof r!="function"&&Xe(7);let o;if(Sn(t)){const i=Dc(this),l=Ys(t,void 0);let s=!0;try{o=n(l),s=!1}finally{s?Ks(i):Gs(i)}return zc(i,r),Nc(o,i)}else if(!t||typeof t!="object"){if(o=n(t),o===void 0&&(o=t),o===Pp&&(o=void 0),this.autoFreeze_&&au(o,!0),r){const i=[],l=[];_n("Patches").generateReplacementPatches_(t,o,i,l),r(i,l)}return o}else Xe(1,t)},this.produceWithPatches=(t,n)=>{if(typeof t=="function")return(l,...s)=>this.produceWithPatches(l,a=>t(a,...s));let r,o;return[this.produce(t,n,(l,s)=>{r=l,o=s}),r,o]},typeof(e==null?void 0:e.autoFreeze)=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof(e==null?void 0:e.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){Sn(e)||Xe(8),sr(e)&&(e=nv(e));const t=Dc(this),n=Ys(e,void 0);return n[Re].isManual_=!0,Gs(t),n}finishDraft(e,t){const n=e&&e[Re];(!n||!n.isManual_)&&Xe(9);const{scope_:r}=n;return zc(r,t),Nc(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const o=t[n];if(o.path.length===0&&o.op==="replace"){e=o.value;break}}n>-1&&(t=t.slice(n+1));const r=_n("Patches").applyPatches_;return sr(e)?r(e,t):this.produce(e,o=>r(o,t))}};function Ys(e,t){const n=pl(e)?_n("MapSet").proxyMap_(e,t):ml(e)?_n("MapSet").proxySet_(e,t):qh(e,t);return(t?t.scope_:Tp()).drafts_.push(n),n}function nv(e){return sr(e)||Xe(10,e),Mp(e)}function Mp(e){if(!Sn(e)||hl(e))return e;const t=e[Re];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=Qs(e,t.scope_.immer_.useStrictShallowCopy_)}else n=Qs(e,!0);return uo(n,(r,o)=>{Op(n,r,Mp(o))}),t&&(t.finalized_=!1),n}var Le=new tv,Rp=Le.produce;Le.produceWithPatches.bind(Le);Le.setAutoFreeze.bind(Le);Le.setUseStrictShallowCopy.bind(Le);Le.applyPatches.bind(Le);Le.createDraft.bind(Le);Le.finishDraft.bind(Le);const rv=e=>(t,n,r)=>(r.setState=(o,i,...l)=>{const s=typeof o=="function"?Rp(o):o;return t(s,i,...l)},e(r.setState,n,r)),Lp=rv,{user_settings:ov,media_mode:iv,tree_mode:lv}=window.fbv_data,{DEFAULT_FOLDER:ji,FOLDER_STARTUP:zp}=ov,Xs={PREVIOUS:-2,UN_CATEGORIZED:0,ALL:-1},sv=(e,t)=>{const n=window.location.search,r=new URLSearchParams(n),o=parseInt(r.get("fbv")),i=iv==="list"&&!isNaN(o);return ji==Xs.PREVIOUS?!e&&t.selectedKeys[0]!=Xs.PREVIOUS?t.selectedKeys[0]:[i?o:zp]:[i?o:ji]},av=()=>{const e=window.location.search,t=new URLSearchParams(e),n=window.typenow,r=parseInt(t.get(`fbv_pt_tax_${n}`));return ji==Xs.PREVIOUS?[isNaN(r)?+zp:r]:[isNaN(r)?+ji:r]},uv=(e,t)=>lv==="post_type"?av():sv(e,t),cv=e=>({isContextMenuOpen:!1,contextMenuDetail:null,showDeleteConfirm:!1,cuttingNode:null,setIsContextMenuOpen:t=>{e(n=>{n.isContextMenuOpen=t})},setShowDeleteConfirm:t=>{e(n=>{n.showDeleteConfirm=t})},setContextMenuDetail:t=>{e(n=>{n.contextMenuDetail=t,n.showDeleteConfirm=!0})},setCuttingNode:t=>{e(n=>{n.cuttingNode=t})}});let fv={data:""},dv=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||fv,pv=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,mv=/\/\*[^]*?\*\/|  +/g,Ic=/\n+/g,Dt=(e,t)=>{let n="",r="",o="";for(let i in e){let l=e[i];i[0]=="@"?i[1]=="i"?n=i+" "+l+";":r+=i[1]=="f"?Dt(l,i):i+"{"+Dt(l,i[1]=="k"?"":t)+"}":typeof l=="object"?r+=Dt(l,t?t.replace(/([^,])+/g,s=>i.replace(/(^:.*)|([^,])+/g,a=>/&/.test(a)?a.replace(/&/g,s):s?s+" "+a:a)):i):l!=null&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),o+=Dt.p?Dt.p(i,l):i+":"+l+";")}return n+(t&&o?t+"{"+o+"}":o)+r},at={},Dp=e=>{if(typeof e=="object"){let t="";for(let n in e)t+=n+Dp(e[n]);return t}return e},hv=(e,t,n,r,o)=>{let i=Dp(e),l=at[i]||(at[i]=(a=>{let u=0,c=11;for(;u<a.length;)c=101*c+a.charCodeAt(u++)>>>0;return"go"+c})(i));if(!at[l]){let a=i!==e?e:(u=>{let c,d,m=[{}];for(;c=pv.exec(u.replace(mv,""));)c[4]?m.shift():c[3]?(d=c[3].replace(Ic," ").trim(),m.unshift(m[0][d]=m[0][d]||{})):m[0][c[1]]=c[2].replace(Ic," ").trim();return m[0]})(e);at[l]=Dt(o?{["@keyframes "+l]:a}:a,n?"":"."+l)}let s=n&&at.g?at.g:null;return n&&(at.g=at[l]),((a,u,c,d)=>{d?u.data=u.data.replace(d,a):u.data.indexOf(a)===-1&&(u.data=c?a+u.data:u.data+a)})(at[l],t,r,s),l},vv=(e,t,n)=>e.reduce((r,o,i)=>{let l=t[i];if(l&&l.call){let s=l(n),a=s&&s.props&&s.props.className||/^go/.test(s)&&s;l=a?"."+a:s&&typeof s=="object"?s.props?"":Dt(s,""):s===!1?"":s}return r+o+(l??"")},"");function vl(e){let t=this||{},n=e.call?e(t.p):e;return hv(n.unshift?n.raw?vv(n,[].slice.call(arguments,1),t.p):n.reduce((r,o)=>Object.assign(r,o&&o.call?o(t.p):o),{}):n,dv(t.target),t.g,t.o,t.k)}let Np,Js,qs;vl.bind({g:1});let xt=vl.bind({k:1});function gv(e,t,n,r){Dt.p=t,Np=e,Js=n,qs=r}function tn(e,t){let n=this||{};return function(){let r=arguments;function o(i,l){let s=Object.assign({},i),a=s.className||o.className;n.p=Object.assign({theme:Js&&Js()},s),n.o=/ *go\d+/.test(a),s.className=vl.apply(n,r)+(a?" "+a:""),t&&(s.ref=l);let u=e;return e[0]&&(u=s.as||e,delete s.as),qs&&u[0]&&qs(s),Np(u,s)}return t?t(o):o}}var yv=e=>typeof e=="function",Hi=(e,t)=>yv(e)?e(t):e,wv=(()=>{let e=0;return()=>(++e).toString()})(),Fp=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),xv=20,si=new Map,Cv=1e3,jc=e=>{if(si.has(e))return;let t=setTimeout(()=>{si.delete(e),$n({type:4,toastId:e})},Cv);si.set(e,t)},Sv=e=>{let t=si.get(e);t&&clearTimeout(t)},ea=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,xv)};case 1:return t.toast.id&&Sv(t.toast.id),{...e,toasts:e.toasts.map(i=>i.id===t.toast.id?{...i,...t.toast}:i)};case 2:let{toast:n}=t;return e.toasts.find(i=>i.id===n.id)?ea(e,{type:1,toast:n}):ea(e,{type:0,toast:n});case 3:let{toastId:r}=t;return r?jc(r):e.toasts.forEach(i=>{jc(i.id)}),{...e,toasts:e.toasts.map(i=>i.id===r||r===void 0?{...i,visible:!1}:i)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(i=>i.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let o=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(i=>({...i,pauseDuration:i.pauseDuration+o}))}}},ai=[],ui={toasts:[],pausedAt:void 0},$n=e=>{ui=ea(ui,e),ai.forEach(t=>{t(ui)})},_v={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Ev=(e={})=>{let[t,n]=g.useState(ui);g.useEffect(()=>(ai.push(n),()=>{let o=ai.indexOf(n);o>-1&&ai.splice(o,1)}),[t]);let r=t.toasts.map(o=>{var i,l;return{...e,...e[o.type],...o,duration:o.duration||((i=e[o.type])==null?void 0:i.duration)||(e==null?void 0:e.duration)||_v[o.type],style:{...e.style,...(l=e[o.type])==null?void 0:l.style,...o.style}}});return{...t,toasts:r}},kv=(e,t="blank",n)=>({createdAt:Date.now(),visible:!0,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...n,id:(n==null?void 0:n.id)||wv()}),xo=e=>(t,n)=>{let r=kv(t,e,n);return $n({type:2,toast:r}),r.id},Oe=(e,t)=>xo("blank")(e,t);Oe.error=xo("error");Oe.success=xo("success");Oe.loading=xo("loading");Oe.custom=xo("custom");Oe.dismiss=e=>{$n({type:3,toastId:e})};Oe.remove=e=>$n({type:4,toastId:e});Oe.promise=(e,t,n)=>{let r=Oe.loading(t.loading,{...n,...n==null?void 0:n.loading});return e.then(o=>(Oe.success(Hi(t.success,o),{id:r,...n,...n==null?void 0:n.success}),o)).catch(o=>{Oe.error(Hi(t.error,o),{id:r,...n,...n==null?void 0:n.error})}),e};var bv=(e,t)=>{$n({type:1,toast:{id:e,height:t}})},Pv=()=>{$n({type:5,time:Date.now()})},$v=e=>{let{toasts:t,pausedAt:n}=Ev(e);g.useEffect(()=>{if(n)return;let i=Date.now(),l=t.map(s=>{if(s.duration===1/0)return;let a=(s.duration||0)+s.pauseDuration-(i-s.createdAt);if(a<0){s.visible&&Oe.dismiss(s.id);return}return setTimeout(()=>Oe.dismiss(s.id),a)});return()=>{l.forEach(s=>s&&clearTimeout(s))}},[t,n]);let r=g.useCallback(()=>{n&&$n({type:6,time:Date.now()})},[n]),o=g.useCallback((i,l)=>{let{reverseOrder:s=!1,gutter:a=8,defaultPosition:u}=l||{},c=t.filter(h=>(h.position||u)===(i.position||u)&&h.height),d=c.findIndex(h=>h.id===i.id),m=c.filter((h,w)=>w<d&&h.visible).length;return c.filter(h=>h.visible).slice(...s?[m+1]:[0,m]).reduce((h,w)=>h+(w.height||0)+a,0)},[t]);return{toasts:t,handlers:{updateHeight:bv,startPause:Pv,endPause:r,calculateOffset:o}}},Ov=xt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Tv=xt`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Av=xt`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Mv=tn("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ov} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Tv} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Av} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Rv=xt`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Lv=tn("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Rv} 1s linear infinite;
`,zv=xt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Dv=xt`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Nv=tn("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${zv} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Dv} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Fv=tn("div")`
  position: absolute;
`,Iv=tn("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,jv=xt`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Hv=tn("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${jv} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Vv=({toast:e})=>{let{icon:t,type:n,iconTheme:r}=e;return t!==void 0?typeof t=="string"?g.createElement(Hv,null,t):t:n==="blank"?null:g.createElement(Iv,null,g.createElement(Lv,{...r}),n!=="loading"&&g.createElement(Fv,null,n==="error"?g.createElement(Mv,{...r}):g.createElement(Nv,{...r})))},Uv=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Bv=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,Wv="0%{opacity:0;} 100%{opacity:1;}",Qv="0%{opacity:1;} 100%{opacity:0;}",Kv=tn("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Gv=tn("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Zv=(e,t)=>{let n=e.includes("top")?1:-1,[r,o]=Fp()?[Wv,Qv]:[Uv(n),Bv(n)];return{animation:t?`${xt(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${xt(o)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},Yv=g.memo(({toast:e,position:t,style:n,children:r})=>{let o=e.height?Zv(e.position||t||"top-center",e.visible):{opacity:0},i=g.createElement(Vv,{toast:e}),l=g.createElement(Gv,{...e.ariaProps},Hi(e.message,e));return g.createElement(Kv,{className:e.className,style:{...o,...n,...e.style}},typeof r=="function"?r({icon:i,message:l}):g.createElement(g.Fragment,null,i,l))});gv(g.createElement);var Xv=({id:e,className:t,style:n,onHeightUpdate:r,children:o})=>{let i=g.useCallback(l=>{if(l){let s=()=>{let a=l.getBoundingClientRect().height;r(e,a)};s(),new MutationObserver(s).observe(l,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return g.createElement("div",{ref:i,className:t,style:n},o)},Jv=(e,t)=>{let n=e.includes("top"),r=n?{top:0}:{bottom:0},o=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:Fp()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(n?1:-1)}px)`,...r,...o}},qv=vl`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Uo=16,Uw=({reverseOrder:e,position:t="top-center",toastOptions:n,gutter:r,children:o,containerStyle:i,containerClassName:l})=>{let{toasts:s,handlers:a}=$v(n);return g.createElement("div",{style:{position:"fixed",zIndex:9999,top:Uo,left:Uo,right:Uo,bottom:Uo,pointerEvents:"none",...i},className:l,onMouseEnter:a.startPause,onMouseLeave:a.endPause},s.map(u=>{let c=u.position||t,d=a.calculateOffset(u,{reverseOrder:e,gutter:r,defaultPosition:t}),m=Jv(c,d);return g.createElement(Xv,{id:u.id,key:u.id,onHeightUpdate:a.updateHeight,className:u.visible?qv:"",style:m},u.type==="custom"?Hi(u.message,u):o?o(u):g.createElement(Yv,{toast:u,position:c}))}))},dt=Oe;const{fbv_data:eg,jQuery:tg}=window,Ip=(e="GET",t={})=>tg.ajax({url:window.ajaxurl,beforeSend:()=>{},method:e,data:{...t,nonce:eg.nonce}}),{fbv_data:Hc,jQuery:ng}=window,ie=(e="GET",t="",n={})=>ng.ajax({url:Hc.json_url+t,beforeSend:()=>{},method:e,dataType:"json",contentType:"application/json",headers:{"X-WP-Nonce":Hc.rest_nonce},data:e==="GET"?n:JSON.stringify(n)}),Or=window.fbv_data.icl_lang||window.fbv_data.pll_lang||void 0,rg={getTree(e){return ie("GET","/get-folders",{...e,language:Or})},createFolder(e){return ie("POST","/new-folder",e)},updateFolder(e){return ie("POST","/update-folder",e)},deleteFolders(e){return ie("POST","/delete-folder",{ids:e,language:Or})},assignFolder(e){return ie("POST","/assign-folder",{...e,language:Or})},updateFolderOrd(e){return ie("POST","/update-folder-ord",{...e,language:Or})},updateFolderColor(e){return ie("POST","/update-folder-color",e)},setFolderCounter(e){return ie("GET","/set-folder-counter",{type:e,language:Or})},downloadFolder(e){return Ip("GET",{action:"fbv_download_folder",folder_id:e})}},Bo=window.fbv_data.icl_lang||window.fbv_data.pll_lang||void 0,og={getTree(e){return ie("GET","/pt-folders",{...e,post_type:window.typenow,language:Bo})},createFolder(e){return ie("POST","/pt-new-folder",{...e,post_type:window.typenow})},updateFolder(e){return ie("POST","/pt-edit-folder",{...e,post_type:window.typenow})},deleteFolders(e){return ie("POST","/pt-delete-folder",{ids:e,post_type:window.typenow,language:Bo})},assignFolder({ids:e,folderId:t}){return ie("POST","/pt-set-folder",{ids:e,folderId:t,post_type:window.typenow,language:Bo})},updateFolderColor(e){return ie("POST","/pt-set-folder-color",{...e,post_type:window.typenow})},updateFolderOrd(e){return ie("POST","/pt-update-tree",{...e,post_type:window.typenow})},setFolderCounter(e){return ie("GET","/pt-folder-counter",{type:e,post_type:window.typenow,language:Bo})},updateSetting(e){return Ip("POST",e)}},Ke=(e,t,n)=>{e.forEach((r,o,i)=>{if(r.key==t){n(r,o,i);return}r.children&&Ke(r.children,t,n)})},ig=(e,t)=>{for(const n of e){if(n.id===t)return n;if(n.children&&n.children.length>0){const r=ig(n.children,t);if(r)return r}}return null};function lg(e,t){for(const n of e){if(n.id==t)return[n];if(n.children&&n.children.length>0){const r=lg(n.children,t);if(r)return[n,...r]}}return null}const sg=e=>e.flatMap(t=>{var r;const n=[t.key];return(r=t.children)!=null&&r.length&&n.push(...sg(t.children)),n});function Bw(e){return new DOMParser().parseFromString(e,"text/html").documentElement.textContent}function Rt(e){return window.fbv_data.i18n[e]}const{tree_mode:ag,user_settings:ug}=window.fbv_data,on="#",{DEFAULT_SORT_FOLDERS:An,DEFAULT_FOLDER:cg,DEFAULT_SORT_FILES:Mn}=ug,ln=ag==="attachment"?rg:og,fg=(e,t)=>({editFolderId:null,selectedKeys:[cg],checkedKeys:[],checkable:!1,expandedKeys:[],treeData:[],isLoading:!1,isProcessing:!1,allAttachmentsCount:0,unAttachmentsCount:0,attachmentsCount:{display:{},actual:{}},folderQuery:{orderby:An==null?void 0:An.orderby,order:An==null?void 0:An.order},fileQuery:{orderby:Mn==null?void 0:Mn.orderby,order:Mn==null?void 0:Mn.order},setSearch:n=>e(r=>{r.folderQuery.search=n}),setExpandedKeys:n=>e(r=>{Array.isArray(n)?r.expandedKeys=n:r.expandedKeys=[...new Set([...r.expandedKeys,n])]}),setTreeData:n=>e(r=>{r.treeData=n}),setCheckable:n=>e(r=>{r.checkable=n,r.checkedKeys=[]}),setSelectedKeys:n=>e(r=>{r.selectedKeys=n}),setCheckedKeys:n=>e(r=>{r.checkedKeys=n}),addExpandedKeys(n){e(r=>{r.expandedKeys.indexOf(n)===-1&&r.expandedKeys.push(n)})},setEditFolderId:n=>{e(r=>{r.editFolderId=n})},removeExpandedKeys:n=>{e(r=>{r.expandedKeys=[...r.expandedKeys].filter(o=>Array.isArray(n)?n.indexOf(o)===-1:o!==n)})},createFolder:async n=>{try{e(i=>{i.isProcessing=!0});const r=parseInt(n.parent)>0?n.parent:0,o=await ln.createFolder({...n,parent:r});return e(i=>{r?Ke(i.treeData,r,l=>{var s;(s=l.children)==null||s.push(o)}):i.treeData.splice(i.treeData.length+1,0,o)}),t().clearEditFolder(),e(i=>{i.isProcessing=!1}),t().treeData}catch(r){return e(o=>{o.isProcessing=!1}),r}},updateFolder:async n=>{try{e(o=>{o.isProcessing=!0});const r=await ln.updateFolder(n);e(o=>{o.editFolderId&&r&&(Ke(o.treeData,o.editFolderId,(i,l,s)=>{s.splice(l,1,{...n})}),o.isProcessing=!1,o.editFolderId=null)}),t().removeExpandedKeys(t().editFolderId||[])}catch(r){return console.log("error",r),t().removeExpandedKeys(t().editFolderId||[]),e(o=>{o.isProcessing=!1}),r}},deleteFolders:async n=>{try{e(i=>{i.isProcessing=!0});const r=t().checkable?t().checkedKeys:[n||t().selectedKeys[0]],o=await ln.deleteFolders(r);e(i=>{i.checkable?i.checkedKeys.forEach(l=>{Ke(i.treeData,l,(s,a,u)=>{u.splice(a,1)}),i.checkedKeys=i.checkedKeys.filter(s=>s!==l)}):Ke(i.treeData,r[0],(l,s,a)=>{a.splice(s,1)})}),t().calculateAttachmentsCount(o)}catch(r){console.log({error:r})}finally{e(r=>{r.isProcessing=!1})}},createChildFolder:async n=>{const r={id:on,parent:0,"data-count":0,"data-id":on,title:Rt("new_folder"),children:[],ord:0,key:on},o=n||t().selectedKeys[0];r.parent=parseInt(o),parseInt(o)>0&&t().setExpandedKeys(o),e(i=>{parseInt(o)>0?Ke(i.treeData,o,l=>{l.children.push({...r})}):i.treeData.push({...r}),i.editFolderId=on})},editFolder:async n=>{e(r=>{const o=n||t().selectedKeys[0];r.editFolderId=o})},clearEditFolder:n=>{e(r=>{Ke(r.treeData,on,(o,i,l)=>{n?l.splice(i,1,n):l.splice(i,1)}),r.expandedKeys.indexOf(on)!==-1&&n!=null&&n.id&&r.addExpandedKeys(n==null?void 0:n.id),r.editFolderId=null,r.removeExpandedKeys(on)})},setFolderQuery:n=>e({folderQuery:n}),updateFolderColor:async(n,r)=>{e(o=>{o.isProcessing=!0});try{await ln.updateFolderColor({folderId:n,color:r}),e(o=>{Ke(o.treeData,n,i=>{i.color=r}),o.isProcessing=!1}),dt.success(Rt("update_done"))}catch(o){dt.success(Rt("update_error")),console.log({error:o})}finally{e(o=>{o.isProcessing=!1})}},setFileQuery:n=>{e(r=>{r.fileQuery=n})},getTreeData:async()=>{try{e(r=>{r.isLoading=!0});const n=await ln.getTree(t().folderQuery);return e(r=>{r.treeData=n.tree,r.isLoading=!1,r.allAttachmentsCount=n.allAttachmentsCount,r.attachmentsCount=n.attachmentsCount,r.unAttachmentsCount=n.allAttachmentsCount-Object.values(n.attachmentsCount.actual).reduce((o,i)=>o+=+i,0)}),n.tree}catch(n){return console.log("error",n),e(r=>{r.isLoading=!1}),[]}},calculateAttachmentsCount:n=>{e(r=>{r.attachmentsCount=n,r.unAttachmentsCount=r.allAttachmentsCount-Object.values(n.actual).reduce((o,i)=>o+=+i,0)})},updateAttachmentCount:()=>{e(n=>{n.allAttachmentsCount--,parseInt(n.selectedKeys[0])>0?(n.attachmentsCount.display[n.selectedKeys[0]]--,n.attachmentsCount.actual[n.selectedKeys[0]]--):n.unAttachmentsCount--})},dnd:async(n,r,o,i)=>{e(u=>{u.isProcessing=!0});let l=null,s=0;const a=Rp(t().treeData,u=>{if(Ke(u,r,(c,d,m)=>{m.splice(d,1),l={...c},l.parent=s}),i===0)Ke(u,n,c=>{c.children=c.children||[],c.children.unshift(l)}),l.parent=n,s=n;else{let c,d;Ke(u,n,(m,h,w)=>{c=w,d=h}),i===-1?c.splice(d,0,l):(l.parent=o,s=o,c.splice(d+1,0,l))}});try{const u=await ln.updateFolderOrd({dropPosition:i,dragNodeId:r,dropNodeId:n,toParentId:s});e(c=>{c.treeData=a,typeof u=="object"&&(c.attachmentsCount=u)}),dt.success(Rt("move_done"))}catch{dt.error(Rt("move_error"))}finally{e(u=>{u.isProcessing=!1})}},setFolderCounter:async n=>{try{const r=await ln.setFolderCounter(n);window.fbv_data.user_settings.FOLDER_COUNTER_TYPE=n,e({attachmentsCount:r})}catch(r){dt.error(Rt("please_try_again")),console.log({error:r})}}}),dg=e=>({splitterWidth:300,displayFloatingTree:!1,setDisplayFloatingTree:()=>{e(t=>{t.displayFloatingTree=!t.displayFloatingTree})}}),pg=e=>({isUploading:!1,showUploader:!1,maximizeUploadModal:!0,uploadedAttachments:[],uploadingFiles:[],bytesPerSec:0,currentUploadingFile:null,progressPercent:0,totalUploadingSize:0,toggleModal:()=>e(t=>{t.maximizeUploadModal=!t.maximizeUploadModal}),closeModal:()=>e(t=>{t.maximizeUploadModal=!1,t.showUploader=!1}),resetUploader:()=>e(t=>{t.showUploader=!1,t.isUploading=!1,t.maximizeUploadModal=!1,t.uploadedAttachments=[],t.bytesPerSec=0,t.progressPercent=0,t.totalUploadingSize=0,t.uploadingFiles=[],t.currentUploadingFile=null})}),{tree_mode:mg}=window.fbv_data,{typenow:hg}=window,En=bp()(Lp(Ih(Hh(Qh((...e)=>({...fg(...e),...cv(...e),...pg(...e),...dg(...e)}),{name:mg==="attachment"?"filebird":`filebird-${hg}`,partialize:e=>({selectedKeys:e.selectedKeys,expandedKeys:e.expandedKeys,maximizeUploadModal:e.maximizeUploadModal}),merge:(e,t)=>({...t,selectedKeys:uv(e,t),expandedKeys:e.expandedKeys})})))));En.subscribe(e=>e.folderQuery,()=>{En.getState().getTreeData()},{equalityFn:(e,t)=>{const n=e.order===t.order&&e.orderby===t.orderby;return window.fbv_data.folder_search_api?n&&e.search===t.search:n}});const{fbv_data:ci}=window;function Ww(e){try{return e.controller.content.mode()!=="browse"||e.attachments.options.model.get("toolbar")==="gallery-edit"}catch{return!1}}function vg(){let e=!1;return function(t){(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4)))&&(e=!0)}(navigator.userAgent||navigator.vendor),e}const Qw=(e,t,n=null)=>{let r={fbv:e,ignore:+new Date},o={...n};n&&(o.orderby==="reset"&&(o={orderby:"date",order:"DESC"}),r={...r,orderby:o.orderby,order:o.order.toUpperCase()});try{t.collection.props.set(r)}catch(i){console.log("Can not refresh the backbone view"),console.log(i)}},Kw=(e,t)=>{if(e==="name"||e==="modified"){const n=new URLSearchParams(window.location.search);n.set("orderby",e),n.set("order",t),window.history.replaceState({},"",`${window.location.pathname}?${n}`),window.location.reload()}else if(e==="reset"){const n=new URLSearchParams(window.location.search);n.set("post_type",n.get("post_type")||"attachment"),n.delete("orderby"),n.delete("order"),window.history.replaceState({},"",`${window.location.pathname}?${n}`),window.location.reload()}else{const n=jQuery(".wp-list-table th#"+e+" > a").attr("href"),r=new URLSearchParams(n);r.set("order",t),window.location.href=decodeURIComponent(r.toString())}},Gw=(e,t,n)=>{var r;if(t){const o=t.toolbar.get("fbv-filter");(r=t.controller)!=null&&r.isModeActive("grid")&&t.controller.state().get("selection").reset(),window.mlaModal?(t.collection.props.set({fbv:e},{silent:!0}),t.$el.find("#mla-search-submit").click()):n?t.collection.props.set({fbv:e,ignore:+new Date}):o.$el.val(e).trigger("change")}},Zw=e=>{jQuery("#posts-filter").find('select[name="fbv"]').val(e).trigger("change"),jQuery("#posts-filter").trigger("submit"),jQuery("#mla-filter").length&&(jQuery("#mla-filter").find('select[name="fbv"]').val(e).trigger("change"),jQuery("#mla-filter").trigger("submit"))},Yw=e=>{var t=jQuery("#posts-filter").find(".fbv-filter").attr("name");jQuery("#posts-filter").find('[name="'+t+'"]').val(e).trigger("change"),jQuery("#posts-filter").trigger("submit")};function Xw(){return jQuery(".media-frame").hasClass("mode-select")||jQuery(".media-frame").hasClass("mode-eml-grid")}const jp=e=>{let t=[];return e.forEach(n=>{t.push({term_id:n.id,term_name:n.title}),n.children&&n.children.length&&(t=t.concat(jp(n.children)))}),[...t]},Jw=e=>{window.fbv_data.folders=[{term_id:-1,term_name:ci.i18n.all_files},{term_id:0,term_name:ci.i18n.uncategorized},...jp(e)];const t=jQuery(".attachments-browser");if(t.each(function(){const n=jQuery(this),r=n.data("backboneView"),o={browser:n,view:r};if(o.browser.length>0&&typeof o.view=="object")try{o.view.toolbar.get("fbv-filter").createFilters(!0)}catch(i){console.log("Can not refresh the filters"),console.log(i)}}),!t.length){const n=En.getState().selectedKeys[0]||jQuery("#filter-by-fbv").val();jQuery("#filter-by-fbv").html(ci.folders.map(function(r){const o=jQuery("<option></option>").val(r.term_id).html(r.term_name);return r.term_id==n&&o.attr("selected","selected"),o[0].outerHTML}).join(""))}},qw=(e,t)=>{if(t)e.forEach(n=>{t.$el.find('ul.attachments li[data-id="'+n+'"]').detach()});else if(e.forEach(n=>{jQuery("table.wp-list-table #the-list").find("tr#post-"+n).remove()}),jQuery("table.wp-list-table #the-list").html().trim()==""){const n='<td class="colspanchange" colspan="'+(jQuery("table.wp-list-table > thead > tr > th").length+1)+'">'+ci.i18n.noMedia+"</td>";jQuery("table.wp-list-table #the-list").html('<tr class="no-items">'+n+"</tr>")}},Hp=vg();function Vp(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Vp(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function ta(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Vp(e))&&(r&&(r+=" "),r+=t);return r}const e3=Object.freeze(Object.defineProperty({__proto__:null,clsx:ta,default:ta},Symbol.toStringTag,{value:"Module"}));function Up(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Up(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function Bp(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Up(e))&&(r&&(r+=" "),r+=t);return r}function Ue(){return Ue=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ue.apply(this,arguments)}function gg(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Wp(...e){return t=>e.forEach(n=>gg(n,t))}function hr(...e){return g.useCallback(Wp(...e),e)}const cu=g.forwardRef((e,t)=>{const{children:n,...r}=e,o=g.Children.toArray(n),i=o.find(yg);if(i){const l=i.props.children,s=o.map(a=>a===i?g.Children.count(l)>1?g.Children.only(null):g.isValidElement(l)?l.props.children:null:a);return g.createElement(na,Ue({},r,{ref:t}),g.isValidElement(l)?g.cloneElement(l,void 0,s):null)}return g.createElement(na,Ue({},r,{ref:t}),n)});cu.displayName="Slot";const na=g.forwardRef((e,t)=>{const{children:n,...r}=e;return g.isValidElement(n)?g.cloneElement(n,{...wg(r,n.props),ref:t?Wp(t,n.ref):n.ref}):g.Children.count(n)>1?g.Children.only(null):null});na.displayName="SlotClone";const Qp=({children:e})=>g.createElement(g.Fragment,null,e);function yg(e){return g.isValidElement(e)&&e.type===Qp}function wg(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...s)=>{i(...s),o(...s)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function xg(e,t){const n={},r=Object.keys(e);for(let o=0;o<r.length;o++){const i=r[o];t(i)&&(n[i]=e[i])}return n}const Kp=(e={})=>{const t=e.compose||Bp,n=e.shouldForwardProp||(o=>o[0]!=="$"),r=o=>{const i=(l,s=n)=>{const a=(u,...c)=>{const d=typeof u=="function",m=!d&&String.raw({raw:u},...c);return g.forwardRef((h,w)=>{const{className:y,asChild:C,...p}=h,f=typeof l=="function"?l(h):l||{},v=xg({...f,...p},s),x=C?cu:o,S=d?u(h):m;return g.createElement(x,{ref:w,className:typeof S=="function"?_=>t(S(_),typeof y=="function"?y(_):y):t(S,y),...v})})};return a.transientProps=u=>i(l,typeof u=="function"?d=>!u(d):d=>!u.includes(d)),l===void 0&&(a.attrs=u=>i(u)),a};return i()};return new Proxy(o=>r(o),{get(o,i){return r(i)}})},t3=Kp(),fu="-";function Cg(e){const t=_g(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;function o(l){const s=l.split(fu);return s[0]===""&&s.length!==1&&s.shift(),Gp(s,t)||Sg(l)}function i(l,s){const a=n[l]||[];return s&&r[l]?[...a,...r[l]]:a}return{getClassGroupId:o,getConflictingClassGroupIds:i}}function Gp(e,t){var l;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Gp(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(fu);return(l=t.validators.find(({validator:s})=>s(i)))==null?void 0:l.classGroupId}const Vc=/^\[(.+)\]$/;function Sg(e){if(Vc.test(e)){const t=Vc.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function _g(e){const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return kg(Object.entries(e.classGroups),n).forEach(([i,l])=>{ra(l,r,i,t)}),r}function ra(e,t,n,r){e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:Uc(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(Eg(o)){ra(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,l])=>{ra(l,Uc(t,i),n,r)})})}function Uc(e,t){let n=e;return t.split(fu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function Eg(e){return e.isThemeGetter}function kg(e,t){return t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([l,s])=>[t+l,s])):i);return[n,o]}):e}function bg(e){if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;function o(i,l){n.set(i,l),t++,t>e&&(t=0,r=n,n=new Map)}return{get(i){let l=n.get(i);if(l!==void 0)return l;if((l=r.get(i))!==void 0)return o(i,l),l},set(i,l){n.has(i)?n.set(i,l):o(i,l)}}}const Zp="!";function Pg(e){const t=e.separator,n=t.length===1,r=t[0],o=t.length;return function(l){const s=[];let a=0,u=0,c;for(let y=0;y<l.length;y++){let C=l[y];if(a===0){if(C===r&&(n||l.slice(y,y+o)===t)){s.push(l.slice(u,y)),u=y+o;continue}if(C==="/"){c=y;continue}}C==="["?a++:C==="]"&&a--}const d=s.length===0?l:l.substring(u),m=d.startsWith(Zp),h=m?d.substring(1):d,w=c&&c>u?c-u:void 0;return{modifiers:s,hasImportantModifier:m,baseClassName:h,maybePostfixModifierPosition:w}}}function $g(e){if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t}function Og(e){return{cache:bg(e.cacheSize),splitModifiers:Pg(e),...Cg(e)}}const Tg=/\s+/;function Ag(e,t){const{splitModifiers:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=new Set;return e.trim().split(Tg).map(l=>{const{modifiers:s,hasImportantModifier:a,baseClassName:u,maybePostfixModifierPosition:c}=n(l);let d=r(c?u.substring(0,c):u),m=!!c;if(!d){if(!c)return{isTailwindClass:!1,originalClassName:l};if(d=r(u),!d)return{isTailwindClass:!1,originalClassName:l};m=!1}const h=$g(s).join(":");return{isTailwindClass:!0,modifierId:a?h+Zp:h,classGroupId:d,originalClassName:l,hasPostfixModifier:m}}).reverse().filter(l=>{if(!l.isTailwindClass)return!0;const{modifierId:s,classGroupId:a,hasPostfixModifier:u}=l,c=s+a;return i.has(c)?!1:(i.add(c),o(a,u).forEach(d=>i.add(s+d)),!0)}).reverse().map(l=>l.originalClassName).join(" ")}function Mg(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Yp(t))&&(r&&(r+=" "),r+=n);return r}function Yp(e){if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Yp(e[r]))&&(n&&(n+=" "),n+=t);return n}function Bc(e,...t){let n,r,o,i=l;function l(a){const u=t.reduce((c,d)=>d(c),e());return n=Og(u),r=n.cache.get,o=n.cache.set,i=s,s(a)}function s(a){const u=r(a);if(u)return u;const c=Ag(a,n);return o(a,c),c}return function(){return i(Mg.apply(null,arguments))}}function Q(e){const t=n=>n[e]||[];return t.isThemeGetter=!0,t}const Xp=/^\[(?:([a-z-]+):)?(.+)\]$/i,Rg=/^\d+\/\d+$/,Lg=new Set(["px","full","screen"]),zg=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Dg=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ng=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Fg=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function Qe(e){return pn(e)||Lg.has(e)||Rg.test(e)}function Ot(e){return vr(e,"length",Qg)}function pn(e){return!!e&&!Number.isNaN(Number(e))}function Wo(e){return vr(e,"number",pn)}function Tr(e){return!!e&&Number.isInteger(Number(e))}function Ig(e){return e.endsWith("%")&&pn(e.slice(0,-1))}function D(e){return Xp.test(e)}function Tt(e){return zg.test(e)}const jg=new Set(["length","size","percentage"]);function Hg(e){return vr(e,jg,Jp)}function Vg(e){return vr(e,"position",Jp)}const Ug=new Set(["image","url"]);function Bg(e){return vr(e,Ug,Gg)}function Wg(e){return vr(e,"",Kg)}function Ar(){return!0}function vr(e,t,n){const r=Xp.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1}function Qg(e){return Dg.test(e)}function Jp(){return!1}function Kg(e){return Ng.test(e)}function Gg(e){return Fg.test(e)}function Wc(){const e=Q("colors"),t=Q("spacing"),n=Q("blur"),r=Q("brightness"),o=Q("borderColor"),i=Q("borderRadius"),l=Q("borderSpacing"),s=Q("borderWidth"),a=Q("contrast"),u=Q("grayscale"),c=Q("hueRotate"),d=Q("invert"),m=Q("gap"),h=Q("gradientColorStops"),w=Q("gradientColorStopPositions"),y=Q("inset"),C=Q("margin"),p=Q("opacity"),f=Q("padding"),v=Q("saturate"),x=Q("scale"),S=Q("sepia"),_=Q("skew"),b=Q("space"),k=Q("translate"),M=()=>["auto","contain","none"],T=()=>["auto","hidden","clip","visible","scroll"],z=()=>["auto",D,t],R=()=>[D,t],U=()=>["",Qe,Ot],I=()=>["auto",pn,D],se=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],N=()=>["solid","dashed","dotted","double","none"],he=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"],P=()=>["start","end","center","between","around","evenly","stretch"],A=()=>["","0",D],L=()=>["auto","avoid","all","avoid-page","page","left","right","column"],H=()=>[pn,Wo],W=()=>[pn,D];return{cacheSize:500,separator:":",theme:{colors:[Ar],spacing:[Qe,Ot],blur:["none","",Tt,D],brightness:H(),borderColor:[e],borderRadius:["none","","full",Tt,D],borderSpacing:R(),borderWidth:U(),contrast:H(),grayscale:A(),hueRotate:W(),invert:A(),gap:R(),gradientColorStops:[e],gradientColorStopPositions:[Ig,Ot],inset:z(),margin:z(),opacity:H(),padding:R(),saturate:H(),scale:H(),sepia:A(),skew:W(),space:R(),translate:R()},classGroups:{aspect:[{aspect:["auto","square","video",D]}],container:["container"],columns:[{columns:[Tt]}],"break-after":[{"break-after":L()}],"break-before":[{"break-before":L()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...se(),D]}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Tr,D]}],basis:[{basis:z()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",D]}],grow:[{grow:A()}],shrink:[{shrink:A()}],order:[{order:["first","last","none",Tr,D]}],"grid-cols":[{"grid-cols":[Ar]}],"col-start-end":[{col:["auto",{span:["full",Tr,D]},D]}],"col-start":[{"col-start":I()}],"col-end":[{"col-end":I()}],"grid-rows":[{"grid-rows":[Ar]}],"row-start-end":[{row:["auto",{span:[Tr,D]},D]}],"row-start":[{"row-start":I()}],"row-end":[{"row-end":I()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",D]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",D]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...P()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...P(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...P(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[f]}],px:[{px:[f]}],py:[{py:[f]}],ps:[{ps:[f]}],pe:[{pe:[f]}],pt:[{pt:[f]}],pr:[{pr:[f]}],pb:[{pb:[f]}],pl:[{pl:[f]}],m:[{m:[C]}],mx:[{mx:[C]}],my:[{my:[C]}],ms:[{ms:[C]}],me:[{me:[C]}],mt:[{mt:[C]}],mr:[{mr:[C]}],mb:[{mb:[C]}],ml:[{ml:[C]}],"space-x":[{"space-x":[b]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[b]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",D,t]}],"min-w":[{"min-w":["min","max","fit",D,Qe]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[Tt]},Tt,D]}],h:[{h:[D,t,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",Qe,D]}],"max-h":[{"max-h":[D,t,"min","max","fit"]}],"font-size":[{text:["base",Tt,Ot]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Wo]}],"font-family":[{font:[Ar]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",D]}],"line-clamp":[{"line-clamp":["none",pn,Wo]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Qe,D]}],"list-image":[{"list-image":["none",D]}],"list-style-type":[{list:["none","disc","decimal",D]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[p]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[p]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...N(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Qe,Ot]}],"underline-offset":[{"underline-offset":["auto",Qe,D]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",D]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",D]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[p]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...se(),Vg]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Hg]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Bg]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[h]}],"gradient-via":[{via:[h]}],"gradient-to":[{to:[h]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[p]}],"border-style":[{border:[...N(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[p]}],"divide-style":[{divide:N()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...N()]}],"outline-offset":[{"outline-offset":[Qe,D]}],"outline-w":[{outline:[Qe,Ot]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[p]}],"ring-offset-w":[{"ring-offset":[Qe,Ot]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Tt,Wg]}],"shadow-color":[{shadow:[Ar]}],opacity:[{opacity:[p]}],"mix-blend":[{"mix-blend":he()}],"bg-blend":[{"bg-blend":he()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",Tt,D]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[v]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[p]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",D]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",D]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",D]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[Tr,D]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[_]}],"skew-y":[{"skew-y":[_]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",D]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",D]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",D]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Qe,Ot,Wo]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}function Zg(e,{cacheSize:t,prefix:n,separator:r,extend:o={},override:i={}}){fi(e,"cacheSize",t),fi(e,"prefix",n),fi(e,"separator",r);for(const l in i)Yg(e[l],i[l]);for(const l in o)Xg(e[l],o[l]);return e}function fi(e,t,n){n!==void 0&&(e[t]=n)}function Yg(e,t){if(t)for(const n in t)fi(e,n,t[n])}function Xg(e,t){if(t)for(const n in t){const r=t[n];r!==void 0&&(e[n]=(e[n]||[]).concat(r))}}function Jg(e,...t){return typeof e=="function"?Bc(Wc,e,...t):Bc(()=>Zg(Wc(),e),...t)}const qg=Jg({prefix:"fb-"});function ey(...e){return qg(ta(e))}const ty=Kp({compose:ey}),n3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z"})})),r3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M20 6H12L10 4H4A2 2 0 0 0 2 6V18A2 2 0 0 0 4 20H20A2 2 0 0 0 22 18V8A2 2 0 0 0 20 6M17 13V17H15V14H13V17H11V13H9L14 9L19 13Z"})})),o3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M15,12H17V17H15V12M15,18H17V20H15V18M23,16A7,7 0 0,1 16,23C13.62,23 11.5,21.81 10.25,20H3C1.89,20 1,19.1 1,18V6C1,4.89 1.89,4 3,4H9L11,6H19A2,2 0 0,1 21,8V11.1C22.24,12.36 23,14.09 23,16M16,11A5,5 0 0,0 11,16A5,5 0 0,0 16,21A5,5 0 0,0 21,16A5,5 0 0,0 16,11Z"})})),i3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M22,4H14L12,2H6A2,2 0 0,0 4,4V16A2,2 0 0,0 6,18H22A2,2 0 0,0 24,16V6A2,2 0 0,0 22,4M2,6H0V11H0V20A2,2 0 0,0 2,22H20V20H2V6Z"})})),qp=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M19,20H4C2.89,20 2,19.1 2,18V6C2,4.89 2.89,4 4,4H10L12,6H19A2,2 0 0,1 21,8H21L4,8V18L6.14,10H23.21L20.93,18.5C20.7,19.37 19.92,20 19,20Z"})})),l3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M19.39 10.74L11 19.13V20H4C2.9 20 2 19.11 2 18V6C2 4.89 2.89 4 4 4H10L12 6H20C21.1 6 22 6.89 22 8V10.15C21.74 10.06 21.46 10 21.17 10C20.5 10 19.87 10.26 19.39 10.74M13 19.96V22H15.04L21.17 15.88L19.13 13.83L13 19.96M22.85 13.47L21.53 12.15C21.33 11.95 21 11.95 20.81 12.15L19.83 13.13L21.87 15.17L22.85 14.19C23.05 14 23.05 13.67 22.85 13.47Z"})})),s3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"})})),a3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M19 17H22L18 21L14 17H17V3H19M11 13V15L7.67 19H11V21H5V19L8.33 15H5V13M9 3H7C5.9 3 5 3.9 5 5V11H7V9H9V11H11V5C11 3.9 10.11 3 9 3M9 7H7V5H9Z"})})),u3=g.memo(()=>vn("svg",{viewBox:"0 0 31 31",fill:"currentColor",children:[$("path",{d:"M29.3155 19.9766H19.9766V29.2537H23.0689V25.2954L27.0272 29.3155L29.2537 27.1509L25.2336 23.0689H29.3155V19.9766Z",fill:"currentColor"}),$("path",{d:"M30.8 15.3382H27.7076C27.7076 13.6683 27.3984 12.0602 26.7181 10.5141L29.5631 9.33896C30.3671 11.2562 30.8 13.2972 30.8 15.3382ZM26.2233 4.39116C24.306 2.53574 21.9558 1.1751 19.3582 0.432932L18.5542 3.46345C20.5952 4.02008 22.5124 5.13333 23.9968 6.61767L26.2233 4.39116ZM14.1012 3.09237L13.792 0C11.2562 0.24739 8.72048 1.1751 6.61767 2.65944L8.3494 5.19518C10.0193 4.02008 11.9984 3.27791 14.1012 3.09237ZM5.31888 8.10201L2.84498 6.30843C1.2988 8.41124 0.309237 10.8851 0 13.4827L3.09237 13.8538C3.33976 11.8129 4.08193 9.77189 5.31888 8.10201ZM6.30843 23.8112C4.8241 22.2651 3.77269 20.3478 3.27791 18.245L0.24739 18.9871C0.865863 21.5847 2.16466 23.9968 4.02008 25.9759L6.30843 23.8112ZM15.3382 27.7076C13.4209 27.7076 11.6273 27.2747 9.89558 26.4707L8.53494 29.2538C10.6378 30.3052 12.9261 30.8 15.3382 30.8V27.7076Z",fill:"currentColor"})]})),c3=g.memo(()=>vn("svg",{viewBox:"0 0 31 31",fill:"currentColor",children:[$("path",{d:"M15.0274 31C13.9414 31 12.8554 30.9361 12.1526 30.8722C10.4916 30.6806 9.34171 29.4668 9.02229 27.678C8.9584 27.2308 8.89452 26.8475 8.83063 26.4003C8.83063 26.3364 8.83063 26.3364 8.83063 26.3364H8.76675C8.5751 26.4003 8.38344 26.4642 8.19179 26.5281C7.93625 26.6558 7.68071 26.7197 7.36129 26.8475C5.57252 27.4863 3.78375 26.7836 2.82548 25.1865C2.05887 23.9088 1.29225 22.5672 0.525637 21.2257C-0.368747 19.6285 -0.0493245 17.712 1.35614 16.4982C1.54779 16.3704 1.73944 16.1788 1.9311 16.051C2.12275 15.9232 2.31441 15.7316 2.44217 15.6038L2.50606 15.5399C2.50606 15.5399 2.50606 15.5399 2.44217 15.476C2.25052 15.3483 2.05887 15.1566 1.9311 15.0288C1.73944 14.9011 1.54779 14.7094 1.42002 14.5817C-0.0493243 13.304 -0.432632 11.4513 0.525637 9.72643C1.22837 8.44874 1.99498 7.17105 2.7616 5.89335C3.71987 4.23235 5.57252 3.59351 7.42517 4.23235C7.61683 4.29624 7.80848 4.36012 8.00013 4.42401C8.25567 4.55178 8.51121 4.61566 8.76675 4.67955C8.83063 4.67955 8.83063 4.67955 8.83063 4.61566C8.89452 4.16847 8.9584 3.72128 9.02229 3.33797C9.34171 1.5492 10.4916 0.335394 12.1526 0.14374C13.622 -0.0479134 16.4329 -0.0479134 17.9022 0.14374C19.5632 0.335394 20.7132 1.5492 21.0326 3.33797C21.0965 3.78516 21.1604 4.16847 21.2242 4.61566C21.2242 4.67955 21.2242 4.67955 21.2242 4.67955H21.2881C21.4798 4.61566 21.6714 4.55178 21.927 4.42401C22.1825 4.36012 22.4381 4.23235 22.6297 4.16847C24.3546 3.52962 26.2711 4.23235 27.1655 5.82947C27.9321 7.10716 28.6987 8.44874 29.4654 9.79031C30.3597 11.3874 30.0403 13.304 28.6349 14.5178C28.4432 14.7094 28.2515 14.8372 28.0599 15.0288C27.8682 15.1566 27.7405 15.2844 27.5488 15.476L27.4849 15.5399C27.4849 15.5399 27.4849 15.5399 27.5488 15.6038C27.7405 15.7316 27.9321 15.9232 28.1238 16.051C28.3154 16.1788 28.4432 16.3065 28.6349 16.4982C30.1042 17.7759 30.4875 19.6285 29.5292 21.3534C28.8265 22.6311 28.0599 23.9088 27.2933 25.1865C26.335 26.8475 24.4824 27.4863 22.6297 26.8475C22.4381 26.7836 22.2464 26.7197 22.1186 26.6558C21.8631 26.5281 21.6076 26.4642 21.352 26.4003C21.2881 26.4003 21.2881 26.4003 21.2881 26.4642C21.2242 26.8475 21.1604 27.2308 21.0965 27.6141V27.7419C20.7771 29.5307 19.6271 30.7445 17.9661 30.9361C17.1356 31 16.0496 31 15.0274 31ZM8.76675 23.1422C9.34171 23.1422 9.85279 23.27 10.3639 23.5894C11.1944 24.0366 11.7693 24.931 11.8971 25.8892C11.961 26.3364 12.0249 26.7836 12.0887 27.2308C12.1526 27.7419 12.3443 27.7419 12.4082 27.7419C13.622 27.8697 16.1774 27.8697 17.3912 27.7419C17.4551 27.7419 17.6467 27.7419 17.7106 27.2308V27.103C17.7745 26.7197 17.8384 26.3364 17.9022 25.8892C18.0939 24.931 18.605 24.1005 19.4355 23.6533C20.266 23.1422 21.2242 23.0783 22.1825 23.3977C22.5019 23.5255 22.7575 23.5894 23.0769 23.7172C23.2685 23.781 23.3963 23.8449 23.588 23.9088C23.9713 24.0366 24.1629 23.9727 24.3546 23.6533C25.1212 22.3756 25.8239 21.0979 26.5267 19.8202C26.7183 19.5008 26.6544 19.2452 26.3989 18.9897C26.2711 18.8619 26.0795 18.7342 25.8878 18.6064C25.6962 18.4147 25.5045 18.287 25.3129 18.0953C24.5462 17.4565 24.1629 16.5621 24.1629 15.6038C24.1629 14.6455 24.6101 13.7512 25.3129 13.1123C25.5045 12.9207 25.6962 12.7929 25.8878 12.6651C26.0795 12.5374 26.2711 12.3457 26.3989 12.2179C26.6544 12.0263 26.7183 11.7707 26.5267 11.4513C25.76 10.1736 25.0573 8.83204 24.2907 7.55435C24.0991 7.23493 23.7796 7.23493 23.588 7.29881C23.3963 7.3627 23.1408 7.49047 22.9491 7.55435C22.6936 7.61824 22.5019 7.74601 22.2464 7.80989C21.2881 8.12931 20.266 8.06543 19.4994 7.61824C18.6689 7.17105 18.0939 6.27666 17.9661 5.31839C17.9661 4.80732 17.9022 4.36012 17.8384 3.91293C17.7745 3.40185 17.5828 3.40185 17.5189 3.40185C16.3051 3.27409 13.7497 3.27409 12.5359 3.40185C12.4721 3.40185 12.2804 3.40185 12.2165 3.91293C12.1526 4.36012 12.0887 4.80732 12.0249 5.25451C11.8332 6.21278 11.3221 7.04328 10.4916 7.49047C9.66113 7.93766 8.70286 8.06543 7.7446 7.74601C7.48906 7.61824 7.16963 7.55435 6.9141 7.42658C6.72244 7.3627 6.59467 7.29881 6.40302 7.23493C6.01971 7.10716 5.82806 7.17105 5.6364 7.49047C4.86979 8.76816 4.16706 10.0459 3.46433 11.3235C3.27267 11.643 3.33656 11.8985 3.5921 12.154C3.78375 12.2818 3.91152 12.4096 4.10317 12.5374C4.29483 12.729 4.48648 12.8568 4.67813 13.0484C5.44475 13.6873 5.82806 14.5817 5.82806 15.5399C5.82806 16.4982 5.38087 17.3926 4.67813 18.0314C4.48648 18.2231 4.29483 18.3508 4.10317 18.5425C3.91152 18.6703 3.71987 18.8619 3.5921 18.9897C3.33656 19.1813 3.27267 19.4369 3.46433 19.7563C4.23094 21.034 4.93367 22.3756 5.70029 23.6533C5.89194 23.9727 6.21137 23.9727 6.40302 23.9088C6.65856 23.8449 6.9141 23.7172 7.10575 23.6533C7.2974 23.5894 7.48906 23.5255 7.7446 23.3977C8.00013 23.2061 8.38344 23.1422 8.76675 23.1422Z",fill:"currentColor"}),$("path",{d:"M15.0273 21.4174C14.9634 21.4174 14.9634 21.4174 15.0273 21.4174C13.4302 21.4174 11.9608 20.7785 10.8748 19.6925C9.72486 18.5426 9.1499 17.0732 9.1499 15.54C9.1499 12.2819 11.8331 9.6626 15.0273 9.6626C16.6244 9.6626 18.0937 10.3014 19.1798 11.3875C20.2658 12.5374 20.9047 14.0068 20.9047 15.54C20.8408 18.7981 18.2215 21.4174 15.0273 21.4174ZM14.9634 12.8568C13.5579 12.8568 12.2802 14.0706 12.2802 15.54C12.2802 16.2427 12.5358 16.8816 13.0469 17.3926C13.5579 17.9037 14.1968 18.2231 14.8995 18.2231C16.305 18.2231 17.5827 17.0093 17.5827 15.54C17.5827 14.8373 17.3271 14.1984 16.8161 13.6873C16.3689 13.1762 15.73 12.8568 14.9634 12.8568C15.0273 12.8568 14.9634 12.8568 14.9634 12.8568Z",fill:"currentColor"})]})),f3=g.memo(()=>$("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:$("path",{d:"M19,3L13,9L15,11L22,4V3M12,12.5A0.5,0.5 0 0,1 11.5,12A0.5,0.5 0 0,1 12,11.5A0.5,0.5 0 0,1 12.5,12A0.5,0.5 0 0,1 12,12.5M6,20A2,2 0 0,1 4,18C4,16.89 4.9,16 6,16A2,2 0 0,1 8,18C8,19.11 7.1,20 6,20M6,8A2,2 0 0,1 4,6C4,4.89 4.9,4 6,4A2,2 0 0,1 8,6C8,7.11 7.1,8 6,8M9.64,7.64C9.87,7.14 10,6.59 10,6A4,4 0 0,0 6,2A4,4 0 0,0 2,6A4,4 0 0,0 6,10C6.59,10 7.14,9.87 7.64,9.64L10,12L7.64,14.36C7.14,14.13 6.59,14 6,14A4,4 0 0,0 2,18A4,4 0 0,0 6,22A4,4 0 0,0 10,18C10,17.41 9.87,16.86 9.64,16.36L12,14L19,21H22V20L9.64,7.64Z"})})),d3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M19,20H5V4H7V7H17V4H19M12,2A1,1 0 0,1 13,3A1,1 0 0,1 12,4A1,1 0 0,1 11,3A1,1 0 0,1 12,2M19,2H14.82C14.4,0.84 13.3,0 12,0C10.7,0 9.6,0.84 9.18,2H5A2,2 0 0,0 3,4V20A2,2 0 0,0 5,22H19A2,2 0 0,0 21,20V4A2,2 0 0,0 19,2Z"})})),p3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M13 19C13 19.34 13.04 19.67 13.09 20H4C2.9 20 2 19.11 2 18V6C2 4.89 2.89 4 4 4H10L12 6H20C21.1 6 22 6.89 22 8V13.81C21.12 13.3 20.1 13 19 13C15.69 13 13 15.69 13 19M22.54 16.88L21.12 15.47L19 17.59L16.88 15.47L15.47 16.88L17.59 19L15.47 21.12L16.88 22.54L19 20.41L21.12 22.54L22.54 21.12L20.41 19L22.54 16.88Z"})})),m3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M16.5,12C19,12 21,14 21,16.5C21,17.38 20.75,18.21 20.31,18.9L23.39,22L22,23.39L18.88,20.32C18.19,20.75 17.37,21 16.5,21C14,21 12,19 12,16.5C12,14 14,12 16.5,12M16.5,14A2.5,2.5 0 0,0 14,16.5A2.5,2.5 0 0,0 16.5,19A2.5,2.5 0 0,0 19,16.5A2.5,2.5 0 0,0 16.5,14M9,4L11,6H19A2,2 0 0,1 21,8V11.81C19.83,10.69 18.25,10 16.5,10A6.5,6.5 0 0,0 10,16.5C10,17.79 10.37,19 11,20H3C1.89,20 1,19.1 1,18V6C1,4.89 1.89,4 3,4H9Z"})})),h3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"})}));g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M19 13C19.34 13 19.67 13.04 20 13.09V8L14 2H6C4.89 2 4 2.89 4 4V20C4 21.11 4.89 22 6 22H13.81C13.3 21.12 13 20.1 13 19C13 15.69 15.69 13 19 13M13 3.5L18.5 9H13V3.5M23.5 20L21 23L18.5 20H20V16H22V20H23.5M19.5 18H18V22H16V18H14.5L17 15L19.5 18Z"})}));const v3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M10.5 17a1 1 0 0 1-.71-.29 1 1 0 0 1 0-1.42L13.1 12 9.92 8.69a1 1 0 0 1 0-1.41 1 1 0 0 1 1.42 0l3.86 4a1 1 0 0 1 0 1.4l-4 4a1 1 0 0 1-.7.32z"})}));g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M13,7H11V11H7V13H11V17H13V13H17V11H13V7Z"})}));g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7,13H17V11H7"})}));const g3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M13 19C13 19.34 13.04 19.67 13.09 20H4C2.9 20 2 19.11 2 18V6C2 4.89 2.89 4 4 4H10L12 6H20C21.1 6 22 6.89 22 8V13.81C21.12 13.3 20.1 13 19 13C15.69 13 13 15.69 13 19M20 18V15H18V18H15V20H18V23H20V20H23V18H20Z"})})),y3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"})})),w3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M14.5,8A1.5,1.5 0 0,1 13,6.5A1.5,1.5 0 0,1 14.5,5A1.5,1.5 0 0,1 16,6.5A1.5,1.5 0 0,1 14.5,8M9.5,8A1.5,1.5 0 0,1 8,6.5A1.5,1.5 0 0,1 9.5,5A1.5,1.5 0 0,1 11,6.5A1.5,1.5 0 0,1 9.5,8M6.5,12A1.5,1.5 0 0,1 5,10.5A1.5,1.5 0 0,1 6.5,9A1.5,1.5 0 0,1 8,10.5A1.5,1.5 0 0,1 6.5,12M12,3A9,9 0 0,0 3,12A9,9 0 0,0 12,21A1.5,1.5 0 0,0 13.5,19.5C13.5,19.11 13.35,18.76 13.11,18.5C12.88,18.23 12.73,17.88 12.73,17.5A1.5,1.5 0 0,1 14.23,16H16A5,5 0 0,0 21,11C21,6.58 16.97,3 12,3Z"})})),x3=g.memo(()=>$("svg",{viewBox:"0 0 32 37",fill:"currentColor",children:$("path",{d:"M16.1112 9.04626C16.1112 10.3089 16.1112 11.4532 16.1112 12.5975C16.1112 12.7554 16.1504 12.9526 16.1112 13.1105C16.072 13.3078 15.9936 13.5051 15.9152 13.7024C15.7193 13.6234 15.5234 13.6234 15.3666 13.5051C13.6424 12.2424 11.9183 10.9403 10.1549 9.67759C9.05767 8.88842 7.99965 8.09925 6.90244 7.27063C6.43221 6.9155 6.43221 6.79712 6.90244 6.442C9.76302 4.3507 12.5844 2.2594 15.4058 0.168098C15.5626 0.0497228 15.7977 -0.0291942 15.9152 0.0102643C16.0328 0.0891813 16.1112 0.325932 16.1112 0.523225C16.1112 1.86481 16.1112 3.16694 16.1112 4.54799C16.9341 4.62691 17.7178 4.70582 18.4623 4.8242C26.809 6.20525 32.6477 13.7418 31.9423 22.1859C31.3937 29.0912 26.3387 34.9705 19.6771 36.5488C10.6251 38.719 1.6907 32.8397 0.0448812 23.6459C-0.190235 22.3043 0.515114 21.16 1.76907 20.8838C2.98384 20.6471 4.12023 21.3968 4.35535 22.7383C4.94314 25.8556 6.4714 28.4204 9.01849 30.3144C14.9748 34.7337 23.7133 32.3268 26.613 25.461C29.4736 18.6741 25.6726 11.0981 18.5407 9.36193C17.7962 9.20409 17.0124 9.16463 16.1112 9.04626Z"})})),C3=g.memo(()=>$("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:$("path",{d:"M10 7V9H9V15H10V17H6V15H7V9H6V7H10M16 7C17.11 7 18 7.9 18 9V15C18 16.11 17.11 17 16 17H12V7M16 9H14V15H16V9Z"})})),ny=ty.button`active:fb-bg-primary active:fb-text-white fb-pointer-events-auto fb-shadow-[0_5px_9px_0_rgba(0,0,0,.16)] fb-p-[13px_30px] fb-flex fb-justify-center fb-items-center fb-bg-white fb-border-none fb-rounded-[100px] fb-text-primary fb-text-sm fb-leading-none fb-m-auto fb-outline-none fb-transition-all fb-duration-200`,ry=()=>{const e=En(t=>t.setDisplayFloatingTree);return $(ga,{children:$("button",{onClick:e,className:"button fb-m-0 fb-flex fb-items-center fb-justify-center fb-text-xl [&_svg]:fb-w-[1em] [&_svg]:fb-h-[1em]",children:$(qp,{})})})},Qc=()=>{const e=En(n=>n.setDisplayFloatingTree),t=En(n=>n.displayFloatingTree);return $("div",{className:"fb-pointer-events-none fb-left-0 fb-bottom-4 fb-fixed fb-right-0 fb-select-none fb-w-screen fb-z-biggest",children:$(ny,{onClick:e,className:t?"fb-p-[13px]":"",children:t?$(iy,{}):$(oy,{})})})},oy=()=>vn(ga,{children:[$("i",{className:"fb-mr-2 fb-text-xl fb-flex fb-items-center [&_svg]:fb-w-[1em] [&_svg]:fb-h-[1em]",children:$(qp,{})}),$("span",{children:Rt("open_filebird")})]}),iy=()=>$("i",{className:"fb-text-xl fb-flex fb-items-center [&_svg]:fb-w-[1em] [&_svg]:fb-h-[1em]",children:$("svg",{viewBox:"0 0 24 24",children:$("path",{fill:"currentColor",d:"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"})})}),{is_rtl:ly}=window.fbv_data,du=document.createElement("div");du.classList.add("filebird-portal");document.body.appendChild(du);const sy=({children:e,modal:t})=>{const n=En(r=>r.displayFloatingTree);return vn(ga,{children:[t?$(ry,{}):$(Qc,{}),mr.createPortal($(ha.StrictMode,{children:vn("div",{id:"filebird-root-mobile",className:n?"show":"",dir:ly?"rtl":"ltr",children:[e,t&&n&&$(Qc,{})]})}),du)]})};var Kc=function(){return Kc=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},Kc.apply(this,arguments)};function S3(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function e1(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),o,i=[],l;try{for(;(t===void 0||t-- >0)&&!(o=r.next()).done;)i.push(o.value)}catch(s){l={error:s}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(l)throw l.error}}return i}function _3(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var t1=function(e){return typeof e=="function"},E3=function(e){return typeof e=="string"},k3=function(e){return typeof e>"u"},ay=!1;const uy=ay;function Gc(e,t){if(e===t)return!0;for(var n=0;n<e.length;n++)if(!Object.is(e[n],t[n]))return!1;return!0}function cy(e){var t=g.useRef(e);return t.current=e,t}var fy=function(e){uy&&(t1(e)||console.error("useUnmount expected parameter is a function, got ".concat(typeof e)));var t=cy(e);g.useEffect(function(){return function(){t.current()}},[])};const n1=fy;var dy=!!(typeof window<"u"&&window.document&&window.document.createElement);const r1=dy;function oa(e,t){if(r1){if(!e)return t;var n;return t1(e)?n=e():"current"in e?n=e.current:n=e,n}}var py=function(e){var t=function(n,r,o){var i=g.useRef(!1),l=g.useRef([]),s=g.useRef([]),a=g.useRef();e(function(){var u,c=Array.isArray(o)?o:[o],d=c.map(function(m){return oa(m)});if(!i.current){i.current=!0,l.current=d,s.current=r,a.current=n();return}(d.length!==l.current.length||!Gc(d,l.current)||!Gc(r,s.current))&&((u=a.current)===null||u===void 0||u.call(a),l.current=d,s.current=r,a.current=n())}),n1(function(){var u;(u=a.current)===null||u===void 0||u.call(a),i.current=!1})};return t};const o1=py;var my=o1(g.useEffect);const hy=my;function vy(e){var t=g.useRef(0),n=e1(g.useState(e),2),r=n[0],o=n[1],i=g.useCallback(function(l){cancelAnimationFrame(t.current),t.current=requestAnimationFrame(function(){o(l)})},[]);return n1(function(){cancelAnimationFrame(t.current)}),[r,i]}var i1=function(){if(typeof Map<"u")return Map;function e(t,n){var r=-1;return t.some(function(o,i){return o[0]===n?(r=i,!0):!1}),r}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(n){var r=e(this.__entries__,n),o=this.__entries__[r];return o&&o[1]},t.prototype.set=function(n,r){var o=e(this.__entries__,n);~o?this.__entries__[o][1]=r:this.__entries__.push([n,r])},t.prototype.delete=function(n){var r=this.__entries__,o=e(r,n);~o&&r.splice(o,1)},t.prototype.has=function(n){return!!~e(this.__entries__,n)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(n,r){r===void 0&&(r=null);for(var o=0,i=this.__entries__;o<i.length;o++){var l=i[o];n.call(r,l[1],l[0])}},t}()}(),ia=typeof window<"u"&&typeof document<"u"&&window.document===document,Vi=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),gy=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(Vi):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)}}(),yy=2;function wy(e,t){var n=!1,r=!1,o=0;function i(){n&&(n=!1,e()),r&&s()}function l(){gy(i)}function s(){var a=Date.now();if(n){if(a-o<yy)return;r=!0}else n=!0,r=!1,setTimeout(l,t);o=a}return s}var xy=20,Cy=["top","right","bottom","left","width","height","size","weight"],Sy=typeof MutationObserver<"u",_y=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=wy(this.refresh.bind(this),xy)}return e.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},e.prototype.removeObserver=function(t){var n=this.observers_,r=n.indexOf(t);~r&&n.splice(r,1),!n.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var t=this.updateObservers_();t&&this.refresh()},e.prototype.updateObservers_=function(){var t=this.observers_.filter(function(n){return n.gatherActive(),n.hasActive()});return t.forEach(function(n){return n.broadcastActive()}),t.length>0},e.prototype.connect_=function(){!ia||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Sy?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){!ia||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(t){var n=t.propertyName,r=n===void 0?"":n,o=Cy.some(function(i){return!!~r.indexOf(i)});o&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),l1=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var o=r[n];Object.defineProperty(e,o,{value:t[o],enumerable:!1,writable:!1,configurable:!0})}return e},ar=function(e){var t=e&&e.ownerDocument&&e.ownerDocument.defaultView;return t||Vi},s1=gl(0,0,0,0);function Ui(e){return parseFloat(e)||0}function Zc(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(r,o){var i=e["border-"+o+"-width"];return r+Ui(i)},0)}function Ey(e){for(var t=["top","right","bottom","left"],n={},r=0,o=t;r<o.length;r++){var i=o[r],l=e["padding-"+i];n[i]=Ui(l)}return n}function ky(e){var t=e.getBBox();return gl(0,0,t.width,t.height)}function by(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return s1;var r=ar(e).getComputedStyle(e),o=Ey(r),i=o.left+o.right,l=o.top+o.bottom,s=Ui(r.width),a=Ui(r.height);if(r.boxSizing==="border-box"&&(Math.round(s+i)!==t&&(s-=Zc(r,"left","right")+i),Math.round(a+l)!==n&&(a-=Zc(r,"top","bottom")+l)),!$y(e)){var u=Math.round(s+i)-t,c=Math.round(a+l)-n;Math.abs(u)!==1&&(s-=u),Math.abs(c)!==1&&(a-=c)}return gl(o.left,o.top,s,a)}var Py=function(){return typeof SVGGraphicsElement<"u"?function(e){return e instanceof ar(e).SVGGraphicsElement}:function(e){return e instanceof ar(e).SVGElement&&typeof e.getBBox=="function"}}();function $y(e){return e===ar(e).document.documentElement}function Oy(e){return ia?Py(e)?ky(e):by(e):s1}function Ty(e){var t=e.x,n=e.y,r=e.width,o=e.height,i=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,l=Object.create(i.prototype);return l1(l,{x:t,y:n,width:r,height:o,top:n,right:t+r,bottom:o+n,left:t}),l}function gl(e,t,n,r){return{x:e,y:t,width:n,height:r}}var Ay=function(){function e(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=gl(0,0,0,0),this.target=t}return e.prototype.isActive=function(){var t=Oy(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},e}(),My=function(){function e(t,n){var r=Ty(n);l1(this,{target:t,contentRect:r})}return e}(),Ry=function(){function e(t,n,r){if(this.activeObservations_=[],this.observations_=new i1,typeof t!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=n,this.callbackCtx_=r}return e.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof ar(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(t)||(n.set(t,new Ay(t)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof ar(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(t)&&(n.delete(t),n.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(n){n.isActive()&&t.activeObservations_.push(n)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,n=this.activeObservations_.map(function(r){return new My(r.target,r.broadcastRect())});this.callback_.call(t,n,t),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),a1=typeof WeakMap<"u"?new WeakMap:new i1,u1=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=_y.getInstance(),r=new Ry(t,n,this);a1.set(this,r)}return e}();["observe","unobserve","disconnect"].forEach(function(e){u1.prototype[e]=function(){var t;return(t=a1.get(this))[e].apply(t,arguments)}});var Ly=function(){return typeof Vi.ResizeObserver<"u"?Vi.ResizeObserver:u1}(),zy=o1(g.useLayoutEffect);const Dy=zy;var Ny=r1?Dy:hy;const Fy=Ny;function Iy(e){var t=e1(vy(function(){var o=oa(e);return o?{width:o.clientWidth,height:o.clientHeight}:void 0}),2),n=t[0],r=t[1];return Fy(function(){var o=oa(e);if(o){var i=new Ly(function(l){l.forEach(function(s){var a=s.target,u=a.clientWidth,c=a.clientHeight;r({width:u,height:c})})});return i.observe(o),function(){i.disconnect()}}},[],e),n}function Ct(){return Ct=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ct.apply(this,arguments)}function ft(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function c1(e,t=[]){let n=[];function r(i,l){const s=g.createContext(l),a=n.length;n=[...n,l];function u(d){const{scope:m,children:h,...w}=d,y=(m==null?void 0:m[e][a])||s,C=g.useMemo(()=>w,Object.values(w));return g.createElement(y.Provider,{value:C},h)}function c(d,m){const h=(m==null?void 0:m[e][a])||s,w=g.useContext(h);if(w)return w;if(l!==void 0)return l;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,c]}const o=()=>{const i=n.map(l=>g.createContext(l));return function(s){const a=(s==null?void 0:s[e])||i;return g.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return o.scopeName=e,[r,jy(o,...t)]}function jy(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const l=r.reduce((s,{useScope:a,scopeName:u})=>{const d=a(i)[`__scope${u}`];return{...s,...d}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}const Hy=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],On=Hy.reduce((e,t)=>{const n=g.forwardRef((r,o)=>{const{asChild:i,...l}=r,s=i?cu:t;return g.useEffect(()=>{window[Symbol.for("radix-ui")]=!0},[]),g.createElement(s,Ue({},l,{ref:o}))});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Vy(e,t){e&&mr.flushSync(()=>e.dispatchEvent(t))}function gr(e){const t=g.useRef(e);return g.useEffect(()=>{t.current=e}),g.useMemo(()=>(...n)=>{var r;return(r=t.current)===null||r===void 0?void 0:r.call(t,...n)},[])}function Uy(e,t=globalThis==null?void 0:globalThis.document){const n=gr(e);g.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r),()=>t.removeEventListener("keydown",r)},[n,t])}const la="dismissableLayer.update",By="dismissableLayer.pointerDownOutside",Wy="dismissableLayer.focusOutside";let Yc;const Qy=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ky=g.forwardRef((e,t)=>{var n;const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:l,onInteractOutside:s,onDismiss:a,...u}=e,c=g.useContext(Qy),[d,m]=g.useState(null),h=(n=d==null?void 0:d.ownerDocument)!==null&&n!==void 0?n:globalThis==null?void 0:globalThis.document,[,w]=g.useState({}),y=hr(t,k=>m(k)),C=Array.from(c.layers),[p]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),f=C.indexOf(p),v=d?C.indexOf(d):-1,x=c.layersWithOutsidePointerEventsDisabled.size>0,S=v>=f,_=Gy(k=>{const M=k.target,T=[...c.branches].some(z=>z.contains(M));!S||T||(i==null||i(k),s==null||s(k),k.defaultPrevented||a==null||a())},h),b=Zy(k=>{const M=k.target;[...c.branches].some(z=>z.contains(M))||(l==null||l(k),s==null||s(k),k.defaultPrevented||a==null||a())},h);return Uy(k=>{v===c.layers.size-1&&(o==null||o(k),!k.defaultPrevented&&a&&(k.preventDefault(),a()))},h),g.useEffect(()=>{if(d)return r&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(Yc=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),Xc(),()=>{r&&c.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=Yc)}},[d,h,r,c]),g.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),Xc())},[d,c]),g.useEffect(()=>{const k=()=>w({});return document.addEventListener(la,k),()=>document.removeEventListener(la,k)},[]),g.createElement(On.div,Ue({},u,{ref:y,style:{pointerEvents:x?S?"auto":"none":void 0,...e.style},onFocusCapture:ft(e.onFocusCapture,b.onFocusCapture),onBlurCapture:ft(e.onBlurCapture,b.onBlurCapture),onPointerDownCapture:ft(e.onPointerDownCapture,_.onPointerDownCapture)}))});function Gy(e,t=globalThis==null?void 0:globalThis.document){const n=gr(e),r=g.useRef(!1),o=g.useRef(()=>{});return g.useEffect(()=>{const i=s=>{if(s.target&&!r.current){let u=function(){f1(By,n,a,{discrete:!0})};const a={originalEvent:s};s.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(l),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Zy(e,t=globalThis==null?void 0:globalThis.document){const n=gr(e),r=g.useRef(!1);return g.useEffect(()=>{const o=i=>{i.target&&!r.current&&f1(Wy,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Xc(){const e=new CustomEvent(la);document.dispatchEvent(e)}function f1(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Vy(o,i):o.dispatchEvent(i)}const ur=globalThis!=null&&globalThis.document?g.useLayoutEffect:()=>{},Yy=r0["useId".toString()]||(()=>{});let Xy=0;function Jy(e){const[t,n]=g.useState(Yy());return ur(()=>{e||n(r=>r??String(Xy++))},[e]),e||(t?`radix-${t}`:"")}const qy=["top","right","bottom","left"],Zt=Math.min,Pe=Math.max,Bi=Math.round,Qo=Math.floor,Yt=e=>({x:e,y:e}),e2={left:"right",right:"left",bottom:"top",top:"bottom"},t2={start:"end",end:"start"};function sa(e,t,n){return Pe(e,Zt(t,n))}function St(e,t){return typeof e=="function"?e(t):e}function _t(e){return e.split("-")[0]}function yr(e){return e.split("-")[1]}function pu(e){return e==="x"?"y":"x"}function mu(e){return e==="y"?"height":"width"}function wr(e){return["top","bottom"].includes(_t(e))?"y":"x"}function hu(e){return pu(wr(e))}function n2(e,t,n){n===void 0&&(n=!1);const r=yr(e),o=hu(e),i=mu(o);let l=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=Wi(l)),[l,Wi(l)]}function r2(e){const t=Wi(e);return[aa(e),t,aa(t)]}function aa(e){return e.replace(/start|end/g,t=>t2[t])}function o2(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],l=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:l;default:return[]}}function i2(e,t,n,r){const o=yr(e);let i=o2(_t(e),n==="start",r);return o&&(i=i.map(l=>l+"-"+o),t&&(i=i.concat(i.map(aa)))),i}function Wi(e){return e.replace(/left|right|bottom|top/g,t=>e2[t])}function l2(e){return{top:0,right:0,bottom:0,left:0,...e}}function d1(e){return typeof e!="number"?l2(e):{top:e,right:e,bottom:e,left:e}}function Qi(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function Jc(e,t,n){let{reference:r,floating:o}=e;const i=wr(t),l=hu(t),s=mu(l),a=_t(t),u=i==="y",c=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,m=r[s]/2-o[s]/2;let h;switch(a){case"top":h={x:c,y:r.y-o.height};break;case"bottom":h={x:c,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:d};break;case"left":h={x:r.x-o.width,y:d};break;default:h={x:r.x,y:r.y}}switch(yr(t)){case"start":h[l]-=m*(n&&u?-1:1);break;case"end":h[l]+=m*(n&&u?-1:1);break}return h}const s2=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,s=i.filter(Boolean),a=await(l.isRTL==null?void 0:l.isRTL(t));let u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=Jc(u,r,a),m=r,h={},w=0;for(let y=0;y<s.length;y++){const{name:C,fn:p}=s[y],{x:f,y:v,data:x,reset:S}=await p({x:c,y:d,initialPlacement:r,placement:m,strategy:o,middlewareData:h,rects:u,platform:l,elements:{reference:e,floating:t}});if(c=f??c,d=v??d,h={...h,[C]:{...h[C],...x}},S&&w<=50){w++,typeof S=="object"&&(S.placement&&(m=S.placement),S.rects&&(u=S.rects===!0?await l.getElementRects({reference:e,floating:t,strategy:o}):S.rects),{x:c,y:d}=Jc(u,m,a)),y=-1;continue}}return{x:c,y:d,placement:m,strategy:o,middlewareData:h}};async function po(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:l,elements:s,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:m=!1,padding:h=0}=St(t,e),w=d1(h),C=s[m?d==="floating"?"reference":"floating":d],p=Qi(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(C)))==null||n?C:C.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:a})),f=d==="floating"?{...l.floating,x:r,y:o}:l.reference,v=await(i.getOffsetParent==null?void 0:i.getOffsetParent(s.floating)),x=await(i.isElement==null?void 0:i.isElement(v))?await(i.getScale==null?void 0:i.getScale(v))||{x:1,y:1}:{x:1,y:1},S=Qi(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:f,offsetParent:v,strategy:a}):f);return{top:(p.top-S.top+w.top)/x.y,bottom:(S.bottom-p.bottom+w.bottom)/x.y,left:(p.left-S.left+w.left)/x.x,right:(S.right-p.right+w.right)/x.x}}const qc=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:l,elements:s}=t,{element:a,padding:u=0}=St(e,t)||{};if(a==null)return{};const c=d1(u),d={x:n,y:r},m=hu(o),h=mu(m),w=await l.getDimensions(a),y=m==="y",C=y?"top":"left",p=y?"bottom":"right",f=y?"clientHeight":"clientWidth",v=i.reference[h]+i.reference[m]-d[m]-i.floating[h],x=d[m]-i.reference[m],S=await(l.getOffsetParent==null?void 0:l.getOffsetParent(a));let _=S?S[f]:0;(!_||!await(l.isElement==null?void 0:l.isElement(S)))&&(_=s.floating[f]||i.floating[h]);const b=v/2-x/2,k=_/2-w[h]/2-1,M=Zt(c[C],k),T=Zt(c[p],k),z=M,R=_-w[h]-T,U=_/2-w[h]/2+b,I=sa(z,U,R),N=yr(o)!=null&&U!=I&&i.reference[h]/2-(U<z?M:T)-w[h]/2<0?U<z?z-U:R-U:0;return{[m]:d[m]-N,data:{[m]:I,centerOffset:U-I+N}}}}),a2=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n;const{placement:r,middlewareData:o,rects:i,initialPlacement:l,platform:s,elements:a}=t,{mainAxis:u=!0,crossAxis:c=!0,fallbackPlacements:d,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:w=!0,...y}=St(e,t),C=_t(r),p=_t(l)===l,f=await(s.isRTL==null?void 0:s.isRTL(a.floating)),v=d||(p||!w?[Wi(l)]:r2(l));!d&&h!=="none"&&v.push(...i2(l,w,h,f));const x=[l,...v],S=await po(t,y),_=[];let b=((n=o.flip)==null?void 0:n.overflows)||[];if(u&&_.push(S[C]),c){const z=n2(r,i,f);_.push(S[z[0]],S[z[1]])}if(b=[...b,{placement:r,overflows:_}],!_.every(z=>z<=0)){var k,M;const z=(((k=o.flip)==null?void 0:k.index)||0)+1,R=x[z];if(R)return{data:{index:z,overflows:b},reset:{placement:R}};let U=(M=b.filter(I=>I.overflows[0]<=0).sort((I,se)=>I.overflows[1]-se.overflows[1])[0])==null?void 0:M.placement;if(!U)switch(m){case"bestFit":{var T;const I=(T=b.map(se=>[se.placement,se.overflows.filter(N=>N>0).reduce((N,he)=>N+he,0)]).sort((se,N)=>se[1]-N[1])[0])==null?void 0:T[0];I&&(U=I);break}case"initialPlacement":U=l;break}if(r!==U)return{reset:{placement:U}}}return{}}}};function ef(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function tf(e){return qy.some(t=>e[t]>=0)}const u2=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=St(e,t);switch(r){case"referenceHidden":{const i=await po(t,{...o,elementContext:"reference"}),l=ef(i,n.reference);return{data:{referenceHiddenOffsets:l,referenceHidden:tf(l)}}}case"escaped":{const i=await po(t,{...o,altBoundary:!0}),l=ef(i,n.floating);return{data:{escapedOffsets:l,escaped:tf(l)}}}default:return{}}}}};async function c2(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),l=_t(n),s=yr(n),a=wr(n)==="y",u=["left","top"].includes(l)?-1:1,c=i&&a?-1:1,d=St(t,e);let{mainAxis:m,crossAxis:h,alignmentAxis:w}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...d};return s&&typeof w=="number"&&(h=s==="end"?w*-1:w),a?{x:h*c,y:m*u}:{x:m*u,y:h*c}}const f2=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){const{x:n,y:r}=t,o=await c2(t,e);return{x:n+o.x,y:r+o.y,data:o}}}},d2=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:s={fn:C=>{let{x:p,y:f}=C;return{x:p,y:f}}},...a}=St(e,t),u={x:n,y:r},c=await po(t,a),d=wr(_t(o)),m=pu(d);let h=u[m],w=u[d];if(i){const C=m==="y"?"top":"left",p=m==="y"?"bottom":"right",f=h+c[C],v=h-c[p];h=sa(f,h,v)}if(l){const C=d==="y"?"top":"left",p=d==="y"?"bottom":"right",f=w+c[C],v=w-c[p];w=sa(f,w,v)}const y=s.fn({...t,[m]:h,[d]:w});return{...y,data:{x:y.x-n,y:y.y-r}}}}},p2=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:s=0,mainAxis:a=!0,crossAxis:u=!0}=St(e,t),c={x:n,y:r},d=wr(o),m=pu(d);let h=c[m],w=c[d];const y=St(s,t),C=typeof y=="number"?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(a){const v=m==="y"?"height":"width",x=i.reference[m]-i.floating[v]+C.mainAxis,S=i.reference[m]+i.reference[v]-C.mainAxis;h<x?h=x:h>S&&(h=S)}if(u){var p,f;const v=m==="y"?"width":"height",x=["top","left"].includes(_t(o)),S=i.reference[d]-i.floating[v]+(x&&((p=l.offset)==null?void 0:p[d])||0)+(x?0:C.crossAxis),_=i.reference[d]+i.reference[v]+(x?0:((f=l.offset)==null?void 0:f[d])||0)-(x?C.crossAxis:0);w<S?w=S:w>_&&(w=_)}return{[m]:h,[d]:w}}}},m2=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){const{placement:n,rects:r,platform:o,elements:i}=t,{apply:l=()=>{},...s}=St(e,t),a=await po(t,s),u=_t(n),c=yr(n),d=wr(n)==="y",{width:m,height:h}=r.floating;let w,y;u==="top"||u==="bottom"?(w=u,y=c===(await(o.isRTL==null?void 0:o.isRTL(i.floating))?"start":"end")?"left":"right"):(y=u,w=c==="end"?"top":"bottom");const C=h-a[w],p=m-a[y],f=!t.middlewareData.shift;let v=C,x=p;if(d){const _=m-a.left-a.right;x=c||f?Zt(p,_):_}else{const _=h-a.top-a.bottom;v=c||f?Zt(C,_):_}if(f&&!c){const _=Pe(a.left,0),b=Pe(a.right,0),k=Pe(a.top,0),M=Pe(a.bottom,0);d?x=m-2*(_!==0||b!==0?_+b:Pe(a.left,a.right)):v=h-2*(k!==0||M!==0?k+M:Pe(a.top,a.bottom))}await l({...t,availableWidth:x,availableHeight:v});const S=await o.getDimensions(i.floating);return m!==S.width||h!==S.height?{reset:{rects:!0}}:{}}}};function Xt(e){return p1(e)?(e.nodeName||"").toLowerCase():"#document"}function Ae(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function bt(e){var t;return(t=(p1(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function p1(e){return e instanceof Node||e instanceof Ae(e).Node}function Et(e){return e instanceof Element||e instanceof Ae(e).Element}function lt(e){return e instanceof HTMLElement||e instanceof Ae(e).HTMLElement}function nf(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ae(e).ShadowRoot}function Co(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Be(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function h2(e){return["table","td","th"].includes(Xt(e))}function vu(e){const t=gu(),n=Be(e);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function v2(e){let t=cr(e);for(;lt(t)&&!yl(t);){if(vu(t))return t;t=cr(t)}return null}function gu(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function yl(e){return["html","body","#document"].includes(Xt(e))}function Be(e){return Ae(e).getComputedStyle(e)}function wl(e){return Et(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function cr(e){if(Xt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||nf(e)&&e.host||bt(e);return nf(t)?t.host:t}function m1(e){const t=cr(e);return yl(t)?e.ownerDocument?e.ownerDocument.body:e.body:lt(t)&&Co(t)?t:m1(t)}function Ki(e,t){var n;t===void 0&&(t=[]);const r=m1(e),o=r===((n=e.ownerDocument)==null?void 0:n.body),i=Ae(r);return o?t.concat(i,i.visualViewport||[],Co(r)?r:[]):t.concat(r,Ki(r))}function h1(e){const t=Be(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=lt(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,s=Bi(n)!==i||Bi(r)!==l;return s&&(n=i,r=l),{width:n,height:r,$:s}}function yu(e){return Et(e)?e:e.contextElement}function Xn(e){const t=yu(e);if(!lt(t))return Yt(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=h1(t);let l=(i?Bi(n.width):n.width)/r,s=(i?Bi(n.height):n.height)/o;return(!l||!Number.isFinite(l))&&(l=1),(!s||!Number.isFinite(s))&&(s=1),{x:l,y:s}}const g2=Yt(0);function v1(e){const t=Ae(e);return!gu()||!t.visualViewport?g2:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function y2(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ae(e)?!1:t}function kn(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=yu(e);let l=Yt(1);t&&(r?Et(r)&&(l=Xn(r)):l=Xn(e));const s=y2(i,n,r)?v1(i):Yt(0);let a=(o.left+s.x)/l.x,u=(o.top+s.y)/l.y,c=o.width/l.x,d=o.height/l.y;if(i){const m=Ae(i),h=r&&Et(r)?Ae(r):r;let w=m.frameElement;for(;w&&r&&h!==m;){const y=Xn(w),C=w.getBoundingClientRect(),p=Be(w),f=C.left+(w.clientLeft+parseFloat(p.paddingLeft))*y.x,v=C.top+(w.clientTop+parseFloat(p.paddingTop))*y.y;a*=y.x,u*=y.y,c*=y.x,d*=y.y,a+=f,u+=v,w=Ae(w).frameElement}}return Qi({width:c,height:d,x:a,y:u})}function w2(e){let{rect:t,offsetParent:n,strategy:r}=e;const o=lt(n),i=bt(n);if(n===i)return t;let l={scrollLeft:0,scrollTop:0},s=Yt(1);const a=Yt(0);if((o||!o&&r!=="fixed")&&((Xt(n)!=="body"||Co(i))&&(l=wl(n)),lt(n))){const u=kn(n);s=Xn(n),a.x=u.x+n.clientLeft,a.y=u.y+n.clientTop}return{width:t.width*s.x,height:t.height*s.y,x:t.x*s.x-l.scrollLeft*s.x+a.x,y:t.y*s.y-l.scrollTop*s.y+a.y}}function x2(e){return Array.from(e.getClientRects())}function g1(e){return kn(bt(e)).left+wl(e).scrollLeft}function C2(e){const t=bt(e),n=wl(e),r=e.ownerDocument.body,o=Pe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Pe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let l=-n.scrollLeft+g1(e);const s=-n.scrollTop;return Be(r).direction==="rtl"&&(l+=Pe(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:s}}function S2(e,t){const n=Ae(e),r=bt(e),o=n.visualViewport;let i=r.clientWidth,l=r.clientHeight,s=0,a=0;if(o){i=o.width,l=o.height;const u=gu();(!u||u&&t==="fixed")&&(s=o.offsetLeft,a=o.offsetTop)}return{width:i,height:l,x:s,y:a}}function _2(e,t){const n=kn(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=lt(e)?Xn(e):Yt(1),l=e.clientWidth*i.x,s=e.clientHeight*i.y,a=o*i.x,u=r*i.y;return{width:l,height:s,x:a,y:u}}function rf(e,t,n){let r;if(t==="viewport")r=S2(e,n);else if(t==="document")r=C2(bt(e));else if(Et(t))r=_2(t,n);else{const o=v1(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Qi(r)}function y1(e,t){const n=cr(e);return n===t||!Et(n)||yl(n)?!1:Be(n).position==="fixed"||y1(n,t)}function E2(e,t){const n=t.get(e);if(n)return n;let r=Ki(e).filter(s=>Et(s)&&Xt(s)!=="body"),o=null;const i=Be(e).position==="fixed";let l=i?cr(e):e;for(;Et(l)&&!yl(l);){const s=Be(l),a=vu(l);!a&&s.position==="fixed"&&(o=null),(i?!a&&!o:!a&&s.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Co(l)&&!a&&y1(e,l))?r=r.filter(c=>c!==l):o=s,l=cr(l)}return t.set(e,r),r}function k2(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const l=[...n==="clippingAncestors"?E2(t,this._c):[].concat(n),r],s=l[0],a=l.reduce((u,c)=>{const d=rf(t,c,o);return u.top=Pe(d.top,u.top),u.right=Zt(d.right,u.right),u.bottom=Zt(d.bottom,u.bottom),u.left=Pe(d.left,u.left),u},rf(t,s,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function b2(e){return h1(e)}function P2(e,t,n){const r=lt(t),o=bt(t),i=n==="fixed",l=kn(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const a=Yt(0);if(r||!r&&!i)if((Xt(t)!=="body"||Co(o))&&(s=wl(t)),r){const u=kn(t,!0,i,t);a.x=u.x+t.clientLeft,a.y=u.y+t.clientTop}else o&&(a.x=g1(o));return{x:l.left+s.scrollLeft-a.x,y:l.top+s.scrollTop-a.y,width:l.width,height:l.height}}function of(e,t){return!lt(e)||Be(e).position==="fixed"?null:t?t(e):e.offsetParent}function w1(e,t){const n=Ae(e);if(!lt(e))return n;let r=of(e,t);for(;r&&h2(r)&&Be(r).position==="static";)r=of(r,t);return r&&(Xt(r)==="html"||Xt(r)==="body"&&Be(r).position==="static"&&!vu(r))?n:r||v2(e)||n}const $2=async function(e){let{reference:t,floating:n,strategy:r}=e;const o=this.getOffsetParent||w1,i=this.getDimensions;return{reference:P2(t,await o(n),r),floating:{x:0,y:0,...await i(n)}}};function O2(e){return Be(e).direction==="rtl"}const T2={convertOffsetParentRelativeRectToViewportRelativeRect:w2,getDocumentElement:bt,getClippingRect:k2,getOffsetParent:w1,getElementRects:$2,getClientRects:x2,getDimensions:b2,getScale:Xn,isElement:Et,isRTL:O2};function A2(e,t){let n=null,r;const o=bt(e);function i(){clearTimeout(r),n&&n.disconnect(),n=null}function l(s,a){s===void 0&&(s=!1),a===void 0&&(a=1),i();const{left:u,top:c,width:d,height:m}=e.getBoundingClientRect();if(s||t(),!d||!m)return;const h=Qo(c),w=Qo(o.clientWidth-(u+d)),y=Qo(o.clientHeight-(c+m)),C=Qo(u),f={rootMargin:-h+"px "+-w+"px "+-y+"px "+-C+"px",threshold:Pe(0,Zt(1,a))||1};let v=!0;function x(S){const _=S[0].intersectionRatio;if(_!==a){if(!v)return l();_?l(!1,_):r=setTimeout(()=>{l(!1,1e-7)},100)}v=!1}try{n=new IntersectionObserver(x,{...f,root:o.ownerDocument})}catch{n=new IntersectionObserver(x,f)}n.observe(e)}return l(!0),i}function M2(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:l=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=yu(e),c=o||i?[...u?Ki(u):[],...Ki(t)]:[];c.forEach(p=>{o&&p.addEventListener("scroll",n,{passive:!0}),i&&p.addEventListener("resize",n)});const d=u&&s?A2(u,n):null;let m=-1,h=null;l&&(h=new ResizeObserver(p=>{let[f]=p;f&&f.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{h&&h.observe(t)})),n()}),u&&!a&&h.observe(u),h.observe(t));let w,y=a?kn(e):null;a&&C();function C(){const p=kn(e);y&&(p.x!==y.x||p.y!==y.y||p.width!==y.width||p.height!==y.height)&&n(),y=p,w=requestAnimationFrame(C)}return n(),()=>{c.forEach(p=>{o&&p.removeEventListener("scroll",n),i&&p.removeEventListener("resize",n)}),d&&d(),h&&h.disconnect(),h=null,a&&cancelAnimationFrame(w)}}const R2=(e,t,n)=>{const r=new Map,o={platform:T2,...n},i={...o.platform,_c:r};return s2(e,t,{...o,platform:i})},L2=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?qc({element:r.current,padding:o}).fn(n):{}:r?qc({element:r,padding:o}).fn(n):{}}}};var di=typeof document<"u"?g.useLayoutEffect:g.useEffect;function Gi(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!=t.length)return!1;for(r=n;r--!==0;)if(!Gi(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Gi(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function x1(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function lf(e,t){const n=x1(e);return Math.round(t*n)/n}function sf(e){const t=g.useRef(e);return di(()=>{t.current=e}),t}function z2(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:s=!0,whileElementsMounted:a,open:u}=e,[c,d]=g.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,h]=g.useState(r);Gi(m,r)||h(r);const[w,y]=g.useState(null),[C,p]=g.useState(null),f=g.useCallback(N=>{N!=_.current&&(_.current=N,y(N))},[y]),v=g.useCallback(N=>{N!==b.current&&(b.current=N,p(N))},[p]),x=i||w,S=l||C,_=g.useRef(null),b=g.useRef(null),k=g.useRef(c),M=sf(a),T=sf(o),z=g.useCallback(()=>{if(!_.current||!b.current)return;const N={placement:t,strategy:n,middleware:m};T.current&&(N.platform=T.current),R2(_.current,b.current,N).then(he=>{const P={...he,isPositioned:!0};R.current&&!Gi(k.current,P)&&(k.current=P,mr.flushSync(()=>{d(P)}))})},[m,t,n,T]);di(()=>{u===!1&&k.current.isPositioned&&(k.current.isPositioned=!1,d(N=>({...N,isPositioned:!1})))},[u]);const R=g.useRef(!1);di(()=>(R.current=!0,()=>{R.current=!1}),[]),di(()=>{if(x&&(_.current=x),S&&(b.current=S),x&&S){if(M.current)return M.current(x,S,z);z()}},[x,S,z,M]);const U=g.useMemo(()=>({reference:_,floating:b,setReference:f,setFloating:v}),[f,v]),I=g.useMemo(()=>({reference:x,floating:S}),[x,S]),se=g.useMemo(()=>{const N={position:n,left:0,top:0};if(!I.floating)return N;const he=lf(I.floating,c.x),P=lf(I.floating,c.y);return s?{...N,transform:"translate("+he+"px, "+P+"px)",...x1(I.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:he,top:P}},[n,s,I.floating,c.x,c.y]);return g.useMemo(()=>({...c,update:z,refs:U,elements:I,floatingStyles:se}),[c,z,U,I,se])}const D2=g.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return g.createElement(On.svg,Ue({},i,{ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none"}),e.asChild?n:g.createElement("polygon",{points:"0,0 30,0 15,10"}))}),N2=D2;function F2(e){const[t,n]=g.useState(void 0);return ur(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let l,s;if("borderBoxSize"in i){const a=i.borderBoxSize,u=Array.isArray(a)?a[0]:a;l=u.inlineSize,s=u.blockSize}else l=e.offsetWidth,s=e.offsetHeight;n({width:l,height:s})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}const C1="Popper",[S1,_1]=c1(C1),[I2,E1]=S1(C1),j2=e=>{const{__scopePopper:t,children:n}=e,[r,o]=g.useState(null);return g.createElement(I2,{scope:t,anchor:r,onAnchorChange:o},n)},H2="PopperAnchor",V2=g.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=E1(H2,n),l=g.useRef(null),s=hr(t,l);return g.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||l.current)}),r?null:g.createElement(On.div,Ue({},o,{ref:s}))}),k1="PopperContent",[U2,B2]=S1(k1),W2=g.forwardRef((e,t)=>{var n,r,o,i,l,s,a,u;const{__scopePopper:c,side:d="bottom",sideOffset:m=0,align:h="center",alignOffset:w=0,arrowPadding:y=0,avoidCollisions:C=!0,collisionBoundary:p=[],collisionPadding:f=0,sticky:v="partial",hideWhenDetached:x=!1,updatePositionStrategy:S="optimized",onPlaced:_,...b}=e,k=E1(k1,c),[M,T]=g.useState(null),z=hr(t,xr=>T(xr)),[R,U]=g.useState(null),I=F2(R),se=(n=I==null?void 0:I.width)!==null&&n!==void 0?n:0,N=(r=I==null?void 0:I.height)!==null&&r!==void 0?r:0,he=d+(h!=="center"?"-"+h:""),P=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},A=Array.isArray(p)?p:[p],L=A.length>0,H={padding:P,boundary:A.filter(Z2),altBoundary:L},{refs:W,floatingStyles:nn,placement:st,isPositioned:Pt,middlewareData:xe}=z2({strategy:"fixed",placement:he,whileElementsMounted:(...xr)=>M2(...xr,{animationFrame:S==="always"}),elements:{reference:k.anchor},middleware:[f2({mainAxis:m+N,alignmentAxis:w}),C&&d2({mainAxis:!0,crossAxis:!1,limiter:v==="partial"?p2():void 0,...H}),C&&a2({...H}),m2({...H,apply:({elements:xr,rects:Su,availableWidth:N1,availableHeight:F1})=>{const{width:I1,height:j1}=Su.reference,Eo=xr.floating.style;Eo.setProperty("--radix-popper-available-width",`${N1}px`),Eo.setProperty("--radix-popper-available-height",`${F1}px`),Eo.setProperty("--radix-popper-anchor-width",`${I1}px`),Eo.setProperty("--radix-popper-anchor-height",`${j1}px`)}}),R&&L2({element:R,padding:y}),Y2({arrowWidth:se,arrowHeight:N}),x&&u2({strategy:"referenceHidden",...H})]}),[rn,A1]=b1(st),_o=gr(_);ur(()=>{Pt&&(_o==null||_o())},[Pt,_o]);const M1=(o=xe.arrow)===null||o===void 0?void 0:o.x,R1=(i=xe.arrow)===null||i===void 0?void 0:i.y,L1=((l=xe.arrow)===null||l===void 0?void 0:l.centerOffset)!==0,[z1,D1]=g.useState();return ur(()=>{M&&D1(window.getComputedStyle(M).zIndex)},[M]),g.createElement("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...nn,transform:Pt?nn.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:z1,"--radix-popper-transform-origin":[(s=xe.transformOrigin)===null||s===void 0?void 0:s.x,(a=xe.transformOrigin)===null||a===void 0?void 0:a.y].join(" ")},dir:e.dir},g.createElement(U2,{scope:c,placedSide:rn,onArrowChange:U,arrowX:M1,arrowY:R1,shouldHideArrow:L1},g.createElement(On.div,Ue({"data-side":rn,"data-align":A1},b,{ref:z,style:{...b.style,animation:Pt?void 0:"none",opacity:(u=xe.hide)!==null&&u!==void 0&&u.referenceHidden?0:void 0}}))))}),Q2="PopperArrow",K2={top:"bottom",right:"left",bottom:"top",left:"right"},G2=g.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=B2(Q2,r),l=K2[i.placedSide];return g.createElement("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0}},g.createElement(N2,Ue({},o,{ref:n,style:{...o.style,display:"block"}})))});function Z2(e){return e!==null}const Y2=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;const{placement:s,rects:a,middlewareData:u}=t,d=((n=u.arrow)===null||n===void 0?void 0:n.centerOffset)!==0,m=d?0:e.arrowWidth,h=d?0:e.arrowHeight,[w,y]=b1(s),C={start:"0%",center:"50%",end:"100%"}[y],p=((r=(o=u.arrow)===null||o===void 0?void 0:o.x)!==null&&r!==void 0?r:0)+m/2,f=((i=(l=u.arrow)===null||l===void 0?void 0:l.y)!==null&&i!==void 0?i:0)+h/2;let v="",x="";return w==="bottom"?(v=d?C:`${p}px`,x=`${-h}px`):w==="top"?(v=d?C:`${p}px`,x=`${a.floating.height+h}px`):w==="right"?(v=`${-h}px`,x=d?C:`${f}px`):w==="left"&&(v=`${a.floating.width+h}px`,x=d?C:`${f}px`),{data:{x:v,y:x}}}});function b1(e){const[t,n="center"]=e.split("-");return[t,n]}const X2=j2,J2=V2,q2=W2,ew=G2,tw=g.forwardRef((e,t)=>{var n;const{container:r=globalThis==null||(n=globalThis.document)===null||n===void 0?void 0:n.body,...o}=e;return r?ph.createPortal(g.createElement(On.div,Ue({},o,{ref:t})),r):null});function nw(e,t){return g.useReducer((n,r)=>{const o=t[n][r];return o??n},e)}const wu=e=>{const{present:t,children:n}=e,r=rw(t),o=typeof n=="function"?n({present:r.isPresent}):g.Children.only(n),i=hr(r.ref,o.ref);return typeof n=="function"||r.isPresent?g.cloneElement(o,{ref:i}):null};wu.displayName="Presence";function rw(e){const[t,n]=g.useState(),r=g.useRef({}),o=g.useRef(e),i=g.useRef("none"),l=e?"mounted":"unmounted",[s,a]=nw(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const u=Ko(r.current);i.current=s==="mounted"?u:"none"},[s]),ur(()=>{const u=r.current,c=o.current;if(c!==e){const m=i.current,h=Ko(u);e?a("MOUNT"):h==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(c&&m!==h?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),ur(()=>{if(t){const u=d=>{const h=Ko(r.current).includes(d.animationName);d.target===t&&h&&mr.flushSync(()=>a("ANIMATION_END"))},c=d=>{d.target===t&&(i.current=Ko(r.current))};return t.addEventListener("animationstart",c),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{t.removeEventListener("animationstart",c),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:g.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Ko(e){return(e==null?void 0:e.animationName)||"none"}function ow({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=iw({defaultProp:t,onChange:n}),i=e!==void 0,l=i?e:r,s=gr(n),a=g.useCallback(u=>{if(i){const d=typeof u=="function"?u(e):u;d!==e&&s(d)}else o(u)},[i,e,o,s]);return[l,a]}function iw({defaultProp:e,onChange:t}){const n=g.useState(e),[r]=n,o=g.useRef(r),i=gr(t);return g.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}const lw=g.forwardRef((e,t)=>g.createElement(On.span,Ue({},e,{ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}))),sw=lw,[xl,b3]=c1("Tooltip",[_1]),Cl=_1(),aw="TooltipProvider",uw=700,ua="tooltip.open",[cw,xu]=xl(aw),fw=e=>{const{__scopeTooltip:t,delayDuration:n=uw,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,[l,s]=g.useState(!0),a=g.useRef(!1),u=g.useRef(0);return g.useEffect(()=>{const c=u.current;return()=>window.clearTimeout(c)},[]),g.createElement(cw,{scope:t,isOpenDelayed:l,delayDuration:n,onOpen:g.useCallback(()=>{window.clearTimeout(u.current),s(!1)},[]),onClose:g.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>s(!0),r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:g.useCallback(c=>{a.current=c},[]),disableHoverableContent:o},i)},Cu="Tooltip",[dw,So]=xl(Cu),pw=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:i,disableHoverableContent:l,delayDuration:s}=e,a=xu(Cu,e.__scopeTooltip),u=Cl(t),[c,d]=g.useState(null),m=Jy(),h=g.useRef(0),w=l??a.disableHoverableContent,y=s??a.delayDuration,C=g.useRef(!1),[p=!1,f]=ow({prop:r,defaultProp:o,onChange:b=>{b?(a.onOpen(),document.dispatchEvent(new CustomEvent(ua))):a.onClose(),i==null||i(b)}}),v=g.useMemo(()=>p?C.current?"delayed-open":"instant-open":"closed",[p]),x=g.useCallback(()=>{window.clearTimeout(h.current),C.current=!1,f(!0)},[f]),S=g.useCallback(()=>{window.clearTimeout(h.current),f(!1)},[f]),_=g.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>{C.current=!0,f(!0)},y)},[y,f]);return g.useEffect(()=>()=>window.clearTimeout(h.current),[]),g.createElement(X2,u,g.createElement(dw,{scope:t,contentId:m,open:p,stateAttribute:v,trigger:c,onTriggerChange:d,onTriggerEnter:g.useCallback(()=>{a.isOpenDelayed?_():x()},[a.isOpenDelayed,_,x]),onTriggerLeave:g.useCallback(()=>{w?S():window.clearTimeout(h.current)},[S,w]),onOpen:x,onClose:S,disableHoverableContent:w},n))},af="TooltipTrigger",mw=g.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=So(af,n),i=xu(af,n),l=Cl(n),s=g.useRef(null),a=hr(t,s,o.onTriggerChange),u=g.useRef(!1),c=g.useRef(!1),d=g.useCallback(()=>u.current=!1,[]);return g.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),g.createElement(J2,Ct({asChild:!0},l),g.createElement(On.button,Ct({"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute},r,{ref:a,onPointerMove:ft(e.onPointerMove,m=>{m.pointerType!=="touch"&&!c.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),c.current=!0)}),onPointerLeave:ft(e.onPointerLeave,()=>{o.onTriggerLeave(),c.current=!1}),onPointerDown:ft(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:ft(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ft(e.onBlur,o.onClose),onClick:ft(e.onClick,o.onClose)})))}),P1="TooltipPortal",[hw,vw]=xl(P1,{forceMount:void 0}),gw=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=So(P1,t);return g.createElement(hw,{scope:t,forceMount:n},g.createElement(wu,{present:n||i.open},g.createElement(tw,{asChild:!0,container:o},r)))},mo="TooltipContent",yw=g.forwardRef((e,t)=>{const n=vw(mo,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=So(mo,e.__scopeTooltip);return g.createElement(wu,{present:r||l.open},l.disableHoverableContent?g.createElement($1,Ct({side:o},i,{ref:t})):g.createElement(ww,Ct({side:o},i,{ref:t})))}),ww=g.forwardRef((e,t)=>{const n=So(mo,e.__scopeTooltip),r=xu(mo,e.__scopeTooltip),o=g.useRef(null),i=hr(t,o),[l,s]=g.useState(null),{trigger:a,onClose:u}=n,c=o.current,{onPointerInTransitChange:d}=r,m=g.useCallback(()=>{s(null),d(!1)},[d]),h=g.useCallback((w,y)=>{const C=w.currentTarget,p={x:w.clientX,y:w.clientY},f=Ew(p,C.getBoundingClientRect()),v=kw(p,f),x=bw(y.getBoundingClientRect()),S=$w([...v,...x]);s(S),d(!0)},[d]);return g.useEffect(()=>()=>m(),[m]),g.useEffect(()=>{if(a&&c){const w=C=>h(C,c),y=C=>h(C,a);return a.addEventListener("pointerleave",w),c.addEventListener("pointerleave",y),()=>{a.removeEventListener("pointerleave",w),c.removeEventListener("pointerleave",y)}}},[a,c,h,m]),g.useEffect(()=>{if(l){const w=y=>{const C=y.target,p={x:y.clientX,y:y.clientY},f=(a==null?void 0:a.contains(C))||(c==null?void 0:c.contains(C)),v=!Pw(p,l);f?m():v&&(m(),u())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[a,c,l,u,m]),g.createElement($1,Ct({},e,{ref:i}))}),[xw,Cw]=xl(Cu,{isInside:!1}),$1=g.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:l,...s}=e,a=So(mo,n),u=Cl(n),{onClose:c}=a;return g.useEffect(()=>(document.addEventListener(ua,c),()=>document.removeEventListener(ua,c)),[c]),g.useEffect(()=>{if(a.trigger){const d=m=>{const h=m.target;h!=null&&h.contains(a.trigger)&&c()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[a.trigger,c]),g.createElement(Ky,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:d=>d.preventDefault(),onDismiss:c},g.createElement(q2,Ct({"data-state":a.stateAttribute},u,s,{ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"}}),g.createElement(Qp,null,r),g.createElement(xw,{scope:n,isInside:!0},g.createElement(sw,{id:a.contentId,role:"tooltip"},o||r))))}),Sw="TooltipArrow",_w=g.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Cl(n);return Cw(Sw,n).isInside?null:g.createElement(ew,Ct({},o,r,{ref:t}))});function Ew(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function kw(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function bw(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Pw(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,l=t.length-1;i<t.length;l=i++){const s=t[i].x,a=t[i].y,u=t[l].x,c=t[l].y;a>r!=c>r&&n<(u-s)*(r-a)/(c-a)+s&&(o=!o)}return o}function $w(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Ow(t)}function Ow(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],l=t[t.length-2];if((i.x-l.x)*(o.y-l.y)>=(i.y-l.y)*(o.x-l.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],l=n[n.length-2];if((i.x-l.x)*(o.y-l.y)>=(i.y-l.y)*(o.x-l.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}const Tw=fw,Aw=pw,Mw=mw,Rw=gw,Lw=yw,zw=_w,Dw=({children:e,content:t,arrow:n=!0,requiresOverflowToShow:r=!1,...o})=>{const i=g.useRef(null),l=Iy(i),s=g.useMemo(()=>!!(!r||i.current&&i.current.firstElementChild&&i.current.firstElementChild.offsetWidth>i.current.offsetWidth),[l]);return $(Tw,{children:vn(Aw,{delayDuration:200,children:[$(Mw,{asChild:!0,children:e(i)}),s&&$(Rw,{children:vn(Lw,{className:Bp(["fb-z-biggest fb-relative fb-rounded fb-bg-[#4c4b4b] fb-p-[5px] fb-text-white fb-opacity-90 fb-will-change-transform","tooltip-top:fb-animate-slide-up-and-fade","tooltip-down:fb-animate-slide-down-and-fade","tooltip-left:fb-animate-slide-left-and-fade","tooltip-right:fb-animate-slide-right-and-fade"]),...o,children:[t,n&&$(zw,{className:"fb-fill-[#4c4b4b]"})]})})]})})},P3=g.memo(Dw);var O1=(e=>(e.MEDIA_PAGE="upload-php",e.POST_TYPE_PAGE="filebird-post-type",e))(O1||{});const{is_rtl:T1}=window.fbv_data,$3=e=>{var n;const t=document.createElement("div");if(t.id="filebird-root",t.setAttribute("dir",T1?"rtl":"ltr"),(n=document.getElementById("wpbody"))==null||n.prepend(t),Hp){t.classList.add("mobile"),pi.createRoot(t).render($(sy,{children:$(e,{})}));return}pi.createRoot(t).render($(ha.StrictMode,{children:$(e,{})}))},O3=()=>{const{media_mode:e}=window.fbv_data;return e};function T3(){const{body:e}=document;return Object.values(O1).find(r=>e.classList.contains(r))}function Nw(e){try{return!!e.closest("body").children("#wp-uxbuilder-wrap").length}catch{return!1}}function A3(e,t){if(!e||!t||Hp)return;const n=e.$el.closest("[id^='__wp-uploader']:not('.wp-core-ui')");Nw(e.$el)?n.hasClass("fbv-ux-builder-modal")||n.addClass("fbv-ux-builder-modal"):n.hasClass("fbv-modal")||(n.attr("dir",T1?"rtl":"ltr"),n.addClass("fbv-modal"))}function M3(){!jQuery("body").attr("dir")&&jQuery("body").attr("dir",window.fbv_data.is_rtl?"rtl":"ltr")}const Fw=e=>ie("POST","/set-settings",e),Iw=e=>ie("POST","/set-user-settings",e),{i18n:Go,user_settings:jw}=window.fbv_data,R3=bp(Lp(e=>({settings:jw,modalOpen:!1,isFetching:!1,displayFolderId:!1,setModalOpen:t=>e({modalOpen:t}),setDisplayFolderId:()=>e(t=>({displayFolderId:!t.displayFolderId})),updateSetting:async t=>{e({isFetching:!0});try{await Fw(t),dt.success(Go.set_setting_success)}catch(n){console.log({error:n}),dt.error(Go.set_setting_fail)}finally{e({isFetching:!1,modalOpen:!1})}},setSettings:(t,n)=>e(r=>{r.settings[t]=n}),updateUserSetting:async t=>{e({isFetching:!0});try{await Iw(t),dt.success(Go.set_setting_success)}catch(n){console.log({error:n}),dt.error(Go.set_setting_fail)}finally{e({isFetching:!1,modalOpen:!1})}}})));function L3(e){const t=g.useRef({value:e,previous:e});return g.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}const z3=()=>$("span",{className:"fb-ml-2 fb-bg-[#4caf51] fb-rounded-xl fb-leading-none fb-py-1 fb-font-normal fb-px-2 fb-text-xxs fb-text-white",children:Rt("pro")});export{gr as $,Bp as A,ty as B,p3 as C,a3 as D,r3 as E,ga as F,o3 as G,n3 as H,i3 as I,qp as J,m3 as K,g3 as L,l3 as M,y3 as N,w3 as O,x3 as P,f3 as Q,ha as R,Xs as S,P3 as T,d3 as U,t3 as V,lg as W,mr as X,Bw as Y,ig as Z,Rt as _,$ as a,hr as a0,On as a1,Ue as a2,c1 as a3,wu as a4,Ct as a5,cu as a6,ft as a7,Ky as a8,ow as a9,ph as aA,Ly as aB,sg as aC,A3 as aD,Xw as aE,sy as aF,T3 as aG,M3 as aH,O1 as aI,$3 as aJ,O3 as aK,r0 as aL,ey as aM,bp as aN,Lp as aO,ie as aP,Ip as aQ,Jy as aa,tw as ab,L3 as ac,F2 as ad,R3 as ae,Vw as af,e3 as ag,Hp as ah,v3 as ai,h3 as aj,Uw as ak,_1 as al,X2 as am,J2 as an,q2 as ao,ew as ap,Wp as aq,Vy as ar,z3 as as,u3 as at,c3 as au,C3 as av,s3 as aw,Ww as ax,Qw as ay,Kw as az,Gw as b,Jw as c,pi as d,t1 as e,Hw as f,ca as g,e1 as h,uy as i,vn as j,E3 as k,Kc as l,S3 as m,cy as n,_3 as o,n1 as p,k3 as q,g as r,r1 as s,Yw as t,En as u,Zw as v,rg as w,og as x,qw as y,dt as z};
