import{r as m,aL as Xr,V as Oe,_ as g,j as R,F as be,a as d,aM as St,ak as Zr,B as Tt,aN as en,aO as tn,a3 as rn,a0 as nn,a9 as an,a1 as wr,a2 as At,a7 as on,ac as ln,ad as sn,ae as xt,T as Qt,as as Et,ah as cn,z as X,aP as ae,A as ne,aQ as dn,aH as fn,d as un,R as hn}from"./ProBadge-60c93dc8.js";/**
 * @remix-run/router v1.7.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},B.apply(this,arguments)}var G;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(G||(G={}));const Xt="popstate";function mn(e){e===void 0&&(e={});function t(a,o){let{pathname:i="/",search:l="",hash:s=""}=ie(a.location.hash.substr(1));return Ye("",{pathname:i,search:l,hash:s},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function r(a,o){let i=a.document.querySelector("base"),l="";if(i&&i.getAttribute("href")){let s=a.location.href,c=s.indexOf("#");l=c===-1?s:s.slice(0,c)}return l+"#"+(typeof o=="string"?o:Ce(o))}function n(a,o){_e(a.pathname.charAt(0)==="/","relative pathnames are not supported in hash history.push("+JSON.stringify(o)+")")}return pn(t,r,n,e)}function T(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function _e(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function bn(){return Math.random().toString(36).substr(2,8)}function Zt(e,t){return{usr:e.state,key:e.key,idx:t}}function Ye(e,t,r,n){return r===void 0&&(r=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?ie(t):t,{state:r,key:t&&t.key||n||bn()})}function Ce(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function ie(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function pn(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:o=!1}=n,i=a.history,l=G.Pop,s=null,c=b();c==null&&(c=0,i.replaceState(B({},i.state,{idx:c}),""));function b(){return(i.state||{idx:null}).idx}function y(){l=G.Pop;let v=b(),S=v==null?null:v-c;c=v,s&&s({action:l,location:w.location,delta:S})}function x(v,S){l=G.Push;let M=Ye(w.location,v,S);r&&r(M,v),c=b()+1;let U=Zt(M,c),u=w.createHref(M);try{i.pushState(U,"",u)}catch(O){if(O instanceof DOMException&&O.name==="DataCloneError")throw O;a.location.assign(u)}o&&s&&s({action:l,location:w.location,delta:1})}function L(v,S){l=G.Replace;let M=Ye(w.location,v,S);r&&r(M,v),c=b();let U=Zt(M,c),u=w.createHref(M);i.replaceState(U,"",u),o&&s&&s({action:l,location:w.location,delta:0})}function _(v){let S=a.location.origin!=="null"?a.location.origin:a.location.href,M=typeof v=="string"?v:Ce(v);return T(S,"No window.location.(origin|href) available to create URL for href: "+M),new URL(M,S)}let w={get action(){return l},get location(){return e(a,i)},listen(v){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(Xt,y),s=v,()=>{a.removeEventListener(Xt,y),s=null}},createHref(v){return t(a,v)},createURL:_,encodeLocation(v){let S=_(v);return{pathname:S.pathname,search:S.search,hash:S.hash}},push:x,replace:L,go(v){return i.go(v)}};return w}var q;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(q||(q={}));const gn=new Set(["lazy","caseSensitive","path","id","index","children"]);function vn(e){return e.index===!0}function Lt(e,t,r,n){return r===void 0&&(r=[]),n===void 0&&(n={}),e.map((a,o)=>{let i=[...r,o],l=typeof a.id=="string"?a.id:i.join("-");if(T(a.index!==!0||!a.children,"Cannot specify children on an index route"),T(!n[l],'Found a route id collision on id "'+l+`".  Route id's must be globally unique within Data Router usages`),vn(a)){let s=B({},a,t(a),{id:l});return n[l]=s,s}else{let s=B({},a,t(a),{id:l,children:void 0});return n[l]=s,a.children&&(s.children=Lt(a.children,t,i,n)),s}})}function De(e,t,r){r===void 0&&(r="/");let n=typeof t=="string"?ie(t):t,a=Ue(n.pathname||"/",r);if(a==null)return null;let o=xr(e);yn(o);let i=null;for(let l=0;i==null&&l<o.length;++l)i=Pn(o[l],Dn(a));return i}function xr(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(o,i,l)=>{let s={relativePath:l===void 0?o.path||"":l,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};s.relativePath.startsWith("/")&&(T(s.relativePath.startsWith(n),'Absolute route path "'+s.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),s.relativePath=s.relativePath.slice(n.length));let c=fe([n,s.relativePath]),b=r.concat(s);o.children&&o.children.length>0&&(T(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),xr(o.children,t,b,c)),!(o.path==null&&!o.index)&&t.push({path:c,score:En(c,o.index),routesMeta:b})};return e.forEach((o,i)=>{var l;if(o.path===""||!((l=o.path)!=null&&l.includes("?")))a(o,i);else for(let s of _r(o.path))a(o,i,s)}),t}function _r(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return a?[o,""]:[o];let i=_r(n.join("/")),l=[];return l.push(...i.map(s=>s===""?o:[o,s].join("/"))),a&&l.push(...i),l.map(s=>e.startsWith("/")&&s===""?"/":s)}function yn(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ln(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const wn=/^:\w+$/,xn=3,_n=2,Cn=1,Rn=10,Sn=-2,er=e=>e==="*";function En(e,t){let r=e.split("/"),n=r.length;return r.some(er)&&(n+=Sn),t&&(n+=_n),r.filter(a=>!er(a)).reduce((a,o)=>a+(wn.test(o)?xn:o===""?Cn:Rn),n)}function Ln(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Pn(e,t){let{routesMeta:r}=e,n={},a="/",o=[];for(let i=0;i<r.length;++i){let l=r[i],s=i===r.length-1,c=a==="/"?t:t.slice(a.length)||"/",b=kn({path:l.relativePath,caseSensitive:l.caseSensitive,end:s},c);if(!b)return null;Object.assign(n,b.params);let y=l.route;o.push({params:n,pathname:fe([a,b.pathname]),pathnameBase:On(fe([a,b.pathnameBase])),route:y}),b.pathnameBase!=="/"&&(a=fe([a,b.pathnameBase]))}return o}function kn(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Nn(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:n.reduce((c,b,y)=>{if(b==="*"){let x=l[y]||"";i=o.slice(0,o.length-x.length).replace(/(.)\/+$/,"$1")}return c[b]=Mn(l[y]||"",b),c},{}),pathname:o,pathnameBase:i,pattern:e}}function Nn(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),_e(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^$?{}|()[\]]/g,"\\$&").replace(/\/:(\w+)/g,(i,l)=>(n.push(l),"/([^\\/]+)"));return e.endsWith("*")?(n.push("*"),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function Dn(e){try{return decodeURI(e)}catch(t){return _e(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Mn(e,t){try{return decodeURIComponent(e)}catch(r){return _e(!1,'The value for the URL param "'+t+'" will not be decoded because'+(' the string "'+e+'" is a malformed URL segment. This is probably')+(" due to a bad percent encoding ("+r+").")),e}}function Ue(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Tn(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?ie(e):e;return{pathname:r?r.startsWith("/")?r:An(r,t):t,search:Un(n),hash:jn(a)}}function An(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function _t(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function st(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Ot(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=ie(e):(a=B({},e),T(!a.pathname||!a.pathname.includes("?"),_t("?","pathname","search",a)),T(!a.pathname||!a.pathname.includes("#"),_t("#","pathname","hash",a)),T(!a.search||!a.search.includes("#"),_t("#","search","hash",a)));let o=e===""||a.pathname==="",i=o?"/":a.pathname,l;if(n||i==null)l=r;else{let y=t.length-1;if(i.startsWith("..")){let x=i.split("/");for(;x[0]==="..";)x.shift(),y-=1;a.pathname=x.join("/")}l=y>=0?t[y]:"/"}let s=Tn(a,l),c=i&&i!=="/"&&i.endsWith("/"),b=(o||i===".")&&r.endsWith("/");return!s.pathname.endsWith("/")&&(c||b)&&(s.pathname+="/"),s}const fe=e=>e.join("/").replace(/\/\/+/g,"/"),On=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Un=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,jn=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class Ut{constructor(t,r,n,a){a===void 0&&(a=!1),this.status=t,this.statusText=r||"",this.internal=a,n instanceof Error?(this.data=n.toString(),this.error=n):this.data=n}}function Cr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Rr=["post","put","patch","delete"],Fn=new Set(Rr),In=["get",...Rr],$n=new Set(In),Bn=new Set([301,302,303,307,308]),zn=new Set([307,308]),Ct={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Hn={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ze={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},Sr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Vn=e=>({hasErrorBoundary:!!e.hasErrorBoundary});function Wn(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u",n=!r;T(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let a;if(e.mapRouteProperties)a=e.mapRouteProperties;else if(e.detectErrorBoundary){let f=e.detectErrorBoundary;a=h=>({hasErrorBoundary:f(h)})}else a=Vn;let o={},i=Lt(e.routes,a,void 0,o),l,s=e.basename||"/",c=B({v7_normalizeFormMethod:!1,v7_prependBasename:!1},e.future),b=null,y=new Set,x=null,L=null,_=null,w=e.hydrationData!=null,v=De(i,e.history.location,s),S=null;if(v==null){let f=Z(404,{pathname:e.history.location.pathname}),{matches:h,route:p}=sr(i);v=h,S={[p.id]:f}}let M=!v.some(f=>f.route.lazy)&&(!v.some(f=>f.route.loader)||e.hydrationData!=null),U,u={historyAction:e.history.action,location:e.history.location,matches:v,initialized:M,navigation:Ct,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||S,fetchers:new Map,blockers:new Map},O=G.Pop,H=!1,$,A=!1,z=!1,ee=[],Ee=[],W=new Map,Ze=0,je=-1,Le=new Map,le=new Set,Pe=new Map,ge=new Map,ve=new Map,ut=!1;function Fr(){return b=e.history.listen(f=>{let{action:h,location:p,delta:C}=f;if(ut){ut=!1;return}_e(ve.size===0||C!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let D=Kt({currentLocation:u.location,nextLocation:p,historyAction:h});if(D&&C!=null){ut=!0,e.history.go(C*-1),tt(D,{state:"blocked",location:p,proceed(){tt(D,{state:"proceeding",proceed:void 0,reset:void 0,location:p}),e.history.go(C)},reset(){let k=new Map(u.blockers);k.set(D,ze),J({blockers:k})}});return}return ue(h,p)}),u.initialized||ue(G.Pop,u.location),U}function Ir(){b&&b(),y.clear(),$&&$.abort(),u.fetchers.forEach((f,h)=>mt(h)),u.blockers.forEach((f,h)=>Gt(h))}function $r(f){return y.add(f),()=>y.delete(f)}function J(f){u=B({},u,f),y.forEach(h=>h(u))}function Fe(f,h){var p,C;let D=u.actionData!=null&&u.navigation.formMethod!=null&&re(u.navigation.formMethod)&&u.navigation.state==="loading"&&((p=f.state)==null?void 0:p._isRedirect)!==!0,k;h.actionData?Object.keys(h.actionData).length>0?k=h.actionData:k=null:D?k=u.actionData:k=null;let N=h.loaderData?lr(u.loaderData,h.loaderData,h.matches||[],h.errors):u.loaderData,P=u.blockers;P.size>0&&(P=new Map(P),P.forEach((I,Q)=>P.set(Q,ze)));let E=H===!0||u.navigation.formMethod!=null&&re(u.navigation.formMethod)&&((C=f.state)==null?void 0:C._isRedirect)!==!0;l&&(i=l,l=void 0),A||O===G.Pop||(O===G.Push?e.history.push(f,f.state):O===G.Replace&&e.history.replace(f,f.state)),J(B({},h,{actionData:k,loaderData:N,historyAction:O,location:f,initialized:!0,navigation:Ct,revalidation:"idle",restoreScrollPosition:Jt(f,h.matches||u.matches),preventScrollReset:E,blockers:P})),O=G.Pop,H=!1,A=!1,z=!1,ee=[],Ee=[]}async function $t(f,h){if(typeof f=="number"){e.history.go(f);return}let p=Pt(u.location,u.matches,s,c.v7_prependBasename,f,h==null?void 0:h.fromRouteId,h==null?void 0:h.relative),{path:C,submission:D,error:k}=tr(c.v7_normalizeFormMethod,!1,p,h),N=u.location,P=Ye(u.location,C,h&&h.state);P=B({},P,e.history.encodeLocation(P));let E=h&&h.replace!=null?h.replace:void 0,I=G.Push;E===!0?I=G.Replace:E===!1||D!=null&&re(D.formMethod)&&D.formAction===u.location.pathname+u.location.search&&(I=G.Replace);let Q=h&&"preventScrollReset"in h?h.preventScrollReset===!0:void 0,j=Kt({currentLocation:N,nextLocation:P,historyAction:I});if(j){tt(j,{state:"blocked",location:P,proceed(){tt(j,{state:"proceeding",proceed:void 0,reset:void 0,location:P}),$t(f,h)},reset(){let V=new Map(u.blockers);V.set(j,ze),J({blockers:V})}});return}return await ue(I,P,{submission:D,pendingError:k,preventScrollReset:Q,replace:h&&h.replace})}function Br(){if(ht(),J({revalidation:"loading"}),u.navigation.state!=="submitting"){if(u.navigation.state==="idle"){ue(u.historyAction,u.location,{startUninterruptedRevalidation:!0});return}ue(O||u.historyAction,u.navigation.location,{overrideNavigation:u.navigation})}}async function ue(f,h,p){$&&$.abort(),$=null,O=f,A=(p&&p.startUninterruptedRevalidation)===!0,Jr(u.location,u.matches),H=(p&&p.preventScrollReset)===!0;let C=l||i,D=p&&p.overrideNavigation,k=De(C,h,s);if(!k){let V=Z(404,{pathname:h.pathname}),{matches:Y,route:ye}=sr(C);bt(),Fe(h,{matches:Y,loaderData:{},errors:{[ye.id]:V}});return}if(u.initialized&&!z&&Yn(u.location,h)&&!(p&&p.submission&&re(p.submission.formMethod))){Fe(h,{matches:k});return}$=new AbortController;let N=Ve(e.history,h,$.signal,p&&p.submission),P,E;if(p&&p.pendingError)E={[Me(k).route.id]:p.pendingError};else if(p&&p.submission&&re(p.submission.formMethod)){let V=await zr(N,h,p.submission,k,{replace:p.replace});if(V.shortCircuited)return;P=V.pendingActionData,E=V.pendingActionError,D=nt(h,p.submission),N=new Request(N.url,{signal:N.signal})}let{shortCircuited:I,loaderData:Q,errors:j}=await Hr(N,h,k,D,p&&p.submission,p&&p.fetcherSubmission,p&&p.replace,P,E);I||($=null,Fe(h,B({matches:k},P?{actionData:P}:{},{loaderData:Q,errors:j})))}async function zr(f,h,p,C,D){D===void 0&&(D={}),ht();let k=ta(h,p);J({navigation:k});let N,P=Nt(C,h);if(!P.route.action&&!P.route.lazy)N={type:q.error,error:Z(405,{method:f.method,pathname:h.pathname,routeId:P.route.id})};else if(N=await He("action",f,P,C,o,a,s),f.signal.aborted)return{shortCircuited:!0};if(Te(N)){let E;return D&&D.replace!=null?E=D.replace:E=N.location===u.location.pathname+u.location.search,await Ie(u,N,{submission:p,replace:E}),{shortCircuited:!0}}if(Je(N)){let E=Me(C,P.route.id);return(D&&D.replace)!==!0&&(O=G.Push),{pendingActionData:{},pendingActionError:{[E.route.id]:N.error}}}if(xe(N))throw Z(400,{type:"defer-action"});return{pendingActionData:{[P.route.id]:N.data}}}async function Hr(f,h,p,C,D,k,N,P,E){let I=C||nt(h,D),Q=D||k||fr(I),j=l||i,[V,Y]=rr(e.history,u,p,Q,h,z,ee,Ee,Pe,le,j,s,P,E);if(bt(F=>!(p&&p.some(te=>te.route.id===F))||V&&V.some(te=>te.route.id===F)),je=++Ze,V.length===0&&Y.length===0){let F=Vt();return Fe(h,B({matches:p,loaderData:{},errors:E||null},P?{actionData:P}:{},F?{fetchers:new Map(u.fetchers)}:{})),{shortCircuited:!0}}if(!A){Y.forEach(te=>{let me=u.fetchers.get(te.key),wt=We(void 0,me?me.data:void 0);u.fetchers.set(te.key,wt)});let F=P||u.actionData;J(B({navigation:I},F?Object.keys(F).length===0?{actionData:null}:{actionData:F}:{},Y.length>0?{fetchers:new Map(u.fetchers)}:{}))}Y.forEach(F=>{W.has(F.key)&&he(F.key),F.controller&&W.set(F.key,F.controller)});let ye=()=>Y.forEach(F=>he(F.key));$&&$.signal.addEventListener("abort",ye);let{results:we,loaderResults:$e,fetcherResults:pt}=await zt(u.matches,p,V,Y,f);if(f.signal.aborted)return{shortCircuited:!0};$&&$.signal.removeEventListener("abort",ye),Y.forEach(F=>W.delete(F.key));let se=cr(we);if(se){if(se.idx>=V.length){let F=Y[se.idx-V.length].key;le.add(F)}return await Ie(u,se.result,{replace:N}),{shortCircuited:!0}}let{loaderData:ce,errors:rt}=ir(u,p,V,$e,E,Y,pt,ge);ge.forEach((F,te)=>{F.subscribe(me=>{(me||F.done)&&ge.delete(te)})});let gt=Vt(),vt=Wt(je),yt=gt||vt||Y.length>0;return B({loaderData:ce,errors:rt},yt?{fetchers:new Map(u.fetchers)}:{})}function Bt(f){return u.fetchers.get(f)||Hn}function Vr(f,h,p,C){if(n)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");W.has(f)&&he(f);let D=l||i,k=Pt(u.location,u.matches,s,c.v7_prependBasename,p,h,C==null?void 0:C.relative),N=De(D,k,s);if(!N){et(f,h,Z(404,{pathname:k}));return}let{path:P,submission:E,error:I}=tr(c.v7_normalizeFormMethod,!0,k,C);if(I){et(f,h,I);return}let Q=Nt(N,P);if(H=(C&&C.preventScrollReset)===!0,E&&re(E.formMethod)){Wr(f,h,P,Q,N,E);return}Pe.set(f,{routeId:h,path:P}),Gr(f,h,P,Q,N,E)}async function Wr(f,h,p,C,D,k){if(ht(),Pe.delete(f),!C.route.action&&!C.route.lazy){let K=Z(405,{method:k.formMethod,pathname:p,routeId:h});et(f,h,K);return}let N=u.fetchers.get(f),P=ra(k,N);u.fetchers.set(f,P),J({fetchers:new Map(u.fetchers)});let E=new AbortController,I=Ve(e.history,p,E.signal,k);W.set(f,E);let Q=Ze,j=await He("action",I,C,D,o,a,s);if(I.signal.aborted){W.get(f)===E&&W.delete(f);return}if(Te(j))if(W.delete(f),je>Q){let K=Ne(void 0);u.fetchers.set(f,K),J({fetchers:new Map(u.fetchers)});return}else{le.add(f);let K=We(k);return u.fetchers.set(f,K),J({fetchers:new Map(u.fetchers)}),Ie(u,j,{submission:k,isFetchActionRedirect:!0})}if(Je(j)){et(f,h,j.error);return}if(xe(j))throw Z(400,{type:"defer-action"});let V=u.navigation.location||u.location,Y=Ve(e.history,V,E.signal),ye=l||i,we=u.navigation.state!=="idle"?De(ye,u.navigation.location,s):u.matches;T(we,"Didn't find any matches after fetcher action");let $e=++Ze;Le.set(f,$e);let pt=We(k,j.data);u.fetchers.set(f,pt);let[se,ce]=rr(e.history,u,we,k,V,z,ee,Ee,Pe,le,ye,s,{[C.route.id]:j.data},void 0);ce.filter(K=>K.key!==f).forEach(K=>{let Be=K.key,Yt=u.fetchers.get(Be),Qr=We(void 0,Yt?Yt.data:void 0);u.fetchers.set(Be,Qr),W.has(Be)&&he(Be),K.controller&&W.set(Be,K.controller)}),J({fetchers:new Map(u.fetchers)});let rt=()=>ce.forEach(K=>he(K.key));E.signal.addEventListener("abort",rt);let{results:gt,loaderResults:vt,fetcherResults:yt}=await zt(u.matches,we,se,ce,Y);if(E.signal.aborted)return;E.signal.removeEventListener("abort",rt),Le.delete(f),W.delete(f),ce.forEach(K=>W.delete(K.key));let F=cr(gt);if(F){if(F.idx>=se.length){let K=ce[F.idx-se.length].key;le.add(K)}return Ie(u,F.result)}let{loaderData:te,errors:me}=ir(u,u.matches,se,vt,void 0,ce,yt,ge);if(u.fetchers.has(f)){let K=Ne(j.data);u.fetchers.set(f,K)}let wt=Wt($e);u.navigation.state==="loading"&&$e>je?(T(O,"Expected pending action"),$&&$.abort(),Fe(u.navigation.location,{matches:we,loaderData:te,errors:me,fetchers:new Map(u.fetchers)})):(J(B({errors:me,loaderData:lr(u.loaderData,te,we,me)},wt||ce.length>0?{fetchers:new Map(u.fetchers)}:{})),z=!1)}async function Gr(f,h,p,C,D,k){let N=u.fetchers.get(f),P=We(k,N?N.data:void 0);u.fetchers.set(f,P),J({fetchers:new Map(u.fetchers)});let E=new AbortController,I=Ve(e.history,p,E.signal);W.set(f,E);let Q=Ze,j=await He("loader",I,C,D,o,a,s);if(xe(j)&&(j=await Pr(j,I.signal,!0)||j),W.get(f)===E&&W.delete(f),I.signal.aborted)return;if(Te(j))if(je>Q){let Y=Ne(void 0);u.fetchers.set(f,Y),J({fetchers:new Map(u.fetchers)});return}else{le.add(f),await Ie(u,j);return}if(Je(j)){let Y=Me(u.matches,h);u.fetchers.delete(f),J({fetchers:new Map(u.fetchers),errors:{[Y.route.id]:j.error}});return}T(!xe(j),"Unhandled fetcher deferred data");let V=Ne(j.data);u.fetchers.set(f,V),J({fetchers:new Map(u.fetchers)})}async function Ie(f,h,p){let{submission:C,replace:D,isFetchActionRedirect:k}=p===void 0?{}:p;h.revalidate&&(z=!0);let N=Ye(f.location,h.location,B({_isRedirect:!0},k?{_isFetchActionRedirect:!0}:{}));if(T(N,"Expected a location on the redirect navigation"),Sr.test(h.location)&&r){let I=e.history.createURL(h.location),Q=Ue(I.pathname,s)==null;if(t.location.origin!==I.origin||Q){D?t.location.replace(h.location):t.location.assign(h.location);return}}$=null;let P=D===!0?G.Replace:G.Push,E=C||fr(f.navigation);if(zn.has(h.status)&&E&&re(E.formMethod))await ue(P,N,{submission:B({},E,{formAction:h.location}),preventScrollReset:H});else if(k)await ue(P,N,{overrideNavigation:nt(N),fetcherSubmission:E,preventScrollReset:H});else{let I=nt(N,E);await ue(P,N,{overrideNavigation:I,preventScrollReset:H})}}async function zt(f,h,p,C,D){let k=await Promise.all([...p.map(E=>He("loader",D,E,h,o,a,s)),...C.map(E=>E.matches&&E.match&&E.controller?He("loader",Ve(e.history,E.path,E.controller.signal),E.match,E.matches,o,a,s):{type:q.error,error:Z(404,{pathname:E.path})})]),N=k.slice(0,p.length),P=k.slice(p.length);return await Promise.all([dr(f,p,N,N.map(()=>D.signal),!1,u.loaderData),dr(f,C.map(E=>E.match),P,C.map(E=>E.controller?E.controller.signal:null),!0)]),{results:k,loaderResults:N,fetcherResults:P}}function ht(){z=!0,ee.push(...bt()),Pe.forEach((f,h)=>{W.has(h)&&(Ee.push(h),he(h))})}function et(f,h,p){let C=Me(u.matches,h);mt(f),J({errors:{[C.route.id]:p},fetchers:new Map(u.fetchers)})}function mt(f){let h=u.fetchers.get(f);W.has(f)&&!(h&&h.state==="loading"&&Le.has(f))&&he(f),Pe.delete(f),Le.delete(f),le.delete(f),u.fetchers.delete(f)}function he(f){let h=W.get(f);T(h,"Expected fetch controller: "+f),h.abort(),W.delete(f)}function Ht(f){for(let h of f){let p=Bt(h),C=Ne(p.data);u.fetchers.set(h,C)}}function Vt(){let f=[],h=!1;for(let p of le){let C=u.fetchers.get(p);T(C,"Expected fetcher: "+p),C.state==="loading"&&(le.delete(p),f.push(p),h=!0)}return Ht(f),h}function Wt(f){let h=[];for(let[p,C]of Le)if(C<f){let D=u.fetchers.get(p);T(D,"Expected fetcher: "+p),D.state==="loading"&&(he(p),Le.delete(p),h.push(p))}return Ht(h),h.length>0}function Kr(f,h){let p=u.blockers.get(f)||ze;return ve.get(f)!==h&&ve.set(f,h),p}function Gt(f){u.blockers.delete(f),ve.delete(f)}function tt(f,h){let p=u.blockers.get(f)||ze;T(p.state==="unblocked"&&h.state==="blocked"||p.state==="blocked"&&h.state==="blocked"||p.state==="blocked"&&h.state==="proceeding"||p.state==="blocked"&&h.state==="unblocked"||p.state==="proceeding"&&h.state==="unblocked","Invalid blocker state transition: "+p.state+" -> "+h.state);let C=new Map(u.blockers);C.set(f,h),J({blockers:C})}function Kt(f){let{currentLocation:h,nextLocation:p,historyAction:C}=f;if(ve.size===0)return;ve.size>1&&_e(!1,"A router only supports one blocker at a time");let D=Array.from(ve.entries()),[k,N]=D[D.length-1],P=u.blockers.get(k);if(!(P&&P.state==="proceeding")&&N({currentLocation:h,nextLocation:p,historyAction:C}))return k}function bt(f){let h=[];return ge.forEach((p,C)=>{(!f||f(C))&&(p.cancel(),h.push(C),ge.delete(C))}),h}function qr(f,h,p){if(x=f,_=h,L=p||null,!w&&u.navigation===Ct){w=!0;let C=Jt(u.location,u.matches);C!=null&&J({restoreScrollPosition:C})}return()=>{x=null,_=null,L=null}}function qt(f,h){return L&&L(f,h.map(C=>ea(C,u.loaderData)))||f.key}function Jr(f,h){if(x&&_){let p=qt(f,h);x[p]=_()}}function Jt(f,h){if(x){let p=qt(f,h),C=x[p];if(typeof C=="number")return C}return null}function Yr(f){o={},l=Lt(f,a,void 0,o)}return U={get basename(){return s},get state(){return u},get routes(){return i},initialize:Fr,subscribe:$r,enableScrollRestoration:qr,navigate:$t,fetch:Vr,revalidate:Br,createHref:f=>e.history.createHref(f),encodeLocation:f=>e.history.encodeLocation(f),getFetcher:Bt,deleteFetcher:mt,dispose:Ir,getBlocker:Kr,deleteBlocker:Gt,_internalFetchControllers:W,_internalActiveDeferreds:ge,_internalSetRoutes:Yr},U}function Gn(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Pt(e,t,r,n,a,o,i){let l,s;if(o!=null&&i!=="path"){l=[];for(let b of t)if(l.push(b),b.route.id===o){s=b;break}}else l=t,s=t[t.length-1];let c=Ot(a||".",st(l).map(b=>b.pathnameBase),Ue(e.pathname,r)||e.pathname,i==="path");return a==null&&(c.search=e.search,c.hash=e.hash),(a==null||a===""||a===".")&&s&&s.route.index&&!jt(c.search)&&(c.search=c.search?c.search.replace(/^\?/,"?index&"):"?index"),n&&r!=="/"&&(c.pathname=c.pathname==="/"?r:fe([r,c.pathname])),Ce(c)}function tr(e,t,r,n){if(!n||!Gn(n))return{path:r};if(n.formMethod&&!Zn(n.formMethod))return{path:r,error:Z(405,{method:n.formMethod})};let a=()=>({path:r,error:Z(400,{type:"invalid-body"})}),o=n.formMethod||"get",i=e?o.toUpperCase():o.toLowerCase(),l=Lr(r);if(n.body!==void 0){if(n.formEncType==="text/plain"){if(!re(i))return a();let x=typeof n.body=="string"?n.body:n.body instanceof FormData||n.body instanceof URLSearchParams?Array.from(n.body.entries()).reduce((L,_)=>{let[w,v]=_;return""+L+w+"="+v+`
`},""):String(n.body);return{path:r,submission:{formMethod:i,formAction:l,formEncType:n.formEncType,formData:void 0,json:void 0,text:x}}}else if(n.formEncType==="application/json"){if(!re(i))return a();try{let x=typeof n.body=="string"?JSON.parse(n.body):n.body;return{path:r,submission:{formMethod:i,formAction:l,formEncType:n.formEncType,formData:void 0,json:x,text:void 0}}}catch{return a()}}}T(typeof FormData=="function","FormData is not available in this environment");let s,c;if(n.formData)s=kt(n.formData),c=n.formData;else if(n.body instanceof FormData)s=kt(n.body),c=n.body;else if(n.body instanceof URLSearchParams)s=n.body,c=or(s);else if(n.body==null)s=new URLSearchParams,c=new FormData;else try{s=new URLSearchParams(n.body),c=or(s)}catch{return a()}let b={formMethod:i,formAction:l,formEncType:n&&n.formEncType||"application/x-www-form-urlencoded",formData:c,json:void 0,text:void 0};if(re(b.formMethod))return{path:r,submission:b};let y=ie(r);return t&&y.search&&jt(y.search)&&s.append("index",""),y.search="?"+s,{path:Ce(y),submission:b}}function Kn(e,t){let r=e;if(t){let n=e.findIndex(a=>a.route.id===t);n>=0&&(r=e.slice(0,n))}return r}function rr(e,t,r,n,a,o,i,l,s,c,b,y,x,L){let _=L?Object.values(L)[0]:x?Object.values(x)[0]:void 0,w=e.createURL(t.location),v=e.createURL(a),S=L?Object.keys(L)[0]:void 0,U=Kn(r,S).filter((O,H)=>{if(O.route.lazy)return!0;if(O.route.loader==null)return!1;if(qn(t.loaderData,t.matches[H],O)||i.some(z=>z===O.route.id))return!0;let $=t.matches[H],A=O;return nr(O,B({currentUrl:w,currentParams:$.params,nextUrl:v,nextParams:A.params},n,{actionResult:_,defaultShouldRevalidate:o||w.pathname+w.search===v.pathname+v.search||w.search!==v.search||Er($,A)}))}),u=[];return s.forEach((O,H)=>{if(!r.some(Ee=>Ee.route.id===O.routeId))return;let $=De(b,O.path,y);if(!$){u.push({key:H,routeId:O.routeId,path:O.path,matches:null,match:null,controller:null});return}let A=t.fetchers.get(H),z=Nt($,O.path),ee=!1;c.has(H)?ee=!1:l.includes(H)?ee=!0:A&&A.state!=="idle"&&A.data===void 0?ee=o:ee=nr(z,B({currentUrl:w,currentParams:t.matches[t.matches.length-1].params,nextUrl:v,nextParams:r[r.length-1].params},n,{actionResult:_,defaultShouldRevalidate:o})),ee&&u.push({key:H,routeId:O.routeId,path:O.path,matches:$,match:z,controller:new AbortController})}),[U,u]}function qn(e,t,r){let n=!t||r.route.id!==t.route.id,a=e[r.route.id]===void 0;return n||a}function Er(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function nr(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}async function ar(e,t,r){if(!e.lazy)return;let n=await e.lazy();if(!e.lazy)return;let a=r[e.id];T(a,"No route found in manifest");let o={};for(let i in n){let s=a[i]!==void 0&&i!=="hasErrorBoundary";_e(!s,'Route "'+a.id+'" has a static property "'+i+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+i+'" will be ignored.')),!s&&!gn.has(i)&&(o[i]=n[i])}Object.assign(a,o),Object.assign(a,B({},t(a),{lazy:void 0}))}async function He(e,t,r,n,a,o,i,l){l===void 0&&(l={});let s,c,b,y=_=>{let w,v=new Promise((S,M)=>w=M);return b=()=>w(),t.signal.addEventListener("abort",b),Promise.race([_({request:t,params:r.params,context:l.requestContext}),v])};try{let _=r.route[e];if(r.route.lazy)if(_)c=(await Promise.all([y(_),ar(r.route,o,a)]))[0];else if(await ar(r.route,o,a),_=r.route[e],_)c=await y(_);else if(e==="action"){let w=new URL(t.url),v=w.pathname+w.search;throw Z(405,{method:t.method,pathname:v,routeId:r.route.id})}else return{type:q.data,data:void 0};else if(_)c=await y(_);else{let w=new URL(t.url),v=w.pathname+w.search;throw Z(404,{pathname:v})}T(c!==void 0,"You defined "+(e==="action"?"an action":"a loader")+" for route "+('"'+r.route.id+"\" but didn't return anything from your `"+e+"` ")+"function. Please return a value or `null`.")}catch(_){s=q.error,c=_}finally{b&&t.signal.removeEventListener("abort",b)}if(Xn(c)){let _=c.status;if(Bn.has(_)){let S=c.headers.get("Location");if(T(S,"Redirects returned/thrown from loaders/actions must have a Location header"),!Sr.test(S))S=Pt(new URL(t.url),n.slice(0,n.indexOf(r)+1),i,!0,S);else if(!l.isStaticRequest){let M=new URL(t.url),U=S.startsWith("//")?new URL(M.protocol+S):new URL(S),u=Ue(U.pathname,i)!=null;U.origin===M.origin&&u&&(S=U.pathname+U.search+U.hash)}if(l.isStaticRequest)throw c.headers.set("Location",S),c;return{type:q.redirect,status:_,location:S,revalidate:c.headers.get("X-Remix-Revalidate")!==null}}if(l.isRouteRequest)throw{type:s||q.data,response:c};let w,v=c.headers.get("Content-Type");return v&&/\bapplication\/json\b/.test(v)?w=await c.json():w=await c.text(),s===q.error?{type:s,error:new Ut(_,c.statusText,w),headers:c.headers}:{type:q.data,data:w,statusCode:c.status,headers:c.headers}}if(s===q.error)return{type:s,error:c};if(Qn(c)){var x,L;return{type:q.deferred,deferredData:c,statusCode:(x=c.init)==null?void 0:x.status,headers:((L=c.init)==null?void 0:L.headers)&&new Headers(c.init.headers)}}return{type:q.data,data:c}}function Ve(e,t,r,n){let a=e.createURL(Lr(t)).toString(),o={signal:r};if(n&&re(n.formMethod)){let{formMethod:i,formEncType:l}=n;o.method=i.toUpperCase(),l==="application/json"?(o.headers=new Headers({"Content-Type":l}),o.body=JSON.stringify(n.json)):l==="text/plain"?o.body=n.text:l==="application/x-www-form-urlencoded"&&n.formData?o.body=kt(n.formData):o.body=n.formData}return new Request(a,o)}function kt(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function or(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function Jn(e,t,r,n,a){let o={},i=null,l,s=!1,c={};return r.forEach((b,y)=>{let x=t[y].route.id;if(T(!Te(b),"Cannot handle redirect results in processLoaderData"),Je(b)){let L=Me(e,x),_=b.error;n&&(_=Object.values(n)[0],n=void 0),i=i||{},i[L.route.id]==null&&(i[L.route.id]=_),o[x]=void 0,s||(s=!0,l=Cr(b.error)?b.error.status:500),b.headers&&(c[x]=b.headers)}else xe(b)?(a.set(x,b.deferredData),o[x]=b.deferredData.data):o[x]=b.data,b.statusCode!=null&&b.statusCode!==200&&!s&&(l=b.statusCode),b.headers&&(c[x]=b.headers)}),n&&(i=n,o[Object.keys(n)[0]]=void 0),{loaderData:o,errors:i,statusCode:l||200,loaderHeaders:c}}function ir(e,t,r,n,a,o,i,l){let{loaderData:s,errors:c}=Jn(t,r,n,a,l);for(let b=0;b<o.length;b++){let{key:y,match:x,controller:L}=o[b];T(i!==void 0&&i[b]!==void 0,"Did not find corresponding fetcher result");let _=i[b];if(!(L&&L.signal.aborted))if(Je(_)){let w=Me(e.matches,x==null?void 0:x.route.id);c&&c[w.route.id]||(c=B({},c,{[w.route.id]:_.error})),e.fetchers.delete(y)}else if(Te(_))T(!1,"Unhandled fetcher revalidation redirect");else if(xe(_))T(!1,"Unhandled fetcher deferred data");else{let w=Ne(_.data);e.fetchers.set(y,w)}}return{loaderData:s,errors:c}}function lr(e,t,r,n){let a=B({},t);for(let o of r){let i=o.route.id;if(t.hasOwnProperty(i)?t[i]!==void 0&&(a[i]=t[i]):e[i]!==void 0&&o.route.loader&&(a[i]=e[i]),n&&n.hasOwnProperty(i))break}return a}function Me(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function sr(e){let t=e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Z(e,t){let{pathname:r,routeId:n,method:a,type:o}=t===void 0?{}:t,i="Unknown Server Error",l="Unknown @remix-run/router error";return e===400?(i="Bad Request",a&&r&&n?l="You made a "+a+' request to "'+r+'" but '+('did not provide a `loader` for route "'+n+'", ')+"so there is no way to handle the request.":o==="defer-action"?l="defer() is not supported in actions":o==="invalid-body"&&(l="Unable to encode submission body")):e===403?(i="Forbidden",l='Route "'+n+'" does not match URL "'+r+'"'):e===404?(i="Not Found",l='No route matches URL "'+r+'"'):e===405&&(i="Method Not Allowed",a&&r&&n?l="You made a "+a.toUpperCase()+' request to "'+r+'" but '+('did not provide an `action` for route "'+n+'", ')+"so there is no way to handle the request.":a&&(l='Invalid request method "'+a.toUpperCase()+'"')),new Ut(e||500,i,new Error(l),!0)}function cr(e){for(let t=e.length-1;t>=0;t--){let r=e[t];if(Te(r))return{result:r,idx:t}}}function Lr(e){let t=typeof e=="string"?ie(e):e;return Ce(B({},t,{hash:""}))}function Yn(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function xe(e){return e.type===q.deferred}function Je(e){return e.type===q.error}function Te(e){return(e&&e.type)===q.redirect}function Qn(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}function Xn(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function Zn(e){return $n.has(e.toLowerCase())}function re(e){return Fn.has(e.toLowerCase())}async function dr(e,t,r,n,a,o){for(let i=0;i<r.length;i++){let l=r[i],s=t[i];if(!s)continue;let c=e.find(y=>y.route.id===s.route.id),b=c!=null&&!Er(c,s)&&(o&&o[s.route.id])!==void 0;if(xe(l)&&(a||b)){let y=n[i];T(y,"Expected an AbortSignal for revalidating fetcher deferred result"),await Pr(l,y,a).then(x=>{x&&(r[i]=x||r[i])})}}}async function Pr(e,t,r){if(r===void 0&&(r=!1),!await e.deferredData.resolveData(t)){if(r)try{return{type:q.data,data:e.deferredData.unwrappedData}}catch(a){return{type:q.error,error:a}}return{type:q.data,data:e.deferredData.data}}}function jt(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function ea(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function Nt(e,t){let r=typeof t=="string"?ie(t).search:t.search;if(e[e.length-1].route.index&&jt(r||""))return e[e.length-1];let n=st(e);return n[n.length-1]}function fr(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:o,json:i}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(o!=null)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};if(i!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:i,text:void 0}}}function nt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function ta(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function We(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t," _hasFetcherDoneAnything ":!0}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t," _hasFetcherDoneAnything ":!0}}function ra(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0," _hasFetcherDoneAnything ":!0}}function Ne(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e," _hasFetcherDoneAnything ":!0}}/**
 * React Router v6.14.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function it(){return it=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},it.apply(this,arguments)}const ct=m.createContext(null),Ft=m.createContext(null),Re=m.createContext(null),dt=m.createContext(null),pe=m.createContext({outlet:null,matches:[],isDataRoute:!1}),kr=m.createContext(null);function na(e,t){let{relative:r}=t===void 0?{}:t;Xe()||T(!1);let{basename:n,navigator:a}=m.useContext(Re),{hash:o,pathname:i,search:l}=It(e,{relative:r}),s=i;return n!=="/"&&(s=i==="/"?n:fe([n,i])),a.createHref({pathname:s,search:l,hash:o})}function Xe(){return m.useContext(dt)!=null}function Se(){return Xe()||T(!1),m.useContext(dt).location}function Nr(e){m.useContext(Re).static||m.useLayoutEffect(e)}function Dr(){let{isDataRoute:e}=m.useContext(pe);return e?ga():aa()}function aa(){Xe()||T(!1);let e=m.useContext(ct),{basename:t,navigator:r}=m.useContext(Re),{matches:n}=m.useContext(pe),{pathname:a}=Se(),o=JSON.stringify(st(n).map(s=>s.pathnameBase)),i=m.useRef(!1);return Nr(()=>{i.current=!0}),m.useCallback(function(s,c){if(c===void 0&&(c={}),!i.current)return;if(typeof s=="number"){r.go(s);return}let b=Ot(s,JSON.parse(o),a,c.relative==="path");e==null&&t!=="/"&&(b.pathname=b.pathname==="/"?t:fe([t,b.pathname])),(c.replace?r.replace:r.push)(b,c.state,c)},[t,r,o,a,e])}const oa=m.createContext(null);function ia(e){let t=m.useContext(pe).outlet;return t&&m.createElement(oa.Provider,{value:e},t)}function It(e,t){let{relative:r}=t===void 0?{}:t,{matches:n}=m.useContext(pe),{pathname:a}=Se(),o=JSON.stringify(st(n).map(i=>i.pathnameBase));return m.useMemo(()=>Ot(e,JSON.parse(o),a,r==="path"),[e,o,a,r])}function la(e,t,r){Xe()||T(!1);let{navigator:n}=m.useContext(Re),{matches:a}=m.useContext(pe),o=a[a.length-1],i=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let s=Se(),c;if(t){var b;let w=typeof t=="string"?ie(t):t;l==="/"||(b=w.pathname)!=null&&b.startsWith(l)||T(!1),c=w}else c=s;let y=c.pathname||"/",x=l==="/"?y:y.slice(l.length)||"/",L=De(e,{pathname:x}),_=ua(L&&L.map(w=>Object.assign({},w,{params:Object.assign({},i,w.params),pathname:fe([l,n.encodeLocation?n.encodeLocation(w.pathname).pathname:w.pathname]),pathnameBase:w.pathnameBase==="/"?l:fe([l,n.encodeLocation?n.encodeLocation(w.pathnameBase).pathname:w.pathnameBase])})),a,r);return t&&_?m.createElement(dt.Provider,{value:{location:it({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:G.Pop}},_):_}function sa(){let e=pa(),t=Cr(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},o=null;return m.createElement(m.Fragment,null,m.createElement("h2",null,"Unexpected Application Error!"),m.createElement("h3",{style:{fontStyle:"italic"}},t),r?m.createElement("pre",{style:a},r):null,o)}const ca=m.createElement(sa,null);class da extends m.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error||r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error?m.createElement(pe.Provider,{value:this.props.routeContext},m.createElement(kr.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function fa(e){let{routeContext:t,match:r,children:n}=e,a=m.useContext(ct);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),m.createElement(pe.Provider,{value:t},n)}function ua(e,t,r){var n;if(t===void 0&&(t=[]),r===void 0&&(r=null),e==null){var a;if((a=r)!=null&&a.errors)e=r.matches;else return null}let o=e,i=(n=r)==null?void 0:n.errors;if(i!=null){let l=o.findIndex(s=>s.route.id&&(i==null?void 0:i[s.route.id]));l>=0||T(!1),o=o.slice(0,Math.min(o.length,l+1))}return o.reduceRight((l,s,c)=>{let b=s.route.id?i==null?void 0:i[s.route.id]:null,y=null;r&&(y=s.route.errorElement||ca);let x=t.concat(o.slice(0,c+1)),L=()=>{let _;return b?_=y:s.route.Component?_=m.createElement(s.route.Component,null):s.route.element?_=s.route.element:_=l,m.createElement(fa,{match:s,routeContext:{outlet:l,matches:x,isDataRoute:r!=null},children:_})};return r&&(s.route.ErrorBoundary||s.route.errorElement||c===0)?m.createElement(da,{location:r.location,revalidation:r.revalidation,component:y,error:b,children:L(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):L()},null)}var Dt;(function(e){e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate"})(Dt||(Dt={}));var Qe;(function(e){e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId"})(Qe||(Qe={}));function ha(e){let t=m.useContext(ct);return t||T(!1),t}function ma(e){let t=m.useContext(Ft);return t||T(!1),t}function ba(e){let t=m.useContext(pe);return t||T(!1),t}function Mr(e){let t=ba(),r=t.matches[t.matches.length-1];return r.route.id||T(!1),r.route.id}function pa(){var e;let t=m.useContext(kr),r=ma(Qe.UseRouteError),n=Mr(Qe.UseRouteError);return t||((e=r.errors)==null?void 0:e[n])}function ga(){let{router:e}=ha(Dt.UseNavigateStable),t=Mr(Qe.UseNavigateStable),r=m.useRef(!1);return Nr(()=>{r.current=!0}),m.useCallback(function(a,o){o===void 0&&(o={}),r.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,it({fromRouteId:t},o)))},[e,t])}const va="startTransition",ur=Xr[va];function ya(e){let{fallbackElement:t,router:r,future:n}=e,[a,o]=m.useState(r.state),{v7_startTransition:i}=n||{},l=m.useCallback(y=>{i&&ur?ur(()=>o(y)):o(y)},[o,i]);m.useLayoutEffect(()=>r.subscribe(l),[r,l]);let s=m.useMemo(()=>({createHref:r.createHref,encodeLocation:r.encodeLocation,go:y=>r.navigate(y),push:(y,x,L)=>r.navigate(y,{state:x,preventScrollReset:L==null?void 0:L.preventScrollReset}),replace:(y,x,L)=>r.navigate(y,{replace:!0,state:x,preventScrollReset:L==null?void 0:L.preventScrollReset})}),[r]),c=r.basename||"/",b=m.useMemo(()=>({router:r,navigator:s,static:!1,basename:c}),[r,s,c]);return m.createElement(m.Fragment,null,m.createElement(ct.Provider,{value:b},m.createElement(Ft.Provider,{value:a},m.createElement(_a,{basename:c,location:a.location,navigationType:a.historyAction,navigator:s},a.initialized?m.createElement(wa,{routes:r.routes,state:a}):t))),null)}function wa(e){let{routes:t,state:r}=e;return la(t,void 0,r)}function xa(e){return ia(e.context)}function _a(e){let{basename:t="/",children:r=null,location:n,navigationType:a=G.Pop,navigator:o,static:i=!1}=e;Xe()&&T(!1);let l=t.replace(/^\/*/,"/"),s=m.useMemo(()=>({basename:l,navigator:o,static:i}),[l,o,i]);typeof n=="string"&&(n=ie(n));let{pathname:c="/",search:b="",hash:y="",state:x=null,key:L="default"}=n,_=m.useMemo(()=>{let w=Ue(c,l);return w==null?null:{location:{pathname:w,search:b,hash:y,state:x,key:L},navigationType:a}},[l,c,b,y,x,L,a]);return _==null?null:m.createElement(Re.Provider,{value:s},m.createElement(dt.Provider,{children:r,value:_}))}var hr;(function(e){e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error"})(hr||(hr={}));new Promise(()=>{});function Ca(e){let t={hasErrorBoundary:e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&Object.assign(t,{element:m.createElement(e.Component),Component:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:m.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}/**
 * React Router DOM v6.14.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ae(){return Ae=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ae.apply(this,arguments)}function Tr(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,o;for(o=0;o<n.length;o++)a=n[o],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function Ra(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Sa(e,t){return e.button===0&&(!t||t==="_self")&&!Ra(e)}function Mt(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let n=e[r];return t.concat(Array.isArray(n)?n.map(a=>[r,a]):[[r,n]])},[]))}function Ea(e,t){let r=Mt(e);if(t)for(let n of t.keys())r.has(n)||t.getAll(n).forEach(a=>{r.append(n,a)});return r}const La=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset"],Pa=["aria-current","caseSensitive","className","end","style","to","children"];function ka(e,t){return Wn({basename:t==null?void 0:t.basename,future:Ae({},t==null?void 0:t.future,{v7_prependBasename:!0}),history:mn({window:t==null?void 0:t.window}),hydrationData:(t==null?void 0:t.hydrationData)||Na(),routes:e,mapRouteProperties:Ca}).initialize()}function Na(){var e;let t=(e=window)==null?void 0:e.__staticRouterHydrationData;return t&&t.errors&&(t=Ae({},t,{errors:Da(t.errors)})),t}function Da(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,a]of t)if(a&&a.__type==="RouteErrorResponse")r[n]=new Ut(a.status,a.statusText,a.data,a.internal===!0);else if(a&&a.__type==="Error"){if(a.__subType){let o=window[a.__subType];if(typeof o=="function")try{let i=new o(a.message);i.stack="",r[n]=i}catch{}}if(r[n]==null){let o=new Error(a.message);o.stack="",r[n]=o}}else r[n]=a;return r}const Ma=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Ta=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Aa=m.forwardRef(function(t,r){let{onClick:n,relative:a,reloadDocument:o,replace:i,state:l,target:s,to:c,preventScrollReset:b}=t,y=Tr(t,La),{basename:x}=m.useContext(Re),L,_=!1;if(typeof c=="string"&&Ta.test(c)&&(L=c,Ma))try{let M=new URL(window.location.href),U=c.startsWith("//")?new URL(M.protocol+c):new URL(c),u=Ue(U.pathname,x);U.origin===M.origin&&u!=null?c=u+U.search+U.hash:_=!0}catch{}let w=na(c,{relative:a}),v=Ua(c,{replace:i,state:l,target:s,preventScrollReset:b,relative:a});function S(M){n&&n(M),M.defaultPrevented||v(M)}return m.createElement("a",Ae({},y,{href:L||w,onClick:_||o?n:S,ref:r,target:s}))}),Oa=m.forwardRef(function(t,r){let{"aria-current":n="page",caseSensitive:a=!1,className:o="",end:i=!1,style:l,to:s,children:c}=t,b=Tr(t,Pa),y=It(s,{relative:b.relative}),x=Se(),L=m.useContext(Ft),{navigator:_}=m.useContext(Re),w=_.encodeLocation?_.encodeLocation(y).pathname:y.pathname,v=x.pathname,S=L&&L.navigation&&L.navigation.location?L.navigation.location.pathname:null;a||(v=v.toLowerCase(),S=S?S.toLowerCase():null,w=w.toLowerCase());let M=v===w||!i&&v.startsWith(w)&&v.charAt(w.length)==="/",U=S!=null&&(S===w||!i&&S.startsWith(w)&&S.charAt(w.length)==="/"),u=M?n:void 0,O;typeof o=="function"?O=o({isActive:M,isPending:U}):O=[o,M?"active":null,U?"pending":null].filter(Boolean).join(" ");let H=typeof l=="function"?l({isActive:M,isPending:U}):l;return m.createElement(Aa,Ae({},b,{"aria-current":u,className:O,ref:r,style:H,to:s}),typeof c=="function"?c({isActive:M,isPending:U}):c)});var mr;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher"})(mr||(mr={}));var br;(function(e){e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(br||(br={}));function Ua(e,t){let{target:r,replace:n,state:a,preventScrollReset:o,relative:i}=t===void 0?{}:t,l=Dr(),s=Se(),c=It(e,{relative:i});return m.useCallback(b=>{if(Sa(b,r)){b.preventDefault();let y=n!==void 0?n:Ce(s)===Ce(c);l(e,{replace:y,state:a,preventScrollReset:o,relative:i})}},[s,l,c,n,a,r,e,o,i])}function ja(e){let t=m.useRef(Mt(e)),r=m.useRef(!1),n=Se(),a=m.useMemo(()=>Ea(n.search,r.current?null:t.current),[n.search]),o=Dr(),i=m.useCallback((l,s)=>{const c=Mt(typeof l=="function"?l(a):l);r.current=!0,o("?"+c,s)},[o,a]);return[a,i]}const Fa=Oe.a.attrs({href:"#"})`fb-items-center fb-bg-[#81b441] fb-rounded fb-text-white fb-inline-flex fb-font-medium fb-leading-none fb-py-3 fb-px-5 fb-no-underline fb-outline-none fb-cursor-pointer fb-mt-[10px]
  focusin:fb-text-white focusin:fb-shadow-none focusin:fb-outline-none
  active:fb-text-white active:fb-shadow-none active:fb-outline-none
  hover:fb-shadow-none hover:fb-text-white hover:fb-outline-none hover:fb-bg-[#74a13a]
`,Ia=[g("feature_1"),g("feature_2"),g("feature_3"),g("feature_4"),g("feature_5"),g("feature_6"),g("feature_7")],$a=({buynow:e})=>R(be,{children:[R("div",{children:[d("h1",{className:"fb-mt-0 fb-font-semibold",children:g("upgrade_to_pro")}),R("h2",{className:"fb-flex fb-items-center",children:[g("explore_filebird"),d("span",{className:"fb-ml-2 fb-bg-[#4caf51] fb-rounded-3xl fb-py-1 fb-font-normal fb-px-2 fb-text-sm fb-text-white",children:g("lifetime_license")})]})]}),d("ul",{className:"fb-my-3 fb-mx-0",children:Ia.map(t=>R("li",{className:"fb-flex fb-items-center",children:[d("svg",{className:"fb-text-[#4caf50] fb-mr-2 fb-flex-shrink-0",width:20,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",children:d("path",{d:"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"})}),t]},t))}),R(Fa,{onClick:e,children:[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",viewBox:"0 0 32 32",fill:"currentColor",className:"fb-mr-[10px] fb-text-base",children:d("path",{d:"M26.2,2.1C22-2.8,8.4,6.7,8.5,19c0,0.3-0.3,0.6-0.6,0.6c-0.2,0-0.4-0.1-0.5-0.3c-1.4-3-1.6-6.5-0.5-9.6C7.1,9.4,6.9,9.1,6.6,9C6.4,8.9,6.2,9,6.1,9.1c-2.3,2.4-3.5,5.6-3.5,8.9c-0.1,7.2,5.7,13.1,12.8,13.1c0.1,0,0.2,0,0.3,0C34.2,30.7,29.9,6.5,26.2,2.1z"})}),d("span",{className:"fb-uppercase",children:g("go_pro")})]})]}),ft=({children:e,footer:t})=>d(be,{children:R("div",{className:"fb-p-4 wp-md:fb-p-12 fb-flex-1",children:[e,t&&d("div",{className:"fb-flex fb-justify-end fb-mt-6",children:t})]})}),{asset_url:Ba}=window.fbv_data,za=Oe.div`fb-max-w-[250px] fb-w-[250px] fb-h-[250px] fb-mb-4 fb-bg-no-repeat fb-object-cover fb-shrink-0 fb-grow-0`,pr=()=>{const e=()=>{window.open("https://1.envato.market/GoPro-FileBird-Premium","_blank")},t=`${Ba}img/sad-bird.svg`;return d(ft,{children:R("div",{className:"fb-flex fb-flex-col wp-md:fb-flex-row fb-items-center fb-justify-center",children:[d(za,{style:{backgroundImage:`url(${t})`}}),d("div",{children:d($a,{buynow:e})})]})})},Ha=m.forwardRef((e,t)=>R("svg",{...e,ref:t,width:"18",height:"21",viewBox:"0 0 18 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[d("path",{d:"M7.58605 1.2209L2.94419 3.00713C1.87442 3.41568 1 4.70784 1 5.86698V12.9264C1 14.0475 1.72558 15.5202 2.6093 16.1948L6.6093 19.2447C7.92093 20.2518 10.0791 20.2518 11.3907 19.2447L15.3907 16.1948C16.2744 15.5202 17 14.0475 17 12.9264V5.86698C17 4.69834 16.1256 3.40618 15.0558 2.99762L10.414 1.2209C9.62326 0.926366 8.35814 0.926366 7.58605 1.2209Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),d("path",{d:"M6.24652 10.8864L7.7442 12.3356L11.7442 8.46509",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})),Va=m.forwardRef((e,t)=>R("svg",{...e,ref:t,width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[d("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.7381 6.28381L18.1082 5.21619C17.5753 4.31282 16.3944 4.00118 15.4682 4.51952V4.51952C15.0274 4.77319 14.5013 4.84516 14.0061 4.71956C13.5109 4.59396 13.0871 4.28111 12.8283 3.85C12.6618 3.57598 12.5723 3.26387 12.5689 2.94524V2.94524C12.584 2.43439 12.3866 1.93935 12.0219 1.5729C11.6572 1.20645 11.1562 0.99979 10.633 1H9.36394C8.85134 0.999994 8.35987 1.19951 7.99828 1.55439C7.63668 1.90928 7.43478 2.39027 7.43725 2.89095V2.89095C7.42205 3.92468 6.55973 4.75487 5.50129 4.75476C5.17507 4.75145 4.85553 4.66406 4.57499 4.50143V4.50143C3.64885 3.98309 2.46792 4.29473 1.93504 5.1981L1.25885 6.28381C0.726609 7.18605 1.04132 8.33879 1.96283 8.86238V8.86238C2.56183 9.20017 2.93082 9.82442 2.93082 10.5C2.93082 11.1756 2.56183 11.7998 1.96283 12.1376V12.1376C1.0425 12.6577 0.727436 13.8076 1.25885 14.7071V14.7071L1.89799 15.7838C2.14767 16.2239 2.56658 16.5486 3.06204 16.6861C3.55749 16.8237 4.08864 16.7627 4.53794 16.5167V16.5167C4.97962 16.2649 5.50596 16.196 5.99996 16.3251C6.49396 16.4542 6.91469 16.7707 7.16862 17.2043C7.33512 17.4783 7.42459 17.7904 7.42798 18.109V18.109C7.42798 19.1534 8.29474 20 9.36394 20H10.633C11.6986 20 12.5638 19.1589 12.5689 18.1181V18.1181C12.5665 17.6158 12.7696 17.1335 13.1332 16.7783C13.4968 16.4232 13.9907 16.2247 14.5049 16.2271C14.8303 16.2357 15.1485 16.3227 15.4312 16.4805V16.4805C16.3549 17.0003 17.5351 16.6929 18.0711 15.7929V15.7929L18.7381 14.7071C18.9962 14.2743 19.0671 13.7589 18.9349 13.2749C18.8028 12.7909 18.4786 12.3784 18.0341 12.1286V12.1286C17.5896 11.8788 17.2654 11.4662 17.1332 10.9822C17.0011 10.4983 17.0719 9.9828 17.3301 9.55C17.498 9.26372 17.741 9.02635 18.0341 8.86238V8.86238C18.9501 8.33908 19.264 7.19307 18.7381 6.29286V6.29286V6.28381Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),d("ellipse",{cx:"10.003",cy:"10.5",rx:"2.66773",ry:"2.60571",fill:"currentColor"})]})),Wa=m.forwardRef((e,t)=>R("svg",{...e,ref:t,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[d("path",{d:"M7.03899 10.648H8.60499V14.293C8.60499 14.833 9.27099 15.085 9.63099 14.68L13.465 10.324C13.798 9.946 13.528 9.352 13.024 9.352H11.458V5.707C11.458 5.167 10.792 4.915 10.432 5.32L6.59799 9.676C6.26499 10.054 6.53499 10.648 7.03899 10.648Z",fill:"currentColor"}),d("path",{d:"M10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19Z",stroke:"currentColor",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]})),Ga=m.forwardRef((e,t)=>R("svg",{...e,ref:t,width:"20",height:"19",viewBox:"0 0 20 19",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[d("path",{d:"M3 4.21429L6 1L9 4.21429",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),d("path",{d:"M5.99963 1V11",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),d("path",{d:"M16 7.78571L13 11L10 7.78571",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),d("path",{d:"M13.0004 11L13.0004 1",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),d("path",{d:"M1 16C6.835 18 13.165 18 19 16",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})),Ka=[{hash:"/",icon:Ha,label:g("go_pro")},{hash:"settings",icon:Va,label:g("settings")},{hash:"tools",icon:Ga,label:g("tools")},{hash:"import-export",icon:Wa,label:g("import_export")}],qa=()=>d("aside",{className:"fb-fixed fb-border-solid fb-border-0 fb-bottom-0 fb-left-0 fb-z-50 fb-w-full fb-h-16 fb-bg-white fb-border-gray-200 wp-md:fb-static wp-md:fb-max-w-64 wp-md:fb-h-auto wp-md:fb-pt-4 wp-md:fb-border-r wp-md:fb-border-[rgba(177,187,202,0.15)]","aria-label":"Sidebar",children:d("div",{className:"fb-overflow-y-auto fb-bg-white fb-h-full fb-shadow-[0px_-5px_20px_20px_rgba(0,0,0,0.031372549)] wp-md:fb-shadow-none wp-md:fb-h-auto",children:d("ul",{className:"fb-grid fb-m-0 fb-h-full fb-grid-cols-4 fb-mx-auto fb-font-medium wp-md:fb-block",children:Ka.map(e=>d("li",{className:"fb-m-0 fb-flex wp-md:fb-mb-1",children:R(Oa,{to:e.hash,className:({isActive:t})=>St("fb-transition-shadow fb-duration-150 fb-ease-linear fb-inline-flex fb-flex-1 fb-outline-none fb-no-underline fb-text-teal-950 fb-flex-col fb-items-center fb-justify-center hover:fb-bg-gray-50 fb-group focus:fb-shadow-admin-mobile-menu focus:fb-bg-admin-light focus:fb-text-admin-primary wp-md:fb-flex-row wp-md:fb-justify-start wp-md:fb-py-3 wp-md:fb-px-5 wp-md:focus:fb-shadow-admin-menu",{"fb-shadow-admin-mobile-menu fb-bg-admin-light fb-text-admin-primary wp-md:fb-shadow-admin-menu":t}),children:[d(e.icon,{className:"fb-flex-shrink-0 fb-mb-2 fb-w-5 fb-h-5 fb-transition fb-duration-75 group-hover:fb-text-admin-primary wp-md:fb-mr-3 wp-md:fb-mb-0"}),d("span",{className:"fb-text-xs group-hover:fb-text-blue-600 wp-md:fb-text-base",children:e.label})]})},e.hash))})})}),Ja=()=>{const e=Se();return m.useEffect(()=>{let t=e.pathname;e.pathname==="/"&&(t="/activation"),jQuery(".filebird-admin-menu-item").removeClass("selected"),jQuery(`.filebird-admin-menu-item:has(a[href*="${t}"])`).addClass("selected")},[e.pathname]),R(be,{children:[d("div",{className:"wp-md:fb-max-w-[1060px] fb-m-auto",children:R("div",{className:"fb-flex fb-bg-white fb-rounded-xl wp-md:fb-min-h-[675px] fb-overflow-hidden",children:[d(qa,{}),d(xa,{})]})}),d(Zr,{containerStyle:{top:40}})]})},Ya=()=>d("h3",{children:" There is not available"}),de=Tt.button(({$secondary:e,$danger:t})=>["button fb-flex fb-items-center fb-justify-center fb-gap-1 fb-min-w-24 fb-bg-admin-primary fb-font-medium fb-before-icon fb-text-white fb-outline-none hover:fb-bg-admin-primary-darker active:fb-bg-admin-primary-darker focus:fb-shadow-admin-button fb-transition-shadow",e&&"fb-bg-white fb-text-admin-primary hover:fb-bg-admin-primary hover:fb-text-white active:fb-text-white",t&&"danger fb-bg-admin-danger-lighter fb-border-transparent fb-text-admin-danger hover:fb-bg-red-200 active:fb-bg-red-200 focus:fb-shadow-[inset_0_0_0_1px_#fff,0_0_0_0.5px_#C82D270F]"]),oe=Tt.div`fb-h-[1px] fb-w-full fb-bg-[#EEF3F9] fb-my-5 wp-md:fb-my-9`,{enabled_post_types:Qa}=window.fbv_admin,gr=en(tn(e=>({postTypes:{enabledSlugs:Qa},setEnabledPostTypes:t=>e(r=>{r.postTypes.enabledSlugs=t})}))),{post_types:Ar}=window.fbv_admin,Xa=Object.keys(Ar),Za=m.forwardRef((e,t)=>{const r=gr(o=>o.postTypes.enabledSlugs),n=gr(o=>o.setEnabledPostTypes),a=o=>{const{value:i}=o.target,l=r.indexOf(i),s=[...r];l===-1?s.push(i):s.splice(l,1),n(s)};return d("div",{className:"fb-flex fb-flex-col fb-gap-3",children:Xa.map(o=>R("div",{className:"fb-gap-3 fb-flex fb-items-center",children:[d("input",{className:"fb-m-0 fb-relative fb-border-admin-secondary fb-shadow-none checked:before:fb-content-checked-box checked:before:fb-w-3 checked:before:fb-h-2 checked:before:fb-float-none checked:before:fb-absolute checked:before:fb-transform-checked-box checked:before:fb-inset-0 checked:before:fb-m-auto checked:fb-border-admin-primary",defaultChecked:r.indexOf(o)!==-1,type:"checkbox",value:o,id:o,onChange:a}),d("label",{htmlFor:o,children:Ar[o]})]},o))})}),Or="Switch",[eo,No]=rn(Or),[to,ro]=eo(Or),no=m.forwardRef((e,t)=>{const{__scopeSwitch:r,name:n,checked:a,defaultChecked:o,required:i,disabled:l,value:s="on",onCheckedChange:c,...b}=e,[y,x]=m.useState(null),L=nn(t,M=>x(M)),_=m.useRef(!1),w=y?!!y.closest("form"):!0,[v=!1,S]=an({prop:a,defaultProp:o,onChange:c});return m.createElement(to,{scope:r,checked:v,disabled:l},m.createElement(wr.button,At({type:"button",role:"switch","aria-checked":v,"aria-required":i,"data-state":Ur(v),"data-disabled":l?"":void 0,disabled:l,value:s},b,{ref:L,onClick:on(e.onClick,M=>{S(U=>!U),w&&(_.current=M.isPropagationStopped(),_.current||M.stopPropagation())})})),w&&m.createElement(io,{control:y,bubbles:!_.current,name:n,value:s,checked:v,required:i,disabled:l,style:{transform:"translateX(-100%)"}}))}),ao="SwitchThumb",oo=m.forwardRef((e,t)=>{const{__scopeSwitch:r,...n}=e,a=ro(ao,r);return m.createElement(wr.span,At({"data-state":Ur(a.checked),"data-disabled":a.disabled?"":void 0},n,{ref:t}))}),io=e=>{const{control:t,checked:r,bubbles:n=!0,...a}=e,o=m.useRef(null),i=ln(r),l=sn(t);return m.useEffect(()=>{const s=o.current,c=window.HTMLInputElement.prototype,y=Object.getOwnPropertyDescriptor(c,"checked").set;if(i!==r&&y){const x=new Event("click",{bubbles:n});y.call(s,r),s.dispatchEvent(x)}},[i,r,n]),m.createElement("input",At({type:"checkbox","aria-hidden":!0,defaultChecked:r},a,{tabIndex:-1,ref:o,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}}))};function Ur(e){return e?"checked":"unchecked"}const jr=no,lo=oo,qe=m.forwardRef(({className:e,...t},r)=>d(jr,{className:St("fb-peer fb-p-0 fb-inline-flex fb-h-[18px] fb-w-9 fb-shrink-0 fb-cursor-pointer fb-items-center fb-rounded-full fb-border fb-border-teal-950 fb-border-solid fb-bg-transparent fb-shadow-sm fb-transition-colors focus-visible:fb-outline-none focus-visible:fb-ring-2 focus-visible:fb-ring-ring focus-visible:fb-ring-offset-2 focus-visible:fb-ring-offset-white disabled:fb-cursor-not-allowed disabled:fb-opacity-50 data-[state=checked]:fb-bg-admin-primary data-[state=unchecked]:fb-bg-input data-[state=checked]:fb-border-transparent",e),...t,ref:r,children:d(lo,{className:St("fb-pointer-events-none fb-block fb-h-3 fb-w-3 fb-rounded-full fb-bg-teal-950 fb-shadow-lg fb-ring-0 fb-transition-transform data-[state=checked]:fb-translate-x-5 data-[state=checked]:fb-bg-white data-[state=unchecked]:fb-translate-x-0.5")})}));qe.displayName=jr.displayName;const{asset_url:Rt,license:so}=window.fbv_data,co={default:{img:`${Rt}img/default.svg`,title:g("default"),needActive:!1},windows:{img:`${Rt}img/windows.svg`,title:"Windows",needActive:!0},dropbox:{img:`${Rt}img/dropbox.svg`,title:"Dropbox",needActive:!0}},fo=Oe.div`fb-flex fb-gap-[10px] fb-w-full`,vr=Tt.img`hover:fb-border-admin-primary fb-flex-grow fb-flex-shrink-0 fb-basis-0 fb-p-[10px] fb-rounded fb-border fb-border-[#E1E9F4] fb-border-solid fb-duration-200 fb-transition-colors fb-ease-in-out`,uo=()=>{const e=xt(c=>c.settings),t=xt(c=>c.setSettings),r=xt(c=>c.updateSetting),[n,a]=m.useState(!1),o=m.useRef(null),[i,l]=m.useState(!1);return d(be,{children:R(ft,{footer:d(de,{disabled:n,className:n?"updating-message":"",onClick:async()=>{a(!0);try{await r(e)}catch(c){X.error(g("set_setting_fail")),console.log({error:c})}finally{a(!1)}},children:g("save_changes")}),children:[d("h3",{className:"fb-mt-0 fb-p-0",children:g("general")}),R("div",{className:"fb-flex fb-flex-col fb-gap-[10px]",children:[R("div",{className:"fb-flex fb-gap-[10px]",children:[d(qe,{checked:!!e.USER_MODE,onCheckedChange:c=>t("USER_MODE",c)}),R("span",{children:[g("each_user_has_own_folder"),"?"]})]}),R("div",{className:"fb-flex fb-gap-[10px]",children:[d(qe,{checked:!!e.SHOW_BREAD_CRUMB,onCheckedChange:c=>t("SHOW_BREAD_CRUMB",c)}),d("span",{children:g("show_breadcrumb")})]}),R("div",{className:"fb-flex fb-gap-[10px]",children:[d(qe,{checked:!!e.SVG_SUPPORT,onCheckedChange:c=>t("SVG_SUPPORT",c)}),d("span",{children:g("svg_upload_desc")})]}),R("div",{className:"fb-flex fb-gap-[10px] fb-items-center",children:[d(qe,{checked:!!e.IS_SEARCH_USING_API,onCheckedChange:c=>t("IS_SEARCH_USING_API",c)}),d("span",{children:g("searching_folder_api")}),d(Qt,{sideOffset:5,content:g("searching_folder_api"),children:()=>d("svg",{className:"fb-w-4 fb-h-4 fb-cursor-pointer",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:d("path",{d:"M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z"})})})]})]}),d(oe,{}),d("h3",{children:g("folder_counter")}),R("div",{className:"fb-flex fb-flex-col fb-gap-[10px]",children:[R("div",{className:"fb-flex fb-gap-[10px] fb-items-center",children:[d("input",{className:"fb-m-0 fb-shadow-none checked:before:fb-bg-admin-primary checked:before:fb-content-[''] fb-border-admin-secondary checked:fb-border-admin-primary",id:"counter_file_in_folder",checked:e.FOLDER_COUNTER_TYPE=="counter_file_in_folder",type:"radio",name:"counter_type",onChange:()=>t("FOLDER_COUNTER_TYPE","counter_file_in_folder")}),d("label",{htmlFor:"counter_file_in_folder",children:g("count_in_folder")})]}),R("div",{className:"fb-flex fb-gap-[10px] fb-items-center",children:[d("input",{className:"fb-peer disabled:fb-cursor-not-allowed disabled:fb-opacity-50 fb-m-0 fb-shadow-none checked:before:fb-bg-admin-primary checked:before:fb-content-[''] fb-border-admin-secondary checked:fb-border-admin-primary",id:"counter_file_in_folder_and_sub",checked:e.FOLDER_COUNTER_TYPE=="counter_file_in_folder_and_sub",type:"radio",name:"counter_type",disabled:!0,onChange:()=>t("FOLDER_COUNTER_TYPE","counter_file_in_folder_and_sub")}),R("label",{className:"fb-flex fb-items-center peer-disabled:fb-cursor-not-allowed peer-disabled:fb-opacity-70",htmlFor:"counter_file_in_folder_and_sub",children:[g("count_nested")," ",d(Et,{})]})]})]}),d(oe,{}),d("h3",{children:g("select_theme")}),i&&d("div",{className:"fb-p-4 fb-mb-4 fb-text-sm fb-text-red-800 fb-rounded-lg fb-bg-red-50 ",role:"alert",children:d("span",{className:"fb-font-medium",children:g("active_to_use_feature")})}),d(fo,{children:Object.entries(co).map(([c,b])=>d(m.Fragment,{children:b.needActive&&!so.status?d(Qt,{sideOffset:5,content:g("active_to_update"),children:()=>R("div",{className:"fb-flex fb-flex-col fb-w-1/3 fb-cursor-pointer fb-text-center",onClick:()=>cn&&l(!0),children:[d(vr,{className:c===e.THEME.name&&"fb-border-admin-primary",src:b.img,alt:b.title}),d("span",{className:"fb-mt-3",children:b.title})]},c)}):R("div",{className:"fb-flex fb-flex-col fb-w-1/3 fb-cursor-pointer fb-text-center",onClick:()=>{t("THEME",{...e.THEME,name:c}),l(!1)},children:[d(vr,{className:c===e.THEME.name&&"fb-border-primary",src:b.img,alt:b.title}),d("span",{className:"fb-mt-3",children:b.title})]},c)},c))}),d(oe,{}),R("div",{className:"fb-opacity-50 fb-cursor-not-allowed fb-pointer-events-none",children:[R("h3",{className:"fb-flex fb-items-center",children:[g("which_post_types_do_you_want")," ",d(Et,{})]}),d(Za,{ref:o}),d(oe,{})]})]})})},{fbv_data:at}=window,lt={async exportCSV(e=""){const t=e==""?"/export-csv":"/export-csv/?id="+e;return ae("GET",t)},async importCSV(e){return jQuery.ajax({url:at.json_url+"/import-csv",beforeSend:()=>{},method:"POST",processData:!1,contentType:!1,cache:!1,data:e,headers:{"X-WP-Nonce":at.rest_nonce}})},async getCSVDetail(e){return jQuery.ajax({url:at.json_url+"/import-csv-detail",method:"POST",processData:!1,contentType:!1,beforeSend:function(t){t.setRequestHeader("X-WP-Nonce",at.rest_nonce)},data:e})},async importPlugin(e){try{return await ae("GET",`/import/get-folders/${e}`),await ae("GET",`/import/get-attachments/${e}`),await ae("GET",`/import/run/${e}`)}catch(t){console.log({error:t})}}};function ho(e){const t=e.data;if(!t||!t.length)return;const r=e.columnDelimiter||",",n=e.lineDelimiter||`
`,a=Object.keys(t[0]);let o="";return o+=a.join(r),o+=n,t.forEach(i=>{let l=0;a.forEach(s=>{l>0&&(o+=r),o+=i[s],l++}),o+=n}),o}const mo=e=>{const t=ho({data:e.data}),r=new Blob([t],{type:"text/csv;charset=utf-8;"});if(!t)return;const n=e.filename||"export.csv",a=document.createElement("a");a.setAttribute("href",URL.createObjectURL(r)),a.setAttribute("download",n),a.click()};async function bo(e){const t=new FormData;if(!e.length)return Promise.reject("There is no file!");if(e.length){t.append("file",e[0]);const r=await lt.getCSVDetail(t);let n={"":"","-1":g("all_folders"),0:g("common_folders")};return Object.entries(r).length&&(n={...n,...r}),n}return null}const po=()=>{const[e,t]=m.useState(!1),[r,n]=m.useState(!1),[a,o]=m.useState(""),[i,l]=m.useState(!1),[s,c]=m.useState(null),b=m.useRef(new FormData),y=async()=>{t(!0);const v=document.querySelector("#filebird-export-btn").getAttribute("data-id");await X.promise(lt.exportCSV(v),{loading:g("loading"),success:S=>(t(!1),S.folders.length?(x(S.folders),g("successfully_exported")):g("no_folders_export")),error:()=>g("please_try_again")})},x=v=>{mo({filename:"filebird.csv",data:v})},L=async v=>{const S=v.target.files,M=await bo(S);c(M),S.length&&b.current.append("file",S[0])},_=v=>{const S=v.target.value;b.current.append("userId",S),o(S?S==="-1"?g("all_folders_description"):S==="0"?g("common_folders_description"):`${g("user_folders_description")} ${v.target.options[v.target.selectedIndex].text}.`:"")},w=async()=>{n(!0);try{const v=await lt.importCSV(b.current);if(n(!1),v.success)l(!0),X.success(g("successfully_imported"));else{if(v.message){X.error(v.message);return}X.error(g("please_try_again"))}}catch{n(!1),X.error(g("please_try_again"))}};return R(be,{children:[d("h2",{children:g("folders_for_media_library")}),R("div",{className:"fb-flex fb-flex-col wp-md:fb-flex-row wp-md:fb-items-center fb-justify-between fb-mb-5 wp-md:fb-mb-9",children:[R("div",{className:"fb-mb-4 wp-md:fb-mb-0",children:[d("h4",{className:"fb-mt-0 fb-mb-4 fb-p-0",children:g("export_csv")}),d("p",{className:"description wp-md:fb-m-0",children:g("export_csv_desc")})]}),d(de,{disabled:e,className:ne("wp-md:fb-ml-0 fb-ml-auto",{"updating-message":e}),onClick:()=>y(),id:"filebird-export-btn","data-id":"",children:g("export_csv_now")})]}),d("h4",{className:"fb-mt-0 fb-mb-4 fb-p-0",children:g("import_csv")}),d("div",{className:"flex-item-center",children:d("input",{className:"fb-max-w-80",type:"file",accept:".csv",name:"csv_file",onChange:L})}),s&&R("p",{className:ne({hidden:!s}),children:[d("span",{children:g("choose_user_folder")})," ",d("select",{onChange:_,children:Object.entries(s).sort().map(([v,S])=>d("option",{value:v,children:S},v))})," ",d("button",{disabled:!a||r||i,className:ne("button button-large",{hidden:!s,"updating-message":r}),type:"button",onClick:w,children:i?g("imported"):g("update_noti_btn")})]}),d("p",{children:a}),d("p",{className:"description",children:d("span",{className:"[&_a]:fb-text-admin-primary",dangerouslySetInnerHTML:{__html:g("import_csv_desc")}})})]})},go=()=>{const e={folders:14,items:2};return R(be,{children:[R("div",{className:"fb-inline-block fb-bg-gray-200 fb-mr-2 fb-rounded-full fb-px-2 fb-py-1",children:[e.folders," folders"]}),R("div",{className:"fb-bg-gray-200 fb-inline-block fb-rounded-full fb-px-2 fb-py-1",children:[e.items," items"]})]})},yr=[],vo={wf:{name:"Wicked Folders",author:"Wicked Plugins"},premio:{name:"Folders",author:"Premio"}},yo=()=>R("div",{className:"fb-opacity-50 fb-cursor-not-allowed fb-pointer-events-none",children:[R("h2",{children:[g("folders_for_post_types")," ",d(Et,{})]}),Object.entries(vo).map(([e,t])=>d("ul",{children:R("li",{className:"fb-bg-[#f6f7f7] fb-flex fb-items-center fb-p-3 fb-rounded-md fb-flex-wrap",children:[R("div",{className:"fb-w-1/2 wp-md:fb-w-1/3 wp-md:fb-mb-0 fb-mb-2",children:[d("h3",{className:"fb-mt-0 fb-mb-2",children:t.name}),R("p",{className:"fb-m-0 fb-font-medium description",children:[g("by")," (",t.author,")"]})]}),d("div",{className:"fb-w-1/2 wp-md:fb-w-1/3 wp-md:fb-text-center fb-text-right",children:d(go,{})}),d("div",{className:"wp-md:fb-w-1/3 fb-w-full",children:d(de,{disabled:yr.includes(e),className:ne("fb-ml-auto"),children:yr.includes(e)?g("imported"):g("import")})})]})},e))]}),{data_import:wo}=window.fbv_data,xo=()=>{const[e,t]=m.useState(wo.plugins),[r,n]=m.useState(!1),[a,o]=m.useState(""),i=async l=>{n(!0),o(l);try{const s=await lt.importPlugin(l),c={...e},b='<div class="njt-success-notice notice notice-success is-dismissible"><p>'+s.mess+`</p><button type="button" class="notice-dismiss" onClick="jQuery('.njt-success-notice').remove()"><span class="screen-reader-text">Dismiss this notice.</span></button></div>`;jQuery(".wrap").prepend(b),n(!1),o(""),c[l].completed=!0,t(c)}catch(s){console.log({error:s}),X.error(g("please_try_again")),n(!1),o("")}};return R(be,{children:[d("h3",{className:"fb-mt-0 fb-mb-4 fb-p-0",children:g("import")}),d("p",{className:"description fb-mb-4",children:g("import_folders_desc")}),!!Object.keys(e).length&&d("table",{className:"widefat striped fb-rounded-xl fb-overflow-hidden fb-shadow-none fb-border-[#EEF3F9]",children:d("tbody",{children:Object.values(e).map(l=>R("tr",{className:"importer-item",children:[R("td",{className:"import-system fb-block fb-p-3 wp-md:fb-table-cell wp-md:fb-p-6",children:[d("span",{className:"importer-title",children:d("b",{children:l.name})}),R("span",{children:[g("by")," (",l.author,")"]})]}),R("td",{className:"desc fb-block fb-p-3 wp-md:fb-p-5 wp-md:fb-table-cell",children:[d("span",{className:"importer-desc wp-md:fb-table-cell fb-mb-5 wp-md:fb-mb-5",dangerouslySetInnerHTML:{__html:l.description}}),d("span",{className:"importer-action fb-m-0 wp-md:fb-mt-4",children:d(de,{$secondary:!0,onClick:()=>i(l.prefix),className:ne("fb-m-0 fb-ml-auto wp-md:fb-ml-0",{"updating-message":l.prefix===a&&r}),disabled:r&&l.prefix===a||l.completed,children:l.completed?g("imported"):g("update_noti_btn")})})]})]},l.prefix))})})]})},_o=()=>R(ft,{children:[d(xo,{}),d(oe,{}),d(po,{}),d(oe,{}),d(yo,{})]}),ke={cleanData(){return ae("GET","/wipe-data")},async generateApi(){return ae("POST","/fbv-api",{act:"generate-key"})},async wipeData(){return ae("POST","/fb-wipe-clear-all-data")},async getOldFolders(){return ae("POST","/fb-get-old-data")},async insertOldFolders(e){return ae("POST","/fb-insert-old-data",e)},async generateAttachmentSize(e){return ae("POST","/generate-attachment-size",e)},async syncWPML(e){return dn("POST",e)}},{update_database_notice:Co}=window.fbv_data,{rest_api_key:Ro}=window.fbv_admin,{media_url:So}=window.fbv_data,{wpml:Eo}=window.fbv_admin,Ge=Oe.div`fb-flex fb-flex-col wp-md:fb-flex-row wp-md:fb-items-center fb-justify-between wp-md:fb-gap-10`,Ke=Oe.h3`fb-mt-0 fb-mb-4 fb-p-0`,ot=Oe.p`wp-md:fb-m-0`,Lo=()=>{const[e,t]=m.useState(!1),[r,n]=m.useState(!1),[a,o]=m.useState(Ro),[i,l]=m.useState(!1),[s,c]=m.useState(!1),[b,y]=m.useState(!1),[x,L]=m.useState(""),[_]=ja(),w=m.useRef(!1),v=!!_.get("autorun")&&Co,S=()=>{if(!confirm(g("are_you_sure")))return!1;t(!0),X.promise(ke.generateApi(),{loading:g("generating"),success:A=>(t(!1),A.success&&o(A.data.key),g("generated")),error:A=>(t(!1),A.responseJSON.message)})},M=async()=>{if(!confirm(g("are_you_sure")))return!1;n(!0),X.promise(ke.wipeData(),{loading:g("loading"),success:A=>(n(!1),location.reload(),A.data.mess),error:A=>(n(!1),A.data.mess)})},U=async(A,z)=>{if(typeof A[z]<"u")try{await ke.insertOldFolders({folders:A[z]}),await U(A,z+1)}catch{w.current=!0,X.error(g("please_try_again"))}else l(!1)},u=async()=>{w.current=!1;try{l(!0);const A=await ke.getOldFolders();if(A.success)try{if(await U(A.data.folders,0),l(!1),w.current)return;X.success(R("span",{children:[g("filebird_db_updated")," ",d("a",{href:So,children:g("go_to_media")})]}),{style:{minWidth:"365px"}}),jQuery("#njt-fb-update-db-noti .notice-dismiss").trigger("click")}catch(z){console.log({err:z}),l(!1)}}catch{l(!1),X.error(g("import_failed"))}},O=async()=>{c(!0);try{await H(1),X.success(g("generated")),c(!1)}catch{X.error(g("please_try_again")),c(!1)}},H=async A=>{try{const z=await ke.generateAttachmentSize({page:A});z.success&&z.next=="1"&&await H(A+1)}catch{X.error(g("please_try_again"))}},$=async()=>{y(!0);try{const A=await ke.syncWPML({action:"fbv_sync_wpml"});L(A.message),y(!1)}catch{y(!1)}};return m.useEffect(()=>{v&&u()},[v]),R(ft,{children:[R(Ge,{children:[R("div",{className:"fb-flex-1",children:[d(Ke,{children:g("import_from_old_version")}),d(ot,{children:g("update_old_folder_desc")})]}),d(de,{disabled:i,className:ne("fb-ml-auto wp-md:fb-w-0",{"updating-message":i}),onClick:()=>u(),children:g("update")})]}),d(oe,{}),R(Ge,{children:[R("div",{className:"fb-flex-1",children:[d(Ke,{children:g("rest_api_key")}),d("p",{dangerouslySetInnerHTML:{__html:g("generate_api_desc")}}),d("input",{className:ne({hidden:!a,"large-text":!0}),readOnly:!0,value:a,type:"text",autoComplete:"off"})]}),d(de,{className:ne("fb-ml-auto fb-mt-4 wp-md:fb-w-0 wp-md:fb-m-0",{"updating-message":e}),disabled:e,onClick:S,children:g("generate")})]}),d(oe,{}),R(Ge,{children:[R("div",{className:"fb-flex-1",children:[d(Ke,{children:g("attachment_size")}),d(ot,{dangerouslySetInnerHTML:{__html:g("generate_attachment_size_desc")}})]}),d(de,{className:ne("fb-ml-auto wp-md:fb-w-0",{"updating-message":s}),disabled:s,onClick:O,children:s?g("generating"):g("generate")})]}),d(oe,{}),Eo.display_sync&&R(be,{children:[R(Ge,{children:[R("div",{className:"fb-flex-1",children:[d(Ke,{children:g("sync_wpml")}),d(ot,{dangerouslySetInnerHTML:{__html:g("sync_wpml_desc")}})]}),d(de,{className:ne("fb-ml-auto wp-md:fb-w-0",{"updating-message":b}),disabled:b||!!x,onClick:$,type:"button",children:x||g("sync")})]}),d(oe,{})]}),R(Ge,{children:[R("div",{className:"fb-flex-1",children:[d(Ke,{children:g("clear_all_data")}),d(ot,{dangerouslySetInnerHTML:{__html:g("clear_all_data_desc")}})]}),d(de,{className:ne("fb-ml-auto wp-md:fb-w-0",{"updating-message":r}),$danger:!0,disabled:r,onClick:M,children:g("clear")})]})]})},Po=ka([{path:"/",element:d(Ja,{}),errorElement:d(Ya,{}),children:[{index:!0,element:d(pr,{})},{path:"activation",element:d(pr,{})},{path:":tools",element:d(Lo,{})},{path:"import-export",element:d(_o,{})},{path:"settings",element:d(uo,{})}]}]);document.addEventListener("DOMContentLoaded",()=>{fn();const e=document.getElementById("filebird-setting");e&&un.createRoot(e).render(d(hn.StrictMode,{children:d(ya,{router:Po})}))});
