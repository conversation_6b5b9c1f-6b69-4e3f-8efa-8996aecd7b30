!function(_){"use strict";function y(e,t){return 0<t.length?((e=_('<div class="notice notice-'+e+' is-dismissible"><p>'+t+'</p><button type="button" class="notice-dismiss"></button></div>')).children("button").on("click",function(e){_(e.target).parent().remove()}),e):null}function h(t){var e=t.find(".button-copy"),n=t.find(".button-send"),a=t.find(".button-delete");let o=e.add(n).add(a),i=t.find("hr").first(),r=null,u=parseInt(t.data("id"))||0,d=t.data("type"),s=t.find(".mphbrp-payment-request-link").attr("href"),c=!1;function l(e,t){p();e=y(e,t);null!==e&&(e.insertBefore(i),r=e)}function p(){null!==r&&(r.remove(),r=null)}function f(){c=!c,o.prop("disabled",c)}e.on("click",function(e){e.preventDefault(),f(),p(),function(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);var t=document.createElement("textarea");t.style.position="fixed",t.style.top="0",t.style.left="0",t.value=e,document.body.appendChild(t),t.focus(),t.select();let n=!1;try{n=document.execCommand("copy")}catch(e){}return document.body.removeChild(t),n?Promise.resolve(n):Promise.reject(n)}(s).then(function(){l("success",MPHBRP._data.messages.copied),f()},function(){l("error",MPHBRP._data.messages.unableToCopy),f()})}),n.on("click",function(e){e.preventDefault(),f(),p(),_.ajax({url:MPHBRP._data.ajaxUrl,type:"POST",dataType:"json",data:{action:"mphbrp_send_payment_request",mphb_nonce:MPHBRP._data.nonces.sendPaymentRequest,booking_id:MPHBRP._data.page.bookingId,request:{id:u,type:d}}}).done(function(e){l(e.data.status,e.data.message)}).fail(function(e){l("error",e.statusText)}).always(function(){f()})}),a.on("click",function(e){e.preventDefault(),f(),p(),_.ajax({url:MPHBRP._data.ajaxUrl,type:"POST",dataType:"json",data:{action:"mphbrp_delete_payment_request",mphb_nonce:MPHBRP._data.nonces.deletePaymentRequest,booking_id:MPHBRP._data.page.bookingId,request:{id:u}}}).done(function(e){"success"==e.data.status?t.remove():l("error",e.data.message)}).fail(function(e){l("error",e.statusText)}).always(function(){f()})})}_(function(){{var o=_("#mphbrp-disable-auto-request-control");let n=o.find('input[type="checkbox"]'),a=null;function i(e,t){r();e=y(e,t);null!==e&&(e.insertAfter(o),a=e)}function r(){null!==a&&(a.remove(),a=null)}n.on("change",function(e){n.prop("disabled",!0),r();let t=n.prop("checked");_.ajax({url:MPHBRP._data.ajaxUrl,type:"POST",dataType:"json",data:{action:"mphbrp_disable_auto_request",mphb_nonce:MPHBRP._data.nonces.disableAutoRequest,booking_id:MPHBRP._data.page.bookingId,disabled:t}}).done(function(e){"error"==e.data.status&&(i("error",e.data.message),n.prop("checked",!t))}).fail(function(e){i("error",e.statusText),n.prop("checked",!t)}).always(function(){n.prop("disabled",!1)})})}_(".mphbrp-payment-request").each(function(e,t){h(_(t))});var s=_("#mphb-add-payment-request-form");if(0!=s.length){var c=s.find(".mphbrp-custom-fields"),l=s.find(".button-new");let e=s.find(".button-add"),t=s.find(".button-cancel"),n=c.add(l).add(e).add(t),a=null,o=s.find('input[name="mphbrp_custom_request_type"]'),i=s.find('input[name="mphbrp_custom_request_amount"]'),r=s.find('textarea[name="mphbrp_custom_request_description"]'),u=!1,d=!1;function p(e,t){f();e=y(e,t);null!==e&&(s.append(e),a=e)}function f(){null!==a&&(a.remove(),a=null)}function m(){u=!u,e.prop("disabled",u),t.prop("disabled",u)}function b(){n.toggleClass("mphb-hide"),(d=!d)||(o.filter('[value="percent"]').prop("checked",!0),i.val(""),r.val(""),f())}l.on("click",function(e){e.preventDefault(),b()}),e.on("click",function(e){e.preventDefault(),m(),f(),_.ajax({url:MPHBRP._data.ajaxUrl,type:"POST",dataType:"json",data:{action:"mphbrp_add_payment_request",mphb_nonce:MPHBRP._data.nonces.addPaymentRequest,booking_id:MPHBRP._data.page.bookingId,request:function(){var e={type:o.filter(":checked").val(),amount:parseFloat(i.val()),description:r.val()};isNaN(e.amount)?e.amount=0:e.amount=Math.abs(e.amount);return e}()}}).done(function(e){var t;"success"==e.data.status?(t=e.data.html,t=_(t),_("#mphbrp-payment-requests").append(t),h(t),b()):p("error",e.data.message)}).fail(function(e){p("error",e.statusText)}).always(function(){m()})}),t.on("click",function(e){e.preventDefault(),b()}),s.find("input, textarea").on("keydown",function(e){"Enter"==e.key&&e.preventDefault()})}})}(jQuery);