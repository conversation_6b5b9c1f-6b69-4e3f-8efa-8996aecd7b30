<?php

namespace MPHB\Stripe\Util;

class ObjectTypes
{
    /**
     * @var array Mapping from object types to resource classes
     */
    const mapping = [
        \MPHB\Stripe\Collection::OBJECT_NAME => \MPHB\Stripe\Collection::class,
        \MPHB\Stripe\Issuing\CardDetails::OBJECT_NAME => \MPHB\Stripe\Issuing\CardDetails::class,
        \MPHB\Stripe\SearchResult::OBJECT_NAME => \MPHB\Stripe\SearchResult::class,
        \MPHB\Stripe\File::OBJECT_NAME_ALT => \MPHB\Stripe\File::class,
        // The beginning of the section generated from our OpenAPI spec
        \MPHB\Stripe\Account::OBJECT_NAME => \MPHB\Stripe\Account::class,
        \MPHB\Stripe\AccountLink::OBJECT_NAME => \MPHB\Stripe\AccountLink::class,
        \MPHB\Stripe\AccountSession::OBJECT_NAME => \MPHB\Stripe\AccountSession::class,
        \MPHB\Stripe\ApplePayDomain::OBJECT_NAME => \MPHB\Stripe\ApplePayDomain::class,
        \MPHB\Stripe\ApplicationFee::OBJECT_NAME => \MPHB\Stripe\ApplicationFee::class,
        \MPHB\Stripe\ApplicationFeeRefund::OBJECT_NAME => \MPHB\Stripe\ApplicationFeeRefund::class,
        \MPHB\Stripe\Apps\Secret::OBJECT_NAME => \MPHB\Stripe\Apps\Secret::class,
        \MPHB\Stripe\Balance::OBJECT_NAME => \MPHB\Stripe\Balance::class,
        \MPHB\Stripe\BalanceTransaction::OBJECT_NAME => \MPHB\Stripe\BalanceTransaction::class,
        \MPHB\Stripe\BankAccount::OBJECT_NAME => \MPHB\Stripe\BankAccount::class,
        \MPHB\Stripe\BillingPortal\Configuration::OBJECT_NAME => \MPHB\Stripe\BillingPortal\Configuration::class,
        \MPHB\Stripe\BillingPortal\Session::OBJECT_NAME => \MPHB\Stripe\BillingPortal\Session::class,
        \MPHB\Stripe\Capability::OBJECT_NAME => \MPHB\Stripe\Capability::class,
        \MPHB\Stripe\Card::OBJECT_NAME => \MPHB\Stripe\Card::class,
        \MPHB\Stripe\CashBalance::OBJECT_NAME => \MPHB\Stripe\CashBalance::class,
        \MPHB\Stripe\Charge::OBJECT_NAME => \MPHB\Stripe\Charge::class,
        \MPHB\Stripe\Checkout\Session::OBJECT_NAME => \MPHB\Stripe\Checkout\Session::class,
        \MPHB\Stripe\Climate\Order::OBJECT_NAME => \MPHB\Stripe\Climate\Order::class,
        \MPHB\Stripe\Climate\Product::OBJECT_NAME => \MPHB\Stripe\Climate\Product::class,
        \MPHB\Stripe\Climate\Supplier::OBJECT_NAME => \MPHB\Stripe\Climate\Supplier::class,
        \MPHB\Stripe\CountrySpec::OBJECT_NAME => \MPHB\Stripe\CountrySpec::class,
        \MPHB\Stripe\Coupon::OBJECT_NAME => \MPHB\Stripe\Coupon::class,
        \MPHB\Stripe\CreditNote::OBJECT_NAME => \MPHB\Stripe\CreditNote::class,
        \MPHB\Stripe\CreditNoteLineItem::OBJECT_NAME => \MPHB\Stripe\CreditNoteLineItem::class,
        \MPHB\Stripe\Customer::OBJECT_NAME => \MPHB\Stripe\Customer::class,
        \MPHB\Stripe\CustomerBalanceTransaction::OBJECT_NAME => \MPHB\Stripe\CustomerBalanceTransaction::class,
        \MPHB\Stripe\CustomerCashBalanceTransaction::OBJECT_NAME => \MPHB\Stripe\CustomerCashBalanceTransaction::class,
        \MPHB\Stripe\CustomerSession::OBJECT_NAME => \MPHB\Stripe\CustomerSession::class,
        \MPHB\Stripe\Discount::OBJECT_NAME => \MPHB\Stripe\Discount::class,
        \MPHB\Stripe\Dispute::OBJECT_NAME => \MPHB\Stripe\Dispute::class,
        \MPHB\Stripe\EphemeralKey::OBJECT_NAME => \MPHB\Stripe\EphemeralKey::class,
        \MPHB\Stripe\Event::OBJECT_NAME => \MPHB\Stripe\Event::class,
        \MPHB\Stripe\ExchangeRate::OBJECT_NAME => \MPHB\Stripe\ExchangeRate::class,
        \MPHB\Stripe\File::OBJECT_NAME => \MPHB\Stripe\File::class,
        \MPHB\Stripe\FileLink::OBJECT_NAME => \MPHB\Stripe\FileLink::class,
        \MPHB\Stripe\FinancialConnections\Account::OBJECT_NAME => \MPHB\Stripe\FinancialConnections\Account::class,
        \MPHB\Stripe\FinancialConnections\AccountOwner::OBJECT_NAME => \MPHB\Stripe\FinancialConnections\AccountOwner::class,
        \MPHB\Stripe\FinancialConnections\AccountOwnership::OBJECT_NAME => \MPHB\Stripe\FinancialConnections\AccountOwnership::class,
        \MPHB\Stripe\FinancialConnections\Session::OBJECT_NAME => \MPHB\Stripe\FinancialConnections\Session::class,
        \MPHB\Stripe\FinancialConnections\Transaction::OBJECT_NAME => \MPHB\Stripe\FinancialConnections\Transaction::class,
        \MPHB\Stripe\FundingInstructions::OBJECT_NAME => \MPHB\Stripe\FundingInstructions::class,
        \MPHB\Stripe\Identity\VerificationReport::OBJECT_NAME => \MPHB\Stripe\Identity\VerificationReport::class,
        \MPHB\Stripe\Identity\VerificationSession::OBJECT_NAME => \MPHB\Stripe\Identity\VerificationSession::class,
        \MPHB\Stripe\Invoice::OBJECT_NAME => \MPHB\Stripe\Invoice::class,
        \MPHB\Stripe\InvoiceItem::OBJECT_NAME => \MPHB\Stripe\InvoiceItem::class,
        \MPHB\Stripe\InvoiceLineItem::OBJECT_NAME => \MPHB\Stripe\InvoiceLineItem::class,
        \MPHB\Stripe\Issuing\Authorization::OBJECT_NAME => \MPHB\Stripe\Issuing\Authorization::class,
        \MPHB\Stripe\Issuing\Card::OBJECT_NAME => \MPHB\Stripe\Issuing\Card::class,
        \MPHB\Stripe\Issuing\Cardholder::OBJECT_NAME => \MPHB\Stripe\Issuing\Cardholder::class,
        \MPHB\Stripe\Issuing\Dispute::OBJECT_NAME => \MPHB\Stripe\Issuing\Dispute::class,
        \MPHB\Stripe\Issuing\Token::OBJECT_NAME => \MPHB\Stripe\Issuing\Token::class,
        \MPHB\Stripe\Issuing\Transaction::OBJECT_NAME => \MPHB\Stripe\Issuing\Transaction::class,
        \MPHB\Stripe\LineItem::OBJECT_NAME => \MPHB\Stripe\LineItem::class,
        \MPHB\Stripe\LoginLink::OBJECT_NAME => \MPHB\Stripe\LoginLink::class,
        \MPHB\Stripe\Mandate::OBJECT_NAME => \MPHB\Stripe\Mandate::class,
        \MPHB\Stripe\PaymentIntent::OBJECT_NAME => \MPHB\Stripe\PaymentIntent::class,
        \MPHB\Stripe\PaymentLink::OBJECT_NAME => \MPHB\Stripe\PaymentLink::class,
        \MPHB\Stripe\PaymentMethod::OBJECT_NAME => \MPHB\Stripe\PaymentMethod::class,
        \MPHB\Stripe\PaymentMethodConfiguration::OBJECT_NAME => \MPHB\Stripe\PaymentMethodConfiguration::class,
        \MPHB\Stripe\PaymentMethodDomain::OBJECT_NAME => \MPHB\Stripe\PaymentMethodDomain::class,
        \MPHB\Stripe\Payout::OBJECT_NAME => \MPHB\Stripe\Payout::class,
        \MPHB\Stripe\Person::OBJECT_NAME => \MPHB\Stripe\Person::class,
        \MPHB\Stripe\Plan::OBJECT_NAME => \MPHB\Stripe\Plan::class,
        \MPHB\Stripe\Price::OBJECT_NAME => \MPHB\Stripe\Price::class,
        \MPHB\Stripe\Product::OBJECT_NAME => \MPHB\Stripe\Product::class,
        \MPHB\Stripe\PromotionCode::OBJECT_NAME => \MPHB\Stripe\PromotionCode::class,
        \MPHB\Stripe\Quote::OBJECT_NAME => \MPHB\Stripe\Quote::class,
        \MPHB\Stripe\Radar\EarlyFraudWarning::OBJECT_NAME => \MPHB\Stripe\Radar\EarlyFraudWarning::class,
        \MPHB\Stripe\Radar\ValueList::OBJECT_NAME => \MPHB\Stripe\Radar\ValueList::class,
        \MPHB\Stripe\Radar\ValueListItem::OBJECT_NAME => \MPHB\Stripe\Radar\ValueListItem::class,
        \MPHB\Stripe\Refund::OBJECT_NAME => \MPHB\Stripe\Refund::class,
        \MPHB\Stripe\Reporting\ReportRun::OBJECT_NAME => \MPHB\Stripe\Reporting\ReportRun::class,
        \MPHB\Stripe\Reporting\ReportType::OBJECT_NAME => \MPHB\Stripe\Reporting\ReportType::class,
        \MPHB\Stripe\Review::OBJECT_NAME => \MPHB\Stripe\Review::class,
        \MPHB\Stripe\SetupAttempt::OBJECT_NAME => \MPHB\Stripe\SetupAttempt::class,
        \MPHB\Stripe\SetupIntent::OBJECT_NAME => \MPHB\Stripe\SetupIntent::class,
        \MPHB\Stripe\ShippingRate::OBJECT_NAME => \MPHB\Stripe\ShippingRate::class,
        \MPHB\Stripe\Sigma\ScheduledQueryRun::OBJECT_NAME => \MPHB\Stripe\Sigma\ScheduledQueryRun::class,
        \MPHB\Stripe\Source::OBJECT_NAME => \MPHB\Stripe\Source::class,
        \MPHB\Stripe\SourceTransaction::OBJECT_NAME => \MPHB\Stripe\SourceTransaction::class,
        \MPHB\Stripe\Subscription::OBJECT_NAME => \MPHB\Stripe\Subscription::class,
        \MPHB\Stripe\SubscriptionItem::OBJECT_NAME => \MPHB\Stripe\SubscriptionItem::class,
        \MPHB\Stripe\SubscriptionSchedule::OBJECT_NAME => \MPHB\Stripe\SubscriptionSchedule::class,
        \MPHB\Stripe\Tax\Calculation::OBJECT_NAME => \MPHB\Stripe\Tax\Calculation::class,
        \MPHB\Stripe\Tax\CalculationLineItem::OBJECT_NAME => \MPHB\Stripe\Tax\CalculationLineItem::class,
        \MPHB\Stripe\Tax\Registration::OBJECT_NAME => \MPHB\Stripe\Tax\Registration::class,
        \MPHB\Stripe\Tax\Settings::OBJECT_NAME => \MPHB\Stripe\Tax\Settings::class,
        \MPHB\Stripe\Tax\Transaction::OBJECT_NAME => \MPHB\Stripe\Tax\Transaction::class,
        \MPHB\Stripe\Tax\TransactionLineItem::OBJECT_NAME => \MPHB\Stripe\Tax\TransactionLineItem::class,
        \MPHB\Stripe\TaxCode::OBJECT_NAME => \MPHB\Stripe\TaxCode::class,
        \MPHB\Stripe\TaxId::OBJECT_NAME => \MPHB\Stripe\TaxId::class,
        \MPHB\Stripe\TaxRate::OBJECT_NAME => \MPHB\Stripe\TaxRate::class,
        \MPHB\Stripe\Terminal\Configuration::OBJECT_NAME => \MPHB\Stripe\Terminal\Configuration::class,
        \MPHB\Stripe\Terminal\ConnectionToken::OBJECT_NAME => \MPHB\Stripe\Terminal\ConnectionToken::class,
        \MPHB\Stripe\Terminal\Location::OBJECT_NAME => \MPHB\Stripe\Terminal\Location::class,
        \MPHB\Stripe\Terminal\Reader::OBJECT_NAME => \MPHB\Stripe\Terminal\Reader::class,
        \MPHB\Stripe\TestHelpers\TestClock::OBJECT_NAME => \MPHB\Stripe\TestHelpers\TestClock::class,
        \MPHB\Stripe\Token::OBJECT_NAME => \MPHB\Stripe\Token::class,
        \MPHB\Stripe\Topup::OBJECT_NAME => \MPHB\Stripe\Topup::class,
        \MPHB\Stripe\Transfer::OBJECT_NAME => \MPHB\Stripe\Transfer::class,
        \MPHB\Stripe\TransferReversal::OBJECT_NAME => \MPHB\Stripe\TransferReversal::class,
        \MPHB\Stripe\Treasury\CreditReversal::OBJECT_NAME => \MPHB\Stripe\Treasury\CreditReversal::class,
        \MPHB\Stripe\Treasury\DebitReversal::OBJECT_NAME => \MPHB\Stripe\Treasury\DebitReversal::class,
        \MPHB\Stripe\Treasury\FinancialAccount::OBJECT_NAME => \MPHB\Stripe\Treasury\FinancialAccount::class,
        \MPHB\Stripe\Treasury\FinancialAccountFeatures::OBJECT_NAME => \MPHB\Stripe\Treasury\FinancialAccountFeatures::class,
        \MPHB\Stripe\Treasury\InboundTransfer::OBJECT_NAME => \MPHB\Stripe\Treasury\InboundTransfer::class,
        \MPHB\Stripe\Treasury\OutboundPayment::OBJECT_NAME => \MPHB\Stripe\Treasury\OutboundPayment::class,
        \MPHB\Stripe\Treasury\OutboundTransfer::OBJECT_NAME => \MPHB\Stripe\Treasury\OutboundTransfer::class,
        \MPHB\Stripe\Treasury\ReceivedCredit::OBJECT_NAME => \MPHB\Stripe\Treasury\ReceivedCredit::class,
        \MPHB\Stripe\Treasury\ReceivedDebit::OBJECT_NAME => \MPHB\Stripe\Treasury\ReceivedDebit::class,
        \MPHB\Stripe\Treasury\Transaction::OBJECT_NAME => \MPHB\Stripe\Treasury\Transaction::class,
        \MPHB\Stripe\Treasury\TransactionEntry::OBJECT_NAME => \MPHB\Stripe\Treasury\TransactionEntry::class,
        \MPHB\Stripe\UsageRecord::OBJECT_NAME => \MPHB\Stripe\UsageRecord::class,
        \MPHB\Stripe\UsageRecordSummary::OBJECT_NAME => \MPHB\Stripe\UsageRecordSummary::class,
        \MPHB\Stripe\WebhookEndpoint::OBJECT_NAME => \MPHB\Stripe\WebhookEndpoint::class,
    ];
}
