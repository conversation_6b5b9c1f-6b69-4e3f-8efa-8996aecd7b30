"use strict";!function(W){W(function(){var a;MPHB.ajaxApiHelper={_activeAjaxRequests:{},_roomTypeCalendarsData:{},getLoadedRoomTypeCalendarData:function(e,t,a,n){var i=1<arguments.length&&void 0!==t&&t,r=2<arguments.length&&void 0!==a&&a,s=3<arguments.length&&void 0!==n&&n,o=JSON.stringify([e,i,r,s]);return void 0===this._roomTypeCalendarsData[o]&&(this._roomTypeCalendarsData[o]={}),this._roomTypeCalendarsData[o]},loadRoomTypeCalendarData:function(e,t,a,n,i,r,s,o,l){var c=this,h=6<arguments.length&&void 0!==s?s:function(){},d=7<arguments.length&&void 0!==o?o:function(){},m=8<arguments.length&&void 0!==l?l:0,p=new Date(e.getTime());p.setDate(p.getDate()-1);var u=W.datepick.formatDate("yyyy-mm-dd",p),f=new Date(e.getFullYear(),e.getMonth()+t,1),y=W.datepick.formatDate("yyyy-mm-dd",f),k=this.getLoadedRoomTypeCalendarData(a,n,i,r),C=k[u];if(void 0!==k[u]&&k[u].hasOwnProperty("roomTypeStatus")&&void 0!==k[y]&&k[y].hasOwnProperty("roomTypeStatus"))return k;for(;p.getTime()<f.getTime()&&void 0!==C&&C.hasOwnProperty("roomTypeStatus");)p=W.datepick.add(p,1,"d"),u=W.datepick.formatDate("yyyy-mm-dd",p),C=k[u];if(p.getTime()<f.getTime()){if(0<m){var b=new Date(p.getFullYear(),p.getMonth()+m+1,1);f.getTime()<b.getTime()&&(f=b,y=W.datepick.formatDate("yyyy-mm-dd",f))}h();var g=JSON.stringify([a,n,i,r,u,y]);if(!this._activeAjaxRequests[g]){var _=performance.now(),v={action:"mphb_get_room_type_calendar_data",mphb_nonce:MPHB._data.nonces.mphb_get_room_type_calendar_data,mphb_is_admin:MPHB._data.isAdmin,mphb_locale:MPHB._data.settings.currentLanguage,start_date:u,end_date:y,room_type_id:a,is_show_prices:n,is_truncate_prices:i,is_show_prices_currency:r};console.log("START LOADING: "+JSON.stringify(v));var D=W.ajax({url:MPHB._data.ajaxUrl,type:"GET",dataType:"json",data:v,success:function(e){var t=performance.now()-_;console.log("DATA LOADED: "+JSON.stringify(v)+" TIME: "+(t/1e3).toFixed(2)+" sec",e.data),Object.assign(k,e.data)},error:function(e){void 0!==e.responseJSON.data.errorMessage?console.error(e.responseJSON.data.errorMessage):console.error(e)},complete:function(){delete c._activeAjaxRequests[g]}});this._activeAjaxRequests[g]=D}this._activeAjaxRequests[g].then(function(){d()})}}},MPHB.calendarHelper={ROOM_STATUS_AVAILABLE:"available",ROOM_STATUS_NOT_AVAILABLE:"not-available",ROOM_STATUS_BOOKED:"booked",ROOM_STATUS_PAST:"past",ROOM_STATUS_EARLIER_MIN_ADVANCE:"earlier-min-advance",ROOM_STATUS_LATER_MAX_ADVANCE:"later-max-advance",ROOM_STATUS_BOOKING_BUFFER:"booking-buffer",getCalendarDateAttributesFromAvailability:function(e,t,a,n,i,r,s,o,l,c){var h=4<arguments.length&&void 0!==i&&i,d=5<arguments.length&&void 0!==r?r:null,m=6<arguments.length&&void 0!==s?s:null,p=7<arguments.length&&void 0!==o?o:null,u=8<arguments.length&&void 0!==l?l:null,f=9<arguments.length&&void 0!==c?c:null,y={selectable:!1,dateClass:"mphb-date-cell",title:""};if(!a)return y.dateClass+=" mphb-extra-date",y;var k=n[W.datepick.formatDate("yyyy-mm-dd",t)];if(void 0===k||0===Object.keys(k).length||!k.hasOwnProperty("roomTypeStatus"))return y;var C=new Date(t.getTime());C.setDate(C.getDate()-1);var b=n[W.datepick.formatDate("yyyy-mm-dd",C)],g=void 0!==b&&b.hasOwnProperty("roomTypeStatus")&&MPHB.calendarHelper.ROOM_STATUS_AVAILABLE===b.roomTypeStatus,_=void 0!==b&&b.hasOwnProperty("roomTypeStatus")&&MPHB.calendarHelper.ROOM_STATUS_PAST===b.roomTypeStatus,v=void 0!==b&&b.hasOwnProperty("roomTypeStatus")&&MPHB.calendarHelper.ROOM_STATUS_NOT_AVAILABLE===b.roomTypeStatus,D=void 0!==b&&b.hasOwnProperty("isCheckInNotAllowed")&&b.isCheckInNotAllowed,S=void 0!==b&&b.hasOwnProperty("isStayInNotAllowed")&&b.isStayInNotAllowed,P=v&&!S&&void 0!==b&&b.hasOwnProperty("availableRoomsCount")&&0<b.availableRoomsCount,M=new Date(t.getTime());M.setDate(t.getDate()+1);var H=n[W.datepick.formatDate("yyyy-mm-dd",M)],T=void 0!==H&&H.hasOwnProperty("isStayInNotAllowed")&&H.isStayInNotAllowed,B=void 0!==H&&H.hasOwnProperty("isCheckOutNotAllowed")&&H.isCheckOutNotAllowed,w=void 0!==H&&H.hasOwnProperty("roomTypeStatus")&&MPHB.calendarHelper.ROOM_STATUS_NOT_AVAILABLE===H.roomTypeStatus,O=w&&!T&&void 0!==H&&H.hasOwnProperty("availableRoomsCount")&&0<H.availableRoomsCount,A=MPHB.calendarHelper.ROOM_STATUS_NOT_AVAILABLE===k.roomTypeStatus,I=!(MPHB.calendarHelper.ROOM_STATUS_BOOKED!==k.roomTypeStatus||k.hasOwnProperty("isCheckInDate")&&k.isCheckInDate||k.hasOwnProperty("isCheckOutDate")&&k.isCheckOutDate),E=k.hasOwnProperty("isCheckInDate")&&k.isCheckInDate,F=k.hasOwnProperty("isCheckOutDate")&&k.isCheckOutDate,x=k.hasOwnProperty("isStayInNotAllowed")&&k.isStayInNotAllowed,R=k.hasOwnProperty("isCheckInNotAllowed")&&k.isCheckInNotAllowed,U=k.hasOwnProperty("isCheckOutNotAllowed")&&k.isCheckOutNotAllowed,L=k.hasOwnProperty("isEarlierThanMinAdvanceDate")&&k.isEarlierThanMinAdvanceDate,$=k.hasOwnProperty("isLaterThanMaxAdvanceDate")&&k.isLaterThanMaxAdvanceDate,N=A&&!x&&k.hasOwnProperty("availableRoomsCount")&&0<k.availableRoomsCount;MPHB.calendarHelper.ROOM_STATUS_PAST===k.roomTypeStatus?(y.dateClass+=" mphb-past-date",y.isPastDate=!0):(MPHB.calendarHelper.ROOM_STATUS_AVAILABLE===k.roomTypeStatus?y.dateClass+=" mphb-available-date":MPHB.calendarHelper.ROOM_STATUS_NOT_AVAILABLE===k.roomTypeStatus?y.dateClass+=" mphb-not-available-date":MPHB.calendarHelper.ROOM_STATUS_BOOKED===k.roomTypeStatus&&(y.dateClass+=" mphb-booked-date"),E?y.dateClass+=" mphb-date-check-in":F&&(y.dateClass+=" mphb-date-check-out",MPHB.calendarHelper.ROOM_STATUS_EARLIER_MIN_ADVANCE!==k.roomTypeStatus&&MPHB.calendarHelper.ROOM_STATUS_LATER_MAX_ADVANCE!==k.roomTypeStatus||(y.dateClass+=" mphb-booked-date mphb-available-date"))),x&&(y.dateClass+=" mphb-not-stay-in-date"),N&&U?(1===e?y.dateClass+=" mphb-out-of-season-date":2===e?y.dateClass+=" mphb-not-check-in-date":3===e&&(y.dateClass+=" mphb-not-check-out-date"),y.isUnavailable=!0):((g&&N&&!U||F&&N||O&&B&&R)&&(1===e?y.dateClass+=" mphb-out-of-season-date--check-in":2===e&&(y.dateClass+=" mphb-not-check-in-date"),y.isUnavailableCheckIn=!0),P&&U&&(1===e?y.dateClass+=" mphb-out-of-season-date--check-out":3===e&&(y.dateClass+=" mphb-not-check-out-date"),y.isUnavailableCheckOut=!0)),A&&!x&&!N&&(!g||D)||I||S&&E||F&&x||x&&U?(1===e?y.dateClass+=" mphb-mark-as-unavailable":2===e?y.dateClass+=" mphb-not-check-in-date":3===e&&(y.dateClass+=" mphb-not-check-out-date"),y.isUnavailable=!0):((x||E||L&&1!==e||$&&1!==e||R&&2===e||R&&T&&B||R&&w&&!N&&!O&&!T||A&&g&&!N&&!D)&&(1===e?y.dateClass+=" mphb-mark-as-unavailable--check-in":2===e&&(y.dateClass+=" mphb-not-check-in-date"),y.isUnavailableCheckIn=!0),(F||U&&3===e||S&&U||_&&U||v&&U&&!P)&&(1===e?y.dateClass+=" mphb-mark-as-unavailable--check-out":3===e&&(y.dateClass+=" mphb-not-check-out-date"),y.isUnavailableCheckOut=!0)),y.title="";var j=[];return MPHB.calendarHelper.ROOM_STATUS_PAST===k.roomTypeStatus?y.title=MPHB._data.translations.past:(MPHB.calendarHelper.ROOM_STATUS_AVAILABLE===k.roomTypeStatus&&(y.title=MPHB._data.translations.available+" ("+(k.hasOwnProperty("availableRoomsCount")?k.availableRoomsCount:"undefined")+")"),MPHB.calendarHelper.ROOM_STATUS_NOT_AVAILABLE===k.roomTypeStatus&&(y.title=MPHB._data.translations.notAvailable),MPHB.calendarHelper.ROOM_STATUS_BOOKED===k.roomTypeStatus&&(y.title=MPHB._data.translations.booked),MPHB.calendarHelper.ROOM_STATUS_EARLIER_MIN_ADVANCE!==k.roomTypeStatus&&MPHB.calendarHelper.ROOM_STATUS_LATER_MAX_ADVANCE!==k.roomTypeStatus||(y.title=MPHB._data.translations.notAvailable),MPHB.calendarHelper.ROOM_STATUS_NOT_AVAILABLE!==k.roomTypeStatus&&MPHB.calendarHelper.ROOM_STATUS_BOOKED!==k.roomTypeStatus||3!==e||U||(y.title=MPHB._data.translations.available),x&&3!==e&&j.push(MPHB._data.translations.notStayIn),!L&&MPHB.calendarHelper.ROOM_STATUS_EARLIER_MIN_ADVANCE!==k.roomTypeStatus||j.push(MPHB._data.translations.earlierMinAdvance),!$&&MPHB.calendarHelper.ROOM_STATUS_LATER_MAX_ADVANCE!==k.roomTypeStatus||j.push(MPHB._data.translations.laterMaxAdvance),R&&3!==e&&j.push(MPHB._data.translations.notCheckIn),U&&2!==e&&j.push(MPHB._data.translations.notCheckOut)),j.length&&(y.title+="\n"+MPHB._data.translations.rules+" "+j.join(", ")),h&&k.hasOwnProperty("price")&&(y.content=t.getDate()+'<span class="mphb-date-cell__price">'+k.price+"</span>"),2===e?!a||y.isPastDate||y.isUnavailable||y.isUnavailableCheckIn?y.dateClass+=" mphb-unselectable-date":(y.selectable=!0,y.dateClass+=" mphb-selectable-date"):3===e&&(a&&(null!==d&&MPHB.Utils.formatDateToCompare(t)===MPHB.Utils.formatDateToCompare(d)&&(y.title+=" "+MPHB._data.translations.checkInDate,y.dateClass+=" mphb-check-in-date"),null!==m&&m.getTime()>t.getTime()&&(y.title+="\n"+MPHB._data.translations.lessThanMinDaysStay,y.dateClass+=" mphb-earlier-min-date"),null!==p&&p.getTime()<t.getTime()&&(y.title+="\n"+MPHB._data.translations.moreThanMaxDaysStay,y.dateClass+=" mphb-later-max-date")),(null===u||u.getTime()<=t.getTime())&&(null===f||f.getTime()>=t.getTime())&&!y.isUnavailableCheckOut&&!y.isUnavailable?(y.selectable=!0,y.dateClass+=" mphb-selectable-date"):y.dateClass+=" mphb-unselectable-date"),y},calculateMinMaxCheckOutDateForSelection:function(e,t){var a=MPHB.Utils.cloneDate(e),n=W.datepick.formatDate("yyyy-mm-dd",a),i=null,r=!1;a.setHours(12,0,0,0);var s=null,o=null,l=null,c=null;if(void 0===(i=t[n])||0===Object.keys(i).length||!i.hasOwnProperty("roomTypeStatus"))return{minStayDateAfterCheckIn:s,maxStayDateAfterCheckIn:o,minCheckOutDateForSelection:l,maxCheckOutDateForSelection:c};i.hasOwnProperty("minStayNights")&&(a.setDate(a.getDate()+i.minStayNights),a.setHours(23,59,59,999),n=W.datepick.formatDate("yyyy-mm-dd",a),(s=MPHB.Utils.cloneDate(a)).setHours(0,0,0,1)),i.hasOwnProperty("maxStayNights")&&((o=MPHB.Utils.cloneDate(e)).setDate(o.getDate()+i.maxStayNights),o.setHours(23,59,59,999));do{if(void 0===(i=t[n])||0===Object.keys(i).length||!i.hasOwnProperty("roomTypeStatus"))break;MPHB.calendarHelper.ROOM_STATUS_PAST===i.roomTypeStatus||MPHB.calendarHelper.ROOM_STATUS_EARLIER_MIN_ADVANCE===i.roomTypeStatus||i.hasOwnProperty("isCheckOutNotAllowed")&&i.isCheckOutNotAllowed||(null===l&&(l=MPHB.Utils.cloneDate(a)),c=MPHB.Utils.cloneDate(a)),r=(!i.hasOwnProperty("isStayInNotAllowed")||!i.isStayInNotAllowed)&&(null===o||o.getTime()>a.getTime())&&MPHB.calendarHelper.ROOM_STATUS_BOOKED!==i.roomTypeStatus&&MPHB.calendarHelper.ROOM_STATUS_NOT_AVAILABLE!==i.roomTypeStatus,a.setDate(a.getDate()+1),n=W.datepick.formatDate("yyyy-mm-dd",a)}while(r);return null!==l&&l.setHours(0,0,0,1),null!==c&&c.setHours(23,59,59,999),{minStayDateAfterCheckIn:s,maxStayDateAfterCheckIn:o,minCheckOutDateForSelection:l,maxCheckOutDateForSelection:c}}},can.Control("MPHB.Datepicker",{},{$datepickerInputElement:null,form:null,hiddenElement:null,roomTypeId:null,firstAvailableCheckInDate:null,init:function(e,t){this.$datepickerInputElement=e,this.form=t.form,this.roomTypeId=t.roomTypeId,this.firstAvailableCheckInDate=new Date(t.firstAvailableCheckInDateYmd);var a=this.element.attr("id")+"-hidden";if(this.hiddenElement=W("#"+a),this.hiddenElement.val()){var n=W.datepick.parseDate(MPHB._data.settings.dateTransferFormat,this.hiddenElement.val()),i=W.datepick.formatDate(MPHB._data.settings.dateFormat,n);this.element.val(i)}this.initDatepick()},initDatepick:function(){var e={dateFormat:MPHB._data.settings.dateFormat,altFormat:MPHB._data.settings.dateTransferFormat,altField:this.hiddenElement,minDate:W.datepick.parseDate(MPHB._data.settings.dateTransferFormat,MPHB._data.today),monthsToShow:MPHB._data.settings.numberOfMonthDatepicker,firstDay:MPHB._data.settings.firstDay,pickerClass:MPHB._data.settings.datepickerClass,useMouseWheel:!1,showSpeed:0},t=W.extend(e,this.getDatepickSettings());this.element.datepick(t)},getDatepickSettings:function(){return{}},getDate:function(){var e=this.element.val(),t=null;try{t=W.datepick.parseDate(MPHB._data.settings.dateFormat,e)}catch(e){t=null}return t},getFormattedDate:function(e){void 0===e&&(e=MPHB._data.settings.dateFormat);var t=this.getDate();return t?W.datepick.formatDate(e,t):""},setDate:function(e){this.element.datepick("setDate",e)},getOption:function(e){return this.element.datepick("option",e)},setOption:function(e,t){this.element.datepick("option",e,t)},getMinDate:function(){var e=this.getOption("minDate");return null!==e&&""!==e?MPHB.Utils.cloneDate(e):null},getMaxDate:function(){var e=this.getOption("maxDate");return null!==e&&""!==e?MPHB.Utils.cloneDate(e):null},getMaxAdvanceDate:function(){var e=this.getOption("maxAdvanceDate");return e?MPHB.Utils.cloneDate(e):null},clear:function(){this.element.datepick("clear")},formatDate:function(e,t){return t=void 0!==t?t:"yyyy-mm-dd",W.datepick.formatDate(t,e)},lock:function(){W(".datepick-popup").addClass("mphb-loading")},unlock:function(){W(".datepick-popup").removeClass("mphb-loading")},refresh:function(e){var t=0<arguments.length&&void 0!==e&&e;W.datepick._update(this.element[0],!0),t&&W.datepick._updateInput(this.element[0],!1)}}),MPHB.FlexsliderGallery=can.Control.extend({},{sliderEl:null,navSliderEl:null,groupId:null,init:function(e){this.sliderEl=e,this.groupId=e.data("group");var t=W('.mphb-gallery-thumbnail-slider[data-group="'+this.groupId+'"]');t.length&&(this.navSliderEl=t);var a=this;W(window).on("load",function(){a.initSliders()}),"complete"==document.readyState&&this.initSliders()},initSliders:function(){if(!this.slidersLoaded){var e=this.sliderEl.data("flexslider-atts");if(this.navSliderEl){var t=this.navSliderEl.data("flexslider-atts");t.asNavFor='.mphb-flexslider-gallery-wrapper[data-group="'+this.groupId+'"]',t.itemWidth=this.navSliderEl.find("ul > li img").width(),e.sync='.mphb-gallery-thumbnail-slider[data-group="'+this.groupId+'"]',this.navSliderEl.addClass("flexslider mphb-flexslider mphb-gallery-thumbnails-slider").flexslider(t)}this.sliderEl.addClass("flexslider mphb-flexslider mphb-gallery-slider").flexslider(e),this.slidersLoaded=!0}}}),MPHB.format_price=function(e,t){t=t||{};var a=MPHB._data.settings.currency;t=W.extend({trim_zeros:!1},a,t),e=MPHB.number_format(e,t.decimals,t.decimal_separator,t.thousand_separator);var n=t.price_format.replace("%s",e);if(t.trim_zeros){var i=new RegExp("\\"+t.decimal_separator+"0+$|(\\"+t.decimal_separator+"\\d*[1-9])0+$");n=n.replace(i,"$1")}return'<span class="mphb-price">'+n+"</span>"},MPHB.number_format=function(e,t,a,n){var i,r,s="";return t=t||0,a=a||".",n=n||",",e<0&&(s="-",e*=-1),3<(r=(i=parseInt(e=(+e||0).toFixed(t))+"").length)?r%=3:r=0,s+(r?i.substr(0,r)+n:"")+i.substr(r).replace(/(\d{3})(?=\d)/g,"$1"+n)+(t?a+Math.abs(e-i).toFixed(t).replace(/-/,0).slice(2):"")},MPHB.post=function(e,t,a){e="mphb_"+e,t=W.extend({action:e,mphb_nonce:MPHB._data.nonces[e],lang:MPHB._data.settings.currentLanguage},t);var n=W.extend({url:MPHB._data.ajaxUrl,type:"POST",dataType:"json",data:t},a);return W.ajax(n)},MPHB.get_today_date=function(){return W.datepick.parseDate(MPHB._data.settings.dateTransferFormat,MPHB._data.today)},MPHB.TermsSwitcher=can.Construct.extend({},{init:function(e){var t=e.children(".mphb-terms-and-conditions");0<t.length&&e.find(".mphb-terms-and-conditions-link").on("click",function(e){e.preventDefault(),t.toggleClass("mphb-active")})}}),MPHB.Utils=can.Construct.extend({formatDateToCompare:function(e){return W.datepick.formatDate("yyyymmdd",e)},compareDates:function(e,t,a){e=MPHB.Utils.formatDateToCompare(e),t=MPHB.Utils.formatDateToCompare(t);if(null==a)return t<e?1:e<t?-1:0;switch(a){case">":return t<e;case">=":return t<=e;case"<":return e<t;case"<=":return e<=t;case"=":case"==":return e==t;case"!=":return e!=t;default:return!1}},cloneDate:function(e){return new Date(e.getTime())},arrayUnique:function(e){return e.filter(function(e,t,a){return a.indexOf(e)===t})},arrayMin:function(e){return Math.min.apply(null,e)},arrayMax:function(e){return Math.max.apply(null,e)},arrayDiff:function(e,t){return e.filter(function(e){return t.indexOf(e)<0})},inArray:function(e,t){return-1!==t.indexOf(e)}},{}),MPHB.Gateway=can.Construct.extend({},{amount:0,paymentDescription:"",init:function(e){this.billingSection=e.billingSection,this.initSettings(e.settings)},initSettings:function(e){this.amount=e.amount,this.paymentDescription=e.paymentDescription},canSubmit:function(){return Promise.resolve(!0)},updateData:function(e){this.amount=e.amount,this.paymentDescription=e.paymentDescription},afterSelection:function(){},cancelSelection:function(){},onInput:function(){}}),MPHB.BeanstreamGateway=MPHB.Gateway.extend({},{scriptUrl:"",isCanSubmit:!1,loadHandler:null,validityHandler:null,tokenRequestHandler:null,tokenUpdatedHandler:null,initSettings:function(e){this._super(e),this.scriptUrl=e.scriptUrl||"https://payform.beanstream.com/v1.1.0/payfields/beanstream_payfields.js",this.validityHandler=this.validityChanged.bind(this),this.tokenRequestHandler=this.tokenRequested.bind(this),this.tokenUpdatedHandler=this.tokenUpdated.bind(this)},canSubmit:function(){return Promise.resolve(this.isCanSubmit)},afterSelection:function(t){if(this._super(t),0<t.length){var e=document.createElement("script");e.id="payfields-script",e.src=this.scriptUrl,e.dataset.submitform="true",e.dataset.async="true",null!=this.loadHandler&&W(document).off("beanstream_payfields_loaded",this.loadHandler),this.loadHandler=function(e){W("[data-beanstream-id]").appendTo(t)},W(document).on("beanstream_payfields_loaded",this.loadHandler),t.append(e),t.removeClass("mphb-billing-fields-hidden")}W(document).on("beanstream_payfields_inputValidityChanged",this.validityHandler).on("beanstream_payfields_tokenRequested",this.tokenRequestHandler).on("beanstream_payfields_tokenUpdated",this.tokenUpdatedHandler)},cancelSelection:function(){W(document).off("beanstream_payfields_inputValidityChanged",this.validityHandler).off("beanstream_payfields_tokenRequested",this.tokenRequestHandler).off("beanstream_payfields_tokenUpdated",this.tokenUpdatedHandler)},validityChanged:function(e){(e.eventDetail||e.originalEvent.eventDetail).isValid||(this.isCanSubmit=!1)},tokenRequested:function(){this.billingSection.showPreloader()},tokenUpdated:function(e){var t=e.eventDetail||e.originalEvent.eventDetail;t.success?this.isCanSubmit=!0:(this.isCanSubmit=!1,this.billingSection.showError(MPHB._data.translations.tokenizationFailure.replace("(%s)",t.message))),this.billingSection.hidePreloader()}}),MPHB.BillingSection=can.Control.extend({},{updateBillingFieldsTimeout:null,parentForm:null,billingFieldsWrapperEl:null,gateways:{},amounts:{},lastGatewayId:null,init:function(e,t){this.parentForm=t.form,this.billingFieldsWrapperEl=this.element.find(".mphb-billing-fields"),this.initGateways(t.gateways)},initGateways:function(e){var i=this;W.each(e,function(e,t){var a={billingSection:i,settings:t},n=null;try{switch(e){case"braintree":n=new MPHB.BraintreeGateway(a);break;case"beanstream":n=new MPHB.BeanstreamGateway(a);break;case"stripe":n=new MPHB.StripeGateway(a);break;default:n=new MPHB.Gateway(a)}}catch(e){console.error(e)}null!=n&&(i.gateways[e]=n,i.amounts[e]=t.amount)}),this.notifySelectedGateway()},getBookingDetails:function(){return this.parentForm.parseFormToJSON()},getRoomDetails:function(){return this.getBookingDetails().mphb_room_details||{}},getRoomTypeIds:function(){var e=this.getRoomDetails(),t=[];for(var a in e){var n=parseInt(e[a].room_type_id);isNaN(n)||-1!=t.indexOf(n)||t.push(n)}return t},updateBillingInfo:function(e){var t=this,a=e.val();this.showPreloader(),this.billingFieldsWrapperEl.empty().addClass("mphb-billing-fields-hidden"),clearTimeout(this.updateBillingFieldsTimeout),this.updateBillingFieldsTimeout=setTimeout(function(){var e=t.getBookingDetails();W.ajax({url:MPHB._data.ajaxUrl,type:"GET",dataType:"json",data:{action:"mphb_get_billing_fields",mphb_nonce:MPHB._data.nonces.mphb_get_billing_fields,mphb_gateway_id:a,formValues:e,lang:MPHB._data.settings.currentLanguage},success:function(e){e.hasOwnProperty("success")?e.success?(t.lastGatewayId&&t.gateways[t.lastGatewayId].cancelSelection(),t.billingFieldsWrapperEl.html(e.data.fields),e.data.hasVisibleFields?t.billingFieldsWrapperEl.removeClass("mphb-billing-fields-hidden"):t.billingFieldsWrapperEl.addClass("mphb-billing-fields-hidden"),t.notifySelectedGateway(a)):t.showError(e.data.message):t.showError(MPHB._data.translations.errorHasOccured)},error:function(){t.showError(MPHB._data.translations.errorHasOccured)},complete:function(){t.hidePreloader()}})},500)},'[name="mphb_gateway_id"] change':function(e,t){this.updateBillingInfo(e,t)},hideErrors:function(){this.parentForm.hideErrors()},showError:function(e){this.parentForm.showError(e)},showPreloader:function(){this.parentForm.showPreloader()},hidePreloader:function(){this.parentForm.hidePreloader()},onInput:function(e,t){var a=this.gateways[this.getSelectedGateway()];a&&a.onInput(e,t)},canSubmit:function(e,t){var a=this.gateways[this.getSelectedGateway()];return a?a.canSubmit(e,t):Promise.resolve(!0)},getSelectedGateway:function(){var e=this.getSelectedGatewayEl();return e&&0<e.length?e.val():""},getSelectedGatewayEl:function(){var e=this.element.find('[name="mphb_gateway_id"]');return 1==e.length?e:e.filter(":checked")},getSelectedGatewayAmount:function(){var e=this.getSelectedGateway();return this.amounts.hasOwnProperty(e)?this.amounts[e]:0},notifySelectedGateway:function(e){if((e=e||this.getSelectedGateway())&&this.gateways.hasOwnProperty(e)){this.gateways[e].afterSelection(this.billingFieldsWrapperEl);var t=this.parentForm.getCountry();!1!==t&&this.gateways[e].onInput("country",t)}this.lastGatewayId=e},updateGatewaysData:function(e){var a=this;W.each(e,function(e,t){a.gateways.hasOwnProperty(e)&&a.gateways[e].updateData(t)})}}),MPHB.BraintreeGateway=MPHB.Gateway.extend({},{clientToken:"",checkout:null,initSettings:function(e){this._super(e),this.clientToken=e.clientToken},canSubmit:function(){return Promise.resolve(this.isNonceStored())},storeNonce:function(e){this.billingSection.billingFieldsWrapperEl.find('[name="mphb_braintree_payment_nonce"]').val(e)},isNonceStored:function(){var e=this.billingSection.billingFieldsWrapperEl.find('[name="mphb_braintree_payment_nonce"]');return e.length&&""!=e.val()},afterSelection:function(e){if(this._super(e),null!=braintree){var t="mphb-braintree-container-"+this.clientToken.substr(0,8);e.append('<div id="'+t+'"></div>');var a=this;braintree.setup(this.clientToken,"dropin",{container:t,onReady:function(e){a.checkout=e},onPaymentMethodReceived:function(e){a.storeNonce(e.nonce),a.billingSection.parentForm.element.submit(),a.billingSection.showPreloader()}}),e.removeClass("mphb-billing-fields-hidden")}},cancelSelection:function(){if(this._super(),null!=this.checkout){var e=this;this.checkout.teardown(function(){e.checkout=null})}}}),MPHB.CouponSection=can.Control.extend({},{applyCouponTimeout:null,parentForm:null,appliedCouponEl:null,couponEl:null,messageHolderEl:null,init:function(e,t){this.parentForm=t.form,this.couponEl=e.find('[name="mphb_coupon_code"]'),this.appliedCouponEl=e.find('[name="mphb_applied_coupon_code"]'),this.messageHolderEl=e.find(".mphb-coupon-message")},".mphb-apply-coupon-code-button click":function(e,t){t.preventDefault(),t.stopPropagation(),this.clearMessage();var a=this.couponEl.val();if(a.length){this.appliedCouponEl.val("");var n=this;this.showPreloader(),clearTimeout(this.applyCouponTimeout),this.applyCouponTimeout=setTimeout(function(){var e=n.parentForm.parseFormToJSON();W.ajax({url:MPHB._data.ajaxUrl,type:"POST",dataType:"json",data:{action:"mphb_apply_coupon",mphb_nonce:MPHB._data.nonces.mphb_apply_coupon,mphb_coupon_code:a,formValues:e,lang:MPHB._data.settings.currentLanguage},success:function(e){e.hasOwnProperty("success")?e.success?(n.parentForm.setCheckoutData(e.data),n.couponEl.val(""),n.appliedCouponEl.val(e.data.coupon.applied_code),n.showMessage(e.data.coupon.message)):n.showMessage(e.data.message):n.showMessage(MPHB._data.translations.errorHasOccured)},error:function(){n.showMessage(MPHB._data.translations.errorHasOccured)},complete:function(){n.hidePreloader()}})},500)}else this.showMessage(MPHB._data.translations.emptyCouponCode)},removeCoupon:function(){this.appliedCouponEl.val(""),this.clearMessage()},showPreloader:function(){this.parentForm.showPreloader()},hidePreloader:function(){this.parentForm.hidePreloader()},clearMessage:function(){this.messageHolderEl.html("").addClass("mphb-hide")},showMessage:function(e){this.messageHolderEl.html(e).removeClass("mphb-hide")}}),MPHB.CheckoutForm=can.Control.extend({myThis:null},{priceBreakdownTableEl:null,bookBtnEl:null,errorsWrapperEl:null,preloaderEl:null,billingSection:null,couponSection:null,waitResponse:!1,updateInfoTimeout:null,updateRatesTimeout:null,freeBooking:!1,currentInfoAjax:null,toPay:0,init:function(){(MPHB.CheckoutForm.myThis=this).bookBtnEl=this.element.find("input[type=submit]"),this.errorsWrapperEl=this.element.find(".mphb-errors-wrapper"),this.preloaderEl=this.element.find(".mphb-preloader"),this.priceBreakdownTableEl=this.element.find("table.mphb-price-breakdown"),MPHB._data.settings.useBilling&&(this.billingSection=new MPHB.BillingSection(this.element.find("#mphb-billing-details"),{form:this,gateways:MPHB._data.gateways})),MPHB._data.settings.useCoupons&&(this.couponSection=new MPHB.CouponSection(this.element.find("#mphb-coupon-details"),{form:this})),this.element.find(".mphb-room-details").each(function(e,t){new MPHB.GuestsChooser(W(t),{minAdults:MPHB._data.checkout.min_adults,minChildren:MPHB._data.checkout.min_children})});var e=this;W(".mphb-room-details").each(function(){e.updateRatePrices(W(this))}),this.updateCheckoutInfo()},setTotal:function(e,t){this.toPay=e,this.element.find(".mphb-total-price-field").html(t)},setDeposit:function(e,t){this.toPay=e,this.element.find(".mphb-deposit-amount-field").html(t)},setupPriceBreakdown:function(e){this.priceBreakdownTableEl.replaceWith(e),this.priceBreakdownTableEl=this.element.find("table.mphb-price-breakdown")},updateCheckoutInfo:function(){var t=this;t.hideErrors(),t.showPreloader(),clearTimeout(this.updateInfoTimeout),this.updateInfoTimeout=setTimeout(function(){var e=t.parseFormToJSON();t.currentInfoAjax=W.ajax({url:MPHB._data.ajaxUrl,type:"GET",dataType:"json",data:{action:"mphb_update_checkout_info",mphb_nonce:MPHB._data.nonces.mphb_update_checkout_info,formValues:e,lang:MPHB._data.settings.currentLanguage},beforeSend:function(){null!=t.currentInfoAjax&&(t.currentInfoAjax.abort(),t.hideErrors())},success:function(e){e.hasOwnProperty("success")?e.success?e.data&&t.setCheckoutData(e.data):t.showError(e.data.message):t.showError(MPHB._data.translations.errorHasOccured)},error:function(){t.showError(MPHB._data.translations.errorHasOccured)},complete:function(){t.hidePreloader(),t.currentInfoAjax=null}})},500)},setCheckoutData:function(e){this.setTotal(e.newAmount,e.priceHtml),this.setupPriceBreakdown(e.priceBreakdown),MPHB._data.settings.useBilling&&(this.setDeposit(e.depositAmount,e.depositPrice),this.billingSection.updateGatewaysData(e.gateways),e.isFree?this.setFreeMode():this.unsetFreeMode()),this.element[0].dispatchEvent(new Event("CheckoutDataChanged"))},setFreeMode:function(){this.freeBooking=!0,this.billingSection.element.addClass("mphb-hide"),this.element.append(W("<input />",{type:"hidden",name:"mphb_gateway_id",value:"manual",id:"mphb-manual-payment-input"}))},unsetFreeMode:function(){this.freeBooking=!1,this.billingSection.element.removeClass("mphb-hide"),this.element.find("#mphb-manual-payment-input").remove()},updateRatePrices:function(e){if(e&&e.length){var t=parseInt(e.attr("data-index")),a=e.find(".mphb_sc_checkout-rate"),n=W.map(a,function(e){return parseInt(e.value)});if(!(n.length<=1)){var i=this.parseFormToJSON(),r=i.mphb_room_details[t],s=r.adults||"",o=r.children||"";clearTimeout(this.updateRatesTimeout),this.updateRatesTimeout=setTimeout(function(){W.ajax({url:MPHB._data.ajaxUrl,type:"GET",dataType:"json",data:{action:"mphb_update_rate_prices",mphb_nonce:MPHB._data.nonces.mphb_update_rate_prices,rates:n,adults:s,children:o,check_in_date:i.mphb_check_in_date,check_out_date:i.mphb_check_out_date,lang:MPHB._data.settings.currentLanguage},success:function(e){if(e.hasOwnProperty("success")){var i=e.data;W.each(a,function(e,t){var a=t.value;if(null!=i[a]){var n=W(t).parent().children("strong");n.children(".mphb-price").remove(),n.append(i[a])}})}}})},500)}}},".mphb_checkout-guests-chooser change":function(e){this.updateRatePrices(e.closest(".mphb-room-details")),this.updateCheckoutInfo()},".mphb_checkout-rate change":function(){this.updateCheckoutInfo()},".mphb_checkout-service, .mphb_checkout-service-adults change":function(){this.updateCheckoutInfo()},".mphb_checkout-service-quantity input":function(){this.updateCheckoutInfo()},'select[name="mphb_country"] change':function(e){if(null!=this.billingSection){var t=W(e).val();this.billingSection.onInput("country",t)}},getCountry:function(){return this.getCustomerDetail("country")},".mphb-price-breakdown-expand click":function(e,t){t.preventDefault(),W(e).blur();var a=W(e).parents("tr.mphb-price-breakdown-group");a.find(".mphb-price-breakdown-rate").toggleClass("mphb-hide"),a.nextUntil("tr.mphb-price-breakdown-group").toggleClass("mphb-hide"),W(e).children(".mphb-inner-icon").toggleClass("mphb-hide")},hideErrors:function(){this.errorsWrapperEl.empty().addClass("mphb-hide")},showError:function(e){this.errorsWrapperEl.html(e).removeClass("mphb-hide")},showPreloader:function(){this.waitResponse=!0,this.bookBtnEl.attr("disabled","disabled"),this.preloaderEl.removeClass("mphb-hide")},hidePreloader:function(){this.waitResponse=!1,this.bookBtnEl.removeAttr("disabled"),this.preloaderEl.addClass("mphb-hide")},parseFormToJSON:function(){return!!(this.element&&0<this.element.length)&&this.element.serializeJSON()},getCustomerDetail:function(e){var t=this.element.find("#mphb_"+e);return 0<t.length&&t.val()},getCustomerDetails:function(){var a={email:"",first_name:"",last_name:""},n=this;if(["name","first_name","last_name","email","phone","country","address1","city","state","zip"].forEach(function(e){var t=n.getCustomerDetail(e);!1!==t&&(a[e]=t)}),!a.name){var e=a.first_name+" "+a.last_name;a.name=e.trim()}return a},getToPayAmount:function(){var e=this.toPay;return 0==e&&(e=this.billingSection.getSelectedGatewayAmount()),e},submit:function(){if(this.waitResponse)return!1;if(MPHB._data.settings.useBilling&&!this.freeBooking){var e=this.getToPayAmount(),t=this.getCustomerDetails(),a=this;return this.showPreloader(),this.billingSection.canSubmit(e,t).then(function(e){e?a.element[0].submit():a.hidePreloader()}).catch(function(e){a.hidePreloader(),console.error("Billing error. "+e.message)}),!1}},"#mphb-price-details .mphb-remove-coupon click":function(e,t){t.preventDefault(),t.stopPropagation(),MPHB._data.settings.useCoupons&&(this.couponSection.removeCoupon(),this.updateCheckoutInfo())}}),MPHB.GuestsChooser=can.Control.extend({},{$adultsChooser:null,$childrenChooser:null,minAdults:0,minChildren:0,maxAdults:0,maxChildren:0,totalCapacity:0,init:function(e,t){var a=e.find(".mphb_checkout-guests-chooser");a.length<2||(this.$adultsChooser=W(a[0]),this.$childrenChooser=W(a[1]),this.minAdults=t.minAdults,this.minChildren=t.minChildren,this.maxAdults=parseInt(this.$adultsChooser.data("max-allowed")),this.maxChildren=parseInt(this.$childrenChooser.data("max-allowed")),this.totalCapacity=parseInt(a.data("max-total")),this.maxAdults+this.maxChildren>this.totalCapacity&&this.$adultsChooser.on("change",this.limitChildren.bind(this)))},limitChildren:function(){var e=this.$adultsChooser.val(),t=this.findMax(e,this.minChildren,this.maxChildren);this.limitOptions(this.$childrenChooser,this.minChildren,t,e)},findMax:function(e,t,a){var n=this.totalCapacity;return""!==e&&(n=this.totalCapacity-e,n=Math.max(t,n)),Math.min(n,a)},limitOptions:function(e,t,n,a){var i=t;e.children().each(function(e,t){var a=t.value;""!==a&&(a=parseInt(a),n<a?W(t).remove():i<a&&(i=a))});for(var r=i+1;r<=n;r++){var s=jQuery('<option value="'+r+'">'+r+"</option>");e.append(s)}""!==a&&e.children(":selected").prop("selected",!1)}}),(a=jQuery)("#mphb-render-checkout-login").click(function(e){e.preventDefault(),e.stopPropagation();var t=a(this).parents(".mphb-login-form-wrap").find(".mphb-login-form");t.hasClass("mphb-hide")?t.removeClass("mphb-hide"):t.addClass("mphb-hide")}),MPHB.StripeGateway=MPHB.Gateway.extend({},{publicKey:"",locale:"auto",currency:"EUR",successUrl:window.location.href,defaultCountry:"",paymentDescription:"Accommodation(s) reservation",statementDescriptor:"Hotel Booking",fullAddressRequired:!1,i18n:{},style:{},api:null,elements:null,cardControl:null,sepaDebitControl:null,payments:null,customer:null,defaultCustomer:null,mountWrapper:null,errorsWrapper:null,hasErrors:!1,undefinedError:MPHB._data.translations.errorHasOccured,init:function(e){this._super(e),this.api=Stripe(this.publicKey),this.elements=this.api.elements({locale:this.locale}),this.cardControl=this.elements.create("card",{style:this.style,hidePostalCode:this.fullAddressRequired}),this.sepaDebitControl=this.elements.create("iban",{style:this.style,supportedCountries:["SEPA"]}),this.payments=new MPHB.StripeGateway.PaymentMethods(e.settings.paymentMethods,this.defaultCountry,e.settings.currency),this.addListeners()},initSettings:function(e){this._super(e),this.publicKey=e.publicKey,this.locale=e.locale,this.currency=e.currency,this.successUrl=e.successUrl,this.defaultCountry=e.defaultCountry,this.paymentDescription=e.paymentDescription,this.statementDescriptor=e.statementDescriptor,this.fullAddressRequired=MPHB._data.settings.fullAddressRequired,this.defaultCustomer=e.customer,this.i18n=e.i18n,this.style=e.style,this.idempotencyKey=W(".mphb_sc_checkout-form").find('input[name="'+e.idempotencyKeyFieldName+'"]').val()},addListeners:function(){var e=this.onChange.bind(this);this.cardControl.on("change",e),this.sepaDebitControl.on("change",e)},onChange:function(e){e.error?(this.showError(e.error.message),this.hasErrors=!0):(this.hideErrors(),this.hasErrors=!1)},onInput:function(e,t){"country"===e&&this.payments.selectCountry(t)},afterSelection:function(e){this._super(e),e.append(this.mountHtml()),this.mountWrapper=e,this.errorsWrapper=e.find("#mphb-stripe-errors"),this.cardControl.mount("#mphb-stripe-card-element"),this.payments.isEnabled("sepa_debit")&&this.sepaDebitControl.mount("#mphb-stripe-iban-element"),this.payments.mount(e);var t=this;this.payments.inputs.on("change",function(){switch(t.payments.currentPayment){case"card":t.cardControl.clear();break;case"sepa_debit":t.sepaDebitControl.clear()}t.payments.selectPayment(this.value)}),e.removeClass("mphb-billing-fields-hidden")},cancelSelection:function(){this._super(),this.mountWrapper=null,this.errorsWrapper=null,this.cardControl.unmount(),this.payments.isEnabled("sepa_debit")&&this.sepaDebitControl.unmount(),this.payments.unmount()},canSubmit:function(e,t){return this.hasErrors?Promise.resolve(!1):(this.setCustomer(t),this.createPaymentMethod().then(this.createPaymentIntent.bind(this,e)).then(this.confirmPayment.bind(this)).then(this.handleStripeErrors.bind(this)).then(this.completePayment.bind(this)))},setCustomer:function(e){var t=W.extend({},e);t.email||(t.email=this.defaultCustomer.email),t.name||(t.name=this.defaultCustomer.name,t.first_name=this.defaultCustomer.first_name,t.last_name=this.defaultCustomer.last_name),t.hasOwnProperty("country")||(t.country=this.payments.currentCountry),this.customer=t},createPaymentMethod:function(){return"card"===this.payments.currentPayment?this.api.createPaymentMethod({type:"card",card:this.cardControl,billing_details:{name:this.customer.name,email:this.customer.email}}):"bancontact"===this.payments.currentPayment?this.api.createPaymentMethod({type:"bancontact",billing_details:{name:this.customer.name,email:this.customer.email}}):"ideal"===this.payments.currentPayment?this.api.createPaymentMethod({type:"ideal",ideal:{bank:null,bic:null},billing_details:{name:this.customer.name,email:this.customer.email}}):"giropay"===this.payments.currentPayment?this.api.createPaymentMethod({type:"giropay",billing_details:{name:this.customer.name,email:this.customer.email}}):"sepa_debit"===this.payments.currentPayment?this.api.createPaymentMethod({type:"sepa_debit",sepa_debit:this.sepaDebitControl,billing_details:{name:this.customer.name,email:this.customer.email}}):"klarna"===this.payments.currentPayment?this.api.createPaymentMethod({type:"klarna",billing_details:{address:{country:this.customer.country},name:this.customer.name,email:this.customer.email}}):void 0},createPaymentIntent:function(e,n){var i=this;return new Promise(function(t,a){MPHB.post("create_stripe_payment_intent",{amount:e,description:i.paymentDescription,paymentMethodType:n.paymentMethod.type,paymentMethodId:n.paymentMethod.id,idempotencyKey:i.idempotencyKey,roomTypeIds:i.billingSection.getRoomTypeIds()},{success:function(e){e.hasOwnProperty("success")&&e.success?t({id:e.data.id,clientSecret:e.data.client_secret,paymentMethodId:n.paymentMethod.id}):(i.showError(i.undefinedError),a(new Error(i.undefinedError)))},error:function(){void 0!==response.responseJSON.data.errorMessage?(i.showError(response.responseJSON.data.errorMessage),a(new Error(response.responseJSON.data.errorMessage))):(i.showError(i.undefinedError),a(new Error(i.undefinedError)))}})})},confirmPayment:function(e){return"card"===this.payments.currentPayment?this.api.confirmCardPayment(e.clientSecret,{payment_method:e.paymentMethodId}):"bancontact"===this.payments.currentPayment?this.api.confirmBancontactPayment(e.clientSecret,{payment_method:e.paymentMethodId,return_url:this.successUrl},{handleActions:!1}):"ideal"===this.payments.currentPayment?this.api.confirmIdealPayment(e.clientSecret,{payment_method:e.paymentMethodId,return_url:this.successUrl},{handleActions:!1}):"giropay"===this.payments.currentPayment?this.api.confirmGiropayPayment(e.clientSecret,{payment_method:e.paymentMethodId,return_url:this.successUrl},{handleActions:!1}):"sepa_debit"===this.payments.currentPayment?this.api.confirmSepaDebitPayment(e.clientSecret,{payment_method:e.paymentMethodId}):"klarna"===this.payments.currentPayment?this.api.confirmKlarnaPayment(e.clientSecret,{payment_method:e.paymentMethodId,return_url:this.successUrl},{handleActions:!1}):void 0},handleStripeErrors:function(e){if(e.error)throw this.showError(e.error.message),new Error(e.error.message);if(null!=e.paymentIntent)return e.paymentIntent},completePayment:function(e){return this.saveToCheckout("payment_method",this.payments.currentPayment),this.saveToCheckout("payment_intent_id",e.id),"requires_action"==e.status&&"redirect_to_url"==e.next_action.type&&this.saveToCheckout("redirect_url",e.next_action.redirect_to_url.url),!0},saveToCheckout:function(e,t){this.mountWrapper.find("#mphb_stripe_"+e).val(t)},mountHtml:function(){var e='<section id="mphb-stripe-payment-container" class="mphb-stripe-payment-container">';return e+=this.methodsHtml(),e+=this.fieldsHtml("card"),e+=this.fieldsHtml("bancontact"),e+=this.fieldsHtml("ideal"),e+=this.fieldsHtml("giropay"),e+=this.fieldsHtml("sepa_debit"),e+=this.fieldsHtml("klarna"),e+='<div id="mphb-stripe-errors"></div>',e+="</section>"},methodsHtml:function(){if(this.payments.onlyCardEnabled())return"";var i=this.i18n,r='<nav id="mphb-stripe-payment-methods">';return r+="<ul>",this.payments.forEach(function(e,t,a){if(t.isEnabled){var n=a.isSelected(e);r+='<li class="mphb-stripe-payment-method '+e+(n?" active":"")+'">',r+="<label>",r+='<input type="radio" name="stripe_payment_method" value="'+e+'"'+(n?' checked="checked"':"")+" />"+i[e],r+="</label>",r+="</li>"}}),r+="</ul>",r+="</nav>"},fieldsHtml:function(e){if(!this.payments.isEnabled(e))return"";var t="";switch(t+='<div class="mphb-stripe-payment-fields '+e+(this.payments.isSelected(e)?"":" mphb-hide")+'">',t+="<fieldset>",e){case"card":t+=this.cardHtml();break;case"sepa_debit":t+=this.ibanHtml();break;default:t+=this.redirectHtml()}return t+="</fieldset>","sepa_debit"==e&&(t+='<p class="notice">'+this.i18n.iban_policy+"</p>"),t+="</div>"},cardHtml:function(){var e="";return this.payments.onlyCardEnabled()&&(e+='<label for="mphb-stripe-card-element">'+this.i18n.card_description+"</label>"),e+='<div id="mphb-stripe-card-element" class="mphb-stripe-element"></div>'},ibanHtml:function(){return'<label for="mphb-stripe-iban-element">'+this.i18n.iban+'</label><div id="mphb-stripe-iban-element" class="mphb-stripe-element"></div>'},redirectHtml:function(){return'<p class="notice">'+this.i18n.redirect_notice+"</p>"},showError:function(e){this.errorsWrapper.html(e).removeClass("mphb-hide")},hideErrors:function(){this.errorsWrapper.addClass("mphb-hide").text("")}}),MPHB.DirectBooking=can.Control.extend({},{reservationForm:null,elementsToHide:null,quantitySection:null,wrapperWithSelect:null,wrapperWithoutSelect:null,priceWrapper:null,quantitySelect:null,availableLabel:null,typeId:0,init:function(e,t){this.reservationForm=t.reservationForm,this.elementsToHide=e.find(".mphb-reserve-room-section, .mphb-rooms-quantity-wrapper, .mphb-regular-price"),this.quantitySection=e.find(".mphb-reserve-room-section"),this.wrapperWithSelect=this.quantitySection.find(".mphb-rooms-quantity-wrapper.mphb-rooms-quantity-multiple"),this.wrapperWithoutSelect=this.quantitySection.find(".mphb-rooms-quantity-wrapper.mphb-rooms-quantity-single"),this.priceWrapper=this.quantitySection.find(".mphb-period-price"),this.quantitySelect=this.quantitySection.find(".mphb-rooms-quantity"),this.availableLabel=this.quantitySection.find(".mphb-available-rooms-count"),this.typeId=e.find('input[name="mphb_room_type_id"]').val(),this.typeId=parseInt(this.typeId)},hideSections:function(){this.elementsToHide.addClass("mphb-hide"),this.reservationForm.reserveBtnWrapper.removeClass("mphb-hide")},showSections:function(e){this.reservationForm.reserveBtnWrapper.addClass("mphb-hide"),this.quantitySection.removeClass("mphb-hide"),e&&this.priceWrapper.removeClass("mphb-hide")},resetQuantityOptions:function(e){this.quantitySelect.empty();for(var t=1;t<=e;t++){var a='<option value="'+t+'">'+t+"</option>";this.quantitySelect.append(a)}this.quantitySelect.val(1),this.availableLabel.text(e),1<e?this.wrapperWithSelect.removeClass("mphb-hide"):this.wrapperWithoutSelect.removeClass("mphb-hide")},setupPrice:function(e,t){this.priceWrapper.children(".mphb-price, .mphb-price-period, .mphb-tax-information").remove(),0<e&&""!=t&&this.priceWrapper.append(t)},showError:function(e){this.hideSections(),this.reservationForm.showError(e)},loadAvailabilityAndPriceData:function(){var e=this.reservationForm.checkInDatepicker.getDate(),t=this.reservationForm.checkOutDatepicker.getDate();if(e&&t){this.reservationForm.clearErrors(),this.reservationForm.lock();var a=this;W.ajax({url:MPHB._data.ajaxUrl,type:"GET",dataType:"json",data:{action:"mphb_get_room_type_availability_data",mphb_nonce:MPHB._data.nonces.mphb_get_room_type_availability_data,room_type_id:this.typeId,check_in_date:W.datepick.formatDate(MPHB._data.settings.dateTransferFormat,e),check_out_date:W.datepick.formatDate(MPHB._data.settings.dateTransferFormat,t),adults_count:this.reservationForm.getAdults(),children_count:this.reservationForm.getChildren(),lang:MPHB._data.settings.currentLanguage},success:function(e){e.success?(a.resetQuantityOptions(e.data.freeCount),a.setupPrice(e.data.price,e.data.priceHtml),a.showSections(0<e.data.price)):a.showError(e.data.message)},error:function(){a.showError(MPHB._data.translations.errorHasOccured)},complete:function(){a.reservationForm.unlock()}})}},"input.mphb-datepick change":function(){this.hideSections()},".mphb-reserve-btn click":function(e,t){t.preventDefault(),t.stopPropagation();var a=this.reservationForm.checkInDatepicker.getDate(),n=this.reservationForm.checkOutDatepicker.getDate();a&&n?this.loadAvailabilityAndPriceData():(a?this.showError(MPHB._data.translations.checkOutNotValid):this.showError(MPHB._data.translations.checkInNotValid),this.reservationForm.unlock())},'input.mphb-datepick, select[name="mphb_children"] change':function(){this.loadAvailabilityAndPriceData()},'select[name="mphb_adults"] change':function(e){var t=jQuery('select[name="mphb_children"]');if(t.length&&void 0!==t.data("max-total")){var a=parseInt(t.data("min-allowed")),n=parseInt(t.data("max-allowed")),i=parseInt(t.data("max-total")),r=parseInt(e.val()),s=Math.min(Math.max(a,i-r),n),o=parseInt(t.val());t.empty();for(var l=a;l<=s;l++){var c=jQuery('<option value="'+l+'"'+(l===o?'selected="selected"':"")+">"+l+"</option>");t.append(c)}}this.loadAvailabilityAndPriceData()}}),MPHB.ReservationForm=can.Control.extend({},{$formElement:null,checkInDatepicker:null,checkOutDatepicker:null,reserveBtnWrapper:null,errorsWrapper:null,directBooking:null,roomTypeId:null,init:function(e){this.$formElement=e,this.roomTypeId=parseInt(this.$formElement.attr("id").replace(/^booking-form-/,"")),this.errorsWrapper=this.$formElement.find(".mphb-errors-wrapper");var t=this.$formElement.attr("data-first_available_check_in_date");t=t||W.datepick.formatDate("yyyy-mm-dd",new Date),this.checkInDatepicker=new MPHB.RoomTypeCheckInDatepicker(this.$formElement.find('input[type="text"][id^=mphb_check_in_date]'),{form:this,roomTypeId:"1"==MPHB._data.settings.isDirectBooking?this.roomTypeId:0,firstAvailableCheckInDateYmd:t}),this.checkOutDatepicker=new MPHB.RoomTypeCheckOutDatepicker(this.$formElement.find('input[type="text"][id^=mphb_check_out_date]'),{form:this,roomTypeId:"1"==MPHB._data.settings.isDirectBooking?this.roomTypeId:0,firstAvailableCheckInDateYmd:t}),this.reserveBtnWrapper=this.$formElement.find(".mphb-reserve-btn-wrapper"),"1"==MPHB._data.settings.isDirectBooking&&(this.directBooking=new MPHB.DirectBooking(this.$formElement,{reservationForm:this})),W(window).on("mphb-update-date-room-type-"+this.roomTypeId,this.proxy(function(){this.checkInDatepicker.refresh(),this.checkOutDatepicker.refresh()})),this.unlock()},updateCheckOutLimitations:function(){this.checkOutDatepicker.updateCheckOutLimitations(this.checkInDatepicker.getDate())},getAdults:function(){var e=this.$formElement.find('[name="mphb_adults"]');return 0<e.length?parseInt(e.val()):""},getChildren:function(){var e=this.$formElement.find('[name="mphb_children"]');return 0<e.length?parseInt(e.val()):""},showError:function(e){this.clearErrors();var t=W("<p>",{class:"mphb-error",html:e});this.errorsWrapper.append(t).removeClass("mphb-hide")},clearErrors:function(){this.errorsWrapper.empty().addClass("mphb-hide")},lock:function(){this.element.addClass("mphb-loading")},unlock:function(){this.element.removeClass("mphb-loading")},onDatepickChange:function(){null!==this.directBooking&&this.directBooking.hideSections()}}),MPHB.RoomTypeCalendar=can.Control.extend({},{roomTypeId:null,$calendarElement:null,isShowPrices:!1,isTruncatePrices:!0,isShowPricesCurrency:!1,allShownMonthsCount:1,isClickable:!1,reservationFormElement:null,$reservationFormCheckInElement:null,$reservationFormCheckOutElement:null,isSyncWithReservationFormInitialised:!1,isSyncWithReservationFormOn:!0,lastDrawDate:null,isCheckInSelected:!1,isCheckOutSelected:!1,minCheckOutDateForSelection:null,maxCheckOutDateForSelection:null,minStayDateAfterCheckIn:null,maxStayDateAfterCheckIn:null,init:function(e){var i=this;this.$calendarElement=e,this.roomTypeId=parseInt(this.$calendarElement.data("roomTypeId")),void 0!==this.$calendarElement.data("is_show_prices")&&(this.isShowPrices=Boolean(this.$calendarElement.data("is_show_prices"))),void 0!==this.$calendarElement.data("is_truncate_prices")&&(this.isTruncatePrices=Boolean(this.$calendarElement.data("is_truncate_prices"))),void 0!==this.$calendarElement.data("is_show_prices_currency")&&(this.isShowPricesCurrency=Boolean(this.$calendarElement.data("is_show_prices_currency")));var t=MPHB._data.settings.numberOfMonthCalendar,a=this.$calendarElement.attr("data-monthstoshow");if(a){var n=a.split(",");t=1==n.length?parseInt(a):n}Array.isArray(t)?this.allShownMonthsCount=parseInt(t[0])*parseInt(t[1]):this.allShownMonthsCount=t,"1"==MPHB._data.settings.isDirectBooking&&(this.reservationFormElement=W("#booking-form-"+this.roomTypeId),this.isClickable=0<this.reservationFormElement.length,this.isClickable&&(this.$reservationFormCheckInElement=this.reservationFormElement.find('input[type="text"][id^=mphb_check_in_date]'),this.$reservationFormCheckOutElement=this.reservationFormElement.find('input[type="text"][id^=mphb_check_out_date]')));var r=this.$calendarElement.attr("data-first_available_check_in_date");r=r||W.datepick.formatDate("yyyy-mm-dd",new Date);var s=new Date(r);MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(s.getFullYear(),s.getMonth(),1),this.allShownMonthsCount,this.roomTypeId,this.isShowPrices,this.isTruncatePrices,this.isShowPricesCurrency,function(){i.$calendarElement.addClass("mphb-loading")},function(){i.doAfterNewCalendarDataLoaded(i),i.$calendarElement.removeClass("mphb-loading")}),this.$calendarElement.hide().datepick({minDate:MPHB.get_today_date(),defaultDate:s,monthsToShow:t,firstDay:MPHB._data.settings.firstDay,pickerClass:MPHB._data.settings.datepickerClass,useMouseWheel:!1,rangeSelect:i.isClickable,showSpeed:0,onChangeMonthYear:function(e,t){i.isClickable&&(i.lastDrawDate=W.datepick._getInst(i.$calendarElement).drawDate),MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(e,t-1,1),i.allShownMonthsCount,i.roomTypeId,i.isShowPrices,i.isTruncatePrices,i.isShowPricesCurrency,function(){i.$calendarElement.addClass("mphb-loading")},function(){i.doAfterNewCalendarDataLoaded(i),i.$calendarElement.removeClass("mphb-loading")})},onDate:function(e,t){var a=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(i.roomTypeId,i.isShowPrices,i.isTruncatePrices,i.isShowPricesCurrency),n=MPHB.calendarHelper.getCalendarDateAttributesFromAvailability(1,e,t,a,i.isShowPrices);return t&&(n=i.fillClickableCalendarDateData(n,e)),n},onSelect:function(e){if(i.isClickable&&0!==e.length){if(!i.isCheckInSelected||i.isCheckInSelected&&i.isCheckOutSelected){i.isCheckInSelected=!0,i.isCheckOutSelected=!1;var t=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(i.roomTypeId,i.isShowPrices,i.isTruncatePrices,i.isShowPricesCurrency),a=MPHB.calendarHelper.calculateMinMaxCheckOutDateForSelection(e[0],t);i.minCheckOutDateForSelection=a.minCheckOutDateForSelection,i.maxCheckOutDateForSelection=a.maxCheckOutDateForSelection,i.minStayDateAfterCheckIn=a.minStayDateAfterCheckIn,i.maxStayDateAfterCheckIn=a.maxStayDateAfterCheckIn}else i.isCheckOutSelected=!0;var n=W.datepick._getInst(i.$calendarElement);n.drawDate=i.lastDrawDate,n.options.setSelectedDatesToStatusBar(n,e),i.fillReservationFormWithSelectedDates(e)}},onShow:function(e,t){e.find(".datepick-highlight").removeClass("datepick-highlight"),i.isClickable&&(i.lastDrawDate=t.drawDate,t.options.initStatusBar(e,t),t.options.initSelectionOnHover(e,t))},setSelectedDatesToStatusBar:function(e,t){var a=MPHB._data.translations.selectDates;i.isCheckInSelected&&(a=W.datepick.formatDate(MPHB._data.settings.dateFormat,t[0]),i.isCheckOutSelected&&(a+=" - "+W.datepick.formatDate(MPHB._data.settings.dateFormat,t[1]))),e.options.renderer.picker='<div class="datepick"><div class="datepick-nav">{link:prev}{link:today}{link:next}</div>{months}<div class="datepick-ctrl"><div class="mphb-calendar__selected-dates">'+a+'</div>{link:clear}</div><div class="datepick-clear-fix"></div></div>'},initStatusBar:function(e,t){t.options.renderer=Object.assign({},t.options.renderer),t.options.setSelectedDatesToStatusBar(t,i.$calendarElement.datepick("getDate")),t.options.commands=Object.assign({},t.options.commands),t.options.commands.close.keystroke={},t.options.commands.clear.keystroke={keyCode:27,altKey:!1},t.options.commands.clear.action=function(e){if(e===W.datepick._getInst(i.$calendarElement)||e===W.datepick._getInst(i.$reservationFormCheckInElement)||e===W.datepick._getInst(i.$reservationFormCheckOutElement)){if(i.isCheckInSelected){var t=MPHB.Utils.cloneDate(i.lastDrawDate);i.isCheckInSelected=!1,i.isCheckOutSelected=!1,i.$calendarElement.datepick("setDate",null),i.$reservationFormCheckInElement.datepick("setDate",null),i.$reservationFormCheckOutElement.datepick("setDate",null),e.drawDate=t,i.lastDrawDate=t,i.refresh()}}else e.elem.datepick("clear")}},initSelectionOnHover:function(e,t){e.find(t.get("renderer").daySelector+" a").hover(function(){if(i.isCheckInSelected&&!i.isCheckOutSelected){var e=W.datepick.retrieveDate(i.$calendarElement,this),t=i.$calendarElement.datepick("getDate"),a=MPHB.Utils.cloneDate(t[0]);if(a.setDate(a.getDate()+1),t[0].getTime()<e.getTime()&&i.$calendarElement.datepick("isSelectable",e))for(;e.getTime()>a.getTime();)i.$calendarElement.find(".dp"+a.getTime()).not(".mphb-extra-date").addClass("mphb-selected-date"),a.setDate(a.getDate()+1)}},function(){i.isCheckInSelected&&!i.isCheckOutSelected&&i.$calendarElement.find(".mphb-selected-date").removeClass("mphb-selected-date")})}}).show()},doAfterNewCalendarDataLoaded:function(e){if(e.isCheckInSelected&&!e.isCheckOutSelected){var t=e.$calendarElement.datepick("getDate"),a=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(e.roomTypeId,e.isShowPrices,e.isTruncatePrices,e.isShowPricesCurrency),n=MPHB.calendarHelper.calculateMinMaxCheckOutDateForSelection(t[0],a);e.minCheckOutDateForSelection=n.minCheckOutDateForSelection,e.maxCheckOutDateForSelection=n.maxCheckOutDateForSelection,e.minStayDateAfterCheckIn=n.minStayDateAfterCheckIn,e.maxStayDateAfterCheckIn=n.maxStayDateAfterCheckIn}e.refresh(),e.isClickable&&!e.isSyncWithReservationFormInitialised&&(e.isSyncWithReservationFormInitialised=!0,e.initSyncWithReservationForm())},fillClickableCalendarDateData:function(e,t){if(!this.isClickable)return e.selectable=!1,e;var a=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(this.roomTypeId,this.isShowPrices,this.isTruncatePrices,this.isShowPricesCurrency)[W.datepick.formatDate("yyyy-mm-dd",t)];if(void 0===a||0===Object.keys(a).length||!a.hasOwnProperty("roomTypeStatus"))return e;if(!this.isCheckInSelected||this.isCheckInSelected&&this.isCheckOutSelected?MPHB.calendarHelper.ROOM_STATUS_AVAILABLE!==a.roomTypeStatus||a.hasOwnProperty("isCheckInNotAllowed")&&a.isCheckInNotAllowed?(e.selectable=!1,e.dateClass+=" mphb-unselectable-date--check-in"):(e.selectable=!0,e.dateClass+=" mphb-selectable-date--check-in"):(!(null!==this.minCheckOutDateForSelection&&this.minCheckOutDateForSelection.getTime()<=t.getTime()&&(null===this.maxCheckOutDateForSelection||this.maxCheckOutDateForSelection.getTime()>=t.getTime()))||a.hasOwnProperty("isCheckOutNotAllowed")&&a.isCheckOutNotAllowed?(e.selectable=!1,e.dateClass+=" mphb-unselectable-date--check-out"):(e.selectable=!0,e.dateClass+=" mphb-selectable-date--check-out"),null!==this.minStayDateAfterCheckIn&&this.minStayDateAfterCheckIn.getTime()>t.getTime()&&(e.title+="\n"+MPHB._data.translations.lessThanMinDaysStay),null!==this.maxStayDateAfterCheckIn&&this.maxStayDateAfterCheckIn.getTime()<t.getTime()&&(e.title+="\n"+MPHB._data.translations.moreThanMaxDaysStay)),this.isCheckInSelected||this.isCheckOutSelected){var n=this.$calendarElement.datepick("getDate"),i=MPHB.Utils.cloneDate(n[0]),r=W.datepick.formatDate("yyyy-mm-dd",i),s=MPHB.Utils.cloneDate(n[1]),o=W.datepick.formatDate("yyyy-mm-dd",s),l=W.datepick.formatDate("yyyy-mm-dd",t);i.setHours(0,0,0,0),s.setHours(23,59,59,999),r===l?e.dateClass+=" mphb-selected-date--check-in":this.isCheckOutSelected&&o===l?e.dateClass+=" mphb-selected-date--check-out":this.isCheckInSelected&&this.isCheckOutSelected&&i.getTime()<=t.getTime()&&s.getTime()>=t.getTime()&&(e.dateClass+=" mphb-selected-date")}return e},selectCheckInDateInCalendar:function(e){var t=this.$calendarElement.datepick("getDate");W.datepick.formatDate("yyyy-mm-dd",t[0])!==W.datepick.formatDate("yyyy-mm-dd",e)&&(this.isCheckInSelected&&!this.isCheckOutSelected&&(this.isCheckInSelected=!1),this.$calendarElement.datepick("setDate",e),W.datepick._getInst(this.$calendarElement).pickingRange=!0,this.refresh())},selectCheckOutDateInCalendar:function(e){if(this.isCheckInSelected){var t=this.$calendarElement.datepick("getDate");W.datepick.formatDate("yyyy-mm-dd",t[1])!==W.datepick.formatDate("yyyy-mm-dd",e)&&(this.isCheckOutSelected&&(this.isCheckOutSelected=!1),this.$calendarElement.datepick("setDate",t[0],e),this.refresh())}},initSyncWithReservationForm:function(){var a=this,e=this.$reservationFormCheckInElement.datepick("getDate")[0],t=this.$reservationFormCheckOutElement.datepick("getDate")[0];e&&(this.isSyncWithReservationFormOn=!1,this.selectCheckInDateInCalendar(e),t&&this.selectCheckOutDateInCalendar(t),this.isSyncWithReservationFormOn=!0),this.$reservationFormCheckInElement.change(function(e){var t=a.$reservationFormCheckInElement.datepick("getDate")[0];a.isSyncWithReservationFormOn=!1,a.selectCheckInDateInCalendar(t),a.isSyncWithReservationFormOn=!0}),this.$reservationFormCheckOutElement.change(function(e){var t=a.$reservationFormCheckOutElement.datepick("getDate")[0];void 0!==t&&(a.isSyncWithReservationFormOn=!1,a.selectCheckOutDateInCalendar(t),a.isSyncWithReservationFormOn=!0)})},fillReservationFormWithSelectedDates:function(e){if(this.isSyncWithReservationFormOn){if(this.isCheckInSelected){var t=this.$reservationFormCheckInElement.datepick("getDate")[0];W.datepick.formatDate("yyyy-mm-dd",e[0])!==W.datepick.formatDate("yyyy-mm-dd",t)&&this.$reservationFormCheckInElement.datepick("setDate",e[0])}if(this.isCheckOutSelected){var a=this.$reservationFormCheckOutElement.datepick("getDate")[0];W.datepick.formatDate("yyyy-mm-dd",e[1])!==W.datepick.formatDate("yyyy-mm-dd",a)&&this.$reservationFormCheckOutElement.datepick("setDate",e[1])}else this.$reservationFormCheckOutElement.datepick("setDate",null)}},refresh:function(){this.$calendarElement.hide(),W.datepick._update(this.$calendarElement,!0),this.$calendarElement.show()}}),MPHB.Datepicker("MPHB.RoomTypeCheckInDatepicker",{},{getDatepickSettings:function(){var n=this;return{defaultDate:this.firstAvailableCheckInDate,onShow:function(e,t){MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(t.drawDate.getFullYear(),t.drawDate.getMonth(),1),MPHB._data.settings.numberOfMonthDatepicker,n.roomTypeId,!1,!1,!1,function(){n.lock()},function(){n.form.updateCheckOutLimitations(),n.refresh(),n.unlock()},MPHB._data.settings.numberOfMonthDatepicker),e.find(".datepick-highlight").removeClass("datepick-highlight")},onChangeMonthYear:function(e,t){MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(e,t-1,1),MPHB._data.settings.numberOfMonthDatepicker,n.roomTypeId,!1,!1,!1,function(){n.lock()},function(){n.refresh(),n.unlock()},MPHB._data.settings.numberOfMonthDatepicker)},onSelect:function(){n.form.updateCheckOutLimitations(),n.form.onDatepickChange(),n.form.checkOutDatepicker.clear(),n.element.trigger("change")},onDate:function(e,t){var a=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(n.roomTypeId);return MPHB.calendarHelper.getCalendarDateAttributesFromAvailability(2,e,t,a)},pickerClass:"mphb-datepick-popup mphb-check-in-datepick "+MPHB._data.settings.datepickerClass}}}),MPHB.RoomTypeCheckOutDatepicker=MPHB.Datepicker.extend({},{minCheckOutDateForSelection:null,maxCheckOutDateForSelection:null,minStayDateAfterCheckIn:null,maxStayDateAfterCheckIn:null,updateCheckOutLimitations:function(e){if(!e)return this.minCheckOutDateForSelection=null,this.maxCheckOutDateForSelection=null,this.minStayDateAfterCheckIn=null,this.maxStayDateAfterCheckIn=null,void this.element.datepick("option","defaultDate",this.firstAvailableCheckInDate);var t=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(this.roomTypeId),a=MPHB.calendarHelper.calculateMinMaxCheckOutDateForSelection(e,t);this.minCheckOutDateForSelection=a.minCheckOutDateForSelection,this.maxCheckOutDateForSelection=a.maxCheckOutDateForSelection,this.minStayDateAfterCheckIn=a.minStayDateAfterCheckIn,this.maxStayDateAfterCheckIn=a.maxStayDateAfterCheckIn,this.element.datepick("option","defaultDate",this.minCheckOutDateForSelection?this.minCheckOutDateForSelection:e)},getDatepickSettings:function(){var n=this;return{defaultDate:this.minCheckOutDateForSelection?this.minCheckOutDateForSelection:this.firstAvailableCheckInDate,onShow:function(e,t){MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(t.drawDate.getFullYear(),t.drawDate.getMonth(),1),MPHB._data.settings.numberOfMonthDatepicker,n.roomTypeId,!1,!1,!1,function(){n.lock()},function(){n.form.updateCheckOutLimitations(),n.refresh(),n.unlock()},MPHB._data.settings.numberOfMonthDatepicker),e.find(".datepick-highlight").removeClass("datepick-highlight")},onChangeMonthYear:function(e,t){MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(e,t-1,1),MPHB._data.settings.numberOfMonthDatepicker,n.roomTypeId,!1,!1,!1,function(){n.lock()},function(){n.form.updateCheckOutLimitations(),n.refresh(),n.unlock()},MPHB._data.settings.numberOfMonthDatepicker)},onSelect:function(){n.form.onDatepickChange(),n.element.trigger("change")},onDate:function(e,t){var a=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(n.roomTypeId);return MPHB.calendarHelper.getCalendarDateAttributesFromAvailability(3,e,t,a,!1,n.form.checkInDatepicker.getDate(),n.minStayDateAfterCheckIn,n.maxStayDateAfterCheckIn,n.minCheckOutDateForSelection,n.maxCheckOutDateForSelection)},pickerClass:"mphb-datepick-popup mphb-check-out-datepick "+MPHB._data.settings.datepickerClass}}}),MPHB.SearchCheckInDatepicker=MPHB.Datepicker.extend({},{getDatepickSettings:function(){var a=this;return{defaultDate:this.firstAvailableCheckInDate,onShow:function(e,t){MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(t.drawDate.getFullYear(),t.drawDate.getMonth(),1),MPHB._data.settings.numberOfMonthDatepicker,0,!1,!1,!1,function(){a.lock()},function(){a.form.updateCheckOutLimitations(),a.refresh(),a.unlock()},MPHB._data.settings.numberOfMonthDatepicker),e.find(".datepick-highlight").removeClass("datepick-highlight")},onChangeMonthYear:function(e,t){MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(e,t-1,1),MPHB._data.settings.numberOfMonthDatepicker,0,!1,!1,!1,function(){a.lock()},function(){a.refresh(),a.unlock()},MPHB._data.settings.numberOfMonthDatepicker)},onSelect:function(){a.form.updateCheckOutLimitations()},onDate:function(e,t){var a=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(0);return MPHB.calendarHelper.getCalendarDateAttributesFromAvailability(2,e,t,a)},pickerClass:"mphb-datepick-popup mphb-check-in-datepick "+MPHB._data.settings.datepickerClass}}}),MPHB.SearchCheckOutDatepicker=MPHB.Datepicker.extend({},{minCheckOutDateForSelection:null,maxCheckOutDateForSelection:null,minStayDateAfterCheckIn:null,maxStayDateAfterCheckIn:null,updateCheckOutLimitations:function(e){if(!e)return this.minCheckOutDateForSelection=null,this.maxCheckOutDateForSelection=null,this.minStayDateAfterCheckIn=null,void(this.maxStayDateAfterCheckIn=null);var t=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(0),a=MPHB.calendarHelper.calculateMinMaxCheckOutDateForSelection(e,t);this.minCheckOutDateForSelection=a.minCheckOutDateForSelection,this.maxCheckOutDateForSelection=a.maxCheckOutDateForSelection,this.minStayDateAfterCheckIn=a.minStayDateAfterCheckIn,this.maxStayDateAfterCheckIn=a.maxStayDateAfterCheckIn,(!this.getDate()||this.getDate()<=e)&&this.setDate(this.minCheckOutDateForSelection)},getDatepickSettings:function(){var i=this;return{defaultDate:this.firstAvailableCheckInDate,onShow:function(e,t){MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(t.drawDate.getFullYear(),t.drawDate.getMonth(),1),MPHB._data.settings.numberOfMonthDatepicker,0,!1,!1,!1,function(){i.lock()},function(){i.form.updateCheckOutLimitations(),i.refresh(),i.unlock()},MPHB._data.settings.numberOfMonthDatepicker),e.find(".datepick-highlight").removeClass("datepick-highlight")},onChangeMonthYear:function(e,t){var a=W.datepick._getInst(i.element[0]),n=new Date(a.drawDate.getTime());MPHB.ajaxApiHelper.loadRoomTypeCalendarData(new Date(e,t-1,1),MPHB._data.settings.numberOfMonthDatepicker,0,!1,!1,!1,function(){i.lock()},function(){i.form.updateCheckOutLimitations(),W.datepick._getInst(i.element[0]).drawDate=n,i.refresh(),i.unlock()},MPHB._data.settings.numberOfMonthDatepicker)},onDate:function(e,t){var a=MPHB.ajaxApiHelper.getLoadedRoomTypeCalendarData(0);return MPHB.calendarHelper.getCalendarDateAttributesFromAvailability(3,e,t,a,!1,i.form.checkInDatepicker.getDate(),i.minStayDateAfterCheckIn,i.maxStayDateAfterCheckIn,i.minCheckOutDateForSelection,i.maxCheckOutDateForSelection)},pickerClass:"mphb-datepick-popup mphb-check-out-datepick "+MPHB._data.settings.datepickerClass}}}),MPHB.SearchForm=can.Control.extend({},{checkInDatepicker:null,checkOutDatepicker:null,init:function(e){var t=e.attr("data-first_available_check_in_date");t=t||W.datepick.formatDate("yyyy-mm-dd",new Date),this.checkInDatepicker=new MPHB.SearchCheckInDatepicker(e.find('.mphb-datepick[id^="mphb_check_in_date"]'),{form:this,roomTypeId:0,firstAvailableCheckInDateYmd:t}),this.checkOutDatepicker=new MPHB.SearchCheckOutDatepicker(e.find('.mphb-datepick[id^="mphb_check_out_date"]'),{form:this,roomTypeId:0,firstAvailableCheckInDateYmd:t})},updateCheckOutLimitations:function(){this.checkOutDatepicker.updateCheckOutLimitations(this.checkInDatepicker.getDate())}}),MPHB.RoomBookSection=can.Control.extend({},{roomTypeId:null,roomTitle:"",roomPrice:0,quantitySelect:null,bookButton:null,confirmButton:null,removeButton:null,messageHolder:null,messageWrapper:null,form:null,init:function(e,t){this.reservationCart=t.reservationCart,this.roomTypeId=parseInt(e.attr("data-room-type-id")),this.roomTitle=e.attr("data-room-type-title"),this.roomPrice=parseFloat(e.attr("data-room-price")),this.confirmButton=e.find(".mphb-confirm-reservation"),this.quantitySelect=e.find(".mphb-rooms-quantity"),this.messageWrapper=e.find(".mphb-rooms-reservation-message-wrapper"),this.messageHolder=e.find(".mphb-rooms-reservation-message")},getRoomTypeId:function(){return this.roomTypeId},getPrice:function(){return this.roomPrice},".mphb-book-button click":function(e,t){t.preventDefault(),t.stopPropagation();var a=this.quantitySelect.length?parseInt(this.quantitySelect.val()):1;if(this.reservationCart.addToCart(this.roomTypeId,a),MPHB._data.settings.isDirectBooking)e.prop("disabled",!0),this.reservationCart.confirmReservation();else{var n=(1==a?MPHB._data.translations.roomsAddedToReservation_singular:MPHB._data.translations.roomsAddedToReservation_plural).replace("%1$d",a).replace("%2$s",this.roomTitle);this.messageHolder.html(n),this.element.addClass("mphb-rooms-added")}},".mphb-remove-from-reservation click":function(e,t){t.preventDefault(),t.stopPropagation(),this.reservationCart.removeFromCart(this.roomTypeId),this.messageHolder.empty(),this.element.removeClass("mphb-rooms-added")},".mphb-confirm-reservation click":function(e,t){t.preventDefault(),t.stopPropagation(),this.reservationCart.confirmReservation()}}),MPHB.ReservationCart=can.Control.extend({},{cartForm:null,cartDetails:null,roomBookSections:{},cartContents:{},init:function(e){this.cartForm=e.find("#mphb-reservation-cart"),this.cartDetails=e.find(".mphb-reservation-details"),this.initRoomBookSections(e.find(".mphb-reserve-room-section"))},initRoomBookSections:function(e){var a,n=this;W.each(e,function(e,t){a=new MPHB.RoomBookSection(W(t),{reservationCart:n}),n.roomBookSections[a.getRoomTypeId()]=a})},addToCart:function(e,t){this.cartContents[e]=t,this.updateCartView(),this.updateCartInputs()},removeFromCart:function(e){delete this.cartContents[e],this.updateCartView(),this.updateCartInputs()},calcRoomsInCart:function(){var a=0;return W.each(this.cartContents,function(e,t){a+=t}),a},calcTotalPrice:function(){var a=0,n=0,i=this;return W.each(this.cartContents,function(e,t){n=i.roomBookSections[e].getPrice(),a+=n*t}),a},updateCartView:function(){if(W.isEmptyObject(this.cartContents))this.cartForm.addClass("mphb-empty-cart");else{var e=this.calcRoomsInCart(),t=(1==e?MPHB._data.translations.countRoomsSelected_singular:MPHB._data.translations.countRoomsSelected_plural).replace("%s",e);this.cartDetails.find(".mphb-cart-message").html(t);var a=this.calcTotalPrice(),n=MPHB.format_price(a,{trim_zeros:!0});this.cartDetails.find(".mphb-cart-total-price>.mphb-cart-total-price-value").html(n),this.cartForm.removeClass("mphb-empty-cart")}},updateCartInputs:function(){this.cartForm.find('[name^="mphb_rooms_details"]').remove();var n=this;W.each(this.cartContents,function(e,t){var a=W("<input />",{name:"mphb_rooms_details["+e+"]",type:"hidden",value:t});n.cartForm.prepend(a)})},confirmReservation:function(){this.cartForm.submit()}}),MPHB.StripeGateway.PaymentMethods=can.Construct.extend({},{listAll:["card","bancontact","ideal","giropay","sepa_debit","klarna"],klarnaAllowedCountryCodes:["AT","AU","BE","CA","CH","CZ","DE","DK","ES","FI","FR","GB","GR","IE","IT","NL","NO","NZ","PL","PT","SE","US"],listEnabled:["card"],paymentMethods:{},currentPayment:"card",currentCountry:"",currentCurrencyCode:"",inputs:null,isMounted:!1,init:function(e,t,a){this.listEnabled=e.slice(0),this.currentCurrencyCode=a,this.initPayments(),this.selectCountry(t)},initPayments:function(){var t=this;this.forEach(function(e){t.paymentMethods[e]={isEnabled:0<=t.listEnabled.indexOf(e),nav:null,fields:null}})},selectPayment:function(e){e!=this.currentPayment&&this.paymentMethods.hasOwnProperty(e)&&(this.togglePayment(this.currentPayment,!1),this.togglePayment(e,!0),this.currentPayment=e)},togglePayment:function(e,t){this.isMounted&&(this.paymentMethods[e].nav.toggleClass("active",t),this.paymentMethods[e].fields.toggleClass("mphb-hide",!t))},selectCountry:function(e){e!==this.currentCountry&&(this.currentCountry=e,this.selectPayment("card"),this.showRelevantMethods())},showRelevantMethods:function(){if(this.isMounted){var n=this;this.forEach(function(e,t){var a=t.isEnabled;"klarna"===e&&(a=-1!==n.klarnaAllowedCountryCodes.indexOf(n.currentCountry),"GB"===n.currentCountry&&"GBP"!==n.currentCurrencyCode&&(a=!1)),t.nav.toggleClass("mphb-hide",!a),t.fields.toggleClass("mphb-hide",e!=n.currentPayment)}),this.inputs.val([this.currentPayment])}},mount:function(a){this.forEach(function(e,t){t.nav=a.find(".mphb-stripe-payment-method."+e),t.fields=a.find(".mphb-stripe-payment-fields."+e)}),this.inputs=a.find('input[name="stripe_payment_method"]'),this.isMounted=!0,this.showRelevantMethods()},unmount:function(){this.forEach(function(e,t){t.nav=null,t.fields=null}),this.inputs=null,this.isMounted=!1},forEach:function(t){var a=this;this.listAll.forEach(function(e){t(e,a.paymentMethods[e],a)})},isEnabled:function(e){return this.paymentMethods[e].isEnabled},onlyCardEnabled:function(){return 1==this.listEnabled.length&&this.paymentMethods.card.isEnabled},isSelected:function(e){return e==this.currentPayment}}),MPHB._data.page.isCheckoutPage?new MPHB.CheckoutForm(W(".mphb_sc_checkout-form")):MPHB._data.page.isCreateBookingPage&&new MPHB.CheckoutForm(W(".mphb_cb_checkout_form")),MPHB._data.page.isSearchResultsPage&&new MPHB.ReservationCart(W(".mphb_sc_search_results-wrapper"));var e=W(".mphb-calendar.mphb-datepick");W.each(e,function(e,t){new MPHB.RoomTypeCalendar(W(t))});var t=W(".mphb-booking-form");W.each(t,function(e,t){new MPHB.ReservationForm(W(t))});var n=W("form.mphb_sc_search-form, form.mphb_widget_search-form, form.mphb_cb_search_form");W.each(n,function(e,t){new MPHB.SearchForm(W(t))});var i=W(".mphb-flexslider-gallery-wrapper");W.each(i,function(e,t){new MPHB.FlexsliderGallery(t)});var r=W(".mphb-checkout-terms-wrapper");0<r.length&&new MPHB.TermsSwitcher(r),null==W.ui&&(W.ui={}),null==W.ui.version&&(W.ui.version="1.5-")})}(jQuery);