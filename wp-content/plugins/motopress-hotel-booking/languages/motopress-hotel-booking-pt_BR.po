# Copyright (C) 2025 MotoPress
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: hotel-booking-plugin\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/motopress-hotel-booking\n"
"Last-Translator: \n"
"Language-Team: Portuguese, Brazilian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-19T19:58:50+00:00\n"
"PO-Revision-Date: 2025-03-05 20:50\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: motopress-hotel-booking\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: hotel-booking-plugin\n"
"X-Crowdin-Project-ID: 463550\n"
"X-Crowdin-Language: pt-BR\n"
"X-Crowdin-File: motopress-hotel-booking.pot\n"
"X-Crowdin-File-ID: 44\n"
"Language: pt_BR\n"

#. Plugin Name of the plugin
#. translators: Name of the plugin, do not translate
#: motopress-hotel-booking.php
#: includes/script-managers/block-script-manager.php:27
msgid "Hotel Booking"
msgstr "Hotel Booking"

#. Plugin URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/products/hotel-booking/"
msgstr ""

#. Description of the plugin
#: motopress-hotel-booking.php
msgid "Manage your hotel booking services. Perfect for hotels, villas, guest houses, hostels, and apartments of all sizes."
msgstr "Gerencie seus serviços de reserva de hotéis."

#. Author of the plugin
#: motopress-hotel-booking.php
msgid "MotoPress"
msgstr ""

#. Author URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/"
msgstr ""

#: functions.php:71
msgctxt "Post Status"
msgid "New"
msgstr "Novo"

#: functions.php:74
msgctxt "Post Status"
msgid "Auto Draft"
msgstr "Rascunho Automático"

#. translators: %s: URL to plugins.php page
#: functions.php:518
msgid "You are using two instances of Hotel Booking plugin at the same time, please <a href=\"%s\">deactivate one of them</a>."
msgstr ""

#: functions.php:535
msgid "<a href=\"%s\">Upgrade to Premium</a> to enable this feature."
msgstr ""

#: includes/actions-handler.php:100
#: includes/admin/sync-logs-list-table.php:91
#: includes/csv/csv-export-handler.php:33
#: includes/csv/csv-export-handler.php:51
#: includes/payments/gateways/stripe-gateway.php:560
#: includes/payments/gateways/stripe-gateway.php:572
#: includes/payments/gateways/stripe-gateway.php:631
msgid "Error"
msgstr "Erro"

#: includes/admin/customers-list-table.php:143
#: includes/admin/menu-pages/rooms-generator-menu-page.php:84
#: includes/admin/sync-rooms-list-table.php:146
#: includes/post-types/room-type-cpt.php:354
#: templates/account/bookings.php:80
msgid "View"
msgstr "Visualizar"

#: includes/admin/customers-list-table.php:147
#: includes/admin/fields/abstract-complex-field.php:25
#: includes/admin/fields/rules-list-field.php:61
#: includes/admin/sync-rooms-list-table.php:147
msgid "Delete"
msgstr "Excluir"

#: includes/admin/customers-list-table.php:212
#: includes/post-types/attributes-cpt.php:301
msgid "Name"
msgstr "Nome"

#: includes/admin/customers-list-table.php:213
#: includes/admin/menu-pages/customers-menu-page.php:207
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:122
#: includes/bundles/customer-bundle.php:110
#: includes/csv/bookings/bookings-exporter-helper.php:83
#: includes/post-types/booking-cpt.php:106
#: includes/post-types/payment-cpt.php:263
#: includes/views/shortcodes/checkout-view.php:618
#: templates/account/account-details.php:34
msgid "Email"
msgstr "E-mail"

#: includes/admin/customers-list-table.php:214
#: includes/admin/menus.php:72
#: includes/admin/menus.php:73
#: includes/post-types/booking-cpt.php:241
#: includes/shortcodes/account-shortcode.php:239
msgid "Bookings"
msgstr "Reservas"

#: includes/admin/customers-list-table.php:215
msgid "Date Registered"
msgstr "Data de Cadastro"

#: includes/admin/customers-list-table.php:216
msgid "Last Active"
msgstr "Ativo pela última vez"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:16
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:33
msgid "Terms"
msgstr "Termos"

#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:27
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:46
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:41
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:115
msgid "Created on:"
msgstr "Criado em:"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:88
msgid "Please add attribute in default language to configure terms."
msgstr "Por favor, adicione atributo no idioma padrão para configurar termos."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:98
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:116
msgid "Configure terms"
msgstr "Configurar termos"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:20
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:107
#: includes/post-types/reserved-room-cpt.php:22
msgid "Reserved Accommodations"
msgstr "Acomodações Reservadas"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:21
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:66
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Update Booking"
msgstr "Atualizar Reserva"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:22
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:12
msgid "Logs"
msgstr "Registros"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:54
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:125
msgid "Delete Permanently"
msgstr "Deletar permanentemente"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:58
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:127
msgid "Move to Trash"
msgstr "Mover para Lixeira"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Create Booking"
msgstr "Criar reserva"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:85
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:98
msgid "Resend Email"
msgstr "Reenviar Email"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:100
msgid "Send a copy of the Approved Booking email to the customer`s email address."
msgstr ""

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:116
#: templates/edit-booking/edit-reserved-rooms.php:35
msgid "Edit Accommodations"
msgstr "Editar acomodações"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:125
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:85
#: includes/shortcodes/booking-confirmation-shortcode.php:298
msgid "Date:"
msgstr "Data:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:130
msgid "Author:"
msgstr "Autor:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:145
#: includes/payments/gateways/stripe-gateway.php:528
msgid "Auto"
msgstr "Automático"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:155
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:89
msgid "Message:"
msgstr "Mensagem:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:227
msgid "Confirmation email has been sent to customer."
msgstr "Confirmação de email foi enviada ao cliente."

#: includes/admin/edit-cpt-pages/coupon-edit-cpt-page.php:14
msgid "Coupon code"
msgstr "Código do cupom"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:11
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:64
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Update Payment"
msgstr "Atualizar Pagamento"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:44
msgid "Modified on:"
msgstr "Modificado em:"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Create Payment"
msgstr "Criar Pagamento"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:47
msgid "Season Prices"
msgstr "Preços de Época"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:48
msgid "<code>Please select Accommodation Type and click Create Rate button to continue.</code>"
msgstr ""

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:66
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:65
#: includes/views/loop-room-type-view.php:113
#: includes/views/single-room-type-view.php:205
#: template-functions.php:920
#: templates/create-booking/results/reserve-rooms.php:51
#: templates/widgets/rooms/room-content.php:77
#: templates/widgets/search-availability/search-form.php:78
msgid "Adults:"
msgstr "Adultos:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:66
#: includes/views/loop-room-type-view.php:128
#: includes/views/single-room-type-view.php:220
#: template-functions.php:925
#: templates/create-booking/results/reserve-rooms.php:52
#: templates/widgets/rooms/room-content.php:91
#: templates/widgets/search-availability/search-form.php:103
msgid "Children:"
msgstr "Crianças:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:70
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:63
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:110
#: includes/shortcodes/booking-confirmation-shortcode.php:306
#: includes/shortcodes/search-results-shortcode.php:770
#: includes/shortcodes/search-results-shortcode.php:921
#: templates/shortcodes/booking-details/booking-details.php:33
msgid "Total:"
msgstr "Total:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:80
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Update Rate"
msgstr "Taxa de atualização"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:97
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
msgid "Active"
msgstr "Ativo"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:98
#: includes/admin/groups/license-settings-group.php:61
msgid "Disabled"
msgstr "Desativado"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Create Rate"
msgstr "Criar Taxa"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:190
msgid "Duplicate Rate"
msgstr "Duplicar Taxa"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:12
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:36
#: includes/admin/menu-pages/booking-rules-menu-page.php:219
#: includes/admin/menu-pages/booking-rules-menu-page.php:264
#: includes/admin/menu-pages/booking-rules-menu-page.php:310
#: includes/admin/menu-pages/booking-rules-menu-page.php:356
#: includes/admin/menu-pages/booking-rules-menu-page.php:487
#: includes/admin/menu-pages/booking-rules-menu-page.php:533
#: includes/admin/menu-pages/booking-rules-menu-page.php:579
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:208
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:304
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:377
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:448
#: includes/post-types/room-cpt.php:31
#: includes/post-types/room-cpt.php:41
#: includes/wizard.php:103
msgid "Accommodations"
msgstr "Acomodações"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:15
#: includes/post-types/attributes-cpt.php:54
#: includes/post-types/attributes-cpt.php:61
#: includes/post-types/attributes-cpt.php:65
msgid "Attributes"
msgstr "Atributos"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:18
msgid "Accommodation Reviews"
msgstr "Comentários da Acomodação"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:27
msgid "Allow guests to <a href=\"%s\" target=\"_blank\">submit star ratings and reviews</a> evaluating your accommodations."
msgstr "Permita que os hóspedes <a href=\"%s\" target=\"_blank\"> submetam classificações e críticas de estrelas </a> avaliando suas acomodações."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:69
msgid "Number of Accommodations:"
msgstr "Nº de Acomodações:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:74
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:29
msgid "Count of real accommodations of this type in your hotel."
msgstr "Contagem de acomodações reais deste tipo em seu hotel."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:122
msgid "Total Accommodations:"
msgstr "Total de Acomodações:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:159
msgid "Show Accommodations"
msgstr "Mostrar acomodações"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:139
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:18
#: includes/admin/menu-pages/rooms-generator-menu-page.php:146
#: includes/admin/menu-pages/rooms-generator-menu-page.php:150
msgid "Generate Accommodations"
msgstr "Gerar Acomodações"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:147
msgid "Active Accommodations:"
msgstr "Acomodações Ativas:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:168
#: includes/post-types/room-cpt.php:93
msgid "Linked Accommodations"
msgstr ""

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:173
msgid "Link accommodations on the Edit Accommodation page to ensure bookings for one make any linked properties unavailable for the same dates."
msgstr "Vincular acomodações na página Editar Acomodação para garantir que as reservas de uma só pessoa tornem quaisquer propriedades vinculadas indisponíveis nas mesmas datas."

#: includes/admin/fields/abstract-complex-field.php:24
#: includes/admin/fields/rules-list-field.php:57
#: templates/edit-booking/add-room-popup.php:45
msgid "Add"
msgstr "Adicionar"

#: includes/admin/fields/amount-field.php:74
msgid "Per adult:"
msgstr "Por adulto:"

#: includes/admin/fields/amount-field.php:77
msgid "Per child:"
msgstr "Por Crianças:"

#: includes/admin/fields/amount-field.php:198
msgid "Per adult: "
msgstr "Por adulto: "

#: includes/admin/fields/amount-field.php:200
msgid "Per child: "
msgstr "Por Crianças: "

#: includes/admin/fields/complex-horizontal-field.php:71
#: includes/admin/fields/rules-list-field.php:62
#: templates/account/bookings.php:22
#: templates/account/bookings.php:79
#: templates/edit-booking/edit-reserved-rooms.php:47
msgid "Actions"
msgstr "Ações"

#: includes/admin/fields/complex-horizontal-field.php:111
msgid "Move up"
msgstr ""

#: includes/admin/fields/complex-horizontal-field.php:112
msgid "Move down"
msgstr ""

#: includes/admin/fields/complex-horizontal-field.php:113
msgid "Move to top"
msgstr ""

#: includes/admin/fields/complex-horizontal-field.php:114
msgid "Move to bottom"
msgstr ""

#: includes/admin/fields/complex-vertical-field.php:17
#: includes/admin/menu-pages/settings-menu-page.php:580
#: includes/settings/main-settings.php:25
#: includes/settings/main-settings.php:43
msgid "Default"
msgstr "Padrão"

#: includes/admin/fields/dynamic-select-field.php:61
#: includes/admin/fields/page-select-field.php:16
#: includes/admin/menu-pages/customers-menu-page.php:260
#: includes/admin/menu-pages/rooms-generator-menu-page.php:38
#: includes/admin/menu-pages/settings-menu-page.php:420
#: includes/post-types/booking-cpt.php:122
#: includes/post-types/booking-cpt.php:179
#: includes/post-types/payment-cpt.php:231
#: includes/post-types/rate-cpt.php:31
#: includes/post-types/room-cpt.php:79
#: includes/views/shortcodes/checkout-view.php:250
#: includes/views/shortcodes/checkout-view.php:273
#: templates/account/account-details.php:51
#: templates/edit-booking/add-room-popup.php:30
#: templates/edit-booking/add-room-popup.php:38
msgid "— Select —"
msgstr "— Selecionar —"

#: includes/admin/fields/install-plugin-field.php:33
msgid "Install & Activate"
msgstr "Instalar & Ativar"

#: includes/admin/fields/media-field.php:76
msgid "Add image"
msgstr "Adicione Imagem"

#: includes/admin/fields/media-field.php:76
msgid "Add gallery"
msgstr "Adicionar galeria"

#: includes/admin/fields/media-field.php:77
msgid "Remove image"
msgstr "Remover imagem"

#: includes/admin/fields/media-field.php:77
msgid "Remove gallery"
msgstr "Remover galeria"

#: includes/admin/fields/multiple-checkbox-field.php:88
#: template-functions.php:1088
msgid "Select all"
msgstr "Selecionar tudo"

#: includes/admin/fields/multiple-checkbox-field.php:92
#: template-functions.php:1090
msgid "Unselect all"
msgstr "Desmarque todos"

#: includes/admin/fields/notes-list-field.php:23
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:68
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:33
#: includes/csv/bookings/bookings-exporter-helper.php:112
#: assets/blocks/blocks.js:593
#: assets/blocks/blocks.js:929
#: assets/blocks/blocks.js:1113
msgid "Date"
msgstr "Data"

#: includes/admin/fields/notes-list-field.php:33
msgid "Author"
msgstr "Autor"

#: includes/admin/fields/rules-list-field.php:59
#: includes/admin/room-list-table.php:154
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:115
#: includes/bookings-calendar.php:613
#: includes/script-managers/admin-script-manager.php:97
msgid "Edit"
msgstr "Editar"

#: includes/admin/fields/rules-list-field.php:60
#: includes/admin/sync-rooms-list-table.php:81
#: includes/ajax.php:951
#: includes/script-managers/admin-script-manager.php:98
msgid "Done"
msgstr "Pronto"

#: includes/admin/fields/rules-list-field.php:64
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:85
#: includes/admin/menu-pages/booking-rules-menu-page.php:180
#: includes/admin/menu-pages/booking-rules-menu-page.php:183
#: includes/admin/menu-pages/booking-rules-menu-page.php:407
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:211
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:307
#: includes/script-managers/admin-script-manager.php:95
msgid "All"
msgstr "Todos"

#: includes/admin/fields/rules-list-field.php:65
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:83
#: includes/post-types/coupon-cpt.php:81
#: includes/post-types/coupon-cpt.php:113
#: includes/post-types/coupon-cpt.php:158
#: includes/script-managers/admin-script-manager.php:96
msgid "None"
msgstr "Nenhum"

#: includes/admin/fields/time-picker-field.php:13
msgid "HH:MM"
msgstr ""

#: includes/admin/fields/total-price-field.php:18
msgid "Recalculate Total Price"
msgstr "Recalcular Preço Total"

#: includes/admin/fields/variable-pricing-field.php:89
#: includes/views/booking-view.php:121
msgid "Nights"
msgstr "Noites"

#: includes/admin/fields/variable-pricing-field.php:97
#: includes/script-managers/admin-script-manager.php:102
msgid "and more"
msgstr "ou mais"

#: includes/admin/fields/variable-pricing-field.php:98
#: includes/admin/menu-pages/edit-booking/edit-control.php:95
#: includes/script-managers/admin-script-manager.php:101
#: includes/shortcodes/search-results-shortcode.php:1009
#: includes/views/booking-view.php:400
#: templates/edit-booking/edit-reserved-rooms.php:70
msgid "Remove"
msgstr "Remover"

#: includes/admin/fields/variable-pricing-field.php:104
msgid "Add length of stay"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:109
msgid "Base Occupancy"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:110
#: includes/admin/fields/variable-pricing-field.php:170
msgid "Price per night"
msgstr "Preço por noite"

#: includes/admin/fields/variable-pricing-field.php:118
#: includes/admin/fields/variable-pricing-field.php:167
#: includes/emails/templaters/reserved-rooms-templater.php:175
#: includes/post-types/room-type-cpt.php:283
#: includes/views/booking-view.php:105
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:242
#: template-functions.php:765
#: templates/create-booking/search/search-form.php:94
#: templates/shortcodes/search/search-form.php:80
#: assets/blocks/blocks.js:147
msgid "Adults"
msgstr "Adultos"

#: includes/admin/fields/variable-pricing-field.php:122
#: includes/csv/bookings/bookings-exporter-helper.php:80
#: includes/emails/templaters/reserved-rooms-templater.php:179
#: includes/post-types/room-type-cpt.php:292
#: includes/views/booking-view.php:116
#: templates/create-booking/search/search-form.php:109
#: templates/shortcodes/search/search-form.php:103
#: assets/blocks/blocks.js:162
msgid "Children"
msgstr "Crianças"

#: includes/admin/fields/variable-pricing-field.php:130
msgid "Price per extra adult"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:137
msgid "Price per extra child"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:154
msgid "Enable variable pricing"
msgstr "Ativar preços variáveis"

#: includes/admin/fields/variable-pricing-field.php:188
msgid "Add Variation"
msgstr "Adicionar variação"

#: includes/admin/fields/variable-pricing-field.php:215
#: includes/admin/fields/variable-pricing-field.php:231
msgid "Remove variation"
msgstr "Remover variação"

#: includes/admin/groups/license-settings-group.php:21
msgid "The License Key is required in order to get automatic plugin updates and support. You can manage your License Key in your personal account. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Learn more</a>."
msgstr "A chave de licença é necessária para obter as atualizações e suporte automáticos do plugin. É possível gerenciar sua chave de licença em sua conta pessoal. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Saiba mais</a>."

#: includes/admin/groups/license-settings-group.php:28
msgid "License Key"
msgstr "Chave de Licença"

#: includes/admin/groups/license-settings-group.php:42
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:62
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:22
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:28
#: includes/admin/menu-pages/create-booking/checkout-step.php:63
#: includes/admin/sync-logs-list-table.php:72
#: includes/admin/sync-rooms-list-table.php:127
#: includes/csv/bookings/bookings-exporter-helper.php:72
#: template-functions.php:977
#: templates/edit-booking/edit-reserved-rooms.php:46
msgid "Status"
msgstr "Estado"

#: includes/admin/groups/license-settings-group.php:49
msgid "Inactive"
msgstr "Inativo"

#: includes/admin/groups/license-settings-group.php:55
msgid "Valid until"
msgstr "Válido até"

#: includes/admin/groups/license-settings-group.php:57
msgid "Valid (Lifetime)"
msgstr "Valido por (Toda a Vida)"

#: includes/admin/groups/license-settings-group.php:64
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:123
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:85
msgid "Expired"
msgstr "Expirado"

#: includes/admin/groups/license-settings-group.php:67
msgid "Invalid"
msgstr "Invalido"

#: includes/admin/groups/license-settings-group.php:71
msgid "Your License Key does not match the installed plugin. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>How to fix this.</a>"
msgstr "Sua chave de licença não corresponde ao plugin instalado. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>Saiba como resolver isso.</a>"

#: includes/admin/groups/license-settings-group.php:74
msgid "Product ID is not valid"
msgstr "o ID do produto não está válido."

#: includes/admin/groups/license-settings-group.php:83
msgid "Action"
msgstr "Ação"

#: includes/admin/groups/license-settings-group.php:90
msgid "Activate License"
msgstr "Ativar Licença"

#: includes/admin/groups/license-settings-group.php:96
msgid "Deactivate License"
msgstr "Desativar Licença"

#: includes/admin/groups/license-settings-group.php:103
msgid "Renew License"
msgstr "Renovar Licença"

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:9
msgid "Attributes let you define extra accommodation data, such as location or type. You can use these attributes in the search availability form as advanced search filters."
msgstr ""

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:72
msgid "This attribute refers to non-unique taxonomy - %1$s - which was already registered with attribute %2$s."
msgstr ""

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:92
msgid "You cannot manage terms of trashed attributes."
msgstr ""

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:17
#: includes/admin/menu-pages/calendar-menu-page.php:31
#: includes/post-types/booking-cpt.php:246
msgid "New Booking"
msgstr "Nova Reserva"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:61
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:26
#: includes/admin/menu-pages/shortcodes-menu-page.php:376
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:64
#: includes/csv/bookings/bookings-exporter-helper.php:71
#: includes/post-types/booking-cpt.php:42
#: includes/post-types/payment-cpt.php:150
msgid "ID"
msgstr "ID"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:63
msgid "Check-in / Check-out"
msgstr "Check-in — Check-out"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:64
#: includes/views/booking-view.php:107
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:244
#: template-functions.php:767
#: templates/shortcodes/search/search-form.php:82
msgid "Guests"
msgstr "Convidados"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:65
#: templates/emails/admin-customer-cancelled-booking.php:23
#: templates/emails/admin-customer-confirmed-booking.php:23
#: templates/emails/admin-payment-confirmed-booking.php:30
#: templates/emails/admin-pending-booking.php:23
msgid "Customer Info"
msgstr "Informação do Cliente"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:66
#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:16
#: includes/post-types/rate-cpt.php:64
#: includes/post-types/service-cpt.php:132
#: includes/post-types/service-cpt.php:137
#: includes/views/single-service-view.php:18
#: includes/widgets/rooms-widget.php:201
#: assets/blocks/blocks.js:496
#: assets/blocks/blocks.js:547
#: assets/blocks/blocks.js:746
#: assets/blocks/blocks.js:883
#: assets/blocks/blocks.js:1067
#: assets/blocks/blocks.js:1275
msgid "Price"
msgstr "Preço"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:67
#: includes/admin/menu-pages/booking-rules-menu-page.php:402
#: includes/admin/room-list-table.php:93
#: includes/admin/sync-rooms-list-table.php:126
#: includes/bookings-calendar.php:829
#: includes/bookings-calendar.php:847
#: includes/csv/bookings/bookings-exporter-helper.php:77
#: includes/post-types/room-cpt.php:32
#: includes/post-types/room-cpt.php:74
#: includes/post-types/room-type-cpt.php:60
#: templates/edit-booking/add-room-popup.php:36
#: templates/edit-booking/edit-reserved-rooms.php:45
msgid "Accommodation"
msgstr "Alojamento"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:121
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:83
msgid "Expire %s"
msgstr "Expira %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:138
#: includes/script-managers/admin-script-manager.php:99
msgid "Adults: "
msgstr "Adultos: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:142
#: includes/script-managers/admin-script-manager.php:100
msgid "Children: "
msgstr "Crianças: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:184
msgid "%s night"
msgid_plural "%s nights"
msgstr[0] "%s noite"
msgstr[1] "%s noites"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:197
#: includes/bookings-calendar.php:1189
msgid "Summary: %s."
msgstr "Resumo: %s."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:280
msgid "Paid: %s"
msgstr "Pago: %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:345
msgid "Set to %s"
msgstr "Definir como %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:460
msgid "Booking status changed."
msgid_plural "%s booking statuses changed."
msgstr[0] "Status da reserva alterado."
msgstr[1] "%s Status das reservas alterados."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:506
msgid "All accommodation types"
msgstr "Todos os Tipos de Acomodações"

#. translators: The number of imported bookings: "Imported <span>(11)</span>"
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:526
msgid "Imported %s"
msgstr "Importado '%s'"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:19
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:29
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:168
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:264
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:349
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:420
#: includes/post-types/coupon-cpt.php:93
#: includes/post-types/coupon-cpt.php:124
#: includes/post-types/coupon-cpt.php:169
#: includes/post-types/payment-cpt.php:182
#: includes/views/booking-view.php:126
#: includes/views/booking-view.php:172
#: includes/views/booking-view.php:208
#: includes/views/booking-view.php:266
#: includes/views/booking-view.php:297
#: includes/views/booking-view.php:350
#: template-functions.php:978
msgid "Amount"
msgstr "Valor"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:20
msgid "Uses"
msgstr "Usos"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:21
#: includes/post-types/coupon-cpt.php:187
msgid "Expiration Date"
msgstr "Data de Validade"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:41
#: includes/views/edit-booking/checkout-view.php:115
#: template-functions.php:900
msgid "Accommodation:"
msgstr "Acomodações:"

#. translators: %s is a coupon amount per day
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:60
msgid "%s per day"
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:67
msgid "Service:"
msgstr "Serviço:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:88
msgid "Fee:"
msgstr "Taxa:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:153
msgid "Note: the use of coupons is disabled in settings."
msgstr "Nota: o uso de cupons está desabilitado nas configurações."

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:165
#: includes/admin/menu-pages/settings-menu-page.php:299
msgid "Enable the use of coupons."
msgstr "Permitir o uso de cupons."

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:27
#: includes/admin/menu-pages/customers-menu-page.php:299
msgid "Customer"
msgstr "Cliente"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:30
#: includes/post-types/booking-cpt.php:242
#: templates/account/bookings.php:18
#: templates/account/bookings.php:66
msgid "Booking"
msgstr "Reserva"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:31
#: includes/post-types/payment-cpt.php:159
msgid "Gateway"
msgstr ""

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:32
#: includes/post-types/payment-cpt.php:223
msgid "Transaction ID"
msgstr "ID da transação"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:105
#: includes/admin/menu-pages/create-booking/booking-step.php:66
#: includes/bookings-calendar.php:606
#: includes/script-managers/admin-script-manager.php:103
msgid "Booking #%s"
msgstr "Reserva #%s"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:13
msgid "Rates are used to offer different prices of the same accommodation type depending on extra conditions, e.g. With Breakfast, With No Breakfast, Refundable etc. Guests will choose the preferable rate when submitting a booking request. Create one default rate if you have no price tiers. To add price variations for different periods - open a rate, add a season, and set the price."
msgstr ""

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:23
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:32
#: includes/admin/menu-pages/booking-rules-menu-page.php:393
#: includes/admin/menu-pages/rooms-generator-menu-page.php:34
#: includes/csv/bookings/bookings-exporter-helper.php:75
#: includes/post-types/rate-cpt.php:30
#: includes/post-types/room-cpt.php:84
#: includes/post-types/room-type-cpt.php:54
#: templates/create-booking/search/search-form.php:82
#: templates/edit-booking/add-room-popup.php:28
#: templates/edit-booking/edit-reserved-rooms.php:44
#: assets/blocks/blocks.js:282
#: assets/blocks/blocks.js:1202
#: assets/blocks/blocks.js:1424
#: assets/blocks/blocks.js:1506
msgid "Accommodation Type"
msgstr "Tipo de Acomodação"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:24
msgid "Season &#8212; Price"
msgstr "Preço &#8212; de Temporada"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:67
#: includes/post-types/rate-cpt.php:73
msgid "Add New Season Price"
msgstr "Adicionar Novo Preço de Temporada"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:104
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:94
#: includes/post-types/season-cpt.php:71
msgid "Annually"
msgstr ""

#. translators: %s: A date string such as "December 31, 2025".
#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:108
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:98
msgid "Annually until %s"
msgstr ""

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:201
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:281
msgid "Rate was duplicated."
msgstr "A taxa foi duplicada."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:13
msgid "These are real accommodations like rooms, apartments, houses, villas, beds (for hostels) etc."
msgstr "Estas são acomodações reais, como quartos, apartamentos, casas, moradias, camas (de hostels) etc."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:77
#: includes/admin/menu-pages/reports-menu-page.php:129
#: includes/bookings-calendar.php:746
msgid "All Accommodation Types"
msgstr "Todos os Tipos de Acomodações"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:11
msgid "These are not physical accommodations, but their types. E.g. standard double room. To specify the real number of existing accommodations, you'll need to use Generate Accommodations menu."
msgstr "Estas não são acomodações físicas, e sim os tipos. Por exemplo, quarto duplo padrão. Para especificar o número real de acomodações existentes, é preciso usar o menu Gerar Acomodações."

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:34
#: includes/post-types/room-type-cpt.php:275
#: includes/post-types/room-type-cpt.php:302
#: templates/create-booking/results/reserve-rooms.php:36
msgid "Capacity"
msgstr "Capacidade"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:35
msgid "Bed Type"
msgstr "Tipo de Cama"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:68
#: includes/views/loop-room-type-view.php:152
#: includes/views/single-room-type-view.php:244
#: templates/widgets/rooms/room-content.php:167
msgid "Size:"
msgstr "Tamanho:"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:114
msgid "Active:"
msgstr "Ativo:"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:13
msgid "Seasons are real periods of time, dates or days that come with different prices for accommodations. E.g. Winter 2018 ($120 per night), Christmas ($150 per night)."
msgstr "As estações são os períodos  de tempo, datas ou dias que oferecem outros  preços. Por exemplo, no inverno de 2018 ($120 por noite), Natal ($150 por noite)."

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:20
msgid "Start"
msgstr "Iniciar"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:21
msgid "End"
msgstr "Terminar"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:22
#: includes/admin/menu-pages/booking-rules-menu-page.php:210
#: includes/admin/menu-pages/booking-rules-menu-page.php:255
msgid "Days"
msgstr "Dias"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:23
#: includes/post-types/season-cpt.php:67
msgid "Repeat"
msgstr "Repetir"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:11
msgid "Services are extra offers that you can sell or give for free. E.g. Thai massage, transfer, babysitting. Guests can pre-order them when placing a booking."
msgstr "Os serviços são ofertas extras que é possível oferecer por uma taxa ou gratuitamente. Por exemplo. Massagem tailandesa, transferência, babá. Os hóspedes podem pré-encomendá-los ao fazer a reserva."

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:17
#: includes/post-types/service-cpt.php:150
msgid "Periodicity"
msgstr "Periodo"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:18
#: includes/post-types/service-cpt.php:206
msgid "Charge"
msgstr "Alterar"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:41
#: includes/entities/service.php:193
#: includes/post-types/service-cpt.php:153
msgid "Per Day"
msgstr "Por Dia"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:43
#: includes/post-types/service-cpt.php:154
msgid "Guest Choice"
msgstr ""

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:45
#: includes/entities/service.php:197
#: includes/post-types/service-cpt.php:152
msgid "Once"
msgstr "Uma vez"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:203
#: includes/post-types/service-cpt.php:209
msgid "Per Guest"
msgstr ""

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:205
#: includes/post-types/service-cpt.php:208
msgid "Per Accommodation"
msgstr "Por Acomodação"

#: includes/admin/manage-tax-pages/facility-manage-tax-page.php:11
msgid "These are accommodation amenities, generally free ones. E.g. air-conditioning, wifi."
msgstr "Estas são as comodidades da acomodação, geralmente gratuitas. Por exemplo. ar condicionado e Wi-Fi."

#: includes/admin/menu-pages/booking-rules-menu-page.php:34
msgid "Booking rules saved."
msgstr "Regras de reserva salvas."

#: includes/admin/menu-pages/booking-rules-menu-page.php:41
#: includes/admin/menu-pages/booking-rules-menu-page.php:602
#: includes/admin/menu-pages/booking-rules-menu-page.php:606
#: includes/admin/menu-pages/settings-menu-page.php:622
msgid "Booking Rules"
msgstr "Regras de Reservas"

#: includes/admin/menu-pages/booking-rules-menu-page.php:90
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:70
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:118
#: templates/account/account-details.php:94
msgid "Save Changes"
msgstr "Salvar alterações"

#: includes/admin/menu-pages/booking-rules-menu-page.php:199
msgid "Check-in days"
msgstr "Dias de Check-in"

#: includes/admin/menu-pages/booking-rules-menu-page.php:200
msgid "Guests can check in any day."
msgstr "Os hóspedes podem fazer check-in a qualquer dia."

#: includes/admin/menu-pages/booking-rules-menu-page.php:201
#: includes/admin/menu-pages/booking-rules-menu-page.php:246
#: includes/admin/menu-pages/booking-rules-menu-page.php:291
#: includes/admin/menu-pages/booking-rules-menu-page.php:337
#: includes/admin/menu-pages/booking-rules-menu-page.php:383
#: includes/admin/menu-pages/booking-rules-menu-page.php:468
#: includes/admin/menu-pages/booking-rules-menu-page.php:514
#: includes/admin/menu-pages/booking-rules-menu-page.php:560
msgid "Add rule"
msgstr "Adicionar regra"

#: includes/admin/menu-pages/booking-rules-menu-page.php:229
#: includes/admin/menu-pages/booking-rules-menu-page.php:274
#: includes/admin/menu-pages/booking-rules-menu-page.php:320
#: includes/admin/menu-pages/booking-rules-menu-page.php:366
#: includes/admin/menu-pages/booking-rules-menu-page.php:497
#: includes/admin/menu-pages/booking-rules-menu-page.php:543
#: includes/admin/menu-pages/booking-rules-menu-page.php:589
#: includes/post-types/season-cpt.php:98
#: includes/post-types/season-cpt.php:108
msgid "Seasons"
msgstr "Temporada"

#: includes/admin/menu-pages/booking-rules-menu-page.php:244
msgid "Check-out days"
msgstr "Dias de Check-out"

#: includes/admin/menu-pages/booking-rules-menu-page.php:245
msgid "Guests can check out any day."
msgstr "Os hóspedes podem fazer check-out a qualquer dia."

#: includes/admin/menu-pages/booking-rules-menu-page.php:289
#: includes/admin/menu-pages/booking-rules-menu-page.php:300
msgid "Minimum stay"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:290
msgid "There are no minimum stay rules."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:301
#: includes/admin/menu-pages/booking-rules-menu-page.php:347
#: includes/admin/menu-pages/booking-rules-menu-page.php:478
#: includes/admin/menu-pages/booking-rules-menu-page.php:524
#: includes/admin/menu-pages/booking-rules-menu-page.php:570
msgid "nights"
msgstr "noites"

#: includes/admin/menu-pages/booking-rules-menu-page.php:335
#: includes/admin/menu-pages/booking-rules-menu-page.php:346
msgid "Maximum stay"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:336
msgid "There are no maximum stay rules."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:381
msgid "Block accommodation"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:382
msgid "There are no blocking accommodation rules."
msgstr "Não há regras de bloqueio de acomodação."

#: includes/admin/menu-pages/booking-rules-menu-page.php:414
#: includes/bookings-calendar.php:723
#: includes/bookings-calendar.php:817
msgid "From"
msgstr "De"

#: includes/admin/menu-pages/booking-rules-menu-page.php:424
msgid "Till"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:434
msgid "Restriction"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:436
msgid "Not check-in rule marks the date as unavailable for check-in."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:437
msgid "Not check-out rule marks the date as unavailable for check-out."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:438
msgid "Not stay-in rule displays the date as blocked. This date is unavailable for check-in and check-out on the next date."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:439
msgid "Not stay-in with Not check-out rules completely block the selected date, additionally displaying the previous date as unavailable for check-in."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:444
#: includes/script-managers/public-script-manager.php:208
msgid "Not check-in"
msgstr "Não checkin"

#: includes/admin/menu-pages/booking-rules-menu-page.php:445
#: includes/script-managers/public-script-manager.php:209
msgid "Not check-out"
msgstr "Não checkout"

#: includes/admin/menu-pages/booking-rules-menu-page.php:446
#: includes/script-managers/public-script-manager.php:207
msgid "Not stay-in"
msgstr "Não ficar"

#: includes/admin/menu-pages/booking-rules-menu-page.php:454
msgid "Comment"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:466
#: includes/admin/menu-pages/booking-rules-menu-page.php:477
msgid "Minimum advance reservation"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:467
msgid "There are no minimum advance reservation rules."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:512
#: includes/admin/menu-pages/booking-rules-menu-page.php:523
msgid "Maximum advance reservation"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:513
msgid "There are no maximum advance reservation rules."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:558
#: includes/admin/menu-pages/booking-rules-menu-page.php:569
msgid "Booking buffer"
msgstr "Regras de Reservas"

#: includes/admin/menu-pages/booking-rules-menu-page.php:559
msgid "There are no booking buffer rules."
msgstr ""

#: includes/admin/menu-pages/calendar-menu-page.php:41
#: includes/admin/menu-pages/calendar-menu-page.php:69
msgid "Booking Calendar"
msgstr "Calendário de Reserva"

#: includes/admin/menu-pages/calendar-menu-page.php:65
msgid "Calendar"
msgstr "Calendário"

#: includes/admin/menu-pages/create-booking-menu-page.php:135
#: includes/admin/menu-pages/create-booking-menu-page.php:169
#: includes/post-types/booking-cpt.php:244
msgid "Add New Booking"
msgstr "Adicionar Nova Reserva"

#: includes/admin/menu-pages/create-booking-menu-page.php:136
msgid "Clear Search Results"
msgstr "Limpar os Resultados da Pesquisa"

#: includes/admin/menu-pages/create-booking-menu-page.php:184
#: includes/admin/menu-pages/edit-booking-menu-page.php:69
msgid "Note: booking rules are disabled in the plugin settings and are not taken into account."
msgstr "Nota: as regras de reserva estão desativadas nas configurações do plugin e não são levadas em conta."

#: includes/admin/menu-pages/create-booking/booking-step.php:50
#: includes/shortcodes/checkout-shortcode/step-booking.php:478
msgid "Unable to create booking. Please try again."
msgstr "Não foi possível criar a reserva. Por favor, tente novamente."

#: includes/admin/menu-pages/create-booking/booking-step.php:74
#: includes/shortcodes/checkout-shortcode/step-booking.php:107
msgid "Booking is blocked due to maintenance reason. Please try again later."
msgstr "A reserva foi bloqueada devido a manutenção. Por favor, tente novamente mais tarde."

#: includes/admin/menu-pages/create-booking/booking-step.php:121
#: includes/admin/menu-pages/create-booking/booking-step.php:275
#: includes/admin/menu-pages/create-booking/checkout-step.php:130
#: includes/admin/menu-pages/edit-booking/booking-control.php:34
#: includes/admin/menu-pages/edit-booking/checkout-control.php:61
#: includes/admin/menu-pages/edit-booking/summary-control.php:56
#: includes/shortcodes/checkout-shortcode/step-booking.php:183
#: includes/shortcodes/checkout-shortcode/step-booking.php:363
#: includes/shortcodes/checkout-shortcode/step-checkout.php:170
#: includes/utils/parse-utils.php:250
msgid "There are no accommodations selected for reservation."
msgstr "Não há acomodações selecionadas para as reservas."

#: includes/admin/menu-pages/create-booking/booking-step.php:123
#: includes/admin/menu-pages/create-booking/booking-step.php:155
#: includes/admin/menu-pages/create-booking/checkout-step.php:132
#: includes/admin/menu-pages/create-booking/checkout-step.php:165
#: includes/admin/menu-pages/create-booking/checkout-step.php:196
#: includes/utils/parse-utils.php:210
#: includes/utils/parse-utils.php:285
#: includes/utils/parse-utils.php:305
msgid "Selected accommodations are not valid."
msgstr "As acomodações selecionadas são inválidas."

#: includes/admin/menu-pages/create-booking/booking-step.php:150
#: includes/admin/menu-pages/create-booking/checkout-step.php:160
#: includes/admin/menu-pages/create-booking/step.php:191
#: includes/ajax.php:612
#: includes/shortcodes/checkout-shortcode/step-booking.php:200
#: includes/shortcodes/checkout-shortcode/step-booking.php:207
#: includes/shortcodes/checkout-shortcode/step-checkout.php:187
#: includes/shortcodes/checkout-shortcode/step-checkout.php:199
#: includes/utils/parse-utils.php:301
msgid "Accommodation Type is not valid."
msgstr "O tipo de acomodação não está correto."

#: includes/admin/menu-pages/create-booking/booking-step.php:160
#: includes/admin/menu-pages/create-booking/booking-step.php:184
#: includes/ajax.php:623
#: includes/shortcodes/checkout-shortcode/step-booking.php:213
#: includes/shortcodes/checkout-shortcode/step-booking.php:231
#: includes/utils/parse-utils.php:322
msgid "Rate is not valid."
msgstr "A tarifa não é válida."

#: includes/admin/menu-pages/create-booking/booking-step.php:189
#: includes/admin/menu-pages/create-booking/step.php:211
#: includes/admin/menu-pages/create-booking/step.php:215
#: includes/shortcodes/checkout-shortcode/step-booking.php:237
#: includes/shortcodes/search-results-shortcode.php:634
#: includes/utils/parse-utils.php:163
#: includes/utils/parse-utils.php:326
msgid "Adults number is not valid."
msgstr "O número de adultos não está válido."

#: includes/admin/menu-pages/create-booking/booking-step.php:194
#: includes/admin/menu-pages/create-booking/step.php:235
#: includes/admin/menu-pages/create-booking/step.php:239
#: includes/ajax.php:500
#: includes/shortcodes/checkout-shortcode/step-booking.php:243
#: includes/shortcodes/search-results-shortcode.php:650
#: includes/utils/parse-utils.php:187
#: includes/utils/parse-utils.php:330
msgid "Children number is not valid."
msgstr "O número de crianças não está válido."

#: includes/admin/menu-pages/create-booking/booking-step.php:199
#: includes/ajax.php:634
#: includes/shortcodes/checkout-shortcode/step-booking.php:248
#: includes/utils/parse-utils.php:334
msgid "The total number of guests is not valid."
msgstr "O número total de convidados não é válido."

#: includes/admin/menu-pages/create-booking/booking-step.php:210
#: includes/admin/menu-pages/create-booking/checkout-step.php:181
#: includes/shortcodes/checkout-shortcode/step-booking.php:259
#: includes/shortcodes/checkout-shortcode/step-checkout.php:245
#: includes/utils/parse-utils.php:345
msgid "Selected dates do not meet booking rules for type %s"
msgstr "As datas selecionadas estão em desacordo com as regras de reserva para o tipo %s"

#: includes/admin/menu-pages/create-booking/booking-step.php:263
#: includes/admin/menu-pages/create-booking/checkout-step.php:186
#: includes/utils/parse-utils.php:264
msgid "Accommodations are not available."
msgstr "Acomodações indisponíveis."

#: includes/admin/menu-pages/create-booking/checkout-step.php:170
#: includes/shortcodes/checkout-shortcode/step-checkout.php:234
msgid "There are no rates for requested dates."
msgstr "Não há taxas para as datas solicitadas."

#: includes/admin/menu-pages/create-booking/results-step.php:211
#: includes/admin/menu-pages/settings-menu-page.php:542
#: includes/wizard.php:93
msgid "Search Results"
msgstr "Resultados da Busca"

#: includes/admin/menu-pages/create-booking/search-step.php:47
#: includes/admin/menu-pages/create-booking/search-step.php:50
msgid "— Any —"
msgstr "— Qualquer —"

#: includes/admin/menu-pages/create-booking/step.php:34
msgid "Search parameters are not set."
msgstr "Parâmetros de pesquisa não estão definidos."

#: includes/admin/menu-pages/create-booking/step.php:129
#: includes/ajax.php:438
#: includes/script-managers/public-script-manager.php:223
#: includes/shortcodes/checkout-shortcode/step.php:53
#: includes/shortcodes/search-results-shortcode.php:665
#: includes/utils/parse-utils.php:87
msgid "Check-in date is not valid."
msgstr "A data de Check-in não é válido."

#: includes/admin/menu-pages/create-booking/step.php:131
#: includes/shortcodes/checkout-shortcode/step.php:56
#: includes/shortcodes/search-results-shortcode.php:668
#: includes/utils/parse-utils.php:89
msgid "Check-in date cannot be earlier than today."
msgstr "A data de Checkin não deve ser antes da data de hoje."

#: includes/admin/menu-pages/create-booking/step.php:157
#: includes/ajax.php:457
#: includes/script-managers/public-script-manager.php:224
#: includes/shortcodes/checkout-shortcode/step.php:90
#: includes/shortcodes/search-results-shortcode.php:686
#: includes/utils/parse-utils.php:120
msgid "Check-out date is not valid."
msgstr "A data de checkout não está correta."

#: includes/admin/menu-pages/create-booking/step.php:168
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:106
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:210
#: includes/shortcodes/checkout-shortcode/step.php:101
#: includes/shortcodes/search-results-shortcode.php:698
#: includes/utils/parse-utils.php:131
msgid "Nothing found. Please try again with different search parameters."
msgstr "Nada foi encontrado. Tente novamente com parâmetros de pesquisa diferentes."

#: includes/admin/menu-pages/customers-menu-page.php:54
msgid "Sorry, you are not allowed to access this page."
msgstr "Desculpe, você não tem permissão para acessar esta página."

#: includes/admin/menu-pages/customers-menu-page.php:160
msgid "User ID"
msgstr "ID do usuário"

#: includes/admin/menu-pages/customers-menu-page.php:171
#: templates/account/account-details.php:30
msgid "Username"
msgstr "Nome de usuário"

#: includes/admin/menu-pages/customers-menu-page.php:183
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:114
#: includes/bundles/customer-bundle.php:92
#: includes/csv/bookings/bookings-exporter-helper.php:81
#: includes/post-types/booking-cpt.php:90
#: includes/post-types/payment-cpt.php:247
#: includes/views/shortcodes/checkout-view.php:588
#: templates/account/account-details.php:22
msgid "First Name"
msgstr "Nome"

#: includes/admin/menu-pages/customers-menu-page.php:195
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:118
#: includes/bundles/customer-bundle.php:101
#: includes/csv/bookings/bookings-exporter-helper.php:82
#: includes/post-types/booking-cpt.php:98
#: includes/post-types/payment-cpt.php:255
#: includes/views/shortcodes/checkout-view.php:603
#: templates/account/account-details.php:26
msgid "Last Name"
msgstr "Sobrenome"

#: includes/admin/menu-pages/customers-menu-page.php:219
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:126
#: includes/bundles/customer-bundle.php:119
#: includes/csv/bookings/bookings-exporter-helper.php:84
#: includes/post-types/booking-cpt.php:114
#: includes/post-types/payment-cpt.php:271
#: includes/views/shortcodes/checkout-view.php:633
#: templates/account/account-details.php:38
msgid "Phone"
msgstr "Telefone"

#: includes/admin/menu-pages/customers-menu-page.php:231
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:134
#: includes/bundles/customer-bundle.php:137
#: includes/csv/bookings/bookings-exporter-helper.php:86
#: includes/post-types/booking-cpt.php:131
#: includes/views/shortcodes/checkout-view.php:675
#: templates/account/account-details.php:42
msgid "Address"
msgstr "Endereço"

#: includes/admin/menu-pages/customers-menu-page.php:243
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:142
#: includes/bundles/customer-bundle.php:155
#: includes/csv/bookings/bookings-exporter-helper.php:88
#: includes/post-types/booking-cpt.php:147
#: includes/post-types/payment-cpt.php:311
#: includes/views/shortcodes/checkout-view.php:705
#: templates/account/account-details.php:64
msgid "State / County"
msgstr "Estado"

#: includes/admin/menu-pages/customers-menu-page.php:255
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:130
#: includes/csv/bookings/bookings-exporter-helper.php:85
#: includes/post-types/booking-cpt.php:123
#: includes/post-types/payment-cpt.php:279
#: templates/account/account-details.php:46
msgid "Country"
msgstr "País"

#: includes/admin/menu-pages/customers-menu-page.php:268
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:138
#: includes/bundles/customer-bundle.php:146
#: includes/csv/bookings/bookings-exporter-helper.php:87
#: includes/post-types/booking-cpt.php:139
#: includes/post-types/payment-cpt.php:303
#: includes/views/shortcodes/checkout-view.php:690
#: templates/account/account-details.php:68
msgid "City"
msgstr "Cidade"

#: includes/admin/menu-pages/customers-menu-page.php:280
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:146
#: includes/bundles/customer-bundle.php:164
#: includes/csv/bookings/bookings-exporter-helper.php:89
#: includes/post-types/booking-cpt.php:155
#: includes/views/shortcodes/checkout-view.php:720
#: templates/account/account-details.php:72
msgid "Postcode"
msgstr "Código Postal"

#: includes/admin/menu-pages/customers-menu-page.php:301
#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/i-cal-import-menu-page.php:162
#: includes/admin/menu-pages/i-cal-import-menu-page.php:209
#: includes/admin/menu-pages/i-cal-menu-page.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:40
#: includes/post-types/editable-cpt.php:95
msgid "Back"
msgstr "Retornar"

#: includes/admin/menu-pages/customers-menu-page.php:305
msgid "Edit User Profile"
msgstr "Editar Perfil do Usuário"

#: includes/admin/menu-pages/customers-menu-page.php:322
msgid "Customer data updated."
msgstr "Dados do cliente atualizados."

#: includes/admin/menu-pages/customers-menu-page.php:328
msgid "User account updated."
msgstr "Conta de usuário atualizada."

#: includes/admin/menu-pages/customers-menu-page.php:363
#: includes/admin/menu-pages/i-cal-menu-page.php:150
msgid "Update"
msgstr "Atuzalizar"

#: includes/admin/menu-pages/customers-menu-page.php:369
#: includes/admin/menu-pages/customers-menu-page.php:382
#: includes/admin/menu-pages/customers-menu-page.php:386
msgid "Customers"
msgstr "Clientes"

#: includes/admin/menu-pages/edit-booking-menu-page.php:80
msgid "The booking is not set."
msgstr "A reserva não está definida."

#: includes/admin/menu-pages/edit-booking-menu-page.php:88
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:49
msgid "The booking not found."
msgstr "Reserva não encontrada."

#: includes/admin/menu-pages/edit-booking-menu-page.php:140
msgid "Edit Booking #%d"
msgstr "Editar reserva #%d"

#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/reports-menu-page.php:201
msgid "Cancel"
msgstr "Cancelar"

#: includes/admin/menu-pages/edit-booking-menu-page.php:227
#: includes/admin/menu-pages/edit-booking-menu-page.php:234
#: includes/post-types/booking-cpt.php:245
msgid "Edit Booking"
msgstr "Editar Reserva"

#: includes/admin/menu-pages/edit-booking/booking-control.php:18
#: includes/admin/menu-pages/edit-booking/checkout-control.php:35
#: includes/admin/menu-pages/edit-booking/edit-control.php:56
#: includes/admin/menu-pages/edit-booking/summary-control.php:31
msgid "You cannot edit the imported booking. Please update the source booking and resync your calendars."
msgstr "Você não pode editar a reserva importada. Atualize a reserva de origem e sincronize novamente seus calendários."

#: includes/admin/menu-pages/edit-booking/booking-control.php:22
#: includes/ajax-api/ajax-actions/abstract-ajax-api-action.php:142
#: includes/ajax.php:184
msgid "Request does not pass security verification. Please refresh the page and try one more time."
msgstr "Esta solicitação não passou pela verificação de segurança. Atualize a página e tente novamente."

#: includes/admin/menu-pages/edit-booking/booking-control.php:26
#: includes/admin/menu-pages/edit-booking/checkout-control.php:40
#: includes/admin/menu-pages/edit-booking/summary-control.php:33
#: includes/utils/parse-utils.php:233
msgid "Check-in date is not set."
msgstr "A data de check-in não está definida."

#: includes/admin/menu-pages/edit-booking/booking-control.php:30
#: includes/admin/menu-pages/edit-booking/checkout-control.php:42
#: includes/admin/menu-pages/edit-booking/summary-control.php:35
#: includes/utils/parse-utils.php:235
msgid "Check-out date is not set."
msgstr "A data de check-out não está definida."

#: includes/admin/menu-pages/edit-booking/booking-control.php:72
msgid "Unable to update booking. Please try again."
msgstr "Não foi possível atualizar a reserva. Por favor, tente novamente."

#: includes/admin/menu-pages/edit-booking/booking-control.php:75
msgid "Booking was edited."
msgstr "A reserva foi editada."

#: includes/admin/menu-pages/edit-booking/edit-control.php:94
#: includes/script-managers/public-script-manager.php:203
#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Available"
msgstr "Disponível"

#: includes/admin/menu-pages/edit-booking/edit-control.php:96
#: templates/edit-booking/edit-reserved-rooms.php:71
msgid "Replace"
msgstr "Substituir"

#: includes/admin/menu-pages/edit-booking/summary-control.php:148
msgid "— Add new —"
msgstr "— Adicionar novo —"

#: includes/admin/menu-pages/extensions-menu-page.php:137
#: includes/admin/menu-pages/extensions-menu-page.php:185
#: includes/admin/menu-pages/extensions-menu-page.php:190
#: includes/admin/menu-pages/settings-menu-page.php:1192
msgid "Extensions"
msgstr "Extensões"

#: includes/admin/menu-pages/extensions-menu-page.php:140
msgid "Extend the functionality of Hotel Booking plugin with the number of helpful addons for your custom purposes."
msgstr "Estenda a funcionalidade do plugin Hotel Booking com os addons mais úteis para os seus objetivos."

#: includes/admin/menu-pages/extensions-menu-page.php:170
msgid "Get this Extension"
msgstr "Obter essa extensão"

#: includes/admin/menu-pages/extensions-menu-page.php:178
msgid "No extensions found."
msgstr ""

#: includes/admin/menu-pages/i-cal-import-menu-page.php:80
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:60
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:102
#: includes/i-cal/logs-handler.php:73
msgid "Abort Process"
msgstr "Abortar Processo"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:81
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:61
msgid "Aborting..."
msgstr "Abortar..."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:161
#: includes/admin/menu-pages/i-cal-import-menu-page.php:210
#: includes/admin/menu-pages/i-cal-import-menu-page.php:224
#: includes/admin/room-list-table.php:156
msgid "Import Calendar"
msgstr "Importar Calendario"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:166
msgid "Accommodation: %s"
msgstr "Acomodação: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:169
msgid "Accommodation Type: %s"
msgstr "Tipo de Acomodação: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:176
msgid "Please be patient while the calendars are imported. You will be notified via this page when the process is completed."
msgstr "Por favor, seja paciente enquando os calendários estão sendo importados. Você será notificado quando o processo terminar."

#: includes/admin/menu-pages/i-cal-menu-page.php:67
msgid "Accommodation updated."
msgstr "Acomodação atualizada.."

#: includes/admin/menu-pages/i-cal-menu-page.php:73
msgid "This calendar has already been imported for another accommodation."
msgstr ""

#: includes/admin/menu-pages/i-cal-menu-page.php:103
msgid "Sync, Import and Export Calendars"
msgstr "Sincronizar, Importar e Exportar Calendários"

#. translators: %s - room name. Example: "Comfort Triple 1"
#: includes/admin/menu-pages/i-cal-menu-page.php:113
msgid "Edit External Calendars of \"%s\""
msgstr ""

#: includes/admin/menu-pages/i-cal-menu-page.php:122
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:101
msgid "Sync All External Calendars"
msgstr "Sincronizar Todos os Calendários Externos"

#: includes/admin/menu-pages/i-cal-menu-page.php:123
msgid "Sync your bookings across all online channels like Booking.com, TripAdvisor, Airbnb etc. via iCalendar file format."
msgstr "Sincronizar em todos os canais as reservas online como o Booking.com, TripAdvisor, Airbnb etc. Com o formado de arquivo iCalendar."

#: includes/admin/menu-pages/i-cal-menu-page.php:219
msgid "Calendar URL"
msgstr "URL do Calendário"

#: includes/admin/menu-pages/i-cal-menu-page.php:225
msgid "Add New Calendar"
msgstr "Adicionar Novo Calendário"

#: includes/admin/menu-pages/i-cal-menu-page.php:233
#: includes/admin/menu-pages/i-cal-menu-page.php:237
msgid "Sync Calendars"
msgstr "Sincronizar Calendários"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:62
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:103
#: includes/i-cal/logs-handler.php:83
msgid "Delete All Logs"
msgstr "Deletar Todos os Logs"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:63
msgid "Deleting..."
msgstr "Deletando..."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:64
msgid "%d item"
msgstr ""

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:65
msgid "%d items"
msgstr "%d itens"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:91
msgid "Calendars Synchronization Status"
msgstr "Estado da Sincronização dos Calendários"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:92
msgid "Here you can see synchronization status of your external calendars."
msgstr "Aqui é possível ver o estado de sincronização de seus calendários externos."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:134
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:148
msgid "Calendars Sync Status"
msgstr "Estado da Sincronização dos Calendários"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:151
msgid "Display calendars synchronization status."
msgstr "Mostrar o estado da sincronização dos calendários."

#: includes/admin/menu-pages/language-menu-page.php:10
#: includes/admin/menu-pages/language-menu-page.php:37
msgid "Language Guide"
msgstr "Guia de Idiomas"

#: includes/admin/menu-pages/language-menu-page.php:11
msgid "Default language"
msgstr "Idioma Padrão"

#: includes/admin/menu-pages/language-menu-page.php:13
msgid "This plugin will display all system messages, labels, buttons in the language set in <em>General > Settings > Site Language</em>. If the plugin is not available in your language, you may <a href=\"%s\">contribute your translation</a>."
msgstr "Este plugin exibirá todas as mensagens do sistema, rótulos, botões no idioma definido em <em>Geral > Configurações > Idioma do Site</em>. Se o plugin não estiver disponível no seu idioma, é possível <a href=\"%s\">Contribuir com a sua tradução</a>."

#: includes/admin/menu-pages/language-menu-page.php:14
msgid "Custom translations and edits"
msgstr "Traduções personalizadas e edições"

#: includes/admin/menu-pages/language-menu-page.php:15
msgid "You may customize plugin translation by editing the needed texts or adding your translation following these steps:"
msgstr "É possível personalizar a tradução de plugins editando os textos necessários ou adicionando sua tradução seguindo estas etapas:"

#: includes/admin/menu-pages/language-menu-page.php:17
msgid "Take the source file for your translations %s or needed translated locale."
msgstr "Coloque o arquivo de origem para suas traduções %s ou o local necessário."

#: includes/admin/menu-pages/language-menu-page.php:18
msgid "Translate texts with any translation program like Poedit, Loco, Pootle etc."
msgstr "Traduza textos com qualquer programa de tradução como o Poedit, Loco, Pootle etc."

#: includes/admin/menu-pages/language-menu-page.php:19
msgid "Put created .mo file with your translations into the folder %s. Where {lang} is ISO-639 language code and {country} is ISO-3166 country code. Example: Brazilian Portuguese file would be called motopress-hotel-booking-pt_BR.mo."
msgstr "Coloque o arquivo .mo criado com suas traduções na pasta %s. Onde {lang} é o código de idioma ISO-639 e {country} é o código de país ISO-3166. Exemplo: o arquivo Português Brasileiro seria chamado motopress-hotel-booking-pt_BR.mo."

#: includes/admin/menu-pages/language-menu-page.php:22
msgid "Multilingual content"
msgstr "Conteúdo multilíngue"

#: includes/admin/menu-pages/language-menu-page.php:23
msgid "If your site is multilingual, you may use additional plugins to translate your added content into multiple languages allowing the site visitors to switch them."
msgstr "Se seu site for multilíngue, é possível usar plugins adicionais para traduzir o conteúdo em vários idiomas, permitindo que os visitantes do site alterem o idioma."

#: includes/admin/menu-pages/language-menu-page.php:33
msgid "Language"
msgstr "Idioma"

#: includes/admin/menu-pages/reports-menu-page.php:52
#: includes/admin/menu-pages/reports-menu-page.php:211
#: includes/admin/menu-pages/reports-menu-page.php:215
msgid "Reports"
msgstr "Relatórios"

#: includes/admin/menu-pages/reports-menu-page.php:55
#: includes/admin/room-list-table.php:94
msgid "Export"
msgstr "Exportar"

#: includes/admin/menu-pages/reports-menu-page.php:132
#: includes/bookings-calendar.php:695
msgid "All Statuses"
msgstr ""

#: includes/admin/menu-pages/reports-menu-page.php:138
msgid "Booking dates between"
msgstr ""

#: includes/admin/menu-pages/reports-menu-page.php:139
msgid "Check-in date between"
msgstr "Data de check-in entre"

#: includes/admin/menu-pages/reports-menu-page.php:140
msgid "Check-out date between"
msgstr "Data de check-out entre"

#: includes/admin/menu-pages/reports-menu-page.php:141
msgid "In-house between"
msgstr ""

#: includes/admin/menu-pages/reports-menu-page.php:142
msgid "Date of reservation between"
msgstr "Data da reserva entre"

#: includes/admin/menu-pages/reports-menu-page.php:152
msgid "Export Bookings"
msgstr "Exportar Reservas"

#: includes/admin/menu-pages/reports-menu-page.php:164
msgid "Choose start date"
msgstr "Escolha a data de início"

#: includes/admin/menu-pages/reports-menu-page.php:165
msgid "Choose end date"
msgstr "Escolha a data final"

#: includes/admin/menu-pages/reports-menu-page.php:171
msgid "Also export imported bookings"
msgstr ""

#: includes/admin/menu-pages/reports-menu-page.php:175
msgid "Select columns to export"
msgstr "Selecionar colunas para exportar"

#: includes/admin/menu-pages/reports-menu-page.php:185
msgid "Generate CSV"
msgstr "Gerar Arquivo CSV"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:25
msgid "Number of accommodations"
msgstr "Número de reserva"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:57
#: includes/payments/gateways/gateway.php:494
#: includes/widgets/rooms-widget.php:185
#: templates/create-booking/results/reserve-rooms.php:35
#: assets/blocks/blocks.js:436
#: assets/blocks/blocks.js:686
#: assets/blocks/blocks.js:1215
msgid "Title"
msgstr "Titulo"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:61
msgid "Leave empty to use accommodation type title."
msgstr "Deixe em branco para usar o título do tipo de acomodação."

#: includes/admin/menu-pages/rooms-generator-menu-page.php:66
msgid "Generate"
msgstr "Gerar"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:75
msgid "Accommodation generated."
msgid_plural "%s accommodations generated."
msgstr[0] "Acomodação criada."
msgstr[1] "%s acomodações criadas."

#: includes/admin/menu-pages/settings-menu-page.php:113
msgid "General"
msgstr "Geral"

#: includes/admin/menu-pages/settings-menu-page.php:116
msgid "Pages"
msgstr "Páginas"

#: includes/admin/menu-pages/settings-menu-page.php:123
msgid "Search Results Page"
msgstr "Buscar Resultados de Páginas"

#: includes/admin/menu-pages/settings-menu-page.php:124
msgid "Select page to display search results. Use search results shortcode on this page."
msgstr "Selecione a página para exibir os resultados da busca. Use o shortcode do resultados da busca nesta página."

#: includes/admin/menu-pages/settings-menu-page.php:132
msgid "Checkout Page"
msgstr "Página de Checkout"

#: includes/admin/menu-pages/settings-menu-page.php:133
msgid "Select page user will be redirected to complete booking."
msgstr "Selecione a página que o usuário será redirecionado para concluir a reserva."

#: includes/admin/menu-pages/settings-menu-page.php:141
msgid "Terms & Conditions"
msgstr "Termos e Condições"

#: includes/admin/menu-pages/settings-menu-page.php:142
msgid "If you define a \"Terms\" page the customer will be asked if they accept them when checking out."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:150
msgid "Open the Terms & Conditions page in a new window"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:151
msgid "By enabling this option you can avoid errors related to displaying your terms & conditions inline for website pages created in page builders."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:159
msgid "My Account Page"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:160
msgid "Select a page to display user account. Use the customer account shortcode on this page."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:170
#: includes/admin/menu-pages/settings-menu-page.php:177
#: includes/post-types/payment-cpt.php:205
msgid "Currency"
msgstr "Moeda"

#: includes/admin/menu-pages/settings-menu-page.php:186
msgid "Currency Position"
msgstr "Posição da moeda"

#: includes/admin/menu-pages/settings-menu-page.php:195
msgid "Decimal Separator"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:204
msgid "Thousand Separator"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:214
msgid "Number of Decimals"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:226
msgid "Misc"
msgstr "Diversos"

#: includes/admin/menu-pages/settings-menu-page.php:233
msgid "Square Units"
msgstr "Unidades quadradas"

#: includes/admin/menu-pages/settings-menu-page.php:242
msgid "Datepicker Date Format"
msgstr "Formato da data Datepicker"

#: includes/admin/menu-pages/settings-menu-page.php:251
#: includes/emails/templaters/email-templater.php:148
msgid "Check-out Time"
msgstr "Hora de Check-out"

#: includes/admin/menu-pages/settings-menu-page.php:259
#: includes/emails/templaters/email-templater.php:144
msgid "Check-in Time"
msgstr "Hora de Check-in"

#: includes/admin/menu-pages/settings-menu-page.php:267
msgid "Bed Types"
msgstr "Tipos de Camas"

#: includes/admin/menu-pages/settings-menu-page.php:274
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:155
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:251
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:338
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:409
#: includes/post-types/attributes-cpt.php:365
#: includes/post-types/coupon-cpt.php:79
#: includes/post-types/coupon-cpt.php:111
#: includes/post-types/coupon-cpt.php:156
msgid "Type"
msgstr "Tipo"

#: includes/admin/menu-pages/settings-menu-page.php:279
msgid "Add Bed Type"
msgstr "Adicionar Tipo de Cama"

#: includes/admin/menu-pages/settings-menu-page.php:286
msgid "Show Lowest Price for"
msgstr "Mostrar Preço Mais Baixo Para"

#: includes/admin/menu-pages/settings-menu-page.php:287
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:185
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:281
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:367
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:438
msgid "days"
msgstr "dias"

#: includes/admin/menu-pages/settings-menu-page.php:291
msgid "Lowest price of accommodation for selected number of days if check-in and check-out dates are not set. Example: set 0 to display today's lowest price, set 7 to display the lowest price for the next week."
msgstr "O preço mais baixo de acomodação para um número seleccionado de dias, se as datas de check-in e check-out não estiverem definidas. Por exemplo: insira 0 para exibir o preço mais baixo de hoje, insira 7 para exibir o preço mais baixo para a próxima semana."

#: includes/admin/menu-pages/settings-menu-page.php:298
#: includes/post-types/coupon-cpt.php:288
msgid "Coupons"
msgstr "Cupons"

#: includes/admin/menu-pages/settings-menu-page.php:308
msgid "Default calendar view"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:309
msgid "Initial display format of the administrator bookings calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:317
msgid "Text on Checkout"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:318
msgid "This text will appear on the checkout page."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:329
msgid "Disable Booking"
msgstr "Desativar reserva"

#: includes/admin/menu-pages/settings-menu-page.php:336
msgid "Hide reservation forms and buttons"
msgstr "Ocultar formulários e botões de reserva"

#: includes/admin/menu-pages/settings-menu-page.php:345
msgid "Text instead of reservation form while booking is disabled"
msgstr "Texto ao invés do formulário de reserva durante a reserva foi desativado"

#: includes/admin/menu-pages/settings-menu-page.php:356
#: includes/admin/menu-pages/shortcodes-menu-page.php:510
#: includes/wizard.php:115
#: assets/blocks/blocks.js:1562
#: assets/blocks/blocks.js:1592
msgid "Booking Confirmation"
msgstr "Confirmação de reserva"

#: includes/admin/menu-pages/settings-menu-page.php:363
msgid "Confirmation Mode"
msgstr "Modo de confirmação"

#: includes/admin/menu-pages/settings-menu-page.php:365
msgid "By customer via email"
msgstr "Pelo cliente manualmente"

#: includes/admin/menu-pages/settings-menu-page.php:366
msgid "By admin manually"
msgstr "Pelo administrador manualmente"

#: includes/admin/menu-pages/settings-menu-page.php:367
msgid "Confirmation upon payment"
msgstr "Confirmação após o pagamento"

#: includes/admin/menu-pages/settings-menu-page.php:376
msgid "Booking Confirmed Page"
msgstr "Página Confirmada de Reserva"

#: includes/admin/menu-pages/settings-menu-page.php:377
msgid "Page user will be redirected to once the booking is confirmed via email or by admin."
msgstr "O usuário da página será redirecionado assim que a reserva for confirmada por e-mail ou pelo administrador."

#: includes/admin/menu-pages/settings-menu-page.php:385
msgid "Approval Time for User"
msgstr "Tempo de Aprovação Para o Usuário"

#: includes/admin/menu-pages/settings-menu-page.php:386
msgid "Period of time in minutes the user is given to confirm booking via email. Unconfirmed bookings become Abandoned and accommodation status changes to Available."
msgstr "Período de tempo em minutos que é dado ao usuário para confirmar a reserva por e-mail. As reservas não confirmadas são canceladas e alteradas para disponíveis."

#: includes/admin/menu-pages/settings-menu-page.php:396
msgid "Country of residence field is required for reservation."
msgstr "O país de residência é obrigatório para a reserva."

#: includes/admin/menu-pages/settings-menu-page.php:404
msgid "Full address fields are required for reservation."
msgstr "O endereço completo é necessário para a reserva."

#: includes/admin/menu-pages/settings-menu-page.php:412
msgid "Customer information is required when placing admin bookings."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:421
msgid "Default Country on Checkout"
msgstr "País Padrão no Checkout"

#: includes/admin/menu-pages/settings-menu-page.php:429
#: includes/emails/templaters/email-templater.php:199
#: includes/post-types/booking-cpt.php:194
#: includes/views/shortcodes/checkout-view.php:477
msgid "Price Breakdown"
msgstr "Detalhamento do preço"

#: includes/admin/menu-pages/settings-menu-page.php:430
msgid "Price breakdown unfolded by default."
msgstr "Manter o detalhamento aberto na página de reserva."

#: includes/admin/menu-pages/settings-menu-page.php:439
msgid "Accounts"
msgstr "Contas"

#: includes/admin/menu-pages/settings-menu-page.php:446
msgid "Account creation"
msgstr "Criação de conta"

#: includes/admin/menu-pages/settings-menu-page.php:447
msgid "Automatically create an account for a user at checkout."
msgstr "Criar conta de usuário no processo de reserva."

#: includes/admin/menu-pages/settings-menu-page.php:455
msgid "Allow customers to create an account during checkout."
msgstr "Permitir que os clientes criem uma conta durante o processo de reserva."

#: includes/admin/menu-pages/settings-menu-page.php:463
msgid "Allow customers to log into their existing account during checkout."
msgstr "Permitir que os clientes façam login em sua conta existente durante o processo de reserva."

#: includes/admin/menu-pages/settings-menu-page.php:472
#: includes/upgrader.php:751
#: includes/wizard.php:164
msgid "Booking Cancellation"
msgstr "Cancelamento da Reserva"

#: includes/admin/menu-pages/settings-menu-page.php:479
msgid "User can cancel booking via link provided inside email."
msgstr "O usuário pode cancelar a reserva através do link fornecido no e-mail."

#: includes/admin/menu-pages/settings-menu-page.php:487
msgid "Booking Cancelation Page"
msgstr "Página de cancelamento de reserva"

#: includes/admin/menu-pages/settings-menu-page.php:488
msgid "Page to confirm booking cancelation."
msgstr "Página para confirmar o cancelamento da reserva."

#: includes/admin/menu-pages/settings-menu-page.php:496
msgid "Booking Canceled Page"
msgstr "Página de Cancelamento de Reserva"

#: includes/admin/menu-pages/settings-menu-page.php:497
msgid "Page to redirect to after a booking is canceled."
msgstr "Página a ser mostrada quando a reserva for cancelada."

#: includes/admin/menu-pages/settings-menu-page.php:506
msgid "Search Options"
msgstr "Opções de Busca"

#: includes/admin/menu-pages/settings-menu-page.php:515
msgid "Max Adults"
msgstr "Máximo de Adultos"

#: includes/admin/menu-pages/settings-menu-page.php:516
msgid "Maximum accommodation occupancy available in the Search Form."
msgstr "Máximo de lugares de acomodação disponíveis no formulário de buca."

#: includes/admin/menu-pages/settings-menu-page.php:526
msgid "Max Children"
msgstr "Máximo de Crianças"

#: includes/admin/menu-pages/settings-menu-page.php:534
msgid "Age of Child"
msgstr "Idade da Criança"

#: includes/admin/menu-pages/settings-menu-page.php:535
msgid "Optional description of the \"Children\" field."
msgstr "Descrição opcional do campo \"Crianças\"."

#: includes/admin/menu-pages/settings-menu-page.php:543
msgid "Limit search results based on the requested number of guests."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:551
msgid "Book button behavior on the search results page"
msgstr "Comportamento do botão Reservar na página de resultados da pesquisa"

#: includes/admin/menu-pages/settings-menu-page.php:552
msgid "Redirect to the checkout page immediately after successful addition to reservation."
msgstr "Redirecione para a página de checkout imediatamente após a adição bem-sucedida à reserva."

#: includes/admin/menu-pages/settings-menu-page.php:560
msgid "Recommendation"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:561
msgid "Enable search form to recommend the best set of accommodations according to a number of guests."
msgstr "Habilite o formulário de busca para recomendar o melhor conjunto de acomodações de acordo com um número de hóspedes."

#: includes/admin/menu-pages/settings-menu-page.php:570
msgid "Skip Search Results"
msgstr "Ignorar Resultados da Pesquisa"

#: includes/admin/menu-pages/settings-menu-page.php:571
msgid "Skip search results page and enable direct booking from accommodation pages."
msgstr "Ignore a página de resultados de pesquisa e habilite a reserva direta nas páginas de acomodações."

#: includes/admin/menu-pages/settings-menu-page.php:578
msgid "Direct Booking Form"
msgstr "Formulario directo de reserva"

#: includes/admin/menu-pages/settings-menu-page.php:581
msgid "Show price for selected period"
msgstr "mostrar preço para o periodo seleccionado"

#: includes/admin/menu-pages/settings-menu-page.php:582
msgid "Show price together with adults and children fields"
msgstr "Mostrar preço juntamente com campos de adultos e crianças"

#: includes/admin/menu-pages/settings-menu-page.php:592
msgid "Enable \"adults\" and \"children\" options for my website (default)."
msgstr "Ativar opções \"adultos\" e \"crianças\" no meu site (padrão)."

#: includes/admin/menu-pages/settings-menu-page.php:593
msgid "Disable \"children\" option for my website (hide \"children\" field and use Guests label instead)."
msgstr "Desative a opção \"crianças\" no meu site (oculte o campo \"crianças\" e use o marcador Convidados)."

#: includes/admin/menu-pages/settings-menu-page.php:594
msgid "Disable \"adults\" and \"children\" options for my website."
msgstr "Desativar opções de \"adultos\" e \"crianças\" do meu site."

#: includes/admin/menu-pages/settings-menu-page.php:597
msgid "Guest Management"
msgstr "Gerenciamento de Hóspedes"

#: includes/admin/menu-pages/settings-menu-page.php:598
msgid "Applies to frontend only."
msgstr "Aplica-se apenas à interface pública."

#: includes/admin/menu-pages/settings-menu-page.php:606
msgid "Hide \"adults\" and \"children\" fields within search availability forms."
msgstr "Ocultar campos \"adultos\" e \"crianças\" dos formulários de pesquisa de disponibilidade."

#: includes/admin/menu-pages/settings-menu-page.php:614
msgid "Remember the user's selected number of guests until the checkout page."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:623
msgid "Do not apply booking rules for admin bookings."
msgstr "Não aplicar regras de reserva para as reservas de administradores."

#: includes/admin/menu-pages/settings-menu-page.php:631
msgid "Display Options"
msgstr "Opções de Exibição"

#: includes/admin/menu-pages/settings-menu-page.php:638
msgid "Display gallery images of accommodation page in lightbox."
msgstr "Exibir galeria de imagens da página de acomodação na caixa luminada."

#: includes/admin/menu-pages/settings-menu-page.php:645
#: includes/admin/menu-pages/shortcodes-menu-page.php:61
#: assets/blocks/blocks.js:250
#: assets/blocks/blocks.js:385
msgid "Availability Calendar"
msgstr "Calendario de disponibilidade"

#: includes/admin/menu-pages/settings-menu-page.php:646
#: includes/admin/menu-pages/shortcodes-menu-page.php:76
#: assets/blocks/blocks.js:307
msgid "Display per-night prices in the availability calendar."
msgstr "Exibir preços por noite no calendário de disponibilidade."

#: includes/admin/menu-pages/settings-menu-page.php:654
#: includes/admin/menu-pages/shortcodes-menu-page.php:82
#: assets/blocks/blocks.js:318
msgid "Truncate per-night prices in the availability calendar."
msgstr "Exibir preços por noite no calendário de disponibilidade."

#: includes/admin/menu-pages/settings-menu-page.php:662
#: includes/admin/menu-pages/shortcodes-menu-page.php:88
#: assets/blocks/blocks.js:329
msgid "Display the currency sign in the availability calendar."
msgstr "Exibir o símbolo da moeda no calendário de disponibilidade."

#: includes/admin/menu-pages/settings-menu-page.php:672
msgid "Calendar Theme"
msgstr "Tema do Calendário"

#: includes/admin/menu-pages/settings-menu-page.php:673
msgid "Select theme for an availability calendar."
msgstr "Selecione o tema para um calendário de disponibilidade."

#: includes/admin/menu-pages/settings-menu-page.php:680
msgid "Template Mode"
msgstr "Modo de Template"

#: includes/admin/menu-pages/settings-menu-page.php:682
msgid "Developer Mode"
msgstr "Modo de Desenvolvedor"

#: includes/admin/menu-pages/settings-menu-page.php:683
msgid "Theme Mode"
msgstr "Modo de Tema"

#: includes/admin/menu-pages/settings-menu-page.php:685
msgid "Choose Theme Mode to display the content with the styles of your theme. Choose Developer Mode to control appearance of the content with custom page templates, actions and filters. This option can't be changed if your theme is initially integrated with the plugin."
msgstr "Escolha o modo do tema para exibir o conteúdo com os estilos do seu tema. Escolha Modo de desenvolvedor para controlar a aparência do conteúdo com modelos de página personalizados, ações e filtros. Esta opção não poderá ser alterada se seu tema estiver inicialmente integrado com o plugin."

#: includes/admin/menu-pages/settings-menu-page.php:698
msgid "More Styles"
msgstr "Mais estilos"

#: includes/admin/menu-pages/settings-menu-page.php:699
msgid "Extend the styling options of Hotel Booking plugin with the new free addon - Hotel Booking Styles."
msgstr "Amplie as opções de estilo do plug-in Booking Hotel com o novo complemento gratuito - Styles Booking Hotels."

#: includes/admin/menu-pages/settings-menu-page.php:711
msgid "Calendars Synchronization"
msgstr "Sincronização de Calendários"

#: includes/admin/menu-pages/settings-menu-page.php:718
msgid "Export admin blocks."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:726
msgid "Do not export imported bookings."
msgstr "Não exportar reservas importadas."

#: includes/admin/menu-pages/settings-menu-page.php:734
msgid "Export and import bookings with buffer time included."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:742
msgid "Minimize Logs"
msgstr "Minimizar Logs"

#: includes/admin/menu-pages/settings-menu-page.php:743
msgid "Enable the plugin to record only important messages."
msgstr "Habilitar o plugin para gravar apenas mensagens importantes."

#: includes/admin/menu-pages/settings-menu-page.php:751
msgid "Calendars Synchronization Scheduler"
msgstr "Agendamento da Sincronização dos Calendários"

#: includes/admin/menu-pages/settings-menu-page.php:762
msgid "Enable automatic external calendars synchronization"
msgstr "Habilitar a sincronização automática de calendários externos"

#: includes/admin/menu-pages/settings-menu-page.php:771
msgid "Clock"
msgstr "Relógio"

#: includes/admin/menu-pages/settings-menu-page.php:772
msgid "Sync calendars at this time (UTC) or starting at this time every interval below."
msgstr "Sincronizar calendários nesse horário (UTC) ou iniciar nesse horário a cada intervalo abaixo."

#: includes/admin/menu-pages/settings-menu-page.php:783
msgid "Interval"
msgstr "Intervalo"

#: includes/admin/menu-pages/settings-menu-page.php:785
#: includes/crons/cron-manager.php:102
msgid "Quarter an Hour"
msgstr "15min"

#: includes/admin/menu-pages/settings-menu-page.php:786
#: includes/crons/cron-manager.php:107
msgid "Half an Hour"
msgstr "Meia hora"

#: includes/admin/menu-pages/settings-menu-page.php:787
msgid "Once Hourly"
msgstr "Uma vez por hora"

#: includes/admin/menu-pages/settings-menu-page.php:788
msgid "Twice Daily"
msgstr "Duas vezes ao dia"

#: includes/admin/menu-pages/settings-menu-page.php:789
msgid "Once Daily"
msgstr "Uma vez ao dia"

#: includes/admin/menu-pages/settings-menu-page.php:800
msgid "Automatically delete sync logs older than"
msgstr "Eliminar automaticamente os logs de sincronização mais antigos que"

#: includes/admin/menu-pages/settings-menu-page.php:802
msgid "Day"
msgstr "Dia"

#: includes/admin/menu-pages/settings-menu-page.php:803
msgid "Week"
msgstr "Semana"

#: includes/admin/menu-pages/settings-menu-page.php:804
#: includes/bookings-calendar.php:575
msgid "Month"
msgstr "Mês"

#: includes/admin/menu-pages/settings-menu-page.php:805
#: includes/bookings-calendar.php:576
msgid "Quarter"
msgstr "Trimestre"

#: includes/admin/menu-pages/settings-menu-page.php:806
msgid "Half a Year"
msgstr "Meio ano"

#: includes/admin/menu-pages/settings-menu-page.php:807
msgid "Never Delete"
msgstr "Nunca excluir"

#: includes/admin/menu-pages/settings-menu-page.php:817
msgid "Block Editor"
msgstr "Editor de blocos"

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/admin/menu-pages/settings-menu-page.php:832
msgid "Enable block editor for \"%s\"."
msgstr "Ativar o editor de blocos para \"%s\"."

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/post-types/coupon-cpt.php:59
#: includes/post-types/room-type-cpt.php:53
#: includes/post-types/room-type-cpt.php:64
#: includes/widgets/rooms-widget.php:21
msgid "Accommodation Types"
msgstr "Tipos de Acomodações"

#: includes/admin/menu-pages/settings-menu-page.php:832
#: includes/csv/bookings/bookings-exporter-helper.php:97
#: includes/emails/templaters/reserved-rooms-templater.php:183
#: includes/post-types/coupon-cpt.php:136
#: includes/post-types/service-cpt.php:91
#: includes/post-types/service-cpt.php:101
#: includes/views/booking-view.php:202
msgid "Services"
msgstr "Serviços"

#: includes/admin/menu-pages/settings-menu-page.php:863
msgid "Admin Emails"
msgstr "Emails de administração"

#: includes/admin/menu-pages/settings-menu-page.php:876
msgid "Customer Emails"
msgstr "E-mails do cliente"

#: includes/admin/menu-pages/settings-menu-page.php:881
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the <a>Hotel Booking & Mailchimp Integration</a>."
msgstr "Transforme convidados únicos em clientes fiéis enviando campanhas de marketing automáticas com a <a> Integração de reservas de hotéis e mailchimp </a>."

#: includes/admin/menu-pages/settings-menu-page.php:884
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the Hotel Booking & Mailchimp Integration."
msgstr "Transforme convidados únicos em clientes fiéis enviando campanhas de marketing automáticas com a Integração de Reservas de Hotel e Mailchimp."

#: includes/admin/menu-pages/settings-menu-page.php:903
msgid "Cancellation Details Template"
msgstr "Modelo de Detalhes do Cancelamento"

#: includes/admin/menu-pages/settings-menu-page.php:904
msgid "Used for %cancellation_details% tag."
msgstr "Usado para %cancellation_details% tag."

#: includes/admin/menu-pages/settings-menu-page.php:926
msgid "Email Settings"
msgstr "Configurações do E-mail"

#: includes/admin/menu-pages/settings-menu-page.php:931
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with <a>Hotel Booking Notifier</a>."
msgstr "Enviar notificações de e-mail automatizadas, como instruções de coleta de chaves, regras da casa, antes e depois da chegada/partida com o <a>Notificador do Hotel Booking</a>."

#: includes/admin/menu-pages/settings-menu-page.php:934
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with Hotel Booking Notifier."
msgstr "Enviar notificações de e-mail automatizadas, como instruções de coleta de chaves, regras da casa, antes e depois da chegada/partida com o Notificador do Hotel Booking."

#: includes/admin/menu-pages/settings-menu-page.php:942
msgid "Email Sender"
msgstr "Remetente do E-mail"

#: includes/admin/menu-pages/settings-menu-page.php:949
msgid "Administrator Email"
msgstr "E-mail do administrador"

#: includes/admin/menu-pages/settings-menu-page.php:958
msgid "From Email"
msgstr "De E-mail"

#: includes/admin/menu-pages/settings-menu-page.php:967
msgid "From Name"
msgstr "De nome"

#: includes/admin/menu-pages/settings-menu-page.php:977
msgid "Logo URL"
msgstr "Logotipo do URL"

#: includes/admin/menu-pages/settings-menu-page.php:987
msgid "Footer Text"
msgstr "Texto de Rodapé"

#: includes/admin/menu-pages/settings-menu-page.php:997
msgid "Reserved Accommodation Details Template"
msgstr "Template dos Detalhes da Acomodação reservada"

#: includes/admin/menu-pages/settings-menu-page.php:998
msgid "Used for %reserved_rooms_details% tag."
msgstr "Usado para %reserved_rooms_details% tag."

#: includes/admin/menu-pages/settings-menu-page.php:1011
msgid "Styles"
msgstr "Estilos"

#: includes/admin/menu-pages/settings-menu-page.php:1018
msgid "Base Color"
msgstr "Cor de Base"

#: includes/admin/menu-pages/settings-menu-page.php:1027
msgid "Background Color"
msgstr "Cor do Plano de Fundo"

#: includes/admin/menu-pages/settings-menu-page.php:1036
msgid "Body Background Color"
msgstr "Cor do Plano de Fundo do Corpo"

#: includes/admin/menu-pages/settings-menu-page.php:1045
msgid "Body Text Color"
msgstr "Cor do Corpo do Texto"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "Payment Gateways"
msgstr "Gateways de pagamento"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "General Settings"
msgstr "Configurações Gerais"

#: includes/admin/menu-pages/settings-menu-page.php:1071
msgid "Need more gateways? Use our Hotel Booking <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a> extension."
msgstr "Precisa de mais gateways? Use nossa extensão Hotel Booking <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a>."

#: includes/admin/menu-pages/settings-menu-page.php:1075
msgid "You may also email the <a href=\"%s\" target=\"_blank\">balance payment request</a> link to your guests."
msgstr "Você também pode enviar por e-mail o link <a href=\"%s\" target=\"_blank\"> solicitação de pagamento do saldo </a> para seus convidados."

#: includes/admin/menu-pages/settings-menu-page.php:1086
msgid "User Pays"
msgstr "Usuário Paga"

#: includes/admin/menu-pages/settings-menu-page.php:1088
msgid "Full Amount"
msgstr "Quantia total"

#: includes/admin/menu-pages/settings-menu-page.php:1089
#: includes/views/booking-view.php:463
msgid "Deposit"
msgstr "Depósito"

#: includes/admin/menu-pages/settings-menu-page.php:1098
msgid "Deposit Type"
msgstr "Tipo de Depósito"

#: includes/admin/menu-pages/settings-menu-page.php:1100
#: includes/post-types/coupon-cpt.php:115
#: includes/post-types/coupon-cpt.php:160
msgid "Fixed"
msgstr "Fixo"

#: includes/admin/menu-pages/settings-menu-page.php:1101
msgid "Percent"
msgstr "Por cento"

#: includes/admin/menu-pages/settings-menu-page.php:1110
msgid "Deposit Amount"
msgstr "Valor do Depósito"

#: includes/admin/menu-pages/settings-menu-page.php:1121
msgid "Deposit Time Frame (days)"
msgstr "Período para depósito (dias)"

#: includes/admin/menu-pages/settings-menu-page.php:1122
msgid "Apply deposit to bookings made in at least the selected number of days prior to the check-in date. Otherwise, the full amount is charged."
msgstr "Solicitar depósito às reservas feitas em pelo menos o número selecionado de dias antes da data de check-in. Caso contrário, o valor total é cobrado."

#: includes/admin/menu-pages/settings-menu-page.php:1133
msgid "Force Secure Checkout"
msgstr "Forçar Checkout Seguro"

#: includes/admin/menu-pages/settings-menu-page.php:1135
msgid "Force SSL (HTTPS) on the checkout pages. You must have an SSL certificate installed to use this option."
msgstr "Forçar SSL (HTTPS) nas páginas de checkout. É necessário ter um certificado SSL instalado para usar esta opção."

#: includes/admin/menu-pages/settings-menu-page.php:1142
msgid "Reservation Received Page"
msgstr "Página Reserva Recebida"

#: includes/admin/menu-pages/settings-menu-page.php:1150
msgid "Failed Transaction Page"
msgstr "Página de Falha na Transação"

#: includes/admin/menu-pages/settings-menu-page.php:1158
msgid "Default Gateway"
msgstr "Gateway Padrão"

#: includes/admin/menu-pages/settings-menu-page.php:1172
#: includes/payments/gateways/bank-gateway.php:127
msgid "Pending Payment Time"
msgstr "Tempo Pendente de Pagamento"

#: includes/admin/menu-pages/settings-menu-page.php:1173
msgid "Period of time in minutes the user is given to complete payment. Unpaid bookings become Abandoned and accommodation status changes to Available."
msgstr "Período de tempo em minutos o usuário é dado para completar o pagamento. As reservas não pagas são canceladas e as alteradas para disponíveis."

#: includes/admin/menu-pages/settings-menu-page.php:1195
msgid "Install <a href=\"%s\" target=\"_blank\">Hotel Booking addons</a> to manage their settings."
msgstr "Instalar <a href=\"%s\" target=\"_blank\">addons Hotel Booking</a> para gerenciar suas configurações."

#: includes/admin/menu-pages/settings-menu-page.php:1197
msgid "Install Hotel Booking addons to manage their settings."
msgstr "Instalar addons Hotel Booking para gerenciar suas configurações."

#: includes/admin/menu-pages/settings-menu-page.php:1214
msgid "Advanced"
msgstr "Avançado"

#: includes/admin/menu-pages/settings-menu-page.php:1226
#: includes/admin/menu-pages/settings-menu-page.php:1228
msgid "License"
msgstr "Licença"

#: includes/admin/menu-pages/settings-menu-page.php:1305
msgid "Settings saved."
msgstr "Configurações salvas."

#: includes/admin/menu-pages/settings-menu-page.php:1344
msgid "<strong>Note:</strong> Payment methods will appear on the checkout page only when Confirmation Upon Payment is enabled in Accommodation > Settings > General > Confirmation Mode."
msgstr "<strong>Nota: Os métodos de pagamento</strong> só aparecerão na página de pagamento quando a confirmação de pagamento estiver ativada em Acomodações > Geral > Modo de Confirmação."

#: includes/admin/menu-pages/settings-menu-page.php:1432
#: includes/admin/menu-pages/settings-menu-page.php:1436
#: assets/blocks/blocks.js:141
#: assets/blocks/blocks.js:276
#: assets/blocks/blocks.js:429
#: assets/blocks/blocks.js:679
#: assets/blocks/blocks.js:1196
#: assets/blocks/blocks.js:1419
#: assets/blocks/blocks.js:1501
msgid "Settings"
msgstr "Configurações"

#: includes/admin/menu-pages/shortcodes-menu-page.php:21
#: assets/blocks/blocks.js:117
msgid "Availability Search Form"
msgstr "Formulário de Busca de Disponibilidade"

#: includes/admin/menu-pages/shortcodes-menu-page.php:22
msgid "Display search form."
msgstr "Mostrar formulário de busca."

#: includes/admin/menu-pages/shortcodes-menu-page.php:25
#: assets/blocks/blocks.js:148
msgid "The number of adults presetted in the search form."
msgstr "O número de adultos apresentadas no formulário de busca."

#: includes/admin/menu-pages/shortcodes-menu-page.php:30
#: assets/blocks/blocks.js:163
msgid "The number of children presetted in the search form."
msgstr "O número de crianças apresentadas no formulário de busca."

#: includes/admin/menu-pages/shortcodes-menu-page.php:35
msgid "Check-in date presetted in the search form."
msgstr "Data de checkin mostrada no formulário de pesquisa."

#: includes/admin/menu-pages/shortcodes-menu-page.php:36
#: includes/admin/menu-pages/shortcodes-menu-page.php:41
msgid "date in format %s"
msgstr "data no formato %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:40
msgid "Check-out date presetted in the search form."
msgstr "Data de checkout mostrada no formulário de busca."

#: includes/admin/menu-pages/shortcodes-menu-page.php:45
#: assets/blocks/blocks.js:201
msgid "Custom attributes for advanced search."
msgstr "Atributos personalizados para pesquisa avançada."

#: includes/admin/menu-pages/shortcodes-menu-page.php:46
#: assets/blocks/blocks.js:202
msgid "Comma-separated slugs of attributes."
msgstr "Slugs de atributos separados por vírgula."

#: includes/admin/menu-pages/shortcodes-menu-page.php:50
#: includes/admin/menu-pages/shortcodes-menu-page.php:94
#: includes/admin/menu-pages/shortcodes-menu-page.php:180
#: includes/admin/menu-pages/shortcodes-menu-page.php:259
#: includes/admin/menu-pages/shortcodes-menu-page.php:361
#: includes/admin/menu-pages/shortcodes-menu-page.php:422
#: includes/admin/menu-pages/shortcodes-menu-page.php:450
#: includes/admin/menu-pages/shortcodes-menu-page.php:470
#: includes/admin/menu-pages/shortcodes-menu-page.php:494
#: includes/admin/menu-pages/shortcodes-menu-page.php:514
#: includes/admin/menu-pages/shortcodes-menu-page.php:530
#: includes/admin/menu-pages/shortcodes-menu-page.php:546
msgid "Custom CSS class for shortcode wrapper"
msgstr "Classe personalizada de CSS para wrapper de shortcode"

#: includes/admin/menu-pages/shortcodes-menu-page.php:51
#: includes/admin/menu-pages/shortcodes-menu-page.php:95
#: includes/admin/menu-pages/shortcodes-menu-page.php:181
#: includes/admin/menu-pages/shortcodes-menu-page.php:260
#: includes/admin/menu-pages/shortcodes-menu-page.php:362
#: includes/admin/menu-pages/shortcodes-menu-page.php:423
#: includes/admin/menu-pages/shortcodes-menu-page.php:451
#: includes/admin/menu-pages/shortcodes-menu-page.php:471
#: includes/admin/menu-pages/shortcodes-menu-page.php:495
#: includes/admin/menu-pages/shortcodes-menu-page.php:515
#: includes/admin/menu-pages/shortcodes-menu-page.php:531
#: includes/admin/menu-pages/shortcodes-menu-page.php:547
msgid "whitespace separated css classes"
msgstr "espaços em branco separaram as classes do css"

#: includes/admin/menu-pages/shortcodes-menu-page.php:65
#: includes/admin/menu-pages/shortcodes-menu-page.php:465
#: includes/admin/menu-pages/shortcodes-menu-page.php:489
#: includes/csv/bookings/bookings-exporter-helper.php:76
#: includes/emails/templaters/reserved-rooms-templater.php:187
msgid "Accommodation Type ID"
msgstr "ID do Tipo de Acomodação"

#: includes/admin/menu-pages/shortcodes-menu-page.php:66
#: includes/admin/menu-pages/shortcodes-menu-page.php:466
msgid "ID of Accommodation Type to check availability."
msgstr "ID do Tipo de Acomodação para verificação de disponibilidade."

#: includes/admin/menu-pages/shortcodes-menu-page.php:67
#: includes/admin/menu-pages/shortcodes-menu-page.php:378
#: includes/admin/menu-pages/shortcodes-menu-page.php:467
#: includes/admin/menu-pages/shortcodes-menu-page.php:491
msgid "integer number"
msgstr "número inteiro"

#: includes/admin/menu-pages/shortcodes-menu-page.php:70
#: assets/blocks/blocks.js:295
msgid "How many months to show."
msgstr "Quantidade de meses a serem mostrados."

#: includes/admin/menu-pages/shortcodes-menu-page.php:72
#: assets/blocks/blocks.js:296
msgid "Set the number of columns or the number of rows and columns separated by comma. Example: \"3\" or \"2,3\""
msgstr "Defina o número de colunas ou o número de linhas e colunas separados por vírgula. Exemplo: \"3\" ou \"2,3\""

#: includes/admin/menu-pages/shortcodes-menu-page.php:106
#: assets/blocks/blocks.js:251
msgid "Display availability calendar of the current accommodation type or by ID."
msgstr "Exibir calendário de disponibilidade do atual tipo de acomodação ou por ID."

#: includes/admin/menu-pages/shortcodes-menu-page.php:111
#: assets/blocks/blocks.js:397
#: assets/blocks/blocks.js:630
msgid "Availability Search Results"
msgstr "Resultados de Buscas de Disponibilidade"

#: includes/admin/menu-pages/shortcodes-menu-page.php:112
#: assets/blocks/blocks.js:398
msgid "Display listing of accommodation types that meet the search criteria."
msgstr "Mostrar a lista de tipos de acomoção."

#: includes/admin/menu-pages/shortcodes-menu-page.php:115
#: includes/admin/menu-pages/shortcodes-menu-page.php:212
#: includes/admin/menu-pages/shortcodes-menu-page.php:381
#: assets/blocks/blocks.js:437
#: assets/blocks/blocks.js:687
#: assets/blocks/blocks.js:1216
msgid "Whether to display title of the accommodation type."
msgstr "Mostrar ou não o titlo do tipo de acomodação."

#: includes/admin/menu-pages/shortcodes-menu-page.php:120
#: includes/admin/menu-pages/shortcodes-menu-page.php:217
#: includes/admin/menu-pages/shortcodes-menu-page.php:386
#: assets/blocks/blocks.js:449
#: assets/blocks/blocks.js:699
#: assets/blocks/blocks.js:1228
msgid "Whether to display featured image of the accommodation type."
msgstr "Mostrar ou não a imagem em destaque do tipo de acomodação."

#: includes/admin/menu-pages/shortcodes-menu-page.php:125
#: includes/admin/menu-pages/shortcodes-menu-page.php:222
#: includes/admin/menu-pages/shortcodes-menu-page.php:391
#: assets/blocks/blocks.js:461
#: assets/blocks/blocks.js:711
#: assets/blocks/blocks.js:1240
msgid "Whether to display gallery of the accommodation type."
msgstr "Mostrar ou não a galeria de tipos de acomodações."

#: includes/admin/menu-pages/shortcodes-menu-page.php:130
#: includes/admin/menu-pages/shortcodes-menu-page.php:227
#: includes/admin/menu-pages/shortcodes-menu-page.php:396
#: assets/blocks/blocks.js:473
#: assets/blocks/blocks.js:723
#: assets/blocks/blocks.js:1252
msgid "Whether to display excerpt (short description) of the accommodation type."
msgstr "Mostrar ou não o excerpt (descrição curta) do tipo de acomodações."

#: includes/admin/menu-pages/shortcodes-menu-page.php:135
#: includes/admin/menu-pages/shortcodes-menu-page.php:232
#: includes/admin/menu-pages/shortcodes-menu-page.php:401
#: assets/blocks/blocks.js:485
#: assets/blocks/blocks.js:735
#: assets/blocks/blocks.js:1264
msgid "Whether to display details of the accommodation type."
msgstr "Mostrar ou não os detalhes do tipo de acomodação."

#: includes/admin/menu-pages/shortcodes-menu-page.php:140
#: includes/admin/menu-pages/shortcodes-menu-page.php:237
#: includes/admin/menu-pages/shortcodes-menu-page.php:406
#: includes/admin/menu-pages/shortcodes-menu-page.php:427
#: assets/blocks/blocks.js:497
#: assets/blocks/blocks.js:747
#: assets/blocks/blocks.js:1276
msgid "Whether to display price of the accommodation type."
msgstr "Mostrar ou não o preço do tipo de acomodação."

#: includes/admin/menu-pages/shortcodes-menu-page.php:145
#: includes/admin/menu-pages/shortcodes-menu-page.php:242
#: includes/admin/menu-pages/shortcodes-menu-page.php:411
msgid "Show View Details button"
msgstr "Mostrar o Botão Ver Detalhes"

#: includes/admin/menu-pages/shortcodes-menu-page.php:146
#: includes/admin/menu-pages/shortcodes-menu-page.php:243
#: includes/admin/menu-pages/shortcodes-menu-page.php:412
#: assets/blocks/blocks.js:509
#: assets/blocks/blocks.js:759
#: assets/blocks/blocks.js:1288
msgid "Whether to display \"View Details\" button with the link to accommodation type."
msgstr "Exibir ou não o botão \"Ver Detalhes\" com o link para o tipo de acomodação."

#: includes/admin/menu-pages/shortcodes-menu-page.php:151
#: includes/admin/menu-pages/shortcodes-menu-page.php:284
#: includes/admin/menu-pages/shortcodes-menu-page.php:332
msgid "Sort by."
msgstr "Ordenar por."

#: includes/admin/menu-pages/shortcodes-menu-page.php:153
#: includes/admin/menu-pages/shortcodes-menu-page.php:173
#: includes/admin/menu-pages/shortcodes-menu-page.php:286
#: includes/admin/menu-pages/shortcodes-menu-page.php:306
#: includes/admin/menu-pages/shortcodes-menu-page.php:334
#: includes/admin/menu-pages/shortcodes-menu-page.php:354
msgid "%1$s. See the <a href=\"%2$s\" target=\"_blank\">full list</a>."
msgstr "%1$s. Veja a <a href=\"%2$s\" target=\"_blank\">lista completa</a>."

#: includes/admin/menu-pages/shortcodes-menu-page.php:160
#: includes/admin/menu-pages/shortcodes-menu-page.php:293
#: includes/admin/menu-pages/shortcodes-menu-page.php:341
msgid "Designates the ascending or descending order of sorting."
msgstr "Mostra a ordem crescente ou decrescente."

#: includes/admin/menu-pages/shortcodes-menu-page.php:162
#: includes/admin/menu-pages/shortcodes-menu-page.php:295
#: includes/admin/menu-pages/shortcodes-menu-page.php:343
msgid "ASC - from lowest to highest values (1, 2, 3). DESC - from highest to lowest values (3, 2, 1)."
msgstr "CRE - de menor para maior valor (1, 2, 3). DEC - de maior para menor valor (3, 2, 1)."

#: includes/admin/menu-pages/shortcodes-menu-page.php:166
#: includes/admin/menu-pages/shortcodes-menu-page.php:299
#: includes/admin/menu-pages/shortcodes-menu-page.php:347
#: assets/blocks/blocks.js:574
#: assets/blocks/blocks.js:910
#: assets/blocks/blocks.js:1094
msgid "Custom field name. Required if \"orderby\" is one of the \"meta_value\", \"meta_value_num\" or \"meta_value_*\"."
msgstr "Nome do campo personalizado. Obrigatório se \"orderby\" é um dos \"meta_value\", \"meta_value_num\" ou \"meta_value_*\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:167
#: includes/admin/menu-pages/shortcodes-menu-page.php:300
#: includes/admin/menu-pages/shortcodes-menu-page.php:348
msgid "custom field name"
msgstr "nome do campo personalizado"

#: includes/admin/menu-pages/shortcodes-menu-page.php:168
#: includes/admin/menu-pages/shortcodes-menu-page.php:177
#: includes/admin/menu-pages/shortcodes-menu-page.php:301
#: includes/admin/menu-pages/shortcodes-menu-page.php:310
#: includes/admin/menu-pages/shortcodes-menu-page.php:349
#: includes/admin/menu-pages/shortcodes-menu-page.php:358
#: includes/admin/menu-pages/shortcodes-menu-page.php:645
msgid "empty string"
msgstr "string vazia"

#: includes/admin/menu-pages/shortcodes-menu-page.php:171
#: includes/admin/menu-pages/shortcodes-menu-page.php:304
#: includes/admin/menu-pages/shortcodes-menu-page.php:352
msgid "Specified type of the custom field. Can be used in conjunction with orderby=\"meta_value\"."
msgstr "Tipo especificado do campo personalizado. Pode ser usado em conjunto com orderby=\"meta_value\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:185
msgid "Sort by. Use \"orderby\" insted."
msgstr "Ordenar por \"ordenar\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:191
#: includes/admin/menu-pages/shortcodes-menu-page.php:248
msgid "Show Book button"
msgstr "Mostrar o botão de reserva"

#: includes/admin/menu-pages/shortcodes-menu-page.php:192
#: includes/admin/menu-pages/shortcodes-menu-page.php:249
#: includes/admin/menu-pages/shortcodes-menu-page.php:417
#: assets/blocks/blocks.js:771
#: assets/blocks/blocks.js:1300
msgid "Whether to display Book button."
msgstr "Mostrar o botão de reserva ou não."

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "NOTE:"
msgstr "Nota:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
msgid "Use only on page that you set as Search Results Page in <a href=\"%s\">Settings</a>"
msgstr "Usar apenas na página que foi definida como página de resultados de buscas nas <a href=\"%s\">Configurações</a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:209
#: assets/blocks/blocks.js:642
msgid "Accommodation Types Listing"
msgstr "Listas de Tipos de Acomodações"

#: includes/admin/menu-pages/shortcodes-menu-page.php:254
#: includes/admin/menu-pages/shortcodes-menu-page.php:327
#: assets/blocks/blocks.js:804
#: assets/blocks/blocks.js:1028
msgid "Count per page"
msgstr "Contagem por página"

#: includes/admin/menu-pages/shortcodes-menu-page.php:255
#: assets/blocks/blocks.js:805
msgid "integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "inteiro, -1 para exibir todos, padrão: \"Páginas do blog são mostradas no máximo\""

#: includes/admin/menu-pages/shortcodes-menu-page.php:264
#: assets/blocks/blocks.js:817
msgid "IDs of categories that will be shown."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:265
#: includes/admin/menu-pages/shortcodes-menu-page.php:270
#: includes/admin/menu-pages/shortcodes-menu-page.php:275
#: includes/admin/menu-pages/shortcodes-menu-page.php:323
#: assets/blocks/blocks.js:1017
msgid "Comma-separated IDs."
msgstr "IDs Separados-Por-Vírgulas."

#: includes/admin/menu-pages/shortcodes-menu-page.php:269
#: assets/blocks/blocks.js:829
msgid "IDs of tags that will be shown."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:274
#: assets/blocks/blocks.js:793
msgid "IDs of accommodations that will be shown."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:279
#: assets/blocks/blocks.js:841
msgid "Logical relationship between each taxonomy when there is more than one."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:319
#: assets/blocks/blocks.js:983
msgid "Services Listing"
msgstr "Lista de serviços"

#: includes/admin/menu-pages/shortcodes-menu-page.php:322
#: assets/blocks/blocks.js:792
msgid "IDs"
msgstr "IDs"

#: includes/admin/menu-pages/shortcodes-menu-page.php:324
#: assets/blocks/blocks.js:1016
msgid "IDs of services that will be shown. "
msgstr "IDs dos serviços que serão mostrados. "

#: includes/admin/menu-pages/shortcodes-menu-page.php:368
msgid "Show All Services"
msgstr "Mostrar Todos os Serviços"

#: includes/admin/menu-pages/shortcodes-menu-page.php:373
#: assets/blocks/blocks.js:1167
#: assets/blocks/blocks.js:1344
msgid "Single Accommodation Type"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:377
msgid "ID of accommodation type to display."
msgstr "ID do tipo de acomodação a ser exibido."

#: includes/admin/menu-pages/shortcodes-menu-page.php:441
msgid "Display accommodation type with title and image."
msgstr "Exibir tipo de acomodação com o título e imagem."

#: includes/admin/menu-pages/shortcodes-menu-page.php:446
#: assets/blocks/blocks.js:1356
#: assets/blocks/blocks.js:1386
msgid "Checkout Form"
msgstr "Formulário de Checkout"

#: includes/admin/menu-pages/shortcodes-menu-page.php:447
#: assets/blocks/blocks.js:1357
msgid "Display checkout form."
msgstr "Exibir formulário de Checkout."

#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "Use only on page that you set as Checkout Page in <a href=\"%s\">Settings</a>"
msgstr "Usar apenas na página que você configurou como Página de Checkout em <a href=\"%s\">Configuração</a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:462
#: assets/blocks/blocks.js:1398
#: assets/blocks/blocks.js:1468
msgid "Booking Form"
msgstr "Formulário de Reserva"

#: includes/admin/menu-pages/shortcodes-menu-page.php:481
msgid "Show Booking Form for Accommodation Type with id 777"
msgstr "Mostrar o Formulário de Reserva do Tipo de Acomodação com id 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:486
#: assets/blocks/blocks.js:1480
#: assets/blocks/blocks.js:1550
msgid "Accommodation Rates List"
msgstr "Lista de Tarifas de Acomodação"

#: includes/admin/menu-pages/shortcodes-menu-page.php:490
#: assets/blocks/blocks.js:1204
msgid "ID of accommodation type."
msgstr "ID do tipo de acomodação."

#: includes/admin/menu-pages/shortcodes-menu-page.php:505
msgid "Show Accommodation Rates List for accommodation type with id 777"
msgstr "Mostrar lista de preços dos tipos de acomodação com id 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:511
#: assets/blocks/blocks.js:1563
msgid "Display booking and payment details."
msgstr "Exibir detalhes de reserva e pagamento."

#: includes/admin/menu-pages/shortcodes-menu-page.php:521
msgid "Use this shortcode on Booking Confirmed and Reservation Received pages"
msgstr "Use este código de acesso nas páginas Reserva confirmada e Reserva recebida"

#: includes/admin/menu-pages/shortcodes-menu-page.php:526
msgid "Booking Cancelation"
msgstr "Cancelamento da Reserva"

#: includes/admin/menu-pages/shortcodes-menu-page.php:527
msgid "Display booking cancelation details."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:537
msgid "Use this shortcode on the Booking Cancelation page"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:542
msgid "Customer Account"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:543
msgid "Display log in form or customer account area."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:553
msgid "Use this shortcode to create the My Account page."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:565
#: includes/admin/menu-pages/shortcodes-menu-page.php:699
#: includes/admin/menu-pages/shortcodes-menu-page.php:703
msgid "Shortcodes"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:569
msgid "Shortcode"
msgstr "Shortcode"

#: includes/admin/menu-pages/shortcodes-menu-page.php:570
#: includes/post-types/attributes-cpt.php:307
msgid "Parameters"
msgstr "Parâmetros"

#: includes/admin/menu-pages/shortcodes-menu-page.php:571
msgid "Example"
msgstr "Exemplo"

#: includes/admin/menu-pages/shortcodes-menu-page.php:603
#: includes/admin/menu-pages/shortcodes-menu-page.php:625
msgid "Deprecated since %s"
msgstr "Desatualizado desde %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:635
msgid "Values:"
msgstr "Valores:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:640
msgid "Default:"
msgstr "Padrão:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:687
msgid "Optional."
msgstr "Opcional."

#: includes/admin/menu-pages/shortcodes-menu-page.php:695
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:30
#: includes/payments/gateways/gateway.php:365
#: includes/views/shortcodes/checkout-view.php:247
#: includes/views/shortcodes/checkout-view.php:270
#: includes/views/shortcodes/checkout-view.php:538
#: includes/views/shortcodes/checkout-view.php:572
#: templates/account/account-details.php:34
msgid "Required"
msgstr "Requeridos"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:34
msgid "Taxes and fees saved."
msgstr "Impostos e taxas economizados."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:41
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:459
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:463
msgid "Taxes & Fees"
msgstr "Impostos e Taxas"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:136
#: includes/csv/bookings/bookings-exporter-helper.php:102
#: includes/views/booking-view.php:296
msgid "Fees"
msgstr "Taxas"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:137
msgid "No fees have been created yet."
msgstr "Nenhuma taxa foi criada ainda."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:138
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:234
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:321
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:392
#: includes/post-types/booking-cpt.php:219
msgid "Add new"
msgstr "Adicionar novo"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:146
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:242
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:329
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:400
msgid "Label"
msgstr "Rótulo"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:147
msgid "New fee"
msgstr "Nova taxa"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:158
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:254
msgid "Per guest / per day"
msgstr "Por hóspede / por dia"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:159
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:255
msgid "Per accommodation / per day"
msgstr "Por acomodação / por dia"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:160
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:256
msgid "Per accommodation (%)"
msgstr "Por acomodação (%)"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:182
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:278
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:364
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:435
msgid "Limit"
msgstr "Limite"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:183
msgid "How often this fee is charged. Set 0 to charge each day of the stay period. Set 1 to charge once."
msgstr ""

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:197
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:200
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:293
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:296
msgid "Include"
msgstr ""

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:198
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:294
msgid "Show accommodation rate with this charge included"
msgstr ""

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:232
#: includes/csv/bookings/bookings-exporter-helper.php:95
#: includes/views/booking-view.php:171
msgid "Accommodation Taxes"
msgstr "Taxas de acomodação"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:233
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:320
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:391
msgid "No taxes have been created yet."
msgstr "Nenhum imposto foi criado ainda."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:243
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:330
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:401
msgid "New tax"
msgstr "Novo imposto"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:279
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:365
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:436
msgid "Limit of days the fee is charged. Set 0 to charge each day of stay period. Set 1 to charge once."
msgstr ""

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:319
#: includes/views/booking-view.php:265
msgid "Service Taxes"
msgstr "Impostos sobre serviços"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:341
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:412
#: includes/post-types/coupon-cpt.php:114
#: includes/post-types/coupon-cpt.php:159
msgid "Percentage"
msgstr "Porcentagem"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:390
#: includes/views/booking-view.php:349
msgid "Fee Taxes"
msgstr "Impostos sobre taxas"

#: includes/admin/room-list-table.php:95
msgid "External Calendars"
msgstr "Calendários Externos"

#: includes/admin/room-list-table.php:157
#: includes/admin/room-list-table.php:211
msgid "Sync External Calendars"
msgstr "Sincronizar Calendário Externo"

#: includes/admin/room-list-table.php:163
#: includes/admin/sync-rooms-list-table.php:65
msgctxt "Placeholder for empty accommodation title"
msgid "(no title)"
msgstr "(sem titulo)"

#: includes/admin/room-list-table.php:185
msgid "Download Calendar"
msgstr "Fazer o Download do Calendário"

#: includes/admin/sync-logs-list-table.php:73
msgid "Message"
msgstr "Mensagem"

#: includes/admin/sync-logs-list-table.php:82
msgid "Success"
msgstr ""

#: includes/admin/sync-logs-list-table.php:85
msgid "Info"
msgstr ""

#: includes/admin/sync-logs-list-table.php:88
msgid "Warning"
msgstr ""

#: includes/admin/sync-rooms-list-table.php:71
msgctxt "This is date and time format 31/12/2017 - 23:59:59"
msgid "d/m/Y - H:i:s"
msgstr ""

#: includes/admin/sync-rooms-list-table.php:75
#: includes/ajax.php:945
msgid "Waiting"
msgstr "A espera"

#: includes/admin/sync-rooms-list-table.php:78
#: includes/ajax.php:948
msgid "Processing"
msgstr "Processando"

#: includes/admin/sync-rooms-list-table.php:128
msgctxt "Total number of processed bookings"
msgid "Total"
msgstr "Total"

#: includes/admin/sync-rooms-list-table.php:129
msgid "Succeed"
msgstr "Sucedeu"

#: includes/admin/sync-rooms-list-table.php:130
msgid "Skipped"
msgstr "Pulado"

#: includes/admin/sync-rooms-list-table.php:131
msgid "Failed"
msgstr "Falhou"

#: includes/admin/sync-rooms-list-table.php:132
msgid "Removed"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:58
msgid "Copying to clipboard failed. Please press Ctrl/Cmd+C to copy."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:80
msgid "Description is missing."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:83
msgid "User is missing."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:86
msgid "Permission is missing."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:102
msgid "You do not have permission to assign API Keys to the selected user."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:132
msgid "API Key updated successfully."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:164
msgid "API Key generated successfully. Make sure to copy your new keys now as the secret key will be hidden once you leave this page."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:176
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:116
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:119
msgid "Revoke key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:46
msgid "No keys found."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:57
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:22
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:95
#: includes/payments/gateways/gateway.php:504
#: includes/post-types/coupon-cpt.php:37
#: includes/post-types/rate-cpt.php:81
msgid "Description"
msgstr "Descrição"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:58
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:81
msgid "Consumer key ending in"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:59
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:36
msgid "User"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:60
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:55
msgid "Permissions"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:61
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:89
msgid "Last access"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:99
msgid "API key"
msgstr ""

#. translators: %d: API key ID.
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:111
msgid "ID: %d"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:126
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:227
msgid "Revoke"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:182
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:65
msgid "Read"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:183
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:66
msgid "Write"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:184
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:67
msgid "Read/Write"
msgstr ""

#. translators: 1: last access date 2: last access time
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:205
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:96
msgid "%1$s at %2$s"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:213
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:100
msgid "Unknown"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:68
msgid "You do not have permission to edit this API Key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:89
msgid "REST API"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:91
msgid "Add key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:176
msgid "You do not have permission to revoke this API Key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:191
msgid "You do not have permission to edit API Keys"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:211
msgid "You do not have permission to revoke API Keys"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:13
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:114
msgid "Generate API key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:25
msgid "Friendly name for identifying this key."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:39
msgid "Owner of these keys."
msgstr "Proprietário destas chaves."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:58
msgid "Access type of these keys."
msgstr "Tipo de acesso dessas chaves."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:148
msgid "Consumer key"
msgstr "Chave do cliente"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copied!"
msgstr "Copiado!"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copy"
msgstr "Copiar"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:156
msgid "Consumer secret"
msgstr "Chave privada do cliente"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:164
msgid "QR Code"
msgstr "QR Code"

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:33
#: includes/ajax-api/ajax-actions/update-booking-notes.php:52
#: includes/ajax.php:344
#: includes/ajax.php:388
#: includes/csv/bookings/bookings-query.php:85
msgid "Please complete all required fields and try again."
msgstr "Por favor, preencha todos os campos obrigatórios e tente novamente."

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:55
msgid "Sorry, the minimum allowed payment amount is %s to use this payment method."
msgstr "Desculpe, o valor mínimo permitido para usar este método de pagamento é %s."

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:60
#: includes/post-types/booking-cpt.php:35
msgid "Booking Information"
msgstr "Informação de Reserva"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:68
#: includes/emails/templaters/email-templater.php:136
#: includes/post-types/booking-cpt.php:51
#: template-functions.php:706
#: template-functions.php:710
#: templates/create-booking/search/search-form.php:53
#: templates/edit-booking/edit-dates.php:33
#: templates/shortcodes/search/search-form.php:43
#: templates/widgets/search-availability/search-form.php:43
#: assets/blocks/blocks.js:177
msgid "Check-in Date"
msgstr "Data do Checkin"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:72
#: includes/emails/templaters/email-templater.php:140
#: includes/post-types/booking-cpt.php:60
#: template-functions.php:715
#: template-functions.php:719
#: templates/create-booking/search/search-form.php:73
#: templates/edit-booking/edit-dates.php:42
#: templates/shortcodes/search/search-form.php:63
#: templates/widgets/search-availability/search-form.php:62
#: assets/blocks/blocks.js:189
msgid "Check-out Date"
msgstr "Data do Checkout"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:91
msgid "Summary"
msgstr "Resumo"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:99
msgid "Source"
msgstr "Fonte"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:110
#: includes/post-types/booking-cpt.php:83
#: templates/emails/customer-approved-booking.php:31
#: templates/emails/customer-cancelled-booking.php:29
#: templates/emails/customer-confirmation-booking.php:35
#: templates/emails/customer-pending-booking.php:32
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:30
msgid "Customer Information"
msgstr "Informação do Cliente"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:150
#: includes/csv/bookings/bookings-exporter-helper.php:90
#: includes/emails/templaters/email-templater.php:189
#: includes/post-types/booking-cpt.php:164
msgid "Customer Note"
msgstr "Nota do cliente"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:156
#: includes/post-types/booking-cpt.php:171
msgid "Additional Information"
msgstr "Informação Adicional"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:160
#: includes/csv/bookings/bookings-exporter-helper.php:107
#: includes/post-types/booking-cpt.php:178
#: includes/post-types/coupon-cpt.php:289
msgid "Coupon"
msgstr "Cupom"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:164
#: includes/post-types/booking-cpt.php:187
msgid "Total Booking Price"
msgstr "Preço Total da Reserva"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:173
#: includes/bundles/customer-bundle.php:173
#: includes/post-types/booking-cpt.php:201
#: includes/views/shortcodes/checkout-view.php:735
msgid "Notes"
msgstr "Notas"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:192
msgid "%1$s on %2$s"
msgstr "%1$s no(a) %2$s"

#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:185
#: template-functions.php:88
msgid "Based on your search parameters"
msgstr "Com base nos parâmetros de buscas"

#: includes/ajax.php:245
msgid "No bookings found for your request."
msgstr "Nenhuma reserva encontrada para sua solicitação."

#: includes/ajax.php:252
msgid "Uploads directory is not writable."
msgstr "Não existem permissões de escrita na directoria de uploads."

#: includes/ajax.php:314
msgid "No enough data"
msgstr "Não há dados suficientes"

#: includes/ajax.php:332
#: includes/script-managers/admin-script-manager.php:94
msgid "An error has occurred"
msgstr "Ocorreu um erro"

#: includes/ajax.php:372
#: includes/script-managers/public-script-manager.php:199
msgid "An error has occurred, please try again later."
msgstr "Ocorreu um erro, por favor tente novamente mais tarde."

#: includes/ajax.php:473
msgid "The number of adults is not valid."
msgstr "O número de adultos não é válido."

#: includes/ajax.php:477
msgid "The number of guests is not valid."
msgstr "O número total de hóspedes é inválido."

#: includes/ajax.php:519
#: includes/ajax.php:593
msgid "An error has occurred. Please try again later."
msgstr "Um erro ocorreu. Por favor, tente novamente mais tarde."

#: includes/ajax.php:750
msgid "Chosen payment method is not available. Please refresh the page and try one more time."
msgstr "O método de pagamento escolhido não está disponível. Por favor, atualize a página e tente novamente."

#: includes/ajax.php:832
msgid "Coupon applied successfully."
msgstr "O cupom foi aplicado com sucesso."

#: includes/ajax.php:838
#: includes/entities/coupon.php:366
msgid "Coupon is not valid."
msgstr "O cupom não é válido."

#: includes/ajax.php:1046
msgid "You do not have permission to do this action."
msgstr "Você não tem permissão para fazer esta ação."

#: includes/attribute-functions.php:164
#: includes/post-types/attributes-cpt.php:137
#: includes/post-types/attributes-cpt.php:149
#: includes/post-types/attributes-cpt.php:345
msgctxt "Not selected value in the search form."
msgid "&mdash;"
msgstr ""

#: includes/bookings-calendar.php:577
msgid "Year"
msgstr "Ano"

#: includes/bookings-calendar.php:578
#: includes/post-types/attributes-cpt.php:298
#: includes/reports/report-filters.php:94
msgid "Custom"
msgstr "Personalizado"

#: includes/bookings-calendar.php:608
#: includes/post-types/booking-cpt/statuses.php:102
#: includes/reports/data/report-earnings-by-dates-data.php:28
msgctxt "Booking status"
msgid "Confirmed"
msgstr "Confirmado"

#: includes/bookings-calendar.php:645
#: includes/reports/abstract-report.php:46
#: includes/reports/earnings-report.php:361
msgid "Show"
msgstr "Mostrar"

#: includes/bookings-calendar.php:649
#: templates/create-booking/search/search-form.php:131
#: templates/shortcodes/search/search-form.php:138
#: templates/widgets/search-availability/search-form.php:142
msgid "Search"
msgstr "Busca"

#: includes/bookings-calendar.php:652
#: includes/bookings-calendar.php:654
#: includes/bookings-calendar.php:697
#: includes/script-managers/public-script-manager.php:200
msgid "Booked"
msgstr "Reservado"

#: includes/bookings-calendar.php:657
#: includes/bookings-calendar.php:659
#: includes/bookings-calendar.php:698
#: includes/script-managers/public-script-manager.php:202
msgid "Pending"
msgstr "Pendente"

#: includes/bookings-calendar.php:662
#: includes/bookings-calendar.php:664
msgid "External"
msgstr "Externo"

#: includes/bookings-calendar.php:667
#: includes/bookings-calendar.php:669
#: includes/bookings-calendar.php:1133
msgid "Blocked"
msgstr "Bloqueado"

#: includes/bookings-calendar.php:687
msgid "Search results for accommodations that have bookings with status \"%s\" from %s until %s"
msgstr "Buscar resultados de acomodações que têm reservas com status \"%s\" de %s até %s"

#: includes/bookings-calendar.php:696
msgid "Free"
msgstr "Livre"

#: includes/bookings-calendar.php:699
msgid "Locked (Booked or Pending)"
msgstr "Bloqueado (reservado ou pendente)"

#: includes/bookings-calendar.php:729
#: includes/bookings-calendar.php:819
msgid "Until"
msgstr "Até"

#: includes/bookings-calendar.php:775
msgid "Period:"
msgstr "Período:"

#: includes/bookings-calendar.php:782
msgid "&lt; Prev"
msgstr "&lt; Anterior"

#: includes/bookings-calendar.php:799
msgid "Next &gt;"
msgstr "Próximo &gt;"

#: includes/bookings-calendar.php:874
msgid "No accommodations found."
msgstr "Nenhuma acomodação encontrada."

#: includes/bookings-calendar.php:1123
msgid "Check-out #%d"
msgstr "Checkout #%d"

#: includes/bookings-calendar.php:1127
msgid "Check-in #%d"
msgstr "Checkin #%d"

#: includes/bookings-calendar.php:1131
msgid "Booking #%d"
msgstr "Reserva #%d"

#: includes/bookings-calendar.php:1136
#: includes/bookings-calendar.php:1140
#: includes/script-managers/public-script-manager.php:201
msgid "Buffer time."
msgstr "Tempo de buffer."

#: includes/bookings-calendar.php:1143
msgctxt "Availability"
msgid "Free"
msgstr "Disponível"

#: includes/bookings-calendar.php:1172
#: templates/emails/reserved-room-details.php:15
msgid "Adults: %s"
msgstr "Adultos: %s"

#: includes/bookings-calendar.php:1176
#: templates/emails/reserved-room-details.php:17
msgid "Children: %s"
msgstr "Crianças: %s"

#: includes/bookings-calendar.php:1183
msgid "Booking imported with UID %s."
msgstr "Reserva importada com UID %s."

#: includes/bookings-calendar.php:1185
msgid "Imported booking."
msgstr "Reserva importada."

#: includes/bookings-calendar.php:1193
msgid "Description: %s."
msgstr "Descrição: %s."

#: includes/bookings-calendar.php:1197
msgid "Source: %s."
msgstr "Fonte: %s."

#: includes/bundles/countries-bundle.php:16
msgid "Afghanistan"
msgstr "Afeganistão"

#: includes/bundles/countries-bundle.php:17
msgid "&#197;land Islands"
msgstr "Ilhas terrestres"

#: includes/bundles/countries-bundle.php:18
msgid "Albania"
msgstr "Albânia"

#: includes/bundles/countries-bundle.php:19
msgid "Algeria"
msgstr "Algéria"

#: includes/bundles/countries-bundle.php:20
msgid "American Samoa"
msgstr "Samoa Americana"

#: includes/bundles/countries-bundle.php:21
msgid "Andorra"
msgstr "Andorra"

#: includes/bundles/countries-bundle.php:22
msgid "Angola"
msgstr "Angola"

#: includes/bundles/countries-bundle.php:23
msgid "Anguilla"
msgstr "Anguila"

#: includes/bundles/countries-bundle.php:24
msgid "Antarctica"
msgstr "Antártica"

#: includes/bundles/countries-bundle.php:25
msgid "Antigua and Barbuda"
msgstr "Antígua e Barbuda"

#: includes/bundles/countries-bundle.php:26
msgid "Argentina"
msgstr "Argentina"

#: includes/bundles/countries-bundle.php:27
msgid "Armenia"
msgstr "Armênia"

#: includes/bundles/countries-bundle.php:28
msgid "Aruba"
msgstr "Aruba"

#: includes/bundles/countries-bundle.php:29
msgid "Australia"
msgstr "Austrália"

#: includes/bundles/countries-bundle.php:30
msgid "Austria"
msgstr "Áustria"

#: includes/bundles/countries-bundle.php:31
msgid "Azerbaijan"
msgstr "Azerbaijão"

#: includes/bundles/countries-bundle.php:32
msgid "Bahamas"
msgstr "Bahamas"

#: includes/bundles/countries-bundle.php:33
msgid "Bahrain"
msgstr "Bahrein"

#: includes/bundles/countries-bundle.php:34
msgid "Bangladesh"
msgstr "Bangladesh"

#: includes/bundles/countries-bundle.php:35
msgid "Barbados"
msgstr "Barbados"

#: includes/bundles/countries-bundle.php:36
msgid "Belarus"
msgstr "Bielorrússia"

#: includes/bundles/countries-bundle.php:37
msgid "Belgium"
msgstr "Bélgica"

#: includes/bundles/countries-bundle.php:38
msgid "Belau"
msgstr "Belau"

#: includes/bundles/countries-bundle.php:39
msgid "Belize"
msgstr "Belize"

#: includes/bundles/countries-bundle.php:40
msgid "Benin"
msgstr "Benim"

#: includes/bundles/countries-bundle.php:41
msgid "Bermuda"
msgstr "Bermudas"

#: includes/bundles/countries-bundle.php:42
msgid "Bhutan"
msgstr "Butão"

#: includes/bundles/countries-bundle.php:43
msgid "Bolivia"
msgstr "Bolívia"

#: includes/bundles/countries-bundle.php:44
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius e Saba"

#: includes/bundles/countries-bundle.php:45
msgid "Bosnia and Herzegovina"
msgstr "Bósnia e Herzegovina"

#: includes/bundles/countries-bundle.php:46
msgid "Botswana"
msgstr "Botsuana"

#: includes/bundles/countries-bundle.php:47
msgid "Bouvet Island"
msgstr "Ilha Bouve"

#: includes/bundles/countries-bundle.php:48
msgid "Brazil"
msgstr "Brasil"

#: includes/bundles/countries-bundle.php:49
msgid "British Indian Ocean Territory"
msgstr "Território britânico do Oceano Índico"

#: includes/bundles/countries-bundle.php:50
msgid "British Virgin Islands"
msgstr "Ilhas Virgens Britânicas"

#: includes/bundles/countries-bundle.php:51
msgid "Brunei"
msgstr "Brunei"

#: includes/bundles/countries-bundle.php:52
msgid "Bulgaria"
msgstr "Bulgária"

#: includes/bundles/countries-bundle.php:53
msgid "Burkina Faso"
msgstr "Burquina Faso"

#: includes/bundles/countries-bundle.php:54
msgid "Burundi"
msgstr "Burundi"

#: includes/bundles/countries-bundle.php:55
msgid "Cambodia"
msgstr "Camboja"

#: includes/bundles/countries-bundle.php:56
msgid "Cameroon"
msgstr "Camarões"

#: includes/bundles/countries-bundle.php:57
msgid "Canada"
msgstr "Canadá"

#: includes/bundles/countries-bundle.php:58
msgid "Cape Verde"
msgstr "Сabo Verde"

#: includes/bundles/countries-bundle.php:59
msgid "Cayman Islands"
msgstr "Ilhas Cayman"

#: includes/bundles/countries-bundle.php:60
msgid "Central African Republic"
msgstr "República Centro-Africana"

#: includes/bundles/countries-bundle.php:61
msgid "Chad"
msgstr "Chade"

#: includes/bundles/countries-bundle.php:62
msgid "Chile"
msgstr "Chile"

#: includes/bundles/countries-bundle.php:63
msgid "China"
msgstr "China"

#: includes/bundles/countries-bundle.php:64
msgid "Christmas Island"
msgstr "Ilha do Natal"

#: includes/bundles/countries-bundle.php:65
msgid "Cocos (Keeling) Islands"
msgstr "Ilhas Cocos (Keeling)"

#: includes/bundles/countries-bundle.php:66
msgid "Colombia"
msgstr "Colômbia"

#: includes/bundles/countries-bundle.php:67
msgid "Comoros"
msgstr "Comores"

#: includes/bundles/countries-bundle.php:68
msgid "Congo (Brazzaville)"
msgstr "Congo - Brazzaville"

#: includes/bundles/countries-bundle.php:69
msgid "Congo (Kinshasa)"
msgstr "Congo - Kinshasa"

#: includes/bundles/countries-bundle.php:70
msgid "Cook Islands"
msgstr "Ilhas Cook"

#: includes/bundles/countries-bundle.php:71
msgid "Costa Rica"
msgstr "Costa Rica"

#: includes/bundles/countries-bundle.php:72
msgid "Croatia"
msgstr "Croácia"

#: includes/bundles/countries-bundle.php:73
msgid "Cuba"
msgstr "Cuba"

#: includes/bundles/countries-bundle.php:74
msgid "Cura&ccedil;ao"
msgstr "Curaçao"

#: includes/bundles/countries-bundle.php:75
msgid "Cyprus"
msgstr "Chipre"

#: includes/bundles/countries-bundle.php:76
msgid "Czech Republic"
msgstr "República Checa"

#: includes/bundles/countries-bundle.php:77
msgid "Denmark"
msgstr "Dinamarca"

#: includes/bundles/countries-bundle.php:78
msgid "Djibouti"
msgstr "Jibuti"

#: includes/bundles/countries-bundle.php:79
msgid "Dominica"
msgstr "Dominica"

#: includes/bundles/countries-bundle.php:80
msgid "Dominican Republic"
msgstr "República Dominicana"

#: includes/bundles/countries-bundle.php:81
msgid "Ecuador"
msgstr "Equador"

#: includes/bundles/countries-bundle.php:82
msgid "Egypt"
msgstr "Egito"

#: includes/bundles/countries-bundle.php:83
msgid "El Salvador"
msgstr "República do Salvador"

#: includes/bundles/countries-bundle.php:84
msgid "Equatorial Guinea"
msgstr "Guiné Equatorial"

#: includes/bundles/countries-bundle.php:85
msgid "Eritrea"
msgstr "Eritreia"

#: includes/bundles/countries-bundle.php:86
msgid "Estonia"
msgstr "Estônia"

#: includes/bundles/countries-bundle.php:87
msgid "Ethiopia"
msgstr "Etiópia"

#: includes/bundles/countries-bundle.php:88
msgid "Falkland Islands"
msgstr "Ilhas Falkland"

#: includes/bundles/countries-bundle.php:89
msgid "Faroe Islands"
msgstr "Ilhas Faroe"

#: includes/bundles/countries-bundle.php:90
msgid "Fiji"
msgstr "Fiji"

#: includes/bundles/countries-bundle.php:91
msgid "Finland"
msgstr "Finlândia"

#: includes/bundles/countries-bundle.php:92
msgid "France"
msgstr "França"

#: includes/bundles/countries-bundle.php:93
msgid "French Guiana"
msgstr "Guiana Francesa"

#: includes/bundles/countries-bundle.php:94
msgid "French Polynesia"
msgstr "Polinésia Francesa"

#: includes/bundles/countries-bundle.php:95
msgid "French Southern Territories"
msgstr "Territórios Franceses do Sul"

#: includes/bundles/countries-bundle.php:96
msgid "Gabon"
msgstr "Gabão"

#: includes/bundles/countries-bundle.php:97
msgid "Gambia"
msgstr "Gâmbia"

#: includes/bundles/countries-bundle.php:98
msgid "Georgia"
msgstr "Geórgia"

#: includes/bundles/countries-bundle.php:99
msgid "Germany"
msgstr "Alemanha"

#: includes/bundles/countries-bundle.php:100
msgid "Ghana"
msgstr "Gana"

#: includes/bundles/countries-bundle.php:101
msgid "Gibraltar"
msgstr "Gibraltar"

#: includes/bundles/countries-bundle.php:102
msgid "Greece"
msgstr "Grécia"

#: includes/bundles/countries-bundle.php:103
msgid "Greenland"
msgstr "Groelândia"

#: includes/bundles/countries-bundle.php:104
msgid "Grenada"
msgstr "Granada"

#: includes/bundles/countries-bundle.php:105
msgid "Guadeloupe"
msgstr "Guadalupe"

#: includes/bundles/countries-bundle.php:106
msgid "Guam"
msgstr "Guão"

#: includes/bundles/countries-bundle.php:107
msgid "Guatemala"
msgstr "Guatemala"

#: includes/bundles/countries-bundle.php:108
msgid "Guernsey"
msgstr "Guernesei"

#: includes/bundles/countries-bundle.php:109
msgid "Guinea"
msgstr "Guiné"

#: includes/bundles/countries-bundle.php:110
msgid "Guinea-Bissau"
msgstr "Guiné-Bissau"

#: includes/bundles/countries-bundle.php:111
msgid "Guyana"
msgstr "Guiana"

#: includes/bundles/countries-bundle.php:112
msgid "Haiti"
msgstr "Haiti"

#: includes/bundles/countries-bundle.php:113
msgid "Heard Island and McDonald Islands"
msgstr "Ilha Heard e Ilhas McDonald"

#: includes/bundles/countries-bundle.php:114
msgid "Honduras"
msgstr "Honduras"

#: includes/bundles/countries-bundle.php:115
msgid "Hong Kong"
msgstr "Hong Kong"

#: includes/bundles/countries-bundle.php:116
msgid "Hungary"
msgstr "Hungria"

#: includes/bundles/countries-bundle.php:117
msgid "Iceland"
msgstr "Islândia"

#: includes/bundles/countries-bundle.php:118
msgid "India"
msgstr "Índia"

#: includes/bundles/countries-bundle.php:119
msgid "Indonesia"
msgstr "Indonésia"

#: includes/bundles/countries-bundle.php:120
msgid "Iran"
msgstr "Irã"

#: includes/bundles/countries-bundle.php:121
msgid "Iraq"
msgstr "Iraque"

#: includes/bundles/countries-bundle.php:122
msgid "Ireland"
msgstr "Irlanda"

#: includes/bundles/countries-bundle.php:123
msgid "Isle of Man"
msgstr "Ilha do Homem"

#: includes/bundles/countries-bundle.php:124
msgid "Israel"
msgstr "Israel"

#: includes/bundles/countries-bundle.php:125
msgid "Italy"
msgstr "Itália"

#: includes/bundles/countries-bundle.php:126
msgid "Ivory Coast"
msgstr "Costa do Marfim"

#: includes/bundles/countries-bundle.php:127
msgid "Jamaica"
msgstr "Jamaica"

#: includes/bundles/countries-bundle.php:128
msgid "Japan"
msgstr "Japão"

#: includes/bundles/countries-bundle.php:129
msgid "Jersey"
msgstr "Jersey"

#: includes/bundles/countries-bundle.php:130
msgid "Jordan"
msgstr "Jordânia"

#: includes/bundles/countries-bundle.php:131
msgid "Kazakhstan"
msgstr "Cazaquistão"

#: includes/bundles/countries-bundle.php:132
msgid "Kenya"
msgstr "Quênia"

#: includes/bundles/countries-bundle.php:133
msgid "Kiribati"
msgstr "Quiribati"

#: includes/bundles/countries-bundle.php:134
msgid "Kuwait"
msgstr "Kuweit"

#: includes/bundles/countries-bundle.php:135
msgid "Kyrgyzstan"
msgstr "Quirguistão"

#: includes/bundles/countries-bundle.php:136
msgid "Laos"
msgstr "Laos"

#: includes/bundles/countries-bundle.php:137
msgid "Latvia"
msgstr "Letônia"

#: includes/bundles/countries-bundle.php:138
msgid "Lebanon"
msgstr "Líbano"

#: includes/bundles/countries-bundle.php:139
msgid "Lesotho"
msgstr "Lesoto"

#: includes/bundles/countries-bundle.php:140
msgid "Liberia"
msgstr "Libéria"

#: includes/bundles/countries-bundle.php:141
msgid "Libya"
msgstr "Líbia"

#: includes/bundles/countries-bundle.php:142
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: includes/bundles/countries-bundle.php:143
msgid "Lithuania"
msgstr "Lituânia"

#: includes/bundles/countries-bundle.php:144
msgid "Luxembourg"
msgstr "Luxemburgo"

#: includes/bundles/countries-bundle.php:145
msgid "Macao S.A.R., China"
msgstr "Macau S.A.R., China"

#: includes/bundles/countries-bundle.php:146
msgid "Macedonia"
msgstr "Macedônia"

#: includes/bundles/countries-bundle.php:147
msgid "Madagascar"
msgstr "Madagáscar"

#: includes/bundles/countries-bundle.php:148
msgid "Malawi"
msgstr "Malavi"

#: includes/bundles/countries-bundle.php:149
msgid "Malaysia"
msgstr "Malásia"

#: includes/bundles/countries-bundle.php:150
msgid "Maldives"
msgstr "Maldivas"

#: includes/bundles/countries-bundle.php:151
msgid "Mali"
msgstr "Mali"

#: includes/bundles/countries-bundle.php:152
msgid "Malta"
msgstr "Malta"

#: includes/bundles/countries-bundle.php:153
msgid "Marshall Islands"
msgstr "Ilhas Marshall"

#: includes/bundles/countries-bundle.php:154
msgid "Martinique"
msgstr "Martinica"

#: includes/bundles/countries-bundle.php:155
msgid "Mauritania"
msgstr "Mauritânia"

#: includes/bundles/countries-bundle.php:156
msgid "Mauritius"
msgstr "Ilhas Maurício"

#: includes/bundles/countries-bundle.php:157
msgid "Mayotte"
msgstr "Maiote"

#: includes/bundles/countries-bundle.php:158
msgid "Mexico"
msgstr "México"

#: includes/bundles/countries-bundle.php:159
msgid "Micronesia"
msgstr "Micronésia"

#: includes/bundles/countries-bundle.php:160
msgid "Moldova"
msgstr "Moldávia"

#: includes/bundles/countries-bundle.php:161
msgid "Monaco"
msgstr "Mônaco"

#: includes/bundles/countries-bundle.php:162
msgid "Mongolia"
msgstr "Mongólia"

#: includes/bundles/countries-bundle.php:163
msgid "Montenegro"
msgstr "Montenegro"

#: includes/bundles/countries-bundle.php:164
msgid "Montserrat"
msgstr "Montserrat"

#: includes/bundles/countries-bundle.php:165
msgid "Morocco"
msgstr "Marrocos"

#: includes/bundles/countries-bundle.php:166
msgid "Mozambique"
msgstr "Moçambique"

#: includes/bundles/countries-bundle.php:167
msgid "Myanmar"
msgstr "Mianmar"

#: includes/bundles/countries-bundle.php:168
msgid "Namibia"
msgstr "Namíbia"

#: includes/bundles/countries-bundle.php:169
msgid "Nauru"
msgstr "Nauru"

#: includes/bundles/countries-bundle.php:170
msgid "Nepal"
msgstr "Nepal"

#: includes/bundles/countries-bundle.php:171
msgid "Netherlands"
msgstr "Holanda"

#: includes/bundles/countries-bundle.php:172
msgid "New Caledonia"
msgstr "Nova Caledônia"

#: includes/bundles/countries-bundle.php:173
msgid "New Zealand"
msgstr "Nova Zelândia"

#: includes/bundles/countries-bundle.php:174
msgid "Nicaragua"
msgstr "Nicarágua"

#: includes/bundles/countries-bundle.php:175
msgid "Niger"
msgstr "Níger"

#: includes/bundles/countries-bundle.php:176
msgid "Nigeria"
msgstr "Nigéria"

#: includes/bundles/countries-bundle.php:177
msgid "Niue"
msgstr "Niue"

#: includes/bundles/countries-bundle.php:178
msgid "Norfolk Island"
msgstr "Ilha de Norfolk"

#: includes/bundles/countries-bundle.php:179
msgid "Northern Mariana Islands"
msgstr "Ilhas Marianas do Norte"

#: includes/bundles/countries-bundle.php:180
msgid "North Korea"
msgstr "Coreia do Norte"

#: includes/bundles/countries-bundle.php:181
msgid "Norway"
msgstr "Noruega"

#: includes/bundles/countries-bundle.php:182
msgid "Oman"
msgstr "Omã"

#: includes/bundles/countries-bundle.php:183
msgid "Pakistan"
msgstr "Paquistão"

#: includes/bundles/countries-bundle.php:184
msgid "Palestinian Territory"
msgstr "Território Palestino"

#: includes/bundles/countries-bundle.php:185
msgid "Panama"
msgstr "Panamá"

#: includes/bundles/countries-bundle.php:186
msgid "Papua New Guinea"
msgstr "Papua Nova Guiné"

#: includes/bundles/countries-bundle.php:187
msgid "Paraguay"
msgstr "Paraguai"

#: includes/bundles/countries-bundle.php:188
#: includes/settings/main-settings.php:37
msgid "Peru"
msgstr "Peru"

#: includes/bundles/countries-bundle.php:189
msgid "Philippines"
msgstr "Filipinas"

#: includes/bundles/countries-bundle.php:190
msgid "Pitcairn"
msgstr "Ilhas Picárnia"

#: includes/bundles/countries-bundle.php:191
msgid "Poland"
msgstr "Polônia"

#: includes/bundles/countries-bundle.php:192
msgid "Portugal"
msgstr "Portugal"

#: includes/bundles/countries-bundle.php:193
msgid "Puerto Rico"
msgstr "Porto Rico"

#: includes/bundles/countries-bundle.php:194
msgid "Qatar"
msgstr "Catar"

#: includes/bundles/countries-bundle.php:195
msgid "Reunion"
msgstr "Кeunião"

#: includes/bundles/countries-bundle.php:196
msgid "Romania"
msgstr "Romênia"

#: includes/bundles/countries-bundle.php:197
msgid "Russia"
msgstr "Rússia"

#: includes/bundles/countries-bundle.php:198
msgid "Rwanda"
msgstr "Ruanda"

#: includes/bundles/countries-bundle.php:199
msgid "Saint Barth&eacute;lemy"
msgstr "São Bartolomeu"

#: includes/bundles/countries-bundle.php:200
msgid "Saint Helena"
msgstr "Santa Helena"

#: includes/bundles/countries-bundle.php:201
msgid "Saint Kitts and Nevis"
msgstr "São Cristóvão e Nevis"

#: includes/bundles/countries-bundle.php:202
msgid "Saint Lucia"
msgstr "Santa Lúcia"

#: includes/bundles/countries-bundle.php:203
msgid "Saint Martin (French part)"
msgstr "São Martinho (parte francesa)"

#: includes/bundles/countries-bundle.php:204
msgid "Saint Martin (Dutch part)"
msgstr "Ilha de São Martinho (parte holandesa)"

#: includes/bundles/countries-bundle.php:205
msgid "Saint Pierre and Miquelon"
msgstr "São Pedro e Miquelon"

#: includes/bundles/countries-bundle.php:206
msgid "Saint Vincent and the Grenadines"
msgstr "São Vicente e Granadinas"

#: includes/bundles/countries-bundle.php:207
msgid "San Marino"
msgstr "São Marino"

#: includes/bundles/countries-bundle.php:208
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "São Tomé e Príncipe"

#: includes/bundles/countries-bundle.php:209
msgid "Saudi Arabia"
msgstr "Arábia Saudita"

#: includes/bundles/countries-bundle.php:210
msgid "Senegal"
msgstr "Senegal"

#: includes/bundles/countries-bundle.php:211
msgid "Serbia"
msgstr "Sérvia"

#: includes/bundles/countries-bundle.php:212
msgid "Seychelles"
msgstr "Seicheles"

#: includes/bundles/countries-bundle.php:213
msgid "Sierra Leone"
msgstr "Serra Leoa"

#: includes/bundles/countries-bundle.php:214
msgid "Singapore"
msgstr "Cingapura"

#: includes/bundles/countries-bundle.php:215
msgid "Slovakia"
msgstr "Eslováquia"

#: includes/bundles/countries-bundle.php:216
msgid "Slovenia"
msgstr "Eslovênia"

#: includes/bundles/countries-bundle.php:217
msgid "Solomon Islands"
msgstr "Ilhas Salomão"

#: includes/bundles/countries-bundle.php:218
msgid "Somalia"
msgstr "Somália"

#: includes/bundles/countries-bundle.php:219
msgid "South Africa"
msgstr "África do Sul"

#: includes/bundles/countries-bundle.php:220
msgid "South Georgia/Sandwich Islands"
msgstr "Geórgia do Sul/Ilhas Sanduiche"

#: includes/bundles/countries-bundle.php:221
msgid "South Korea"
msgstr "Coreia do Sul"

#: includes/bundles/countries-bundle.php:222
msgid "South Sudan"
msgstr "Sudão do Sul"

#: includes/bundles/countries-bundle.php:223
msgid "Spain"
msgstr "Espanha"

#: includes/bundles/countries-bundle.php:224
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: includes/bundles/countries-bundle.php:225
msgid "Sudan"
msgstr "Sudão"

#: includes/bundles/countries-bundle.php:226
msgid "Suriname"
msgstr "Suriname"

#: includes/bundles/countries-bundle.php:227
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard e Jan Mayen"

#: includes/bundles/countries-bundle.php:228
msgid "Swaziland"
msgstr "Suazilândia"

#: includes/bundles/countries-bundle.php:229
msgid "Sweden"
msgstr "Suécia"

#: includes/bundles/countries-bundle.php:230
msgid "Switzerland"
msgstr "Suíça"

#: includes/bundles/countries-bundle.php:231
msgid "Syria"
msgstr "Síria"

#: includes/bundles/countries-bundle.php:232
msgid "Taiwan"
msgstr "Taiwan"

#: includes/bundles/countries-bundle.php:233
msgid "Tajikistan"
msgstr "Tajiquistão"

#: includes/bundles/countries-bundle.php:234
msgid "Tanzania"
msgstr "Tanzânia"

#: includes/bundles/countries-bundle.php:235
msgid "Thailand"
msgstr "Tailândia"

#: includes/bundles/countries-bundle.php:236
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: includes/bundles/countries-bundle.php:237
msgid "Togo"
msgstr "Togo"

#: includes/bundles/countries-bundle.php:238
msgid "Tokelau"
msgstr "Toquelau"

#: includes/bundles/countries-bundle.php:239
msgid "Tonga"
msgstr "Tonga"

#: includes/bundles/countries-bundle.php:240
msgid "Trinidad and Tobago"
msgstr "Trindade e Tobago"

#: includes/bundles/countries-bundle.php:241
msgid "Tunisia"
msgstr "Tunísia"

#: includes/bundles/countries-bundle.php:242
msgid "Turkey"
msgstr "Turquia"

#: includes/bundles/countries-bundle.php:243
msgid "Turkmenistan"
msgstr "Turquemenistão"

#: includes/bundles/countries-bundle.php:244
msgid "Turks and Caicos Islands"
msgstr "Ilhas Turcas e Caicos"

#: includes/bundles/countries-bundle.php:245
msgid "Tuvalu"
msgstr "Tuvalu"

#: includes/bundles/countries-bundle.php:246
msgid "Uganda"
msgstr "Uganda"

#: includes/bundles/countries-bundle.php:247
msgid "Ukraine"
msgstr "Ucrânia"

#: includes/bundles/countries-bundle.php:248
msgid "United Arab Emirates"
msgstr "Emirados Árabes Unidos"

#: includes/bundles/countries-bundle.php:249
msgid "United Kingdom (UK)"
msgstr "Reino Unido"

#: includes/bundles/countries-bundle.php:250
msgid "United States (US)"
msgstr "Estados Unidos (EUA)"

#: includes/bundles/countries-bundle.php:251
msgid "United States (US) Minor Outlying Islands"
msgstr "Ilhas Menores Distantes dos Estados Unidos (EUA)"

#: includes/bundles/countries-bundle.php:252
msgid "United States (US) Virgin Islands"
msgstr "Ilhas Virgens, Estados Unidos (EUA)"

#: includes/bundles/countries-bundle.php:253
msgid "Uruguay"
msgstr "Uruguai"

#: includes/bundles/countries-bundle.php:254
msgid "Uzbekistan"
msgstr "Uzbequistão"

#: includes/bundles/countries-bundle.php:255
msgid "Vanuatu"
msgstr "Vanuatu"

#: includes/bundles/countries-bundle.php:256
msgid "Vatican"
msgstr "Vaticano"

#: includes/bundles/countries-bundle.php:257
msgid "Venezuela"
msgstr "Venezuela"

#: includes/bundles/countries-bundle.php:258
msgid "Vietnam"
msgstr "Vietnã"

#: includes/bundles/countries-bundle.php:259
msgid "Wallis and Futuna"
msgstr "Wallis e Futuna"

#: includes/bundles/countries-bundle.php:260
msgid "Western Sahara"
msgstr "Saara Ocidental"

#: includes/bundles/countries-bundle.php:261
msgid "Samoa"
msgstr "Samoa"

#: includes/bundles/countries-bundle.php:262
msgid "Yemen"
msgstr "Lémen"

#: includes/bundles/countries-bundle.php:263
msgid "Zambia"
msgstr "Zâmbia"

#: includes/bundles/countries-bundle.php:264
msgid "Zimbabwe"
msgstr "Zimbábue"

#: includes/bundles/currency-bundle.php:17
msgid "Euro"
msgstr "Euro"

#: includes/bundles/currency-bundle.php:18
msgid "United States (US) dollar"
msgstr "Dólar dos Estados Unidos (EUA)"

#: includes/bundles/currency-bundle.php:19
msgid "Pound sterling"
msgstr "Libras Esterlinas"

#: includes/bundles/currency-bundle.php:20
msgid "United Arab Emirates dirham"
msgstr "Dirham dos Emirados Árabes"

#: includes/bundles/currency-bundle.php:21
msgid "Afghan afghani"
msgstr "Afgane afegane"

#: includes/bundles/currency-bundle.php:22
msgid "Albanian lek"
msgstr "Lek albanês"

#: includes/bundles/currency-bundle.php:23
msgid "Armenian dram"
msgstr "Dram armênio"

#: includes/bundles/currency-bundle.php:24
msgid "Netherlands Antillean guilder"
msgstr "Florim das Antilhas Neerlandesas"

#: includes/bundles/currency-bundle.php:25
msgid "Angolan kwanza"
msgstr "Kwanza angolano"

#: includes/bundles/currency-bundle.php:26
msgid "Argentine peso"
msgstr "Peso Argentino"

#: includes/bundles/currency-bundle.php:27
msgid "Australian dollar"
msgstr "Dólares Australianos"

#: includes/bundles/currency-bundle.php:28
msgid "Aruban florin"
msgstr "Florin arubano"

#: includes/bundles/currency-bundle.php:29
msgid "Azerbaijani manat"
msgstr "Manate azeri"

#: includes/bundles/currency-bundle.php:30
msgid "Bosnia and Herzegovina convertible mark"
msgstr "Marca conversível da Bósnia e Herzegovina"

#: includes/bundles/currency-bundle.php:31
msgid "Barbadian dollar"
msgstr "Dólar barbadian"

#: includes/bundles/currency-bundle.php:32
msgid "Bangladeshi taka"
msgstr "Taka Bangladesh"

#: includes/bundles/currency-bundle.php:33
msgid "Bulgarian lev"
msgstr "Lev Búlgaro"

#: includes/bundles/currency-bundle.php:34
msgid "Bahraini dinar"
msgstr "Dinar bareinita"

#: includes/bundles/currency-bundle.php:35
msgid "Burundian franc"
msgstr "Franco burundiano"

#: includes/bundles/currency-bundle.php:36
msgid "Bermudian dollar"
msgstr "Dólar bermudense"

#: includes/bundles/currency-bundle.php:37
msgid "Brunei dollar"
msgstr "Dólar de Brunei"

#: includes/bundles/currency-bundle.php:38
msgid "Bolivian boliviano"
msgstr "Boliviano boliviano"

#: includes/bundles/currency-bundle.php:39
msgid "Brazilian real"
msgstr "Real Brasileiro"

#: includes/bundles/currency-bundle.php:40
msgid "Bahamian dollar"
msgstr "Dólar bahamense"

#: includes/bundles/currency-bundle.php:41
msgid "Bitcoin"
msgstr "Bitcoin"

#: includes/bundles/currency-bundle.php:42
msgid "Bhutanese ngultrum"
msgstr "Ngultrum butanês"

#: includes/bundles/currency-bundle.php:43
msgid "Botswana pula"
msgstr "Pula botsuanense"

#: includes/bundles/currency-bundle.php:44
msgid "Belarusian ruble (old)"
msgstr "Rublo bielo-russo (antigo)"

#: includes/bundles/currency-bundle.php:45
msgid "Belarusian ruble"
msgstr "Rublo bielo-russo"

#: includes/bundles/currency-bundle.php:46
msgid "Belize dollar"
msgstr "Dólar belizenho"

#: includes/bundles/currency-bundle.php:47
msgid "Canadian dollar"
msgstr "Dólares Canadenses"

#: includes/bundles/currency-bundle.php:48
msgid "Congolese franc"
msgstr "Franco congolês"

#: includes/bundles/currency-bundle.php:49
msgid "Swiss franc"
msgstr "Franco Suíço"

#: includes/bundles/currency-bundle.php:50
msgid "Chilean peso"
msgstr "Peso Chileno"

#: includes/bundles/currency-bundle.php:51
msgid "Chinese yuan"
msgstr "Yuan Chinês"

#: includes/bundles/currency-bundle.php:52
msgid "Colombian peso"
msgstr "Peso Colombiano"

#: includes/bundles/currency-bundle.php:53
msgid "Costa Rican col&oacute;n"
msgstr "Colón costa-riquenho"

#: includes/bundles/currency-bundle.php:54
msgid "Cuban convertible peso"
msgstr "Peso cubano conversível"

#: includes/bundles/currency-bundle.php:55
msgid "Cuban peso"
msgstr "Peso cubano"

#: includes/bundles/currency-bundle.php:56
msgid "Cape Verdean escudo"
msgstr "Escudo caboverdiano"

#: includes/bundles/currency-bundle.php:57
msgid "Czech koruna"
msgstr "Coroa Checa"

#: includes/bundles/currency-bundle.php:58
msgid "Djiboutian franc"
msgstr "Franco djibutiano"

#: includes/bundles/currency-bundle.php:59
msgid "Danish krone"
msgstr "Coroa Dinamarquesa"

#: includes/bundles/currency-bundle.php:60
msgid "Dominican peso"
msgstr "Peso dominicano"

#: includes/bundles/currency-bundle.php:61
msgid "Algerian dinar"
msgstr "Dinar argelino"

#: includes/bundles/currency-bundle.php:62
msgid "Egyptian pound"
msgstr "Libra Egípcia"

#: includes/bundles/currency-bundle.php:63
msgid "Eritrean nakfa"
msgstr "Nakfa eritreia"

#: includes/bundles/currency-bundle.php:64
msgid "Ethiopian birr"
msgstr "Birre etíope"

#: includes/bundles/currency-bundle.php:65
msgid "Fijian dollar"
msgstr "Dólar fijiano"

#: includes/bundles/currency-bundle.php:66
msgid "Falkland Islands pound"
msgstr "Libra das Ilhas Malvinas"

#: includes/bundles/currency-bundle.php:67
msgid "Georgian lari"
msgstr "Lari georgiano"

#: includes/bundles/currency-bundle.php:68
msgid "Guernsey pound"
msgstr "Libra de Guernsey"

#: includes/bundles/currency-bundle.php:69
msgid "Ghana cedi"
msgstr "Cedi ganês"

#: includes/bundles/currency-bundle.php:70
msgid "Gibraltar pound"
msgstr "Libra de Gibraltar"

#: includes/bundles/currency-bundle.php:71
msgid "Gambian dalasi"
msgstr "Dalasi gambiano"

#: includes/bundles/currency-bundle.php:72
msgid "Guinean franc"
msgstr "Franco guineense"

#: includes/bundles/currency-bundle.php:73
msgid "Guatemalan quetzal"
msgstr "Quetzal guatemalteco"

#: includes/bundles/currency-bundle.php:74
msgid "Guyanese dollar"
msgstr "Dólar guianense"

#: includes/bundles/currency-bundle.php:75
msgid "Hong Kong dollar"
msgstr "Dólar de Hong Kong"

#: includes/bundles/currency-bundle.php:76
msgid "Honduran lempira"
msgstr "Lempira hondurenha"

#: includes/bundles/currency-bundle.php:77
msgid "Croatian kuna"
msgstr "Kuna Croata"

#: includes/bundles/currency-bundle.php:78
msgid "Haitian gourde"
msgstr "Gourde haitiana"

#: includes/bundles/currency-bundle.php:79
msgid "Hungarian forint"
msgstr "Forint Húngaro"

#: includes/bundles/currency-bundle.php:80
msgid "Indonesian rupiah"
msgstr "Rúpia da Indonésia"

#: includes/bundles/currency-bundle.php:81
msgid "Israeli new shekel"
msgstr "Novo shekel israelense"

#: includes/bundles/currency-bundle.php:82
msgid "Manx pound"
msgstr "Libra manx"

#: includes/bundles/currency-bundle.php:83
msgid "Indian rupee"
msgstr "Rupia Indiana"

#: includes/bundles/currency-bundle.php:84
msgid "Iraqi dinar"
msgstr "Dinar iraquiano"

#: includes/bundles/currency-bundle.php:85
msgid "Iranian rial"
msgstr "Rial iraniano"

#: includes/bundles/currency-bundle.php:86
msgid "Iranian toman"
msgstr "Toman iraniano"

#: includes/bundles/currency-bundle.php:87
msgid "Icelandic kr&oacute;na"
msgstr "Coroa islandesa"

#: includes/bundles/currency-bundle.php:88
msgid "Jersey pound"
msgstr "Libra de Jersey"

#: includes/bundles/currency-bundle.php:89
msgid "Jamaican dollar"
msgstr "Dólar jamaicano"

#: includes/bundles/currency-bundle.php:90
msgid "Jordanian dinar"
msgstr "Dinar jordano"

#: includes/bundles/currency-bundle.php:91
msgid "Japanese yen"
msgstr "Yen japonês"

#: includes/bundles/currency-bundle.php:92
msgid "Kenyan shilling"
msgstr "Xelim Queniano"

#: includes/bundles/currency-bundle.php:93
msgid "Kyrgyzstani som"
msgstr "Som quirguistanês"

#: includes/bundles/currency-bundle.php:94
msgid "Cambodian riel"
msgstr "Riel Cambojano"

#: includes/bundles/currency-bundle.php:95
msgid "Comorian franc"
msgstr "Franco comoriano"

#: includes/bundles/currency-bundle.php:96
msgid "North Korean won"
msgstr "Won norte-coreano"

#: includes/bundles/currency-bundle.php:97
msgid "South Korean won"
msgstr "Won da Coreia do Sul"

#: includes/bundles/currency-bundle.php:98
msgid "Kuwaiti dinar"
msgstr "Dinar Kuwaitiano"

#: includes/bundles/currency-bundle.php:99
msgid "Cayman Islands dollar"
msgstr "Dólar das Ilhas Cayman"

#: includes/bundles/currency-bundle.php:100
msgid "Kazakhstani tenge"
msgstr "Tenge cazaque"

#: includes/bundles/currency-bundle.php:101
msgid "Lao kip"
msgstr "Kip do Laos"

#: includes/bundles/currency-bundle.php:102
msgid "Lebanese pound"
msgstr "Libra Libanesa"

#: includes/bundles/currency-bundle.php:103
msgid "Sri Lankan rupee"
msgstr "Rúpia do Sri Lanka"

#: includes/bundles/currency-bundle.php:104
msgid "Liberian dollar"
msgstr "Dólar Liberiano"

#: includes/bundles/currency-bundle.php:105
msgid "Lesotho loti"
msgstr "Loti lesotense"

#: includes/bundles/currency-bundle.php:106
msgid "Libyan dinar"
msgstr "Dinar líbio"

#: includes/bundles/currency-bundle.php:107
msgid "Moroccan dirham"
msgstr "Dirham marroquino"

#: includes/bundles/currency-bundle.php:108
msgid "Moldovan leu"
msgstr "Leu moldávio"

#: includes/bundles/currency-bundle.php:109
msgid "Malagasy ariary"
msgstr "Ariary malgaxe"

#: includes/bundles/currency-bundle.php:110
msgid "Macedonian denar"
msgstr "Dinar macedônio"

#: includes/bundles/currency-bundle.php:111
msgid "Burmese kyat"
msgstr "Quiate Birmanês"

#: includes/bundles/currency-bundle.php:112
msgid "Mongolian t&ouml;gr&ouml;g"
msgstr "Mongolian t&ouml;gr&ouml;g"

#: includes/bundles/currency-bundle.php:113
msgid "Macanese pataca"
msgstr "Pataca Macaense"

#: includes/bundles/currency-bundle.php:114
msgid "Mauritanian ouguiya"
msgstr "Ouguiya mauritana"

#: includes/bundles/currency-bundle.php:115
msgid "Mauritian rupee"
msgstr "Rupia Mauriciana"

#: includes/bundles/currency-bundle.php:116
msgid "Maldivian rufiyaa"
msgstr "Rúpia maldívia"

#: includes/bundles/currency-bundle.php:117
msgid "Malawian kwacha"
msgstr "Kwacha malauiano"

#: includes/bundles/currency-bundle.php:118
msgid "Mexican peso"
msgstr "Peso Mexicano"

#: includes/bundles/currency-bundle.php:119
msgid "Malaysian ringgit"
msgstr "Ringgits da Malásia"

#: includes/bundles/currency-bundle.php:120
msgid "Mozambican metical"
msgstr "Metical moçambicano"

#: includes/bundles/currency-bundle.php:121
msgid "Namibian dollar"
msgstr "Dólar namibiano"

#: includes/bundles/currency-bundle.php:122
msgid "Nigerian naira"
msgstr "Naira Nigeriano"

#: includes/bundles/currency-bundle.php:123
msgid "Nicaraguan c&oacute;rdoba"
msgstr "Córdoba nicaraguense"

#: includes/bundles/currency-bundle.php:124
msgid "Norwegian krone"
msgstr "Coroa Norueguesa"

#: includes/bundles/currency-bundle.php:125
msgid "Nepalese rupee"
msgstr "Rúpia nepalesa"

#: includes/bundles/currency-bundle.php:126
msgid "New Zealand dollar"
msgstr "Dólar da Nova Zelândia"

#: includes/bundles/currency-bundle.php:127
msgid "Omani rial"
msgstr "Rial omanense"

#: includes/bundles/currency-bundle.php:128
msgid "Panamanian balboa"
msgstr "Balboa panamenho"

#: includes/bundles/currency-bundle.php:129
msgid "Sol"
msgstr "Sol"

#: includes/bundles/currency-bundle.php:130
msgid "Papua New Guinean kina"
msgstr "Kina de Papua Nova Guiné"

#: includes/bundles/currency-bundle.php:131
msgid "Philippine peso"
msgstr "Pesos das Filipinas"

#: includes/bundles/currency-bundle.php:132
msgid "Pakistani rupee"
msgstr "Rupia Paquistanesa"

#: includes/bundles/currency-bundle.php:133
msgid "Polish z&#x142;oty"
msgstr "Polonês z&#x142;oty"

#: includes/bundles/currency-bundle.php:134
msgid "Transnistrian ruble"
msgstr "Rublo transnistriano"

#: includes/bundles/currency-bundle.php:135
msgid "Paraguayan guaran&iacute;"
msgstr "Guarani paraguaio"

#: includes/bundles/currency-bundle.php:136
msgid "Qatari riyal"
msgstr "Rial do Catar"

#: includes/bundles/currency-bundle.php:137
msgid "Romanian leu"
msgstr "Leu romeno"

#: includes/bundles/currency-bundle.php:138
msgid "Serbian dinar"
msgstr "Dinar sérvio"

#: includes/bundles/currency-bundle.php:139
msgid "Russian ruble"
msgstr "Rublo Russo"

#: includes/bundles/currency-bundle.php:140
msgid "Rwandan franc"
msgstr "Franco ruandês"

#: includes/bundles/currency-bundle.php:141
msgid "Saudi riyal"
msgstr "Riyal Saudita"

#: includes/bundles/currency-bundle.php:142
msgid "Solomon Islands dollar"
msgstr "Dólar das Ilhas Salomão"

#: includes/bundles/currency-bundle.php:143
msgid "Seychellois rupee"
msgstr "Rúpia seichelense"

#: includes/bundles/currency-bundle.php:144
msgid "Sudanese pound"
msgstr "Libra sudanesa"

#: includes/bundles/currency-bundle.php:145
msgid "Swedish krona"
msgstr "Coroa Sueca"

#: includes/bundles/currency-bundle.php:146
msgid "Singapore dollar"
msgstr "Dólar de Singapura"

#: includes/bundles/currency-bundle.php:147
msgid "Saint Helena pound"
msgstr "Libra de Santa Helena"

#: includes/bundles/currency-bundle.php:148
msgid "Sierra Leonean leone"
msgstr "Leone serra-leonês"

#: includes/bundles/currency-bundle.php:149
msgid "Somali shilling"
msgstr "Xelim somaliano"

#: includes/bundles/currency-bundle.php:150
msgid "Surinamese dollar"
msgstr "Dólar surinamês"

#: includes/bundles/currency-bundle.php:151
msgid "South Sudanese pound"
msgstr "Libra sul-sudanesa"

#: includes/bundles/currency-bundle.php:152
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe dobra"
msgstr "Coroa islandesa"

#: includes/bundles/currency-bundle.php:153
msgid "Syrian pound"
msgstr "Libra síria"

#: includes/bundles/currency-bundle.php:154
msgid "Swazi lilangeni"
msgstr "Lilangeni suazilandês"

#: includes/bundles/currency-bundle.php:155
msgid "Thai baht"
msgstr "Baht tailandês"

#: includes/bundles/currency-bundle.php:156
msgid "Tajikistani somoni"
msgstr "Somoni tajique"

#: includes/bundles/currency-bundle.php:157
msgid "Turkmenistan manat"
msgstr "Manat turcomeno"

#: includes/bundles/currency-bundle.php:158
msgid "Tunisian dinar"
msgstr "Dinar tunisiano"

#: includes/bundles/currency-bundle.php:159
msgid "Tongan pa&#x2bb;anga"
msgstr "Tongan pa&#x2bb;anga"

#: includes/bundles/currency-bundle.php:160
msgid "Turkish lira"
msgstr "Lira turca"

#: includes/bundles/currency-bundle.php:161
msgid "Trinidad and Tobago dollar"
msgstr "Dólar de Trinidad e Tobago"

#: includes/bundles/currency-bundle.php:162
msgid "New Taiwan dollar"
msgstr "Dólar taiwanês"

#: includes/bundles/currency-bundle.php:163
msgid "Tanzanian shilling"
msgstr "Shilling tanzaniano"

#: includes/bundles/currency-bundle.php:164
msgid "Ukrainian hryvnia"
msgstr "Hryvnia Ucranianas"

#: includes/bundles/currency-bundle.php:165
msgid "Ugandan shilling"
msgstr "Shilling ugandense"

#: includes/bundles/currency-bundle.php:166
msgid "Uruguayan peso"
msgstr "Peso uruguaio"

#: includes/bundles/currency-bundle.php:167
msgid "Uzbekistani som"
msgstr "Som uzbeque"

#: includes/bundles/currency-bundle.php:168
msgid "Venezuelan bol&iacute;var"
msgstr "Bolívar venezuelano"

#: includes/bundles/currency-bundle.php:169
msgid "Bol&iacute;var soberano"
msgstr "Bolívar soberano"

#: includes/bundles/currency-bundle.php:170
msgid "Vietnamese &#x111;&#x1ed3;ng"
msgstr "Vietnamese &#x111;&#x1ed3;ng"

#: includes/bundles/currency-bundle.php:171
msgid "Vanuatu vatu"
msgstr "Vatu de Vanuatu"

#: includes/bundles/currency-bundle.php:172
msgid "Samoan t&#x101;l&#x101;"
msgstr "Samoan t&#x101;l&#x101;"

#: includes/bundles/currency-bundle.php:173
msgid "Central African CFA franc"
msgstr "Franco CFA da África Central"

#: includes/bundles/currency-bundle.php:174
msgid "East Caribbean dollar"
msgstr "Dólar do Caribe Oriental"

#: includes/bundles/currency-bundle.php:175
msgid "West African CFA franc"
msgstr "Franco CFA da África Ocidental"

#: includes/bundles/currency-bundle.php:176
msgid "CFP franc"
msgstr "Franco CPF"

#: includes/bundles/currency-bundle.php:177
msgid "Yemeni rial"
msgstr "Rial iemenita"

#: includes/bundles/currency-bundle.php:178
msgid "South African rand"
msgstr "Rand sul-africano"

#: includes/bundles/currency-bundle.php:179
msgid "Zambian kwacha"
msgstr "Kwacha zambiano"

#: includes/bundles/currency-bundle.php:358
msgid "Before"
msgstr "Antes"

#: includes/bundles/currency-bundle.php:359
msgid "After"
msgstr "Apos"

#: includes/bundles/currency-bundle.php:360
msgid "Before with space"
msgstr "Antes com espaço"

#: includes/bundles/currency-bundle.php:361
msgid "After with space"
msgstr "Apos com espaço"

#: includes/bundles/customer-bundle.php:97
msgid "First name is required."
msgstr "O primeiro nome é requerido."

#: includes/bundles/customer-bundle.php:106
msgid "Last name is required."
msgstr "O sobrenome é requerido."

#: includes/bundles/customer-bundle.php:115
msgid "Email is required."
msgstr "O e-mail é requerido."

#: includes/bundles/customer-bundle.php:124
msgid "Phone is required."
msgstr "O telefone é requerido."

#: includes/bundles/customer-bundle.php:128
#: includes/views/shortcodes/checkout-view.php:650
msgid "Country of residence"
msgstr "País de residência"

#: includes/bundles/customer-bundle.php:133
msgid "Country is required."
msgstr "O país é requerido."

#: includes/bundles/customer-bundle.php:142
msgid "Address is required."
msgstr "O endereço é requerido."

#: includes/bundles/customer-bundle.php:151
msgid "City is required."
msgstr "A cidade é requerida."

#: includes/bundles/customer-bundle.php:160
msgid "State is required."
msgstr "O estado é requerido."

#: includes/bundles/customer-bundle.php:169
msgid "Postcode is required."
msgstr "O código postal é requerido."

#: includes/bundles/customer-bundle.php:178
msgid "Note is required."
msgstr "Nota é necessária."

#: includes/bundles/units-bundle.php:16
msgid "Square Meter"
msgstr "Metro quadrado"

#: includes/bundles/units-bundle.php:17
msgid "Square Foot"
msgstr "Pés quadrados"

#: includes/bundles/units-bundle.php:18
msgid "Square Yard"
msgstr "Jarda quadrada"

#: includes/bundles/units-bundle.php:21
msgid "m²"
msgstr ""

#: includes/bundles/units-bundle.php:22
msgid "ft²"
msgstr ""

#: includes/bundles/units-bundle.php:23
msgid "yd²"
msgstr ""

#: includes/core/helpers/price-helper.php:57
msgctxt "Zero price"
msgid "Free"
msgstr "Gratuito"

#. translators: Price per one night. Example: $99 per night
#: includes/core/helpers/price-helper.php:144
msgctxt "Price per one night. Example: $99 per night"
msgid "per night"
msgstr "por noite"

#. translators: Price for X nights. Example: $99 for 2 nights, $99 for 21 nights
#: includes/core/helpers/price-helper.php:156
msgctxt "Price for X nights. Example: $99 for 2 nights, $99 for 21 nights"
msgid "for %d nights"
msgid_plural "for %d nights"
msgstr[0] "por noite"
msgstr[1] "por %d noites"

#: includes/crons/cron-manager.php:112
msgid "User Approval Time setted in Hotel Booking Settings"
msgstr "A Configuração do Tempo de aprovação do usuário foi especificado em Configurações de Reserva do Hotel"

#: includes/crons/cron-manager.php:117
msgid "Pending Payment Time set in Hotel Booking Settings"
msgstr "O tempo pendente de pagamento foi especificado nas configurações de reserva do hotel"

#: includes/crons/cron-manager.php:122
msgid "Interval for automatic cleaning of synchronization logs."
msgstr ""

#: includes/crons/cron-manager.php:127
msgid "Once a week"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:73
#: templates/account/bookings.php:19
#: templates/account/bookings.php:70
#: templates/create-booking/search/search-form.php:42
#: templates/edit-booking/edit-dates.php:29
#: templates/shortcodes/search/search-form.php:35
msgid "Check-in"
msgstr "Checkin"

#: includes/csv/bookings/bookings-exporter-helper.php:74
#: templates/account/bookings.php:20
#: templates/account/bookings.php:73
#: templates/create-booking/search/search-form.php:62
#: templates/edit-booking/edit-dates.php:38
#: templates/shortcodes/search/search-form.php:55
msgid "Check-out"
msgstr "Checkout"

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/csv/bookings/bookings-exporter-helper.php:78
#: includes/post-types/rate-cpt.php:104
msgid "Rate"
msgstr "Taxa"

#: includes/csv/bookings/bookings-exporter-helper.php:79
msgid "Adults/Guests"
msgstr "Adultos/Convidados"

#: includes/csv/bookings/bookings-exporter-helper.php:91
#: includes/emails/templaters/reserved-rooms-templater.php:223
#: includes/views/edit-booking/checkout-view.php:164
#: includes/views/shortcodes/checkout-view.php:291
msgid "Full Guest Name"
msgstr "Nome completo do Hóspede"

#: includes/csv/bookings/bookings-exporter-helper.php:92
#: includes/views/booking-view.php:141
msgid "Accommodation Subtotal"
msgstr "Subtotal das acomodações"

#: includes/csv/bookings/bookings-exporter-helper.php:93
#: includes/post-types/coupon-cpt.php:72
#: includes/views/booking-view.php:150
msgid "Accommodation Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:94
#: includes/views/booking-view.php:160
msgid "Accommodation Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:96
#: includes/views/booking-view.php:186
msgid "Accommodation Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:98
#: includes/views/booking-view.php:225
msgid "Services Subtotal"
msgstr "Subtotal de Serviços"

#: includes/csv/bookings/bookings-exporter-helper.php:99
#: includes/views/booking-view.php:236
msgid "Services Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:100
#: includes/views/booking-view.php:248
msgid "Services Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:101
#: includes/views/booking-view.php:280
msgid "Service Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:103
#: includes/views/booking-view.php:312
msgid "Fees Subtotal"
msgstr "Subtotal de taxas"

#: includes/csv/bookings/bookings-exporter-helper.php:104
#: includes/views/booking-view.php:321
msgid "Fees Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:105
#: includes/views/booking-view.php:331
msgid "Fees Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:106
#: includes/views/booking-view.php:364
msgid "Fee Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:108
msgid "Discount"
msgstr "Desconto"

#: includes/csv/bookings/bookings-exporter-helper.php:109
#: includes/views/booking-view.php:451
#: templates/account/bookings.php:21
#: templates/account/bookings.php:76
msgid "Total"
msgstr "Total"

#: includes/csv/bookings/bookings-exporter-helper.php:110
msgid "Paid"
msgstr "Pago"

#: includes/csv/bookings/bookings-exporter-helper.php:111
#: includes/post-types/payment-cpt.php:129
#: includes/shortcodes/booking-confirmation-shortcode.php:284
msgid "Payment Details"
msgstr "Detalhes do Pagamento"

#: includes/csv/bookings/bookings-query.php:92
msgid "Please select columns to export."
msgstr "Por favor selecione as colunas que deseja exportar."

#: includes/csv/csv-export-handler.php:32
#: includes/payments/gateways/stripe-gateway.php:559
msgid "Nonce verification failed."
msgstr ""

#: includes/csv/csv-export-handler.php:50
msgid "The file does not exist."
msgstr "O ficheiro não existe."

#: includes/emails/abstract-email.php:441
msgid "Disable this email notification"
msgstr "Desativar esta notificação de e-mail"

#: includes/emails/abstract-email.php:449
msgid "Subject"
msgstr "Título"

#: includes/emails/abstract-email.php:461
msgid "Header"
msgstr "Cabeçalho"

#: includes/emails/abstract-email.php:473
msgid "Email Template"
msgstr "Template de Email"

#: includes/emails/abstract-email.php:570
msgid "\"%s\" email will not be sent: there is no customer email in the booking."
msgstr "\"%s\" o e-mail não será enviado: não há o e-mail do cliente na reserva."

#: includes/emails/abstract-email.php:594
msgid "Deprecated tags in header of %s"
msgstr "Há etiquetas descontinuadas no cabeçalho de %s"

#: includes/emails/abstract-email.php:597
msgid "Deprecated tags in subject of %s"
msgstr "Há etiquetas descontinuadas no tópico %s"

#: includes/emails/abstract-email.php:600
msgid "Deprecated tags in template of %s"
msgstr "Há etiquetas descontinuadas no tópico %s"

#: includes/emails/booking/admin/base-email.php:37
msgid "Recipients"
msgstr "Destinatários"

#: includes/emails/booking/admin/base-email.php:40
msgid "You can use multiple comma-separated emails"
msgstr ""

#: includes/emails/booking/admin/base-email.php:89
msgid "\"%s\" mail was sent to admin."
msgstr "\"%s\" e-mail foi enviado para administrador."

#: includes/emails/booking/admin/base-email.php:93
msgid "\"%s\" mail sending to admin is failed."
msgstr "\"%s\" email falhou ao enviado para administrador."

#: includes/emails/booking/admin/cancelled-email.php:8
msgid "Booking Cancelled"
msgstr "Reserva Cancelada"

#: includes/emails/booking/admin/cancelled-email.php:12
msgid "%site_title% - Booking #%booking_id% Cancelled"
msgstr "%site_title% - Reserva #%booking_id% Cancelada"

#: includes/emails/booking/admin/cancelled-email.php:16
msgid "Email that will be sent to Admin when customer cancels booking."
msgstr "Um e-mail será enviado ao administrador quando o cliente cancelar a reserva."

#: includes/emails/booking/admin/cancelled-email.php:20
#: includes/emails/booking/customer/cancelled-email.php:20
msgid "Cancelled Booking Email"
msgstr "E-mail de Reserva Cancelado"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:8
#: includes/emails/booking/admin/confirmed-email.php:8
#: includes/wizard.php:134
msgid "Booking Confirmed"
msgstr "Reserva Confirmada"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:12
#: includes/emails/booking/admin/confirmed-email.php:12
msgid "%site_title% - Booking #%booking_id% Confirmed"
msgstr "%site_title% - Reserva #%booking_id% Confirmada"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:16
msgid "Email that will be sent to Admin when payment is completed."
msgstr "O e-mail que será enviado ao administrador quando o pagamento for concluído."

#: includes/emails/booking/admin/confirmed-by-payment-email.php:20
msgid "Approved Booking Email (via payment)"
msgstr "E-mail de Reserva Aprovado (via pagamento)"

#: includes/emails/booking/admin/confirmed-email.php:16
msgid "Email that will be sent to Admin when customer confirms booking."
msgstr "Um e-mail será enviado ao administrador quando o cliente confirmar a reserva."

#: includes/emails/booking/admin/confirmed-email.php:20
#: includes/emails/booking/customer/approved-email.php:20
msgid "Approved Booking Email"
msgstr "E-mail de Reserva Aprovada"

#: includes/emails/booking/admin/pending-email.php:8
msgid "Confirm new booking"
msgstr "Confirmar nova reserva"

#: includes/emails/booking/admin/pending-email.php:12
msgid "%site_title% - New booking #%booking_id%"
msgstr "%site_title% - Nova reserva #%booking_id%"

#: includes/emails/booking/admin/pending-email.php:16
msgid "Email that will be sent to administrator after booking is placed."
msgstr "Um e-mail será enviado ao administrador após fazer a reserva."

#: includes/emails/booking/admin/pending-email.php:20
msgid "Pending Booking Email"
msgstr "Email de Reserva Pendente"

#: includes/emails/booking/customer/approved-email.php:8
msgid "Your booking is approved"
msgstr "Sua reserva foi aprovada"

#: includes/emails/booking/customer/approved-email.php:12
msgid "%site_title% - Your booking #%booking_id% is approved"
msgstr "%site_title% - Sua reserva #%booking_id% foi aprovada"

#: includes/emails/booking/customer/approved-email.php:16
msgid "Email that will be sent to customer when booking is approved."
msgstr "O email será enviado ao cliente quando a reserva for aprovada."

#: includes/emails/booking/customer/base-email.php:55
msgid "\"%s\" mail was sent to customer."
msgstr "\"%s\" email foi enviado ao cliente."

#: includes/emails/booking/customer/base-email.php:59
msgid "\"%s\" mail sending is failed."
msgstr "\"%s\" o envio do e-mail falhou."

#: includes/emails/booking/customer/cancelled-email.php:8
msgid "Your booking is cancelled"
msgstr "Sua reserva foi cancelada"

#: includes/emails/booking/customer/cancelled-email.php:12
msgid "%site_title% - Your booking #%booking_id% is cancelled"
msgstr "%site_title% - Sua reserva #%booking_id% foi cancelada"

#: includes/emails/booking/customer/cancelled-email.php:16
msgid "Email that will be sent to customer when booking is cancelled."
msgstr "Um e-mail será enviado ao cliente quando a reserva for cancelada."

#: includes/emails/booking/customer/confirmation-email.php:8
msgid "Confirm your booking"
msgstr "Confirme sua reserva"

#: includes/emails/booking/customer/confirmation-email.php:12
msgid "%site_title% - Confirm your booking #%booking_id%"
msgstr "%site_title% - Confirme sua reserva #%booking_id%"

#: includes/emails/booking/customer/confirmation-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Customer confirmation via email."
msgstr ""

#: includes/emails/booking/customer/confirmation-email.php:17
#: includes/emails/booking/customer/direct-bank-transfer-email.php:43
#: includes/emails/booking/customer/pending-email.php:17
msgid "Email that will be sent to customer after booking is placed."
msgstr "Um e-mail será enviado ao cliente após fazer a reserva."

#: includes/emails/booking/customer/confirmation-email.php:21
msgid "New Booking Email (Confirmation by User)"
msgstr "Novo E-mail de reserva (Confirmação pelo Usuário)"

#: includes/emails/booking/customer/direct-bank-transfer-email.php:35
msgid "Pay for your booking"
msgstr ""

#: includes/emails/booking/customer/direct-bank-transfer-email.php:39
msgid "%site_title% - Pay for your booking #%booking_id%"
msgstr ""

#: includes/emails/booking/customer/direct-bank-transfer-email.php:47
msgid "Payment Instructions Email"
msgstr ""

#: includes/emails/booking/customer/pending-email.php:8
msgid "Your booking is placed"
msgstr "Sua reserva foi efetuada"

#: includes/emails/booking/customer/pending-email.php:12
msgid "%site_title% - Booking #%booking_id% is placed"
msgstr "%site_title% - Reserva #%booking_id% efetuada"

#: includes/emails/booking/customer/pending-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Admin confirmation."
msgstr "Este e-mail é enviado quando \"Modo de Confirmação de Reserva\" estiver definido como confirmação do Administrador."

#: includes/emails/booking/customer/pending-email.php:21
msgid "New Booking Email (Confirmation by Admin)"
msgstr "Novo e-mail de reserva (Сonfirmação Pelo Administrador)"

#: includes/emails/booking/customer/registration-email.php:8
msgid "Welcome"
msgstr ""

#: includes/emails/booking/customer/registration-email.php:12
msgid "%site_title% - account details"
msgstr ""

#: includes/emails/booking/customer/registration-email.php:16
msgid "Email that will be sent to a customer after they registered on your site."
msgstr ""

#: includes/emails/booking/customer/registration-email.php:20
msgid "Customer Registration Email"
msgstr ""

#: includes/emails/templaters/abstract-templater.php:77
msgid "Email Tags"
msgstr ""

#: includes/emails/templaters/abstract-templater.php:87
#: includes/emails/templaters/abstract-templater.php:89
msgid "Deprecated."
msgstr "Descontinuada."

#: includes/emails/templaters/abstract-templater.php:101
msgid "none"
msgstr "nenhum"

#: includes/emails/templaters/cancellation-booking-templater.php:50
msgid "User Cancellation Link"
msgstr "Link de cancelamento do usuário"

#: includes/emails/templaters/email-templater.php:109
msgid "Site title (set in Settings > General)"
msgstr "Título do site (insira em Configurações > Geral)"

#: includes/emails/templaters/email-templater.php:124
#: includes/post-types/payment-cpt.php:232
msgid "Booking ID"
msgstr "ID da Reserva"

#: includes/emails/templaters/email-templater.php:128
msgid "Booking Edit Link"
msgstr "Link Para Editar Reserva"

#: includes/emails/templaters/email-templater.php:132
msgid "Booking Total Price"
msgstr "Preço Total de Reserva"

#: includes/emails/templaters/email-templater.php:153
#: includes/emails/templaters/email-templater.php:296
msgid "Customer First Name"
msgstr "Primeiro Nome do Cliente"

#: includes/emails/templaters/email-templater.php:157
#: includes/emails/templaters/email-templater.php:300
msgid "Customer Last Name"
msgstr "Sobrenome do Cliente"

#: includes/emails/templaters/email-templater.php:161
#: includes/emails/templaters/email-templater.php:304
msgid "Customer Email"
msgstr "Email do Cliente"

#: includes/emails/templaters/email-templater.php:165
#: includes/emails/templaters/email-templater.php:308
msgid "Customer Phone"
msgstr "Telefone do Cliente"

#: includes/emails/templaters/email-templater.php:169
#: includes/emails/templaters/email-templater.php:312
msgid "Customer Country"
msgstr ""

#: includes/emails/templaters/email-templater.php:173
#: includes/emails/templaters/email-templater.php:316
msgid "Customer Address"
msgstr ""

#: includes/emails/templaters/email-templater.php:177
#: includes/emails/templaters/email-templater.php:320
msgid "Customer City"
msgstr "Cidade do Cliente"

#: includes/emails/templaters/email-templater.php:181
#: includes/emails/templaters/email-templater.php:324
msgid "Customer State/County"
msgstr "Estado / município do cliente"

#: includes/emails/templaters/email-templater.php:185
#: includes/emails/templaters/email-templater.php:328
msgid "Customer Postcode"
msgstr "Código Postal do Cliente"

#: includes/emails/templaters/email-templater.php:194
msgid "Reserved Accommodations Details"
msgstr "Detalhes das Acomodações Reservadas"

#: includes/emails/templaters/email-templater.php:216
#: includes/views/create-booking/checkout-view.php:15
#: includes/views/shortcodes/checkout-view.php:164
#: templates/shortcodes/booking-details/booking-details.php:18
msgid "Booking Details"
msgstr "Detalhes da Reserva"

#: includes/emails/templaters/email-templater.php:230
msgid "Confirmation Link"
msgstr "Link de Confirmação"

#: includes/emails/templaters/email-templater.php:234
msgid "Confirmation Link Expiration Time ( UTC )"
msgstr "Tempo de Expiração do Link de Confirmação (UTC)"

#: includes/emails/templaters/email-templater.php:248
msgid "Cancellation Details (if enabled)"
msgstr "Detalhes do Cancelamento (se ativado)"

#: includes/emails/templaters/email-templater.php:262
msgid "The total amount of payment"
msgstr ""

#: includes/emails/templaters/email-templater.php:266
msgid "The unique ID of payment"
msgstr "O ID exclusivo de pagamento"

#: includes/emails/templaters/email-templater.php:270
msgid "The method of payment"
msgstr "O método de pagamento"

#: includes/emails/templaters/email-templater.php:274
msgid "Payment instructions"
msgstr "Instruções de pagamento"

#: includes/emails/templaters/email-templater.php:288
msgid "User login"
msgstr ""

#: includes/emails/templaters/email-templater.php:292
msgid "User password"
msgstr ""

#: includes/emails/templaters/email-templater.php:332
msgid "Link to My Account page"
msgstr ""

#: includes/emails/templaters/email-templater.php:562
#: includes/upgrader.php:868
#: includes/wizard.php:213
msgid "My Account"
msgstr ""

#: includes/emails/templaters/reserved-rooms-templater.php:191
msgid "Accommodation Type Link"
msgstr "Link do Tipo de Acomodação"

#: includes/emails/templaters/reserved-rooms-templater.php:195
msgid "Accommodation Type Title"
msgstr "Título do Tipo de Acomodação"

#: includes/emails/templaters/reserved-rooms-templater.php:199
msgid "Accommodation Title"
msgstr ""

#: includes/emails/templaters/reserved-rooms-templater.php:203
msgid "Accommodation Type Categories"
msgstr "Categorias de Tipo de Alojamento"

#: includes/emails/templaters/reserved-rooms-templater.php:207
msgid "Accommodation Type Bed"
msgstr "Tipo de Cama da Acomodação"

#: includes/emails/templaters/reserved-rooms-templater.php:211
msgid "Accommodation Rate Title"
msgstr "Título da Taxa de Acomodação"

#: includes/emails/templaters/reserved-rooms-templater.php:215
msgid "Accommodation Rate Description"
msgstr "Descrição da Taxa de Acomodação"

#: includes/emails/templaters/reserved-rooms-templater.php:219
msgid "Sequential Number of Accommodation"
msgstr "Número sequencial da Acomodação"

#: includes/entities/coupon.php:370
msgid "This coupon has expired."
msgstr "Este cupom expirou."

#: includes/entities/coupon.php:374
msgid "Sorry, this coupon is not applicable to your booking contents."
msgstr "Desculpe, este cupom não é aplicável aos conteúdos da sua reserva."

#: includes/entities/coupon.php:378
msgid "Coupon usage limit has been reached."
msgstr "O limite de uso do cupom foi atingido."

#: includes/entities/reserved-service.php:98
msgid " &#215; %d night"
msgid_plural " &#215; %d nights"
msgstr[0] "  &#215; %d noite"
msgstr[1] " &#215; %d noites"

#: includes/entities/reserved-service.php:103
#: includes/shortcodes/search-results-shortcode.php:904
msgid "%d adult"
msgid_plural "%d adults"
msgstr[0] "%d Adulto"
msgstr[1] "%d Adultos"

#: includes/entities/reserved-service.php:105
#: includes/shortcodes/search-results-shortcode.php:896
#: includes/shortcodes/search-results-shortcode.php:900
msgid "%d guest"
msgid_plural "%d guests"
msgstr[0] "%d convidado"
msgstr[1] "%d convidados"

#: includes/entities/reserved-service.php:110
msgid " &#215; %d time"
msgid_plural " &#215; %d times"
msgstr[0] ""
msgstr[1] ""

#: includes/entities/service.php:195
msgid "Per Instance"
msgstr "Por vez"

#: includes/i-cal/background-processes/background-synchronizer.php:34
msgid "Maximum execution time is set to %d seconds."
msgstr "O tempo de execução máximo foi definido para %d segundos."

#: includes/i-cal/background-processes/background-synchronizer.php:80
msgid "%d URL pulled for parsing."
msgid_plural "%d URLs pulled for parsing."
msgstr[0] "%d URL foi pulada para análize."
msgstr[1] "As %d URLs foram puladas para análize."

#: includes/i-cal/background-processes/background-synchronizer.php:82
msgid "Skipped. No URLs found for parsing."
msgstr "Pulado. Não foram encontradas URLs para análise."

#: includes/i-cal/background-processes/background-uploader.php:64
msgid "Cannot read uploaded file"
msgstr "O arquivo enviado não pode ser lido"

#: includes/i-cal/background-processes/background-worker.php:327
msgctxt "%s - calendar URI or calendar filename"
msgid "%1$d event found in calendar %2$s"
msgid_plural "%1$d events found in calendar %2$s"
msgstr[0] "\"%1$d evento encontrado no calendário %2$s"
msgstr[1] "%1$d eventos encontrados no calendário %2$s"

#: includes/i-cal/background-processes/background-worker.php:357
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar source is empty (%s)"
msgstr "A fonte do calendário está vazia (%s)"

#: includes/i-cal/background-processes/background-worker.php:370
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar file is not empty, but there are no events in %s"
msgstr "O arquivo do calendário não está vazio, mas não há eventos no %s"

#: includes/i-cal/background-processes/background-worker.php:403
msgid "We will need to check %d previous booking after importing and remove it if the booking is outdated."
msgid_plural "We will need to check %d previous bookings after importing and remove the outdated ones."
msgstr[0] ""
msgstr[1] ""

#: includes/i-cal/background-processes/background-worker.php:425
msgid "Error while loading calendar (%1$s): %2$s"
msgstr "Um erro ocorreu ao carregar o calendário (%1$s): %2$s"

#: includes/i-cal/background-processes/background-worker.php:427
msgctxt "%s - error description"
msgid "Parse error. %s"
msgstr "Erro. %s"

#: includes/i-cal/background-processes/background-worker.php:468
msgid "Skipped. Outdated booking #%d already removed."
msgstr ""

#: includes/i-cal/background-processes/background-worker.php:475
msgid "Skipped. Booking #%d updated with new data."
msgstr "Ignorado. Reserva #%d atualizada com novos dados."

#: includes/i-cal/background-processes/background-worker.php:497
msgid "The outdated booking #%d has been removed."
msgstr "A reserva #%d desatualizada foi removida."

#: includes/i-cal/importer.php:104
msgid "Skipped. Event from %1$s to %2$s has passed."
msgstr "Ignorado. O evento de %1$s para %2$s passou."

#: includes/i-cal/importer.php:120
msgid "New booking #%1$d. The dates from %2$s to %3$s are now blocked."
msgstr "Nova reserva #%1$d. As datas de %2$s a %3$s estão bloqueadas agora."

#: includes/i-cal/importer.php:140
msgid "Success. Booking #%d updated with new data."
msgstr "Ignorado. Reserva #%d atualizada com novos dados."

#: includes/i-cal/importer.php:148
msgid "Skipped. The dates from %1$s to %2$s are already blocked."
msgstr "Ignorado. As datas de %1$s a %2$s já estão bloqueadas."

#: includes/i-cal/importer.php:164
msgid "Success. Booking #%1$d updated with new data. Removed %2$d outdated booking."
msgid_plural "Success. Booking #%1$d updated with new data. Removed %2$d outdated bookings."
msgstr[0] ""
msgstr[1] ""

#: includes/i-cal/importer.php:166
msgid "Success. Booking #%1$d updated with new data."
msgstr "Ignorado. Reserva #%1$d atualizada com novos dados."

#: includes/i-cal/importer.php:177
msgid "Cannot import new event. Dates from %1$s to %2$s are partially blocked by booking %3$s."
msgid_plural "Cannot import new event. Dates from %1$s to %2$s are partially blocked by bookings %3$s."
msgstr[0] ""
msgstr[1] ""

#: includes/i-cal/importer.php:233
msgid "Booking imported with UID %1$s.<br />Summary: %2$s.<br />Description: %3$s.<br />Source: %4$s."
msgstr ""

#: includes/i-cal/logs-handler.php:25
msgid "Process Information"
msgstr "Processar Informação"

#: includes/i-cal/logs-handler.php:35
msgid "Total bookings: %s"
msgstr "Total de reservas: %s"

#: includes/i-cal/logs-handler.php:37
msgid "Success bookings: %s"
msgstr "Reservas bem sucedidas: %s"

#: includes/i-cal/logs-handler.php:39
msgid "Skipped bookings: %s"
msgstr "Reservas que foram puladas: %s"

#: includes/i-cal/logs-handler.php:41
msgid "Failed bookings: %s"
msgstr "Reservas que não foram bem sucedidas: %s"

#: includes/i-cal/logs-handler.php:43
msgid "Removed bookings: %s"
msgstr ""

#: includes/i-cal/logs-handler.php:87
msgid "Expand All"
msgstr "Expandir Todos"

#: includes/i-cal/logs-handler.php:91
msgid "Collapse All"
msgstr "Contrair Todos"

#: includes/i-cal/logs-handler.php:138
msgid "All done! %1$d booking was successfully added."
msgid_plural "All done! %1$d bookings were successfully added."
msgstr[0] "Tudo pronto! A reserva %1$d foi adicionada com sucesso."
msgstr[1] "Tudo pronto! As reservas %1$d foram adicionadas com sucesso."

#: includes/i-cal/logs-handler.php:139
msgid " There was %2$d failure."
msgid_plural " There were %2$d failures."
msgstr[0] " Ocorreu %2$d falha."
msgstr[1] " Ocorreram %2$d falhas."

#: includes/license-notice.php:87
#: includes/license-notice.php:160
msgid "Your License Key is not active. Please, <a href=\"%s\">activate your License Key</a> to get plugin updates."
msgstr ""

#: includes/license-notice.php:152
msgid "Dismiss "
msgstr "Dispensar "

#: includes/linked-rooms.php:31
msgid "Blocked because the linked accommodation is booked"
msgstr ""

#: includes/notices.php:138
#: includes/notices.php:156
#: includes/wizard.php:33
msgid "Hotel Booking Plugin"
msgstr "Plugin de Reserva no Hotel"

#: includes/notices.php:139
msgid "Your database is being updated in the background."
msgstr "Seu banco de dados está sendo atualizado em segundo plano."

#: includes/notices.php:141
msgid "Taking a while? Click here to run it now."
msgstr "Esta demorando? Clique aqui para executá-lo agora."

#: includes/notices.php:157
msgid "Add \"Booking Confirmation\" shortcode to your \"Booking Confirmed\" and \"Reservation Received\" pages to show more details about booking or payment.<br/>Click \"Update Pages\" to apply all changes automatically or skip this notice and add \"Booking Confirmation\" shortcode manually.<br/><b><em>This action will replace the whole content of the pages.</em></b>"
msgstr "Adicione o atalho \"Confirmação de reserva\" às páginas \"Confirmação de reserva\" e \"Reserva recebida\" para mostrar mais detalhes sobre a reserva ou o pagamento. <br/> Clique em \"Atualizar páginas\" para aplicar todas as alterações automaticamente ou ignore este aviso e adicione \"Confirmação de reserva \"shortcode manualmente. <br/> <b> <em> Esta ação substituirá todo o conteúdo das páginas. </em> </b>"

#: includes/notices.php:159
msgid "Update Pages"
msgstr "Atualizar páginas"

#: includes/notices.php:161
#: includes/wizard.php:36
msgid "Skip"
msgstr "Pular"

#: includes/payments/gateways/bank-gateway.php:82
#: includes/payments/gateways/bank-gateway.php:91
msgid "Direct Bank Transfer"
msgstr ""

#: includes/payments/gateways/bank-gateway.php:92
msgid "Make your payment directly into our bank account. Please use your Booking ID as the payment reference."
msgstr "Faça seu pagamento diretamente em nossa conta bancária. Por favor, use seu ID de reserva como referência de pagamento."

#: includes/payments/gateways/bank-gateway.php:118
msgid "Enable Auto-Abandonment"
msgstr ""

#: includes/payments/gateways/bank-gateway.php:119
msgid "Automatically abandon bookings and release reserved slots if payment is not received within a specified time period. You need to manually set the status of paid payments to Completed to avoid automatic abandonment."
msgstr ""

#: includes/payments/gateways/bank-gateway.php:128
msgid "Period of time in hours a user has to pay for a booking. Unpaid bookings become abandoned, and accommodations become available for others."
msgstr ""

#: includes/payments/gateways/beanstream-gateway.php:54
msgid "Beanstream/Bambora"
msgstr ""

#: includes/payments/gateways/beanstream-gateway.php:59
#: includes/payments/gateways/braintree-gateway.php:149
#: includes/payments/gateways/paypal-gateway.php:72
#: includes/payments/gateways/two-checkout-gateway.php:70
msgid "Use the card number %1$s with CVC %2$s and a valid expiration date to test a payment."
msgstr "Use o número do cartão %1$s com o CVC %2$s e uma data de validade válida para testar o pagamento."

#: includes/payments/gateways/beanstream-gateway.php:66
msgid "Pay by Card (Beanstream)"
msgstr "Pague com Cartão (Beanstream)"

#: includes/payments/gateways/beanstream-gateway.php:67
msgid "Pay with your credit card via Beanstream."
msgstr "Pague com cartão de crédito via Beanstream."

#: includes/payments/gateways/beanstream-gateway.php:85
#: includes/payments/gateways/braintree-gateway.php:194
#: includes/payments/gateways/stripe-gateway.php:226
msgid "%1$s is enabled, but the <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "%1$s está ativada, mas a <a href=\"%2$s\">Opção de Forçar Checkout seguro</a> está desativada. Por favor, ative o SSL e assegure se de que o servidor tenha um certificado de SSL válido. Caso contrário, %1$s só funcionará no modo teste."

#: includes/payments/gateways/beanstream-gateway.php:87
#: includes/payments/gateways/braintree-gateway.php:196
#: includes/payments/gateways/stripe-gateway.php:228
msgid "The <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "Stripe está ativado, mas a opção <a href=\"%2$s\"> Forçar o Checkout seguro</a> está desativado. Por favor, ative o SSL e certifique-se de que o seu servidor possui um certificado SSL válido. Caso contrário, %1$s só funcionará no modo de teste."

#: includes/payments/gateways/beanstream-gateway.php:90
msgid "Beanstream"
msgstr ""

#: includes/payments/gateways/beanstream-gateway.php:103
#: includes/payments/gateways/braintree-gateway.php:212
msgid "Merchant ID"
msgstr "ID do Comerciante"

#: includes/payments/gateways/beanstream-gateway.php:105
msgid "Your Merchant ID can be found in the top-right corner of the screen after logging in to the Beanstream Back Office"
msgstr "Seu ID de comerciante pode ser localizado no canto superior direito da tela após de efetuar o login no Beanstream"

#: includes/payments/gateways/beanstream-gateway.php:112
msgid "Payments Passcode"
msgstr "Código de acesso dos Pagamentos"

#: includes/payments/gateways/beanstream-gateway.php:114
msgid "To generate the passcode, navigate to Administration > Account Settings > Order Settings in the sidebar, then scroll to Payment Gateway > Security/Authentication"
msgstr "Para gerar o código de acesso, acesse a  Administração > Configurações da Conta > Configurações do pedido na barra lateral e em seguida, vá até o Gateway de Pagamento > Segurança/Autenticação"

#: includes/payments/gateways/beanstream-gateway.php:163
msgid "Beanstream Payment Error: %s"
msgstr "Erro de Pagamento da Beanstream: %s"

#: includes/payments/gateways/beanstream-gateway.php:201
msgid "Payment single use token is required."
msgstr "É necessário o uso de um token de uso único."

#: includes/payments/gateways/braintree-gateway.php:142
#: includes/payments/gateways/braintree-gateway.php:199
msgid "Braintree"
msgstr ""

#: includes/payments/gateways/braintree-gateway.php:155
#: includes/payments/gateways/stripe-gateway.php:86
msgid "Webhooks Destination URL: %s"
msgstr "Destido das URL Webhooks: %s"

#: includes/payments/gateways/braintree-gateway.php:168
msgid "Pay by Card (Braintree)"
msgstr "Pago por Cartão (Braintree)"

#: includes/payments/gateways/braintree-gateway.php:169
msgid "Pay with your credit card via Braintree."
msgstr "Pago com cartão de credito via Braintree."

#: includes/payments/gateways/braintree-gateway.php:189
msgid "Braintree gateway cannot be enabled due to some problems: %s"
msgstr "A porta do Braintree não pode ser ativada devido a alguns problemas: %s"

#: includes/payments/gateways/braintree-gateway.php:214
msgid "In your Braintree account select Account > My User > View Authorizations."
msgstr "Na conta do Braintree, selecione sua conta > Meu Usuário > Ver autorizações."

#: includes/payments/gateways/braintree-gateway.php:221
#: includes/payments/gateways/stripe-gateway.php:284
msgid "Public Key"
msgstr "Chave Pública"

#: includes/payments/gateways/braintree-gateway.php:229
msgid "Private Key"
msgstr "Chave Privada"

#: includes/payments/gateways/braintree-gateway.php:237
msgid "Merchant Account ID"
msgstr "ID da Conta do Comerciante"

#: includes/payments/gateways/braintree-gateway.php:238
msgid "In case the site currency differs from default currency in your Braintree account, you can set specific merchant account to avoid <a href=\"%s\">complications with currencty conversions</a>. Otherwise leave the field empty."
msgstr "Caso a moeda do site seja diferente da comeda usada na sua conta do Braintree, você poderá especificar a conta do comerciante para evitar <a href=\"%s\">complicações com as converções da moeda</a>. Caso contrario, basta deixar o campo em branco."

#: includes/payments/gateways/braintree-gateway.php:293
msgid "Braintree submitted for settlement (Transaction ID: %s)"
msgstr "O Braintree foi enviado para estabelecimento (ID da Transação: %s)"

#: includes/payments/gateways/braintree-gateway.php:303
msgid "Braintree Payment Error: %s"
msgstr "Erros de Pagamento do Braintree: %s"

#: includes/payments/gateways/braintree-gateway.php:330
msgid "Payment method nonce is required."
msgstr "O método de pagamento não é obrigatório."

#: includes/payments/gateways/braintree/webhook-listener.php:116
msgid "Payment dispute opened"
msgstr "A disputa de pagamento foi aberta"

#: includes/payments/gateways/braintree/webhook-listener.php:121
msgid "Payment dispute lost"
msgstr "A disputa de pagamento foi perdida"

#: includes/payments/gateways/braintree/webhook-listener.php:126
msgid "Payment dispute won"
msgstr "A disputa de pagamento foi ganha"

#: includes/payments/gateways/braintree/webhook-listener.php:143
msgid "Payment refunded in Braintree"
msgstr "Pagamento reembolsado no Braintree"

#: includes/payments/gateways/braintree/webhook-listener.php:147
msgid "Braintree transaction voided"
msgstr "A Transação de Braintree foi anulada"

#: includes/payments/gateways/cash-gateway.php:45
#: includes/payments/gateways/cash-gateway.php:53
msgid "Pay on Arrival"
msgstr ""

#: includes/payments/gateways/cash-gateway.php:54
msgid "Pay with cash on arrival."
msgstr ""

#: includes/payments/gateways/gateway.php:301
msgid "%s is a required field."
msgstr "%s é um campo obrigatório."

#: includes/payments/gateways/gateway.php:314
msgid "%s is not a valid email address."
msgstr "%s não é um e-mail correto."

#. translators: %s is the payment gateway title.
#: includes/payments/gateways/gateway.php:472
msgid "Enable \"%s\""
msgstr ""

#: includes/payments/gateways/gateway.php:482
msgid "Test Mode"
msgstr "Modo de teste"

#: includes/payments/gateways/gateway.php:483
msgid "Enable Sandbox Mode"
msgstr "Ativar Modo Sandbox"

#: includes/payments/gateways/gateway.php:485
msgid "Sandbox can be used to test payments."
msgstr "Sandbox pode ser usado para testar pagamentos."

#: includes/payments/gateways/gateway.php:496
msgid "Payment method title that the customer will see on your website."
msgstr "O título do método de pagamento que o cliente verá no seu website."

#: includes/payments/gateways/gateway.php:506
msgid "Payment method description that the customer will see on your website."
msgstr "Descrição do método de pagamento que o cliente verá no seu website."

#: includes/payments/gateways/gateway.php:516
msgid "Instructions"
msgstr "Instruções"

#: includes/payments/gateways/gateway.php:518
msgid "Instructions for a customer on how to complete the payment."
msgstr ""

#: includes/payments/gateways/gateway.php:543
msgid "Reservation #%d"
msgstr "Reserva #%d"

#: includes/payments/gateways/gateway.php:545
msgid "Accommodation(s) reservation"
msgstr "Reserva(s) de(as) acomodação(ões)"

#: includes/payments/gateways/manual-gateway.php:14
#: includes/payments/gateways/manual-gateway.php:19
msgid "Manual Payment"
msgstr "Pagamento manual"

#: includes/payments/gateways/paypal-gateway.php:67
#: includes/payments/gateways/paypal-gateway.php:80
msgid "PayPal"
msgstr ""

#: includes/payments/gateways/paypal-gateway.php:81
msgid "Pay via PayPal"
msgstr "Pagar com Paypal"

#: includes/payments/gateways/paypal-gateway.php:117
msgid "Paypal Business Email"
msgstr "Conta Corporativa De Paypal"

#: includes/payments/gateways/paypal-gateway.php:125
msgid "Disable IPN Verification"
msgstr "Desativar a Verificação de IPN"

#: includes/payments/gateways/paypal-gateway.php:127
msgid "Specify an IPN listener for a specific payment instead of the listeners specified in your PayPal Profile."
msgstr "Especifique um ouvinte IPN para um pagamento ao invés dos ouvintes especificados no seu Perfil do PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:164
msgid "Payment %s via IPN."
msgstr "Pagamento %s via IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:183
msgid "Payment failed due to invalid PayPal business email."
msgstr "O pagamento falhou devido a emails comerciais inválidos do PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:200
msgid "Payment failed due to invalid currency in PayPal IPN."
msgstr "O pagamento falhou devido à moeda incorreta no PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:215
msgid "Payment failed due to invalid amount in PayPal IPN."
msgstr "O pagamento falhou devido a um valor incorreto no PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:230
msgid "Payment failed due to invalid purchase key in PayPal IPN."
msgstr "O pagamento falhou devido a uma chave de compra inválida no PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:302
msgid "Payment made via eCheck and will clear automatically in 5-8 days."
msgstr "O pagamento feito através do eCheck será concluido automaticamente em 5-8 dias."

#: includes/payments/gateways/paypal/ipn-listener.php:307
msgid "Payment requires a confirmed customer address and must be accepted manually through PayPal."
msgstr "O pagamento requer uma conta confirmado e deve ser aceito manualmente através do PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:312
msgid "Payment must be accepted manually through PayPal due to international account regulations."
msgstr "O pagamento deve ser aceito manualmente via PayPal devido aos regulamentos de contas internacionais."

#: includes/payments/gateways/paypal/ipn-listener.php:317
msgid "Payment received in non-shop currency and must be accepted manually through PayPal."
msgstr "Pagamento recebido em moeda não comercial deve ser aceito manualmente via PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:323
msgid "Payment is being reviewed by PayPal staff as high-risk or in possible violation of government regulations."
msgstr "O pagamento está sendo analizado pela equipe do PayPal como de alto risco ou em possível violação dos regulamentos governamentais."

#: includes/payments/gateways/paypal/ipn-listener.php:328
msgid "Payment was sent to unconfirmed or non-registered email address."
msgstr "O pagamento foi enviado para um endereço de e-mail não confirmado ou não registrado."

#: includes/payments/gateways/paypal/ipn-listener.php:333
msgid "PayPal account must be upgraded before this payment can be accepted."
msgstr "A conta do PayPal deve ser atualizada para que este pagamento possa ser aceito."

#: includes/payments/gateways/paypal/ipn-listener.php:338
msgid "PayPal account is not verified. Verify account in order to accept this payment."
msgstr "A conta do PayPal não está verificada. Verifique a conta para aceitar este pagamento."

#: includes/payments/gateways/paypal/ipn-listener.php:343
msgid "Payment is pending for unknown reasons. Contact PayPal support for assistance."
msgstr "O pagamento está pendente por razões desconhecidas. Entre em contato com o suporte do PayPal para obter assistência."

#: includes/payments/gateways/paypal/ipn-listener.php:363
msgid "Partial PayPal refund processed: %s"
msgstr "Reembolso Parcial do PayPal Processado: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:370
msgid "PayPal Payment #%s Refunded for reason: %s"
msgstr "Pagamento do PayPal #%s Reembolsado pelo motivo: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:374
msgid "PayPal Refund Transaction ID: %s"
msgstr "ID de Reembolso do PayPal: %s"

#: includes/payments/gateways/stripe-gateway.php:149
#: includes/payments/gateways/stripe-gateway.php:231
msgid "Stripe"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:186
msgid "Use the card number %1$s with CVC %2$s, a valid expiration date and random 5-digit ZIP-code to test a payment."
msgstr "Use o número do cartão %1$s com CVC %2$s, uma data de validade válida e um CEP aleatório de 5 dígitos para testar um pagamento."

#: includes/payments/gateways/stripe-gateway.php:202
msgid "Pay by Card (Stripe)"
msgstr "Pagar com Cartão (Stripe)"

#: includes/payments/gateways/stripe-gateway.php:203
msgid "Pay with your credit card via Stripe."
msgstr "Efetuar pagamento com seu cartão de crédito via Stripe."

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:240
#: includes/payments/gateways/stripe-gateway.php:259
#: includes/payments/gateways/stripe-gateway.php:688
msgid "Bancontact"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:241
#: includes/payments/gateways/stripe-gateway.php:260
#: includes/payments/gateways/stripe-gateway.php:689
msgid "iDEAL"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:242
#: includes/payments/gateways/stripe-gateway.php:261
#: includes/payments/gateways/stripe-gateway.php:690
msgid "Giropay"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:243
#: includes/payments/gateways/stripe-gateway.php:262
#: includes/payments/gateways/stripe-gateway.php:691
msgid "SEPA Direct Debit"
msgstr ""

#. translators: %1$s - name of payment method, %2$s - currency codes
#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:244
#: includes/payments/gateways/stripe-gateway.php:269
#: includes/payments/gateways/stripe-gateway.php:276
#: includes/payments/gateways/stripe-gateway.php:692
msgid "Klarna"
msgstr ""

#. translators: %s - currency codes
#: includes/payments/gateways/stripe-gateway.php:252
msgid "The %s currency is selected in the main settings."
msgstr ""

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:258
msgid "%1$s support the following currencies: %2$s."
msgstr ""

#. translators: %1$s - name of payment method, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:268
msgid "%1$s supports: %2$s."
msgstr ""

#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:275
msgid "%s special restrictions."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:293
msgid "Secret Key"
msgstr "Chave Secreta"

#: includes/payments/gateways/stripe-gateway.php:301
msgid "Webhook Secret"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:310
msgid "Payment Methods"
msgstr "Métodos de Pagamento"

#: includes/payments/gateways/stripe-gateway.php:311
msgid "Card Payments"
msgstr "Pagamentos com cartão"

#: includes/payments/gateways/stripe-gateway.php:322
msgid "Checkout Locale"
msgstr "Checkout Local"

#: includes/payments/gateways/stripe-gateway.php:325
msgid "Display Checkout in the user's preferred language, if available."
msgstr "Exibir o Checkout no idioma preferido do usuário, se disponível."

#: includes/payments/gateways/stripe-gateway.php:395
msgid "The payment method is not selected."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:401
msgid "PaymentIntent ID is not set."
msgstr ""

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:430
msgid "Payment for PaymentIntent %s succeeded."
msgstr ""

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:436
msgid "Payment for PaymentIntent %s is processing."
msgstr ""

#. translators: %1$s - Stripe PaymentIntent ID, %2$s - Stripe error message text
#: includes/payments/gateways/stripe-gateway.php:457
msgid "Failed to process PaymentIntent %1$s. %2$s"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:474
msgid "Can't charge the payment again: payment's flow already completed."
msgstr "Não é possível cobrar o pagamento novamente: o fluxo do pagamento já foi concluído."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:496
msgid "Charge %s succeeded."
msgstr ""

#. translators: %1$s - Stripe Charge ID; %2$s - payment price
#: includes/payments/gateways/stripe-gateway.php:503
msgid "Charge %1$s for %2$s created."
msgstr ""

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:508
msgid "Charge %s failed."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:515
msgid "Charge error. %s"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:529
msgid "Argentinean"
msgstr "Argentino"

#: includes/payments/gateways/stripe-gateway.php:530
msgid "Simplified Chinese"
msgstr "Chinês Simplificado"

#: includes/payments/gateways/stripe-gateway.php:531
msgid "Danish"
msgstr "Dinamarquês"

#: includes/payments/gateways/stripe-gateway.php:532
msgid "Dutch"
msgstr "Holandês"

#: includes/payments/gateways/stripe-gateway.php:533
msgid "English"
msgstr "Inglês"

#: includes/payments/gateways/stripe-gateway.php:534
msgid "Finnish"
msgstr "Finlandês"

#: includes/payments/gateways/stripe-gateway.php:535
msgid "French"
msgstr "Francês"

#: includes/payments/gateways/stripe-gateway.php:536
msgid "German"
msgstr "Alemão"

#: includes/payments/gateways/stripe-gateway.php:537
msgid "Italian"
msgstr "Italiano"

#: includes/payments/gateways/stripe-gateway.php:538
msgid "Japanese"
msgstr "Japonês"

#: includes/payments/gateways/stripe-gateway.php:539
msgid "Norwegian"
msgstr "Norueguês"

#: includes/payments/gateways/stripe-gateway.php:540
msgid "Polish"
msgstr "Polaco"

#: includes/payments/gateways/stripe-gateway.php:541
msgid "Russian"
msgstr "Russo"

#: includes/payments/gateways/stripe-gateway.php:542
msgid "Spanish"
msgstr "Espanhol"

#: includes/payments/gateways/stripe-gateway.php:543
msgid "Swedish"
msgstr "Sueco"

#: includes/payments/gateways/stripe-gateway.php:571
msgid "PaymentIntent ID is missing."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:687
msgid "Card"
msgstr "Cartão"

#: includes/payments/gateways/stripe-gateway.php:694
msgid "Credit or debit card"
msgstr "Cartão de Crédito ou de Débito"

#: includes/payments/gateways/stripe-gateway.php:695
msgid "IBAN"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:696
msgid "Select iDEAL Bank"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:698
msgid "You will be redirected to a secure page to complete the payment."
msgstr "Será redireccionado para uma página segura para concluir o pagamento."

#: includes/payments/gateways/stripe-gateway.php:699
msgid "By providing your IBAN and confirming this payment, you are authorizing this merchant and Stripe, our payment service provider, to send instructions to your bank to debit your account and your bank to debit your account in accordance with those instructions. You are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited."
msgstr ""

#. translators: %s - payment method type code like: card
#: includes/payments/gateways/stripe/stripe-api.php:172
msgid "Could not create PaymentIntent for a not allowed payment type: %s"
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:163
msgid "Webhook received. Payment %s was cancelled by the customer."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:174
msgid "Webhook received. Payment %s failed and couldn't be processed."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:200
msgid "Webhook received. Payment %s was successfully processed."
msgstr ""

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:215
msgid "Webhook received. Charge %s succeeded."
msgstr "Webhook recebido. A cobrança %s foi bem sucedida."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:227
msgid "Webhook received. Charge %s failed."
msgstr "Webhook recebido. A cobrança %s falhou."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:238
msgid "Webhook received. The source %s is chargeable."
msgstr ""

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:252
msgid "Webhook received. Payment source %s was cancelled by customer."
msgstr "Webhook recebido. A fonte de pagamento %s foi cancelada pelo cliente."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:263
msgid "Webhook received. Payment source %s failed and couldn't be processed."
msgstr "Webhook recebido. A fonte de pagamento %s falhou e não pôde ser processada."

#: includes/payments/gateways/test-gateway.php:46
#: includes/payments/gateways/test-gateway.php:52
msgid "Test Payment"
msgstr "Teste de Pagamento"

#: includes/payments/gateways/two-checkout-gateway.php:65
#: includes/payments/gateways/two-checkout-gateway.php:101
msgid "2Checkout"
msgstr ""

#: includes/payments/gateways/two-checkout-gateway.php:88
msgid "To setup the callback process for 2Checkout to automatically mark payments completed, you will need to"
msgstr "Para configurar o processo de retorno de chamada 2Checkout para marcar automaticamente os pagamentos concluídos, é necessário"

#: includes/payments/gateways/two-checkout-gateway.php:90
msgid "Login to your 2Checkout account and click the Notifications tab"
msgstr "Faça login com sua conta 2Checkout e clique na aba de Notificações"

#: includes/payments/gateways/two-checkout-gateway.php:91
msgid "Click Enable All Notifications"
msgstr "Clique em Ativar Todas as Notificações"

#: includes/payments/gateways/two-checkout-gateway.php:92
msgid "In the Global URL field, enter the url %s"
msgstr "No campo URL global, insira a url %s"

#: includes/payments/gateways/two-checkout-gateway.php:93
msgid "Click Apply"
msgstr "Clique em Aplicar"

#: includes/payments/gateways/two-checkout-gateway.php:189
msgid "Account Number"
msgstr "Número da Conta"

#: includes/payments/gateways/two-checkout-gateway.php:197
msgid "Secret Word"
msgstr "Palavra Secreta"

#: includes/payments/gateways/two-checkout/ins-listener.php:68
msgid "2Checkout \"Order Created\" notification received."
msgstr "2Checkout \"Pedido criado\" notificação recebida."

#: includes/payments/gateways/two-checkout/ins-listener.php:73
msgid "Payment refunded in 2Checkout"
msgstr "2Checkout revisão de fraudes de completada"

#: includes/payments/gateways/two-checkout/ins-listener.php:80
msgid "2Checkout fraud review passed"
msgstr "2Checkout revisão de fraudes de em andamento"

#: includes/payments/gateways/two-checkout/ins-listener.php:83
msgid "2Checkout fraud review failed"
msgstr "2Checkout revisão de fraudes de em andamento"

#: includes/payments/gateways/two-checkout/ins-listener.php:86
msgid "2Checkout fraud review in progress"
msgstr "2Checkout Analise contra fraude em andamento"

#: includes/post-types/attributes-cpt.php:55
msgid "Attribute"
msgstr ""

#: includes/post-types/attributes-cpt.php:56
msgctxt "Add New Attribute"
msgid "Add New"
msgstr "Adicionar Novo"

#: includes/post-types/attributes-cpt.php:57
msgid "Add New Attribute"
msgstr ""

#: includes/post-types/attributes-cpt.php:58
msgid "Edit Attribute"
msgstr ""

#: includes/post-types/attributes-cpt.php:59
msgid "New Attribute"
msgstr ""

#: includes/post-types/attributes-cpt.php:60
msgid "View Attribute"
msgstr ""

#: includes/post-types/attributes-cpt.php:62
msgid "Search Attribute"
msgstr ""

#: includes/post-types/attributes-cpt.php:63
msgid "No Attributes found"
msgstr ""

#: includes/post-types/attributes-cpt.php:64
msgid "No Attributes found in Trash"
msgstr ""

#: includes/post-types/attributes-cpt.php:66
msgid "Insert into attribute description"
msgstr ""

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:157
msgid "Search %s"
msgstr ""

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:159
msgid "All %s"
msgstr ""

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:161
msgid "Edit %s"
msgstr ""

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:163
msgid "Update %s"
msgstr ""

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:165
msgid "Add new %s"
msgstr ""

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:167
msgid "New %s"
msgstr ""

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:169
msgid "No &quot;%s&quot; found"
msgstr ""

#: includes/post-types/attributes-cpt.php:302
msgid "Name (numeric)"
msgstr ""

#: includes/post-types/attributes-cpt.php:303
msgid "Term ID"
msgstr "ID do Termo"

#: includes/post-types/attributes-cpt.php:314
msgid "Enable Archives"
msgstr ""

#: includes/post-types/attributes-cpt.php:315
msgid "Link the attribute to an archive page with all accommodation types that have this attribute."
msgstr ""

#: includes/post-types/attributes-cpt.php:324
msgid "Visible in Details"
msgstr ""

#: includes/post-types/attributes-cpt.php:325
msgid "Display the attribute in details section of an accommodation type."
msgstr ""

#: includes/post-types/attributes-cpt.php:334
msgid "Default Sort Order"
msgstr ""

#: includes/post-types/attributes-cpt.php:344
msgid "Default Text"
msgstr "Texto predefinido"

#: includes/post-types/attributes-cpt.php:355
msgid "Select"
msgstr ""

#: includes/post-types/booking-cpt.php:75
#: templates/edit-booking/edit-dates.php:24
msgid "Edit Dates"
msgstr "Editar datas"

#: includes/post-types/booking-cpt.php:215
msgid "Note"
msgstr ""

#: includes/post-types/booking-cpt.php:243
msgctxt "Add New Booking"
msgid "Add New Booking"
msgstr "Adicionar Nova Reserva"

#: includes/post-types/booking-cpt.php:247
#: templates/emails/admin-customer-cancelled-booking.php:16
#: templates/emails/admin-customer-confirmed-booking.php:16
#: templates/emails/admin-payment-confirmed-booking.php:16
#: templates/emails/admin-pending-booking.php:16
#: templates/emails/customer-approved-booking.php:24
#: templates/emails/customer-pending-booking.php:26
msgid "View Booking"
msgstr "Visualizar Reserva"

#: includes/post-types/booking-cpt.php:248
msgid "Search Booking"
msgstr "Busca de Reservas"

#: includes/post-types/booking-cpt.php:249
msgid "No bookings found"
msgstr "Nenhuma reserva encontrada"

#: includes/post-types/booking-cpt.php:250
msgid "No bookings found in Trash"
msgstr "Nenhuma reserva foi encontrada na Lixeira"

#: includes/post-types/booking-cpt.php:251
msgid "All Bookings"
msgstr "Todas as reservas"

#: includes/post-types/booking-cpt.php:252
msgid "Insert into booking description"
msgstr "Inserir na descrição da reserva"

#: includes/post-types/booking-cpt.php:253
msgid "Uploaded to this booking"
msgstr "Enviado para para esta reserva"

#: includes/post-types/booking-cpt/statuses.php:58
msgctxt "Booking status"
msgid "Pending User Confirmation"
msgstr "Confirmação de Usuário Pendente"

#: includes/post-types/booking-cpt/statuses.php:63
msgid "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgid_plural "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgstr[0] "Confirmação de Usuário Pendente <span class=\"count\">(%s)</span>"
msgstr[1] "Confirmação de Usuário Pendente <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:69
msgctxt "Booking status"
msgid "Pending Payment"
msgstr "Pagamento Pendente"

#: includes/post-types/booking-cpt/statuses.php:74
msgid "Pending Payment <span class=\"count\">(%s)</span>"
msgid_plural "Pending Payment <span class=\"count\">(%s)</span>"
msgstr[0] "Pagamento Pendente <span class=\"count\">(%s)</span>"
msgstr[1] "Pagamentos Pendentes <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:80
msgctxt "Booking status"
msgid "Pending Admin"
msgstr "Pendente pelo Administrador"

#: includes/post-types/booking-cpt/statuses.php:85
msgid "Pending Admin <span class=\"count\">(%s)</span>"
msgid_plural "Pending Admin <span class=\"count\">(%s)</span>"
msgstr[0] "Pendente pelo Administrador <span class=\"count\">(%s)</span>"
msgstr[1] "Pendente pelo Administrador <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:91
#: includes/reports/data/report-earnings-by-dates-data.php:31
msgctxt "Booking status"
msgid "Abandoned"
msgstr "Abandonado"

#: includes/post-types/booking-cpt/statuses.php:96
#: includes/post-types/payment-cpt/statuses.php:83
msgid "Abandoned <span class=\"count\">(%s)</span>"
msgid_plural "Abandoned <span class=\"count\">(%s)</span>"
msgstr[0] "Abandonado <span class=\"count\">(%s)</span>"
msgstr[1] "Abandonados <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:107
msgid "Confirmed <span class=\"count\">(%s)</span>"
msgid_plural "Confirmed <span class=\"count\">(%s)</span>"
msgstr[0] "Confirmado <span class=\"count\">(%s)</span>"
msgstr[1] "Confirmado <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:113
#: includes/reports/data/report-earnings-by-dates-data.php:30
msgctxt "Booking status"
msgid "Cancelled"
msgstr "Cancelado"

#: includes/post-types/booking-cpt/statuses.php:118
#: includes/post-types/payment-cpt/statuses.php:116
msgid "Cancelled <span class=\"count\">(%s)</span>"
msgid_plural "Cancelled <span class=\"count\">(%s)</span>"
msgstr[0] "Cancelado <span class=\"count\">(%s)</span>"
msgstr[1] "Cancelados <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:180
#: includes/post-types/payment-cpt/statuses.php:213
msgid "Status changed from %s to %s."
msgstr "Estado alterado de %s para %s."

#: includes/post-types/coupon-cpt.php:45
msgid "A brief description to remind you what this code is for."
msgstr ""

#: includes/post-types/coupon-cpt.php:52
msgid "Conditions"
msgstr ""

#: includes/post-types/coupon-cpt.php:60
msgid "Apply a coupon code to selected accommodations in a booking. Leave blank to apply to all accommodations."
msgstr ""

#: includes/post-types/coupon-cpt.php:82
msgid "Percentage discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:83
msgid "Fixed discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:84
msgid "Fixed discount on daily/nightly price"
msgstr ""

#: includes/post-types/coupon-cpt.php:94
#: includes/post-types/coupon-cpt.php:125
#: includes/post-types/coupon-cpt.php:170
msgid "Enter percent or fixed amount according to selected type."
msgstr ""

#: includes/post-types/coupon-cpt.php:104
msgid "Service Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:137
msgid "Apply a coupon code to selected services in a booking. Leave blank to apply to all services."
msgstr ""

#: includes/post-types/coupon-cpt.php:149
msgid "Fee Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:180
msgid "Usage Restrictions"
msgstr ""

#: includes/post-types/coupon-cpt.php:195
msgid "Check-in After"
msgstr "Check-in Depois"

#: includes/post-types/coupon-cpt.php:203
msgid "Check-out Before"
msgstr "Check-out Antes"

#: includes/post-types/coupon-cpt.php:211
msgid "Min days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:212
msgid "For early bird discount. The coupon code applies if a booking is made in a minimum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:222
msgid "Max days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:223
msgid "For last minute discount. The coupon code applies if a booking is made in a maximum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:233
msgid "Minimum Days"
msgstr "Dias Mínimos"

#: includes/post-types/coupon-cpt.php:243
msgid "Maximum Days"
msgstr "Dias Máximos"

#: includes/post-types/coupon-cpt.php:253
msgid "Usage Limit"
msgstr "Limite de Uso"

#: includes/post-types/coupon-cpt.php:263
msgid "Usage Count"
msgstr "Contagem de Uso"

#: includes/post-types/coupon-cpt.php:290
msgctxt "Add New Coupon"
msgid "Add New"
msgstr "Adicionar Novo"

#: includes/post-types/coupon-cpt.php:291
msgid "Add New Coupon"
msgstr "Adicionar Novo Cupom"

#: includes/post-types/coupon-cpt.php:292
msgid "Edit Coupon"
msgstr "Editar Cupom"

#: includes/post-types/coupon-cpt.php:293
msgid "New Coupon"
msgstr "Novo Cupom"

#: includes/post-types/coupon-cpt.php:294
msgid "View Coupon"
msgstr "Visualizar Cupom"

#: includes/post-types/coupon-cpt.php:295
msgid "Search Coupon"
msgstr "Buscar Cupom"

#: includes/post-types/coupon-cpt.php:296
msgid "No coupons found"
msgstr "Nenhum cupom foi encontrado"

#: includes/post-types/coupon-cpt.php:297
msgid "No coupons found in Trash"
msgstr "Nenhum cupom foi encontrado no Lixo"

#: includes/post-types/coupon-cpt.php:298
msgid "All Coupons"
msgstr "Todos os Cupons"

#: includes/post-types/payment-cpt.php:37
msgid "Payment History"
msgstr "Historico de Pagamento"

#: includes/post-types/payment-cpt.php:38
msgid "Payment"
msgstr "Pagamento"

#: includes/post-types/payment-cpt.php:39
msgctxt "Add New Payment"
msgid "Add New"
msgstr "Adicionar Novo"

#: includes/post-types/payment-cpt.php:40
msgid "Add New Payment"
msgstr "Adicionar Pagamento"

#: includes/post-types/payment-cpt.php:41
msgid "Edit Payment"
msgstr "Editar Pagamento"

#: includes/post-types/payment-cpt.php:42
msgid "New Payment"
msgstr "Novo Pagamento"

#: includes/post-types/payment-cpt.php:43
msgid "View Payment"
msgstr "Visualizar Pagamento"

#: includes/post-types/payment-cpt.php:44
msgid "Search Payment"
msgstr "Buscar Pagamento"

#: includes/post-types/payment-cpt.php:45
msgid "No payments found"
msgstr "Nenhum pagamento encontrado"

#: includes/post-types/payment-cpt.php:46
msgid "No payments found in Trash"
msgstr "Nenhum pagamento foi encontrado na Lixeira"

#: includes/post-types/payment-cpt.php:47
msgid "Payments"
msgstr "Pagamentos"

#: includes/post-types/payment-cpt.php:48
msgid "Insert into payment description"
msgstr "Inserir na descrição do pagamento"

#: includes/post-types/payment-cpt.php:49
msgid "Uploaded to this payment"
msgstr "Enviado para este pagamento"

#: includes/post-types/payment-cpt.php:54
msgid "Payments."
msgstr "Pagamentos."

#: includes/post-types/payment-cpt.php:169
msgid "Gateway Mode"
msgstr "Modo de gateway"

#: includes/post-types/payment-cpt.php:171
msgid "Sandbox"
msgstr ""

#: includes/post-types/payment-cpt.php:172
msgid "Live"
msgstr "Ao Vivo"

#: includes/post-types/payment-cpt.php:194
msgid "Fee"
msgstr "Taxa"

#: includes/post-types/payment-cpt.php:215
msgid "Payment Type"
msgstr "Tipo de pagamento"

#: includes/post-types/payment-cpt.php:240
msgid "Billing Info"
msgstr "Informação de Pagamento"

#: includes/post-types/payment-cpt.php:287
msgid "Address 1"
msgstr "Endereço 1"

#: includes/post-types/payment-cpt.php:295
msgid "Address 2"
msgstr "Endereço 2"

#: includes/post-types/payment-cpt.php:319
msgid "Postal Code (ZIP)"
msgstr "Código Postal CEP)"

#: includes/post-types/payment-cpt/statuses.php:45
msgctxt "Payment status"
msgid "Pending"
msgstr "Pendente"

#: includes/post-types/payment-cpt/statuses.php:50
msgid "Pending <span class=\"count\">(%s)</span>"
msgid_plural "Pending <span class=\"count\">(%s)</span>"
msgstr[0] "Pendente <span class=\"count\">(%s)</span>"
msgstr[1] "Pendentes <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:56
msgctxt "Payment status"
msgid "Completed"
msgstr "Concluído"

#: includes/post-types/payment-cpt/statuses.php:61
msgid "Completed <span class=\"count\">(%s)</span>"
msgid_plural "Completed <span class=\"count\">(%s)</span>"
msgstr[0] "Completo <span class=\"count\">(%s)</span>"
msgstr[1] "Completados <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:67
msgctxt "Payment status"
msgid "Failed"
msgstr "Falhou"

#: includes/post-types/payment-cpt/statuses.php:72
msgid "Failed <span class=\"count\">(%s)</span>"
msgid_plural "Failed <span class=\"count\">(%s)</span>"
msgstr[0] "Falhou <span class=\"count\">(%s)</span>"
msgstr[1] "Falhados <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:78
msgctxt "Payment status"
msgid "Abandoned"
msgstr "Abandonado"

#: includes/post-types/payment-cpt/statuses.php:89
msgctxt "Payment status"
msgid "On Hold"
msgstr "Em Espera"

#: includes/post-types/payment-cpt/statuses.php:94
msgid "On Hold <span class=\"count\">(%s)</span>"
msgid_plural "On Hold <span class=\"count\">(%s)</span>"
msgstr[0] "Em Espera <span class=\"count\">(%s)</span>"
msgstr[1] "Em Espera <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:100
msgctxt "Payment status"
msgid "Refunded"
msgstr "Reembolsado"

#: includes/post-types/payment-cpt/statuses.php:105
msgid "Refunded <span class=\"count\">(%s)</span>"
msgid_plural "Refunded <span class=\"count\">(%s)</span>"
msgstr[0] "Reembolsado <span class=\"count\">(%s)</span>"
msgstr[1] "Reembolsados <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:111
msgctxt "Payment status"
msgid "Cancelled"
msgstr "Cancelado"

#: includes/post-types/payment-cpt/statuses.php:180
msgid "Payment (#%s) for this booking is on hold"
msgstr "O pagamento (#%s) desta reserva está em espera"

#: includes/post-types/rate-cpt.php:23
msgid "Rate Info"
msgstr "Classificar informação"

#: includes/post-types/rate-cpt.php:49
#: includes/post-types/season-cpt.php:99
msgid "Season"
msgstr "Temporada"

#: includes/post-types/rate-cpt.php:72
msgid "Move price to top to set higher priority."
msgstr "Mover o preço para cima para prioridade mais alta."

#: includes/post-types/rate-cpt.php:84
msgid "Will be displayed on the checkout page."
msgstr "Será exibido na página de checkout."

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/post-types/rate-cpt.php:103
#: includes/post-types/rate-cpt.php:113
msgid "Rates"
msgstr "Taxas"

#: includes/post-types/rate-cpt.php:105
msgctxt "Add New Rate"
msgid "Add New"
msgstr "Adicionar Novo"

#: includes/post-types/rate-cpt.php:106
msgid "Add New Rate"
msgstr "Adicionar Nova Taxa"

#: includes/post-types/rate-cpt.php:107
msgid "Edit Rate"
msgstr "Editar Taxa"

#: includes/post-types/rate-cpt.php:108
msgid "New Rate"
msgstr "Nova Taxa"

#: includes/post-types/rate-cpt.php:109
msgid "View Rate"
msgstr "Visualizar Taxa"

#: includes/post-types/rate-cpt.php:110
msgid "Search Rate"
msgstr "Taxa de Busca"

#: includes/post-types/rate-cpt.php:111
msgid "No rates found"
msgstr ""

#: includes/post-types/rate-cpt.php:112
msgid "No rates found in Trash"
msgstr ""

#: includes/post-types/rate-cpt.php:114
msgid "Insert into rate description"
msgstr ""

#: includes/post-types/rate-cpt.php:115
msgid "Uploaded to this rate"
msgstr ""

#: includes/post-types/rate-cpt.php:120
msgid "This is where you can add new rates."
msgstr ""

#: includes/post-types/reserved-room-cpt.php:23
msgid "Reserved Accommodation"
msgstr "Acomodação Reservada"

#: includes/post-types/room-cpt.php:33
msgctxt "Add New Accommodation"
msgid "Add New"
msgstr "Adicionar Nova"

#: includes/post-types/room-cpt.php:34
msgid "Add New Accommodation"
msgstr "Adicionar Nova Acomodação"

#: includes/post-types/room-cpt.php:35
msgid "Edit Accommodation"
msgstr "Editar Acomodação"

#: includes/post-types/room-cpt.php:36
msgid "New Accommodation"
msgstr "Nova Acomodação"

#: includes/post-types/room-cpt.php:37
msgid "View Accommodation"
msgstr "Visualizar Acomodação"

#: includes/post-types/room-cpt.php:38
msgid "Search Accommodation"
msgstr "Buscar Acomodação"

#: includes/post-types/room-cpt.php:39
#: templates/create-booking/results/rooms-found.php:21
#: templates/shortcodes/search-results/results-info.php:19
msgid "No accommodations found"
msgstr "Nenhuma acomodação encontrada"

#: includes/post-types/room-cpt.php:40
msgid "No accommodations found in Trash"
msgstr "Não foram acomodações alojamentos na Lixeira"

#: includes/post-types/room-cpt.php:42
msgid "Insert into accommodation description"
msgstr "Inserir na descrição do acomodação"

#: includes/post-types/room-cpt.php:43
msgid "Uploaded to this accommodation"
msgstr "Enviado para esta acomodação"

#: includes/post-types/room-cpt.php:48
msgid "This is where you can add new accommodations to your hotel."
msgstr "Aqui é possível adicionar novas acomodações no seu hotel."

#: includes/post-types/room-cpt.php:106
msgid "Automatically block current accommodation when the selected ones are booked"
msgstr ""

#: includes/post-types/room-type-cpt.php:55
msgctxt "Add New Accommodation Type"
msgid "Add Accommodation Type"
msgstr "Adicionar Tipo de Acomodação"

#: includes/post-types/room-type-cpt.php:56
msgid "Add New Accommodation Type"
msgstr "Adicionar Novo Tipo de Acomodação"

#: includes/post-types/room-type-cpt.php:57
msgid "Edit Accommodation Type"
msgstr "Editar Tipo de Acomodação"

#: includes/post-types/room-type-cpt.php:58
msgid "New Accommodation Type"
msgstr "Novo Tipo de Acomodação"

#: includes/post-types/room-type-cpt.php:59
msgid "View Accommodation Type"
msgstr "Visualizar Tipo de Acomodação"

#: includes/post-types/room-type-cpt.php:61
msgid "Search Accommodation Type"
msgstr "Buscar Tipo de Acomodação"

#: includes/post-types/room-type-cpt.php:62
msgid "No Accommodation types found"
msgstr "Não foram encontrados tipos de acomodação"

#: includes/post-types/room-type-cpt.php:63
msgid "No Accommodation types found in Trash"
msgstr "Não foram encontrados tipos de acomodação na lixeira"

#: includes/post-types/room-type-cpt.php:65
msgid "Insert into accommodation type description"
msgstr "Inserir na descrição do tipo de acomodação"

#: includes/post-types/room-type-cpt.php:66
msgid "Uploaded to this accommodation type"
msgstr "Enviado para este tipo de acomodação"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:83
msgctxt "slug"
msgid "accommodation"
msgstr "accommodation"

#: includes/post-types/room-type-cpt.php:108
msgid "Accommodation Categories"
msgstr "Categorias de Acomodação"

#: includes/post-types/room-type-cpt.php:109
msgid "Accommodation Category"
msgstr "Categorias de Acomodação"

#: includes/post-types/room-type-cpt.php:110
msgid "Search Accommodation Categories"
msgstr "Buscar Categorias de Acomodação"

#: includes/post-types/room-type-cpt.php:111
msgid "Popular Accommodation Categories"
msgstr "Categorias Populares de Acomodação"

#: includes/post-types/room-type-cpt.php:112
msgid "All Accommodation Categories"
msgstr "Todas Categorias de Acomodação"

#: includes/post-types/room-type-cpt.php:113
msgid "Parent Accommodation Category"
msgstr "Categoria Principal de Acomodação"

#: includes/post-types/room-type-cpt.php:114
msgid "Parent Accommodation Category:"
msgstr "Categoria principal de Acomodação:"

#: includes/post-types/room-type-cpt.php:115
msgid "Edit Accommodation Category"
msgstr "Editar Categoria de Acomodação"

#: includes/post-types/room-type-cpt.php:116
msgid "Update Accommodation Category"
msgstr "Atualizar Categoria de Acomodação"

#: includes/post-types/room-type-cpt.php:117
msgid "Add New Accommodation Category"
msgstr "Adicionar Nova Categoria de Acomodação"

#: includes/post-types/room-type-cpt.php:118
msgid "New Accommodation Category Name"
msgstr "Nova Nome de Categoria de Acomodação"

#: includes/post-types/room-type-cpt.php:119
msgid "Separate categories with commas"
msgstr "Separar categorias com vírgulas"

#: includes/post-types/room-type-cpt.php:120
msgid "Add or remove categories"
msgstr "Adicionar ou remover categorias"

#: includes/post-types/room-type-cpt.php:121
msgid "Choose from the most used categories"
msgstr "Escolha entre as categorias mais usadas"

#: includes/post-types/room-type-cpt.php:122
msgid "No categories found."
msgstr "Nenhuma categoria encontrada."

#: includes/post-types/room-type-cpt.php:123
#: assets/blocks/blocks.js:816
msgid "Categories"
msgstr "Categorias"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:138
msgctxt "slug"
msgid "accommodation-category"
msgstr "accommodation-category"

#: includes/post-types/room-type-cpt.php:163
msgid "Accommodation Tags"
msgstr ""

#: includes/post-types/room-type-cpt.php:164
msgid "Accommodation Tag"
msgstr ""

#: includes/post-types/room-type-cpt.php:165
msgid "Search Accommodation Tags"
msgstr ""

#: includes/post-types/room-type-cpt.php:166
msgid "Popular Accommodation Tags"
msgstr ""

#: includes/post-types/room-type-cpt.php:167
msgid "All Accommodation Tags"
msgstr ""

#: includes/post-types/room-type-cpt.php:168
msgid "Parent Accommodation Tag"
msgstr ""

#: includes/post-types/room-type-cpt.php:169
msgid "Parent Accommodation Tag:"
msgstr ""

#: includes/post-types/room-type-cpt.php:170
msgid "Edit Accommodation Tag"
msgstr ""

#: includes/post-types/room-type-cpt.php:171
msgid "Update Accommodation Tag"
msgstr ""

#: includes/post-types/room-type-cpt.php:172
msgid "Add New Accommodation Tag"
msgstr ""

#: includes/post-types/room-type-cpt.php:173
msgid "New Accommodation Tag Name"
msgstr ""

#: includes/post-types/room-type-cpt.php:174
msgid "Separate tags with commas"
msgstr ""

#: includes/post-types/room-type-cpt.php:175
msgid "Add or remove tags"
msgstr ""

#: includes/post-types/room-type-cpt.php:176
msgid "Choose from the most used tags"
msgstr ""

#: includes/post-types/room-type-cpt.php:177
msgid "No tags found."
msgstr ""

#: includes/post-types/room-type-cpt.php:178
#: assets/blocks/blocks.js:828
msgid "Tags"
msgstr "Tags"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:192
msgctxt "slug"
msgid "accommodation-tag"
msgstr "accommodation-tag"

#: includes/post-types/room-type-cpt.php:217
#: includes/post-types/room-type-cpt.php:232
msgid "Amenities"
msgstr "Comodidades"

#: includes/post-types/room-type-cpt.php:218
msgid "Amenity"
msgstr "Comodidade"

#: includes/post-types/room-type-cpt.php:219
msgid "Search Amenities"
msgstr "Buscar Comodidades"

#: includes/post-types/room-type-cpt.php:220
msgid "Popular Amenities"
msgstr "Comodidades Populares"

#: includes/post-types/room-type-cpt.php:221
msgid "All Amenities"
msgstr "Todas as Comodidades"

#: includes/post-types/room-type-cpt.php:222
msgid "Parent Amenity"
msgstr "Comodidade para Pais"

#: includes/post-types/room-type-cpt.php:223
msgid "Parent Amenity:"
msgstr "Comodidade para pais:"

#: includes/post-types/room-type-cpt.php:224
msgid "Edit Amenity"
msgstr "Editar Comodidade"

#: includes/post-types/room-type-cpt.php:225
msgid "Update Amenity"
msgstr "Atualizar Comodidade"

#: includes/post-types/room-type-cpt.php:226
msgid "Add New Amenity"
msgstr "Adicionar Nova Comodidade"

#: includes/post-types/room-type-cpt.php:227
msgid "New Amenity Name"
msgstr "Nome da Nova Comodidade"

#: includes/post-types/room-type-cpt.php:228
msgid "Separate amenities with commas"
msgstr "Separar recursos com virgulas"

#: includes/post-types/room-type-cpt.php:229
msgid "Add or remove amenities"
msgstr "Adicionar ou remover recursos"

#: includes/post-types/room-type-cpt.php:230
msgid "Choose from the most used amenities"
msgstr "Escolha entre os recursos mais utilizadas"

#: includes/post-types/room-type-cpt.php:231
msgid "No amenities found."
msgstr "Nenhum recurso foi encontrado."

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:247
msgctxt "slug"
msgid "accommodation-facility"
msgstr "accommodation-facility"

#: includes/post-types/room-type-cpt.php:293
msgid "State the age or disable children in <a href=\"%s\">settings</a>."
msgstr ""

#: includes/post-types/room-type-cpt.php:303
msgid "Leave this option empty to calculate total capacity automatically to meet the exact number of adults AND children set above. This is the default behavior. Configure this option to allow any variations of adults OR children set above at checkout so that in total it meets the limit of manually set \"Capacity\". For example, configuration \"adults:5\", \"children:4\", \"capacity:5\" means the property can accommodate up to 5 adults, up to 4 children, but up to 5 guests in total (not 9)."
msgstr "Deixe essa opção em branco para calcular automaticamente a capacidade total para atender ao número exato de adultos E crianças definidos acima. Este é o comportamento padrão. Configure esta opção para permitir quaisquer variações de adultos OU crianças definidas acima no check-out, para que, no total, atenda ao limite de \"Capacidade\" definido manualmente. Por exemplo, a configuração \"adultos: 5\", \"crianças: 4\", \"capacidade: 5\" significa que a propriedade pode acomodar até 5 adultos, até 4 crianças, mas até 5 pessoas no total (não 9)."

#: includes/post-types/room-type-cpt.php:313
msgid "Base Adults Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:314
#: includes/post-types/room-type-cpt.php:325
msgid "An optional starting value used when creating seasonal prices in the Rates menu."
msgstr ""

#: includes/post-types/room-type-cpt.php:324
msgid "Base Children Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:334
msgid "Other"
msgstr "Outro"

#: includes/post-types/room-type-cpt.php:340
msgid "Size, %s"
msgstr "Tamanho, %s"

#: includes/post-types/room-type-cpt.php:341
msgid "Leave blank to hide."
msgstr "Deixe em branco para ocultar."

#: includes/post-types/room-type-cpt.php:355
msgid "City view, seaside, swimming pool etc."
msgstr "Vista da cidade, à beira-mar, piscina etc."

#: includes/post-types/room-type-cpt.php:366
msgid "Bed type"
msgstr "Tipo de cama"

#: includes/post-types/room-type-cpt.php:369
msgid "Set bed types list in <a href=\"%link%\" target=\"_blank\">settings</a>."
msgstr "Definir a lista de tipos de cama em <a href=\"%link%\" target=\"_blank\">settings</a>."

#: includes/post-types/room-type-cpt.php:379
msgid "Photo Gallery"
msgstr "Galeria de fotos"

#: includes/post-types/room-type-cpt.php:390
#: includes/post-types/room-type-cpt.php:395
msgid "Available Services"
msgstr "Serviços Disponíveis"

#: includes/post-types/season-cpt.php:25
msgid "Season Info"
msgstr "Informação da Temporada"

#: includes/post-types/season-cpt.php:32
#: includes/reports/earnings-report.php:332
msgid "Start date"
msgstr "Data de Inicio"

#: includes/post-types/season-cpt.php:45
#: includes/reports/earnings-report.php:344
msgid "End date"
msgstr "Data final"

#: includes/post-types/season-cpt.php:55
msgid "Applied for days"
msgstr "Aplicado por dias"

#: includes/post-types/season-cpt.php:59
msgid "Hold Ctrl / Cmd to select multiple."
msgstr "Segure Ctrl/Cmd para selecionar varios."

#: includes/post-types/season-cpt.php:68
msgid "Annual repeats begin on the Start date of the season, for one year from the current date."
msgstr ""

#: includes/post-types/season-cpt.php:70
msgid "Does not repeat"
msgstr ""

#: includes/post-types/season-cpt.php:81
msgid "Repeat until date"
msgstr ""

#: includes/post-types/season-cpt.php:100
msgctxt "Add New Season"
msgid "Add New"
msgstr "Adicionar Nova"

#: includes/post-types/season-cpt.php:101
msgid "Add New Season"
msgstr "Adicionar Nova Temporada"

#: includes/post-types/season-cpt.php:102
msgid "Edit Season"
msgstr "Editar Temporada"

#: includes/post-types/season-cpt.php:103
msgid "New Season"
msgstr "Nova Temporada"

#: includes/post-types/season-cpt.php:104
msgid "View Season"
msgstr "Visualizar Temporada"

#: includes/post-types/season-cpt.php:105
msgid "Search Season"
msgstr "Buscar Temporada"

#: includes/post-types/season-cpt.php:106
msgid "No seasons found"
msgstr "Nenhuma estação foi encontrada"

#: includes/post-types/season-cpt.php:107
msgid "No seasons found in Trash"
msgstr "Nenhuma estação foi encontrada na Lixeira"

#: includes/post-types/season-cpt.php:109
msgid "Insert into season description"
msgstr "Inserir na descrição da temporada"

#: includes/post-types/season-cpt.php:110
msgid "Uploaded to this season"
msgstr "Enviado para esta temporada"

#: includes/post-types/season-cpt.php:115
msgid "This is where you can add new seasons."
msgstr "Aqui é possível adicionar novas temporadas."

#: includes/post-types/service-cpt.php:92
#: includes/views/booking-view.php:206
msgid "Service"
msgstr "Serviço"

#: includes/post-types/service-cpt.php:93
msgctxt "Add New Service"
msgid "Add New"
msgstr "Adicionar Novo"

#: includes/post-types/service-cpt.php:94
msgid "Add New Service"
msgstr "Adicionar Novo Serviço"

#: includes/post-types/service-cpt.php:95
msgid "Edit Service"
msgstr "Editar Serviço"

#: includes/post-types/service-cpt.php:96
msgid "New Service"
msgstr "Novo Serviço"

#: includes/post-types/service-cpt.php:97
msgid "View Service"
msgstr "Visualizar Serviço"

#: includes/post-types/service-cpt.php:98
msgid "Search Service"
msgstr "Buscar Serviço"

#: includes/post-types/service-cpt.php:99
msgid "No services found"
msgstr "Nenhum serviço encontrado"

#: includes/post-types/service-cpt.php:100
msgid "No services found in Trash"
msgstr "Nenhum serviço encontrado na Lixeira"

#: includes/post-types/service-cpt.php:102
msgid "Insert into service description"
msgstr "Inserir na descrição do serviço"

#: includes/post-types/service-cpt.php:103
msgid "Uploaded to this service"
msgstr "Enviados para este serviço"

#. translators: do not translate
#: includes/post-types/service-cpt.php:120
msgctxt "slug"
msgid "services"
msgstr "services"

#: includes/post-types/service-cpt.php:156
msgid "How many times the customer will be charged."
msgstr "Quantas vezes o cliente será tarifado."

#: includes/post-types/service-cpt.php:167
msgid "Minimum"
msgstr "Mínimo"

#: includes/post-types/service-cpt.php:181
msgid "Maximum"
msgstr "Máximo"

#: includes/post-types/service-cpt.php:193
msgid "Empty means unlimited"
msgstr "Vazio significa ilimitado"

#: includes/reports/data/report-earnings-by-dates-data.php:29
msgctxt "Booking status"
msgid "Pending"
msgstr "Pendente"

#: includes/reports/earnings-report.php:91
msgid "Total Sales"
msgstr ""

#: includes/reports/earnings-report.php:94
msgid "Total Without Taxes"
msgstr ""

#: includes/reports/earnings-report.php:97
msgid "Total Fees"
msgstr ""

#: includes/reports/earnings-report.php:100
msgid "Total Services"
msgstr ""

#: includes/reports/earnings-report.php:103
msgid "Total Discounts"
msgstr ""

#: includes/reports/earnings-report.php:106
msgid "Total Bookings"
msgstr "Total de reservas"

#: includes/reports/earnings-report.php:289
#: includes/reports/report-filters.php:38
msgid "Revenue"
msgstr ""

#: includes/reports/earnings-report.php:352
#: includes/views/create-booking/checkout-view.php:56
#: includes/views/shortcodes/checkout-view.php:90
msgid "Apply"
msgstr "Aplicar"

#: includes/reports/earnings-report.php:496
msgid "From %s to %s"
msgstr "De %s até %s"

#: includes/reports/report-filters.php:61
msgid "Today"
msgstr ""

#: includes/reports/report-filters.php:64
msgid "Yesterday"
msgstr ""

#: includes/reports/report-filters.php:67
msgid "This week"
msgstr ""

#: includes/reports/report-filters.php:70
msgid "Last week"
msgstr ""

#: includes/reports/report-filters.php:73
msgid "Last 30 days"
msgstr ""

#: includes/reports/report-filters.php:76
msgid "This month"
msgstr ""

#: includes/reports/report-filters.php:79
msgid "Last month"
msgstr ""

#: includes/reports/report-filters.php:82
msgid "This quarter"
msgstr ""

#: includes/reports/report-filters.php:85
msgid "Last quarter"
msgstr ""

#: includes/reports/report-filters.php:88
msgid "This year"
msgstr ""

#: includes/reports/report-filters.php:91
msgid "Last year"
msgstr ""

#. translators: %s - original Rate title
#: includes/repositories/rate-repository.php:195
msgid "%s - copy"
msgstr ""

#: includes/script-managers/admin-script-manager.php:92
msgid "Accommodation Type Gallery"
msgstr "Galeria de Tipo de Acomodação"

#: includes/script-managers/admin-script-manager.php:93
msgid "Add Gallery To Accommodation Type"
msgstr "Adicionar Galeria ao Tipo de Acomodação"

#: includes/script-managers/admin-script-manager.php:105
msgid "Display imported bookings."
msgstr ""

#: includes/script-managers/admin-script-manager.php:106
msgid "Processing..."
msgstr "Processando..."

#: includes/script-managers/admin-script-manager.php:107
msgid "Cancelling..."
msgstr "Cancelando..."

#: includes/script-managers/admin-script-manager.php:108
msgid "Want to delete?"
msgstr ""

#: includes/script-managers/public-script-manager.php:204
msgid "Not available"
msgstr "Não disponível"

#: includes/script-managers/public-script-manager.php:205
msgid "This is earlier than allowed by our advance reservation rules."
msgstr ""

#: includes/script-managers/public-script-manager.php:206
msgid "This is later than allowed by our advance reservation rules."
msgstr "A data ultrapassa a data permitida pelas regras de pre reserva."

#: includes/script-managers/public-script-manager.php:210
msgid "Day in the past"
msgstr "Dia no passado"

#: includes/script-managers/public-script-manager.php:211
msgid "Check-in date"
msgstr "Data de checkin"

#: includes/script-managers/public-script-manager.php:212
msgid "Less than min days stay"
msgstr "A estadia é menor do que o mínimo de dias"

#: includes/script-managers/public-script-manager.php:213
msgid "More than max days stay"
msgstr "Estadia por mais do número máximo de dias"

#: includes/script-managers/public-script-manager.php:215
msgid "Later than max date for current check-in date"
msgstr "Após a data limite para checkin"

#: includes/script-managers/public-script-manager.php:216
msgid "Rules:"
msgstr "Regras:"

#: includes/script-managers/public-script-manager.php:217
msgid "Tokenisation failed: %s"
msgstr "A tokenização falhou: %s"

#: includes/script-managers/public-script-manager.php:218
#: includes/script-managers/public-script-manager.php:219
msgid "%1$d &times; &ldquo;%2$s&rdquo; has been added to your reservation."
msgid_plural "%1$d &times; &ldquo;%2$s&rdquo; have been added to your reservation."
msgstr[0] "%1$d &times; &ldquo;%2$s&rdquo; foi adicionado a sua reserva."
msgstr[1] "%1$d &times; &ldquo;%2$s&rdquo; foram adicionado a sua reserva."

#: includes/script-managers/public-script-manager.php:220
#: includes/script-managers/public-script-manager.php:221
msgid "%s accommodation selected."
msgid_plural "%s accommodations selected."
msgstr[0] "%s acomodação selecionada."
msgstr[1] "%s acomodações selecionadas."

#: includes/script-managers/public-script-manager.php:222
msgid "Coupon code is empty."
msgstr "O código do cupom está vázio."

#: includes/script-managers/public-script-manager.php:225
msgid "Select dates"
msgstr ""

#: includes/settings/main-settings.php:26
msgid "Dark Blue"
msgstr "Azul Escuro"

#: includes/settings/main-settings.php:27
msgid "Dark Green"
msgstr "Verde Escuro"

#: includes/settings/main-settings.php:28
msgid "Dark Red"
msgstr "Vermelho Escuro"

#: includes/settings/main-settings.php:29
msgid "Grayscale"
msgstr "Cinza"

#: includes/settings/main-settings.php:30
msgid "Light Blue"
msgstr "Azul Claro"

#: includes/settings/main-settings.php:31
msgid "Light Coral"
msgstr "Coral Claro"

#: includes/settings/main-settings.php:32
msgid "Light Green"
msgstr "Verde Claro"

#: includes/settings/main-settings.php:33
msgid "Light Yellow"
msgstr "Amarela claro"

#: includes/settings/main-settings.php:34
msgid "Minimal Blue"
msgstr "Azul Mínimo"

#: includes/settings/main-settings.php:35
msgid "Minimal Orange"
msgstr "Laranja Mínimo"

#: includes/settings/main-settings.php:36
msgid "Minimal"
msgstr "Mínimo"

#: includes/settings/main-settings.php:38
msgid "Sky Blue"
msgstr "Céu azul"

#: includes/settings/main-settings.php:39
msgid "Slate Blue"
msgstr "Azul Ardósia"

#: includes/settings/main-settings.php:40
msgid "Turquoise"
msgstr "Turquesa"

#: includes/shortcodes/account-shortcode.php:212
#: includes/views/shortcodes/checkout-view.php:22
msgid "Invalid login or password."
msgstr ""

#: includes/shortcodes/account-shortcode.php:221
msgid "Account data updated."
msgstr ""

#: includes/shortcodes/account-shortcode.php:227
msgid "Password changed."
msgstr ""

#: includes/shortcodes/account-shortcode.php:238
msgid "Dashboard"
msgstr ""

#: includes/shortcodes/account-shortcode.php:240
msgid "Account"
msgstr ""

#: includes/shortcodes/account-shortcode.php:241
msgid "Logout"
msgstr ""

#: includes/shortcodes/account-shortcode.php:279
msgid "Passwords do not match."
msgstr ""

#: includes/shortcodes/account-shortcode.php:282
msgid "Please, provide a valid current password."
msgstr ""

#: includes/shortcodes/account-shortcode.php:301
#: includes/views/shortcodes/checkout-view.php:54
msgid "Lost your password?"
msgstr ""

#: includes/shortcodes/booking-confirmation-shortcode.php:294
msgid "Payment:"
msgstr "Forma de pagamento:"

#: includes/shortcodes/booking-confirmation-shortcode.php:302
msgid "Payment Method:"
msgstr "Forma de pagamento:"

#: includes/shortcodes/booking-confirmation-shortcode.php:315
#: templates/shortcodes/booking-details/booking-details.php:42
msgid "Status:"
msgstr "Estado:"

#: includes/shortcodes/checkout-shortcode.php:196
msgid "Bookings are disabled in the settings."
msgstr ""

#: includes/shortcodes/checkout-shortcode/step-booking.php:151
msgid "Checkout data is not valid."
msgstr ""

#: includes/shortcodes/checkout-shortcode/step-booking.php:449
msgid "Payment method is not valid."
msgstr "O método de pagamento não está válido."

#: includes/shortcodes/checkout-shortcode/step-checkout.php:193
msgid "Accommodation count is not valid."
msgstr "A contagem da acomodação não está válida."

#: includes/shortcodes/checkout-shortcode/step.php:110
msgid "Accommodation is already booked."
msgstr "A acomodação já está reservada."

#: includes/shortcodes/checkout-shortcode/step.php:120
#: includes/shortcodes/checkout-shortcode/step.php:129
#: includes/shortcodes/checkout-shortcode/step.php:138
msgid "Reservation submitted"
msgstr "Reserva enviada"

#: includes/shortcodes/checkout-shortcode/step.php:121
msgid "Details of your reservation have just been sent to you in a confirmation email. Please check your inbox to complete booking."
msgstr "Os detalhes da sua reserva foram enviados para você por e-mail. Verifique a sua caixa de entrada para concluí-la."

#: includes/shortcodes/checkout-shortcode/step.php:130
#: includes/shortcodes/checkout-shortcode/step.php:139
msgid "We received your booking request. Once it is confirmed we will notify you via email."
msgstr "Recebemos seu pedido de reserva. Assim que for confirmado, nós lhe notificaremos por e-mail."

#: includes/shortcodes/room-rates-shortcode.php:104
#: template-functions.php:31
msgid "Choose dates to see relevant prices"
msgstr "Escolha as datas para ver os preços relevantes"

#: includes/shortcodes/search-results-shortcode.php:766
msgid "Select from available accommodations."
msgstr "Selecione uma das as acomodações disponíveis."

#: includes/shortcodes/search-results-shortcode.php:775
#: includes/shortcodes/search-results-shortcode.php:1013
#: template-functions.php:843
msgid "Confirm Reservation"
msgstr "Confirmar Reserva"

#: includes/shortcodes/search-results-shortcode.php:804
msgid "Recommended for %d adult"
msgid_plural "Recommended for %d adults"
msgstr[0] "Recomendado para %d adulto"
msgstr[1] "Recomendado para %d adultos"

#: includes/shortcodes/search-results-shortcode.php:806
msgid " and %d child"
msgid_plural " and %d children"
msgstr[0] " e %d criança"
msgstr[1] " e %d crianças"

#: includes/shortcodes/search-results-shortcode.php:809
msgid "Recommended for %d guest"
msgid_plural "Recommended for %d guests"
msgstr[0] "Recomendado para %d adulto"
msgstr[1] "Recomendado para %d adultos"

#: includes/shortcodes/search-results-shortcode.php:891
msgid "Max occupancy:"
msgstr "Ocupação Máxima:"

#: includes/shortcodes/search-results-shortcode.php:910
msgid "%d child"
msgid_plural "%d children"
msgstr[0] "%d criança"
msgstr[1] "%d crianças"

#: includes/shortcodes/search-results-shortcode.php:945
#: templates/create-booking/results/reserve-rooms.php:76
msgid "Reserve"
msgstr "Reservar"

#: includes/shortcodes/search-results-shortcode.php:1002
msgid "of %d accommodation available."
msgid_plural "of %d accommodations available."
msgstr[0] "da %d acomodação disponivel."
msgstr[1] "das %d acomodações disponíveis."

#. translators: Verb. To book an accommodation.
#: includes/shortcodes/search-results-shortcode.php:1012
#: template-functions.php:531
#: template-functions.php:544
msgid "Book"
msgstr "Reservar"

#: includes/users-and-roles/customers.php:290
#: includes/users-and-roles/customers.php:383
msgid "Please, provide a valid email."
msgstr ""

#: includes/users-and-roles/customers.php:318
msgid "Could not create a customer."
msgstr ""

#: includes/users-and-roles/customers.php:379
msgid "Could not retrieve a customer."
msgstr ""

#: includes/users-and-roles/customers.php:531
#: includes/users-and-roles/customers.php:577
msgid "Please, provide a valid Customer ID."
msgstr ""

#: includes/users-and-roles/customers.php:563
msgid "A database error."
msgstr ""

#: includes/users-and-roles/customers.php:583
msgid "No customer was deleted."
msgstr ""

#: includes/users-and-roles/customers.php:694
#: includes/views/shortcodes/checkout-view.php:31
msgid "An account with this email already exists. Please, log in."
msgstr ""

#: includes/users-and-roles/roles.php:34
msgid "Hotel Manager"
msgstr ""

#: includes/users-and-roles/roles.php:41
msgid "Hotel Worker"
msgstr ""

#: includes/users-and-roles/roles.php:48
msgid "Hotel Customer"
msgstr ""

#: includes/users-and-roles/user.php:54
msgid "Please provide a valid email address."
msgstr ""

#: includes/users-and-roles/user.php:69
msgid "Please enter a valid account username."
msgstr ""

#: includes/users-and-roles/user.php:73
msgid "An account is already registered with that username. Please choose another."
msgstr ""

#: includes/users-and-roles/user.php:81
msgid "Please enter an account password."
msgstr ""

#: includes/utils/date-utils.php:145
msgid "Sunday"
msgstr "Domigo"

#: includes/utils/date-utils.php:146
msgid "Monday"
msgstr "Segunda-feira"

#: includes/utils/date-utils.php:147
msgid "Tuesday"
msgstr "Terça-feira"

#: includes/utils/date-utils.php:148
msgid "Wednesday"
msgstr "Quarta-feira"

#: includes/utils/date-utils.php:149
msgid "Thursday"
msgstr "Quinta-feira"

#: includes/utils/date-utils.php:150
msgid "Friday"
msgstr "Sexta-feira"

#: includes/utils/date-utils.php:151
msgid "Saturday"
msgstr "Sábado"

#: includes/utils/parse-utils.php:135
msgid "Check-out date cannot be earlier than check-in date."
msgstr "A data de check-out não pode ser anterior à data de check-in."

#: includes/utils/parse-utils.php:159
msgid "Adults number is not valid"
msgstr "O número de adultos não é válido"

#: includes/utils/parse-utils.php:183
msgid "Children number is not valid"
msgstr "O número de crianças não é válida"

#: includes/utils/taxes-and-fees-utils.php:27
msgctxt "Text about taxes and fees below the price."
msgid " (+taxes and fees)"
msgstr ""

#. translators: %s is a tax value
#: includes/utils/taxes-and-fees-utils.php:56
msgctxt "Text about taxes and fees below the price."
msgid " (+%s taxes and fees)"
msgstr ""

#: includes/utils/taxes-and-fees-utils.php:84
msgctxt "Text about taxes and fees below the price."
msgid " (includes taxes and fees)"
msgstr ""

#: includes/views/booking-view.php:79
msgctxt "Accommodation type in price breakdown table. Example: #1 Double Room"
msgid "#%d %s"
msgstr ""

#: includes/views/booking-view.php:82
msgid "Expand"
msgstr "Expandir"

#: includes/views/booking-view.php:91
#: includes/views/edit-booking/checkout-view.php:209
msgid "Rate: %s"
msgstr "Taxa: %s"

#: includes/views/booking-view.php:125
msgid "Dates"
msgstr "Datas"

#: includes/views/booking-view.php:207
#: includes/views/loop-room-type-view.php:39
#: includes/views/single-room-type-view.php:131
#: includes/widgets/rooms-widget.php:197
#: assets/blocks/blocks.js:484
#: assets/blocks/blocks.js:734
#: assets/blocks/blocks.js:1263
msgid "Details"
msgstr "Detalhes"

#: includes/views/booking-view.php:380
msgid "Subtotal"
msgstr ""

#: includes/views/booking-view.php:393
msgid "Coupon: %s"
msgstr "Cupom: %s"

#: includes/views/booking-view.php:412
msgid "Subtotal (excl. taxes)"
msgstr ""

#: includes/views/booking-view.php:422
msgid "Taxes"
msgstr ""

#: includes/views/create-booking/checkout-view.php:55
#: includes/views/shortcodes/checkout-view.php:89
msgid "Coupon Code:"
msgstr "Código do Cupom:"

#: includes/views/edit-booking/checkout-view.php:25
msgid "New Booking Details"
msgstr "Novos detalhes da reserva"

#: includes/views/edit-booking/checkout-view.php:43
msgid "Original Booking Details"
msgstr "Detalhes originais da reserva"

#: includes/views/edit-booking/checkout-view.php:154
#: includes/views/shortcodes/checkout-view.php:269
#: template-functions.php:794
#: templates/create-booking/search/search-form.php:111
#: templates/shortcodes/search/search-form.php:105
msgid "Children %s"
msgstr ""

#: includes/views/edit-booking/checkout-view.php:232
#: templates/emails/reserved-room-details.php:30
msgid "Additional Services"
msgstr "Serviços Adicionais"

#: includes/views/edit-booking/checkout-view.php:249
#: includes/views/reserved-room-view.php:26
#: template-functions.php:937
msgid "x %d guest"
msgid_plural "x %d guests"
msgstr[0] "x %d convidado"
msgstr[1] "x %d convidados"

#: includes/views/edit-booking/checkout-view.php:253
#: includes/views/reserved-room-view.php:31
#: template-functions.php:940
msgid "x %d time"
msgid_plural "x %d times"
msgstr[0] ""
msgstr[1] ""

#: includes/views/global-view.php:53
msgid "Accommodation pagination"
msgstr "Paginação da Acomodação"

#: includes/views/global-view.php:56
msgid "Services pagination"
msgstr "Paginação de serviços"

#: includes/views/loop-room-type-view.php:55
#: includes/views/single-room-type-view.php:147
#: templates/widgets/rooms/room-content.php:99
msgid "Categories:"
msgstr "Categorias:"

#: includes/views/loop-room-type-view.php:67
#: includes/views/single-room-type-view.php:159
#: templates/widgets/rooms/room-content.php:129
msgid "Amenities:"
msgstr "Comodidades:"

#: includes/views/loop-room-type-view.php:97
#: includes/views/loop-room-type-view.php:115
#: includes/views/single-room-type-view.php:189
#: includes/views/single-room-type-view.php:207
#: templates/widgets/rooms/room-content.php:67
#: templates/widgets/rooms/room-content.php:79
#: templates/widgets/search-availability/search-form.php:80
msgid "Guests:"
msgstr "Convidados:"

#: includes/views/loop-room-type-view.php:140
#: includes/views/single-room-type-view.php:232
#: templates/widgets/rooms/room-content.php:175
msgid "Bed Type:"
msgstr "Tipo de Cama:"

#: includes/views/loop-room-type-view.php:164
#: includes/views/single-room-type-view.php:256
#: templates/widgets/rooms/room-content.php:159
msgid "View:"
msgstr "Visualizar:"

#: includes/views/loop-room-type-view.php:184
#: includes/views/single-room-type-view.php:276
#: template-functions.php:839
#: templates/widgets/rooms/room-content.php:227
msgid "Prices start at:"
msgstr "Preço de:"

#: includes/views/loop-service-view.php:46
msgid "Price:"
msgstr "Preço:"

#: includes/views/shortcodes/checkout-view.php:49
msgid "Returning customer?"
msgstr ""

#: includes/views/shortcodes/checkout-view.php:50
msgid "Click here to log in"
msgstr ""

#. translators: 1 - username;
#: includes/views/shortcodes/checkout-view.php:70
#: templates/account/dashboard.php:29
msgid "Hello %1$s (not %1$s? <a href=\"%2$s\">Log out</a>)."
msgstr ""

#: includes/views/shortcodes/checkout-view.php:182
msgid "Accommodation #%d"
msgstr "Acomodação #%d"

#: includes/views/shortcodes/checkout-view.php:186
msgid "Accommodation Type:"
msgstr "Tipo de acomodação:"

#: includes/views/shortcodes/checkout-view.php:324
msgid "Choose Rate"
msgstr "Escolher Taxa"

#: includes/views/shortcodes/checkout-view.php:397
msgid "Choose Additional Services"
msgstr "Escolha Serviços Adicionais"

#: includes/views/shortcodes/checkout-view.php:429
msgid "for "
msgstr "para "

#: includes/views/shortcodes/checkout-view.php:442
msgctxt "Example: Breakfast for X guest(s)"
msgid " guest(s)"
msgstr ""

#: includes/views/shortcodes/checkout-view.php:464
msgid "time(s)"
msgstr "vez(es)"

#: includes/views/shortcodes/checkout-view.php:533
msgctxt "I've read and accept the terms & conditions"
msgid "terms & conditions"
msgstr "termos e condições"

#: includes/views/shortcodes/checkout-view.php:536
msgctxt "I've read and accept the <tag>terms & conditions</tag>"
msgid "I've read and accept the %s"
msgstr "Eu li e aceito os %s"

#: includes/views/shortcodes/checkout-view.php:579
msgid "Your Information"
msgstr "Sua Informação"

#: includes/views/shortcodes/checkout-view.php:582
#: template-functions.php:696
#: templates/widgets/search-availability/search-form.php:24
msgid "Required fields are followed by %s"
msgstr "Os campos obrigatórios são %s"

#: includes/views/shortcodes/checkout-view.php:758
msgid "Create an account"
msgstr ""

#: includes/views/shortcodes/checkout-view.php:775
msgid "Payment Method"
msgstr "Método de Pagamento"

#: includes/views/shortcodes/checkout-view.php:780
msgid "Sorry, it seems that there are no available payment methods."
msgstr "Desculpe, mas parece que não existem métodos de pagamento disponíveis."

#: includes/views/shortcodes/checkout-view.php:874
#: templates/emails/admin-customer-cancelled-booking.php:32
#: templates/emails/admin-customer-confirmed-booking.php:32
#: templates/emails/admin-payment-confirmed-booking.php:39
#: templates/emails/admin-pending-booking.php:32
#: templates/emails/customer-approved-booking.php:28
#: templates/emails/customer-cancelled-booking.php:26
#: templates/emails/customer-confirmation-booking.php:32
#: templates/emails/customer-pending-booking.php:30
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:27
msgid "Total Price:"
msgstr "Valor total:"

#: includes/views/shortcodes/checkout-view.php:886
msgid "Deposit:"
msgstr "Depósito:"

#: includes/views/shortcodes/checkout-view.php:906
#: templates/shortcodes/booking-details/booking-details.php:25
#: templates/widgets/search-availability/search-form.php:35
msgid "Check-in:"
msgstr "Checkin:"

#: includes/views/shortcodes/checkout-view.php:913
msgctxt "from 10:00 am"
msgid "from"
msgstr "das"

#: includes/views/shortcodes/checkout-view.php:929
#: templates/shortcodes/booking-details/booking-details.php:29
#: templates/widgets/search-availability/search-form.php:54
msgid "Check-out:"
msgstr "Checkout:"

#: includes/views/shortcodes/checkout-view.php:936
msgctxt "until 10:00 am"
msgid "until"
msgstr "as"

#: includes/views/shortcodes/checkout-view.php:1012
#: templates/create-booking/checkout/checkout-form.php:42
msgid "Book Now"
msgstr "Reserve Já"

#: includes/views/single-room-type-view.php:127
msgid "Availability"
msgstr "Disponibilidade"

#: includes/views/single-room-type-view.php:292
msgid "Reservation Form"
msgstr "Ficha de Reserva"

#: includes/widgets/rooms-widget.php:24
msgid "Display Accommodation Types"
msgstr "Mostrar Tipos de Acomodação"

#: includes/widgets/rooms-widget.php:169
#: includes/widgets/search-availability-widget.php:236
msgid "Title:"
msgstr "Titlo:"

#: includes/widgets/rooms-widget.php:189
#: assets/blocks/blocks.js:448
#: assets/blocks/blocks.js:698
#: assets/blocks/blocks.js:1227
msgid "Featured Image"
msgstr ""

#: includes/widgets/rooms-widget.php:193
#: assets/blocks/blocks.js:472
#: assets/blocks/blocks.js:722
#: assets/blocks/blocks.js:1251
msgid "Excerpt (short description)"
msgstr "Mostrar Fragmento (breve descrição)"

#: includes/widgets/rooms-widget.php:205
#: assets/blocks/blocks.js:770
#: assets/blocks/blocks.js:1299
msgid "Book Button"
msgstr "Botão de Reserva"

#: includes/widgets/search-availability-widget.php:50
#: includes/wizard.php:84
msgid "Search Availability"
msgstr "Pesquisa de Disponibilidade"

#: includes/widgets/search-availability-widget.php:53
msgid "Search Availability Form"
msgstr "Formulário de Pesquisa de Disponibilidade"

#: includes/widgets/search-availability-widget.php:240
msgid "Check-in Date:"
msgstr "Date de Checkout:"

#: includes/widgets/search-availability-widget.php:241
#: includes/widgets/search-availability-widget.php:246
msgctxt "Date format tip"
msgid "Preset date. Formatted as %s"
msgstr "Data predefinida. Formatada como %s"

#: includes/widgets/search-availability-widget.php:244
msgid "Check-out Date:"
msgstr "Date de Check-out:"

#: includes/widgets/search-availability-widget.php:249
msgid "Preset Adults:"
msgstr "Preset Adultos:"

#: includes/widgets/search-availability-widget.php:257
msgid "Preset Children:"
msgstr "Preset Crianças:"

#: includes/widgets/search-availability-widget.php:265
msgid "Attributes:"
msgstr "Atributos:"

#: includes/wizard.php:34
msgid "Booking Confirmation and Search Results pages are required to handle bookings. Press \"Install Pages\" button to create and set up these pages. Dismiss this notice if you already installed them."
msgstr "As páginas de Checkout e Resultados de buscas são necessárias para gerenciar reservas. Pressione o botão \"Instalar Páginas\" para criar e configurá-las. Ignore este aviso se já tiver instalado elas."

#: includes/wizard.php:35
msgid "Install Pages"
msgstr "Instalar Páginas"

#: includes/wizard.php:147
msgid "Booking Canceled"
msgstr ""

#: includes/wizard.php:148
msgid "Your reservation is canceled."
msgstr ""

#: includes/wizard.php:183
msgid "Reservation Received"
msgstr "Reserva Recebida."

#: includes/wizard.php:196
msgid "Transaction Failed"
msgstr ""

#: includes/wizard.php:197
msgid "Unfortunately, your transaction cannot be completed at this time. Please try again or contact us."
msgstr ""

#: plugin.php:1100
msgid "Prices start at: %s"
msgstr ""

#: template-functions.php:563
msgid "View Details"
msgstr "Ver Detalhes"

#: template-functions.php:593
#: template-functions.php:652
msgid "Accommodation %s not found."
msgstr ""

#: template-functions.php:707
#: template-functions.php:716
#: templates/create-booking/search/search-form.php:43
#: templates/create-booking/search/search-form.php:63
#: templates/edit-booking/edit-dates.php:30
#: templates/edit-booking/edit-dates.php:39
#: templates/shortcodes/search/search-form.php:36
#: templates/shortcodes/search/search-form.php:56
#: templates/widgets/search-availability/search-form.php:36
#: templates/widgets/search-availability/search-form.php:55
msgctxt "Date format tip"
msgid "Formatted as %s"
msgstr "Formatado como %s"

#: template-functions.php:831
msgid "Reserve %1$s of %2$s available accommodations."
msgstr ""

#: template-functions.php:835
msgid "%s is available for selected dates."
msgstr ""

#: template-functions.php:849
#: templates/edit-booking/edit-dates.php:46
msgid "Check Availability"
msgstr "Verificar Disponibilidade"

#: template-functions.php:910
msgid "Rate:"
msgstr "Taxa:"

#: template-functions.php:930
msgid "Services:"
msgstr "Serviços:"

#: template-functions.php:953
msgid "Guest:"
msgstr "Convidados:"

#: template-functions.php:976
msgid "Payment ID"
msgstr "ID de Pagamento"

#: template-functions.php:1008
msgid "Total Paid"
msgstr "Total Pago"

#: template-functions.php:1017
msgid "To Pay"
msgstr "Para Pagar"

#: template-functions.php:1042
msgid "Add Payment Manually"
msgstr "Adicionar Pagamento Manualmente"

#: templates/account/account-details.php:78
msgid "Change Password"
msgstr ""

#: templates/account/account-details.php:81
msgid "Old Password"
msgstr ""

#: templates/account/account-details.php:85
msgid "New Password"
msgstr ""

#: templates/account/account-details.php:89
msgid "Confirm New Password"
msgstr ""

#: templates/account/account-details.php:99
msgid "You are not allowed to access this page."
msgstr ""

#: templates/account/bookings.php:116
#: templates/account/bookings.php:121
msgid "No bookings found."
msgstr "Nenhuma reserva encontrada."

#: templates/account/dashboard.php:41
msgid "From your account dashboard you can view <a href=\"%1$s\">your recent bookings</a> or edit your <a href=\"%2$s\">password and account details</a>."
msgstr ""

#: templates/create-booking/results/reserve-rooms.php:37
msgid "Base price"
msgstr ""

#: templates/create-booking/results/rooms-found.php:19
#: templates/shortcodes/search-results/results-info.php:17
msgid "%s accommodation found"
msgid_plural "%s accommodations found"
msgstr[0] "%s acomodação encontrada"
msgstr[1] "%s acomodações encontradas"

#: templates/create-booking/results/rooms-found.php:24
#: templates/shortcodes/search-results/results-info.php:21
msgid " from %s - till %s"
msgstr " De %s - a %s"

#: templates/edit-booking/add-room-popup.php:24
#: templates/edit-booking/edit-reserved-rooms.php:36
msgid "Add Accommodation"
msgstr "Adicionar acomodação"

#: templates/edit-booking/checkout-form.php:28
msgid "Save"
msgstr "Salvar"

#: templates/edit-booking/edit-dates.php:25
msgid "Choose new dates to check availability of reserved accommodations in the original booking."
msgstr "Escolha novas datas para verificar a disponibilidade de acomodações reservadas na reserva original."

#: templates/edit-booking/edit-reserved-rooms.php:39
msgid "Add, remove or replace accommodations in the original booking."
msgstr "Adicione, remova ou substitua acomodações na reserva original."

#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Not Available"
msgstr "Não disponível"

#: templates/edit-booking/edit-reserved-rooms.php:79
#: templates/edit-booking/summary-table.php:65
msgid "Continue"
msgstr "Continuar"

#: templates/edit-booking/summary-table.php:26
msgid "Choose how to associate data"
msgstr "Escolha como associar os dados"

#: templates/edit-booking/summary-table.php:27
msgid "Use Source Accommodation to assign pre-filled booking information available in the original booking, e.g., full guest name, selected rate, services, etc."
msgstr "Use a acomodação de origem para atribuir informações de reserva pré-preenchidas disponíveis na reserva original, por exemplo, nome completo do hóspede, tarifa selecionada, serviços etc."

#: templates/edit-booking/summary-table.php:32
msgid "Source accommodation"
msgstr "Origem da acomodação"

#: templates/edit-booking/summary-table.php:34
msgid "Target accommodation"
msgstr "Acomodação alvo"

#: templates/emails/admin-customer-cancelled-booking.php:15
msgid "Booking #%s is cancelled by customer."
msgstr "A reserva #%s foi cancelada pelo cliente."

#: templates/emails/admin-customer-cancelled-booking.php:17
#: templates/emails/admin-customer-confirmed-booking.php:17
#: templates/emails/admin-payment-confirmed-booking.php:24
#: templates/emails/admin-pending-booking.php:17
#: templates/emails/customer-approved-booking.php:17
#: templates/emails/customer-cancelled-booking.php:18
#: templates/emails/customer-confirmation-booking.php:24
#: templates/emails/customer-pending-booking.php:19
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:19
msgid "Details of booking"
msgstr "Detalhes da reserva"

#: templates/emails/admin-customer-cancelled-booking.php:18
#: templates/emails/admin-customer-confirmed-booking.php:18
#: templates/emails/admin-payment-confirmed-booking.php:25
#: templates/emails/admin-pending-booking.php:18
#: templates/emails/customer-approved-booking.php:20
#: templates/emails/customer-cancelled-booking.php:21
#: templates/emails/customer-confirmation-booking.php:27
#: templates/emails/customer-pending-booking.php:22
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:22
msgid "Check-in: %1$s, from %2$s"
msgstr "Check-in: %1$s, das %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:20
#: templates/emails/admin-customer-confirmed-booking.php:20
#: templates/emails/admin-payment-confirmed-booking.php:27
#: templates/emails/admin-pending-booking.php:20
#: templates/emails/customer-approved-booking.php:22
#: templates/emails/customer-cancelled-booking.php:23
#: templates/emails/customer-confirmation-booking.php:29
#: templates/emails/customer-pending-booking.php:24
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:24
msgid "Check-out: %1$s, until %2$s"
msgstr "Check-out: %1$s, das %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:24
#: templates/emails/admin-customer-confirmed-booking.php:24
#: templates/emails/admin-payment-confirmed-booking.php:31
#: templates/emails/admin-pending-booking.php:24
#: templates/emails/customer-approved-booking.php:32
#: templates/emails/customer-cancelled-booking.php:30
#: templates/emails/customer-confirmation-booking.php:36
#: templates/emails/customer-pending-booking.php:33
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:31
msgid "Name: %1$s %2$s"
msgstr "Nome: %1$s %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:26
#: templates/emails/admin-customer-confirmed-booking.php:26
#: templates/emails/admin-payment-confirmed-booking.php:33
#: templates/emails/admin-pending-booking.php:26
#: templates/emails/customer-approved-booking.php:34
#: templates/emails/customer-cancelled-booking.php:32
#: templates/emails/customer-confirmation-booking.php:38
#: templates/emails/customer-pending-booking.php:35
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:33
msgid "Email: %s"
msgstr "E-mail: %s"

#: templates/emails/admin-customer-cancelled-booking.php:28
#: templates/emails/admin-customer-confirmed-booking.php:28
#: templates/emails/admin-payment-confirmed-booking.php:35
#: templates/emails/admin-pending-booking.php:28
#: templates/emails/customer-approved-booking.php:36
#: templates/emails/customer-cancelled-booking.php:34
#: templates/emails/customer-confirmation-booking.php:40
#: templates/emails/customer-pending-booking.php:37
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:35
msgid "Phone: %s"
msgstr "Telefone: %s"

#: templates/emails/admin-customer-cancelled-booking.php:30
#: templates/emails/admin-customer-confirmed-booking.php:30
#: templates/emails/admin-payment-confirmed-booking.php:37
#: templates/emails/admin-pending-booking.php:30
#: templates/emails/customer-approved-booking.php:38
#: templates/emails/customer-cancelled-booking.php:36
#: templates/emails/customer-confirmation-booking.php:42
#: templates/emails/customer-pending-booking.php:39
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:37
msgid "Note: %s"
msgstr ""

#: templates/emails/admin-customer-confirmed-booking.php:15
msgid "Booking #%s is confirmed by customer."
msgstr "A reserva #%s foi confirmada pelo cliente."

#: templates/emails/admin-payment-confirmed-booking.php:15
msgid "Booking #%s is confirmed by payment."
msgstr "A reserva %s foi confirmada pelo pagamento."

#: templates/emails/admin-payment-confirmed-booking.php:17
msgid "Details of payment"
msgstr "Detalhes do pagamento"

#: templates/emails/admin-payment-confirmed-booking.php:18
msgid "Payment ID: #%s"
msgstr "ID de pagamento: #%s"

#: templates/emails/admin-payment-confirmed-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:16
msgid "Amount: %s"
msgstr "Valor: %s"

#: templates/emails/admin-payment-confirmed-booking.php:22
msgid "Method: %s"
msgstr "Método: %s"

#: templates/emails/admin-pending-booking.php:15
msgid "Booking #%s is pending for Administrator approval."
msgstr "A reserva #%s está pendente e aguardando a aprovação do Administrador."

#: templates/emails/cancellation-details.php:14
msgid "Click the link below to cancel your booking."
msgstr "Clique no link abaixo para cancelar sua reserva."

#: templates/emails/cancellation-details.php:16
msgid "Cancel your booking"
msgstr "Cancelar sua reserva"

#: templates/emails/customer-approved-booking.php:15
msgid "Dear %1$s %2$s, your reservation is approved!"
msgstr "Caro %1$s %2$s, sua reserva foi aprovada!"

#: templates/emails/customer-approved-booking.php:18
#: templates/emails/customer-cancelled-booking.php:19
#: templates/emails/customer-confirmation-booking.php:25
#: templates/emails/customer-pending-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:20
msgid "ID: #%s"
msgstr ""

#: templates/emails/customer-approved-booking.php:41
#: templates/emails/customer-cancelled-booking.php:38
#: templates/emails/customer-confirmation-booking.php:44
#: templates/emails/customer-pending-booking.php:41
#: templates/emails/customer-registration.php:26
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:39
msgid "Thank you!"
msgstr "Obrigado!"

#: templates/emails/customer-cancelled-booking.php:15
msgid "Dear %1$s %2$s, your reservation is cancelled!"
msgstr "Caro %1$s %2$s, a sua reserva foi cancelada!"

#: templates/emails/customer-confirmation-booking.php:14
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:12
msgid "Dear %1$s %2$s, we received your request for reservation."
msgstr "Prezado %1$s %2$s, recebemos seu pedido de reserva."

#: templates/emails/customer-confirmation-booking.php:16
msgid "Click the link below to confirm your booking."
msgstr "Clique no link abaixo para confirmar sua reserva."

#: templates/emails/customer-confirmation-booking.php:18
msgid "Confirm"
msgstr "Confirmar"

#: templates/emails/customer-confirmation-booking.php:20
msgid "Note: link expires on"
msgstr "Note: este link expira em"

#: templates/emails/customer-confirmation-booking.php:20
msgid "UTC"
msgstr ""

#: templates/emails/customer-confirmation-booking.php:22
msgid "If you did not place this booking, please ignore this email."
msgstr "Se você não fez esta reserva, por favor, ignore este e-mail."

#: templates/emails/customer-pending-booking.php:15
msgid "Dear %1$s %2$s, your reservation is pending."
msgstr "Caro %1$s %2$s, a sua reserva está pendente."

#: templates/emails/customer-pending-booking.php:17
msgid "We will notify you by email once it is confirmed by our staff."
msgstr "Nós iremos notificá-lo por e-mail assim que for confirmado pela nossa equipe."

#: templates/emails/customer-registration.php:15
msgid "Hi %1$s %2$s,"
msgstr ""

#: templates/emails/customer-registration.php:17
msgid "Thanks for creating an account on %1$s."
msgstr ""

#: templates/emails/customer-registration.php:19
msgid "You Account Details"
msgstr ""

#: templates/emails/customer-registration.php:20
msgid "Login: %s"
msgstr ""

#: templates/emails/customer-registration.php:21
msgid "Password: %s"
msgstr ""

#: templates/emails/customer-registration.php:22
msgid "Log in here: %s"
msgstr ""

#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:14
msgid "To confirm your booking, please follow the instructions below for payment."
msgstr ""

#: templates/emails/reserved-room-details.php:14
msgid "Accommodation #%s"
msgstr "Acomodação #%s"

#: templates/emails/reserved-room-details.php:21
msgid "Accommodation: <a href=\"%1$s\">%2$s</a>"
msgstr "Acomodação: <a href=\"%1$s\">%2$s</a>"

#: templates/emails/reserved-room-details.php:24
msgid "Accommodation Rate: %s"
msgstr "Taxa de acomodação: %s"

#: templates/emails/reserved-room-details.php:28
msgid "Bed Type: %s"
msgstr "Tipo de cama: %s"

#: templates/required-fields-tip.php:8
msgid "Required fields are followed by"
msgstr "Os campos obrigatórios são"

#: templates/shortcodes/booking-cancellation/already-cancelled.php:7
msgid "Booking is already canceled."
msgstr ""

#: templates/shortcodes/booking-cancellation/booking-cancellation-button.php:15
msgid "Cancel Booking"
msgstr "Cancelar reserva"

#: templates/shortcodes/booking-cancellation/invalid-request.php:7
#: templates/shortcodes/booking-confirmation/invalid-request.php:7
msgid "Invalid request."
msgstr "Pedido inválido."

#: templates/shortcodes/booking-cancellation/not-possible.php:7
msgid "Cancelation of your booking is not possible for some reason. Please contact the website administrator."
msgstr ""

#: templates/shortcodes/booking-confirmation/already-confirmed.php:7
msgid "Booking is already confirmed."
msgstr "A sua reserva já foi confirmada."

#: templates/shortcodes/booking-confirmation/confirmed.php:7
msgid "Your booking is confirmed. Thank You!"
msgstr "Sua reserva foi confirmada. Obrigado!"

#: templates/shortcodes/booking-confirmation/expired.php:7
msgid "Your booking request is expired. Please start a new booking request."
msgstr "Seu pedido de reserva expirou. Por favor, inicie um novo pedido."

#: templates/shortcodes/booking-confirmation/not-possible.php:7
msgid "Confirmation of your booking request is not possible for some reason. Please start a new booking request."
msgstr "A confirmação da sua reserva não foi efetuada por algum motivo. Por favor, faça um novo pedido de reserva."

#: templates/shortcodes/booking-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received and confirmed."
msgstr ""

#: templates/shortcodes/booking-details/booking-details.php:21
msgid "Booking:"
msgstr "Reserva:"

#: templates/shortcodes/booking-details/booking-details.php:47
msgid "Details:"
msgstr "Detalhes:"

#: templates/shortcodes/payment-confirmation/completed.php:11
msgid "Thank you for your payment. Your transaction has been completed."
msgstr "Agradecemos o pagamento. A sua transacção está completa."

#: templates/shortcodes/payment-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received."
msgstr ""

#: templates/shortcodes/room-rates/rate-content.php:17
msgid "from %s"
msgstr "de %s"

#: templates/shortcodes/rooms/not-found.php:7
msgid "No accommodations matching criteria."
msgstr "Nenhuma acomodação corresponde aos critérios."

#: templates/shortcodes/services/not-found.php:7
msgid "No services matched criteria."
msgstr "Nenhum serviço foi encontrado."

#: templates/widgets/rooms/not-found.php:6
msgid "Nothing found."
msgstr "Nada encontrado."

#: templates/widgets/search-availability/search-form.php:105
msgid "Children %s:"
msgstr ""

#: assets/blocks/blocks.js:178
#: assets/blocks/blocks.js:190
msgid "Preset date. Formatted as %s"
msgstr "Data predefinida. Formatada como %s"

#: assets/blocks/blocks.js:283
#: assets/blocks/blocks.js:1425
#: assets/blocks/blocks.js:1507
msgid "Select an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:284
#: assets/blocks/blocks.js:1426
#: assets/blocks/blocks.js:1508
msgid "ID of an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:460
#: assets/blocks/blocks.js:710
#: assets/blocks/blocks.js:1239
msgid "Gallery"
msgstr ""

#: assets/blocks/blocks.js:508
#: assets/blocks/blocks.js:758
#: assets/blocks/blocks.js:1287
msgid "View Button"
msgstr ""

#: assets/blocks/blocks.js:522
#: assets/blocks/blocks.js:558
#: assets/blocks/blocks.js:858
#: assets/blocks/blocks.js:894
#: assets/blocks/blocks.js:1042
#: assets/blocks/blocks.js:1078
msgid "Order"
msgstr ""

#: assets/blocks/blocks.js:530
#: assets/blocks/blocks.js:866
#: assets/blocks/blocks.js:1050
msgid "Order By"
msgstr ""

#: assets/blocks/blocks.js:533
#: assets/blocks/blocks.js:869
#: assets/blocks/blocks.js:1053
msgid "No order"
msgstr ""

#: assets/blocks/blocks.js:534
#: assets/blocks/blocks.js:870
#: assets/blocks/blocks.js:1054
msgid "Post ID"
msgstr ""

#: assets/blocks/blocks.js:535
#: assets/blocks/blocks.js:871
#: assets/blocks/blocks.js:1055
msgid "Post author"
msgstr ""

#: assets/blocks/blocks.js:536
#: assets/blocks/blocks.js:872
#: assets/blocks/blocks.js:1056
msgid "Post title"
msgstr "Título da publicação"

#: assets/blocks/blocks.js:537
#: assets/blocks/blocks.js:873
#: assets/blocks/blocks.js:1057
msgid "Post name (post slug)"
msgstr ""

#: assets/blocks/blocks.js:538
#: assets/blocks/blocks.js:874
#: assets/blocks/blocks.js:1058
msgid "Post date"
msgstr ""

#: assets/blocks/blocks.js:539
#: assets/blocks/blocks.js:875
#: assets/blocks/blocks.js:1059
msgid "Last modified date"
msgstr ""

#: assets/blocks/blocks.js:540
#: assets/blocks/blocks.js:876
#: assets/blocks/blocks.js:1060
msgid "Parent ID"
msgstr ""

#: assets/blocks/blocks.js:541
#: assets/blocks/blocks.js:877
#: assets/blocks/blocks.js:1061
msgid "Random order"
msgstr ""

#: assets/blocks/blocks.js:542
#: assets/blocks/blocks.js:878
#: assets/blocks/blocks.js:1062
msgid "Number of comments"
msgstr ""

#: assets/blocks/blocks.js:543
#: assets/blocks/blocks.js:879
#: assets/blocks/blocks.js:1063
msgid "Relevance"
msgstr ""

#: assets/blocks/blocks.js:544
#: assets/blocks/blocks.js:880
#: assets/blocks/blocks.js:1064
msgid "Page order"
msgstr "Ordem das páginas"

#: assets/blocks/blocks.js:545
#: assets/blocks/blocks.js:881
#: assets/blocks/blocks.js:1065
msgid "Meta value"
msgstr ""

#: assets/blocks/blocks.js:546
#: assets/blocks/blocks.js:882
#: assets/blocks/blocks.js:1066
msgid "Numeric meta value"
msgstr ""

#: assets/blocks/blocks.js:561
#: assets/blocks/blocks.js:897
#: assets/blocks/blocks.js:1081
msgid "Ascending (1, 2, 3)"
msgstr ""

#: assets/blocks/blocks.js:562
#: assets/blocks/blocks.js:898
#: assets/blocks/blocks.js:1082
msgid "Descending (3, 2, 1)"
msgstr ""

#: assets/blocks/blocks.js:573
#: assets/blocks/blocks.js:909
#: assets/blocks/blocks.js:1093
msgid "Meta Name"
msgstr ""

#: assets/blocks/blocks.js:585
#: assets/blocks/blocks.js:921
#: assets/blocks/blocks.js:1105
msgid "Meta Type"
msgstr ""

#: assets/blocks/blocks.js:586
#: assets/blocks/blocks.js:922
#: assets/blocks/blocks.js:1106
msgid "Specified type of the custom field. Can be used in conjunction with \"orderby\" = \"meta_value\"."
msgstr ""

#: assets/blocks/blocks.js:589
#: assets/blocks/blocks.js:925
#: assets/blocks/blocks.js:1109
msgid "Any"
msgstr ""

#: assets/blocks/blocks.js:590
#: assets/blocks/blocks.js:926
#: assets/blocks/blocks.js:1110
msgid "Numeric"
msgstr ""

#: assets/blocks/blocks.js:591
#: assets/blocks/blocks.js:927
#: assets/blocks/blocks.js:1111
msgid "Binary"
msgstr ""

#: assets/blocks/blocks.js:592
#: assets/blocks/blocks.js:928
#: assets/blocks/blocks.js:1112
msgid "String"
msgstr ""

#: assets/blocks/blocks.js:594
#: assets/blocks/blocks.js:930
#: assets/blocks/blocks.js:1114
msgid "Time"
msgstr "Hora"

#: assets/blocks/blocks.js:595
#: assets/blocks/blocks.js:931
#: assets/blocks/blocks.js:1115
msgid "Date and time"
msgstr ""

#: assets/blocks/blocks.js:596
#: assets/blocks/blocks.js:932
#: assets/blocks/blocks.js:1116
msgid "Decimal number"
msgstr ""

#: assets/blocks/blocks.js:597
#: assets/blocks/blocks.js:933
#: assets/blocks/blocks.js:1117
msgid "Signed number"
msgstr ""

#: assets/blocks/blocks.js:598
#: assets/blocks/blocks.js:934
#: assets/blocks/blocks.js:1118
msgid "Unsigned number"
msgstr ""

#: assets/blocks/blocks.js:784
#: assets/blocks/blocks.js:1009
msgid "Query Settings"
msgstr ""

#: assets/blocks/blocks.js:840
msgid "Relation"
msgstr ""

#: assets/blocks/blocks.js:1029
msgid "Values: integer, -1 to display all, default: \"Blog pages show at most\""
msgstr ""

#: assets/blocks/blocks.js:1203
msgid "Select an accommodation type."
msgstr ""

