# Copyright (C) 2025 MotoPress
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: hotel-booking-plugin\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/motopress-hotel-booking\n"
"Last-Translator: \n"
"Language-Team: Spanish\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-19T19:58:50+00:00\n"
"PO-Revision-Date: 2025-03-05 20:50\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: motopress-hotel-booking\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: hotel-booking-plugin\n"
"X-Crowdin-Project-ID: 463550\n"
"X-Crowdin-Language: es-ES\n"
"X-Crowdin-File: motopress-hotel-booking.pot\n"
"X-Crowdin-File-ID: 44\n"
"Language: es_ES\n"

#. Plugin Name of the plugin
#. translators: Name of the plugin, do not translate
#: motopress-hotel-booking.php
#: includes/script-managers/block-script-manager.php:27
msgid "Hotel Booking"
msgstr "Hotel Booking"

#. Plugin URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/products/hotel-booking/"
msgstr "https://motopress.com/products/hotel-book/"

#. Description of the plugin
#: motopress-hotel-booking.php
msgid "Manage your hotel booking services. Perfect for hotels, villas, guest houses, hostels, and apartments of all sizes."
msgstr "Administre sus servicios de reserva de hotel."

#. Author of the plugin
#: motopress-hotel-booking.php
msgid "MotoPress"
msgstr "MotoPress"

#. Author URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/"
msgstr "https://motopress.com/"

#: functions.php:71
msgctxt "Post Status"
msgid "New"
msgstr "Nueva"

#: functions.php:74
msgctxt "Post Status"
msgid "Auto Draft"
msgstr "Borrador"

#. translators: %s: URL to plugins.php page
#: functions.php:518
msgid "You are using two instances of Hotel Booking plugin at the same time, please <a href=\"%s\">deactivate one of them</a>."
msgstr "Está usando dos instancias del plugin Hotel Booking al mismo tiempo, porfavor <a href=\"%s\">desactive una de ellas</a>"

#: functions.php:535
msgid "<a href=\"%s\">Upgrade to Premium</a> to enable this feature."
msgstr "<a href=\"%s\">Actualiza a Premium</a> para activar esta función."

#: includes/actions-handler.php:100
#: includes/admin/sync-logs-list-table.php:91
#: includes/csv/csv-export-handler.php:33
#: includes/csv/csv-export-handler.php:51
#: includes/payments/gateways/stripe-gateway.php:560
#: includes/payments/gateways/stripe-gateway.php:572
#: includes/payments/gateways/stripe-gateway.php:631
msgid "Error"
msgstr "Error"

#: includes/admin/customers-list-table.php:143
#: includes/admin/menu-pages/rooms-generator-menu-page.php:84
#: includes/admin/sync-rooms-list-table.php:146
#: includes/post-types/room-type-cpt.php:354
#: templates/account/bookings.php:80
msgid "View"
msgstr "Ver"

#: includes/admin/customers-list-table.php:147
#: includes/admin/fields/abstract-complex-field.php:25
#: includes/admin/fields/rules-list-field.php:61
#: includes/admin/sync-rooms-list-table.php:147
msgid "Delete"
msgstr "Eliminar"

#: includes/admin/customers-list-table.php:212
#: includes/post-types/attributes-cpt.php:301
msgid "Name"
msgstr "Nombre"

#: includes/admin/customers-list-table.php:213
#: includes/admin/menu-pages/customers-menu-page.php:207
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:122
#: includes/bundles/customer-bundle.php:110
#: includes/csv/bookings/bookings-exporter-helper.php:83
#: includes/post-types/booking-cpt.php:106
#: includes/post-types/payment-cpt.php:263
#: includes/views/shortcodes/checkout-view.php:618
#: templates/account/account-details.php:34
msgid "Email"
msgstr "Email"

#: includes/admin/customers-list-table.php:214
#: includes/admin/menus.php:72
#: includes/admin/menus.php:73
#: includes/post-types/booking-cpt.php:241
#: includes/shortcodes/account-shortcode.php:239
msgid "Bookings"
msgstr "Reservas"

#: includes/admin/customers-list-table.php:215
msgid "Date Registered"
msgstr "Fecha de registro"

#: includes/admin/customers-list-table.php:216
msgid "Last Active"
msgstr "Última actividad"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:16
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:33
msgid "Terms"
msgstr "Términos"

#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:27
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:46
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:41
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:115
msgid "Created on:"
msgstr "Creado:"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:88
msgid "Please add attribute in default language to configure terms."
msgstr "Por favor, añade un atributo en el idioma predeterminado para configurar los términos."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:98
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:116
msgid "Configure terms"
msgstr "Configurar términos"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:20
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:107
#: includes/post-types/reserved-room-cpt.php:22
msgid "Reserved Accommodations"
msgstr "Alojamientos reservados"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:21
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:66
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Update Booking"
msgstr "Actualizar reserva"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:22
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:12
msgid "Logs"
msgstr "Registros"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:54
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:125
msgid "Delete Permanently"
msgstr "Borrar permanentemente"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:58
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:127
msgid "Move to Trash"
msgstr "Mover a la papelera"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Create Booking"
msgstr "Crear reserva"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:85
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:98
msgid "Resend Email"
msgstr "Reenviar Email"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:100
msgid "Send a copy of the Approved Booking email to the customer`s email address."
msgstr "Envíe una copia del correo electrónico de reserva aprobada a la dirección de correo electrónico del cliente."

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:116
#: templates/edit-booking/edit-reserved-rooms.php:35
msgid "Edit Accommodations"
msgstr "Editar alojamientos"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:125
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:85
#: includes/shortcodes/booking-confirmation-shortcode.php:298
msgid "Date:"
msgstr "Fecha:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:130
msgid "Author:"
msgstr "Autor:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:145
#: includes/payments/gateways/stripe-gateway.php:528
msgid "Auto"
msgstr "Automático"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:155
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:89
msgid "Message:"
msgstr "Mensaje:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:227
msgid "Confirmation email has been sent to customer."
msgstr "Se ha enviado un correo electrónico de confirmación al cliente."

#: includes/admin/edit-cpt-pages/coupon-edit-cpt-page.php:14
msgid "Coupon code"
msgstr "Codigo de cupón"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:11
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:64
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Update Payment"
msgstr "Actualizar pago"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:44
msgid "Modified on:"
msgstr "Cambiado:"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Create Payment"
msgstr "Crear pago"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:47
msgid "Season Prices"
msgstr "Precios de temporada"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:48
msgid "<code>Please select Accommodation Type and click Create Rate button to continue.</code>"
msgstr "<code>Porfavor seleccione tipo de alojamiento y haga click en el botón de crear tarifa para continuar.</code>"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:66
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:65
#: includes/views/loop-room-type-view.php:113
#: includes/views/single-room-type-view.php:205
#: template-functions.php:920
#: templates/create-booking/results/reserve-rooms.php:51
#: templates/widgets/rooms/room-content.php:77
#: templates/widgets/search-availability/search-form.php:78
msgid "Adults:"
msgstr "Adultos:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:66
#: includes/views/loop-room-type-view.php:128
#: includes/views/single-room-type-view.php:220
#: template-functions.php:925
#: templates/create-booking/results/reserve-rooms.php:52
#: templates/widgets/rooms/room-content.php:91
#: templates/widgets/search-availability/search-form.php:103
msgid "Children:"
msgstr "Niños:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:70
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:63
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:110
#: includes/shortcodes/booking-confirmation-shortcode.php:306
#: includes/shortcodes/search-results-shortcode.php:770
#: includes/shortcodes/search-results-shortcode.php:921
#: templates/shortcodes/booking-details/booking-details.php:33
msgid "Total:"
msgstr "Total:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:80
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Update Rate"
msgstr "Actualizar tarifa"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:97
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
msgid "Active"
msgstr "Activada"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:98
#: includes/admin/groups/license-settings-group.php:61
msgid "Disabled"
msgstr "Desactivada"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Create Rate"
msgstr "Crear tarifa"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:190
msgid "Duplicate Rate"
msgstr "Duplicar tarifa"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:12
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:36
#: includes/admin/menu-pages/booking-rules-menu-page.php:219
#: includes/admin/menu-pages/booking-rules-menu-page.php:264
#: includes/admin/menu-pages/booking-rules-menu-page.php:310
#: includes/admin/menu-pages/booking-rules-menu-page.php:356
#: includes/admin/menu-pages/booking-rules-menu-page.php:487
#: includes/admin/menu-pages/booking-rules-menu-page.php:533
#: includes/admin/menu-pages/booking-rules-menu-page.php:579
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:208
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:304
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:377
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:448
#: includes/post-types/room-cpt.php:31
#: includes/post-types/room-cpt.php:41
#: includes/wizard.php:103
msgid "Accommodations"
msgstr "Comodidades"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:15
#: includes/post-types/attributes-cpt.php:54
#: includes/post-types/attributes-cpt.php:61
#: includes/post-types/attributes-cpt.php:65
msgid "Attributes"
msgstr "Atributos"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:18
msgid "Accommodation Reviews"
msgstr "Reseñas de alojamientos"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:27
msgid "Allow guests to <a href=\"%s\" target=\"_blank\">submit star ratings and reviews</a> evaluating your accommodations."
msgstr "Permita a los huéspedes <a href=\"%s\" target=\"_blank\">enviar calificaciones con estrellas y reseñas</a> al evaluar sus alojamientos."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:69
msgid "Number of Accommodations:"
msgstr "Número de alojamientos:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:74
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:29
msgid "Count of real accommodations of this type in your hotel."
msgstr "Cantidad de alojamientos reales de este tipo en su hotel."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:122
msgid "Total Accommodations:"
msgstr "Todos los alojamientos:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:159
msgid "Show Accommodations"
msgstr "Mostrar alojamientos"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:139
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:18
#: includes/admin/menu-pages/rooms-generator-menu-page.php:146
#: includes/admin/menu-pages/rooms-generator-menu-page.php:150
msgid "Generate Accommodations"
msgstr "Generar alojamientos"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:147
msgid "Active Accommodations:"
msgstr "Alojamientos activos:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:168
#: includes/post-types/room-cpt.php:93
msgid "Linked Accommodations"
msgstr ""

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:173
msgid "Link accommodations on the Edit Accommodation page to ensure bookings for one make any linked properties unavailable for the same dates."
msgstr ""

#: includes/admin/fields/abstract-complex-field.php:24
#: includes/admin/fields/rules-list-field.php:57
#: templates/edit-booking/add-room-popup.php:45
msgid "Add"
msgstr "Añadir"

#: includes/admin/fields/amount-field.php:74
msgid "Per adult:"
msgstr "Por adulto:"

#: includes/admin/fields/amount-field.php:77
msgid "Per child:"
msgstr "Por niño:"

#: includes/admin/fields/amount-field.php:198
msgid "Per adult: "
msgstr "Por adulto:"

#: includes/admin/fields/amount-field.php:200
msgid "Per child: "
msgstr "Por niño:"

#: includes/admin/fields/complex-horizontal-field.php:71
#: includes/admin/fields/rules-list-field.php:62
#: templates/account/bookings.php:22
#: templates/account/bookings.php:79
#: templates/edit-booking/edit-reserved-rooms.php:47
msgid "Actions"
msgstr "Acciones"

#: includes/admin/fields/complex-horizontal-field.php:111
msgid "Move up"
msgstr "Mover arriba"

#: includes/admin/fields/complex-horizontal-field.php:112
msgid "Move down"
msgstr "Mover abajo"

#: includes/admin/fields/complex-horizontal-field.php:113
msgid "Move to top"
msgstr "Mover al principio"

#: includes/admin/fields/complex-horizontal-field.php:114
msgid "Move to bottom"
msgstr "Mover al final"

#: includes/admin/fields/complex-vertical-field.php:17
#: includes/admin/menu-pages/settings-menu-page.php:580
#: includes/settings/main-settings.php:25
#: includes/settings/main-settings.php:43
msgid "Default"
msgstr "Predeterminado"

#: includes/admin/fields/dynamic-select-field.php:61
#: includes/admin/fields/page-select-field.php:16
#: includes/admin/menu-pages/customers-menu-page.php:260
#: includes/admin/menu-pages/rooms-generator-menu-page.php:38
#: includes/admin/menu-pages/settings-menu-page.php:420
#: includes/post-types/booking-cpt.php:122
#: includes/post-types/booking-cpt.php:179
#: includes/post-types/payment-cpt.php:231
#: includes/post-types/rate-cpt.php:31
#: includes/post-types/room-cpt.php:79
#: includes/views/shortcodes/checkout-view.php:250
#: includes/views/shortcodes/checkout-view.php:273
#: templates/account/account-details.php:51
#: templates/edit-booking/add-room-popup.php:30
#: templates/edit-booking/add-room-popup.php:38
msgid "— Select —"
msgstr "— Elegir —"

#: includes/admin/fields/install-plugin-field.php:33
msgid "Install & Activate"
msgstr "Instalar y Activar"

#: includes/admin/fields/media-field.php:76
msgid "Add image"
msgstr "Añadir imagen"

#: includes/admin/fields/media-field.php:76
msgid "Add gallery"
msgstr "Añadir galería"

#: includes/admin/fields/media-field.php:77
msgid "Remove image"
msgstr "Quitar la imagen"

#: includes/admin/fields/media-field.php:77
msgid "Remove gallery"
msgstr "Eliminar galería"

#: includes/admin/fields/multiple-checkbox-field.php:88
#: template-functions.php:1088
msgid "Select all"
msgstr "Seleccionar todas"

#: includes/admin/fields/multiple-checkbox-field.php:92
#: template-functions.php:1090
msgid "Unselect all"
msgstr "Deseleccionar todas"

#: includes/admin/fields/notes-list-field.php:23
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:68
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:33
#: includes/csv/bookings/bookings-exporter-helper.php:112
#: assets/blocks/blocks.js:593
#: assets/blocks/blocks.js:929
#: assets/blocks/blocks.js:1113
msgid "Date"
msgstr "Fecha"

#: includes/admin/fields/notes-list-field.php:33
msgid "Author"
msgstr "Autor"

#: includes/admin/fields/rules-list-field.php:59
#: includes/admin/room-list-table.php:154
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:115
#: includes/bookings-calendar.php:613
#: includes/script-managers/admin-script-manager.php:97
msgid "Edit"
msgstr "Editar"

#: includes/admin/fields/rules-list-field.php:60
#: includes/admin/sync-rooms-list-table.php:81
#: includes/ajax.php:951
#: includes/script-managers/admin-script-manager.php:98
msgid "Done"
msgstr "Hecho"

#: includes/admin/fields/rules-list-field.php:64
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:85
#: includes/admin/menu-pages/booking-rules-menu-page.php:180
#: includes/admin/menu-pages/booking-rules-menu-page.php:183
#: includes/admin/menu-pages/booking-rules-menu-page.php:407
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:211
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:307
#: includes/script-managers/admin-script-manager.php:95
msgid "All"
msgstr "Todo"

#: includes/admin/fields/rules-list-field.php:65
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:83
#: includes/post-types/coupon-cpt.php:81
#: includes/post-types/coupon-cpt.php:113
#: includes/post-types/coupon-cpt.php:158
#: includes/script-managers/admin-script-manager.php:96
msgid "None"
msgstr "Nada"

#: includes/admin/fields/time-picker-field.php:13
msgid "HH:MM"
msgstr "HH:MM"

#: includes/admin/fields/total-price-field.php:18
msgid "Recalculate Total Price"
msgstr "Volver a calcular el precio total"

#: includes/admin/fields/variable-pricing-field.php:89
#: includes/views/booking-view.php:121
msgid "Nights"
msgstr "Noches"

#: includes/admin/fields/variable-pricing-field.php:97
#: includes/script-managers/admin-script-manager.php:102
msgid "and more"
msgstr "y más"

#: includes/admin/fields/variable-pricing-field.php:98
#: includes/admin/menu-pages/edit-booking/edit-control.php:95
#: includes/script-managers/admin-script-manager.php:101
#: includes/shortcodes/search-results-shortcode.php:1009
#: includes/views/booking-view.php:400
#: templates/edit-booking/edit-reserved-rooms.php:70
msgid "Remove"
msgstr "Borrar"

#: includes/admin/fields/variable-pricing-field.php:104
msgid "Add length of stay"
msgstr "Añadir duración de la estancia"

#: includes/admin/fields/variable-pricing-field.php:109
msgid "Base Occupancy"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:110
#: includes/admin/fields/variable-pricing-field.php:170
msgid "Price per night"
msgstr "Precio por noche"

#: includes/admin/fields/variable-pricing-field.php:118
#: includes/admin/fields/variable-pricing-field.php:167
#: includes/emails/templaters/reserved-rooms-templater.php:175
#: includes/post-types/room-type-cpt.php:283
#: includes/views/booking-view.php:105
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:242
#: template-functions.php:765
#: templates/create-booking/search/search-form.php:94
#: templates/shortcodes/search/search-form.php:80
#: assets/blocks/blocks.js:147
msgid "Adults"
msgstr "Adultos"

#: includes/admin/fields/variable-pricing-field.php:122
#: includes/csv/bookings/bookings-exporter-helper.php:80
#: includes/emails/templaters/reserved-rooms-templater.php:179
#: includes/post-types/room-type-cpt.php:292
#: includes/views/booking-view.php:116
#: templates/create-booking/search/search-form.php:109
#: templates/shortcodes/search/search-form.php:103
#: assets/blocks/blocks.js:162
msgid "Children"
msgstr "Niños"

#: includes/admin/fields/variable-pricing-field.php:130
msgid "Price per extra adult"
msgstr "Precio por adulto extra"

#: includes/admin/fields/variable-pricing-field.php:137
msgid "Price per extra child"
msgstr "Precio por hijo extra"

#: includes/admin/fields/variable-pricing-field.php:154
msgid "Enable variable pricing"
msgstr "Habilitar precios variables"

#: includes/admin/fields/variable-pricing-field.php:188
msgid "Add Variation"
msgstr "Añadir variación"

#: includes/admin/fields/variable-pricing-field.php:215
#: includes/admin/fields/variable-pricing-field.php:231
msgid "Remove variation"
msgstr "Eliminar la variación"

#: includes/admin/groups/license-settings-group.php:21
msgid "The License Key is required in order to get automatic plugin updates and support. You can manage your License Key in your personal account. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Learn more</a>."
msgstr "Se requiere la clave de licencia para conseguir actualizaciones automáticas del plugin y soporte. Usted puede administrar su clave de licencia en su cuenta personal.  <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Más info</a>."

#: includes/admin/groups/license-settings-group.php:28
msgid "License Key"
msgstr "Clave de licencia"

#: includes/admin/groups/license-settings-group.php:42
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:62
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:22
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:28
#: includes/admin/menu-pages/create-booking/checkout-step.php:63
#: includes/admin/sync-logs-list-table.php:72
#: includes/admin/sync-rooms-list-table.php:127
#: includes/csv/bookings/bookings-exporter-helper.php:72
#: template-functions.php:977
#: templates/edit-booking/edit-reserved-rooms.php:46
msgid "Status"
msgstr "Estado"

#: includes/admin/groups/license-settings-group.php:49
msgid "Inactive"
msgstr "Inactiva"

#: includes/admin/groups/license-settings-group.php:55
msgid "Valid until"
msgstr "Válida hasta"

#: includes/admin/groups/license-settings-group.php:57
msgid "Valid (Lifetime)"
msgstr "Válida (de por vida)"

#: includes/admin/groups/license-settings-group.php:64
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:123
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:85
msgid "Expired"
msgstr "Expirada"

#: includes/admin/groups/license-settings-group.php:67
msgid "Invalid"
msgstr "No válida"

#: includes/admin/groups/license-settings-group.php:71
msgid "Your License Key does not match the installed plugin. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>How to fix this.</a>"
msgstr "Su clave de licencia no coincide con el complemento instalado. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>Cómo arreglar esto.</a>"

#: includes/admin/groups/license-settings-group.php:74
msgid "Product ID is not valid"
msgstr "ID de producto no válido"

#: includes/admin/groups/license-settings-group.php:83
msgid "Action"
msgstr "Acción"

#: includes/admin/groups/license-settings-group.php:90
msgid "Activate License"
msgstr "Activar licencia"

#: includes/admin/groups/license-settings-group.php:96
msgid "Deactivate License"
msgstr "Desactivar licencia"

#: includes/admin/groups/license-settings-group.php:103
msgid "Renew License"
msgstr "Renovar licencia"

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:9
msgid "Attributes let you define extra accommodation data, such as location or type. You can use these attributes in the search availability form as advanced search filters."
msgstr "Los atributos le permiten definir datos de alojamiento adicionales, como la ubicación o el tipo. Puede utilizar estos atributos en el formulario de disponibilidad de búsqueda como filtros de búsqueda avanzada."

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:72
msgid "This attribute refers to non-unique taxonomy - %1$s - which was already registered with attribute %2$s."
msgstr "Este atributo se refiere a taxonomía no única: %1$s, que ya estaba registrada con el atributo %2$s."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:92
msgid "You cannot manage terms of trashed attributes."
msgstr "No puede administrar los términos de los atributos eliminados."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:17
#: includes/admin/menu-pages/calendar-menu-page.php:31
#: includes/post-types/booking-cpt.php:246
msgid "New Booking"
msgstr "Nueva reserva"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:61
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:26
#: includes/admin/menu-pages/shortcodes-menu-page.php:376
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:64
#: includes/csv/bookings/bookings-exporter-helper.php:71
#: includes/post-types/booking-cpt.php:42
#: includes/post-types/payment-cpt.php:150
msgid "ID"
msgstr "Identidad"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:63
msgid "Check-in / Check-out"
msgstr "Diá de llegada / Día de salida"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:64
#: includes/views/booking-view.php:107
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:244
#: template-functions.php:767
#: templates/shortcodes/search/search-form.php:82
msgid "Guests"
msgstr "Invitados"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:65
#: templates/emails/admin-customer-cancelled-booking.php:23
#: templates/emails/admin-customer-confirmed-booking.php:23
#: templates/emails/admin-payment-confirmed-booking.php:30
#: templates/emails/admin-pending-booking.php:23
msgid "Customer Info"
msgstr "Información de cliente"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:66
#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:16
#: includes/post-types/rate-cpt.php:64
#: includes/post-types/service-cpt.php:132
#: includes/post-types/service-cpt.php:137
#: includes/views/single-service-view.php:18
#: includes/widgets/rooms-widget.php:201
#: assets/blocks/blocks.js:496
#: assets/blocks/blocks.js:547
#: assets/blocks/blocks.js:746
#: assets/blocks/blocks.js:883
#: assets/blocks/blocks.js:1067
#: assets/blocks/blocks.js:1275
msgid "Price"
msgstr "Precio"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:67
#: includes/admin/menu-pages/booking-rules-menu-page.php:402
#: includes/admin/room-list-table.php:93
#: includes/admin/sync-rooms-list-table.php:126
#: includes/bookings-calendar.php:829
#: includes/bookings-calendar.php:847
#: includes/csv/bookings/bookings-exporter-helper.php:77
#: includes/post-types/room-cpt.php:32
#: includes/post-types/room-cpt.php:74
#: includes/post-types/room-type-cpt.php:60
#: templates/edit-booking/add-room-popup.php:36
#: templates/edit-booking/edit-reserved-rooms.php:45
msgid "Accommodation"
msgstr "Alojamiento"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:121
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:83
msgid "Expire %s"
msgstr "Expira %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:138
#: includes/script-managers/admin-script-manager.php:99
msgid "Adults: "
msgstr "Adultos: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:142
#: includes/script-managers/admin-script-manager.php:100
msgid "Children: "
msgstr "Niños: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:184
msgid "%s night"
msgid_plural "%s nights"
msgstr[0] "%s noche"
msgstr[1] "%s noches"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:197
#: includes/bookings-calendar.php:1189
msgid "Summary: %s."
msgstr "Resumen: %s."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:280
msgid "Paid: %s"
msgstr "Pagado: %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:345
msgid "Set to %s"
msgstr "Establecer %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:460
msgid "Booking status changed."
msgid_plural "%s booking statuses changed."
msgstr[0] "Se cambió el estado de reserva."
msgstr[1] "Los estados de %s reservas cambiaron."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:506
msgid "All accommodation types"
msgstr "Todos los tipos de alojamientos"

#. translators: The number of imported bookings: "Imported <span>(11)</span>"
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:526
msgid "Imported %s"
msgstr "Importadas: %s"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:19
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:29
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:168
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:264
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:349
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:420
#: includes/post-types/coupon-cpt.php:93
#: includes/post-types/coupon-cpt.php:124
#: includes/post-types/coupon-cpt.php:169
#: includes/post-types/payment-cpt.php:182
#: includes/views/booking-view.php:126
#: includes/views/booking-view.php:172
#: includes/views/booking-view.php:208
#: includes/views/booking-view.php:266
#: includes/views/booking-view.php:297
#: includes/views/booking-view.php:350
#: template-functions.php:978
msgid "Amount"
msgstr "Cantidad"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:20
msgid "Uses"
msgstr "Usos"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:21
#: includes/post-types/coupon-cpt.php:187
msgid "Expiration Date"
msgstr "Fecha de Caducidad"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:41
#: includes/views/edit-booking/checkout-view.php:115
#: template-functions.php:900
msgid "Accommodation:"
msgstr "Alojamiento:"

#. translators: %s is a coupon amount per day
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:60
msgid "%s per day"
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:67
msgid "Service:"
msgstr "Servicio:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:88
msgid "Fee:"
msgstr "Pago:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:153
msgid "Note: the use of coupons is disabled in settings."
msgstr "Nota: el uso de cupones está desactivado en la configuración."

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:165
#: includes/admin/menu-pages/settings-menu-page.php:299
msgid "Enable the use of coupons."
msgstr "Habilitar el uso de cupones"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:27
#: includes/admin/menu-pages/customers-menu-page.php:299
msgid "Customer"
msgstr "Cliente"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:30
#: includes/post-types/booking-cpt.php:242
#: templates/account/bookings.php:18
#: templates/account/bookings.php:66
msgid "Booking"
msgstr "Reserva"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:31
#: includes/post-types/payment-cpt.php:159
msgid "Gateway"
msgstr "Pasarela"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:32
#: includes/post-types/payment-cpt.php:223
msgid "Transaction ID"
msgstr "ID de transacción"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:105
#: includes/admin/menu-pages/create-booking/booking-step.php:66
#: includes/bookings-calendar.php:606
#: includes/script-managers/admin-script-manager.php:103
msgid "Booking #%s"
msgstr "Reserva #%s"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:13
msgid "Rates are used to offer different prices of the same accommodation type depending on extra conditions, e.g. With Breakfast, With No Breakfast, Refundable etc. Guests will choose the preferable rate when submitting a booking request. Create one default rate if you have no price tiers. To add price variations for different periods - open a rate, add a season, and set the price."
msgstr "Nota: el uso de cupones está desactivado en la configuración."

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:23
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:32
#: includes/admin/menu-pages/booking-rules-menu-page.php:393
#: includes/admin/menu-pages/rooms-generator-menu-page.php:34
#: includes/csv/bookings/bookings-exporter-helper.php:75
#: includes/post-types/rate-cpt.php:30
#: includes/post-types/room-cpt.php:84
#: includes/post-types/room-type-cpt.php:54
#: templates/create-booking/search/search-form.php:82
#: templates/edit-booking/add-room-popup.php:28
#: templates/edit-booking/edit-reserved-rooms.php:44
#: assets/blocks/blocks.js:282
#: assets/blocks/blocks.js:1202
#: assets/blocks/blocks.js:1424
#: assets/blocks/blocks.js:1506
msgid "Accommodation Type"
msgstr "Tipo de alojamiento"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:24
msgid "Season &#8212; Price"
msgstr "Precio de temporada &#8212;"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:67
#: includes/post-types/rate-cpt.php:73
msgid "Add New Season Price"
msgstr "Añadir nuevo precio de temporada"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:104
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:94
#: includes/post-types/season-cpt.php:71
msgid "Annually"
msgstr "Anualmente"

#. translators: %s: A date string such as "December 31, 2025".
#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:108
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:98
msgid "Annually until %s"
msgstr "Anualmente hasta %s"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:201
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:281
msgid "Rate was duplicated."
msgstr "La tarifa se duplicó."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:13
msgid "These are real accommodations like rooms, apartments, houses, villas, beds (for hostels) etc."
msgstr "Estos son alojamientos reales como habitaciones, apartamentos, casas, villas, camas (hostales), etc."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:77
#: includes/admin/menu-pages/reports-menu-page.php:129
#: includes/bookings-calendar.php:746
msgid "All Accommodation Types"
msgstr "Todos los alojamientos"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:11
msgid "These are not physical accommodations, but their types. E.g. standard double room. To specify the real number of existing accommodations, you'll need to use Generate Accommodations menu."
msgstr "Estos no son alojamientos físicos, sino sus tipos. P.ej. Habitación Doble Estándar. Para especificar el número real de alojamientos existentes, es necesario utilizar el menú Generar alojamientos."

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:34
#: includes/post-types/room-type-cpt.php:275
#: includes/post-types/room-type-cpt.php:302
#: templates/create-booking/results/reserve-rooms.php:36
msgid "Capacity"
msgstr "Capacidad"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:35
msgid "Bed Type"
msgstr "Tipo de cama"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:68
#: includes/views/loop-room-type-view.php:152
#: includes/views/single-room-type-view.php:244
#: templates/widgets/rooms/room-content.php:167
msgid "Size:"
msgstr "Tamaño:"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:114
msgid "Active:"
msgstr "Activo:"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:13
msgid "Seasons are real periods of time, dates or days that come with different prices for accommodations. E.g. Winter 2018 ($120 per night), Christmas ($150 per night)."
msgstr "Las estaciones son períodos reales de tiempo, fechas o días que tienen diferentes precios de alojamiento. P.ej. Invierno 2018 (120 $ por noche), Navidad (150 $ por noche)."

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:20
msgid "Start"
msgstr "Inicio"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:21
msgid "End"
msgstr "Final"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:22
#: includes/admin/menu-pages/booking-rules-menu-page.php:210
#: includes/admin/menu-pages/booking-rules-menu-page.php:255
msgid "Days"
msgstr "Días"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:23
#: includes/post-types/season-cpt.php:67
msgid "Repeat"
msgstr "Repetir"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:11
msgid "Services are extra offers that you can sell or give for free. E.g. Thai massage, transfer, babysitting. Guests can pre-order them when placing a booking."
msgstr "Los servicios son ofertas adicionales que se puede vender u ofrecer de forma gratuita. P.ej. Masaje tailandés, traslado, servicio de niñera. Los huéspedes pueden solicitar ellos al hacer una reserva."

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:17
#: includes/post-types/service-cpt.php:150
msgid "Periodicity"
msgstr "Periodicidad"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:18
#: includes/post-types/service-cpt.php:206
msgid "Charge"
msgstr "Cobro"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:41
#: includes/entities/service.php:193
#: includes/post-types/service-cpt.php:153
msgid "Per Day"
msgstr "Por Día"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:43
#: includes/post-types/service-cpt.php:154
msgid "Guest Choice"
msgstr "Selección del invitado"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:45
#: includes/entities/service.php:197
#: includes/post-types/service-cpt.php:152
msgid "Once"
msgstr "Una sola vez"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:203
#: includes/post-types/service-cpt.php:209
msgid "Per Guest"
msgstr "Por Huesped"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:205
#: includes/post-types/service-cpt.php:208
msgid "Per Accommodation"
msgstr "Por alojamiento"

#: includes/admin/manage-tax-pages/facility-manage-tax-page.php:11
msgid "These are accommodation amenities, generally free ones. E.g. air-conditioning, wifi."
msgstr "Servicios del alojamiento, normalmente gratuitos. P.ej. Aire acondicionado, wifi."

#: includes/admin/menu-pages/booking-rules-menu-page.php:34
msgid "Booking rules saved."
msgstr "reglas de reservas guardadas"

#: includes/admin/menu-pages/booking-rules-menu-page.php:41
#: includes/admin/menu-pages/booking-rules-menu-page.php:602
#: includes/admin/menu-pages/booking-rules-menu-page.php:606
#: includes/admin/menu-pages/settings-menu-page.php:622
msgid "Booking Rules"
msgstr "Reglas de reserva"

#: includes/admin/menu-pages/booking-rules-menu-page.php:90
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:70
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:118
#: templates/account/account-details.php:94
msgid "Save Changes"
msgstr "Guardar cambios"

#: includes/admin/menu-pages/booking-rules-menu-page.php:199
msgid "Check-in days"
msgstr "Días de llegada"

#: includes/admin/menu-pages/booking-rules-menu-page.php:200
msgid "Guests can check in any day."
msgstr "Los húespedes pueden entrar cualquier día"

#: includes/admin/menu-pages/booking-rules-menu-page.php:201
#: includes/admin/menu-pages/booking-rules-menu-page.php:246
#: includes/admin/menu-pages/booking-rules-menu-page.php:291
#: includes/admin/menu-pages/booking-rules-menu-page.php:337
#: includes/admin/menu-pages/booking-rules-menu-page.php:383
#: includes/admin/menu-pages/booking-rules-menu-page.php:468
#: includes/admin/menu-pages/booking-rules-menu-page.php:514
#: includes/admin/menu-pages/booking-rules-menu-page.php:560
msgid "Add rule"
msgstr "Agregar regla"

#: includes/admin/menu-pages/booking-rules-menu-page.php:229
#: includes/admin/menu-pages/booking-rules-menu-page.php:274
#: includes/admin/menu-pages/booking-rules-menu-page.php:320
#: includes/admin/menu-pages/booking-rules-menu-page.php:366
#: includes/admin/menu-pages/booking-rules-menu-page.php:497
#: includes/admin/menu-pages/booking-rules-menu-page.php:543
#: includes/admin/menu-pages/booking-rules-menu-page.php:589
#: includes/post-types/season-cpt.php:98
#: includes/post-types/season-cpt.php:108
msgid "Seasons"
msgstr "Temporadas"

#: includes/admin/menu-pages/booking-rules-menu-page.php:244
msgid "Check-out days"
msgstr "Días de salida"

#: includes/admin/menu-pages/booking-rules-menu-page.php:245
msgid "Guests can check out any day."
msgstr "Los húespedes pueden salir cualquier día"

#: includes/admin/menu-pages/booking-rules-menu-page.php:289
#: includes/admin/menu-pages/booking-rules-menu-page.php:300
msgid "Minimum stay"
msgstr "Estancia mínima"

#: includes/admin/menu-pages/booking-rules-menu-page.php:290
msgid "There are no minimum stay rules."
msgstr "No hay reglas de estancia mínima."

#: includes/admin/menu-pages/booking-rules-menu-page.php:301
#: includes/admin/menu-pages/booking-rules-menu-page.php:347
#: includes/admin/menu-pages/booking-rules-menu-page.php:478
#: includes/admin/menu-pages/booking-rules-menu-page.php:524
#: includes/admin/menu-pages/booking-rules-menu-page.php:570
msgid "nights"
msgstr "noches"

#: includes/admin/menu-pages/booking-rules-menu-page.php:335
#: includes/admin/menu-pages/booking-rules-menu-page.php:346
msgid "Maximum stay"
msgstr "Estancia máxima"

#: includes/admin/menu-pages/booking-rules-menu-page.php:336
msgid "There are no maximum stay rules."
msgstr "No hay reglas de estancia máxima."

#: includes/admin/menu-pages/booking-rules-menu-page.php:381
msgid "Block accommodation"
msgstr "Bloquear alojamiento"

#: includes/admin/menu-pages/booking-rules-menu-page.php:382
msgid "There are no blocking accommodation rules."
msgstr "No hay reglas para el bloqueo de alojamientos"

#: includes/admin/menu-pages/booking-rules-menu-page.php:414
#: includes/bookings-calendar.php:723
#: includes/bookings-calendar.php:817
msgid "From"
msgstr "Desde"

#: includes/admin/menu-pages/booking-rules-menu-page.php:424
msgid "Till"
msgstr "Hasta"

#: includes/admin/menu-pages/booking-rules-menu-page.php:434
msgid "Restriction"
msgstr "Restriccion"

#: includes/admin/menu-pages/booking-rules-menu-page.php:436
msgid "Not check-in rule marks the date as unavailable for check-in."
msgstr "Regla de no facturación/check-in marca la fecha como no disponible para el check-in."

#: includes/admin/menu-pages/booking-rules-menu-page.php:437
msgid "Not check-out rule marks the date as unavailable for check-out."
msgstr "Regla de no facturación/check-in marca la fecha como no disponible para el check-in."

#: includes/admin/menu-pages/booking-rules-menu-page.php:438
msgid "Not stay-in rule displays the date as blocked. This date is unavailable for check-in and check-out on the next date."
msgstr "La \"regla de no estancia\" muestra la fecha como bloqueada. Esta fecha no está disponible para la salida y el check-in en la próxima fecha."

#: includes/admin/menu-pages/booking-rules-menu-page.php:439
msgid "Not stay-in with Not check-out rules completely block the selected date, additionally displaying the previous date as unavailable for check-in."
msgstr "La \"regla de no estancia\" muestra la fecha como bloqueada. Esta fecha no está disponible para la salida y el check-in en la próxima fecha."

#: includes/admin/menu-pages/booking-rules-menu-page.php:444
#: includes/script-managers/public-script-manager.php:208
msgid "Not check-in"
msgstr "Límite de llegada"

#: includes/admin/menu-pages/booking-rules-menu-page.php:445
#: includes/script-managers/public-script-manager.php:209
msgid "Not check-out"
msgstr "Límite de salida"

#: includes/admin/menu-pages/booking-rules-menu-page.php:446
#: includes/script-managers/public-script-manager.php:207
msgid "Not stay-in"
msgstr "Límite de estadía"

#: includes/admin/menu-pages/booking-rules-menu-page.php:454
msgid "Comment"
msgstr "Comentario"

#: includes/admin/menu-pages/booking-rules-menu-page.php:466
#: includes/admin/menu-pages/booking-rules-menu-page.php:477
msgid "Minimum advance reservation"
msgstr "Antelación de reserva mínima"

#: includes/admin/menu-pages/booking-rules-menu-page.php:467
msgid "There are no minimum advance reservation rules."
msgstr "No hay reglas de reserva mínima por adelantado."

#: includes/admin/menu-pages/booking-rules-menu-page.php:512
#: includes/admin/menu-pages/booking-rules-menu-page.php:523
msgid "Maximum advance reservation"
msgstr "Antelación de reserva máxima"

#: includes/admin/menu-pages/booking-rules-menu-page.php:513
msgid "There are no maximum advance reservation rules."
msgstr "No hay reglas de reserva máxima anticipada."

#: includes/admin/menu-pages/booking-rules-menu-page.php:558
#: includes/admin/menu-pages/booking-rules-menu-page.php:569
msgid "Booking buffer"
msgstr "Búfer de reservas"

#: includes/admin/menu-pages/booking-rules-menu-page.php:559
msgid "There are no booking buffer rules."
msgstr "No hay reglas de amortizacion de la reserva."

#: includes/admin/menu-pages/calendar-menu-page.php:41
#: includes/admin/menu-pages/calendar-menu-page.php:69
msgid "Booking Calendar"
msgstr "Calendario de reserva"

#: includes/admin/menu-pages/calendar-menu-page.php:65
msgid "Calendar"
msgstr "Calendario"

#: includes/admin/menu-pages/create-booking-menu-page.php:135
#: includes/admin/menu-pages/create-booking-menu-page.php:169
#: includes/post-types/booking-cpt.php:244
msgid "Add New Booking"
msgstr "Añadir nueva reserva"

#: includes/admin/menu-pages/create-booking-menu-page.php:136
msgid "Clear Search Results"
msgstr "Limpiar resultados de búsqueda"

#: includes/admin/menu-pages/create-booking-menu-page.php:184
#: includes/admin/menu-pages/edit-booking-menu-page.php:69
msgid "Note: booking rules are disabled in the plugin settings and are not taken into account."
msgstr "Nota: las reglas de reserva están desactivadas en la configuración del plugin y no se tienen en cuenta."

#: includes/admin/menu-pages/create-booking/booking-step.php:50
#: includes/shortcodes/checkout-shortcode/step-booking.php:478
msgid "Unable to create booking. Please try again."
msgstr "No se puede crear una reserva. Vuelva a intentarlo."

#: includes/admin/menu-pages/create-booking/booking-step.php:74
#: includes/shortcodes/checkout-shortcode/step-booking.php:107
msgid "Booking is blocked due to maintenance reason. Please try again later."
msgstr "La reserva está bloqueada por razones de mantenimiento. Por favor, inténtelo de nuevo más tarde."

#: includes/admin/menu-pages/create-booking/booking-step.php:121
#: includes/admin/menu-pages/create-booking/booking-step.php:275
#: includes/admin/menu-pages/create-booking/checkout-step.php:130
#: includes/admin/menu-pages/edit-booking/booking-control.php:34
#: includes/admin/menu-pages/edit-booking/checkout-control.php:61
#: includes/admin/menu-pages/edit-booking/summary-control.php:56
#: includes/shortcodes/checkout-shortcode/step-booking.php:183
#: includes/shortcodes/checkout-shortcode/step-booking.php:363
#: includes/shortcodes/checkout-shortcode/step-checkout.php:170
#: includes/utils/parse-utils.php:250
msgid "There are no accommodations selected for reservation."
msgstr "No hay alojamientos seleccionados para hacer una reserva."

#: includes/admin/menu-pages/create-booking/booking-step.php:123
#: includes/admin/menu-pages/create-booking/booking-step.php:155
#: includes/admin/menu-pages/create-booking/checkout-step.php:132
#: includes/admin/menu-pages/create-booking/checkout-step.php:165
#: includes/admin/menu-pages/create-booking/checkout-step.php:196
#: includes/utils/parse-utils.php:210
#: includes/utils/parse-utils.php:285
#: includes/utils/parse-utils.php:305
msgid "Selected accommodations are not valid."
msgstr "Los alojamientos seleccionados no son válidos."

#: includes/admin/menu-pages/create-booking/booking-step.php:150
#: includes/admin/menu-pages/create-booking/checkout-step.php:160
#: includes/admin/menu-pages/create-booking/step.php:191
#: includes/ajax.php:612
#: includes/shortcodes/checkout-shortcode/step-booking.php:200
#: includes/shortcodes/checkout-shortcode/step-booking.php:207
#: includes/shortcodes/checkout-shortcode/step-checkout.php:187
#: includes/shortcodes/checkout-shortcode/step-checkout.php:199
#: includes/utils/parse-utils.php:301
msgid "Accommodation Type is not valid."
msgstr "Tipo de alojamiento no válido."

#: includes/admin/menu-pages/create-booking/booking-step.php:160
#: includes/admin/menu-pages/create-booking/booking-step.php:184
#: includes/ajax.php:623
#: includes/shortcodes/checkout-shortcode/step-booking.php:213
#: includes/shortcodes/checkout-shortcode/step-booking.php:231
#: includes/utils/parse-utils.php:322
msgid "Rate is not valid."
msgstr "Tarifa no válida."

#: includes/admin/menu-pages/create-booking/booking-step.php:189
#: includes/admin/menu-pages/create-booking/step.php:211
#: includes/admin/menu-pages/create-booking/step.php:215
#: includes/shortcodes/checkout-shortcode/step-booking.php:237
#: includes/shortcodes/search-results-shortcode.php:634
#: includes/utils/parse-utils.php:163
#: includes/utils/parse-utils.php:326
msgid "Adults number is not valid."
msgstr "Número de adultos no válido."

#: includes/admin/menu-pages/create-booking/booking-step.php:194
#: includes/admin/menu-pages/create-booking/step.php:235
#: includes/admin/menu-pages/create-booking/step.php:239
#: includes/ajax.php:500
#: includes/shortcodes/checkout-shortcode/step-booking.php:243
#: includes/shortcodes/search-results-shortcode.php:650
#: includes/utils/parse-utils.php:187
#: includes/utils/parse-utils.php:330
msgid "Children number is not valid."
msgstr "Número de niños no válido."

#: includes/admin/menu-pages/create-booking/booking-step.php:199
#: includes/ajax.php:634
#: includes/shortcodes/checkout-shortcode/step-booking.php:248
#: includes/utils/parse-utils.php:334
msgid "The total number of guests is not valid."
msgstr "El número total de huéspedes no es válido."

#: includes/admin/menu-pages/create-booking/booking-step.php:210
#: includes/admin/menu-pages/create-booking/checkout-step.php:181
#: includes/shortcodes/checkout-shortcode/step-booking.php:259
#: includes/shortcodes/checkout-shortcode/step-checkout.php:245
#: includes/utils/parse-utils.php:345
msgid "Selected dates do not meet booking rules for type %s"
msgstr "Las fechas seleccionadas no cumplen con las reglas de reserva para el tipo %s"

#: includes/admin/menu-pages/create-booking/booking-step.php:263
#: includes/admin/menu-pages/create-booking/checkout-step.php:186
#: includes/utils/parse-utils.php:264
msgid "Accommodations are not available."
msgstr "Alojamientos no disponibles"

#: includes/admin/menu-pages/create-booking/checkout-step.php:170
#: includes/shortcodes/checkout-shortcode/step-checkout.php:234
msgid "There are no rates for requested dates."
msgstr "No hay tarifas para las fechas solicitadas."

#: includes/admin/menu-pages/create-booking/results-step.php:211
#: includes/admin/menu-pages/settings-menu-page.php:542
#: includes/wizard.php:93
msgid "Search Results"
msgstr "Resultados de búsqueda"

#: includes/admin/menu-pages/create-booking/search-step.php:47
#: includes/admin/menu-pages/create-booking/search-step.php:50
msgid "— Any —"
msgstr "— Cualquiera —"

#: includes/admin/menu-pages/create-booking/step.php:34
msgid "Search parameters are not set."
msgstr "Los parámetros de búsqueda no están configurados."

#: includes/admin/menu-pages/create-booking/step.php:129
#: includes/ajax.php:438
#: includes/script-managers/public-script-manager.php:223
#: includes/shortcodes/checkout-shortcode/step.php:53
#: includes/shortcodes/search-results-shortcode.php:665
#: includes/utils/parse-utils.php:87
msgid "Check-in date is not valid."
msgstr "Fecha de llegada no válida."

#: includes/admin/menu-pages/create-booking/step.php:131
#: includes/shortcodes/checkout-shortcode/step.php:56
#: includes/shortcodes/search-results-shortcode.php:668
#: includes/utils/parse-utils.php:89
msgid "Check-in date cannot be earlier than today."
msgstr "La fecha de llegada no puede ser anterior a la fecha actual."

#: includes/admin/menu-pages/create-booking/step.php:157
#: includes/ajax.php:457
#: includes/script-managers/public-script-manager.php:224
#: includes/shortcodes/checkout-shortcode/step.php:90
#: includes/shortcodes/search-results-shortcode.php:686
#: includes/utils/parse-utils.php:120
msgid "Check-out date is not valid."
msgstr "Fecha de salida no válida."

#: includes/admin/menu-pages/create-booking/step.php:168
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:106
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:210
#: includes/shortcodes/checkout-shortcode/step.php:101
#: includes/shortcodes/search-results-shortcode.php:698
#: includes/utils/parse-utils.php:131
msgid "Nothing found. Please try again with different search parameters."
msgstr "Nada Encontrado. Por favor, inténtelo de nuevo con diferentes parámetros de búsqueda."

#: includes/admin/menu-pages/customers-menu-page.php:54
msgid "Sorry, you are not allowed to access this page."
msgstr "Lo sentimos, no tienes permiso para acceder a esta página."

#: includes/admin/menu-pages/customers-menu-page.php:160
msgid "User ID"
msgstr "ID de usuario"

#: includes/admin/menu-pages/customers-menu-page.php:171
#: templates/account/account-details.php:30
msgid "Username"
msgstr "Nombre de usuario"

#: includes/admin/menu-pages/customers-menu-page.php:183
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:114
#: includes/bundles/customer-bundle.php:92
#: includes/csv/bookings/bookings-exporter-helper.php:81
#: includes/post-types/booking-cpt.php:90
#: includes/post-types/payment-cpt.php:247
#: includes/views/shortcodes/checkout-view.php:588
#: templates/account/account-details.php:22
msgid "First Name"
msgstr "Nombre"

#: includes/admin/menu-pages/customers-menu-page.php:195
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:118
#: includes/bundles/customer-bundle.php:101
#: includes/csv/bookings/bookings-exporter-helper.php:82
#: includes/post-types/booking-cpt.php:98
#: includes/post-types/payment-cpt.php:255
#: includes/views/shortcodes/checkout-view.php:603
#: templates/account/account-details.php:26
msgid "Last Name"
msgstr "Apellido"

#: includes/admin/menu-pages/customers-menu-page.php:219
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:126
#: includes/bundles/customer-bundle.php:119
#: includes/csv/bookings/bookings-exporter-helper.php:84
#: includes/post-types/booking-cpt.php:114
#: includes/post-types/payment-cpt.php:271
#: includes/views/shortcodes/checkout-view.php:633
#: templates/account/account-details.php:38
msgid "Phone"
msgstr "Teléfono"

#: includes/admin/menu-pages/customers-menu-page.php:231
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:134
#: includes/bundles/customer-bundle.php:137
#: includes/csv/bookings/bookings-exporter-helper.php:86
#: includes/post-types/booking-cpt.php:131
#: includes/views/shortcodes/checkout-view.php:675
#: templates/account/account-details.php:42
msgid "Address"
msgstr "Dirección"

#: includes/admin/menu-pages/customers-menu-page.php:243
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:142
#: includes/bundles/customer-bundle.php:155
#: includes/csv/bookings/bookings-exporter-helper.php:88
#: includes/post-types/booking-cpt.php:147
#: includes/post-types/payment-cpt.php:311
#: includes/views/shortcodes/checkout-view.php:705
#: templates/account/account-details.php:64
msgid "State / County"
msgstr "Estado/Condado"

#: includes/admin/menu-pages/customers-menu-page.php:255
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:130
#: includes/csv/bookings/bookings-exporter-helper.php:85
#: includes/post-types/booking-cpt.php:123
#: includes/post-types/payment-cpt.php:279
#: templates/account/account-details.php:46
msgid "Country"
msgstr "País"

#: includes/admin/menu-pages/customers-menu-page.php:268
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:138
#: includes/bundles/customer-bundle.php:146
#: includes/csv/bookings/bookings-exporter-helper.php:87
#: includes/post-types/booking-cpt.php:139
#: includes/post-types/payment-cpt.php:303
#: includes/views/shortcodes/checkout-view.php:690
#: templates/account/account-details.php:68
msgid "City"
msgstr "Ciudad"

#: includes/admin/menu-pages/customers-menu-page.php:280
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:146
#: includes/bundles/customer-bundle.php:164
#: includes/csv/bookings/bookings-exporter-helper.php:89
#: includes/post-types/booking-cpt.php:155
#: includes/views/shortcodes/checkout-view.php:720
#: templates/account/account-details.php:72
msgid "Postcode"
msgstr "Código postal"

#: includes/admin/menu-pages/customers-menu-page.php:301
#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/i-cal-import-menu-page.php:162
#: includes/admin/menu-pages/i-cal-import-menu-page.php:209
#: includes/admin/menu-pages/i-cal-menu-page.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:40
#: includes/post-types/editable-cpt.php:95
msgid "Back"
msgstr "Atras"

#: includes/admin/menu-pages/customers-menu-page.php:305
msgid "Edit User Profile"
msgstr "Editar el perfil del usuario"

#: includes/admin/menu-pages/customers-menu-page.php:322
msgid "Customer data updated."
msgstr "Datos del cliente actualizados."

#: includes/admin/menu-pages/customers-menu-page.php:328
msgid "User account updated."
msgstr "Cuenta del usuario actualizada."

#: includes/admin/menu-pages/customers-menu-page.php:363
#: includes/admin/menu-pages/i-cal-menu-page.php:150
msgid "Update"
msgstr "Actualizar"

#: includes/admin/menu-pages/customers-menu-page.php:369
#: includes/admin/menu-pages/customers-menu-page.php:382
#: includes/admin/menu-pages/customers-menu-page.php:386
msgid "Customers"
msgstr "Clientes"

#: includes/admin/menu-pages/edit-booking-menu-page.php:80
msgid "The booking is not set."
msgstr "La reserva no está establecida."

#: includes/admin/menu-pages/edit-booking-menu-page.php:88
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:49
msgid "The booking not found."
msgstr "Reserva no encontrada."

#: includes/admin/menu-pages/edit-booking-menu-page.php:140
msgid "Edit Booking #%d"
msgstr "Editar reserva #%d"

#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/reports-menu-page.php:201
msgid "Cancel"
msgstr "Cancelar"

#: includes/admin/menu-pages/edit-booking-menu-page.php:227
#: includes/admin/menu-pages/edit-booking-menu-page.php:234
#: includes/post-types/booking-cpt.php:245
msgid "Edit Booking"
msgstr "Editar reserva"

#: includes/admin/menu-pages/edit-booking/booking-control.php:18
#: includes/admin/menu-pages/edit-booking/checkout-control.php:35
#: includes/admin/menu-pages/edit-booking/edit-control.php:56
#: includes/admin/menu-pages/edit-booking/summary-control.php:31
msgid "You cannot edit the imported booking. Please update the source booking and resync your calendars."
msgstr "No puede editar la reserva importada. Actualice la reserva de origen y vuelva a sincronizar sus calendarios."

#: includes/admin/menu-pages/edit-booking/booking-control.php:22
#: includes/ajax-api/ajax-actions/abstract-ajax-api-action.php:142
#: includes/ajax.php:184
msgid "Request does not pass security verification. Please refresh the page and try one more time."
msgstr "La solicitud no pasa la verificación de seguridad. Actualice la página e inténtelo de nuevo."

#: includes/admin/menu-pages/edit-booking/booking-control.php:26
#: includes/admin/menu-pages/edit-booking/checkout-control.php:40
#: includes/admin/menu-pages/edit-booking/summary-control.php:33
#: includes/utils/parse-utils.php:233
msgid "Check-in date is not set."
msgstr "La fecha de entrada no está establecida."

#: includes/admin/menu-pages/edit-booking/booking-control.php:30
#: includes/admin/menu-pages/edit-booking/checkout-control.php:42
#: includes/admin/menu-pages/edit-booking/summary-control.php:35
#: includes/utils/parse-utils.php:235
msgid "Check-out date is not set."
msgstr "La fecha de salida no está establecida."

#: includes/admin/menu-pages/edit-booking/booking-control.php:72
msgid "Unable to update booking. Please try again."
msgstr "No se pudo actualizar la reserva. Inténtalo de nuevo."

#: includes/admin/menu-pages/edit-booking/booking-control.php:75
msgid "Booking was edited."
msgstr "La reserva fue editada."

#: includes/admin/menu-pages/edit-booking/edit-control.php:94
#: includes/script-managers/public-script-manager.php:203
#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Available"
msgstr "Disponible"

#: includes/admin/menu-pages/edit-booking/edit-control.php:96
#: templates/edit-booking/edit-reserved-rooms.php:71
msgid "Replace"
msgstr "Reemplazar"

#: includes/admin/menu-pages/edit-booking/summary-control.php:148
msgid "— Add new —"
msgstr "- Añadir nuevo -"

#: includes/admin/menu-pages/extensions-menu-page.php:137
#: includes/admin/menu-pages/extensions-menu-page.php:185
#: includes/admin/menu-pages/extensions-menu-page.php:190
#: includes/admin/menu-pages/settings-menu-page.php:1192
msgid "Extensions"
msgstr "Extensiones"

#: includes/admin/menu-pages/extensions-menu-page.php:140
msgid "Extend the functionality of Hotel Booking plugin with the number of helpful addons for your custom purposes."
msgstr "Amplía la funcionalidad del plugin de Hotel Bookingcon el número de complementos útiles para tus propósitos personalizados."

#: includes/admin/menu-pages/extensions-menu-page.php:170
msgid "Get this Extension"
msgstr "Obtener esta extensión"

#: includes/admin/menu-pages/extensions-menu-page.php:178
msgid "No extensions found."
msgstr "No se encontró ninguna extensión."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:80
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:60
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:102
#: includes/i-cal/logs-handler.php:73
msgid "Abort Process"
msgstr "Abortar Proceso"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:81
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:61
msgid "Aborting..."
msgstr "Abortando..."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:161
#: includes/admin/menu-pages/i-cal-import-menu-page.php:210
#: includes/admin/menu-pages/i-cal-import-menu-page.php:224
#: includes/admin/room-list-table.php:156
msgid "Import Calendar"
msgstr "Importar Calendario"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:166
msgid "Accommodation: %s"
msgstr "Alojamiento: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:169
msgid "Accommodation Type: %s"
msgstr "Tipo de Alojamiento: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:176
msgid "Please be patient while the calendars are imported. You will be notified via this page when the process is completed."
msgstr "Por favor sea paciente mientras los calendarios son importados. Recibirá una notificación en esta página cuando el proceso sea completado."

#: includes/admin/menu-pages/i-cal-menu-page.php:67
msgid "Accommodation updated."
msgstr "Alojamiento Actualizado"

#: includes/admin/menu-pages/i-cal-menu-page.php:73
msgid "This calendar has already been imported for another accommodation."
msgstr "Este calendario ya ha sido importado para otro alojamiento."

#: includes/admin/menu-pages/i-cal-menu-page.php:103
msgid "Sync, Import and Export Calendars"
msgstr "Sincronizar, Importar y Exportar Calendarios"

#. translators: %s - room name. Example: "Comfort Triple 1"
#: includes/admin/menu-pages/i-cal-menu-page.php:113
msgid "Edit External Calendars of \"%s\""
msgstr "Editar Calendario Externo de \"%s\""

#: includes/admin/menu-pages/i-cal-menu-page.php:122
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:101
msgid "Sync All External Calendars"
msgstr "Sincronizar todos los calendarios externos"

#: includes/admin/menu-pages/i-cal-menu-page.php:123
msgid "Sync your bookings across all online channels like Booking.com, TripAdvisor, Airbnb etc. via iCalendar file format."
msgstr "Sincronizar tus reservas desde todos los canales onlines como Booking.com, TripAdvisor, Airbnb etc. atraves de un fichero en formato iCalendar "

#: includes/admin/menu-pages/i-cal-menu-page.php:219
msgid "Calendar URL"
msgstr "URL del Calendario"

#: includes/admin/menu-pages/i-cal-menu-page.php:225
msgid "Add New Calendar"
msgstr "Agregar Nuevo Calendario"

#: includes/admin/menu-pages/i-cal-menu-page.php:233
#: includes/admin/menu-pages/i-cal-menu-page.php:237
msgid "Sync Calendars"
msgstr "Sincronizar calendarios"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:62
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:103
#: includes/i-cal/logs-handler.php:83
msgid "Delete All Logs"
msgstr "Eliminar todos los registros"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:63
msgid "Deleting..."
msgstr "Eliminando..."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:64
msgid "%d item"
msgstr "%d elemento"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:65
msgid "%d items"
msgstr "%d elementos"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:91
msgid "Calendars Synchronization Status"
msgstr "Estado de la sincronización de calendarios"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:92
msgid "Here you can see synchronization status of your external calendars."
msgstr "Aquí puede ver el estado de la sincronización de sus calendarios externos."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:134
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:148
msgid "Calendars Sync Status"
msgstr "Estado de la sincronización de calendarios"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:151
msgid "Display calendars synchronization status."
msgstr "Mostrar el estado de la sincronización de calendarios"

#: includes/admin/menu-pages/language-menu-page.php:10
#: includes/admin/menu-pages/language-menu-page.php:37
msgid "Language Guide"
msgstr "Guía del idioma"

#: includes/admin/menu-pages/language-menu-page.php:11
msgid "Default language"
msgstr "Idioma predeterminado"

#: includes/admin/menu-pages/language-menu-page.php:13
msgid "This plugin will display all system messages, labels, buttons in the language set in <em>General > Settings > Site Language</em>. If the plugin is not available in your language, you may <a href=\"%s\">contribute your translation</a>."
msgstr "Este complemento mostrará todos los mensajes del sistema, etiquetas y botones en el idioma establecido en <em>General > Ajustes > Idioma del sitio</em>. Si el complemento no está disponible en su idioma, puede <a href=\"%s\">agregar su traducción</a>."

#: includes/admin/menu-pages/language-menu-page.php:14
msgid "Custom translations and edits"
msgstr "Traducciones y ediciones personalizadas"

#: includes/admin/menu-pages/language-menu-page.php:15
msgid "You may customize plugin translation by editing the needed texts or adding your translation following these steps:"
msgstr "Usted puede personalizar la traducción del complemento, editando los textos necesarios o añadiendo su traducción y siguiendo estos pasos:"

#: includes/admin/menu-pages/language-menu-page.php:17
msgid "Take the source file for your translations %s or needed translated locale."
msgstr "Coja el archivo de origen de sus traducciones %s o la localización traducida necesaria."

#: includes/admin/menu-pages/language-menu-page.php:18
msgid "Translate texts with any translation program like Poedit, Loco, Pootle etc."
msgstr "Traduzca textos por medio de cualquier programa de traducción como Poedit, Loco, Pootle etc."

#: includes/admin/menu-pages/language-menu-page.php:19
msgid "Put created .mo file with your translations into the folder %s. Where {lang} is ISO-639 language code and {country} is ISO-3166 country code. Example: Brazilian Portuguese file would be called motopress-hotel-booking-pt_BR.mo."
msgstr "Coloque el archivo .mo con sus traducciones en la carpeta %s. Donde {lang} es el código de idioma ISO-639 y {country} es el código de país ISO-3166. Ejemplo: El archivo en portugués de Brasil se llamaría motopress-hotel-booking-pt_BR.mo."

#: includes/admin/menu-pages/language-menu-page.php:22
msgid "Multilingual content"
msgstr "Contenido multilingüe"

#: includes/admin/menu-pages/language-menu-page.php:23
msgid "If your site is multilingual, you may use additional plugins to translate your added content into multiple languages allowing the site visitors to switch them."
msgstr "Si su sitio es multilingüe, Usted puede usar complementos adicionales para traducir su contenido agregado a varios idiomas y dejar que los visitantes del sitio los elijan."

#: includes/admin/menu-pages/language-menu-page.php:33
msgid "Language"
msgstr "Idioma"

#: includes/admin/menu-pages/reports-menu-page.php:52
#: includes/admin/menu-pages/reports-menu-page.php:211
#: includes/admin/menu-pages/reports-menu-page.php:215
msgid "Reports"
msgstr "Informes"

#: includes/admin/menu-pages/reports-menu-page.php:55
#: includes/admin/room-list-table.php:94
msgid "Export"
msgstr "Exportar"

#: includes/admin/menu-pages/reports-menu-page.php:132
#: includes/bookings-calendar.php:695
msgid "All Statuses"
msgstr "Todos los estados"

#: includes/admin/menu-pages/reports-menu-page.php:138
msgid "Booking dates between"
msgstr "Dias de entrada entre"

#: includes/admin/menu-pages/reports-menu-page.php:139
msgid "Check-in date between"
msgstr "Check-In entre"

#: includes/admin/menu-pages/reports-menu-page.php:140
msgid "Check-out date between"
msgstr "Check-Out entre"

#: includes/admin/menu-pages/reports-menu-page.php:141
msgid "In-house between"
msgstr "Interno entre"

#: includes/admin/menu-pages/reports-menu-page.php:142
msgid "Date of reservation between"
msgstr "Fechas de reservas entre"

#: includes/admin/menu-pages/reports-menu-page.php:152
msgid "Export Bookings"
msgstr "Exportar reservas"

#: includes/admin/menu-pages/reports-menu-page.php:164
msgid "Choose start date"
msgstr "Seleccione día de entrada"

#: includes/admin/menu-pages/reports-menu-page.php:165
msgid "Choose end date"
msgstr "Selecione dìa de salida"

#: includes/admin/menu-pages/reports-menu-page.php:171
msgid "Also export imported bookings"
msgstr "También exportar reservas importadas"

#: includes/admin/menu-pages/reports-menu-page.php:175
msgid "Select columns to export"
msgstr "Selecciona las columnas para exportar"

#: includes/admin/menu-pages/reports-menu-page.php:185
msgid "Generate CSV"
msgstr "Generar CSV"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:25
msgid "Number of accommodations"
msgstr "Número de alojamientos"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:57
#: includes/payments/gateways/gateway.php:494
#: includes/widgets/rooms-widget.php:185
#: templates/create-booking/results/reserve-rooms.php:35
#: assets/blocks/blocks.js:436
#: assets/blocks/blocks.js:686
#: assets/blocks/blocks.js:1215
msgid "Title"
msgstr "Título"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:61
msgid "Leave empty to use accommodation type title."
msgstr "Deje vacío para usar el título del tipo de alojamiento."

#: includes/admin/menu-pages/rooms-generator-menu-page.php:66
msgid "Generate"
msgstr "Generar"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:75
msgid "Accommodation generated."
msgid_plural "%s accommodations generated."
msgstr[0] "Alojamiento generado."
msgstr[1] "%s alojamientos generados."

#: includes/admin/menu-pages/settings-menu-page.php:113
msgid "General"
msgstr "General"

#: includes/admin/menu-pages/settings-menu-page.php:116
msgid "Pages"
msgstr "Páginas"

#: includes/admin/menu-pages/settings-menu-page.php:123
msgid "Search Results Page"
msgstr "Página de resultados de búsqueda"

#: includes/admin/menu-pages/settings-menu-page.php:124
msgid "Select page to display search results. Use search results shortcode on this page."
msgstr "Seleccione una página en la que se muestren los resultados de búsqueda. Utilice el código corto de resultados de búsqueda en esta página."

#: includes/admin/menu-pages/settings-menu-page.php:132
msgid "Checkout Page"
msgstr "Página de pago"

#: includes/admin/menu-pages/settings-menu-page.php:133
msgid "Select page user will be redirected to complete booking."
msgstr "Seleccione una  página en la que un usuario pueda completar su reserva."

#: includes/admin/menu-pages/settings-menu-page.php:141
msgid "Terms & Conditions"
msgstr "Términos y condiciones"

#: includes/admin/menu-pages/settings-menu-page.php:142
msgid "If you define a \"Terms\" page the customer will be asked if they accept them when checking out."
msgstr "Si define una página de \"Términos\", se le preguntará al cliente si la acepta al momento de finalizar la reserva."

#: includes/admin/menu-pages/settings-menu-page.php:150
msgid "Open the Terms & Conditions page in a new window"
msgstr "Abrir la página de Términos y Condiciones en una ventana nueva"

#: includes/admin/menu-pages/settings-menu-page.php:151
msgid "By enabling this option you can avoid errors related to displaying your terms & conditions inline for website pages created in page builders."
msgstr "Al activar esta opción puede evitar errores relacionados con la visualización de sus términos y condiciones en línea para páginas web creadas en los constructores de páginas."

#: includes/admin/menu-pages/settings-menu-page.php:159
msgid "My Account Page"
msgstr "Página de mi cuenta"

#: includes/admin/menu-pages/settings-menu-page.php:160
msgid "Select a page to display user account. Use the customer account shortcode on this page."
msgstr "Selecciona una página para mostrar la cuenta del usuario. Utiliza el código corto de la cuenta del cliente en esta página."

#: includes/admin/menu-pages/settings-menu-page.php:170
#: includes/admin/menu-pages/settings-menu-page.php:177
#: includes/post-types/payment-cpt.php:205
msgid "Currency"
msgstr "Moneda"

#: includes/admin/menu-pages/settings-menu-page.php:186
msgid "Currency Position"
msgstr "Posición de moneda"

#: includes/admin/menu-pages/settings-menu-page.php:195
msgid "Decimal Separator"
msgstr "Separador decimal"

#: includes/admin/menu-pages/settings-menu-page.php:204
msgid "Thousand Separator"
msgstr "Separador de miles"

#: includes/admin/menu-pages/settings-menu-page.php:214
msgid "Number of Decimals"
msgstr "Número de decimales"

#: includes/admin/menu-pages/settings-menu-page.php:226
msgid "Misc"
msgstr "Misceláneas"

#: includes/admin/menu-pages/settings-menu-page.php:233
msgid "Square Units"
msgstr "Unidades de superficie"

#: includes/admin/menu-pages/settings-menu-page.php:242
msgid "Datepicker Date Format"
msgstr "Formato de fecha en selector de fechas"

#: includes/admin/menu-pages/settings-menu-page.php:251
#: includes/emails/templaters/email-templater.php:148
msgid "Check-out Time"
msgstr "Hora de salida"

#: includes/admin/menu-pages/settings-menu-page.php:259
#: includes/emails/templaters/email-templater.php:144
msgid "Check-in Time"
msgstr "Hora de llegada"

#: includes/admin/menu-pages/settings-menu-page.php:267
msgid "Bed Types"
msgstr "Tipos de camas"

#: includes/admin/menu-pages/settings-menu-page.php:274
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:155
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:251
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:338
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:409
#: includes/post-types/attributes-cpt.php:365
#: includes/post-types/coupon-cpt.php:79
#: includes/post-types/coupon-cpt.php:111
#: includes/post-types/coupon-cpt.php:156
msgid "Type"
msgstr "Tipo"

#: includes/admin/menu-pages/settings-menu-page.php:279
msgid "Add Bed Type"
msgstr "Añadir tipo de cama"

#: includes/admin/menu-pages/settings-menu-page.php:286
msgid "Show Lowest Price for"
msgstr "Mostrar el precio más bajo por"

#: includes/admin/menu-pages/settings-menu-page.php:287
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:185
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:281
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:367
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:438
msgid "days"
msgstr "días"

#: includes/admin/menu-pages/settings-menu-page.php:291
msgid "Lowest price of accommodation for selected number of days if check-in and check-out dates are not set. Example: set 0 to display today's lowest price, set 7 to display the lowest price for the next week."
msgstr "El precio más bajo de alojamiento por el número seleccionado de días si las fechas de llegada y salida no están establecidas. Ejemplo: establezca 0 para mostrar el precio más bajo de hoy, establezca 7 para mostrar el precio más bajo para la semana siguiente."

#: includes/admin/menu-pages/settings-menu-page.php:298
#: includes/post-types/coupon-cpt.php:288
msgid "Coupons"
msgstr "Cupones"

#: includes/admin/menu-pages/settings-menu-page.php:308
msgid "Default calendar view"
msgstr "Vista de calendario por defecto"

#: includes/admin/menu-pages/settings-menu-page.php:309
msgid "Initial display format of the administrator bookings calendar."
msgstr "Formato inicial de visualización del calendario de reservas de administradores."

#: includes/admin/menu-pages/settings-menu-page.php:317
msgid "Text on Checkout"
msgstr "Texto al finalizar reserva"

#: includes/admin/menu-pages/settings-menu-page.php:318
msgid "This text will appear on the checkout page."
msgstr "Este texto aparecerá en la página de pago."

#: includes/admin/menu-pages/settings-menu-page.php:329
msgid "Disable Booking"
msgstr "Desactivar reserva"

#: includes/admin/menu-pages/settings-menu-page.php:336
msgid "Hide reservation forms and buttons"
msgstr "Ocultar formularios y botones de reserva"

#: includes/admin/menu-pages/settings-menu-page.php:345
msgid "Text instead of reservation form while booking is disabled"
msgstr "El texto que se muestra en lugar del formulario de reserva en caso de que la reserva está desactivada"

#: includes/admin/menu-pages/settings-menu-page.php:356
#: includes/admin/menu-pages/shortcodes-menu-page.php:510
#: includes/wizard.php:115
#: assets/blocks/blocks.js:1562
#: assets/blocks/blocks.js:1592
msgid "Booking Confirmation"
msgstr "Confirmación de reserva"

#: includes/admin/menu-pages/settings-menu-page.php:363
msgid "Confirmation Mode"
msgstr "Modo de confirmación"

#: includes/admin/menu-pages/settings-menu-page.php:365
msgid "By customer via email"
msgstr "Por cliente vía email"

#: includes/admin/menu-pages/settings-menu-page.php:366
msgid "By admin manually"
msgstr "Por admin manualmente"

#: includes/admin/menu-pages/settings-menu-page.php:367
msgid "Confirmation upon payment"
msgstr "Confirmación después de pago"

#: includes/admin/menu-pages/settings-menu-page.php:376
msgid "Booking Confirmed Page"
msgstr "Página de reserva confirmada"

#: includes/admin/menu-pages/settings-menu-page.php:377
msgid "Page user will be redirected to once the booking is confirmed via email or by admin."
msgstr "El usuario de la página será redirigido a una vez que la reserva sea confirmada por correo electrónico o por el administrador."

#: includes/admin/menu-pages/settings-menu-page.php:385
msgid "Approval Time for User"
msgstr "Período de confirmación para usuario"

#: includes/admin/menu-pages/settings-menu-page.php:386
msgid "Period of time in minutes the user is given to confirm booking via email. Unconfirmed bookings become Abandoned and accommodation status changes to Available."
msgstr "Un período de tiempo en minutos que un usuario tiene para confirmar su reserva por correo electrónico. Las reservas no confirmadas se convierten en abandonadas y el estado del alojamiento cambia a disponible."

#: includes/admin/menu-pages/settings-menu-page.php:396
msgid "Country of residence field is required for reservation."
msgstr "El campo del país de residencia es obligatorio para la reserva."

#: includes/admin/menu-pages/settings-menu-page.php:404
msgid "Full address fields are required for reservation."
msgstr "Los campos de dirección son obligatorios para la reserva."

#: includes/admin/menu-pages/settings-menu-page.php:412
msgid "Customer information is required when placing admin bookings."
msgstr "Se requiere información del cliente al realizar reservas como administrador."

#: includes/admin/menu-pages/settings-menu-page.php:421
msgid "Default Country on Checkout"
msgstr "País predeterminado al finalizar la compra"

#: includes/admin/menu-pages/settings-menu-page.php:429
#: includes/emails/templaters/email-templater.php:199
#: includes/post-types/booking-cpt.php:194
#: includes/views/shortcodes/checkout-view.php:477
msgid "Price Breakdown"
msgstr "Desglose de precios"

#: includes/admin/menu-pages/settings-menu-page.php:430
msgid "Price breakdown unfolded by default."
msgstr "Desglose de precios desplegado de forma predererminada."

#: includes/admin/menu-pages/settings-menu-page.php:439
msgid "Accounts"
msgstr "Cuentas"

#: includes/admin/menu-pages/settings-menu-page.php:446
msgid "Account creation"
msgstr "Creación de cuenta"

#: includes/admin/menu-pages/settings-menu-page.php:447
msgid "Automatically create an account for a user at checkout."
msgstr "Crea automáticamente una cuenta para un usuario en el momento del pago."

#: includes/admin/menu-pages/settings-menu-page.php:455
msgid "Allow customers to create an account during checkout."
msgstr "Permite a los clientes crear una cuenta durante el pago."

#: includes/admin/menu-pages/settings-menu-page.php:463
msgid "Allow customers to log into their existing account during checkout."
msgstr "Permite a los clientes iniciar sesión en su cuenta existente durante el pago."

#: includes/admin/menu-pages/settings-menu-page.php:472
#: includes/upgrader.php:751
#: includes/wizard.php:164
msgid "Booking Cancellation"
msgstr "Cancelación de reserva"

#: includes/admin/menu-pages/settings-menu-page.php:479
msgid "User can cancel booking via link provided inside email."
msgstr "El usuario puede cancelar su reserva al hacer clic en el enlace enviado a su email."

#: includes/admin/menu-pages/settings-menu-page.php:487
msgid "Booking Cancelation Page"
msgstr "Página de cancelación de reserva"

#: includes/admin/menu-pages/settings-menu-page.php:488
msgid "Page to confirm booking cancelation."
msgstr "Página para confirmar la cancelación de la reserva."

#: includes/admin/menu-pages/settings-menu-page.php:496
msgid "Booking Canceled Page"
msgstr "Página de reserva cancelada"

#: includes/admin/menu-pages/settings-menu-page.php:497
msgid "Page to redirect to after a booking is canceled."
msgstr "Página a la que se redirige después de cancelar una reserva."

#: includes/admin/menu-pages/settings-menu-page.php:506
msgid "Search Options"
msgstr "Buscar opciones"

#: includes/admin/menu-pages/settings-menu-page.php:515
msgid "Max Adults"
msgstr "Número máximo de adultos"

#: includes/admin/menu-pages/settings-menu-page.php:516
msgid "Maximum accommodation occupancy available in the Search Form."
msgstr "La ocupación máxima de alojamiento disponible en el formulario de búsqueda."

#: includes/admin/menu-pages/settings-menu-page.php:526
msgid "Max Children"
msgstr "Número máximo de niños"

#: includes/admin/menu-pages/settings-menu-page.php:534
msgid "Age of Child"
msgstr "Edad del niño"

#: includes/admin/menu-pages/settings-menu-page.php:535
msgid "Optional description of the \"Children\" field."
msgstr "Descripción opcional en el campo \"niños\"."

#: includes/admin/menu-pages/settings-menu-page.php:543
msgid "Limit search results based on the requested number of guests."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:551
msgid "Book button behavior on the search results page"
msgstr "Comportamiento del botón de reserva en la página de resultados de búsqueda"

#: includes/admin/menu-pages/settings-menu-page.php:552
msgid "Redirect to the checkout page immediately after successful addition to reservation."
msgstr "Reenviar a la pagina de confirmación inmediatamente después de añadirse correctamente a la reserva."

#: includes/admin/menu-pages/settings-menu-page.php:560
msgid "Recommendation"
msgstr "Recomendación"

#: includes/admin/menu-pages/settings-menu-page.php:561
msgid "Enable search form to recommend the best set of accommodations according to a number of guests."
msgstr "Active el formulario de búsqueda para recomendar la mejor selección de alojamientos de acuerdo a un número de visitantes."

#: includes/admin/menu-pages/settings-menu-page.php:570
msgid "Skip Search Results"
msgstr "Omitir resultados de la búsqueda"

#: includes/admin/menu-pages/settings-menu-page.php:571
msgid "Skip search results page and enable direct booking from accommodation pages."
msgstr "Omita la página de resultados de búsqueda y habilite la reserva directa desde las páginas de alojamiento."

#: includes/admin/menu-pages/settings-menu-page.php:578
msgid "Direct Booking Form"
msgstr "Formulario de reserva directa"

#: includes/admin/menu-pages/settings-menu-page.php:581
msgid "Show price for selected period"
msgstr "Mostrar precio para el período seleccionado"

#: includes/admin/menu-pages/settings-menu-page.php:582
msgid "Show price together with adults and children fields"
msgstr "Mostrar precio el precio de adultos y niños en el mismo campo"

#: includes/admin/menu-pages/settings-menu-page.php:592
msgid "Enable \"adults\" and \"children\" options for my website (default)."
msgstr "Habilitar las opciones \"adultos\" y \"niños\" para mi sitio web (predeterminado)."

#: includes/admin/menu-pages/settings-menu-page.php:593
msgid "Disable \"children\" option for my website (hide \"children\" field and use Guests label instead)."
msgstr "Desactivar la opción \"niños\" para mi sitio web (ocultar el campo \"niños\" y usar la etiqueta Invitados en su lugar)."

#: includes/admin/menu-pages/settings-menu-page.php:594
msgid "Disable \"adults\" and \"children\" options for my website."
msgstr "Desactivar las opciones \"adultos\" y \"niños\" de mi sitio web."

#: includes/admin/menu-pages/settings-menu-page.php:597
msgid "Guest Management"
msgstr "Gestión de huéspedes"

#: includes/admin/menu-pages/settings-menu-page.php:598
msgid "Applies to frontend only."
msgstr "Sólo se aplica a la parte frontal."

#: includes/admin/menu-pages/settings-menu-page.php:606
msgid "Hide \"adults\" and \"children\" fields within search availability forms."
msgstr "Oculte los campos \"adultos\" y \"niños\" dentro de los formularios de disponibilidad de búsqueda."

#: includes/admin/menu-pages/settings-menu-page.php:614
msgid "Remember the user's selected number of guests until the checkout page."
msgstr "Recuerde el número de invitados seleccionado por el usuario hasta la página de pago."

#: includes/admin/menu-pages/settings-menu-page.php:623
msgid "Do not apply booking rules for admin bookings."
msgstr "No aplicar las reglas de reserva para las reservas de la administración."

#: includes/admin/menu-pages/settings-menu-page.php:631
msgid "Display Options"
msgstr "Mostrar opciones"

#: includes/admin/menu-pages/settings-menu-page.php:638
msgid "Display gallery images of accommodation page in lightbox."
msgstr "Mostrar las imágenes de galería de la página de alojamiento en lightbox."

#: includes/admin/menu-pages/settings-menu-page.php:645
#: includes/admin/menu-pages/shortcodes-menu-page.php:61
#: assets/blocks/blocks.js:250
#: assets/blocks/blocks.js:385
msgid "Availability Calendar"
msgstr "Calendario de disponibilidad"

#: includes/admin/menu-pages/settings-menu-page.php:646
#: includes/admin/menu-pages/shortcodes-menu-page.php:76
#: assets/blocks/blocks.js:307
msgid "Display per-night prices in the availability calendar."
msgstr "Mostrar los precios por noche en el calendario de disponibilidad."

#: includes/admin/menu-pages/settings-menu-page.php:654
#: includes/admin/menu-pages/shortcodes-menu-page.php:82
#: assets/blocks/blocks.js:318
msgid "Truncate per-night prices in the availability calendar."
msgstr "Truncar los precios por noche en el calendario de disponibilidad."

#: includes/admin/menu-pages/settings-menu-page.php:662
#: includes/admin/menu-pages/shortcodes-menu-page.php:88
#: assets/blocks/blocks.js:329
msgid "Display the currency sign in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:672
msgid "Calendar Theme"
msgstr "Tema de calendario"

#: includes/admin/menu-pages/settings-menu-page.php:673
msgid "Select theme for an availability calendar."
msgstr "Elija un tema para un calendario de disponibilidad."

#: includes/admin/menu-pages/settings-menu-page.php:680
msgid "Template Mode"
msgstr "Modo de plantilla"

#: includes/admin/menu-pages/settings-menu-page.php:682
msgid "Developer Mode"
msgstr "Modo de desarrollador"

#: includes/admin/menu-pages/settings-menu-page.php:683
msgid "Theme Mode"
msgstr "Modo de tema"

#: includes/admin/menu-pages/settings-menu-page.php:685
msgid "Choose Theme Mode to display the content with the styles of your theme. Choose Developer Mode to control appearance of the content with custom page templates, actions and filters. This option can't be changed if your theme is initially integrated with the plugin."
msgstr "Elija Modo de tema para mostrar el contenido con los estilos de su tema. Elija Modo de desarrollador para controlar la apariencia del contenido a través de plantillas de páginas personalizadas, acciones y filtros. No se puede cambiar esta opción, si su tema inicialmente está integrado con el complemento."

#: includes/admin/menu-pages/settings-menu-page.php:698
msgid "More Styles"
msgstr "Más estilos"

#: includes/admin/menu-pages/settings-menu-page.php:699
msgid "Extend the styling options of Hotel Booking plugin with the new free addon - Hotel Booking Styles."
msgstr "Amplía la funcionalidad del plugin de Hotel Booking con el número de complementos útiles para tus propósitos personalizados."

#: includes/admin/menu-pages/settings-menu-page.php:711
msgid "Calendars Synchronization"
msgstr "Sincronización de calendarios"

#: includes/admin/menu-pages/settings-menu-page.php:718
msgid "Export admin blocks."
msgstr "Exportar bloques de administración."

#: includes/admin/menu-pages/settings-menu-page.php:726
msgid "Do not export imported bookings."
msgstr "No exportar las reservas importadas."

#: includes/admin/menu-pages/settings-menu-page.php:734
msgid "Export and import bookings with buffer time included."
msgstr "Exportar e importar reservas con tiempo de búfer incluido."

#: includes/admin/menu-pages/settings-menu-page.php:742
msgid "Minimize Logs"
msgstr "Minimizar registros"

#: includes/admin/menu-pages/settings-menu-page.php:743
msgid "Enable the plugin to record only important messages."
msgstr "Activar el plugin para grabar sólo mensajes importantes."

#: includes/admin/menu-pages/settings-menu-page.php:751
msgid "Calendars Synchronization Scheduler"
msgstr "Planificador de sincronización de calendarios"

#: includes/admin/menu-pages/settings-menu-page.php:762
msgid "Enable automatic external calendars synchronization"
msgstr "Habilitar la sincronización automática de calendarios externos"

#: includes/admin/menu-pages/settings-menu-page.php:771
msgid "Clock"
msgstr "reloj"

#: includes/admin/menu-pages/settings-menu-page.php:772
msgid "Sync calendars at this time (UTC) or starting at this time every interval below."
msgstr "Sincronice los calendarios en esta hora (UTC) o comience en esta hora cada intervalo a continuación."

#: includes/admin/menu-pages/settings-menu-page.php:783
msgid "Interval"
msgstr "Intervalo"

#: includes/admin/menu-pages/settings-menu-page.php:785
#: includes/crons/cron-manager.php:102
msgid "Quarter an Hour"
msgstr "Cuarto de hora"

#: includes/admin/menu-pages/settings-menu-page.php:786
#: includes/crons/cron-manager.php:107
msgid "Half an Hour"
msgstr "Media hora"

#: includes/admin/menu-pages/settings-menu-page.php:787
msgid "Once Hourly"
msgstr "Cada hora"

#: includes/admin/menu-pages/settings-menu-page.php:788
msgid "Twice Daily"
msgstr "Dos veces al día"

#: includes/admin/menu-pages/settings-menu-page.php:789
msgid "Once Daily"
msgstr "Diariamente"

#: includes/admin/menu-pages/settings-menu-page.php:800
msgid "Automatically delete sync logs older than"
msgstr "Eliminar automáticamente los registros de sincronización anteriores a"

#: includes/admin/menu-pages/settings-menu-page.php:802
msgid "Day"
msgstr "Día"

#: includes/admin/menu-pages/settings-menu-page.php:803
msgid "Week"
msgstr "Semana"

#: includes/admin/menu-pages/settings-menu-page.php:804
#: includes/bookings-calendar.php:575
msgid "Month"
msgstr "Mes"

#: includes/admin/menu-pages/settings-menu-page.php:805
#: includes/bookings-calendar.php:576
msgid "Quarter"
msgstr "Trimestre"

#: includes/admin/menu-pages/settings-menu-page.php:806
msgid "Half a Year"
msgstr "Medio año"

#: includes/admin/menu-pages/settings-menu-page.php:807
msgid "Never Delete"
msgstr "Nunca borrar"

#: includes/admin/menu-pages/settings-menu-page.php:817
msgid "Block Editor"
msgstr "Editor de bloques"

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/admin/menu-pages/settings-menu-page.php:832
msgid "Enable block editor for \"%s\"."
msgstr "Habilitar el editor de bloques para \"%s\"."

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/post-types/coupon-cpt.php:59
#: includes/post-types/room-type-cpt.php:53
#: includes/post-types/room-type-cpt.php:64
#: includes/widgets/rooms-widget.php:21
msgid "Accommodation Types"
msgstr "Tipos de alojamientos"

#: includes/admin/menu-pages/settings-menu-page.php:832
#: includes/csv/bookings/bookings-exporter-helper.php:97
#: includes/emails/templaters/reserved-rooms-templater.php:183
#: includes/post-types/coupon-cpt.php:136
#: includes/post-types/service-cpt.php:91
#: includes/post-types/service-cpt.php:101
#: includes/views/booking-view.php:202
msgid "Services"
msgstr "Servicios"

#: includes/admin/menu-pages/settings-menu-page.php:863
msgid "Admin Emails"
msgstr "Emails del admin"

#: includes/admin/menu-pages/settings-menu-page.php:876
msgid "Customer Emails"
msgstr "Emails del cliente"

#: includes/admin/menu-pages/settings-menu-page.php:881
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the <a>Hotel Booking & Mailchimp Integration</a>."
msgstr "Convierte a los huéspedes esporádicos en clientes leales enviando campañas de marketing automáticas con la <a>integración de Hotel Booking y Mailchimp</a>."

#: includes/admin/menu-pages/settings-menu-page.php:884
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the Hotel Booking & Mailchimp Integration."
msgstr "Convierte a los huéspedes esporádicos en clientes leales enviando campañas de marketing automáticas con la integración de Hotel Booking y Mailchimp."

#: includes/admin/menu-pages/settings-menu-page.php:903
msgid "Cancellation Details Template"
msgstr "Plantilla de detalles de cancelación"

#: includes/admin/menu-pages/settings-menu-page.php:904
msgid "Used for %cancellation_details% tag."
msgstr "Se utilice para la etiqueta %cancellation_details%."

#: includes/admin/menu-pages/settings-menu-page.php:926
msgid "Email Settings"
msgstr "Ajustes de email"

#: includes/admin/menu-pages/settings-menu-page.php:931
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with <a>Hotel Booking Notifier</a>."
msgstr "Envíe notificaciones automáticas por correo electrónico, como instrucciones para recoger las llaves, reglas de la casa, antes y después de la llegada / salida con <a> Notificador de reservas de hotel </a>."

#: includes/admin/menu-pages/settings-menu-page.php:934
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with Hotel Booking Notifier."
msgstr "Envíe notificaciones automáticas por correo electrónico, como instrucciones para recoger las llaves, reglas de la casa, antes y después de la llegada / salida con el notificador de reservas de hotel."

#: includes/admin/menu-pages/settings-menu-page.php:942
msgid "Email Sender"
msgstr "Remitente de email"

#: includes/admin/menu-pages/settings-menu-page.php:949
msgid "Administrator Email"
msgstr "Email Administrador"

#: includes/admin/menu-pages/settings-menu-page.php:958
msgid "From Email"
msgstr "Email \"De\""

#: includes/admin/menu-pages/settings-menu-page.php:967
msgid "From Name"
msgstr "Nombre \"De\""

#: includes/admin/menu-pages/settings-menu-page.php:977
msgid "Logo URL"
msgstr "URL de logo"

#: includes/admin/menu-pages/settings-menu-page.php:987
msgid "Footer Text"
msgstr "Texto de pie de página"

#: includes/admin/menu-pages/settings-menu-page.php:997
msgid "Reserved Accommodation Details Template"
msgstr "Plantilla de detalles de alojamiento reservado"

#: includes/admin/menu-pages/settings-menu-page.php:998
msgid "Used for %reserved_rooms_details% tag."
msgstr "Se utilice para la etiqueta %reserved_rooms_details%."

#: includes/admin/menu-pages/settings-menu-page.php:1011
msgid "Styles"
msgstr "Estilos"

#: includes/admin/menu-pages/settings-menu-page.php:1018
msgid "Base Color"
msgstr "Color de base"

#: includes/admin/menu-pages/settings-menu-page.php:1027
msgid "Background Color"
msgstr "Color de fondo"

#: includes/admin/menu-pages/settings-menu-page.php:1036
msgid "Body Background Color"
msgstr "Color de fondo de cuerpo"

#: includes/admin/menu-pages/settings-menu-page.php:1045
msgid "Body Text Color"
msgstr "Color de texto de cuerpo"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "Payment Gateways"
msgstr "Pasarelas de pago"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "General Settings"
msgstr "Ajustes generales"

#: includes/admin/menu-pages/settings-menu-page.php:1071
msgid "Need more gateways? Use our Hotel Booking <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a> extension."
msgstr "¿Necesitas más pasarelas? Usa nuestra extensión <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a> de Hotel Booking."

#: includes/admin/menu-pages/settings-menu-page.php:1075
msgid "You may also email the <a href=\"%s\" target=\"_blank\">balance payment request</a> link to your guests."
msgstr "También puedes enviar el enlace <a href=\"%s\" target=\"_blank\">solicitud de pago de saldo</a> por correo electrónico a tus huéspedes."

#: includes/admin/menu-pages/settings-menu-page.php:1086
msgid "User Pays"
msgstr "Usuario paga"

#: includes/admin/menu-pages/settings-menu-page.php:1088
msgid "Full Amount"
msgstr "Total"

#: includes/admin/menu-pages/settings-menu-page.php:1089
#: includes/views/booking-view.php:463
msgid "Deposit"
msgstr "Depósito"

#: includes/admin/menu-pages/settings-menu-page.php:1098
msgid "Deposit Type"
msgstr "Tipo del depósito"

#: includes/admin/menu-pages/settings-menu-page.php:1100
#: includes/post-types/coupon-cpt.php:115
#: includes/post-types/coupon-cpt.php:160
msgid "Fixed"
msgstr "Fijo"

#: includes/admin/menu-pages/settings-menu-page.php:1101
msgid "Percent"
msgstr "Por ciento"

#: includes/admin/menu-pages/settings-menu-page.php:1110
msgid "Deposit Amount"
msgstr "Cantidad del depósito"

#: includes/admin/menu-pages/settings-menu-page.php:1121
msgid "Deposit Time Frame (days)"
msgstr "Marco de tiempo de depósito (días)"

#: includes/admin/menu-pages/settings-menu-page.php:1122
msgid "Apply deposit to bookings made in at least the selected number of days prior to the check-in date. Otherwise, the full amount is charged."
msgstr "Aplicar el depósito a las reservas realizadas al menos el número seleccionado de días antes de la fecha de entrada. De lo contrario, se carga el importe total."

#: includes/admin/menu-pages/settings-menu-page.php:1133
msgid "Force Secure Checkout"
msgstr "Pagos seguros"

#: includes/admin/menu-pages/settings-menu-page.php:1135
msgid "Force SSL (HTTPS) on the checkout pages. You must have an SSL certificate installed to use this option."
msgstr "Utilizar SSL (HTTPS) en las páginas de pago. Usted debe tener un certificado SSL instalado para utilizar esta opción."

#: includes/admin/menu-pages/settings-menu-page.php:1142
msgid "Reservation Received Page"
msgstr "Página de reserva recibida"

#: includes/admin/menu-pages/settings-menu-page.php:1150
msgid "Failed Transaction Page"
msgstr "Página de transacción fallida"

#: includes/admin/menu-pages/settings-menu-page.php:1158
msgid "Default Gateway"
msgstr "Pasarela de pago predeterminada"

#: includes/admin/menu-pages/settings-menu-page.php:1172
#: includes/payments/gateways/bank-gateway.php:127
msgid "Pending Payment Time"
msgstr "Tiempo de pago pendiente"

#: includes/admin/menu-pages/settings-menu-page.php:1173
msgid "Period of time in minutes the user is given to complete payment. Unpaid bookings become Abandoned and accommodation status changes to Available."
msgstr "Período de tiempo en minutos que un usuario tiene para completar su pago. Las reservas no pagadas se convierten en abandonadas y el estado del alojamiento cambia a disponible."

#: includes/admin/menu-pages/settings-menu-page.php:1195
msgid "Install <a href=\"%s\" target=\"_blank\">Hotel Booking addons</a> to manage their settings."
msgstr "Instala <a href=\"%s\" target=\"_blank\">complementos de Hotel Booking</a> para gestionar sus configuraciones."

#: includes/admin/menu-pages/settings-menu-page.php:1197
msgid "Install Hotel Booking addons to manage their settings."
msgstr "Instala complementos de Hotel Booking para gestionar sus configuraciones."

#: includes/admin/menu-pages/settings-menu-page.php:1214
msgid "Advanced"
msgstr "Avanzado"

#: includes/admin/menu-pages/settings-menu-page.php:1226
#: includes/admin/menu-pages/settings-menu-page.php:1228
msgid "License"
msgstr "Licencia"

#: includes/admin/menu-pages/settings-menu-page.php:1305
msgid "Settings saved."
msgstr "Ajustes guardados."

#: includes/admin/menu-pages/settings-menu-page.php:1344
msgid "<strong>Note:</strong> Payment methods will appear on the checkout page only when Confirmation Upon Payment is enabled in Accommodation > Settings > General > Confirmation Mode."
msgstr "<strong>Nota:</strong> Los métodos de pago aparecerán en la página de pago sólo cuando esté activada la Confirmación tras el pago en Alojamiento > Configuración > General > Modo de confirmación."

#: includes/admin/menu-pages/settings-menu-page.php:1432
#: includes/admin/menu-pages/settings-menu-page.php:1436
#: assets/blocks/blocks.js:141
#: assets/blocks/blocks.js:276
#: assets/blocks/blocks.js:429
#: assets/blocks/blocks.js:679
#: assets/blocks/blocks.js:1196
#: assets/blocks/blocks.js:1419
#: assets/blocks/blocks.js:1501
msgid "Settings"
msgstr "Ajustes"

#: includes/admin/menu-pages/shortcodes-menu-page.php:21
#: assets/blocks/blocks.js:117
msgid "Availability Search Form"
msgstr "Formulario de búsqueda de disponibilidad"

#: includes/admin/menu-pages/shortcodes-menu-page.php:22
msgid "Display search form."
msgstr "Mostrar formulario de búsqueda."

#: includes/admin/menu-pages/shortcodes-menu-page.php:25
#: assets/blocks/blocks.js:148
msgid "The number of adults presetted in the search form."
msgstr "El número de adultos preestablecido en el formulario de búsqueda."

#: includes/admin/menu-pages/shortcodes-menu-page.php:30
#: assets/blocks/blocks.js:163
msgid "The number of children presetted in the search form."
msgstr "El número de niños preestablecido en el formulario de búsqueda."

#: includes/admin/menu-pages/shortcodes-menu-page.php:35
msgid "Check-in date presetted in the search form."
msgstr "La fecha de llegada preestablecida en el formulario de búsqueda."

#: includes/admin/menu-pages/shortcodes-menu-page.php:36
#: includes/admin/menu-pages/shortcodes-menu-page.php:41
msgid "date in format %s"
msgstr "fecha en formato %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:40
msgid "Check-out date presetted in the search form."
msgstr "La fecha de salida preestablecida en el formulario de búsqueda."

#: includes/admin/menu-pages/shortcodes-menu-page.php:45
#: assets/blocks/blocks.js:201
msgid "Custom attributes for advanced search."
msgstr "Atributos personalizados para búsqueda avanzada."

#: includes/admin/menu-pages/shortcodes-menu-page.php:46
#: assets/blocks/blocks.js:202
msgid "Comma-separated slugs of attributes."
msgstr "Atributos separados por comas."

#: includes/admin/menu-pages/shortcodes-menu-page.php:50
#: includes/admin/menu-pages/shortcodes-menu-page.php:94
#: includes/admin/menu-pages/shortcodes-menu-page.php:180
#: includes/admin/menu-pages/shortcodes-menu-page.php:259
#: includes/admin/menu-pages/shortcodes-menu-page.php:361
#: includes/admin/menu-pages/shortcodes-menu-page.php:422
#: includes/admin/menu-pages/shortcodes-menu-page.php:450
#: includes/admin/menu-pages/shortcodes-menu-page.php:470
#: includes/admin/menu-pages/shortcodes-menu-page.php:494
#: includes/admin/menu-pages/shortcodes-menu-page.php:514
#: includes/admin/menu-pages/shortcodes-menu-page.php:530
#: includes/admin/menu-pages/shortcodes-menu-page.php:546
msgid "Custom CSS class for shortcode wrapper"
msgstr "Clase CSS personalizada para contenedor de código corto"

#: includes/admin/menu-pages/shortcodes-menu-page.php:51
#: includes/admin/menu-pages/shortcodes-menu-page.php:95
#: includes/admin/menu-pages/shortcodes-menu-page.php:181
#: includes/admin/menu-pages/shortcodes-menu-page.php:260
#: includes/admin/menu-pages/shortcodes-menu-page.php:362
#: includes/admin/menu-pages/shortcodes-menu-page.php:423
#: includes/admin/menu-pages/shortcodes-menu-page.php:451
#: includes/admin/menu-pages/shortcodes-menu-page.php:471
#: includes/admin/menu-pages/shortcodes-menu-page.php:495
#: includes/admin/menu-pages/shortcodes-menu-page.php:515
#: includes/admin/menu-pages/shortcodes-menu-page.php:531
#: includes/admin/menu-pages/shortcodes-menu-page.php:547
msgid "whitespace separated css classes"
msgstr "clases de css separadas por espacios en blanco"

#: includes/admin/menu-pages/shortcodes-menu-page.php:65
#: includes/admin/menu-pages/shortcodes-menu-page.php:465
#: includes/admin/menu-pages/shortcodes-menu-page.php:489
#: includes/csv/bookings/bookings-exporter-helper.php:76
#: includes/emails/templaters/reserved-rooms-templater.php:187
msgid "Accommodation Type ID"
msgstr "ID del tipo de alojamiento"

#: includes/admin/menu-pages/shortcodes-menu-page.php:66
#: includes/admin/menu-pages/shortcodes-menu-page.php:466
msgid "ID of Accommodation Type to check availability."
msgstr "ID del tipo de alojamiento para comprobar disponibilidad."

#: includes/admin/menu-pages/shortcodes-menu-page.php:67
#: includes/admin/menu-pages/shortcodes-menu-page.php:378
#: includes/admin/menu-pages/shortcodes-menu-page.php:467
#: includes/admin/menu-pages/shortcodes-menu-page.php:491
msgid "integer number"
msgstr "número entero"

#: includes/admin/menu-pages/shortcodes-menu-page.php:70
#: assets/blocks/blocks.js:295
msgid "How many months to show."
msgstr "Cuántos meses mostrar."

#: includes/admin/menu-pages/shortcodes-menu-page.php:72
#: assets/blocks/blocks.js:296
msgid "Set the number of columns or the number of rows and columns separated by comma. Example: \"3\" or \"2,3\""
msgstr "Establece el número de columnas o el número de filas y columnas separadas por comas. Ejemplo: \"3\" o \"2,3\""

#: includes/admin/menu-pages/shortcodes-menu-page.php:106
#: assets/blocks/blocks.js:251
msgid "Display availability calendar of the current accommodation type or by ID."
msgstr "Mostrar calendario de disponibilidad del tipo de alojamiento actual o por ID."

#: includes/admin/menu-pages/shortcodes-menu-page.php:111
#: assets/blocks/blocks.js:397
#: assets/blocks/blocks.js:630
msgid "Availability Search Results"
msgstr "Resultados de búsqueda de disponibilidad"

#: includes/admin/menu-pages/shortcodes-menu-page.php:112
#: assets/blocks/blocks.js:398
msgid "Display listing of accommodation types that meet the search criteria."
msgstr "Mostrar una lista de tipos de alojamiento que cumplen los criterios de búsqueda."

#: includes/admin/menu-pages/shortcodes-menu-page.php:115
#: includes/admin/menu-pages/shortcodes-menu-page.php:212
#: includes/admin/menu-pages/shortcodes-menu-page.php:381
#: assets/blocks/blocks.js:437
#: assets/blocks/blocks.js:687
#: assets/blocks/blocks.js:1216
msgid "Whether to display title of the accommodation type."
msgstr "Mostrar o no mostrar el título del tipo de alojamiento."

#: includes/admin/menu-pages/shortcodes-menu-page.php:120
#: includes/admin/menu-pages/shortcodes-menu-page.php:217
#: includes/admin/menu-pages/shortcodes-menu-page.php:386
#: assets/blocks/blocks.js:449
#: assets/blocks/blocks.js:699
#: assets/blocks/blocks.js:1228
msgid "Whether to display featured image of the accommodation type."
msgstr "Mostrar o no mostrar la imagen destacada del tipo de alojamiento."

#: includes/admin/menu-pages/shortcodes-menu-page.php:125
#: includes/admin/menu-pages/shortcodes-menu-page.php:222
#: includes/admin/menu-pages/shortcodes-menu-page.php:391
#: assets/blocks/blocks.js:461
#: assets/blocks/blocks.js:711
#: assets/blocks/blocks.js:1240
msgid "Whether to display gallery of the accommodation type."
msgstr "Mostrar o no mostrar la galería del tipo de alojamiento."

#: includes/admin/menu-pages/shortcodes-menu-page.php:130
#: includes/admin/menu-pages/shortcodes-menu-page.php:227
#: includes/admin/menu-pages/shortcodes-menu-page.php:396
#: assets/blocks/blocks.js:473
#: assets/blocks/blocks.js:723
#: assets/blocks/blocks.js:1252
msgid "Whether to display excerpt (short description) of the accommodation type."
msgstr "Mostrar o no mostrar la descripción breve del tipo de alojamiento."

#: includes/admin/menu-pages/shortcodes-menu-page.php:135
#: includes/admin/menu-pages/shortcodes-menu-page.php:232
#: includes/admin/menu-pages/shortcodes-menu-page.php:401
#: assets/blocks/blocks.js:485
#: assets/blocks/blocks.js:735
#: assets/blocks/blocks.js:1264
msgid "Whether to display details of the accommodation type."
msgstr "Mostrar o no mostrar los detalles del tipo de alojamiento."

#: includes/admin/menu-pages/shortcodes-menu-page.php:140
#: includes/admin/menu-pages/shortcodes-menu-page.php:237
#: includes/admin/menu-pages/shortcodes-menu-page.php:406
#: includes/admin/menu-pages/shortcodes-menu-page.php:427
#: assets/blocks/blocks.js:497
#: assets/blocks/blocks.js:747
#: assets/blocks/blocks.js:1276
msgid "Whether to display price of the accommodation type."
msgstr "Mostrar o no mostrar el precio del tipo de alojamiento."

#: includes/admin/menu-pages/shortcodes-menu-page.php:145
#: includes/admin/menu-pages/shortcodes-menu-page.php:242
#: includes/admin/menu-pages/shortcodes-menu-page.php:411
msgid "Show View Details button"
msgstr "Mostrar botón Ver detalles"

#: includes/admin/menu-pages/shortcodes-menu-page.php:146
#: includes/admin/menu-pages/shortcodes-menu-page.php:243
#: includes/admin/menu-pages/shortcodes-menu-page.php:412
#: assets/blocks/blocks.js:509
#: assets/blocks/blocks.js:759
#: assets/blocks/blocks.js:1288
msgid "Whether to display \"View Details\" button with the link to accommodation type."
msgstr "Mostrar o no mostrar el botón \"Ver detalles\" con el enlace al tipo de alojamiento."

#: includes/admin/menu-pages/shortcodes-menu-page.php:151
#: includes/admin/menu-pages/shortcodes-menu-page.php:284
#: includes/admin/menu-pages/shortcodes-menu-page.php:332
msgid "Sort by."
msgstr "Ordenar por."

#: includes/admin/menu-pages/shortcodes-menu-page.php:153
#: includes/admin/menu-pages/shortcodes-menu-page.php:173
#: includes/admin/menu-pages/shortcodes-menu-page.php:286
#: includes/admin/menu-pages/shortcodes-menu-page.php:306
#: includes/admin/menu-pages/shortcodes-menu-page.php:334
#: includes/admin/menu-pages/shortcodes-menu-page.php:354
msgid "%1$s. See the <a href=\"%2$s\" target=\"_blank\">full list</a>."
msgstr "%1$s. Ver la <a href=\"%2$s\" target=\"_blank\">lista completa</a>."

#: includes/admin/menu-pages/shortcodes-menu-page.php:160
#: includes/admin/menu-pages/shortcodes-menu-page.php:293
#: includes/admin/menu-pages/shortcodes-menu-page.php:341
msgid "Designates the ascending or descending order of sorting."
msgstr "Designa el orden ascendente o descendente de la clasificación."

#: includes/admin/menu-pages/shortcodes-menu-page.php:162
#: includes/admin/menu-pages/shortcodes-menu-page.php:295
#: includes/admin/menu-pages/shortcodes-menu-page.php:343
msgid "ASC - from lowest to highest values (1, 2, 3). DESC - from highest to lowest values (3, 2, 1)."
msgstr "ASC: de los valores más bajos a los más altos (1, 2, 3), DESC: de los más altos a los más bajos (3, 2, 1)."

#: includes/admin/menu-pages/shortcodes-menu-page.php:166
#: includes/admin/menu-pages/shortcodes-menu-page.php:299
#: includes/admin/menu-pages/shortcodes-menu-page.php:347
#: assets/blocks/blocks.js:574
#: assets/blocks/blocks.js:910
#: assets/blocks/blocks.js:1094
msgid "Custom field name. Required if \"orderby\" is one of the \"meta_value\", \"meta_value_num\" or \"meta_value_*\"."
msgstr "Nombre de campo personalizado. Obligatorio si \"orderby\" es uno de los \"meta_value\", \"meta_value_num\" o \"meta_value_*\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:167
#: includes/admin/menu-pages/shortcodes-menu-page.php:300
#: includes/admin/menu-pages/shortcodes-menu-page.php:348
msgid "custom field name"
msgstr "nombre de campo personalizado"

#: includes/admin/menu-pages/shortcodes-menu-page.php:168
#: includes/admin/menu-pages/shortcodes-menu-page.php:177
#: includes/admin/menu-pages/shortcodes-menu-page.php:301
#: includes/admin/menu-pages/shortcodes-menu-page.php:310
#: includes/admin/menu-pages/shortcodes-menu-page.php:349
#: includes/admin/menu-pages/shortcodes-menu-page.php:358
#: includes/admin/menu-pages/shortcodes-menu-page.php:645
msgid "empty string"
msgstr "línea vacía"

#: includes/admin/menu-pages/shortcodes-menu-page.php:171
#: includes/admin/menu-pages/shortcodes-menu-page.php:304
#: includes/admin/menu-pages/shortcodes-menu-page.php:352
msgid "Specified type of the custom field. Can be used in conjunction with orderby=\"meta_value\"."
msgstr "Tipo especificado de campo personalizado. Se puede utilizar junto con orderby=\"meta_value\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:185
msgid "Sort by. Use \"orderby\" insted."
msgstr "Ordenar por. Utilizar \"orderby\" en su lugar."

#: includes/admin/menu-pages/shortcodes-menu-page.php:191
#: includes/admin/menu-pages/shortcodes-menu-page.php:248
msgid "Show Book button"
msgstr "Mostrar botón Reservar"

#: includes/admin/menu-pages/shortcodes-menu-page.php:192
#: includes/admin/menu-pages/shortcodes-menu-page.php:249
#: includes/admin/menu-pages/shortcodes-menu-page.php:417
#: assets/blocks/blocks.js:771
#: assets/blocks/blocks.js:1300
msgid "Whether to display Book button."
msgstr "Mostrar o no mostrar el botón Reservar."

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "NOTE:"
msgstr "NOTA:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
msgid "Use only on page that you set as Search Results Page in <a href=\"%s\">Settings</a>"
msgstr "Utilice sólo en la página que estableció como Página de resultados de búsqueda en <a href=\"%s\">Ajustes</a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:209
#: assets/blocks/blocks.js:642
msgid "Accommodation Types Listing"
msgstr "Lista de tipos de alojamientos"

#: includes/admin/menu-pages/shortcodes-menu-page.php:254
#: includes/admin/menu-pages/shortcodes-menu-page.php:327
#: assets/blocks/blocks.js:804
#: assets/blocks/blocks.js:1028
msgid "Count per page"
msgstr "Número por página"

#: includes/admin/menu-pages/shortcodes-menu-page.php:255
#: assets/blocks/blocks.js:805
msgid "integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "entero, -1 para mostrar todo, de forma predeterminada: \"Las páginas del blog se muestran como máximo\""

#: includes/admin/menu-pages/shortcodes-menu-page.php:264
#: assets/blocks/blocks.js:817
msgid "IDs of categories that will be shown."
msgstr "ID de las categorías que se mostrarán."

#: includes/admin/menu-pages/shortcodes-menu-page.php:265
#: includes/admin/menu-pages/shortcodes-menu-page.php:270
#: includes/admin/menu-pages/shortcodes-menu-page.php:275
#: includes/admin/menu-pages/shortcodes-menu-page.php:323
#: assets/blocks/blocks.js:1017
msgid "Comma-separated IDs."
msgstr "Números IDs separados por comas."

#: includes/admin/menu-pages/shortcodes-menu-page.php:269
#: assets/blocks/blocks.js:829
msgid "IDs of tags that will be shown."
msgstr "ID de las etiquetas que se mostrarán."

#: includes/admin/menu-pages/shortcodes-menu-page.php:274
#: assets/blocks/blocks.js:793
msgid "IDs of accommodations that will be shown."
msgstr "IDs de los alojamientos que se mostrarán."

#: includes/admin/menu-pages/shortcodes-menu-page.php:279
#: assets/blocks/blocks.js:841
msgid "Logical relationship between each taxonomy when there is more than one."
msgstr "Relación lógica entre cada taxonomía cuando hay más de una."

#: includes/admin/menu-pages/shortcodes-menu-page.php:319
#: assets/blocks/blocks.js:983
msgid "Services Listing"
msgstr "Lista de servicios"

#: includes/admin/menu-pages/shortcodes-menu-page.php:322
#: assets/blocks/blocks.js:792
msgid "IDs"
msgstr "IDs"

#: includes/admin/menu-pages/shortcodes-menu-page.php:324
#: assets/blocks/blocks.js:1016
msgid "IDs of services that will be shown. "
msgstr "IDs de los servicios que se mostrarán. "

#: includes/admin/menu-pages/shortcodes-menu-page.php:368
msgid "Show All Services"
msgstr "Mostrar todos los servicios"

#: includes/admin/menu-pages/shortcodes-menu-page.php:373
#: assets/blocks/blocks.js:1167
#: assets/blocks/blocks.js:1344
msgid "Single Accommodation Type"
msgstr "Tipo de alojamiento individual"

#: includes/admin/menu-pages/shortcodes-menu-page.php:377
msgid "ID of accommodation type to display."
msgstr "ID del tipo de alojamiento a mostrar."

#: includes/admin/menu-pages/shortcodes-menu-page.php:441
msgid "Display accommodation type with title and image."
msgstr "Mostrar el tipo de alojamiento con su título e imagen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:446
#: assets/blocks/blocks.js:1356
#: assets/blocks/blocks.js:1386
msgid "Checkout Form"
msgstr "Formulario de pago"

#: includes/admin/menu-pages/shortcodes-menu-page.php:447
#: assets/blocks/blocks.js:1357
msgid "Display checkout form."
msgstr "Mostrar formulario de pago."

#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "Use only on page that you set as Checkout Page in <a href=\"%s\">Settings</a>"
msgstr "Utilice sólo en la página que estableció como Página de pedido en <a href=\"%s\">Ajustes</a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:462
#: assets/blocks/blocks.js:1398
#: assets/blocks/blocks.js:1468
msgid "Booking Form"
msgstr "Formulario de reserva"

#: includes/admin/menu-pages/shortcodes-menu-page.php:481
msgid "Show Booking Form for Accommodation Type with id 777"
msgstr "Mostrar el formulario de reserva para el tipo de alojamiento con id 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:486
#: assets/blocks/blocks.js:1480
#: assets/blocks/blocks.js:1550
msgid "Accommodation Rates List"
msgstr "Lista de tarifas de alojamientos"

#: includes/admin/menu-pages/shortcodes-menu-page.php:490
#: assets/blocks/blocks.js:1204
msgid "ID of accommodation type."
msgstr "ID del tipo de alojamiento."

#: includes/admin/menu-pages/shortcodes-menu-page.php:505
msgid "Show Accommodation Rates List for accommodation type with id 777"
msgstr "Mostrar la lista de calificaciones de reserva para el tipo de alojamiento con id 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:511
#: assets/blocks/blocks.js:1563
msgid "Display booking and payment details."
msgstr "Muestra los detalles de la reserva y el pago."

#: includes/admin/menu-pages/shortcodes-menu-page.php:521
msgid "Use this shortcode on Booking Confirmed and Reservation Received pages"
msgstr "Utilice este código en las páginas Reserva confirmada y Reserva recibida"

#: includes/admin/menu-pages/shortcodes-menu-page.php:526
msgid "Booking Cancelation"
msgstr "Cancelación de reserva"

#: includes/admin/menu-pages/shortcodes-menu-page.php:527
msgid "Display booking cancelation details."
msgstr "Mostrar los detalles de la cancelación de la reserva."

#: includes/admin/menu-pages/shortcodes-menu-page.php:537
msgid "Use this shortcode on the Booking Cancelation page"
msgstr "Utiliza este código corto en la página de cancelación de reserva"

#: includes/admin/menu-pages/shortcodes-menu-page.php:542
msgid "Customer Account"
msgstr "Cuenta del cliente"

#: includes/admin/menu-pages/shortcodes-menu-page.php:543
msgid "Display log in form or customer account area."
msgstr "Mostrar el formulario de inicio de sesión o área de cuenta del cliente."

#: includes/admin/menu-pages/shortcodes-menu-page.php:553
msgid "Use this shortcode to create the My Account page."
msgstr "Utiliza este código corto para crear la página Mi cuenta."

#: includes/admin/menu-pages/shortcodes-menu-page.php:565
#: includes/admin/menu-pages/shortcodes-menu-page.php:699
#: includes/admin/menu-pages/shortcodes-menu-page.php:703
msgid "Shortcodes"
msgstr "Códigos cortos"

#: includes/admin/menu-pages/shortcodes-menu-page.php:569
msgid "Shortcode"
msgstr "Código corto"

#: includes/admin/menu-pages/shortcodes-menu-page.php:570
#: includes/post-types/attributes-cpt.php:307
msgid "Parameters"
msgstr "Parámetros"

#: includes/admin/menu-pages/shortcodes-menu-page.php:571
msgid "Example"
msgstr "Ejemplo"

#: includes/admin/menu-pages/shortcodes-menu-page.php:603
#: includes/admin/menu-pages/shortcodes-menu-page.php:625
msgid "Deprecated since %s"
msgstr "Obsoleto desde %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:635
msgid "Values:"
msgstr "Valores:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:640
msgid "Default:"
msgstr "Predeterminado:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:687
msgid "Optional."
msgstr "Opcional."

#: includes/admin/menu-pages/shortcodes-menu-page.php:695
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:30
#: includes/payments/gateways/gateway.php:365
#: includes/views/shortcodes/checkout-view.php:247
#: includes/views/shortcodes/checkout-view.php:270
#: includes/views/shortcodes/checkout-view.php:538
#: includes/views/shortcodes/checkout-view.php:572
#: templates/account/account-details.php:34
msgid "Required"
msgstr "Obligatorio"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:34
msgid "Taxes and fees saved."
msgstr "Impuestos y cargos guardados"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:41
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:459
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:463
msgid "Taxes & Fees"
msgstr "Impuestos y Cargos"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:136
#: includes/csv/bookings/bookings-exporter-helper.php:102
#: includes/views/booking-view.php:296
msgid "Fees"
msgstr "Cargos"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:137
msgid "No fees have been created yet."
msgstr "No se han creado cargos aún"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:138
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:234
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:321
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:392
#: includes/post-types/booking-cpt.php:219
msgid "Add new"
msgstr "Añadir nuevo"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:146
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:242
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:329
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:400
msgid "Label"
msgstr "Etiqueta"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:147
msgid "New fee"
msgstr "Nuevo cargo"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:158
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:254
msgid "Per guest / per day"
msgstr "Por húesped / por día"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:159
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:255
msgid "Per accommodation / per day"
msgstr "Por alojamiento / por día"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:160
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:256
msgid "Per accommodation (%)"
msgstr "Por Alojamiento (%)"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:182
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:278
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:364
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:435
msgid "Limit"
msgstr "Límite"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:183
msgid "How often this fee is charged. Set 0 to charge each day of the stay period. Set 1 to charge once."
msgstr "Con qué frecuencia se cobra esta tarifa. Establece 0 para cobrar cada día del periodo de estancia. Establece 1 para que se cobre una vez."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:197
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:200
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:293
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:296
msgid "Include"
msgstr "Incluir"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:198
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:294
msgid "Show accommodation rate with this charge included"
msgstr "Mostrar tarifa de alojamiento con este cargo incluido"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:232
#: includes/csv/bookings/bookings-exporter-helper.php:95
#: includes/views/booking-view.php:171
msgid "Accommodation Taxes"
msgstr "Tarifas de alojamiento:"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:233
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:320
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:391
msgid "No taxes have been created yet."
msgstr "Noi se han creado impuestos aún"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:243
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:330
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:401
msgid "New tax"
msgstr "Nuevo impuesto"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:279
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:365
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:436
msgid "Limit of days the fee is charged. Set 0 to charge each day of stay period. Set 1 to charge once."
msgstr "Límite de días en los que se cargará la tarifa. Establece 0 para cobrar cada día de estancia. Establece 1 para cobrar una vez."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:319
#: includes/views/booking-view.php:265
msgid "Service Taxes"
msgstr "Impuesto de servicios"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:341
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:412
#: includes/post-types/coupon-cpt.php:114
#: includes/post-types/coupon-cpt.php:159
msgid "Percentage"
msgstr "Porcentaje"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:390
#: includes/views/booking-view.php:349
msgid "Fee Taxes"
msgstr "Impuestos gratis"

#: includes/admin/room-list-table.php:95
msgid "External Calendars"
msgstr "Calendarios externos"

#: includes/admin/room-list-table.php:157
#: includes/admin/room-list-table.php:211
msgid "Sync External Calendars"
msgstr "Sincronizar calendarios externos"

#: includes/admin/room-list-table.php:163
#: includes/admin/sync-rooms-list-table.php:65
msgctxt "Placeholder for empty accommodation title"
msgid "(no title)"
msgstr "(sin titulo)"

#: includes/admin/room-list-table.php:185
msgid "Download Calendar"
msgstr "Descargar calendario"

#: includes/admin/sync-logs-list-table.php:73
msgid "Message"
msgstr "Mensaje"

#: includes/admin/sync-logs-list-table.php:82
msgid "Success"
msgstr "Acción correcta"

#: includes/admin/sync-logs-list-table.php:85
msgid "Info"
msgstr "Información"

#: includes/admin/sync-logs-list-table.php:88
msgid "Warning"
msgstr "Advertencia"

#: includes/admin/sync-rooms-list-table.php:71
msgctxt "This is date and time format 31/12/2017 - 23:59:59"
msgid "d/m/Y - H:i:s"
msgstr "d/m/Y - H:i:s"

#: includes/admin/sync-rooms-list-table.php:75
#: includes/ajax.php:945
msgid "Waiting"
msgstr "Esperando"

#: includes/admin/sync-rooms-list-table.php:78
#: includes/ajax.php:948
msgid "Processing"
msgstr "Procesando"

#: includes/admin/sync-rooms-list-table.php:128
msgctxt "Total number of processed bookings"
msgid "Total"
msgstr "Total"

#: includes/admin/sync-rooms-list-table.php:129
msgid "Succeed"
msgstr "Exitoso"

#: includes/admin/sync-rooms-list-table.php:130
msgid "Skipped"
msgstr "Omitidos"

#: includes/admin/sync-rooms-list-table.php:131
msgid "Failed"
msgstr "Erróneo"

#: includes/admin/sync-rooms-list-table.php:132
msgid "Removed"
msgstr "Eliminado"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:58
msgid "Copying to clipboard failed. Please press Ctrl/Cmd+C to copy."
msgstr "Error al copiar en el portapapeles. Por favor, pulsa Ctrl/Cmd+C para copiar."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:80
msgid "Description is missing."
msgstr "Falta la descripción."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:83
msgid "User is missing."
msgstr "Falta el usuario."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:86
msgid "Permission is missing."
msgstr "Falta el permiso."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:102
msgid "You do not have permission to assign API Keys to the selected user."
msgstr "No tienes permiso para asignar claves API al usuario seleccionado."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:132
msgid "API Key updated successfully."
msgstr "Clave de API actualizada correctamente."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:164
msgid "API Key generated successfully. Make sure to copy your new keys now as the secret key will be hidden once you leave this page."
msgstr "Clave API generada correctamente. Asegúrate de copiar tus nuevas claves ahora porque la clave secreta se ocultará una vez que salgas de esta página."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:176
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:116
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:119
msgid "Revoke key"
msgstr "Revocar clave"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:46
msgid "No keys found."
msgstr "No se ha encontrado ninguna clave."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:57
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:22
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:95
#: includes/payments/gateways/gateway.php:504
#: includes/post-types/coupon-cpt.php:37
#: includes/post-types/rate-cpt.php:81
msgid "Description"
msgstr "Descripción"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:58
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:81
msgid "Consumer key ending in"
msgstr "Clave de consumidor que termina en"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:59
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:36
msgid "User"
msgstr "Usuario"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:60
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:55
msgid "Permissions"
msgstr "Permisos"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:61
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:89
msgid "Last access"
msgstr "Último acceso"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:99
msgid "API key"
msgstr "Clave API"

#. translators: %d: API key ID.
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:111
msgid "ID: %d"
msgstr "ID: %d"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:126
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:227
msgid "Revoke"
msgstr "Revocar"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:182
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:65
msgid "Read"
msgstr "Lectura"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:183
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:66
msgid "Write"
msgstr "Escritura"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:184
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:67
msgid "Read/Write"
msgstr "Lectura/escritura"

#. translators: 1: last access date 2: last access time
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:205
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:96
msgid "%1$s at %2$s"
msgstr "%1$s a las %2$s"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:213
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:100
msgid "Unknown"
msgstr "Desconocido"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:68
msgid "You do not have permission to edit this API Key"
msgstr "No tienes permiso para editar esta clave API"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:89
msgid "REST API"
msgstr "API de REST"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:91
msgid "Add key"
msgstr "Añadir clave"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:176
msgid "You do not have permission to revoke this API Key"
msgstr "No tienes permiso para revocar esta clave API"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:191
msgid "You do not have permission to edit API Keys"
msgstr "No tienes permiso para editar claves API"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:211
msgid "You do not have permission to revoke API Keys"
msgstr "No tienes permiso para revocar claves API"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:13
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:114
msgid "Generate API key"
msgstr "Generar clave API"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:25
msgid "Friendly name for identifying this key."
msgstr "Nombre amigable para identificar esta clave."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:39
msgid "Owner of these keys."
msgstr "Propietario de estas claves."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:58
msgid "Access type of these keys."
msgstr "Tipo de acceso de estas claves."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:148
msgid "Consumer key"
msgstr "Clave del consumidor"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copied!"
msgstr "¡Copiado!"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copy"
msgstr "Copiar"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:156
msgid "Consumer secret"
msgstr "Secreto del consumidor"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:164
msgid "QR Code"
msgstr "Código QR"

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:33
#: includes/ajax-api/ajax-actions/update-booking-notes.php:52
#: includes/ajax.php:344
#: includes/ajax.php:388
#: includes/csv/bookings/bookings-query.php:85
msgid "Please complete all required fields and try again."
msgstr "Por favor, llene todos los campos obligatorios e inténtelo de nuevo."

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:55
msgid "Sorry, the minimum allowed payment amount is %s to use this payment method."
msgstr "Lo sentimos, pero el pago mínimo permitido de este método de pago es %s."

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:60
#: includes/post-types/booking-cpt.php:35
msgid "Booking Information"
msgstr "Información sobre reserva"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:68
#: includes/emails/templaters/email-templater.php:136
#: includes/post-types/booking-cpt.php:51
#: template-functions.php:706
#: template-functions.php:710
#: templates/create-booking/search/search-form.php:53
#: templates/edit-booking/edit-dates.php:33
#: templates/shortcodes/search/search-form.php:43
#: templates/widgets/search-availability/search-form.php:43
#: assets/blocks/blocks.js:177
msgid "Check-in Date"
msgstr "Fecha de llegada"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:72
#: includes/emails/templaters/email-templater.php:140
#: includes/post-types/booking-cpt.php:60
#: template-functions.php:715
#: template-functions.php:719
#: templates/create-booking/search/search-form.php:73
#: templates/edit-booking/edit-dates.php:42
#: templates/shortcodes/search/search-form.php:63
#: templates/widgets/search-availability/search-form.php:62
#: assets/blocks/blocks.js:189
msgid "Check-out Date"
msgstr "Fecha de salida"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:91
msgid "Summary"
msgstr "Resumen"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:99
msgid "Source"
msgstr "Fuente"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:110
#: includes/post-types/booking-cpt.php:83
#: templates/emails/customer-approved-booking.php:31
#: templates/emails/customer-cancelled-booking.php:29
#: templates/emails/customer-confirmation-booking.php:35
#: templates/emails/customer-pending-booking.php:32
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:30
msgid "Customer Information"
msgstr "Información sobre cliente"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:150
#: includes/csv/bookings/bookings-exporter-helper.php:90
#: includes/emails/templaters/email-templater.php:189
#: includes/post-types/booking-cpt.php:164
msgid "Customer Note"
msgstr "Nota de cliente"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:156
#: includes/post-types/booking-cpt.php:171
msgid "Additional Information"
msgstr "Información adicional"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:160
#: includes/csv/bookings/bookings-exporter-helper.php:107
#: includes/post-types/booking-cpt.php:178
#: includes/post-types/coupon-cpt.php:289
msgid "Coupon"
msgstr "Cupón"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:164
#: includes/post-types/booking-cpt.php:187
msgid "Total Booking Price"
msgstr "Precio total de reserva"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:173
#: includes/bundles/customer-bundle.php:173
#: includes/post-types/booking-cpt.php:201
#: includes/views/shortcodes/checkout-view.php:735
msgid "Notes"
msgstr "Notas"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:192
msgid "%1$s on %2$s"
msgstr "%1$s en %2$s"

#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:185
#: template-functions.php:88
msgid "Based on your search parameters"
msgstr "A la base de sus parámetros de búsqueda"

#: includes/ajax.php:245
msgid "No bookings found for your request."
msgstr "No se encontraron reservas para su solicitud."

#: includes/ajax.php:252
msgid "Uploads directory is not writable."
msgstr "El directorio de subida no permite la escritura."

#: includes/ajax.php:314
msgid "No enough data"
msgstr "No hay suficientes datos"

#: includes/ajax.php:332
#: includes/script-managers/admin-script-manager.php:94
msgid "An error has occurred"
msgstr "Se ha producido un error"

#: includes/ajax.php:372
#: includes/script-managers/public-script-manager.php:199
msgid "An error has occurred, please try again later."
msgstr "Se ha producido un error. Vuelve a intentarlo más tarde."

#: includes/ajax.php:473
msgid "The number of adults is not valid."
msgstr "El número de adultos no es válido."

#: includes/ajax.php:477
msgid "The number of guests is not valid."
msgstr "El número de invitados no es válido."

#: includes/ajax.php:519
#: includes/ajax.php:593
msgid "An error has occurred. Please try again later."
msgstr "Se ha producido un error. Vuelve a intentarlo más tarde."

#: includes/ajax.php:750
msgid "Chosen payment method is not available. Please refresh the page and try one more time."
msgstr "El método de pago elegido no está disponible. Actualice la página y pruebe una vez más."

#: includes/ajax.php:832
msgid "Coupon applied successfully."
msgstr "Cupón aplicado con éxito."

#: includes/ajax.php:838
#: includes/entities/coupon.php:366
msgid "Coupon is not valid."
msgstr "Cupón no válido."

#: includes/ajax.php:1046
msgid "You do not have permission to do this action."
msgstr "No tienes permiso para realizar esta acción."

#: includes/attribute-functions.php:164
#: includes/post-types/attributes-cpt.php:137
#: includes/post-types/attributes-cpt.php:149
#: includes/post-types/attributes-cpt.php:345
msgctxt "Not selected value in the search form."
msgid "&mdash;"
msgstr "&mdash;"

#: includes/bookings-calendar.php:577
msgid "Year"
msgstr "Año"

#: includes/bookings-calendar.php:578
#: includes/post-types/attributes-cpt.php:298
#: includes/reports/report-filters.php:94
msgid "Custom"
msgstr "Predeterminado"

#: includes/bookings-calendar.php:608
#: includes/post-types/booking-cpt/statuses.php:102
#: includes/reports/data/report-earnings-by-dates-data.php:28
msgctxt "Booking status"
msgid "Confirmed"
msgstr "Confirmada"

#: includes/bookings-calendar.php:645
#: includes/reports/abstract-report.php:46
#: includes/reports/earnings-report.php:361
msgid "Show"
msgstr "Mostrar"

#: includes/bookings-calendar.php:649
#: templates/create-booking/search/search-form.php:131
#: templates/shortcodes/search/search-form.php:138
#: templates/widgets/search-availability/search-form.php:142
msgid "Search"
msgstr "Buscar"

#: includes/bookings-calendar.php:652
#: includes/bookings-calendar.php:654
#: includes/bookings-calendar.php:697
#: includes/script-managers/public-script-manager.php:200
msgid "Booked"
msgstr "Reservado"

#: includes/bookings-calendar.php:657
#: includes/bookings-calendar.php:659
#: includes/bookings-calendar.php:698
#: includes/script-managers/public-script-manager.php:202
msgid "Pending"
msgstr "Pendiente"

#: includes/bookings-calendar.php:662
#: includes/bookings-calendar.php:664
msgid "External"
msgstr "Externo"

#: includes/bookings-calendar.php:667
#: includes/bookings-calendar.php:669
#: includes/bookings-calendar.php:1133
msgid "Blocked"
msgstr "Bloqueado"

#: includes/bookings-calendar.php:687
msgid "Search results for accommodations that have bookings with status \"%s\" from %s until %s"
msgstr "Buscar entre los alojamientos que tienen el estado de reserva \"%s\" de %s  a %s"

#: includes/bookings-calendar.php:696
msgid "Free"
msgstr "Gratis"

#: includes/bookings-calendar.php:699
msgid "Locked (Booked or Pending)"
msgstr "Bloqueado (reservado o pendiente)"

#: includes/bookings-calendar.php:729
#: includes/bookings-calendar.php:819
msgid "Until"
msgstr "Hasta"

#: includes/bookings-calendar.php:775
msgid "Period:"
msgstr "Período:"

#: includes/bookings-calendar.php:782
msgid "&lt; Prev"
msgstr "&lt; Anterior"

#: includes/bookings-calendar.php:799
msgid "Next &gt;"
msgstr "Siguiente &gt;"

#: includes/bookings-calendar.php:874
msgid "No accommodations found."
msgstr "No se encontraron alojamientos."

#: includes/bookings-calendar.php:1123
msgid "Check-out #%d"
msgstr "Día de salida #%d"

#: includes/bookings-calendar.php:1127
msgid "Check-in #%d"
msgstr "Día de llegada #%d"

#: includes/bookings-calendar.php:1131
msgid "Booking #%d"
msgstr "Reserva #%d"

#: includes/bookings-calendar.php:1136
#: includes/bookings-calendar.php:1140
#: includes/script-managers/public-script-manager.php:201
msgid "Buffer time."
msgstr "Tiempo extra entre servicios."

#: includes/bookings-calendar.php:1143
msgctxt "Availability"
msgid "Free"
msgstr "Disponible"

#: includes/bookings-calendar.php:1172
#: templates/emails/reserved-room-details.php:15
msgid "Adults: %s"
msgstr "Adultos: %s"

#: includes/bookings-calendar.php:1176
#: templates/emails/reserved-room-details.php:17
msgid "Children: %s"
msgstr "Niños: %s"

#: includes/bookings-calendar.php:1183
msgid "Booking imported with UID %s."
msgstr "Reserva importada con UID %s."

#: includes/bookings-calendar.php:1185
msgid "Imported booking."
msgstr "Reserva importada."

#: includes/bookings-calendar.php:1193
msgid "Description: %s."
msgstr "Descripción: %s."

#: includes/bookings-calendar.php:1197
msgid "Source: %s."
msgstr "Fuente: %s."

#: includes/bundles/countries-bundle.php:16
msgid "Afghanistan"
msgstr "Afganistán"

#: includes/bundles/countries-bundle.php:17
msgid "&#197;land Islands"
msgstr "Islas Åland"

#: includes/bundles/countries-bundle.php:18
msgid "Albania"
msgstr "Albania"

#: includes/bundles/countries-bundle.php:19
msgid "Algeria"
msgstr "Argelia"

#: includes/bundles/countries-bundle.php:20
msgid "American Samoa"
msgstr "Samoa Americana"

#: includes/bundles/countries-bundle.php:21
msgid "Andorra"
msgstr "Andorra"

#: includes/bundles/countries-bundle.php:22
msgid "Angola"
msgstr "Angola"

#: includes/bundles/countries-bundle.php:23
msgid "Anguilla"
msgstr "Anguila"

#: includes/bundles/countries-bundle.php:24
msgid "Antarctica"
msgstr "Antártida"

#: includes/bundles/countries-bundle.php:25
msgid "Antigua and Barbuda"
msgstr "Antigua y Barbuda"

#: includes/bundles/countries-bundle.php:26
msgid "Argentina"
msgstr "Argentina"

#: includes/bundles/countries-bundle.php:27
msgid "Armenia"
msgstr "Armenia"

#: includes/bundles/countries-bundle.php:28
msgid "Aruba"
msgstr "Aruba"

#: includes/bundles/countries-bundle.php:29
msgid "Australia"
msgstr "Australia"

#: includes/bundles/countries-bundle.php:30
msgid "Austria"
msgstr "Austria"

#: includes/bundles/countries-bundle.php:31
msgid "Azerbaijan"
msgstr "Azerbaiyán"

#: includes/bundles/countries-bundle.php:32
msgid "Bahamas"
msgstr "Bahamas"

#: includes/bundles/countries-bundle.php:33
msgid "Bahrain"
msgstr "Baréin"

#: includes/bundles/countries-bundle.php:34
msgid "Bangladesh"
msgstr "Bangladés"

#: includes/bundles/countries-bundle.php:35
msgid "Barbados"
msgstr "Barbados"

#: includes/bundles/countries-bundle.php:36
msgid "Belarus"
msgstr "Belarús"

#: includes/bundles/countries-bundle.php:37
msgid "Belgium"
msgstr "Bélgica"

#: includes/bundles/countries-bundle.php:38
msgid "Belau"
msgstr "Belau"

#: includes/bundles/countries-bundle.php:39
msgid "Belize"
msgstr "Belice"

#: includes/bundles/countries-bundle.php:40
msgid "Benin"
msgstr "Benín"

#: includes/bundles/countries-bundle.php:41
msgid "Bermuda"
msgstr "Bermudas"

#: includes/bundles/countries-bundle.php:42
msgid "Bhutan"
msgstr "Bután"

#: includes/bundles/countries-bundle.php:43
msgid "Bolivia"
msgstr "Bolivia"

#: includes/bundles/countries-bundle.php:44
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Islas BES"

#: includes/bundles/countries-bundle.php:45
msgid "Bosnia and Herzegovina"
msgstr "Bosnia y Herzegovina"

#: includes/bundles/countries-bundle.php:46
msgid "Botswana"
msgstr "Botsuana"

#: includes/bundles/countries-bundle.php:47
msgid "Bouvet Island"
msgstr "Isla Bouvet"

#: includes/bundles/countries-bundle.php:48
msgid "Brazil"
msgstr "Brasil"

#: includes/bundles/countries-bundle.php:49
msgid "British Indian Ocean Territory"
msgstr "Territorio Británico del Océano Índico"

#: includes/bundles/countries-bundle.php:50
msgid "British Virgin Islands"
msgstr "Islas Vírgenes Británicas"

#: includes/bundles/countries-bundle.php:51
msgid "Brunei"
msgstr "Brunéi"

#: includes/bundles/countries-bundle.php:52
msgid "Bulgaria"
msgstr "Bulgaria"

#: includes/bundles/countries-bundle.php:53
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: includes/bundles/countries-bundle.php:54
msgid "Burundi"
msgstr "Burundi"

#: includes/bundles/countries-bundle.php:55
msgid "Cambodia"
msgstr "Camboya"

#: includes/bundles/countries-bundle.php:56
msgid "Cameroon"
msgstr "Camerún"

#: includes/bundles/countries-bundle.php:57
msgid "Canada"
msgstr "Canadá"

#: includes/bundles/countries-bundle.php:58
msgid "Cape Verde"
msgstr "Cabo Verde"

#: includes/bundles/countries-bundle.php:59
msgid "Cayman Islands"
msgstr "Islas Caimán"

#: includes/bundles/countries-bundle.php:60
msgid "Central African Republic"
msgstr "República Centroafricana"

#: includes/bundles/countries-bundle.php:61
msgid "Chad"
msgstr "Chad"

#: includes/bundles/countries-bundle.php:62
msgid "Chile"
msgstr "Chile"

#: includes/bundles/countries-bundle.php:63
msgid "China"
msgstr "China"

#: includes/bundles/countries-bundle.php:64
msgid "Christmas Island"
msgstr "Isla de Navidad"

#: includes/bundles/countries-bundle.php:65
msgid "Cocos (Keeling) Islands"
msgstr "Islas Cocos o Islas Keeling"

#: includes/bundles/countries-bundle.php:66
msgid "Colombia"
msgstr "Colombia"

#: includes/bundles/countries-bundle.php:67
msgid "Comoros"
msgstr "Comoras"

#: includes/bundles/countries-bundle.php:68
msgid "Congo (Brazzaville)"
msgstr "Congo (Brazzaville)"

#: includes/bundles/countries-bundle.php:69
msgid "Congo (Kinshasa)"
msgstr "Congo (Kinshasa)"

#: includes/bundles/countries-bundle.php:70
msgid "Cook Islands"
msgstr "Islas Cook"

#: includes/bundles/countries-bundle.php:71
msgid "Costa Rica"
msgstr "Costa Rica"

#: includes/bundles/countries-bundle.php:72
msgid "Croatia"
msgstr "Croacia"

#: includes/bundles/countries-bundle.php:73
msgid "Cuba"
msgstr "Cuba"

#: includes/bundles/countries-bundle.php:74
msgid "Cura&ccedil;ao"
msgstr "Curazao"

#: includes/bundles/countries-bundle.php:75
msgid "Cyprus"
msgstr "Chipre"

#: includes/bundles/countries-bundle.php:76
msgid "Czech Republic"
msgstr "Chequia"

#: includes/bundles/countries-bundle.php:77
msgid "Denmark"
msgstr "Dinamarca"

#: includes/bundles/countries-bundle.php:78
msgid "Djibouti"
msgstr "Yibuti"

#: includes/bundles/countries-bundle.php:79
msgid "Dominica"
msgstr "Dominica"

#: includes/bundles/countries-bundle.php:80
msgid "Dominican Republic"
msgstr "República Dominicana"

#: includes/bundles/countries-bundle.php:81
msgid "Ecuador"
msgstr "Ecuador"

#: includes/bundles/countries-bundle.php:82
msgid "Egypt"
msgstr "Egipto"

#: includes/bundles/countries-bundle.php:83
msgid "El Salvador"
msgstr "El Salvador"

#: includes/bundles/countries-bundle.php:84
msgid "Equatorial Guinea"
msgstr "Guinea Ecuatorial"

#: includes/bundles/countries-bundle.php:85
msgid "Eritrea"
msgstr "Eritrea"

#: includes/bundles/countries-bundle.php:86
msgid "Estonia"
msgstr "Estonia"

#: includes/bundles/countries-bundle.php:87
msgid "Ethiopia"
msgstr "Etiopía"

#: includes/bundles/countries-bundle.php:88
msgid "Falkland Islands"
msgstr "Islas Malvinas"

#: includes/bundles/countries-bundle.php:89
msgid "Faroe Islands"
msgstr "Islas Feroe"

#: includes/bundles/countries-bundle.php:90
msgid "Fiji"
msgstr "Fiyi"

#: includes/bundles/countries-bundle.php:91
msgid "Finland"
msgstr "Finlandia"

#: includes/bundles/countries-bundle.php:92
msgid "France"
msgstr "Francia"

#: includes/bundles/countries-bundle.php:93
msgid "French Guiana"
msgstr "Guayana Francesa"

#: includes/bundles/countries-bundle.php:94
msgid "French Polynesia"
msgstr "Polinesia Francesa"

#: includes/bundles/countries-bundle.php:95
msgid "French Southern Territories"
msgstr "Tierras Australes Francesas"

#: includes/bundles/countries-bundle.php:96
msgid "Gabon"
msgstr "Gabón"

#: includes/bundles/countries-bundle.php:97
msgid "Gambia"
msgstr "Gambia"

#: includes/bundles/countries-bundle.php:98
msgid "Georgia"
msgstr "Georgia"

#: includes/bundles/countries-bundle.php:99
msgid "Germany"
msgstr "Alemania"

#: includes/bundles/countries-bundle.php:100
msgid "Ghana"
msgstr "Ghana"

#: includes/bundles/countries-bundle.php:101
msgid "Gibraltar"
msgstr "Gibraltar"

#: includes/bundles/countries-bundle.php:102
msgid "Greece"
msgstr "Grecia"

#: includes/bundles/countries-bundle.php:103
msgid "Greenland"
msgstr "Groenlandia"

#: includes/bundles/countries-bundle.php:104
msgid "Grenada"
msgstr "Granada"

#: includes/bundles/countries-bundle.php:105
msgid "Guadeloupe"
msgstr "Guadalupe"

#: includes/bundles/countries-bundle.php:106
msgid "Guam"
msgstr "Guam"

#: includes/bundles/countries-bundle.php:107
msgid "Guatemala"
msgstr "Guatemala"

#: includes/bundles/countries-bundle.php:108
msgid "Guernsey"
msgstr "Guernsey"

#: includes/bundles/countries-bundle.php:109
msgid "Guinea"
msgstr "Guinea"

#: includes/bundles/countries-bundle.php:110
msgid "Guinea-Bissau"
msgstr "Guinea-Bisáu"

#: includes/bundles/countries-bundle.php:111
msgid "Guyana"
msgstr "Guyana"

#: includes/bundles/countries-bundle.php:112
msgid "Haiti"
msgstr "Haití"

#: includes/bundles/countries-bundle.php:113
msgid "Heard Island and McDonald Islands"
msgstr "Islas Heard y McDonald"

#: includes/bundles/countries-bundle.php:114
msgid "Honduras"
msgstr "Honduras"

#: includes/bundles/countries-bundle.php:115
msgid "Hong Kong"
msgstr "Hong Kong"

#: includes/bundles/countries-bundle.php:116
msgid "Hungary"
msgstr "Hungría"

#: includes/bundles/countries-bundle.php:117
msgid "Iceland"
msgstr "Islandia"

#: includes/bundles/countries-bundle.php:118
msgid "India"
msgstr "India"

#: includes/bundles/countries-bundle.php:119
msgid "Indonesia"
msgstr "Indonesia"

#: includes/bundles/countries-bundle.php:120
msgid "Iran"
msgstr "Irán"

#: includes/bundles/countries-bundle.php:121
msgid "Iraq"
msgstr "Irak"

#: includes/bundles/countries-bundle.php:122
msgid "Ireland"
msgstr "Irlanda"

#: includes/bundles/countries-bundle.php:123
msgid "Isle of Man"
msgstr "Isla de Man"

#: includes/bundles/countries-bundle.php:124
msgid "Israel"
msgstr "Israel"

#: includes/bundles/countries-bundle.php:125
msgid "Italy"
msgstr "Italia"

#: includes/bundles/countries-bundle.php:126
msgid "Ivory Coast"
msgstr "Costa de Marfil"

#: includes/bundles/countries-bundle.php:127
msgid "Jamaica"
msgstr "Jamaica"

#: includes/bundles/countries-bundle.php:128
msgid "Japan"
msgstr "Japón"

#: includes/bundles/countries-bundle.php:129
msgid "Jersey"
msgstr "Jersey"

#: includes/bundles/countries-bundle.php:130
msgid "Jordan"
msgstr "Jordania"

#: includes/bundles/countries-bundle.php:131
msgid "Kazakhstan"
msgstr "Kazajistán"

#: includes/bundles/countries-bundle.php:132
msgid "Kenya"
msgstr "Kenia"

#: includes/bundles/countries-bundle.php:133
msgid "Kiribati"
msgstr "Kiribati"

#: includes/bundles/countries-bundle.php:134
msgid "Kuwait"
msgstr "Kuwait"

#: includes/bundles/countries-bundle.php:135
msgid "Kyrgyzstan"
msgstr "Kirguistán"

#: includes/bundles/countries-bundle.php:136
msgid "Laos"
msgstr "Laos"

#: includes/bundles/countries-bundle.php:137
msgid "Latvia"
msgstr "Letonia"

#: includes/bundles/countries-bundle.php:138
msgid "Lebanon"
msgstr "Líbano"

#: includes/bundles/countries-bundle.php:139
msgid "Lesotho"
msgstr "Lesoto"

#: includes/bundles/countries-bundle.php:140
msgid "Liberia"
msgstr "Liberia"

#: includes/bundles/countries-bundle.php:141
msgid "Libya"
msgstr "Libia"

#: includes/bundles/countries-bundle.php:142
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: includes/bundles/countries-bundle.php:143
msgid "Lithuania"
msgstr "Lituania"

#: includes/bundles/countries-bundle.php:144
msgid "Luxembourg"
msgstr "Luxemburgo"

#: includes/bundles/countries-bundle.php:145
msgid "Macao S.A.R., China"
msgstr "Región Administrativa Especial de Macao"

#: includes/bundles/countries-bundle.php:146
msgid "Macedonia"
msgstr "Macedonia"

#: includes/bundles/countries-bundle.php:147
msgid "Madagascar"
msgstr "Madagascar"

#: includes/bundles/countries-bundle.php:148
msgid "Malawi"
msgstr "Malaui"

#: includes/bundles/countries-bundle.php:149
msgid "Malaysia"
msgstr "Malasia"

#: includes/bundles/countries-bundle.php:150
msgid "Maldives"
msgstr "Maldivas"

#: includes/bundles/countries-bundle.php:151
msgid "Mali"
msgstr "Malí"

#: includes/bundles/countries-bundle.php:152
msgid "Malta"
msgstr "Malta"

#: includes/bundles/countries-bundle.php:153
msgid "Marshall Islands"
msgstr "Islas Marshall"

#: includes/bundles/countries-bundle.php:154
msgid "Martinique"
msgstr "Martinica"

#: includes/bundles/countries-bundle.php:155
msgid "Mauritania"
msgstr "Mauritania"

#: includes/bundles/countries-bundle.php:156
msgid "Mauritius"
msgstr "Mauricio"

#: includes/bundles/countries-bundle.php:157
msgid "Mayotte"
msgstr "Mayotte"

#: includes/bundles/countries-bundle.php:158
msgid "Mexico"
msgstr "México"

#: includes/bundles/countries-bundle.php:159
msgid "Micronesia"
msgstr "Micronesia"

#: includes/bundles/countries-bundle.php:160
msgid "Moldova"
msgstr "Moldavia"

#: includes/bundles/countries-bundle.php:161
msgid "Monaco"
msgstr "Mónaco"

#: includes/bundles/countries-bundle.php:162
msgid "Mongolia"
msgstr "Mongolia"

#: includes/bundles/countries-bundle.php:163
msgid "Montenegro"
msgstr "Montenegro"

#: includes/bundles/countries-bundle.php:164
msgid "Montserrat"
msgstr "Montserrat"

#: includes/bundles/countries-bundle.php:165
msgid "Morocco"
msgstr "Marruecos"

#: includes/bundles/countries-bundle.php:166
msgid "Mozambique"
msgstr "Mozambique"

#: includes/bundles/countries-bundle.php:167
msgid "Myanmar"
msgstr "Myanmar"

#: includes/bundles/countries-bundle.php:168
msgid "Namibia"
msgstr "Namibia"

#: includes/bundles/countries-bundle.php:169
msgid "Nauru"
msgstr "Nauru"

#: includes/bundles/countries-bundle.php:170
msgid "Nepal"
msgstr "Nepal"

#: includes/bundles/countries-bundle.php:171
msgid "Netherlands"
msgstr "Países Bajos"

#: includes/bundles/countries-bundle.php:172
msgid "New Caledonia"
msgstr "Nueva Caledonia"

#: includes/bundles/countries-bundle.php:173
msgid "New Zealand"
msgstr "Nueva Zelanda"

#: includes/bundles/countries-bundle.php:174
msgid "Nicaragua"
msgstr "Nicaragua"

#: includes/bundles/countries-bundle.php:175
msgid "Niger"
msgstr "Níger"

#: includes/bundles/countries-bundle.php:176
msgid "Nigeria"
msgstr "Nigeria"

#: includes/bundles/countries-bundle.php:177
msgid "Niue"
msgstr "Niue"

#: includes/bundles/countries-bundle.php:178
msgid "Norfolk Island"
msgstr "Isla Norfolk"

#: includes/bundles/countries-bundle.php:179
msgid "Northern Mariana Islands"
msgstr "Islas Marianas del Norte"

#: includes/bundles/countries-bundle.php:180
msgid "North Korea"
msgstr "República de Corea"

#: includes/bundles/countries-bundle.php:181
msgid "Norway"
msgstr "Noruega"

#: includes/bundles/countries-bundle.php:182
msgid "Oman"
msgstr "Omán"

#: includes/bundles/countries-bundle.php:183
msgid "Pakistan"
msgstr "Pakistán"

#: includes/bundles/countries-bundle.php:184
msgid "Palestinian Territory"
msgstr "Territorios Palestinos"

#: includes/bundles/countries-bundle.php:185
msgid "Panama"
msgstr "Panamá"

#: includes/bundles/countries-bundle.php:186
msgid "Papua New Guinea"
msgstr "Papúa Nueva Guinea"

#: includes/bundles/countries-bundle.php:187
msgid "Paraguay"
msgstr "Paraguay"

#: includes/bundles/countries-bundle.php:188
#: includes/settings/main-settings.php:37
msgid "Peru"
msgstr "Peru"

#: includes/bundles/countries-bundle.php:189
msgid "Philippines"
msgstr "Filipinas"

#: includes/bundles/countries-bundle.php:190
msgid "Pitcairn"
msgstr "Islas Pitcairn"

#: includes/bundles/countries-bundle.php:191
msgid "Poland"
msgstr "Polonia"

#: includes/bundles/countries-bundle.php:192
msgid "Portugal"
msgstr "Portugal"

#: includes/bundles/countries-bundle.php:193
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: includes/bundles/countries-bundle.php:194
msgid "Qatar"
msgstr "Catar"

#: includes/bundles/countries-bundle.php:195
msgid "Reunion"
msgstr "Reunión"

#: includes/bundles/countries-bundle.php:196
msgid "Romania"
msgstr "Rumania"

#: includes/bundles/countries-bundle.php:197
msgid "Russia"
msgstr "Rusia"

#: includes/bundles/countries-bundle.php:198
msgid "Rwanda"
msgstr "Ruanda"

#: includes/bundles/countries-bundle.php:199
msgid "Saint Barth&eacute;lemy"
msgstr "San Bartolomé"

#: includes/bundles/countries-bundle.php:200
msgid "Saint Helena"
msgstr "Santa Elena"

#: includes/bundles/countries-bundle.php:201
msgid "Saint Kitts and Nevis"
msgstr "San Cristóbal y Nieves"

#: includes/bundles/countries-bundle.php:202
msgid "Saint Lucia"
msgstr "Santa Lucía"

#: includes/bundles/countries-bundle.php:203
msgid "Saint Martin (French part)"
msgstr "San Martín (parte francesa)"

#: includes/bundles/countries-bundle.php:204
msgid "Saint Martin (Dutch part)"
msgstr "Sint Maarten (parte neerlandesa)"

#: includes/bundles/countries-bundle.php:205
msgid "Saint Pierre and Miquelon"
msgstr "San Pedro y Miquelón"

#: includes/bundles/countries-bundle.php:206
msgid "Saint Vincent and the Grenadines"
msgstr "San Vicente y las Granadinas"

#: includes/bundles/countries-bundle.php:207
msgid "San Marino"
msgstr "San Marino"

#: includes/bundles/countries-bundle.php:208
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "Santo Tomé y Príncipe"

#: includes/bundles/countries-bundle.php:209
msgid "Saudi Arabia"
msgstr "Arabia Saudita"

#: includes/bundles/countries-bundle.php:210
msgid "Senegal"
msgstr "Senegal"

#: includes/bundles/countries-bundle.php:211
msgid "Serbia"
msgstr "Serbia"

#: includes/bundles/countries-bundle.php:212
msgid "Seychelles"
msgstr "Las Seychelles"

#: includes/bundles/countries-bundle.php:213
msgid "Sierra Leone"
msgstr "Sierra Leona"

#: includes/bundles/countries-bundle.php:214
msgid "Singapore"
msgstr "Singapur"

#: includes/bundles/countries-bundle.php:215
msgid "Slovakia"
msgstr "Eslovaquia"

#: includes/bundles/countries-bundle.php:216
msgid "Slovenia"
msgstr "Eslovenia"

#: includes/bundles/countries-bundle.php:217
msgid "Solomon Islands"
msgstr "Islas Salomón"

#: includes/bundles/countries-bundle.php:218
msgid "Somalia"
msgstr "Somalia"

#: includes/bundles/countries-bundle.php:219
msgid "South Africa"
msgstr "Sudáfrica"

#: includes/bundles/countries-bundle.php:220
msgid "South Georgia/Sandwich Islands"
msgstr "Islas Georgias del Sur y Sandwich del Sur"

#: includes/bundles/countries-bundle.php:221
msgid "South Korea"
msgstr "República de Corea"

#: includes/bundles/countries-bundle.php:222
msgid "South Sudan"
msgstr "Sudán del Sur"

#: includes/bundles/countries-bundle.php:223
msgid "Spain"
msgstr "España"

#: includes/bundles/countries-bundle.php:224
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: includes/bundles/countries-bundle.php:225
msgid "Sudan"
msgstr "Sudán"

#: includes/bundles/countries-bundle.php:226
msgid "Suriname"
msgstr "Surinam"

#: includes/bundles/countries-bundle.php:227
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard y Jan Mayen"

#: includes/bundles/countries-bundle.php:228
msgid "Swaziland"
msgstr "Suazilandia"

#: includes/bundles/countries-bundle.php:229
msgid "Sweden"
msgstr "Suecia"

#: includes/bundles/countries-bundle.php:230
msgid "Switzerland"
msgstr "Suiza"

#: includes/bundles/countries-bundle.php:231
msgid "Syria"
msgstr "Siria"

#: includes/bundles/countries-bundle.php:232
msgid "Taiwan"
msgstr "La isla de Taiwán"

#: includes/bundles/countries-bundle.php:233
msgid "Tajikistan"
msgstr "Tayikistán"

#: includes/bundles/countries-bundle.php:234
msgid "Tanzania"
msgstr "Tanzania"

#: includes/bundles/countries-bundle.php:235
msgid "Thailand"
msgstr "Tailandia"

#: includes/bundles/countries-bundle.php:236
msgid "Timor-Leste"
msgstr "Timor Oriental"

#: includes/bundles/countries-bundle.php:237
msgid "Togo"
msgstr "Togo"

#: includes/bundles/countries-bundle.php:238
msgid "Tokelau"
msgstr "Tokelau"

#: includes/bundles/countries-bundle.php:239
msgid "Tonga"
msgstr "Tonga"

#: includes/bundles/countries-bundle.php:240
msgid "Trinidad and Tobago"
msgstr "Trinidad y Tobago"

#: includes/bundles/countries-bundle.php:241
msgid "Tunisia"
msgstr "Túnez"

#: includes/bundles/countries-bundle.php:242
msgid "Turkey"
msgstr "Turquía"

#: includes/bundles/countries-bundle.php:243
msgid "Turkmenistan"
msgstr "Turkmenistán"

#: includes/bundles/countries-bundle.php:244
msgid "Turks and Caicos Islands"
msgstr "Islas Turcas y Caicos"

#: includes/bundles/countries-bundle.php:245
msgid "Tuvalu"
msgstr "Tuvalu"

#: includes/bundles/countries-bundle.php:246
msgid "Uganda"
msgstr "Uganda"

#: includes/bundles/countries-bundle.php:247
msgid "Ukraine"
msgstr "Ucrania"

#: includes/bundles/countries-bundle.php:248
msgid "United Arab Emirates"
msgstr "Emiratos Árabes Unidos"

#: includes/bundles/countries-bundle.php:249
msgid "United Kingdom (UK)"
msgstr "Reino Unido"

#: includes/bundles/countries-bundle.php:250
msgid "United States (US)"
msgstr "Estados Unidos"

#: includes/bundles/countries-bundle.php:251
msgid "United States (US) Minor Outlying Islands"
msgstr "Islas Ultramarinas Menores de Estados Unidos"

#: includes/bundles/countries-bundle.php:252
msgid "United States (US) Virgin Islands"
msgstr "Islas Vírgenes de los Estados Unidos"

#: includes/bundles/countries-bundle.php:253
msgid "Uruguay"
msgstr "Uruguay"

#: includes/bundles/countries-bundle.php:254
msgid "Uzbekistan"
msgstr "Uzbekistán"

#: includes/bundles/countries-bundle.php:255
msgid "Vanuatu"
msgstr "Vanuatu"

#: includes/bundles/countries-bundle.php:256
msgid "Vatican"
msgstr "Vaticano"

#: includes/bundles/countries-bundle.php:257
msgid "Venezuela"
msgstr "Venezuela"

#: includes/bundles/countries-bundle.php:258
msgid "Vietnam"
msgstr "Vietnam"

#: includes/bundles/countries-bundle.php:259
msgid "Wallis and Futuna"
msgstr "Wallis y Futuna"

#: includes/bundles/countries-bundle.php:260
msgid "Western Sahara"
msgstr "Sáhara Occidental"

#: includes/bundles/countries-bundle.php:261
msgid "Samoa"
msgstr "Samoa"

#: includes/bundles/countries-bundle.php:262
msgid "Yemen"
msgstr "Yemen"

#: includes/bundles/countries-bundle.php:263
msgid "Zambia"
msgstr "Zambia"

#: includes/bundles/countries-bundle.php:264
msgid "Zimbabwe"
msgstr "Zimbabue"

#: includes/bundles/currency-bundle.php:17
msgid "Euro"
msgstr "Euro"

#: includes/bundles/currency-bundle.php:18
msgid "United States (US) dollar"
msgstr "Dólar estadounidense (USD)"

#: includes/bundles/currency-bundle.php:19
msgid "Pound sterling"
msgstr "Libra esterlina"

#: includes/bundles/currency-bundle.php:20
msgid "United Arab Emirates dirham"
msgstr "Dírham de los Emiratos Árabes Unidos"

#: includes/bundles/currency-bundle.php:21
msgid "Afghan afghani"
msgstr "Afgani"

#: includes/bundles/currency-bundle.php:22
msgid "Albanian lek"
msgstr "Lek albanés"

#: includes/bundles/currency-bundle.php:23
msgid "Armenian dram"
msgstr "Dram armenio"

#: includes/bundles/currency-bundle.php:24
msgid "Netherlands Antillean guilder"
msgstr "Florín de las Antillas Holandesas"

#: includes/bundles/currency-bundle.php:25
msgid "Angolan kwanza"
msgstr "Kwanza angoleño"

#: includes/bundles/currency-bundle.php:26
msgid "Argentine peso"
msgstr "Peso de Argentina"

#: includes/bundles/currency-bundle.php:27
msgid "Australian dollar"
msgstr "Dólar australiano"

#: includes/bundles/currency-bundle.php:28
msgid "Aruban florin"
msgstr "Florín de Aruba"

#: includes/bundles/currency-bundle.php:29
msgid "Azerbaijani manat"
msgstr "Manat azerbaiyano"

#: includes/bundles/currency-bundle.php:30
msgid "Bosnia and Herzegovina convertible mark"
msgstr "Marco convertible de Bosnia y Herzegovina"

#: includes/bundles/currency-bundle.php:31
msgid "Barbadian dollar"
msgstr "Dólar barbadense"

#: includes/bundles/currency-bundle.php:32
msgid "Bangladeshi taka"
msgstr "Taka bangladesí"

#: includes/bundles/currency-bundle.php:33
msgid "Bulgarian lev"
msgstr "Lev de Bulgaria"

#: includes/bundles/currency-bundle.php:34
msgid "Bahraini dinar"
msgstr "Dinar bahreiní"

#: includes/bundles/currency-bundle.php:35
msgid "Burundian franc"
msgstr "Franco burundés"

#: includes/bundles/currency-bundle.php:36
msgid "Bermudian dollar"
msgstr "Dólar de las Bahamas"

#: includes/bundles/currency-bundle.php:37
msgid "Brunei dollar"
msgstr "Dólar de Brunéi"

#: includes/bundles/currency-bundle.php:38
msgid "Bolivian boliviano"
msgstr "Boliviano de Bolivia"

#: includes/bundles/currency-bundle.php:39
msgid "Brazilian real"
msgstr "Real brasileño"

#: includes/bundles/currency-bundle.php:40
msgid "Bahamian dollar"
msgstr "Dólar bahameño"

#: includes/bundles/currency-bundle.php:41
msgid "Bitcoin"
msgstr "Bitcoin"

#: includes/bundles/currency-bundle.php:42
msgid "Bhutanese ngultrum"
msgstr "Ngultrum butanés"

#: includes/bundles/currency-bundle.php:43
msgid "Botswana pula"
msgstr "Pula de Botswana"

#: includes/bundles/currency-bundle.php:44
msgid "Belarusian ruble (old)"
msgstr "Rublo bielorruso (antiguo)"

#: includes/bundles/currency-bundle.php:45
msgid "Belarusian ruble"
msgstr "Rublo bielorruso"

#: includes/bundles/currency-bundle.php:46
msgid "Belize dollar"
msgstr "Dólar beliceño"

#: includes/bundles/currency-bundle.php:47
msgid "Canadian dollar"
msgstr "Dólar canadiense"

#: includes/bundles/currency-bundle.php:48
msgid "Congolese franc"
msgstr "Franco congoleño"

#: includes/bundles/currency-bundle.php:49
msgid "Swiss franc"
msgstr "Franco suizo"

#: includes/bundles/currency-bundle.php:50
msgid "Chilean peso"
msgstr "Peso de Chile"

#: includes/bundles/currency-bundle.php:51
msgid "Chinese yuan"
msgstr "Renminbi"

#: includes/bundles/currency-bundle.php:52
msgid "Colombian peso"
msgstr "Peso de Colombia"

#: includes/bundles/currency-bundle.php:53
msgid "Costa Rican col&oacute;n"
msgstr "Col&oacute;n costarricense"

#: includes/bundles/currency-bundle.php:54
msgid "Cuban convertible peso"
msgstr "Peso cubano convertible"

#: includes/bundles/currency-bundle.php:55
msgid "Cuban peso"
msgstr "Peso cubano"

#: includes/bundles/currency-bundle.php:56
msgid "Cape Verdean escudo"
msgstr "Escudo caboverdiano"

#: includes/bundles/currency-bundle.php:57
msgid "Czech koruna"
msgstr "Corona checa"

#: includes/bundles/currency-bundle.php:58
msgid "Djiboutian franc"
msgstr "Franco yibutiano"

#: includes/bundles/currency-bundle.php:59
msgid "Danish krone"
msgstr "Corona danesa"

#: includes/bundles/currency-bundle.php:60
msgid "Dominican peso"
msgstr "Peso de Dominicana"

#: includes/bundles/currency-bundle.php:61
msgid "Algerian dinar"
msgstr "Dinar argelino"

#: includes/bundles/currency-bundle.php:62
msgid "Egyptian pound"
msgstr "Libra egipcia"

#: includes/bundles/currency-bundle.php:63
msgid "Eritrean nakfa"
msgstr "Nakfa eritreo"

#: includes/bundles/currency-bundle.php:64
msgid "Ethiopian birr"
msgstr "Birr etíope"

#: includes/bundles/currency-bundle.php:65
msgid "Fijian dollar"
msgstr "Dólar fiyiano"

#: includes/bundles/currency-bundle.php:66
msgid "Falkland Islands pound"
msgstr "Libra de las Islas Malvinas"

#: includes/bundles/currency-bundle.php:67
msgid "Georgian lari"
msgstr "Lari georgiano"

#: includes/bundles/currency-bundle.php:68
msgid "Guernsey pound"
msgstr "Libra de Guernsey"

#: includes/bundles/currency-bundle.php:69
msgid "Ghana cedi"
msgstr "Cedi ghanés"

#: includes/bundles/currency-bundle.php:70
msgid "Gibraltar pound"
msgstr "Libra gibraltareña"

#: includes/bundles/currency-bundle.php:71
msgid "Gambian dalasi"
msgstr "Gambia dalasi"

#: includes/bundles/currency-bundle.php:72
msgid "Guinean franc"
msgstr "Franco guineano"

#: includes/bundles/currency-bundle.php:73
msgid "Guatemalan quetzal"
msgstr "Quetzal guatemalteco"

#: includes/bundles/currency-bundle.php:74
msgid "Guyanese dollar"
msgstr "Dólar guyanés"

#: includes/bundles/currency-bundle.php:75
msgid "Hong Kong dollar"
msgstr "Dólar de Hong Kong"

#: includes/bundles/currency-bundle.php:76
msgid "Honduran lempira"
msgstr "Lempira hondureño"

#: includes/bundles/currency-bundle.php:77
msgid "Croatian kuna"
msgstr "Kuna croata"

#: includes/bundles/currency-bundle.php:78
msgid "Haitian gourde"
msgstr "Gourde haitiano"

#: includes/bundles/currency-bundle.php:79
msgid "Hungarian forint"
msgstr "Forinto húngaro"

#: includes/bundles/currency-bundle.php:80
msgid "Indonesian rupiah"
msgstr "Rupia indonesia"

#: includes/bundles/currency-bundle.php:81
msgid "Israeli new shekel"
msgstr "Nuevo shéquel israelí"

#: includes/bundles/currency-bundle.php:82
msgid "Manx pound"
msgstr "Libra manesa"

#: includes/bundles/currency-bundle.php:83
msgid "Indian rupee"
msgstr "Rupia india"

#: includes/bundles/currency-bundle.php:84
msgid "Iraqi dinar"
msgstr "Dinar iraquí"

#: includes/bundles/currency-bundle.php:85
msgid "Iranian rial"
msgstr "Rial iraní"

#: includes/bundles/currency-bundle.php:86
msgid "Iranian toman"
msgstr "Tomo iraní"

#: includes/bundles/currency-bundle.php:87
msgid "Icelandic kr&oacute;na"
msgstr "Islandés kr&oacute;na"

#: includes/bundles/currency-bundle.php:88
msgid "Jersey pound"
msgstr "Libra de Jersey"

#: includes/bundles/currency-bundle.php:89
msgid "Jamaican dollar"
msgstr "Dólar jamaicano"

#: includes/bundles/currency-bundle.php:90
msgid "Jordanian dinar"
msgstr "Dinar jordano"

#: includes/bundles/currency-bundle.php:91
msgid "Japanese yen"
msgstr "Yen de Japón"

#: includes/bundles/currency-bundle.php:92
msgid "Kenyan shilling"
msgstr "Chelín keniano"

#: includes/bundles/currency-bundle.php:93
msgid "Kyrgyzstani som"
msgstr "Som kirguís"

#: includes/bundles/currency-bundle.php:94
msgid "Cambodian riel"
msgstr "Riel camboyano"

#: includes/bundles/currency-bundle.php:95
msgid "Comorian franc"
msgstr "Franco comorense"

#: includes/bundles/currency-bundle.php:96
msgid "North Korean won"
msgstr "Won de Corea del norte"

#: includes/bundles/currency-bundle.php:97
msgid "South Korean won"
msgstr "Won surcoreano"

#: includes/bundles/currency-bundle.php:98
msgid "Kuwaiti dinar"
msgstr "Dinar kuwaití"

#: includes/bundles/currency-bundle.php:99
msgid "Cayman Islands dollar"
msgstr "Dólar de las Islas Caimán"

#: includes/bundles/currency-bundle.php:100
msgid "Kazakhstani tenge"
msgstr "Tenge kazajo"

#: includes/bundles/currency-bundle.php:101
msgid "Lao kip"
msgstr "Kip laosiano"

#: includes/bundles/currency-bundle.php:102
msgid "Lebanese pound"
msgstr "Libra libanesa"

#: includes/bundles/currency-bundle.php:103
msgid "Sri Lankan rupee"
msgstr "Rupia de Sri Lanka"

#: includes/bundles/currency-bundle.php:104
msgid "Liberian dollar"
msgstr "Dólar liberiano"

#: includes/bundles/currency-bundle.php:105
msgid "Lesotho loti"
msgstr "Loti de Lesotho"

#: includes/bundles/currency-bundle.php:106
msgid "Libyan dinar"
msgstr "Dinar libio"

#: includes/bundles/currency-bundle.php:107
msgid "Moroccan dirham"
msgstr "Dírham marroquí"

#: includes/bundles/currency-bundle.php:108
msgid "Moldovan leu"
msgstr "Leu moldavo"

#: includes/bundles/currency-bundle.php:109
msgid "Malagasy ariary"
msgstr "Ariary malgache"

#: includes/bundles/currency-bundle.php:110
msgid "Macedonian denar"
msgstr "Denar macedonio"

#: includes/bundles/currency-bundle.php:111
msgid "Burmese kyat"
msgstr "Kyat birmano"

#: includes/bundles/currency-bundle.php:112
msgid "Mongolian t&ouml;gr&ouml;g"
msgstr "T&ouml;gr&ouml;g Mongol"

#: includes/bundles/currency-bundle.php:113
msgid "Macanese pataca"
msgstr "Pataca macaense"

#: includes/bundles/currency-bundle.php:114
msgid "Mauritanian ouguiya"
msgstr "Ouguiya mauritana"

#: includes/bundles/currency-bundle.php:115
msgid "Mauritian rupee"
msgstr "Rupia mauriciana"

#: includes/bundles/currency-bundle.php:116
msgid "Maldivian rufiyaa"
msgstr "Rupia de Maldivas"

#: includes/bundles/currency-bundle.php:117
msgid "Malawian kwacha"
msgstr "Kwacha malauí"

#: includes/bundles/currency-bundle.php:118
msgid "Mexican peso"
msgstr "Peso mexicano"

#: includes/bundles/currency-bundle.php:119
msgid "Malaysian ringgit"
msgstr "Ringgit malasio"

#: includes/bundles/currency-bundle.php:120
msgid "Mozambican metical"
msgstr "Metical mozambiqueño"

#: includes/bundles/currency-bundle.php:121
msgid "Namibian dollar"
msgstr "Dólar namibio"

#: includes/bundles/currency-bundle.php:122
msgid "Nigerian naira"
msgstr "Naira nigeriana"

#: includes/bundles/currency-bundle.php:123
msgid "Nicaraguan c&oacute;rdoba"
msgstr "C&oacute;rdoba nicaragüense"

#: includes/bundles/currency-bundle.php:124
msgid "Norwegian krone"
msgstr "Corona noruega"

#: includes/bundles/currency-bundle.php:125
msgid "Nepalese rupee"
msgstr "Rupia nepalí"

#: includes/bundles/currency-bundle.php:126
msgid "New Zealand dollar"
msgstr "Dólar neozelandés"

#: includes/bundles/currency-bundle.php:127
msgid "Omani rial"
msgstr "Rial omaní"

#: includes/bundles/currency-bundle.php:128
msgid "Panamanian balboa"
msgstr "Balboa panameño"

#: includes/bundles/currency-bundle.php:129
msgid "Sol"
msgstr "Sol"

#: includes/bundles/currency-bundle.php:130
msgid "Papua New Guinean kina"
msgstr "Kina de Papúa Nueva Guinea"

#: includes/bundles/currency-bundle.php:131
msgid "Philippine peso"
msgstr "Peso filipino"

#: includes/bundles/currency-bundle.php:132
msgid "Pakistani rupee"
msgstr "Rupia pakistaní"

#: includes/bundles/currency-bundle.php:133
msgid "Polish z&#x142;oty"
msgstr "Esloti polaco"

#: includes/bundles/currency-bundle.php:134
msgid "Transnistrian ruble"
msgstr "Rublo de Transnistria"

#: includes/bundles/currency-bundle.php:135
msgid "Paraguayan guaran&iacute;"
msgstr "Guaraní paraguayo"

#: includes/bundles/currency-bundle.php:136
msgid "Qatari riyal"
msgstr "Rial qatarí"

#: includes/bundles/currency-bundle.php:137
msgid "Romanian leu"
msgstr "Leu rumano"

#: includes/bundles/currency-bundle.php:138
msgid "Serbian dinar"
msgstr "Dinar serbio"

#: includes/bundles/currency-bundle.php:139
msgid "Russian ruble"
msgstr "Rublo ruso"

#: includes/bundles/currency-bundle.php:140
msgid "Rwandan franc"
msgstr "Franco ruandés"

#: includes/bundles/currency-bundle.php:141
msgid "Saudi riyal"
msgstr "Riyal saudí"

#: includes/bundles/currency-bundle.php:142
msgid "Solomon Islands dollar"
msgstr "Dólar de las Islas Salomón"

#: includes/bundles/currency-bundle.php:143
msgid "Seychellois rupee"
msgstr "Rupia seychellense"

#: includes/bundles/currency-bundle.php:144
msgid "Sudanese pound"
msgstr "Libra sudanesa"

#: includes/bundles/currency-bundle.php:145
msgid "Swedish krona"
msgstr "Corona sueca"

#: includes/bundles/currency-bundle.php:146
msgid "Singapore dollar"
msgstr "Dólar de Singapur"

#: includes/bundles/currency-bundle.php:147
msgid "Saint Helena pound"
msgstr "Libra de Santa Elena"

#: includes/bundles/currency-bundle.php:148
msgid "Sierra Leonean leone"
msgstr "Leone de Sierra Leona"

#: includes/bundles/currency-bundle.php:149
msgid "Somali shilling"
msgstr "Chelín somalí"

#: includes/bundles/currency-bundle.php:150
msgid "Surinamese dollar"
msgstr "Dólar surinamés"

#: includes/bundles/currency-bundle.php:151
msgid "South Sudanese pound"
msgstr "Libra sudanesa"

#: includes/bundles/currency-bundle.php:152
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe dobra"
msgstr "S&atilde;o Tom&eacute; y Pr&iacute;ncipe dobra"

#: includes/bundles/currency-bundle.php:153
msgid "Syrian pound"
msgstr "Libra siria"

#: includes/bundles/currency-bundle.php:154
msgid "Swazi lilangeni"
msgstr "Suazi lilangeni"

#: includes/bundles/currency-bundle.php:155
msgid "Thai baht"
msgstr "Baht tailandés"

#: includes/bundles/currency-bundle.php:156
msgid "Tajikistani somoni"
msgstr "Somoni tayiko"

#: includes/bundles/currency-bundle.php:157
msgid "Turkmenistan manat"
msgstr "Manat turcomano"

#: includes/bundles/currency-bundle.php:158
msgid "Tunisian dinar"
msgstr "Dinar tunecino"

#: includes/bundles/currency-bundle.php:159
msgid "Tongan pa&#x2bb;anga"
msgstr "Pa’anga tongano"

#: includes/bundles/currency-bundle.php:160
msgid "Turkish lira"
msgstr "Lira turca"

#: includes/bundles/currency-bundle.php:161
msgid "Trinidad and Tobago dollar"
msgstr "Dólar de Trinidad y Tobago"

#: includes/bundles/currency-bundle.php:162
msgid "New Taiwan dollar"
msgstr "Nuevo dólar taiwanés"

#: includes/bundles/currency-bundle.php:163
msgid "Tanzanian shilling"
msgstr "Chelín tanzano"

#: includes/bundles/currency-bundle.php:164
msgid "Ukrainian hryvnia"
msgstr "Grivna de Ucrania"

#: includes/bundles/currency-bundle.php:165
msgid "Ugandan shilling"
msgstr "Chelín ugandés"

#: includes/bundles/currency-bundle.php:166
msgid "Uruguayan peso"
msgstr "Peso uruguayo"

#: includes/bundles/currency-bundle.php:167
msgid "Uzbekistani som"
msgstr "Som uzbeko"

#: includes/bundles/currency-bundle.php:168
msgid "Venezuelan bol&iacute;var"
msgstr "Bol&iacute;var venezolano"

#: includes/bundles/currency-bundle.php:169
msgid "Bol&iacute;var soberano"
msgstr "Bol&iacute;var soberano"

#: includes/bundles/currency-bundle.php:170
msgid "Vietnamese &#x111;&#x1ed3;ng"
msgstr "&#x111;&#x1ed3;ng vietnamita"

#: includes/bundles/currency-bundle.php:171
msgid "Vanuatu vatu"
msgstr "Vatu de Vanuatu"

#: includes/bundles/currency-bundle.php:172
msgid "Samoan t&#x101;l&#x101;"
msgstr "Tala samoano"

#: includes/bundles/currency-bundle.php:173
msgid "Central African CFA franc"
msgstr "Franco CFA de África Central"

#: includes/bundles/currency-bundle.php:174
msgid "East Caribbean dollar"
msgstr "Dólar del Caribe Oriental"

#: includes/bundles/currency-bundle.php:175
msgid "West African CFA franc"
msgstr "Franco CFA de África Occidental"

#: includes/bundles/currency-bundle.php:176
msgid "CFP franc"
msgstr "Franco CFP"

#: includes/bundles/currency-bundle.php:177
msgid "Yemeni rial"
msgstr "Rial yemení"

#: includes/bundles/currency-bundle.php:178
msgid "South African rand"
msgstr "Rand sudafricano"

#: includes/bundles/currency-bundle.php:179
msgid "Zambian kwacha"
msgstr "Kwacha zambiano"

#: includes/bundles/currency-bundle.php:358
msgid "Before"
msgstr "Antes"

#: includes/bundles/currency-bundle.php:359
msgid "After"
msgstr "Después"

#: includes/bundles/currency-bundle.php:360
msgid "Before with space"
msgstr "Antes con espacio"

#: includes/bundles/currency-bundle.php:361
msgid "After with space"
msgstr "Después con espacio"

#: includes/bundles/customer-bundle.php:97
msgid "First name is required."
msgstr "Nombre es un campo obligatorio."

#: includes/bundles/customer-bundle.php:106
msgid "Last name is required."
msgstr "Apellido es un campo obligatorio."

#: includes/bundles/customer-bundle.php:115
msgid "Email is required."
msgstr "Email es un campo obligatorio."

#: includes/bundles/customer-bundle.php:124
msgid "Phone is required."
msgstr "Número de teléfono es un campo obligatorio."

#: includes/bundles/customer-bundle.php:128
#: includes/views/shortcodes/checkout-view.php:650
msgid "Country of residence"
msgstr "País de residencia"

#: includes/bundles/customer-bundle.php:133
msgid "Country is required."
msgstr "País es un campo obligatorio."

#: includes/bundles/customer-bundle.php:142
msgid "Address is required."
msgstr "Dirección es un campo obligatorio."

#: includes/bundles/customer-bundle.php:151
msgid "City is required."
msgstr "Ciudad es un campo obligatorio."

#: includes/bundles/customer-bundle.php:160
msgid "State is required."
msgstr "Estado es un campo obligatorio."

#: includes/bundles/customer-bundle.php:169
msgid "Postcode is required."
msgstr "Código postal es un campo obligatorio."

#: includes/bundles/customer-bundle.php:178
msgid "Note is required."
msgstr "Nota es obligatorio."

#: includes/bundles/units-bundle.php:16
msgid "Square Meter"
msgstr "Metro cuadrado"

#: includes/bundles/units-bundle.php:17
msgid "Square Foot"
msgstr "Pie cuadrado"

#: includes/bundles/units-bundle.php:18
msgid "Square Yard"
msgstr "Yarda cuadrada"

#: includes/bundles/units-bundle.php:21
msgid "m²"
msgstr "m²"

#: includes/bundles/units-bundle.php:22
msgid "ft²"
msgstr "ft²"

#: includes/bundles/units-bundle.php:23
msgid "yd²"
msgstr "y²"

#: includes/core/helpers/price-helper.php:57
msgctxt "Zero price"
msgid "Free"
msgstr "Gratis"

#. translators: Price per one night. Example: $99 per night
#: includes/core/helpers/price-helper.php:144
msgctxt "Price per one night. Example: $99 per night"
msgid "per night"
msgstr "por noche"

#. translators: Price for X nights. Example: $99 for 2 nights, $99 for 21 nights
#: includes/core/helpers/price-helper.php:156
msgctxt "Price for X nights. Example: $99 for 2 nights, $99 for 21 nights"
msgid "for %d nights"
msgid_plural "for %d nights"
msgstr[0] "por noche"
msgstr[1] "por %d noches"

#: includes/crons/cron-manager.php:112
msgid "User Approval Time setted in Hotel Booking Settings"
msgstr "Tiempo de confirmación establecido en los ajustes de reserva de hotel"

#: includes/crons/cron-manager.php:117
msgid "Pending Payment Time set in Hotel Booking Settings"
msgstr "Tiempo de pago pendiente establecido en los ajustes de reserva de hotel"

#: includes/crons/cron-manager.php:122
msgid "Interval for automatic cleaning of synchronization logs."
msgstr "Intervalo para limpieza automática y sincronización de informes."

#: includes/crons/cron-manager.php:127
msgid "Once a week"
msgstr "Una vez a la semana"

#: includes/csv/bookings/bookings-exporter-helper.php:73
#: templates/account/bookings.php:19
#: templates/account/bookings.php:70
#: templates/create-booking/search/search-form.php:42
#: templates/edit-booking/edit-dates.php:29
#: templates/shortcodes/search/search-form.php:35
msgid "Check-in"
msgstr "Día de llegada"

#: includes/csv/bookings/bookings-exporter-helper.php:74
#: templates/account/bookings.php:20
#: templates/account/bookings.php:73
#: templates/create-booking/search/search-form.php:62
#: templates/edit-booking/edit-dates.php:38
#: templates/shortcodes/search/search-form.php:55
msgid "Check-out"
msgstr "Día de salida"

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/csv/bookings/bookings-exporter-helper.php:78
#: includes/post-types/rate-cpt.php:104
msgid "Rate"
msgstr "Tarifa"

#: includes/csv/bookings/bookings-exporter-helper.php:79
msgid "Adults/Guests"
msgstr "Adultos/Invitados"

#: includes/csv/bookings/bookings-exporter-helper.php:91
#: includes/emails/templaters/reserved-rooms-templater.php:223
#: includes/views/edit-booking/checkout-view.php:164
#: includes/views/shortcodes/checkout-view.php:291
msgid "Full Guest Name"
msgstr "Nombre completo"

#: includes/csv/bookings/bookings-exporter-helper.php:92
#: includes/views/booking-view.php:141
msgid "Accommodation Subtotal"
msgstr "Subtotal Alojamiento"

#: includes/csv/bookings/bookings-exporter-helper.php:93
#: includes/post-types/coupon-cpt.php:72
#: includes/views/booking-view.php:150
msgid "Accommodation Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:94
#: includes/views/booking-view.php:160
msgid "Accommodation Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:96
#: includes/views/booking-view.php:186
msgid "Accommodation Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:98
#: includes/views/booking-view.php:225
msgid "Services Subtotal"
msgstr "Subtotal de servicios"

#: includes/csv/bookings/bookings-exporter-helper.php:99
#: includes/views/booking-view.php:236
msgid "Services Discount"
msgstr "Descuento por servicio"

#: includes/csv/bookings/bookings-exporter-helper.php:100
#: includes/views/booking-view.php:248
msgid "Services Total"
msgstr "Total de servicios"

#: includes/csv/bookings/bookings-exporter-helper.php:101
#: includes/views/booking-view.php:280
msgid "Service Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:103
#: includes/views/booking-view.php:312
msgid "Fees Subtotal"
msgstr "Subtotal de cargos"

#: includes/csv/bookings/bookings-exporter-helper.php:104
#: includes/views/booking-view.php:321
msgid "Fees Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:105
#: includes/views/booking-view.php:331
msgid "Fees Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:106
#: includes/views/booking-view.php:364
msgid "Fee Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:108
msgid "Discount"
msgstr "Descuento"

#: includes/csv/bookings/bookings-exporter-helper.php:109
#: includes/views/booking-view.php:451
#: templates/account/bookings.php:21
#: templates/account/bookings.php:76
msgid "Total"
msgstr "Total"

#: includes/csv/bookings/bookings-exporter-helper.php:110
msgid "Paid"
msgstr "Pagada"

#: includes/csv/bookings/bookings-exporter-helper.php:111
#: includes/post-types/payment-cpt.php:129
#: includes/shortcodes/booking-confirmation-shortcode.php:284
msgid "Payment Details"
msgstr "Detalles de pago"

#: includes/csv/bookings/bookings-query.php:92
msgid "Please select columns to export."
msgstr "Por favor, selecciona las columnas para la exportación."

#: includes/csv/csv-export-handler.php:32
#: includes/payments/gateways/stripe-gateway.php:559
msgid "Nonce verification failed."
msgstr "Ninguna verificación falló."

#: includes/csv/csv-export-handler.php:50
msgid "The file does not exist."
msgstr "El archivo no existe."

#: includes/emails/abstract-email.php:441
msgid "Disable this email notification"
msgstr "Desactivar esta notificación por correo electrónico"

#: includes/emails/abstract-email.php:449
msgid "Subject"
msgstr "Tema"

#: includes/emails/abstract-email.php:461
msgid "Header"
msgstr "Cabecera"

#: includes/emails/abstract-email.php:473
msgid "Email Template"
msgstr "Plantilla de email"

#: includes/emails/abstract-email.php:570
msgid "\"%s\" email will not be sent: there is no customer email in the booking."
msgstr "El correo electrónico \"%s\" no se enviará: no hay un correo electrónico del cliente en la reserva."

#: includes/emails/abstract-email.php:594
msgid "Deprecated tags in header of %s"
msgstr "Etiquetas obsoletas en el título de %s"

#: includes/emails/abstract-email.php:597
msgid "Deprecated tags in subject of %s"
msgstr "Etiquetas obsoletas en el tema de %s"

#: includes/emails/abstract-email.php:600
msgid "Deprecated tags in template of %s"
msgstr "Etiquetas obsoletas en la plantilla de %s"

#: includes/emails/booking/admin/base-email.php:37
msgid "Recipients"
msgstr "Destinatarios"

#: includes/emails/booking/admin/base-email.php:40
msgid "You can use multiple comma-separated emails"
msgstr "Puede utilizar varios correos electrónicos separados por comas"

#: includes/emails/booking/admin/base-email.php:89
msgid "\"%s\" mail was sent to admin."
msgstr "\"%s\" emails han sido enviados al admin."

#: includes/emails/booking/admin/base-email.php:93
msgid "\"%s\" mail sending to admin is failed."
msgstr "Se ha producido un error al enviar \"%s\" emails al admin."

#: includes/emails/booking/admin/cancelled-email.php:8
msgid "Booking Cancelled"
msgstr "Reserva cancelada"

#: includes/emails/booking/admin/cancelled-email.php:12
msgid "%site_title% - Booking #%booking_id% Cancelled"
msgstr "%site_title% - Reserva #%booking_id% cancelada"

#: includes/emails/booking/admin/cancelled-email.php:16
msgid "Email that will be sent to Admin when customer cancels booking."
msgstr "Correo electrónico que será enviado al administrador después de la cancelación de reserva por usuario."

#: includes/emails/booking/admin/cancelled-email.php:20
#: includes/emails/booking/customer/cancelled-email.php:20
msgid "Cancelled Booking Email"
msgstr "Email de reserva cancelada"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:8
#: includes/emails/booking/admin/confirmed-email.php:8
#: includes/wizard.php:134
msgid "Booking Confirmed"
msgstr "Reserva confirmada"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:12
#: includes/emails/booking/admin/confirmed-email.php:12
msgid "%site_title% - Booking #%booking_id% Confirmed"
msgstr "%site_title% - Reserva #%booking_id% confirmada"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:16
msgid "Email that will be sent to Admin when payment is completed."
msgstr "Correo electrónico que será enviado al administrador cuando se complete el pago."

#: includes/emails/booking/admin/confirmed-by-payment-email.php:20
msgid "Approved Booking Email (via payment)"
msgstr "Email de reserva aprobada (por pago)"

#: includes/emails/booking/admin/confirmed-email.php:16
msgid "Email that will be sent to Admin when customer confirms booking."
msgstr "Correo electrónico que será enviado al administrador después de la confirmación de reserva por usuario."

#: includes/emails/booking/admin/confirmed-email.php:20
#: includes/emails/booking/customer/approved-email.php:20
msgid "Approved Booking Email"
msgstr "Email de reserva aprobada"

#: includes/emails/booking/admin/pending-email.php:8
msgid "Confirm new booking"
msgstr "Confirmar nueva reserva"

#: includes/emails/booking/admin/pending-email.php:12
msgid "%site_title% - New booking #%booking_id%"
msgstr "%site_title% - Nueva reserva #%booking_id%"

#: includes/emails/booking/admin/pending-email.php:16
msgid "Email that will be sent to administrator after booking is placed."
msgstr "Correo electrónico que será enviado al administrador después de la aparición de reserva."

#: includes/emails/booking/admin/pending-email.php:20
msgid "Pending Booking Email"
msgstr "Email de reserva pendiente"

#: includes/emails/booking/customer/approved-email.php:8
msgid "Your booking is approved"
msgstr "Su reserva ha sido aprobada"

#: includes/emails/booking/customer/approved-email.php:12
msgid "%site_title% - Your booking #%booking_id% is approved"
msgstr "%site_title% - Su reserva #%booking_id% ha sido aprobada"

#: includes/emails/booking/customer/approved-email.php:16
msgid "Email that will be sent to customer when booking is approved."
msgstr "Correo electrónico que será enviado al usuario después de la aprobación de su reserva."

#: includes/emails/booking/customer/base-email.php:55
msgid "\"%s\" mail was sent to customer."
msgstr "\"%s\" emails han sido enviados a clientes."

#: includes/emails/booking/customer/base-email.php:59
msgid "\"%s\" mail sending is failed."
msgstr "Se ha producido un error al enviar \"%s\" emails."

#: includes/emails/booking/customer/cancelled-email.php:8
msgid "Your booking is cancelled"
msgstr "Su reserva ha sido cancelada"

#: includes/emails/booking/customer/cancelled-email.php:12
msgid "%site_title% - Your booking #%booking_id% is cancelled"
msgstr "%site_title% - Su reserva #%booking_id% ha sido cancelada"

#: includes/emails/booking/customer/cancelled-email.php:16
msgid "Email that will be sent to customer when booking is cancelled."
msgstr "Correo electrónico que será enviado al usuario en caso de que su reserva se cancela."

#: includes/emails/booking/customer/confirmation-email.php:8
msgid "Confirm your booking"
msgstr "Confirme su reserva"

#: includes/emails/booking/customer/confirmation-email.php:12
msgid "%site_title% - Confirm your booking #%booking_id%"
msgstr "%site_title% - Confirme su reserva #%booking_id%"

#: includes/emails/booking/customer/confirmation-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Customer confirmation via email."
msgstr "Este e-mail es enviado cuando el \"modo de confirmacion de reserva\" esta programado como \"confirmacion del cliente via e-mail\""

#: includes/emails/booking/customer/confirmation-email.php:17
#: includes/emails/booking/customer/direct-bank-transfer-email.php:43
#: includes/emails/booking/customer/pending-email.php:17
msgid "Email that will be sent to customer after booking is placed."
msgstr "Correo electrónico que será enviado al usuario después de que hace una reserva."

#: includes/emails/booking/customer/confirmation-email.php:21
msgid "New Booking Email (Confirmation by User)"
msgstr "Email de nueva reserva (confirmación por usuario)"

#: includes/emails/booking/customer/direct-bank-transfer-email.php:35
msgid "Pay for your booking"
msgstr ""

#: includes/emails/booking/customer/direct-bank-transfer-email.php:39
msgid "%site_title% - Pay for your booking #%booking_id%"
msgstr ""

#: includes/emails/booking/customer/direct-bank-transfer-email.php:47
msgid "Payment Instructions Email"
msgstr "Email de instrucciones de pago"

#: includes/emails/booking/customer/pending-email.php:8
msgid "Your booking is placed"
msgstr "Su reserva ha sido creada"

#: includes/emails/booking/customer/pending-email.php:12
msgid "%site_title% - Booking #%booking_id% is placed"
msgstr "%site_title% - Reserva #%booking_id% creada"

#: includes/emails/booking/customer/pending-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Admin confirmation."
msgstr "Este correo electrónico se envía cuando el \"Modo de confirmación de reserva\" está configurado como Confirmación por administrador."

#: includes/emails/booking/customer/pending-email.php:21
msgid "New Booking Email (Confirmation by Admin)"
msgstr "Email de nueva reserva (confirmación por administrador)"

#: includes/emails/booking/customer/registration-email.php:8
msgid "Welcome"
msgstr "Bienvenido"

#: includes/emails/booking/customer/registration-email.php:12
msgid "%site_title% - account details"
msgstr "%site_title%: detalles de la cuenta"

#: includes/emails/booking/customer/registration-email.php:16
msgid "Email that will be sent to a customer after they registered on your site."
msgstr "Correo electrónico que se enviará a un cliente después de registrarse en tu sitio."

#: includes/emails/booking/customer/registration-email.php:20
msgid "Customer Registration Email"
msgstr "Correo electrónico de registro del cliente"

#: includes/emails/templaters/abstract-templater.php:77
msgid "Email Tags"
msgstr ""

#: includes/emails/templaters/abstract-templater.php:87
#: includes/emails/templaters/abstract-templater.php:89
msgid "Deprecated."
msgstr "Obsoleto."

#: includes/emails/templaters/abstract-templater.php:101
msgid "none"
msgstr "no"

#: includes/emails/templaters/cancellation-booking-templater.php:50
msgid "User Cancellation Link"
msgstr "Enlace de cancelación de usuario"

#: includes/emails/templaters/email-templater.php:109
msgid "Site title (set in Settings > General)"
msgstr "Título del sitio (en Ajustes > Generales)"

#: includes/emails/templaters/email-templater.php:124
#: includes/post-types/payment-cpt.php:232
msgid "Booking ID"
msgstr "ID de reserva"

#: includes/emails/templaters/email-templater.php:128
msgid "Booking Edit Link"
msgstr "Enlace de edición de reserva"

#: includes/emails/templaters/email-templater.php:132
msgid "Booking Total Price"
msgstr "Precio total de reserva"

#: includes/emails/templaters/email-templater.php:153
#: includes/emails/templaters/email-templater.php:296
msgid "Customer First Name"
msgstr "Nombre de cliente"

#: includes/emails/templaters/email-templater.php:157
#: includes/emails/templaters/email-templater.php:300
msgid "Customer Last Name"
msgstr "Apellido de cliente"

#: includes/emails/templaters/email-templater.php:161
#: includes/emails/templaters/email-templater.php:304
msgid "Customer Email"
msgstr "Email de cliente"

#: includes/emails/templaters/email-templater.php:165
#: includes/emails/templaters/email-templater.php:308
msgid "Customer Phone"
msgstr "Teléfono de cliente"

#: includes/emails/templaters/email-templater.php:169
#: includes/emails/templaters/email-templater.php:312
msgid "Customer Country"
msgstr "País del cliente"

#: includes/emails/templaters/email-templater.php:173
#: includes/emails/templaters/email-templater.php:316
msgid "Customer Address"
msgstr "Dirección del cliente"

#: includes/emails/templaters/email-templater.php:177
#: includes/emails/templaters/email-templater.php:320
msgid "Customer City"
msgstr "Ciudad del cliente"

#: includes/emails/templaters/email-templater.php:181
#: includes/emails/templaters/email-templater.php:324
msgid "Customer State/County"
msgstr "Estado/condado del cliente"

#: includes/emails/templaters/email-templater.php:185
#: includes/emails/templaters/email-templater.php:328
msgid "Customer Postcode"
msgstr "Código postal del cliente"

#: includes/emails/templaters/email-templater.php:194
msgid "Reserved Accommodations Details"
msgstr "Detalles de alojamiento reservado"

#: includes/emails/templaters/email-templater.php:216
#: includes/views/create-booking/checkout-view.php:15
#: includes/views/shortcodes/checkout-view.php:164
#: templates/shortcodes/booking-details/booking-details.php:18
msgid "Booking Details"
msgstr "Detalles de reserva"

#: includes/emails/templaters/email-templater.php:230
msgid "Confirmation Link"
msgstr "Enlace de confirmación"

#: includes/emails/templaters/email-templater.php:234
msgid "Confirmation Link Expiration Time ( UTC )"
msgstr "Tiempo de expiración del enlace de confirmación (UTC)"

#: includes/emails/templaters/email-templater.php:248
msgid "Cancellation Details (if enabled)"
msgstr "Detalles de cancelación (si esta opción está activada)"

#: includes/emails/templaters/email-templater.php:262
msgid "The total amount of payment"
msgstr ""

#: includes/emails/templaters/email-templater.php:266
msgid "The unique ID of payment"
msgstr "Número ID único de pago"

#: includes/emails/templaters/email-templater.php:270
msgid "The method of payment"
msgstr "Método de pago"

#: includes/emails/templaters/email-templater.php:274
msgid "Payment instructions"
msgstr "Instrucciones de pago"

#: includes/emails/templaters/email-templater.php:288
msgid "User login"
msgstr "Inicio de sesión del usuario"

#: includes/emails/templaters/email-templater.php:292
msgid "User password"
msgstr "Contraseña del usuario"

#: includes/emails/templaters/email-templater.php:332
msgid "Link to My Account page"
msgstr "Enlace a la página Mi cuenta"

#: includes/emails/templaters/email-templater.php:562
#: includes/upgrader.php:868
#: includes/wizard.php:213
msgid "My Account"
msgstr "Mi cuenta"

#: includes/emails/templaters/reserved-rooms-templater.php:191
msgid "Accommodation Type Link"
msgstr "Enlace del tipo de alojamiento"

#: includes/emails/templaters/reserved-rooms-templater.php:195
msgid "Accommodation Type Title"
msgstr "Título del tipo de alojamiento"

#: includes/emails/templaters/reserved-rooms-templater.php:199
msgid "Accommodation Title"
msgstr ""

#: includes/emails/templaters/reserved-rooms-templater.php:203
msgid "Accommodation Type Categories"
msgstr "Categorías del tipo de alojamiento"

#: includes/emails/templaters/reserved-rooms-templater.php:207
msgid "Accommodation Type Bed"
msgstr "Cama del tipo de alojamiento"

#: includes/emails/templaters/reserved-rooms-templater.php:211
msgid "Accommodation Rate Title"
msgstr "Título de tarifa de alojamiento"

#: includes/emails/templaters/reserved-rooms-templater.php:215
msgid "Accommodation Rate Description"
msgstr "Descripción de tarifa de alojamiento"

#: includes/emails/templaters/reserved-rooms-templater.php:219
msgid "Sequential Number of Accommodation"
msgstr "Número secuencial de alojamientos"

#: includes/entities/coupon.php:370
msgid "This coupon has expired."
msgstr "Este cupón ha caducado"

#: includes/entities/coupon.php:374
msgid "Sorry, this coupon is not applicable to your booking contents."
msgstr "Disculpe, este cupón no es aplicable a los contenidos de su reserva."

#: includes/entities/coupon.php:378
msgid "Coupon usage limit has been reached."
msgstr "Se ha alcanzado el límite de uso de cupones."

#: includes/entities/reserved-service.php:98
msgid " &#215; %d night"
msgid_plural " &#215; %d nights"
msgstr[0] " &#215; %d noche"
msgstr[1] " &#215; %d noches"

#: includes/entities/reserved-service.php:103
#: includes/shortcodes/search-results-shortcode.php:904
msgid "%d adult"
msgid_plural "%d adults"
msgstr[0] "%d adulto"
msgstr[1] "%d adultos"

#: includes/entities/reserved-service.php:105
#: includes/shortcodes/search-results-shortcode.php:896
#: includes/shortcodes/search-results-shortcode.php:900
msgid "%d guest"
msgid_plural "%d guests"
msgstr[0] "%d huésped"
msgstr[1] "%d huéspedes"

#: includes/entities/reserved-service.php:110
msgid " &#215; %d time"
msgid_plural " &#215; %d times"
msgstr[0] " &#215; %d vez"
msgstr[1] " &#215; %d veces"

#: includes/entities/service.php:195
msgid "Per Instance"
msgstr "Por instancia"

#: includes/i-cal/background-processes/background-synchronizer.php:34
msgid "Maximum execution time is set to %d seconds."
msgstr "El tiempo máximo de ejecución se establece en %d segundos."

#: includes/i-cal/background-processes/background-synchronizer.php:80
msgid "%d URL pulled for parsing."
msgid_plural "%d URLs pulled for parsing."
msgstr[0] "%d URL extraída para el análisis."
msgstr[1] "%d URLs extraídas para el análisis."

#: includes/i-cal/background-processes/background-synchronizer.php:82
msgid "Skipped. No URLs found for parsing."
msgstr "Omitido. No se encontraron URLs para analizar."

#: includes/i-cal/background-processes/background-uploader.php:64
msgid "Cannot read uploaded file"
msgstr "No se puede leer el archivo cargado"

#: includes/i-cal/background-processes/background-worker.php:327
msgctxt "%s - calendar URI or calendar filename"
msgid "%1$d event found in calendar %2$s"
msgid_plural "%1$d events found in calendar %2$s"
msgstr[0] "%1$d evento encontrado en el calendario %2$s"
msgstr[1] "%1$d eventos encontrados en el calendario %2$s"

#: includes/i-cal/background-processes/background-worker.php:357
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar source is empty (%s)"
msgstr "La fuente del calendario está vacía (%s)"

#: includes/i-cal/background-processes/background-worker.php:370
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar file is not empty, but there are no events in %s"
msgstr "El archivo de calendario no está vacío, pero no hay eventos en %s"

#: includes/i-cal/background-processes/background-worker.php:403
msgid "We will need to check %d previous booking after importing and remove it if the booking is outdated."
msgid_plural "We will need to check %d previous bookings after importing and remove the outdated ones."
msgstr[0] "Tendremos que verificar %d reserva anterior después de importarla y eliminarla si la reserva está desactualizada."
msgstr[1] "Tendremos que verificar %d reservas anteriores después de importarlas y eliminar las desactualizadas."

#: includes/i-cal/background-processes/background-worker.php:425
msgid "Error while loading calendar (%1$s): %2$s"
msgstr "Error durante la carga del calendario (%1$s): %2$s"

#: includes/i-cal/background-processes/background-worker.php:427
msgctxt "%s - error description"
msgid "Parse error. %s"
msgstr "Error de análisis. %s"

#: includes/i-cal/background-processes/background-worker.php:468
msgid "Skipped. Outdated booking #%d already removed."
msgstr "Omitida. La reserva desactualizada #%d ya se ha eliminado."

#: includes/i-cal/background-processes/background-worker.php:475
msgid "Skipped. Booking #%d updated with new data."
msgstr "Omitidoa La reserva #%d se ha actualizado con nuevos datos."

#: includes/i-cal/background-processes/background-worker.php:497
msgid "The outdated booking #%d has been removed."
msgstr "La reserva desactualizada #%d ya se ha eliminado."

#: includes/i-cal/importer.php:104
msgid "Skipped. Event from %1$s to %2$s has passed."
msgstr ""

#: includes/i-cal/importer.php:120
msgid "New booking #%1$d. The dates from %2$s to %3$s are now blocked."
msgstr ""

#: includes/i-cal/importer.php:140
msgid "Success. Booking #%d updated with new data."
msgstr "Acción correcta. Reserva #%d actualizada con nuevos datos."

#: includes/i-cal/importer.php:148
msgid "Skipped. The dates from %1$s to %2$s are already blocked."
msgstr "Omitida. Las fechas del %1$s al %2$s ya se han bloqueado."

#: includes/i-cal/importer.php:164
msgid "Success. Booking #%1$d updated with new data. Removed %2$d outdated booking."
msgid_plural "Success. Booking #%1$d updated with new data. Removed %2$d outdated bookings."
msgstr[0] "Acción correcta. Reserva #%1$d actualizada con nuevos datos. %2$d reserva desactualizada eliminada."
msgstr[1] "Acción correcta. Reserva #%1$d actualizada con nuevos datos. %2$d reservas desactualizadas eliminadas."

#: includes/i-cal/importer.php:166
msgid "Success. Booking #%1$d updated with new data."
msgstr "Acción correcta. Reserva #%1$d actualizada con nuevos datos."

#: includes/i-cal/importer.php:177
msgid "Cannot import new event. Dates from %1$s to %2$s are partially blocked by booking %3$s."
msgid_plural "Cannot import new event. Dates from %1$s to %2$s are partially blocked by bookings %3$s."
msgstr[0] "No se puede importar un nuevo evento. Las fechas del %1$s al %2$s están parcialmente bloqueadas por la reserva %3$s."
msgstr[1] "No se puede importar un nuevo evento. Las fechas del %1$s al %2$s están parcialmente bloqueadas por las reservas %3$s."

#: includes/i-cal/importer.php:233
msgid "Booking imported with UID %1$s.<br />Summary: %2$s.<br />Description: %3$s.<br />Source: %4$s."
msgstr "Reserva importada con UID %1$s.<br />Sumario: %2$s.<br />Descripción: %3$s.<br />Fuente: %4$s."

#: includes/i-cal/logs-handler.php:25
msgid "Process Information"
msgstr "Procesar información"

#: includes/i-cal/logs-handler.php:35
msgid "Total bookings: %s"
msgstr "Total reservas: %s"

#: includes/i-cal/logs-handler.php:37
msgid "Success bookings: %s"
msgstr "Reservas exitosas: %s"

#: includes/i-cal/logs-handler.php:39
msgid "Skipped bookings: %s"
msgstr "Reservas omitidas: %s"

#: includes/i-cal/logs-handler.php:41
msgid "Failed bookings: %s"
msgstr "Reservas Fallidas: %s"

#: includes/i-cal/logs-handler.php:43
msgid "Removed bookings: %s"
msgstr "Reservas eliminadas: %s"

#: includes/i-cal/logs-handler.php:87
msgid "Expand All"
msgstr "Expandir todas"

#: includes/i-cal/logs-handler.php:91
msgid "Collapse All"
msgstr "Desplegar todo"

#: includes/i-cal/logs-handler.php:138
msgid "All done! %1$d booking was successfully added."
msgid_plural "All done! %1$d bookings were successfully added."
msgstr[0] "¡Todo listo! %1$d reserva se ha agregado correctamente."
msgstr[1] "¡Todo listo! %1$d reservas se han agregado correctamente."

#: includes/i-cal/logs-handler.php:139
msgid " There was %2$d failure."
msgid_plural " There were %2$d failures."
msgstr[0] "Hubo %2$d error."
msgstr[1] "Hubo %2$d errores."

#: includes/license-notice.php:87
#: includes/license-notice.php:160
msgid "Your License Key is not active. Please, <a href=\"%s\">activate your License Key</a> to get plugin updates."
msgstr ""

#: includes/license-notice.php:152
msgid "Dismiss "
msgstr "Despedir "

#: includes/linked-rooms.php:31
msgid "Blocked because the linked accommodation is booked"
msgstr ""

#: includes/notices.php:138
#: includes/notices.php:156
#: includes/wizard.php:33
msgid "Hotel Booking Plugin"
msgstr "Plugin de Reserva de Hotel"

#: includes/notices.php:139
msgid "Your database is being updated in the background."
msgstr "Su base de datos se está actualizando."

#: includes/notices.php:141
msgid "Taking a while? Click here to run it now."
msgstr "¿No quiere esperar? Haga clic aquí para ejecutarlo ahora."

#: includes/notices.php:157
msgid "Add \"Booking Confirmation\" shortcode to your \"Booking Confirmed\" and \"Reservation Received\" pages to show more details about booking or payment.<br/>Click \"Update Pages\" to apply all changes automatically or skip this notice and add \"Booking Confirmation\" shortcode manually.<br/><b><em>This action will replace the whole content of the pages.</em></b>"
msgstr "Agregue el código de \"Confirmación de reserva\" a sus páginas \"Reserva confirmada\" y \"Reserva recibida\" para mostrar más detalles sobre la reserva o el pago.<br/>Haga clic en \"Actualizar páginas\" para aplicar todos los cambios automáticamente u omita este aviso y agregue el código de \"Confirmación de reserva\" manualmente.<br/><b><em> Esta acción reemplazará todo el contenido de las páginas.</em></b>"

#: includes/notices.php:159
msgid "Update Pages"
msgstr "Actualizar Páginas"

#: includes/notices.php:161
#: includes/wizard.php:36
msgid "Skip"
msgstr "Omitir"

#: includes/payments/gateways/bank-gateway.php:82
#: includes/payments/gateways/bank-gateway.php:91
msgid "Direct Bank Transfer"
msgstr "Transferencia bancaria directa"

#: includes/payments/gateways/bank-gateway.php:92
msgid "Make your payment directly into our bank account. Please use your Booking ID as the payment reference."
msgstr "Realice el pago directamente en nuestra cuenta bancaria. Utilice su ID de reserva como concepto de pago."

#: includes/payments/gateways/bank-gateway.php:118
msgid "Enable Auto-Abandonment"
msgstr ""

#: includes/payments/gateways/bank-gateway.php:119
msgid "Automatically abandon bookings and release reserved slots if payment is not received within a specified time period. You need to manually set the status of paid payments to Completed to avoid automatic abandonment."
msgstr ""

#: includes/payments/gateways/bank-gateway.php:128
msgid "Period of time in hours a user has to pay for a booking. Unpaid bookings become abandoned, and accommodations become available for others."
msgstr ""

#: includes/payments/gateways/beanstream-gateway.php:54
msgid "Beanstream/Bambora"
msgstr "Beanstream/Bambora"

#: includes/payments/gateways/beanstream-gateway.php:59
#: includes/payments/gateways/braintree-gateway.php:149
#: includes/payments/gateways/paypal-gateway.php:72
#: includes/payments/gateways/two-checkout-gateway.php:70
msgid "Use the card number %1$s with CVC %2$s and a valid expiration date to test a payment."
msgstr "Utilice el número de tarjeta %1$s con CVC %2$s y una fecha de caducidad válida para probar un pago."

#: includes/payments/gateways/beanstream-gateway.php:66
msgid "Pay by Card (Beanstream)"
msgstr "Pagar con tarjeta (Beanstream)"

#: includes/payments/gateways/beanstream-gateway.php:67
msgid "Pay with your credit card via Beanstream."
msgstr "Pague con su tarjeta de crédito vía Beanstream."

#: includes/payments/gateways/beanstream-gateway.php:85
#: includes/payments/gateways/braintree-gateway.php:194
#: includes/payments/gateways/stripe-gateway.php:226
msgid "%1$s is enabled, but the <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "%1$s se activó, pero la opción de <a href=\"%2$s\">Force Secure Checkout</a> está desactivada. Por favor, active SSL y asegúrese de que su servidor tenga un certificado SSL válido. De lo contrario, %1$s va a funcionar sólo en el modo de prueba."

#: includes/payments/gateways/beanstream-gateway.php:87
#: includes/payments/gateways/braintree-gateway.php:196
#: includes/payments/gateways/stripe-gateway.php:228
msgid "The <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "La opción de <a href=\"%2$s\">Force Secure Checkout</a> está desactivada. Por favor, active SSL y asegúrese de que su servidor tenga un certificado SSL válido. De lo contrario, %1$s va a funcionar sólo en el modo de prueba."

#: includes/payments/gateways/beanstream-gateway.php:90
msgid "Beanstream"
msgstr "Beanstream"

#: includes/payments/gateways/beanstream-gateway.php:103
#: includes/payments/gateways/braintree-gateway.php:212
msgid "Merchant ID"
msgstr "ID de comerciante"

#: includes/payments/gateways/beanstream-gateway.php:105
msgid "Your Merchant ID can be found in the top-right corner of the screen after logging in to the Beanstream Back Office"
msgstr "Su ID de comerciante se puede encontrar en la esquina superior derecha de la pantalla después del inicio de sesión en Beanstream Back Office"

#: includes/payments/gateways/beanstream-gateway.php:112
msgid "Payments Passcode"
msgstr "Contraseña de pago"

#: includes/payments/gateways/beanstream-gateway.php:114
msgid "To generate the passcode, navigate to Administration > Account Settings > Order Settings in the sidebar, then scroll to Payment Gateway > Security/Authentication"
msgstr "Para generar la contraseña, abra Administración > Configuración de cuenta > Configuración de pedido en la barra lateral y desplácese a Pasarela de pago > Seguridad / Autenticación"

#: includes/payments/gateways/beanstream-gateway.php:163
msgid "Beanstream Payment Error: %s"
msgstr "Error de pago a través de Beanstream: %s"

#: includes/payments/gateways/beanstream-gateway.php:201
msgid "Payment single use token is required."
msgstr "Contraseña de un solo uso de pago es obligatoria."

#: includes/payments/gateways/braintree-gateway.php:142
#: includes/payments/gateways/braintree-gateway.php:199
msgid "Braintree"
msgstr "Braintree"

#: includes/payments/gateways/braintree-gateway.php:155
#: includes/payments/gateways/stripe-gateway.php:86
msgid "Webhooks Destination URL: %s"
msgstr "URL de destino de Webhooks: %s"

#: includes/payments/gateways/braintree-gateway.php:168
msgid "Pay by Card (Braintree)"
msgstr "Pagar con tarjeta (Braintree)"

#: includes/payments/gateways/braintree-gateway.php:169
msgid "Pay with your credit card via Braintree."
msgstr "Pague con su tarjeta de crédito a través de Braintree."

#: includes/payments/gateways/braintree-gateway.php:189
msgid "Braintree gateway cannot be enabled due to some problems: %s"
msgstr "No se puede activar la pasarela de pago Braintree debido a algunos problemas: %s"

#: includes/payments/gateways/braintree-gateway.php:214
msgid "In your Braintree account select Account > My User > View Authorizations."
msgstr "En su cuenta de Braintree, haga clic en Account/Cuenta > My User/ Mi usuario > View Autorizations/Ver autorizaciones."

#: includes/payments/gateways/braintree-gateway.php:221
#: includes/payments/gateways/stripe-gateway.php:284
msgid "Public Key"
msgstr "Clave pública"

#: includes/payments/gateways/braintree-gateway.php:229
msgid "Private Key"
msgstr "Clave privada"

#: includes/payments/gateways/braintree-gateway.php:237
msgid "Merchant Account ID"
msgstr "ID de cuenta de comerciante"

#: includes/payments/gateways/braintree-gateway.php:238
msgid "In case the site currency differs from default currency in your Braintree account, you can set specific merchant account to avoid <a href=\"%s\">complications with currencty conversions</a>. Otherwise leave the field empty."
msgstr "En caso de que la moneda del sitio difiera de la moneda predeterminada en su cuenta de Braintree, Usted puede establecer una cuenta de comerciante específica para evitar <a href=\"%s\">complicaciones con conversiones de divisas</a>. De lo contrario, deje el campo vacío."

#: includes/payments/gateways/braintree-gateway.php:293
msgid "Braintree submitted for settlement (Transaction ID: %s)"
msgstr "Braintree presentó para la liquidación (ID de transacción: %s)"

#: includes/payments/gateways/braintree-gateway.php:303
msgid "Braintree Payment Error: %s"
msgstr "Error de pago a través de Braintree: %s"

#: includes/payments/gateways/braintree-gateway.php:330
msgid "Payment method nonce is required."
msgstr "Nonce de método de pago es obligatorio."

#: includes/payments/gateways/braintree/webhook-listener.php:116
msgid "Payment dispute opened"
msgstr "Se abrió un conflicto de pago"

#: includes/payments/gateways/braintree/webhook-listener.php:121
msgid "Payment dispute lost"
msgstr "Conflicto de pago perdido"

#: includes/payments/gateways/braintree/webhook-listener.php:126
msgid "Payment dispute won"
msgstr "Conflicto de pago ganado"

#: includes/payments/gateways/braintree/webhook-listener.php:143
msgid "Payment refunded in Braintree"
msgstr "Pago reembolsado en Braintree"

#: includes/payments/gateways/braintree/webhook-listener.php:147
msgid "Braintree transaction voided"
msgstr "Transacción de Braintree anulada"

#: includes/payments/gateways/cash-gateway.php:45
#: includes/payments/gateways/cash-gateway.php:53
msgid "Pay on Arrival"
msgstr "Pagar a la llegada"

#: includes/payments/gateways/cash-gateway.php:54
msgid "Pay with cash on arrival."
msgstr "Pagar en efectivo a la llegada."

#: includes/payments/gateways/gateway.php:301
msgid "%s is a required field."
msgstr "%s es un campo obligatorio."

#: includes/payments/gateways/gateway.php:314
msgid "%s is not a valid email address."
msgstr "%s es una dirección de correo electrónico no válida."

#. translators: %s is the payment gateway title.
#: includes/payments/gateways/gateway.php:472
msgid "Enable \"%s\""
msgstr "Activar \"%s\""

#: includes/payments/gateways/gateway.php:482
msgid "Test Mode"
msgstr "Modo de prueba"

#: includes/payments/gateways/gateway.php:483
msgid "Enable Sandbox Mode"
msgstr "Activar el modo de Sandbox"

#: includes/payments/gateways/gateway.php:485
msgid "Sandbox can be used to test payments."
msgstr "Se puede utilizar para probar los pagos."

#: includes/payments/gateways/gateway.php:496
msgid "Payment method title that the customer will see on your website."
msgstr "El título del método de pago que el cliente verá en su sitio web."

#: includes/payments/gateways/gateway.php:506
msgid "Payment method description that the customer will see on your website."
msgstr "La descripción del método de pago que el cliente verá en su sitio web."

#: includes/payments/gateways/gateway.php:516
msgid "Instructions"
msgstr "Instrucciones"

#: includes/payments/gateways/gateway.php:518
msgid "Instructions for a customer on how to complete the payment."
msgstr "Instrucciones para el cliente de cómo completar el pago."

#: includes/payments/gateways/gateway.php:543
msgid "Reservation #%d"
msgstr "Reserva #%d"

#: includes/payments/gateways/gateway.php:545
msgid "Accommodation(s) reservation"
msgstr "Reserva de alojamiento(s)"

#: includes/payments/gateways/manual-gateway.php:14
#: includes/payments/gateways/manual-gateway.php:19
msgid "Manual Payment"
msgstr "Pago a mano"

#: includes/payments/gateways/paypal-gateway.php:67
#: includes/payments/gateways/paypal-gateway.php:80
msgid "PayPal"
msgstr "PayPal"

#: includes/payments/gateways/paypal-gateway.php:81
msgid "Pay via PayPal"
msgstr "Pagar a través de PayPal"

#: includes/payments/gateways/paypal-gateway.php:117
msgid "Paypal Business Email"
msgstr "Email comercial de Paypal"

#: includes/payments/gateways/paypal-gateway.php:125
msgid "Disable IPN Verification"
msgstr "Desactivar la verificación IPN"

#: includes/payments/gateways/paypal-gateway.php:127
msgid "Specify an IPN listener for a specific payment instead of the listeners specified in your PayPal Profile."
msgstr "Especifique un recipiente IPN para un pago específico en lugar de los especificados en su perfil de PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:164
msgid "Payment %s via IPN."
msgstr "Pago %s vía IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:183
msgid "Payment failed due to invalid PayPal business email."
msgstr "El pago ha fallado debido al email comercial de PayPal no válido."

#: includes/payments/gateways/paypal/ipn-listener.php:200
msgid "Payment failed due to invalid currency in PayPal IPN."
msgstr "El pago ha fallado debido a la moneda no válida en IPN de PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:215
msgid "Payment failed due to invalid amount in PayPal IPN."
msgstr "El pago ha fallado debido a la cantidad no válida en IPN de PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:230
msgid "Payment failed due to invalid purchase key in PayPal IPN."
msgstr "El pago ha fallado debido a la clave de compra no válida en IPN de PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:302
msgid "Payment made via eCheck and will clear automatically in 5-8 days."
msgstr "El pago ha sido enviado vía eCheck y se aprueba automáticamente dentro de 5-8 días."

#: includes/payments/gateways/paypal/ipn-listener.php:307
msgid "Payment requires a confirmed customer address and must be accepted manually through PayPal."
msgstr "El pago requiere una dirección de cliente confirmada y debe ser aceptado manualmente a través de PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:312
msgid "Payment must be accepted manually through PayPal due to international account regulations."
msgstr "El pago debe ser aceptado manualmente a través de PayPal debido a las regulaciones de cuentas internacionales."

#: includes/payments/gateways/paypal/ipn-listener.php:317
msgid "Payment received in non-shop currency and must be accepted manually through PayPal."
msgstr "El pago ha sido recibido en una moneda que no se utiliza en la tienda y debe ser aceptado manualmente a través de PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:323
msgid "Payment is being reviewed by PayPal staff as high-risk or in possible violation of government regulations."
msgstr "El pago está siendo revisado por el personal de PayPal debido al alto riesgo o a la violación posible de las regulaciones gubernamentales."

#: includes/payments/gateways/paypal/ipn-listener.php:328
msgid "Payment was sent to unconfirmed or non-registered email address."
msgstr "El pago se envió a una dirección de correo electrónico no confirmada o no registrada."

#: includes/payments/gateways/paypal/ipn-listener.php:333
msgid "PayPal account must be upgraded before this payment can be accepted."
msgstr "La cuenta de PayPal debe ser actualizada antes de que se pueda aceptar este pago."

#: includes/payments/gateways/paypal/ipn-listener.php:338
msgid "PayPal account is not verified. Verify account in order to accept this payment."
msgstr "La cuenta de PayPal no ha sido verificada. Verifique esta cuenta para poder aceptar el pago."

#: includes/payments/gateways/paypal/ipn-listener.php:343
msgid "Payment is pending for unknown reasons. Contact PayPal support for assistance."
msgstr "El pago está pendiente por razones desconocidas. Póngase en contacto con el equipo de soporte de PayPal para obtener ayuda."

#: includes/payments/gateways/paypal/ipn-listener.php:363
msgid "Partial PayPal refund processed: %s"
msgstr "Reembolso parcial de PayPal procesado: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:370
msgid "PayPal Payment #%s Refunded for reason: %s"
msgstr "El pago #%s  a través de PayPal ha sido devuelto por esta razón: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:374
msgid "PayPal Refund Transaction ID: %s"
msgstr "ID de transacción de reembolso de PayPal: %s"

#: includes/payments/gateways/stripe-gateway.php:149
#: includes/payments/gateways/stripe-gateway.php:231
msgid "Stripe"
msgstr "Stripe"

#: includes/payments/gateways/stripe-gateway.php:186
msgid "Use the card number %1$s with CVC %2$s, a valid expiration date and random 5-digit ZIP-code to test a payment."
msgstr "Utilice el número de tarjeta %1$s con CVC %2$s, una fecha de vencimiento válida y un código postal aleatorio de 5 dígitos para probar un pago."

#: includes/payments/gateways/stripe-gateway.php:202
msgid "Pay by Card (Stripe)"
msgstr "Pagar con tarjeta (Stripe)"

#: includes/payments/gateways/stripe-gateway.php:203
msgid "Pay with your credit card via Stripe."
msgstr "Pague con su tarjeta de crédito vía Stripe."

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:240
#: includes/payments/gateways/stripe-gateway.php:259
#: includes/payments/gateways/stripe-gateway.php:688
msgid "Bancontact"
msgstr "Bancontact"

#: includes/payments/gateways/stripe-gateway.php:241
#: includes/payments/gateways/stripe-gateway.php:260
#: includes/payments/gateways/stripe-gateway.php:689
msgid "iDEAL"
msgstr "iDEAL"

#: includes/payments/gateways/stripe-gateway.php:242
#: includes/payments/gateways/stripe-gateway.php:261
#: includes/payments/gateways/stripe-gateway.php:690
msgid "Giropay"
msgstr "Giropay"

#: includes/payments/gateways/stripe-gateway.php:243
#: includes/payments/gateways/stripe-gateway.php:262
#: includes/payments/gateways/stripe-gateway.php:691
msgid "SEPA Direct Debit"
msgstr "Débito directo SEPA"

#. translators: %1$s - name of payment method, %2$s - currency codes
#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:244
#: includes/payments/gateways/stripe-gateway.php:269
#: includes/payments/gateways/stripe-gateway.php:276
#: includes/payments/gateways/stripe-gateway.php:692
msgid "Klarna"
msgstr ""

#. translators: %s - currency codes
#: includes/payments/gateways/stripe-gateway.php:252
msgid "The %s currency is selected in the main settings."
msgstr ""

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:258
msgid "%1$s support the following currencies: %2$s."
msgstr ""

#. translators: %1$s - name of payment method, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:268
msgid "%1$s supports: %2$s."
msgstr ""

#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:275
msgid "%s special restrictions."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:293
msgid "Secret Key"
msgstr "Clave secreta"

#: includes/payments/gateways/stripe-gateway.php:301
msgid "Webhook Secret"
msgstr "Secreto de webhook"

#: includes/payments/gateways/stripe-gateway.php:310
msgid "Payment Methods"
msgstr "Formas de pago"

#: includes/payments/gateways/stripe-gateway.php:311
msgid "Card Payments"
msgstr "Pagos con tarjeta"

#: includes/payments/gateways/stripe-gateway.php:322
msgid "Checkout Locale"
msgstr "Página de pago localizada"

#: includes/payments/gateways/stripe-gateway.php:325
msgid "Display Checkout in the user's preferred language, if available."
msgstr "Mostrar la página de pago en el idioma preferido del usuario, si está disponible."

#: includes/payments/gateways/stripe-gateway.php:395
msgid "The payment method is not selected."
msgstr "El método de pago no está seleccionado."

#: includes/payments/gateways/stripe-gateway.php:401
msgid "PaymentIntent ID is not set."
msgstr ""

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:430
msgid "Payment for PaymentIntent %s succeeded."
msgstr "Pago del intento de pago de %s realizado correctamente."

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:436
msgid "Payment for PaymentIntent %s is processing."
msgstr "El pago para PaymentIntent %s se está procesando."

#. translators: %1$s - Stripe PaymentIntent ID, %2$s - Stripe error message text
#: includes/payments/gateways/stripe-gateway.php:457
msgid "Failed to process PaymentIntent %1$s. %2$s"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:474
msgid "Can't charge the payment again: payment's flow already completed."
msgstr "No se puede volver a cobrar el pago: el flujo de pago ya se completó."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:496
msgid "Charge %s succeeded."
msgstr "Carga %s satisfactoria."

#. translators: %1$s - Stripe Charge ID; %2$s - payment price
#: includes/payments/gateways/stripe-gateway.php:503
msgid "Charge %1$s for %2$s created."
msgstr "Cobro de %1$s de %2$s creado."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:508
msgid "Charge %s failed."
msgstr "Error de carga %s."

#: includes/payments/gateways/stripe-gateway.php:515
msgid "Charge error. %s"
msgstr "Error de carga. %s"

#: includes/payments/gateways/stripe-gateway.php:529
msgid "Argentinean"
msgstr "Argentino"

#: includes/payments/gateways/stripe-gateway.php:530
msgid "Simplified Chinese"
msgstr "Chino simplificado"

#: includes/payments/gateways/stripe-gateway.php:531
msgid "Danish"
msgstr "Danés"

#: includes/payments/gateways/stripe-gateway.php:532
msgid "Dutch"
msgstr "Holandés"

#: includes/payments/gateways/stripe-gateway.php:533
msgid "English"
msgstr "Inglés"

#: includes/payments/gateways/stripe-gateway.php:534
msgid "Finnish"
msgstr "Finlandés"

#: includes/payments/gateways/stripe-gateway.php:535
msgid "French"
msgstr "Francés"

#: includes/payments/gateways/stripe-gateway.php:536
msgid "German"
msgstr "Alemán"

#: includes/payments/gateways/stripe-gateway.php:537
msgid "Italian"
msgstr "Italiano"

#: includes/payments/gateways/stripe-gateway.php:538
msgid "Japanese"
msgstr "Japonés"

#: includes/payments/gateways/stripe-gateway.php:539
msgid "Norwegian"
msgstr "Noruego"

#: includes/payments/gateways/stripe-gateway.php:540
msgid "Polish"
msgstr "Polaco"

#: includes/payments/gateways/stripe-gateway.php:541
msgid "Russian"
msgstr "Ruso"

#: includes/payments/gateways/stripe-gateway.php:542
msgid "Spanish"
msgstr "Español"

#: includes/payments/gateways/stripe-gateway.php:543
msgid "Swedish"
msgstr "Sueco"

#: includes/payments/gateways/stripe-gateway.php:571
msgid "PaymentIntent ID is missing."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:687
msgid "Card"
msgstr "Tarjeta"

#: includes/payments/gateways/stripe-gateway.php:694
msgid "Credit or debit card"
msgstr "Tarjeta de crédito o débito"

#: includes/payments/gateways/stripe-gateway.php:695
msgid "IBAN"
msgstr "IBAN"

#: includes/payments/gateways/stripe-gateway.php:696
msgid "Select iDEAL Bank"
msgstr "Seleccionar banco iDEAL"

#: includes/payments/gateways/stripe-gateway.php:698
msgid "You will be redirected to a secure page to complete the payment."
msgstr "Serás redirigido a una página segura para completar el pago."

#: includes/payments/gateways/stripe-gateway.php:699
msgid "By providing your IBAN and confirming this payment, you are authorizing this merchant and Stripe, our payment service provider, to send instructions to your bank to debit your account and your bank to debit your account in accordance with those instructions. You are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited."
msgstr "Al proporcionar su IBAN y confirmar este pago, usted autoriza a este comerciante y a Stripe, nuestro proveedor de servicios de pago, a enviar instrucciones a su banco para debitar su cuenta y a su banco para debitar su cuenta de acuerdo con esas instrucciones. Tiene derecho a un reembolso de su banco según los términos y condiciones de su acuerdo con su banco. Se debe reclamar un reembolso dentro de las 8 semanas a partir de la fecha en que se debitó su cuenta."

#. translators: %s - payment method type code like: card
#: includes/payments/gateways/stripe/stripe-api.php:172
msgid "Could not create PaymentIntent for a not allowed payment type: %s"
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:163
msgid "Webhook received. Payment %s was cancelled by the customer."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:174
msgid "Webhook received. Payment %s failed and couldn't be processed."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:200
msgid "Webhook received. Payment %s was successfully processed."
msgstr ""

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:215
msgid "Webhook received. Charge %s succeeded."
msgstr "Webhook recibido. Carga satisfactoria %s."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:227
msgid "Webhook received. Charge %s failed."
msgstr "Webhook recibido. Error de carga %s."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:238
msgid "Webhook received. The source %s is chargeable."
msgstr "Webhook recibido. La fuente %s es pagable."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:252
msgid "Webhook received. Payment source %s was cancelled by customer."
msgstr "Webhook recibido. El cliente canceló la fuente de pago %s."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:263
msgid "Webhook received. Payment source %s failed and couldn't be processed."
msgstr "Webhook recibido. La fuente de pago %s falló y no se pudo procesar."

#: includes/payments/gateways/test-gateway.php:46
#: includes/payments/gateways/test-gateway.php:52
msgid "Test Payment"
msgstr "Probar pago"

#: includes/payments/gateways/two-checkout-gateway.php:65
#: includes/payments/gateways/two-checkout-gateway.php:101
msgid "2Checkout"
msgstr "2Checkout"

#: includes/payments/gateways/two-checkout-gateway.php:88
msgid "To setup the callback process for 2Checkout to automatically mark payments completed, you will need to"
msgstr "Para configurar de que 2Checkout marque automáticamente los pagos como completados, Usted necesitará"

#: includes/payments/gateways/two-checkout-gateway.php:90
msgid "Login to your 2Checkout account and click the Notifications tab"
msgstr "Inicie sesión en su cuenta de 2Checkout y haga clic en la pestaña de notificaciones"

#: includes/payments/gateways/two-checkout-gateway.php:91
msgid "Click Enable All Notifications"
msgstr "Haga clic en Activar todas las notificaciones"

#: includes/payments/gateways/two-checkout-gateway.php:92
msgid "In the Global URL field, enter the url %s"
msgstr "En el campo de URL global, escriba la url %s"

#: includes/payments/gateways/two-checkout-gateway.php:93
msgid "Click Apply"
msgstr "Haga clic en Aplicar"

#: includes/payments/gateways/two-checkout-gateway.php:189
msgid "Account Number"
msgstr "Número de cuenta"

#: includes/payments/gateways/two-checkout-gateway.php:197
msgid "Secret Word"
msgstr "Palabra secreta"

#: includes/payments/gateways/two-checkout/ins-listener.php:68
msgid "2Checkout \"Order Created\" notification received."
msgstr "La notificación \"Pedido creado\" de 2Checkout ha sido recibida."

#: includes/payments/gateways/two-checkout/ins-listener.php:73
msgid "Payment refunded in 2Checkout"
msgstr "Pago reembolsado en 2Checkout"

#: includes/payments/gateways/two-checkout/ins-listener.php:80
msgid "2Checkout fraud review passed"
msgstr "La comprobación antifraude de 2Checkout está completada"

#: includes/payments/gateways/two-checkout/ins-listener.php:83
msgid "2Checkout fraud review failed"
msgstr "La comprobación antifraude de 2Checkout falló"

#: includes/payments/gateways/two-checkout/ins-listener.php:86
msgid "2Checkout fraud review in progress"
msgstr "La comprobación antifraude de 2Checkout está en curso"

#: includes/post-types/attributes-cpt.php:55
msgid "Attribute"
msgstr "Atributo"

#: includes/post-types/attributes-cpt.php:56
msgctxt "Add New Attribute"
msgid "Add New"
msgstr "Crear nuevo"

#: includes/post-types/attributes-cpt.php:57
msgid "Add New Attribute"
msgstr "Añadir nuevo atributo"

#: includes/post-types/attributes-cpt.php:58
msgid "Edit Attribute"
msgstr "Editar atributo"

#: includes/post-types/attributes-cpt.php:59
msgid "New Attribute"
msgstr "Nuevo atributo"

#: includes/post-types/attributes-cpt.php:60
msgid "View Attribute"
msgstr "Ver atributo"

#: includes/post-types/attributes-cpt.php:62
msgid "Search Attribute"
msgstr "Buscar atributo"

#: includes/post-types/attributes-cpt.php:63
msgid "No Attributes found"
msgstr "No se encontró ningún atributo"

#: includes/post-types/attributes-cpt.php:64
msgid "No Attributes found in Trash"
msgstr "No se encontró ningún atributo en la papelera"

#: includes/post-types/attributes-cpt.php:66
msgid "Insert into attribute description"
msgstr "Insertar en la descripción del atributo"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:157
msgid "Search %s"
msgstr "Buscar %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:159
msgid "All %s"
msgstr "Todos los %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:161
msgid "Edit %s"
msgstr "Editar %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:163
msgid "Update %s"
msgstr "Actualizar %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:165
msgid "Add new %s"
msgstr "Añadir nuevo %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:167
msgid "New %s"
msgstr "Nuevo %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:169
msgid "No &quot;%s&quot; found"
msgstr "No se encontró ningún &quot;%s&quot;"

#: includes/post-types/attributes-cpt.php:302
msgid "Name (numeric)"
msgstr "Nombre (numérico)"

#: includes/post-types/attributes-cpt.php:303
msgid "Term ID"
msgstr "Término ID"

#: includes/post-types/attributes-cpt.php:314
msgid "Enable Archives"
msgstr "Activar archivos"

#: includes/post-types/attributes-cpt.php:315
msgid "Link the attribute to an archive page with all accommodation types that have this attribute."
msgstr "Vincula el atributo a una página de archivo con todos los tipos de alojamiento que tienen este atributo."

#: includes/post-types/attributes-cpt.php:324
msgid "Visible in Details"
msgstr "Visible en Detalles"

#: includes/post-types/attributes-cpt.php:325
msgid "Display the attribute in details section of an accommodation type."
msgstr "Mostrar el atributo en la sección de detalles de un tipo de alojamiento."

#: includes/post-types/attributes-cpt.php:334
msgid "Default Sort Order"
msgstr "Orden de clasificación predeterminado"

#: includes/post-types/attributes-cpt.php:344
msgid "Default Text"
msgstr "Texto por defecto"

#: includes/post-types/attributes-cpt.php:355
msgid "Select"
msgstr "Seleccionar"

#: includes/post-types/booking-cpt.php:75
#: templates/edit-booking/edit-dates.php:24
msgid "Edit Dates"
msgstr "Editar fechas"

#: includes/post-types/booking-cpt.php:215
msgid "Note"
msgstr "Nota"

#: includes/post-types/booking-cpt.php:243
msgctxt "Add New Booking"
msgid "Add New Booking"
msgstr "Añadir nueva"

#: includes/post-types/booking-cpt.php:247
#: templates/emails/admin-customer-cancelled-booking.php:16
#: templates/emails/admin-customer-confirmed-booking.php:16
#: templates/emails/admin-payment-confirmed-booking.php:16
#: templates/emails/admin-pending-booking.php:16
#: templates/emails/customer-approved-booking.php:24
#: templates/emails/customer-pending-booking.php:26
msgid "View Booking"
msgstr "Ver reserva"

#: includes/post-types/booking-cpt.php:248
msgid "Search Booking"
msgstr "Buscar reserva"

#: includes/post-types/booking-cpt.php:249
msgid "No bookings found"
msgstr "No se encontraron reservas"

#: includes/post-types/booking-cpt.php:250
msgid "No bookings found in Trash"
msgstr "No se encontraron reservas en la Papelera"

#: includes/post-types/booking-cpt.php:251
msgid "All Bookings"
msgstr "Todas las reservas"

#: includes/post-types/booking-cpt.php:252
msgid "Insert into booking description"
msgstr "Insertar en la descripción de reserva"

#: includes/post-types/booking-cpt.php:253
msgid "Uploaded to this booking"
msgstr "Subido a esta reserva"

#: includes/post-types/booking-cpt/statuses.php:58
msgctxt "Booking status"
msgid "Pending User Confirmation"
msgstr "Requiere confirmación de usuario"

#: includes/post-types/booking-cpt/statuses.php:63
msgid "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgid_plural "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgstr[0] "Requiere confirmación de usuario <span class=\"count\">(%s)</span>"
msgstr[1] "Requieren confirmación de usuario <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:69
msgctxt "Booking status"
msgid "Pending Payment"
msgstr "Requiere pago"

#: includes/post-types/booking-cpt/statuses.php:74
msgid "Pending Payment <span class=\"count\">(%s)</span>"
msgid_plural "Pending Payment <span class=\"count\">(%s)</span>"
msgstr[0] "Requiere pago <span class=\"count\">(%s)</span>"
msgstr[1] "Requieren pagos <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:80
msgctxt "Booking status"
msgid "Pending Admin"
msgstr "Requiere acción de admin"

#: includes/post-types/booking-cpt/statuses.php:85
msgid "Pending Admin <span class=\"count\">(%s)</span>"
msgid_plural "Pending Admin <span class=\"count\">(%s)</span>"
msgstr[0] "Requiere acción de admin <span class=\"count\">(%s)</span>"
msgstr[1] "Requieren acción de admin <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:91
#: includes/reports/data/report-earnings-by-dates-data.php:31
msgctxt "Booking status"
msgid "Abandoned"
msgstr "Abandonada"

#: includes/post-types/booking-cpt/statuses.php:96
#: includes/post-types/payment-cpt/statuses.php:83
msgid "Abandoned <span class=\"count\">(%s)</span>"
msgid_plural "Abandoned <span class=\"count\">(%s)</span>"
msgstr[0] "Abandonada <span class=\"count\">(%s)</span>"
msgstr[1] "Abandonadas <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:107
msgid "Confirmed <span class=\"count\">(%s)</span>"
msgid_plural "Confirmed <span class=\"count\">(%s)</span>"
msgstr[0] "Confirmada <span class=\"count\">(%s)</span>"
msgstr[1] "Confirmadas <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:113
#: includes/reports/data/report-earnings-by-dates-data.php:30
msgctxt "Booking status"
msgid "Cancelled"
msgstr "Cancelada"

#: includes/post-types/booking-cpt/statuses.php:118
#: includes/post-types/payment-cpt/statuses.php:116
msgid "Cancelled <span class=\"count\">(%s)</span>"
msgid_plural "Cancelled <span class=\"count\">(%s)</span>"
msgstr[0] "Cancelada <span class=\"count\">(%s)</span>"
msgstr[1] "Canceladas <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:180
#: includes/post-types/payment-cpt/statuses.php:213
msgid "Status changed from %s to %s."
msgstr "El estado se cambió de %s a %s."

#: includes/post-types/coupon-cpt.php:45
msgid "A brief description to remind you what this code is for."
msgstr ""

#: includes/post-types/coupon-cpt.php:52
msgid "Conditions"
msgstr ""

#: includes/post-types/coupon-cpt.php:60
msgid "Apply a coupon code to selected accommodations in a booking. Leave blank to apply to all accommodations."
msgstr ""

#: includes/post-types/coupon-cpt.php:82
msgid "Percentage discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:83
msgid "Fixed discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:84
msgid "Fixed discount on daily/nightly price"
msgstr ""

#: includes/post-types/coupon-cpt.php:94
#: includes/post-types/coupon-cpt.php:125
#: includes/post-types/coupon-cpt.php:170
msgid "Enter percent or fixed amount according to selected type."
msgstr "Introduce el porcentaje o el monto fijo de acuerdo al tipo seleccionado."

#: includes/post-types/coupon-cpt.php:104
msgid "Service Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:137
msgid "Apply a coupon code to selected services in a booking. Leave blank to apply to all services."
msgstr ""

#: includes/post-types/coupon-cpt.php:149
msgid "Fee Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:180
msgid "Usage Restrictions"
msgstr ""

#: includes/post-types/coupon-cpt.php:195
msgid "Check-in After"
msgstr "Check-in Después"

#: includes/post-types/coupon-cpt.php:203
msgid "Check-out Before"
msgstr "Check-out Antes"

#: includes/post-types/coupon-cpt.php:211
msgid "Min days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:212
msgid "For early bird discount. The coupon code applies if a booking is made in a minimum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:222
msgid "Max days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:223
msgid "For last minute discount. The coupon code applies if a booking is made in a maximum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:233
msgid "Minimum Days"
msgstr "Días mínimos"

#: includes/post-types/coupon-cpt.php:243
msgid "Maximum Days"
msgstr "Días máximos"

#: includes/post-types/coupon-cpt.php:253
msgid "Usage Limit"
msgstr "Límite de Uso"

#: includes/post-types/coupon-cpt.php:263
msgid "Usage Count"
msgstr "Recuento de Uso"

#: includes/post-types/coupon-cpt.php:290
msgctxt "Add New Coupon"
msgid "Add New"
msgstr "Añadir nuevo"

#: includes/post-types/coupon-cpt.php:291
msgid "Add New Coupon"
msgstr "Agregar Nuevo Cupón"

#: includes/post-types/coupon-cpt.php:292
msgid "Edit Coupon"
msgstr "Editar Cupón"

#: includes/post-types/coupon-cpt.php:293
msgid "New Coupon"
msgstr "Nuevo Cupón"

#: includes/post-types/coupon-cpt.php:294
msgid "View Coupon"
msgstr "Ver Cupón"

#: includes/post-types/coupon-cpt.php:295
msgid "Search Coupon"
msgstr "Buscar Cupón"

#: includes/post-types/coupon-cpt.php:296
msgid "No coupons found"
msgstr "No se encontraron cupones"

#: includes/post-types/coupon-cpt.php:297
msgid "No coupons found in Trash"
msgstr "No se encontraron cupones en la papelera"

#: includes/post-types/coupon-cpt.php:298
msgid "All Coupons"
msgstr "Todos los cupones"

#: includes/post-types/payment-cpt.php:37
msgid "Payment History"
msgstr "Historia de pagos"

#: includes/post-types/payment-cpt.php:38
msgid "Payment"
msgstr "Pago"

#: includes/post-types/payment-cpt.php:39
msgctxt "Add New Payment"
msgid "Add New"
msgstr "Añadir nuevo"

#: includes/post-types/payment-cpt.php:40
msgid "Add New Payment"
msgstr "Añadir nuevo pago"

#: includes/post-types/payment-cpt.php:41
msgid "Edit Payment"
msgstr "Editar pago"

#: includes/post-types/payment-cpt.php:42
msgid "New Payment"
msgstr "Nuevo pago"

#: includes/post-types/payment-cpt.php:43
msgid "View Payment"
msgstr "Ver pago"

#: includes/post-types/payment-cpt.php:44
msgid "Search Payment"
msgstr "Buscar pago"

#: includes/post-types/payment-cpt.php:45
msgid "No payments found"
msgstr "No se encontraron pagos"

#: includes/post-types/payment-cpt.php:46
msgid "No payments found in Trash"
msgstr "No se encontraron pagos en la Papelera"

#: includes/post-types/payment-cpt.php:47
msgid "Payments"
msgstr "Pagos"

#: includes/post-types/payment-cpt.php:48
msgid "Insert into payment description"
msgstr "Insertar en la descripción de pago"

#: includes/post-types/payment-cpt.php:49
msgid "Uploaded to this payment"
msgstr "Subido a este pago"

#: includes/post-types/payment-cpt.php:54
msgid "Payments."
msgstr "Pagos."

#: includes/post-types/payment-cpt.php:169
msgid "Gateway Mode"
msgstr "Modo de pasarela"

#: includes/post-types/payment-cpt.php:171
msgid "Sandbox"
msgstr "Cuadro de envío"

#: includes/post-types/payment-cpt.php:172
msgid "Live"
msgstr "En vivo"

#: includes/post-types/payment-cpt.php:194
msgid "Fee"
msgstr "Pago"

#: includes/post-types/payment-cpt.php:215
msgid "Payment Type"
msgstr "Tipo de pago"

#: includes/post-types/payment-cpt.php:240
msgid "Billing Info"
msgstr "Información de facturación"

#: includes/post-types/payment-cpt.php:287
msgid "Address 1"
msgstr "Dirección 1"

#: includes/post-types/payment-cpt.php:295
msgid "Address 2"
msgstr "Dirección 2"

#: includes/post-types/payment-cpt.php:319
msgid "Postal Code (ZIP)"
msgstr "Código postal"

#: includes/post-types/payment-cpt/statuses.php:45
msgctxt "Payment status"
msgid "Pending"
msgstr "Pendiente"

#: includes/post-types/payment-cpt/statuses.php:50
msgid "Pending <span class=\"count\">(%s)</span>"
msgid_plural "Pending <span class=\"count\">(%s)</span>"
msgstr[0] "Pendiente <span class=\"count\">(%s)</span>"
msgstr[1] "Pendientes <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:56
msgctxt "Payment status"
msgid "Completed"
msgstr "Completado"

#: includes/post-types/payment-cpt/statuses.php:61
msgid "Completed <span class=\"count\">(%s)</span>"
msgid_plural "Completed <span class=\"count\">(%s)</span>"
msgstr[0] "Realizado <span class=\"count\">(%s)</span>"
msgstr[1] "Realizados <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:67
msgctxt "Payment status"
msgid "Failed"
msgstr "Fallado"

#: includes/post-types/payment-cpt/statuses.php:72
msgid "Failed <span class=\"count\">(%s)</span>"
msgid_plural "Failed <span class=\"count\">(%s)</span>"
msgstr[0] "Fallado <span class=\"count\">(%s)</span>"
msgstr[1] "Fallados <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:78
msgctxt "Payment status"
msgid "Abandoned"
msgstr "Abandonado"

#: includes/post-types/payment-cpt/statuses.php:89
msgctxt "Payment status"
msgid "On Hold"
msgstr "En espera"

#: includes/post-types/payment-cpt/statuses.php:94
msgid "On Hold <span class=\"count\">(%s)</span>"
msgid_plural "On Hold <span class=\"count\">(%s)</span>"
msgstr[0] "En espera <span class=\"count\">(%s)</span>"
msgstr[1] "En espera <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:100
msgctxt "Payment status"
msgid "Refunded"
msgstr "Reembolsado"

#: includes/post-types/payment-cpt/statuses.php:105
msgid "Refunded <span class=\"count\">(%s)</span>"
msgid_plural "Refunded <span class=\"count\">(%s)</span>"
msgstr[0] "Reembolsado <span class=\"count\">(%s)</span>"
msgstr[1] "Reembolsado <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:111
msgctxt "Payment status"
msgid "Cancelled"
msgstr "Cancelada"

#: includes/post-types/payment-cpt/statuses.php:180
msgid "Payment (#%s) for this booking is on hold"
msgstr "El pago (#%s) para esta reserva está en espera"

#: includes/post-types/rate-cpt.php:23
msgid "Rate Info"
msgstr "Info sobre tarifa"

#: includes/post-types/rate-cpt.php:49
#: includes/post-types/season-cpt.php:99
msgid "Season"
msgstr "Temporada"

#: includes/post-types/rate-cpt.php:72
msgid "Move price to top to set higher priority."
msgstr "Mover el precio a la parte superior para establecer una mayor prioridad."

#: includes/post-types/rate-cpt.php:84
msgid "Will be displayed on the checkout page."
msgstr "Se mostrará en la página de pago."

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/post-types/rate-cpt.php:103
#: includes/post-types/rate-cpt.php:113
msgid "Rates"
msgstr "Tarifas"

#: includes/post-types/rate-cpt.php:105
msgctxt "Add New Rate"
msgid "Add New"
msgstr "Añadir nueva"

#: includes/post-types/rate-cpt.php:106
msgid "Add New Rate"
msgstr "Añadir nueva tarifa"

#: includes/post-types/rate-cpt.php:107
msgid "Edit Rate"
msgstr "Editar tarifa"

#: includes/post-types/rate-cpt.php:108
msgid "New Rate"
msgstr "Nueva tarifa"

#: includes/post-types/rate-cpt.php:109
msgid "View Rate"
msgstr "Ver tarifa"

#: includes/post-types/rate-cpt.php:110
msgid "Search Rate"
msgstr "Buscar tarifa"

#: includes/post-types/rate-cpt.php:111
msgid "No rates found"
msgstr "Tasas no encontradas"

#: includes/post-types/rate-cpt.php:112
msgid "No rates found in Trash"
msgstr "No se encontraron tarifas en la Papelera"

#: includes/post-types/rate-cpt.php:114
msgid "Insert into rate description"
msgstr "Insertar en la descripción de la tarifa"

#: includes/post-types/rate-cpt.php:115
msgid "Uploaded to this rate"
msgstr "Subido a esta tasa"

#: includes/post-types/rate-cpt.php:120
msgid "This is where you can add new rates."
msgstr "Aquí es donde puede agregar nuevas tarifas."

#: includes/post-types/reserved-room-cpt.php:23
msgid "Reserved Accommodation"
msgstr "Alojamiento reservado"

#: includes/post-types/room-cpt.php:33
msgctxt "Add New Accommodation"
msgid "Add New"
msgstr "Añadir nuevo"

#: includes/post-types/room-cpt.php:34
msgid "Add New Accommodation"
msgstr "Añadir nuevo alojamiento"

#: includes/post-types/room-cpt.php:35
msgid "Edit Accommodation"
msgstr "Editar alojamiento"

#: includes/post-types/room-cpt.php:36
msgid "New Accommodation"
msgstr "Nuevo alojamiento"

#: includes/post-types/room-cpt.php:37
msgid "View Accommodation"
msgstr "Ver alojamiento"

#: includes/post-types/room-cpt.php:38
msgid "Search Accommodation"
msgstr "Buscar alojamiento"

#: includes/post-types/room-cpt.php:39
#: templates/create-booking/results/rooms-found.php:21
#: templates/shortcodes/search-results/results-info.php:19
msgid "No accommodations found"
msgstr "No se encontraron alojamientos"

#: includes/post-types/room-cpt.php:40
msgid "No accommodations found in Trash"
msgstr "No se encontraron alojamientos en la Papelera"

#: includes/post-types/room-cpt.php:42
msgid "Insert into accommodation description"
msgstr "Insertar en la descripción de alojamiento"

#: includes/post-types/room-cpt.php:43
msgid "Uploaded to this accommodation"
msgstr "Subido a este alojamiento"

#: includes/post-types/room-cpt.php:48
msgid "This is where you can add new accommodations to your hotel."
msgstr "Aquí se puede agregar nuevos alojamientos a su hotel."

#: includes/post-types/room-cpt.php:106
msgid "Automatically block current accommodation when the selected ones are booked"
msgstr ""

#: includes/post-types/room-type-cpt.php:55
msgctxt "Add New Accommodation Type"
msgid "Add Accommodation Type"
msgstr "Añadir tipo de alojamiento"

#: includes/post-types/room-type-cpt.php:56
msgid "Add New Accommodation Type"
msgstr "Añadir nuevo tipo de alojamiento"

#: includes/post-types/room-type-cpt.php:57
msgid "Edit Accommodation Type"
msgstr "Editar tipo de alojamiento"

#: includes/post-types/room-type-cpt.php:58
msgid "New Accommodation Type"
msgstr "Nuevo tipo de alojamiento"

#: includes/post-types/room-type-cpt.php:59
msgid "View Accommodation Type"
msgstr "Ver tipo de alojamiento"

#: includes/post-types/room-type-cpt.php:61
msgid "Search Accommodation Type"
msgstr "Buscar tipo de alojamiento"

#: includes/post-types/room-type-cpt.php:62
msgid "No Accommodation types found"
msgstr "No se encontraron tipos de alojamiento"

#: includes/post-types/room-type-cpt.php:63
msgid "No Accommodation types found in Trash"
msgstr "No se encontraron tipos de alojamiento en la Papelera"

#: includes/post-types/room-type-cpt.php:65
msgid "Insert into accommodation type description"
msgstr "Insertar en la descripción del tipo de alojamiento"

#: includes/post-types/room-type-cpt.php:66
msgid "Uploaded to this accommodation type"
msgstr "Subido a este tipo de alojamiento"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:83
msgctxt "slug"
msgid "accommodation"
msgstr "accommodation"

#: includes/post-types/room-type-cpt.php:108
msgid "Accommodation Categories"
msgstr "Categorías de alojamiento"

#: includes/post-types/room-type-cpt.php:109
msgid "Accommodation Category"
msgstr "Categoría de alojamiento"

#: includes/post-types/room-type-cpt.php:110
msgid "Search Accommodation Categories"
msgstr "Buscar categorías de alojamiento"

#: includes/post-types/room-type-cpt.php:111
msgid "Popular Accommodation Categories"
msgstr "Categorías de alojamiento populares"

#: includes/post-types/room-type-cpt.php:112
msgid "All Accommodation Categories"
msgstr "Todas las categorías de alojamiento"

#: includes/post-types/room-type-cpt.php:113
msgid "Parent Accommodation Category"
msgstr "Categoría de alojamiento principal"

#: includes/post-types/room-type-cpt.php:114
msgid "Parent Accommodation Category:"
msgstr "Categoría de alojamiento principal:"

#: includes/post-types/room-type-cpt.php:115
msgid "Edit Accommodation Category"
msgstr "Editar categoría de alojamiento"

#: includes/post-types/room-type-cpt.php:116
msgid "Update Accommodation Category"
msgstr "Actualizar categoría de alojamiento"

#: includes/post-types/room-type-cpt.php:117
msgid "Add New Accommodation Category"
msgstr "Añadir nueva categoría de alojamiento"

#: includes/post-types/room-type-cpt.php:118
msgid "New Accommodation Category Name"
msgstr "Nombre de nueva categoría de alojamiento"

#: includes/post-types/room-type-cpt.php:119
msgid "Separate categories with commas"
msgstr "Separe categorías con comas"

#: includes/post-types/room-type-cpt.php:120
msgid "Add or remove categories"
msgstr "Añada o elimine categorías"

#: includes/post-types/room-type-cpt.php:121
msgid "Choose from the most used categories"
msgstr "Elija entre las categorías más utilizadas"

#: includes/post-types/room-type-cpt.php:122
msgid "No categories found."
msgstr "No se encontraron categorías."

#: includes/post-types/room-type-cpt.php:123
#: assets/blocks/blocks.js:816
msgid "Categories"
msgstr "Categorías"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:138
msgctxt "slug"
msgid "accommodation-category"
msgstr "accommodation-category"

#: includes/post-types/room-type-cpt.php:163
msgid "Accommodation Tags"
msgstr "Etiquetas del alojamiento"

#: includes/post-types/room-type-cpt.php:164
msgid "Accommodation Tag"
msgstr "Etiqueta del alojamiento"

#: includes/post-types/room-type-cpt.php:165
msgid "Search Accommodation Tags"
msgstr "Buscar etiquetas del alojamiento"

#: includes/post-types/room-type-cpt.php:166
msgid "Popular Accommodation Tags"
msgstr "Etiquetas del alojamiento populares"

#: includes/post-types/room-type-cpt.php:167
msgid "All Accommodation Tags"
msgstr "Todas las etiquetas del alojamiento"

#: includes/post-types/room-type-cpt.php:168
msgid "Parent Accommodation Tag"
msgstr "Razón de la etiqueta del alojamiento"

#: includes/post-types/room-type-cpt.php:169
msgid "Parent Accommodation Tag:"
msgstr "Razón de la etiqueta del alojamiento:"

#: includes/post-types/room-type-cpt.php:170
msgid "Edit Accommodation Tag"
msgstr "Editar etiqueta de alojamiento"

#: includes/post-types/room-type-cpt.php:171
msgid "Update Accommodation Tag"
msgstr "Actualizar etiqueta de alojamiento"

#: includes/post-types/room-type-cpt.php:172
msgid "Add New Accommodation Tag"
msgstr "Añadir nueva etiqueta de alojamiento"

#: includes/post-types/room-type-cpt.php:173
msgid "New Accommodation Tag Name"
msgstr "Nuevo nombre de etiqueta de alojamiento"

#: includes/post-types/room-type-cpt.php:174
msgid "Separate tags with commas"
msgstr "Etiquetas separadas con comas"

#: includes/post-types/room-type-cpt.php:175
msgid "Add or remove tags"
msgstr "Añada o elimine etiquetas"

#: includes/post-types/room-type-cpt.php:176
msgid "Choose from the most used tags"
msgstr "Elija de las etiquetas más usadas"

#: includes/post-types/room-type-cpt.php:177
msgid "No tags found."
msgstr "Etiquetas no encontradas."

#: includes/post-types/room-type-cpt.php:178
#: assets/blocks/blocks.js:828
msgid "Tags"
msgstr "Etiquetas"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:192
msgctxt "slug"
msgid "accommodation-tag"
msgstr "accommodation-tag"

#: includes/post-types/room-type-cpt.php:217
#: includes/post-types/room-type-cpt.php:232
msgid "Amenities"
msgstr "Servicios"

#: includes/post-types/room-type-cpt.php:218
msgid "Amenity"
msgstr "Comodidad"

#: includes/post-types/room-type-cpt.php:219
msgid "Search Amenities"
msgstr "Buscar servicios"

#: includes/post-types/room-type-cpt.php:220
msgid "Popular Amenities"
msgstr "Servicios populares"

#: includes/post-types/room-type-cpt.php:221
msgid "All Amenities"
msgstr "Todos los servicios"

#: includes/post-types/room-type-cpt.php:222
msgid "Parent Amenity"
msgstr "Comodidad Padre"

#: includes/post-types/room-type-cpt.php:223
msgid "Parent Amenity:"
msgstr "Comodidad Padre:"

#: includes/post-types/room-type-cpt.php:224
msgid "Edit Amenity"
msgstr "Editar comodidad"

#: includes/post-types/room-type-cpt.php:225
msgid "Update Amenity"
msgstr "Actualizar comodidad"

#: includes/post-types/room-type-cpt.php:226
msgid "Add New Amenity"
msgstr "Agregar nueva comodidad"

#: includes/post-types/room-type-cpt.php:227
msgid "New Amenity Name"
msgstr "Nuevo nombre de comodidad"

#: includes/post-types/room-type-cpt.php:228
msgid "Separate amenities with commas"
msgstr "Separe comodidades con comas"

#: includes/post-types/room-type-cpt.php:229
msgid "Add or remove amenities"
msgstr "Añada o elimine comodidades"

#: includes/post-types/room-type-cpt.php:230
msgid "Choose from the most used amenities"
msgstr "Elija entre las comodidades más utilizadas"

#: includes/post-types/room-type-cpt.php:231
msgid "No amenities found."
msgstr "No se encontraron servicios."

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:247
msgctxt "slug"
msgid "accommodation-facility"
msgstr "accommodation-facility"

#: includes/post-types/room-type-cpt.php:293
msgid "State the age or disable children in <a href=\"%s\">settings</a>."
msgstr "Introduce la edad o desactiva los niños en <a href=\"%s\">la configuración</a>."

#: includes/post-types/room-type-cpt.php:303
msgid "Leave this option empty to calculate total capacity automatically to meet the exact number of adults AND children set above. This is the default behavior. Configure this option to allow any variations of adults OR children set above at checkout so that in total it meets the limit of manually set \"Capacity\". For example, configuration \"adults:5\", \"children:4\", \"capacity:5\" means the property can accommodate up to 5 adults, up to 4 children, but up to 5 guests in total (not 9)."
msgstr "Deje esta opción vacía para calcular la capacidad total automáticamente para cumplir con el número exacto de adultos Y niños establecido anteriormente. Este es el comportamiento predeterminado. Configure esta opción para permitir cualquier variación de adultos O niños establecida anteriormente en el momento del pago para que en total cumpla con el límite de \"Capacidad\" establecido manualmente. Por ejemplo, la configuración \"adultos: 5\", \"niños: 4\", \"capacidad: 5\" significa que la propiedad puede acomodar hasta 5 adultos, hasta 4 niños, pero hasta 5 huéspedes en total (no 9)."

#: includes/post-types/room-type-cpt.php:313
msgid "Base Adults Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:314
#: includes/post-types/room-type-cpt.php:325
msgid "An optional starting value used when creating seasonal prices in the Rates menu."
msgstr ""

#: includes/post-types/room-type-cpt.php:324
msgid "Base Children Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:334
msgid "Other"
msgstr "Otros"

#: includes/post-types/room-type-cpt.php:340
msgid "Size, %s"
msgstr "Tamaño, %s"

#: includes/post-types/room-type-cpt.php:341
msgid "Leave blank to hide."
msgstr "Dejar vacío para esconder."

#: includes/post-types/room-type-cpt.php:355
msgid "City view, seaside, swimming pool etc."
msgstr "Vista a la ciudad, al mar, a la piscina, etc."

#: includes/post-types/room-type-cpt.php:366
msgid "Bed type"
msgstr "Tipo de cama"

#: includes/post-types/room-type-cpt.php:369
msgid "Set bed types list in <a href=\"%link%\" target=\"_blank\">settings</a>."
msgstr "Configure la lista de tipos de camas en <a href=\"%link%\" target=\"_blank\">ajustes</a>."

#: includes/post-types/room-type-cpt.php:379
msgid "Photo Gallery"
msgstr "Fotogalería"

#: includes/post-types/room-type-cpt.php:390
#: includes/post-types/room-type-cpt.php:395
msgid "Available Services"
msgstr "Servicios disponibles"

#: includes/post-types/season-cpt.php:25
msgid "Season Info"
msgstr "Info de temporada"

#: includes/post-types/season-cpt.php:32
#: includes/reports/earnings-report.php:332
msgid "Start date"
msgstr "Fecha de inicio"

#: includes/post-types/season-cpt.php:45
#: includes/reports/earnings-report.php:344
msgid "End date"
msgstr "Fecha final"

#: includes/post-types/season-cpt.php:55
msgid "Applied for days"
msgstr "Aplicado para días"

#: includes/post-types/season-cpt.php:59
msgid "Hold Ctrl / Cmd to select multiple."
msgstr "Mantenga presionada la tecla Ctrl / Cmd para seleccionar múltiples."

#: includes/post-types/season-cpt.php:68
msgid "Annual repeats begin on the Start date of the season, for one year from the current date."
msgstr ""

#: includes/post-types/season-cpt.php:70
msgid "Does not repeat"
msgstr ""

#: includes/post-types/season-cpt.php:81
msgid "Repeat until date"
msgstr ""

#: includes/post-types/season-cpt.php:100
msgctxt "Add New Season"
msgid "Add New"
msgstr "Añadir nueva"

#: includes/post-types/season-cpt.php:101
msgid "Add New Season"
msgstr "Añadir nueva temporada"

#: includes/post-types/season-cpt.php:102
msgid "Edit Season"
msgstr "Editar temporada"

#: includes/post-types/season-cpt.php:103
msgid "New Season"
msgstr "Nueva temporada"

#: includes/post-types/season-cpt.php:104
msgid "View Season"
msgstr "Ver temporada"

#: includes/post-types/season-cpt.php:105
msgid "Search Season"
msgstr "Buscar temporada"

#: includes/post-types/season-cpt.php:106
msgid "No seasons found"
msgstr "No se encontraron temporadas"

#: includes/post-types/season-cpt.php:107
msgid "No seasons found in Trash"
msgstr "No se encontraron temporadas en la Papelera"

#: includes/post-types/season-cpt.php:109
msgid "Insert into season description"
msgstr "Insertar en la descripción de temporada"

#: includes/post-types/season-cpt.php:110
msgid "Uploaded to this season"
msgstr "Subido a esta temporada"

#: includes/post-types/season-cpt.php:115
msgid "This is where you can add new seasons."
msgstr "Aquí se puede agregar nuevas temporadas."

#: includes/post-types/service-cpt.php:92
#: includes/views/booking-view.php:206
msgid "Service"
msgstr "Servicio"

#: includes/post-types/service-cpt.php:93
msgctxt "Add New Service"
msgid "Add New"
msgstr "Añadir nuevo"

#: includes/post-types/service-cpt.php:94
msgid "Add New Service"
msgstr "Añadir nuevo servicio"

#: includes/post-types/service-cpt.php:95
msgid "Edit Service"
msgstr "Editar servicio"

#: includes/post-types/service-cpt.php:96
msgid "New Service"
msgstr "Nuevo servicio"

#: includes/post-types/service-cpt.php:97
msgid "View Service"
msgstr "Ver servicio"

#: includes/post-types/service-cpt.php:98
msgid "Search Service"
msgstr "Buscar servicio"

#: includes/post-types/service-cpt.php:99
msgid "No services found"
msgstr "No se encontraron servicios"

#: includes/post-types/service-cpt.php:100
msgid "No services found in Trash"
msgstr "No se encontraron servicios en la Papelera"

#: includes/post-types/service-cpt.php:102
msgid "Insert into service description"
msgstr "Insertar en la descripción del servicio"

#: includes/post-types/service-cpt.php:103
msgid "Uploaded to this service"
msgstr "Subido a este servicio"

#. translators: do not translate
#: includes/post-types/service-cpt.php:120
msgctxt "slug"
msgid "services"
msgstr "services"

#: includes/post-types/service-cpt.php:156
msgid "How many times the customer will be charged."
msgstr "Cuántas veces se cobrará al cliente."

#: includes/post-types/service-cpt.php:167
msgid "Minimum"
msgstr "Mínimo"

#: includes/post-types/service-cpt.php:181
msgid "Maximum"
msgstr "Máximo"

#: includes/post-types/service-cpt.php:193
msgid "Empty means unlimited"
msgstr "Vacío significa ilimitado"

#: includes/reports/data/report-earnings-by-dates-data.php:29
msgctxt "Booking status"
msgid "Pending"
msgstr "Pendiente"

#: includes/reports/earnings-report.php:91
msgid "Total Sales"
msgstr "Ventas totales"

#: includes/reports/earnings-report.php:94
msgid "Total Without Taxes"
msgstr "Total sin impuestos"

#: includes/reports/earnings-report.php:97
msgid "Total Fees"
msgstr "Tarifas totales"

#: includes/reports/earnings-report.php:100
msgid "Total Services"
msgstr "Servicios totales"

#: includes/reports/earnings-report.php:103
msgid "Total Discounts"
msgstr "Descuentos totales"

#: includes/reports/earnings-report.php:106
msgid "Total Bookings"
msgstr "Reservas totales"

#: includes/reports/earnings-report.php:289
#: includes/reports/report-filters.php:38
msgid "Revenue"
msgstr ""

#: includes/reports/earnings-report.php:352
#: includes/views/create-booking/checkout-view.php:56
#: includes/views/shortcodes/checkout-view.php:90
msgid "Apply"
msgstr "Aplicar"

#: includes/reports/earnings-report.php:496
msgid "From %s to %s"
msgstr "Del %s al %s"

#: includes/reports/report-filters.php:61
msgid "Today"
msgstr "Hoy"

#: includes/reports/report-filters.php:64
msgid "Yesterday"
msgstr "Ayer"

#: includes/reports/report-filters.php:67
msgid "This week"
msgstr "Esta semana"

#: includes/reports/report-filters.php:70
msgid "Last week"
msgstr "Semana pasada"

#: includes/reports/report-filters.php:73
msgid "Last 30 days"
msgstr "Últimos 30 días"

#: includes/reports/report-filters.php:76
msgid "This month"
msgstr "Este mes"

#: includes/reports/report-filters.php:79
msgid "Last month"
msgstr "El mes pasado"

#: includes/reports/report-filters.php:82
msgid "This quarter"
msgstr "Este trimestre"

#: includes/reports/report-filters.php:85
msgid "Last quarter"
msgstr "El trimestre pasado"

#: includes/reports/report-filters.php:88
msgid "This year"
msgstr "Este año"

#: includes/reports/report-filters.php:91
msgid "Last year"
msgstr "El año pasado"

#. translators: %s - original Rate title
#: includes/repositories/rate-repository.php:195
msgid "%s - copy"
msgstr "%s - copiar"

#: includes/script-managers/admin-script-manager.php:92
msgid "Accommodation Type Gallery"
msgstr "Galería del tipo de alojamiento"

#: includes/script-managers/admin-script-manager.php:93
msgid "Add Gallery To Accommodation Type"
msgstr "Añadir galería al tipo de alojamiento"

#: includes/script-managers/admin-script-manager.php:105
msgid "Display imported bookings."
msgstr "Mostrar reservas importadas."

#: includes/script-managers/admin-script-manager.php:106
msgid "Processing..."
msgstr "Procesando..."

#: includes/script-managers/admin-script-manager.php:107
msgid "Cancelling..."
msgstr "Cancelando..."

#: includes/script-managers/admin-script-manager.php:108
msgid "Want to delete?"
msgstr "¿Desea eliminarlo?"

#: includes/script-managers/public-script-manager.php:204
msgid "Not available"
msgstr "No disponible"

#: includes/script-managers/public-script-manager.php:205
msgid "This is earlier than allowed by our advance reservation rules."
msgstr "Esto es antes de lo permitido en nuestras reglas de reserva anticipada."

#: includes/script-managers/public-script-manager.php:206
msgid "This is later than allowed by our advance reservation rules."
msgstr "Esto es más tarde de lo permitido en nuestras reglas de reserva anticipada."

#: includes/script-managers/public-script-manager.php:210
msgid "Day in the past"
msgstr "Día en el pasado"

#: includes/script-managers/public-script-manager.php:211
msgid "Check-in date"
msgstr "Fecha de llegada"

#: includes/script-managers/public-script-manager.php:212
msgid "Less than min days stay"
msgstr "Menos de días mínimos de estadía"

#: includes/script-managers/public-script-manager.php:213
msgid "More than max days stay"
msgstr "Más de un máximo de días de estancia"

#: includes/script-managers/public-script-manager.php:215
msgid "Later than max date for current check-in date"
msgstr "Más tarde de la fecha máxima para la fecha actual de llegada"

#: includes/script-managers/public-script-manager.php:216
msgid "Rules:"
msgstr "Reglas:"

#: includes/script-managers/public-script-manager.php:217
msgid "Tokenisation failed: %s"
msgstr "Error de tokenización: %s"

#: includes/script-managers/public-script-manager.php:218
#: includes/script-managers/public-script-manager.php:219
msgid "%1$d &times; &ldquo;%2$s&rdquo; has been added to your reservation."
msgid_plural "%1$d &times; &ldquo;%2$s&rdquo; have been added to your reservation."
msgstr[0] "%1$d &times; &ldquo;%2$s&rdquo; se ha sido añadido a su reserva."
msgstr[1] "%1$d &times; &ldquo;%2$s&rdquo; se han sido añadidos a su reserva."

#: includes/script-managers/public-script-manager.php:220
#: includes/script-managers/public-script-manager.php:221
msgid "%s accommodation selected."
msgid_plural "%s accommodations selected."
msgstr[0] "%s alojamiento seleccionado."
msgstr[1] "%s alojamientos seleccionados."

#: includes/script-managers/public-script-manager.php:222
msgid "Coupon code is empty."
msgstr "El código del cupón está vacio"

#: includes/script-managers/public-script-manager.php:225
msgid "Select dates"
msgstr ""

#: includes/settings/main-settings.php:26
msgid "Dark Blue"
msgstr "Azul oscuro"

#: includes/settings/main-settings.php:27
msgid "Dark Green"
msgstr "Verde oscuro"

#: includes/settings/main-settings.php:28
msgid "Dark Red"
msgstr "Rojo oscuro"

#: includes/settings/main-settings.php:29
msgid "Grayscale"
msgstr "Escala de grises"

#: includes/settings/main-settings.php:30
msgid "Light Blue"
msgstr "Azul claro"

#: includes/settings/main-settings.php:31
msgid "Light Coral"
msgstr "Coral claro"

#: includes/settings/main-settings.php:32
msgid "Light Green"
msgstr "Verde claro"

#: includes/settings/main-settings.php:33
msgid "Light Yellow"
msgstr "Amarillo claro"

#: includes/settings/main-settings.php:34
msgid "Minimal Blue"
msgstr "Azul minimalista"

#: includes/settings/main-settings.php:35
msgid "Minimal Orange"
msgstr "Naranja minimalista"

#: includes/settings/main-settings.php:36
msgid "Minimal"
msgstr "Minimalista"

#: includes/settings/main-settings.php:38
msgid "Sky Blue"
msgstr "Azul del cielo"

#: includes/settings/main-settings.php:39
msgid "Slate Blue"
msgstr "Azul pizarra"

#: includes/settings/main-settings.php:40
msgid "Turquoise"
msgstr "Turquesa"

#: includes/shortcodes/account-shortcode.php:212
#: includes/views/shortcodes/checkout-view.php:22
msgid "Invalid login or password."
msgstr ""

#: includes/shortcodes/account-shortcode.php:221
msgid "Account data updated."
msgstr "Datos de la cuenta actualizados."

#: includes/shortcodes/account-shortcode.php:227
msgid "Password changed."
msgstr "Contraseña cambiada."

#: includes/shortcodes/account-shortcode.php:238
msgid "Dashboard"
msgstr "Panel de control"

#: includes/shortcodes/account-shortcode.php:240
msgid "Account"
msgstr "Cuenta"

#: includes/shortcodes/account-shortcode.php:241
msgid "Logout"
msgstr "Cerrar sesión"

#: includes/shortcodes/account-shortcode.php:279
msgid "Passwords do not match."
msgstr "Las contraseñas no coinciden."

#: includes/shortcodes/account-shortcode.php:282
msgid "Please, provide a valid current password."
msgstr "Por favor, proporciona una contraseña actual válida."

#: includes/shortcodes/account-shortcode.php:301
#: includes/views/shortcodes/checkout-view.php:54
msgid "Lost your password?"
msgstr ""

#: includes/shortcodes/booking-confirmation-shortcode.php:294
msgid "Payment:"
msgstr "Pago:"

#: includes/shortcodes/booking-confirmation-shortcode.php:302
msgid "Payment Method:"
msgstr "Forma de pago:"

#: includes/shortcodes/booking-confirmation-shortcode.php:315
#: templates/shortcodes/booking-details/booking-details.php:42
msgid "Status:"
msgstr "Estado:"

#: includes/shortcodes/checkout-shortcode.php:196
msgid "Bookings are disabled in the settings."
msgstr ""

#: includes/shortcodes/checkout-shortcode/step-booking.php:151
msgid "Checkout data is not valid."
msgstr "Los datos del pago no son válidos."

#: includes/shortcodes/checkout-shortcode/step-booking.php:449
msgid "Payment method is not valid."
msgstr "Método de pago incorrecto."

#: includes/shortcodes/checkout-shortcode/step-checkout.php:193
msgid "Accommodation count is not valid."
msgstr "Número de alojamientos no válido."

#: includes/shortcodes/checkout-shortcode/step.php:110
msgid "Accommodation is already booked."
msgstr "El alojamiento ya está reservado."

#: includes/shortcodes/checkout-shortcode/step.php:120
#: includes/shortcodes/checkout-shortcode/step.php:129
#: includes/shortcodes/checkout-shortcode/step.php:138
msgid "Reservation submitted"
msgstr "Reserva enviada"

#: includes/shortcodes/checkout-shortcode/step.php:121
msgid "Details of your reservation have just been sent to you in a confirmation email. Please check your inbox to complete booking."
msgstr "Los detalles de reserva han sido enviados a su email en un mensaje de confirmación. Por favor, revise su email para completar la reserva."

#: includes/shortcodes/checkout-shortcode/step.php:130
#: includes/shortcodes/checkout-shortcode/step.php:139
msgid "We received your booking request. Once it is confirmed we will notify you via email."
msgstr "Hemos recibido su solicitud de reserva. Una vez ella sea confirmada, le notificaremos por correo electrónico."

#: includes/shortcodes/room-rates-shortcode.php:104
#: template-functions.php:31
msgid "Choose dates to see relevant prices"
msgstr "Establezca las fechas para ver los precios adecuados"

#: includes/shortcodes/search-results-shortcode.php:766
msgid "Select from available accommodations."
msgstr "Elija entre alojamientos disponibles."

#: includes/shortcodes/search-results-shortcode.php:775
#: includes/shortcodes/search-results-shortcode.php:1013
#: template-functions.php:843
msgid "Confirm Reservation"
msgstr "Confirmar reserva"

#: includes/shortcodes/search-results-shortcode.php:804
msgid "Recommended for %d adult"
msgid_plural "Recommended for %d adults"
msgstr[0] "Se recomienda para %d adulto"
msgstr[1] "Se recomienda para %d adultos"

#: includes/shortcodes/search-results-shortcode.php:806
msgid " and %d child"
msgid_plural " and %d children"
msgstr[0] " y %d niño"
msgstr[1] " y %d niños"

#: includes/shortcodes/search-results-shortcode.php:809
msgid "Recommended for %d guest"
msgid_plural "Recommended for %d guests"
msgstr[0] "Recomendado para  %d invitado"
msgstr[1] "Recomendado para %d invitados"

#: includes/shortcodes/search-results-shortcode.php:891
msgid "Max occupancy:"
msgstr "Maxima Ocupación:"

#: includes/shortcodes/search-results-shortcode.php:910
msgid "%d child"
msgid_plural "%d children"
msgstr[0] "%d niño"
msgstr[1] "%d niños"

#: includes/shortcodes/search-results-shortcode.php:945
#: templates/create-booking/results/reserve-rooms.php:76
msgid "Reserve"
msgstr "Reservar"

#: includes/shortcodes/search-results-shortcode.php:1002
msgid "of %d accommodation available."
msgid_plural "of %d accommodations available."
msgstr[0] "de %d alojamiento disponible."
msgstr[1] "de %d alojamientos disponibles."

#. translators: Verb. To book an accommodation.
#: includes/shortcodes/search-results-shortcode.php:1012
#: template-functions.php:531
#: template-functions.php:544
msgid "Book"
msgstr "Hacer reserva"

#: includes/users-and-roles/customers.php:290
#: includes/users-and-roles/customers.php:383
msgid "Please, provide a valid email."
msgstr "Por favor, proporciona un correo electrónico válido."

#: includes/users-and-roles/customers.php:318
msgid "Could not create a customer."
msgstr "No se pudo crear un cliente."

#: includes/users-and-roles/customers.php:379
msgid "Could not retrieve a customer."
msgstr "No se pudo recuperar un cliente."

#: includes/users-and-roles/customers.php:531
#: includes/users-and-roles/customers.php:577
msgid "Please, provide a valid Customer ID."
msgstr "Por favor, proporciona un ID de cliente válido."

#: includes/users-and-roles/customers.php:563
msgid "A database error."
msgstr "Error de la base de datos."

#: includes/users-and-roles/customers.php:583
msgid "No customer was deleted."
msgstr "No se eliminó ningún cliente."

#: includes/users-and-roles/customers.php:694
#: includes/views/shortcodes/checkout-view.php:31
msgid "An account with this email already exists. Please, log in."
msgstr "Ya existe una cuenta con este correo electrónico. Por favor, inicia sesión."

#: includes/users-and-roles/roles.php:34
msgid "Hotel Manager"
msgstr "Gerente del hotel"

#: includes/users-and-roles/roles.php:41
msgid "Hotel Worker"
msgstr "Trabajador del hotel"

#: includes/users-and-roles/roles.php:48
msgid "Hotel Customer"
msgstr "Cliente del hotel"

#: includes/users-and-roles/user.php:54
msgid "Please provide a valid email address."
msgstr "Por favor, proporciona una dirección de correo electrónico válida."

#: includes/users-and-roles/user.php:69
msgid "Please enter a valid account username."
msgstr "Por favor, introduce un nombre de usuario de cuenta válido."

#: includes/users-and-roles/user.php:73
msgid "An account is already registered with that username. Please choose another."
msgstr "Una cuenta ya está registrada con ese nombre de usuario. Por favor, elige otro."

#: includes/users-and-roles/user.php:81
msgid "Please enter an account password."
msgstr "Por favor, introduce la contraseña de la cuenta."

#: includes/utils/date-utils.php:145
msgid "Sunday"
msgstr "Domingo"

#: includes/utils/date-utils.php:146
msgid "Monday"
msgstr "Lunes"

#: includes/utils/date-utils.php:147
msgid "Tuesday"
msgstr "Martes"

#: includes/utils/date-utils.php:148
msgid "Wednesday"
msgstr "Miércoles"

#: includes/utils/date-utils.php:149
msgid "Thursday"
msgstr "Jueves"

#: includes/utils/date-utils.php:150
msgid "Friday"
msgstr "Viernes"

#: includes/utils/date-utils.php:151
msgid "Saturday"
msgstr "Sábado"

#: includes/utils/parse-utils.php:135
msgid "Check-out date cannot be earlier than check-in date."
msgstr "La fecha de salida no puede ser anterior a la fecha de entrada."

#: includes/utils/parse-utils.php:159
msgid "Adults number is not valid"
msgstr "La cantidad de adultos no es válida"

#: includes/utils/parse-utils.php:183
msgid "Children number is not valid"
msgstr "La cantidad de niños no es válida"

#: includes/utils/taxes-and-fees-utils.php:27
msgctxt "Text about taxes and fees below the price."
msgid " (+taxes and fees)"
msgstr " (+impuestos y cargos)"

#. translators: %s is a tax value
#: includes/utils/taxes-and-fees-utils.php:56
msgctxt "Text about taxes and fees below the price."
msgid " (+%s taxes and fees)"
msgstr " (+%s impuestos y cargos)"

#: includes/utils/taxes-and-fees-utils.php:84
msgctxt "Text about taxes and fees below the price."
msgid " (includes taxes and fees)"
msgstr " (+%s impuestos y cargos)"

#: includes/views/booking-view.php:79
msgctxt "Accommodation type in price breakdown table. Example: #1 Double Room"
msgid "#%d %s"
msgstr "#%d %s"

#: includes/views/booking-view.php:82
msgid "Expand"
msgstr "Expandir"

#: includes/views/booking-view.php:91
#: includes/views/edit-booking/checkout-view.php:209
msgid "Rate: %s"
msgstr "Tarifa: %s"

#: includes/views/booking-view.php:125
msgid "Dates"
msgstr "Fechas"

#: includes/views/booking-view.php:207
#: includes/views/loop-room-type-view.php:39
#: includes/views/single-room-type-view.php:131
#: includes/widgets/rooms-widget.php:197
#: assets/blocks/blocks.js:484
#: assets/blocks/blocks.js:734
#: assets/blocks/blocks.js:1263
msgid "Details"
msgstr "Detalles"

#: includes/views/booking-view.php:380
msgid "Subtotal"
msgstr "Subtotal"

#: includes/views/booking-view.php:393
msgid "Coupon: %s"
msgstr "Cupón: %s"

#: includes/views/booking-view.php:412
msgid "Subtotal (excl. taxes)"
msgstr "Subtotal (excl. impuestos)"

#: includes/views/booking-view.php:422
msgid "Taxes"
msgstr "Impuestos"

#: includes/views/create-booking/checkout-view.php:55
#: includes/views/shortcodes/checkout-view.php:89
msgid "Coupon Code:"
msgstr "Código de Cupón"

#: includes/views/edit-booking/checkout-view.php:25
msgid "New Booking Details"
msgstr "Detalles de la nueva reserva"

#: includes/views/edit-booking/checkout-view.php:43
msgid "Original Booking Details"
msgstr "Detalles de la reserva original."

#: includes/views/edit-booking/checkout-view.php:154
#: includes/views/shortcodes/checkout-view.php:269
#: template-functions.php:794
#: templates/create-booking/search/search-form.php:111
#: templates/shortcodes/search/search-form.php:105
msgid "Children %s"
msgstr "Niños %s"

#: includes/views/edit-booking/checkout-view.php:232
#: templates/emails/reserved-room-details.php:30
msgid "Additional Services"
msgstr "Servicios adicionales"

#: includes/views/edit-booking/checkout-view.php:249
#: includes/views/reserved-room-view.php:26
#: template-functions.php:937
msgid "x %d guest"
msgid_plural "x %d guests"
msgstr[0] "x %d huesped"
msgstr[1] "x %d huéspedes"

#: includes/views/edit-booking/checkout-view.php:253
#: includes/views/reserved-room-view.php:31
#: template-functions.php:940
msgid "x %d time"
msgid_plural "x %d times"
msgstr[0] "x %d vez"
msgstr[1] "x %d veces"

#: includes/views/global-view.php:53
msgid "Accommodation pagination"
msgstr "Paginación de alojamiento"

#: includes/views/global-view.php:56
msgid "Services pagination"
msgstr "Paginación de servicios"

#: includes/views/loop-room-type-view.php:55
#: includes/views/single-room-type-view.php:147
#: templates/widgets/rooms/room-content.php:99
msgid "Categories:"
msgstr "Categorías:"

#: includes/views/loop-room-type-view.php:67
#: includes/views/single-room-type-view.php:159
#: templates/widgets/rooms/room-content.php:129
msgid "Amenities:"
msgstr "Servicios"

#: includes/views/loop-room-type-view.php:97
#: includes/views/loop-room-type-view.php:115
#: includes/views/single-room-type-view.php:189
#: includes/views/single-room-type-view.php:207
#: templates/widgets/rooms/room-content.php:67
#: templates/widgets/rooms/room-content.php:79
#: templates/widgets/search-availability/search-form.php:80
msgid "Guests:"
msgstr "Huéspedes:"

#: includes/views/loop-room-type-view.php:140
#: includes/views/single-room-type-view.php:232
#: templates/widgets/rooms/room-content.php:175
msgid "Bed Type:"
msgstr "Tipo de cama:"

#: includes/views/loop-room-type-view.php:164
#: includes/views/single-room-type-view.php:256
#: templates/widgets/rooms/room-content.php:159
msgid "View:"
msgstr "Vista:"

#: includes/views/loop-room-type-view.php:184
#: includes/views/single-room-type-view.php:276
#: template-functions.php:839
#: templates/widgets/rooms/room-content.php:227
msgid "Prices start at:"
msgstr "Precio desde:"

#: includes/views/loop-service-view.php:46
msgid "Price:"
msgstr "Precio:"

#: includes/views/shortcodes/checkout-view.php:49
msgid "Returning customer?"
msgstr "¿Ciente recurrente?"

#: includes/views/shortcodes/checkout-view.php:50
msgid "Click here to log in"
msgstr "Haga clic aquí para iniciar sesión"

#. translators: 1 - username;
#: includes/views/shortcodes/checkout-view.php:70
#: templates/account/dashboard.php:29
msgid "Hello %1$s (not %1$s? <a href=\"%2$s\">Log out</a>)."
msgstr "Hola %1$s (no %1$s? <a href=\"%2$s\">Cerrar sesión</a>)."

#: includes/views/shortcodes/checkout-view.php:182
msgid "Accommodation #%d"
msgstr "Alojamiento #%d"

#: includes/views/shortcodes/checkout-view.php:186
msgid "Accommodation Type:"
msgstr "Tipo de alojamiento:"

#: includes/views/shortcodes/checkout-view.php:324
msgid "Choose Rate"
msgstr "Elegir tarifa"

#: includes/views/shortcodes/checkout-view.php:397
msgid "Choose Additional Services"
msgstr "Elegir servicios adicionales"

#: includes/views/shortcodes/checkout-view.php:429
msgid "for "
msgstr "para "

#: includes/views/shortcodes/checkout-view.php:442
msgctxt "Example: Breakfast for X guest(s)"
msgid " guest(s)"
msgstr "huésped(es)"

#: includes/views/shortcodes/checkout-view.php:464
msgid "time(s)"
msgstr "vez/veces"

#: includes/views/shortcodes/checkout-view.php:533
msgctxt "I've read and accept the terms & conditions"
msgid "terms & conditions"
msgstr "términos y condiciones"

#: includes/views/shortcodes/checkout-view.php:536
msgctxt "I've read and accept the <tag>terms & conditions</tag>"
msgid "I've read and accept the %s"
msgstr "He leído y acepto los %s"

#: includes/views/shortcodes/checkout-view.php:579
msgid "Your Information"
msgstr "Su información"

#: includes/views/shortcodes/checkout-view.php:582
#: template-functions.php:696
#: templates/widgets/search-availability/search-form.php:24
msgid "Required fields are followed by %s"
msgstr "Los campos obligatorios son seguidos por %s"

#: includes/views/shortcodes/checkout-view.php:758
msgid "Create an account"
msgstr "Crear una cuenta"

#: includes/views/shortcodes/checkout-view.php:775
msgid "Payment Method"
msgstr "Método de pago"

#: includes/views/shortcodes/checkout-view.php:780
msgid "Sorry, it seems that there are no available payment methods."
msgstr "Lo sentimos, pero parece que no hay métodos de pago disponibles."

#: includes/views/shortcodes/checkout-view.php:874
#: templates/emails/admin-customer-cancelled-booking.php:32
#: templates/emails/admin-customer-confirmed-booking.php:32
#: templates/emails/admin-payment-confirmed-booking.php:39
#: templates/emails/admin-pending-booking.php:32
#: templates/emails/customer-approved-booking.php:28
#: templates/emails/customer-cancelled-booking.php:26
#: templates/emails/customer-confirmation-booking.php:32
#: templates/emails/customer-pending-booking.php:30
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:27
msgid "Total Price:"
msgstr "Precio total:"

#: includes/views/shortcodes/checkout-view.php:886
msgid "Deposit:"
msgstr "Depósito:"

#: includes/views/shortcodes/checkout-view.php:906
#: templates/shortcodes/booking-details/booking-details.php:25
#: templates/widgets/search-availability/search-form.php:35
msgid "Check-in:"
msgstr "Día de llegada:"

#: includes/views/shortcodes/checkout-view.php:913
msgctxt "from 10:00 am"
msgid "from"
msgstr "desde las"

#: includes/views/shortcodes/checkout-view.php:929
#: templates/shortcodes/booking-details/booking-details.php:29
#: templates/widgets/search-availability/search-form.php:54
msgid "Check-out:"
msgstr "Día de salida:"

#: includes/views/shortcodes/checkout-view.php:936
msgctxt "until 10:00 am"
msgid "until"
msgstr "hasta las"

#: includes/views/shortcodes/checkout-view.php:1012
#: templates/create-booking/checkout/checkout-form.php:42
msgid "Book Now"
msgstr "Reservar ahora"

#: includes/views/single-room-type-view.php:127
msgid "Availability"
msgstr "Disponibilidad"

#: includes/views/single-room-type-view.php:292
msgid "Reservation Form"
msgstr "Formulario de reserva"

#: includes/widgets/rooms-widget.php:24
msgid "Display Accommodation Types"
msgstr "Mostrar tipos de alojamientos"

#: includes/widgets/rooms-widget.php:169
#: includes/widgets/search-availability-widget.php:236
msgid "Title:"
msgstr "Título:"

#: includes/widgets/rooms-widget.php:189
#: assets/blocks/blocks.js:448
#: assets/blocks/blocks.js:698
#: assets/blocks/blocks.js:1227
msgid "Featured Image"
msgstr "Imagen destacada"

#: includes/widgets/rooms-widget.php:193
#: assets/blocks/blocks.js:472
#: assets/blocks/blocks.js:722
#: assets/blocks/blocks.js:1251
msgid "Excerpt (short description)"
msgstr "Mostrar descripción breve"

#: includes/widgets/rooms-widget.php:205
#: assets/blocks/blocks.js:770
#: assets/blocks/blocks.js:1299
msgid "Book Button"
msgstr "Botón \"Reservar\""

#: includes/widgets/search-availability-widget.php:50
#: includes/wizard.php:84
msgid "Search Availability"
msgstr "Buscar disponibilidad"

#: includes/widgets/search-availability-widget.php:53
msgid "Search Availability Form"
msgstr "Formulario de búsqueda de disponibilidad"

#: includes/widgets/search-availability-widget.php:240
msgid "Check-in Date:"
msgstr "Fecha de llegada:"

#: includes/widgets/search-availability-widget.php:241
#: includes/widgets/search-availability-widget.php:246
msgctxt "Date format tip"
msgid "Preset date. Formatted as %s"
msgstr "Fecha preestablecida. En formato %s"

#: includes/widgets/search-availability-widget.php:244
msgid "Check-out Date:"
msgstr "Fecha de salida:"

#: includes/widgets/search-availability-widget.php:249
msgid "Preset Adults:"
msgstr "Adultos preestablecidos:"

#: includes/widgets/search-availability-widget.php:257
msgid "Preset Children:"
msgstr "Niños preestablecidos:"

#: includes/widgets/search-availability-widget.php:265
msgid "Attributes:"
msgstr "Atributos:"

#: includes/wizard.php:34
msgid "Booking Confirmation and Search Results pages are required to handle bookings. Press \"Install Pages\" button to create and set up these pages. Dismiss this notice if you already installed them."
msgstr "Es necesario tener las páginas de confirmación de reserva y de resultados de búsqueda para poder gestionar las reservas. Haga clic en el botón \"Instalar páginas\" para crear y configurar estas páginas. Descarte este aviso si ya ha creado ellas."

#: includes/wizard.php:35
msgid "Install Pages"
msgstr "Instalar páginas"

#: includes/wizard.php:147
msgid "Booking Canceled"
msgstr "Reserva cancelada"

#: includes/wizard.php:148
msgid "Your reservation is canceled."
msgstr "Tu reservación se ha cancelado."

#: includes/wizard.php:183
msgid "Reservation Received"
msgstr "Reserva Recibida"

#: includes/wizard.php:196
msgid "Transaction Failed"
msgstr "Transacción fallida"

#: includes/wizard.php:197
msgid "Unfortunately, your transaction cannot be completed at this time. Please try again or contact us."
msgstr "Desafortunadamente, tu transacción no puede completarse en este momento. Por favor, inténtalo de nuevo o ponte en contacto con nosotros."

#: plugin.php:1100
msgid "Prices start at: %s"
msgstr "Precios inician desde: %s"

#: template-functions.php:563
msgid "View Details"
msgstr "Ver detalles"

#: template-functions.php:593
#: template-functions.php:652
msgid "Accommodation %s not found."
msgstr ""

#: template-functions.php:707
#: template-functions.php:716
#: templates/create-booking/search/search-form.php:43
#: templates/create-booking/search/search-form.php:63
#: templates/edit-booking/edit-dates.php:30
#: templates/edit-booking/edit-dates.php:39
#: templates/shortcodes/search/search-form.php:36
#: templates/shortcodes/search/search-form.php:56
#: templates/widgets/search-availability/search-form.php:36
#: templates/widgets/search-availability/search-form.php:55
msgctxt "Date format tip"
msgid "Formatted as %s"
msgstr "Formato %s"

#: template-functions.php:831
msgid "Reserve %1$s of %2$s available accommodations."
msgstr "Reserva %1$s de %2$s alojamientos disponibles."

#: template-functions.php:835
msgid "%s is available for selected dates."
msgstr "%s está disponible para las fechas seleccionadas."

#: template-functions.php:849
#: templates/edit-booking/edit-dates.php:46
msgid "Check Availability"
msgstr "Comprobar disponibilidad"

#: template-functions.php:910
msgid "Rate:"
msgstr "Tarifa:"

#: template-functions.php:930
msgid "Services:"
msgstr "Servicios:"

#: template-functions.php:953
msgid "Guest:"
msgstr "Huésped:"

#: template-functions.php:976
msgid "Payment ID"
msgstr "ID de pago"

#: template-functions.php:1008
msgid "Total Paid"
msgstr "Total pagado"

#: template-functions.php:1017
msgid "To Pay"
msgstr "A pagar"

#: template-functions.php:1042
msgid "Add Payment Manually"
msgstr "Añadir pago manualmente"

#: templates/account/account-details.php:78
msgid "Change Password"
msgstr "Cambiar contraseña"

#: templates/account/account-details.php:81
msgid "Old Password"
msgstr "Contraseña anterior"

#: templates/account/account-details.php:85
msgid "New Password"
msgstr "Nueva contraseña"

#: templates/account/account-details.php:89
msgid "Confirm New Password"
msgstr "Confirmar nueva contraseña"

#: templates/account/account-details.php:99
msgid "You are not allowed to access this page."
msgstr "No tiene spermiso para acceder a esta página."

#: templates/account/bookings.php:116
#: templates/account/bookings.php:121
msgid "No bookings found."
msgstr "No se encontraron reservas."

#: templates/account/dashboard.php:41
msgid "From your account dashboard you can view <a href=\"%1$s\">your recent bookings</a> or edit your <a href=\"%2$s\">password and account details</a>."
msgstr "En el panel de control de tu cuenta, puedes ver <a href=\"%1$s\">tus reservas recientes</a> o editar la <a href=\"%2$s\">contraseña y los detalles de tu cuenta</a>."

#: templates/create-booking/results/reserve-rooms.php:37
msgid "Base price"
msgstr "Precio base"

#: templates/create-booking/results/rooms-found.php:19
#: templates/shortcodes/search-results/results-info.php:17
msgid "%s accommodation found"
msgid_plural "%s accommodations found"
msgstr[0] "Se encontró %s alojamiento"
msgstr[1] "Se encontraron %s alojamientos"

#: templates/create-booking/results/rooms-found.php:24
#: templates/shortcodes/search-results/results-info.php:21
msgid " from %s - till %s"
msgstr " desde %s - hasta %s"

#: templates/edit-booking/add-room-popup.php:24
#: templates/edit-booking/edit-reserved-rooms.php:36
msgid "Add Accommodation"
msgstr "Añadir alojamiento"

#: templates/edit-booking/checkout-form.php:28
msgid "Save"
msgstr "Guardar"

#: templates/edit-booking/edit-dates.php:25
msgid "Choose new dates to check availability of reserved accommodations in the original booking."
msgstr "Elige nuevas fechas para comprobar la disponibilidad del alojamiento en la reserva original."

#: templates/edit-booking/edit-reserved-rooms.php:39
msgid "Add, remove or replace accommodations in the original booking."
msgstr "Añade, elimina o sustituye un alojamiento en la reserva original."

#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Not Available"
msgstr "No disponible"

#: templates/edit-booking/edit-reserved-rooms.php:79
#: templates/edit-booking/summary-table.php:65
msgid "Continue"
msgstr "Continuar"

#: templates/edit-booking/summary-table.php:26
msgid "Choose how to associate data"
msgstr "Elige como asociar los datos"

#: templates/edit-booking/summary-table.php:27
msgid "Use Source Accommodation to assign pre-filled booking information available in the original booking, e.g., full guest name, selected rate, services, etc."
msgstr "Utilice Alojamiento de Origen para asignar información de reserva completada previamente disponible en la reserva original, por ejemplo, nombre completo del huésped, tarifa seleccionada, servicios, etc."

#: templates/edit-booking/summary-table.php:32
msgid "Source accommodation"
msgstr "Alojamiento de origen"

#: templates/edit-booking/summary-table.php:34
msgid "Target accommodation"
msgstr "Alojamiento de destino"

#: templates/emails/admin-customer-cancelled-booking.php:15
msgid "Booking #%s is cancelled by customer."
msgstr "Reserva #%s ha sido cancelada por el cliente."

#: templates/emails/admin-customer-cancelled-booking.php:17
#: templates/emails/admin-customer-confirmed-booking.php:17
#: templates/emails/admin-payment-confirmed-booking.php:24
#: templates/emails/admin-pending-booking.php:17
#: templates/emails/customer-approved-booking.php:17
#: templates/emails/customer-cancelled-booking.php:18
#: templates/emails/customer-confirmation-booking.php:24
#: templates/emails/customer-pending-booking.php:19
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:19
msgid "Details of booking"
msgstr "Detalles de reserva"

#: templates/emails/admin-customer-cancelled-booking.php:18
#: templates/emails/admin-customer-confirmed-booking.php:18
#: templates/emails/admin-payment-confirmed-booking.php:25
#: templates/emails/admin-pending-booking.php:18
#: templates/emails/customer-approved-booking.php:20
#: templates/emails/customer-cancelled-booking.php:21
#: templates/emails/customer-confirmation-booking.php:27
#: templates/emails/customer-pending-booking.php:22
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:22
msgid "Check-in: %1$s, from %2$s"
msgstr "Llegada: : %1$s, hasta las %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:20
#: templates/emails/admin-customer-confirmed-booking.php:20
#: templates/emails/admin-payment-confirmed-booking.php:27
#: templates/emails/admin-pending-booking.php:20
#: templates/emails/customer-approved-booking.php:22
#: templates/emails/customer-cancelled-booking.php:23
#: templates/emails/customer-confirmation-booking.php:29
#: templates/emails/customer-pending-booking.php:24
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:24
msgid "Check-out: %1$s, until %2$s"
msgstr "Salida: %1$s, hasta las %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:24
#: templates/emails/admin-customer-confirmed-booking.php:24
#: templates/emails/admin-payment-confirmed-booking.php:31
#: templates/emails/admin-pending-booking.php:24
#: templates/emails/customer-approved-booking.php:32
#: templates/emails/customer-cancelled-booking.php:30
#: templates/emails/customer-confirmation-booking.php:36
#: templates/emails/customer-pending-booking.php:33
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:31
msgid "Name: %1$s %2$s"
msgstr "Nombre: %1$s %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:26
#: templates/emails/admin-customer-confirmed-booking.php:26
#: templates/emails/admin-payment-confirmed-booking.php:33
#: templates/emails/admin-pending-booking.php:26
#: templates/emails/customer-approved-booking.php:34
#: templates/emails/customer-cancelled-booking.php:32
#: templates/emails/customer-confirmation-booking.php:38
#: templates/emails/customer-pending-booking.php:35
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:33
msgid "Email: %s"
msgstr "Correo electrónico: %s"

#: templates/emails/admin-customer-cancelled-booking.php:28
#: templates/emails/admin-customer-confirmed-booking.php:28
#: templates/emails/admin-payment-confirmed-booking.php:35
#: templates/emails/admin-pending-booking.php:28
#: templates/emails/customer-approved-booking.php:36
#: templates/emails/customer-cancelled-booking.php:34
#: templates/emails/customer-confirmation-booking.php:40
#: templates/emails/customer-pending-booking.php:37
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:35
msgid "Phone: %s"
msgstr "Teléfono: %s"

#: templates/emails/admin-customer-cancelled-booking.php:30
#: templates/emails/admin-customer-confirmed-booking.php:30
#: templates/emails/admin-payment-confirmed-booking.php:37
#: templates/emails/admin-pending-booking.php:30
#: templates/emails/customer-approved-booking.php:38
#: templates/emails/customer-cancelled-booking.php:36
#: templates/emails/customer-confirmation-booking.php:42
#: templates/emails/customer-pending-booking.php:39
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:37
msgid "Note: %s"
msgstr "Nota: %s"

#: templates/emails/admin-customer-confirmed-booking.php:15
msgid "Booking #%s is confirmed by customer."
msgstr "La reserva #%s ha sido confirmada por el cliente."

#: templates/emails/admin-payment-confirmed-booking.php:15
msgid "Booking #%s is confirmed by payment."
msgstr "La reserva #%s ha sido confirmada por el pago."

#: templates/emails/admin-payment-confirmed-booking.php:17
msgid "Details of payment"
msgstr "Detalles de pago"

#: templates/emails/admin-payment-confirmed-booking.php:18
msgid "Payment ID: #%s"
msgstr "ID de pago: #%s"

#: templates/emails/admin-payment-confirmed-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:16
msgid "Amount: %s"
msgstr "Cantidad: %s"

#: templates/emails/admin-payment-confirmed-booking.php:22
msgid "Method: %s"
msgstr "Método: %s"

#: templates/emails/admin-pending-booking.php:15
msgid "Booking #%s is pending for Administrator approval."
msgstr "La reserva #%s requiere la aprobación del administrador."

#: templates/emails/cancellation-details.php:14
msgid "Click the link below to cancel your booking."
msgstr "Haga clic en el enlace para cancelar su reserva."

#: templates/emails/cancellation-details.php:16
msgid "Cancel your booking"
msgstr "Cancelar reserva"

#: templates/emails/customer-approved-booking.php:15
msgid "Dear %1$s %2$s, your reservation is approved!"
msgstr "Estimado/a %1$s %2$s, su reserva está aprobada!"

#: templates/emails/customer-approved-booking.php:18
#: templates/emails/customer-cancelled-booking.php:19
#: templates/emails/customer-confirmation-booking.php:25
#: templates/emails/customer-pending-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:20
msgid "ID: #%s"
msgstr "ID: #%s"

#: templates/emails/customer-approved-booking.php:41
#: templates/emails/customer-cancelled-booking.php:38
#: templates/emails/customer-confirmation-booking.php:44
#: templates/emails/customer-pending-booking.php:41
#: templates/emails/customer-registration.php:26
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:39
msgid "Thank you!"
msgstr "¡Gracias!"

#: templates/emails/customer-cancelled-booking.php:15
msgid "Dear %1$s %2$s, your reservation is cancelled!"
msgstr "Estimado/a %1$s %2$s, su reserva está cancelada!"

#: templates/emails/customer-confirmation-booking.php:14
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:12
msgid "Dear %1$s %2$s, we received your request for reservation."
msgstr "Estimado/a %1$s %2$s, hemos recibido su solicitud de reserva."

#: templates/emails/customer-confirmation-booking.php:16
msgid "Click the link below to confirm your booking."
msgstr "Haga clic en el enlace para cancelar su reserva."

#: templates/emails/customer-confirmation-booking.php:18
msgid "Confirm"
msgstr "Confirmar"

#: templates/emails/customer-confirmation-booking.php:20
msgid "Note: link expires on"
msgstr "Nota: el enlace caduca en"

#: templates/emails/customer-confirmation-booking.php:20
msgid "UTC"
msgstr "UTC"

#: templates/emails/customer-confirmation-booking.php:22
msgid "If you did not place this booking, please ignore this email."
msgstr "Si Usted no hizo esta reserva, por favor, ignore este mensaje."

#: templates/emails/customer-pending-booking.php:15
msgid "Dear %1$s %2$s, your reservation is pending."
msgstr "Estimado/a %1$s %2$s, su reserva está pendiente."

#: templates/emails/customer-pending-booking.php:17
msgid "We will notify you by email once it is confirmed by our staff."
msgstr "Le notificaremos por correo electrónico, una vez que su reserva sea confirmada por nuestro personal."

#: templates/emails/customer-registration.php:15
msgid "Hi %1$s %2$s,"
msgstr "Hola %1$s %2$s,"

#: templates/emails/customer-registration.php:17
msgid "Thanks for creating an account on %1$s."
msgstr "Gracias por crear una cuenta en: %1$s."

#: templates/emails/customer-registration.php:19
msgid "You Account Details"
msgstr "Los detalles de tu cuenta"

#: templates/emails/customer-registration.php:20
msgid "Login: %s"
msgstr "Inicio de sesión: %s"

#: templates/emails/customer-registration.php:21
msgid "Password: %s"
msgstr "Contraseña: %s"

#: templates/emails/customer-registration.php:22
msgid "Log in here: %s"
msgstr "Inicia sesión aquí: %s"

#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:14
msgid "To confirm your booking, please follow the instructions below for payment."
msgstr ""

#: templates/emails/reserved-room-details.php:14
msgid "Accommodation #%s"
msgstr "Alojamiento #%s"

#: templates/emails/reserved-room-details.php:21
msgid "Accommodation: <a href=\"%1$s\">%2$s</a>"
msgstr "Alojamiento: <a href=\"%1$s\">%2$s</a>"

#: templates/emails/reserved-room-details.php:24
msgid "Accommodation Rate: %s"
msgstr "Tarifa de alojamiento: %s"

#: templates/emails/reserved-room-details.php:28
msgid "Bed Type: %s"
msgstr "Tipo de cama: %s"

#: templates/required-fields-tip.php:8
msgid "Required fields are followed by"
msgstr "Los campos obligatorios son seguidos por"

#: templates/shortcodes/booking-cancellation/already-cancelled.php:7
msgid "Booking is already canceled."
msgstr "La reserva ya está cancelada."

#: templates/shortcodes/booking-cancellation/booking-cancellation-button.php:15
msgid "Cancel Booking"
msgstr "Cancelar reserva"

#: templates/shortcodes/booking-cancellation/invalid-request.php:7
#: templates/shortcodes/booking-confirmation/invalid-request.php:7
msgid "Invalid request."
msgstr "Solicitud no válida."

#: templates/shortcodes/booking-cancellation/not-possible.php:7
msgid "Cancelation of your booking is not possible for some reason. Please contact the website administrator."
msgstr "La cancelación de tu reserva no es posible por alguna razón. Por favor, comunícate con el administrador del sitio web."

#: templates/shortcodes/booking-confirmation/already-confirmed.php:7
msgid "Booking is already confirmed."
msgstr "La reserva ya está confirmada."

#: templates/shortcodes/booking-confirmation/confirmed.php:7
msgid "Your booking is confirmed. Thank You!"
msgstr "Su reserva está confirmada. ¡Gracias!"

#: templates/shortcodes/booking-confirmation/expired.php:7
msgid "Your booking request is expired. Please start a new booking request."
msgstr "Su solicitud de reserva ha caducado. Por favor, envíe una nueva solicitud de reserva."

#: templates/shortcodes/booking-confirmation/not-possible.php:7
msgid "Confirmation of your booking request is not possible for some reason. Please start a new booking request."
msgstr "No se puede confirmar su solicitud de reserva por alguna razón. Por favor, envíe una nueva solicitud de reserva."

#: templates/shortcodes/booking-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received and confirmed."
msgstr "Nos complace informarte que tu solicitud de reserva se ha recibido y confirmado."

#: templates/shortcodes/booking-details/booking-details.php:21
msgid "Booking:"
msgstr "Reserva:"

#: templates/shortcodes/booking-details/booking-details.php:47
msgid "Details:"
msgstr "Detalles:"

#: templates/shortcodes/payment-confirmation/completed.php:11
msgid "Thank you for your payment. Your transaction has been completed."
msgstr "Gracias por su pago. Su transacción se ha completado."

#: templates/shortcodes/payment-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received."
msgstr "Nos complace informarle de que su solicitud de reserva se ha recibido."

#: templates/shortcodes/room-rates/rate-content.php:17
msgid "from %s"
msgstr "desde %s"

#: templates/shortcodes/rooms/not-found.php:7
msgid "No accommodations matching criteria."
msgstr "Ningún alojamiento coincide con los criterios."

#: templates/shortcodes/services/not-found.php:7
msgid "No services matched criteria."
msgstr "Ningún servicio coincide con los criterios."

#: templates/widgets/rooms/not-found.php:6
msgid "Nothing found."
msgstr "Nada encontrado."

#: templates/widgets/search-availability/search-form.php:105
msgid "Children %s:"
msgstr "Niños %s:"

#: assets/blocks/blocks.js:178
#: assets/blocks/blocks.js:190
msgid "Preset date. Formatted as %s"
msgstr "Fecha preestablecida. En formato %s"

#: assets/blocks/blocks.js:283
#: assets/blocks/blocks.js:1425
#: assets/blocks/blocks.js:1507
msgid "Select an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:284
#: assets/blocks/blocks.js:1426
#: assets/blocks/blocks.js:1508
msgid "ID of an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:460
#: assets/blocks/blocks.js:710
#: assets/blocks/blocks.js:1239
msgid "Gallery"
msgstr "Galería"

#: assets/blocks/blocks.js:508
#: assets/blocks/blocks.js:758
#: assets/blocks/blocks.js:1287
msgid "View Button"
msgstr "Botón \"Ver\""

#: assets/blocks/blocks.js:522
#: assets/blocks/blocks.js:558
#: assets/blocks/blocks.js:858
#: assets/blocks/blocks.js:894
#: assets/blocks/blocks.js:1042
#: assets/blocks/blocks.js:1078
msgid "Order"
msgstr "Pida"

#: assets/blocks/blocks.js:530
#: assets/blocks/blocks.js:866
#: assets/blocks/blocks.js:1050
msgid "Order By"
msgstr "Ordenar por"

#: assets/blocks/blocks.js:533
#: assets/blocks/blocks.js:869
#: assets/blocks/blocks.js:1053
msgid "No order"
msgstr "Sin ordenar"

#: assets/blocks/blocks.js:534
#: assets/blocks/blocks.js:870
#: assets/blocks/blocks.js:1054
msgid "Post ID"
msgstr "ID del mensaje"

#: assets/blocks/blocks.js:535
#: assets/blocks/blocks.js:871
#: assets/blocks/blocks.js:1055
msgid "Post author"
msgstr "Autor del mensaje"

#: assets/blocks/blocks.js:536
#: assets/blocks/blocks.js:872
#: assets/blocks/blocks.js:1056
msgid "Post title"
msgstr "Título de entrada"

#: assets/blocks/blocks.js:537
#: assets/blocks/blocks.js:873
#: assets/blocks/blocks.js:1057
msgid "Post name (post slug)"
msgstr "Nombre del mensaje (post slug)"

#: assets/blocks/blocks.js:538
#: assets/blocks/blocks.js:874
#: assets/blocks/blocks.js:1058
msgid "Post date"
msgstr "Fecha de publicación"

#: assets/blocks/blocks.js:539
#: assets/blocks/blocks.js:875
#: assets/blocks/blocks.js:1059
msgid "Last modified date"
msgstr "Fecha de la última modificación"

#: assets/blocks/blocks.js:540
#: assets/blocks/blocks.js:876
#: assets/blocks/blocks.js:1060
msgid "Parent ID"
msgstr "ID de categoría principal"

#: assets/blocks/blocks.js:541
#: assets/blocks/blocks.js:877
#: assets/blocks/blocks.js:1061
msgid "Random order"
msgstr "Orden aleatorio"

#: assets/blocks/blocks.js:542
#: assets/blocks/blocks.js:878
#: assets/blocks/blocks.js:1062
msgid "Number of comments"
msgstr "Número de comentarios"

#: assets/blocks/blocks.js:543
#: assets/blocks/blocks.js:879
#: assets/blocks/blocks.js:1063
msgid "Relevance"
msgstr "Relevancia"

#: assets/blocks/blocks.js:544
#: assets/blocks/blocks.js:880
#: assets/blocks/blocks.js:1064
msgid "Page order"
msgstr "Orden de páginas"

#: assets/blocks/blocks.js:545
#: assets/blocks/blocks.js:881
#: assets/blocks/blocks.js:1065
msgid "Meta value"
msgstr "Valor meta"

#: assets/blocks/blocks.js:546
#: assets/blocks/blocks.js:882
#: assets/blocks/blocks.js:1066
msgid "Numeric meta value"
msgstr "Valor meta numérico"

#: assets/blocks/blocks.js:561
#: assets/blocks/blocks.js:897
#: assets/blocks/blocks.js:1081
msgid "Ascending (1, 2, 3)"
msgstr "Ascendente (1, 2, 3)"

#: assets/blocks/blocks.js:562
#: assets/blocks/blocks.js:898
#: assets/blocks/blocks.js:1082
msgid "Descending (3, 2, 1)"
msgstr "Descendente (3, 2, 1)"

#: assets/blocks/blocks.js:573
#: assets/blocks/blocks.js:909
#: assets/blocks/blocks.js:1093
msgid "Meta Name"
msgstr "Nombre de meta"

#: assets/blocks/blocks.js:585
#: assets/blocks/blocks.js:921
#: assets/blocks/blocks.js:1105
msgid "Meta Type"
msgstr "Tipo de meta"

#: assets/blocks/blocks.js:586
#: assets/blocks/blocks.js:922
#: assets/blocks/blocks.js:1106
msgid "Specified type of the custom field. Can be used in conjunction with \"orderby\" = \"meta_value\"."
msgstr "Tipo especificado de campo personalizado. Se puede utilizar junto con \"orderby\" = \"meta_value\"."

#: assets/blocks/blocks.js:589
#: assets/blocks/blocks.js:925
#: assets/blocks/blocks.js:1109
msgid "Any"
msgstr "Cualquiera"

#: assets/blocks/blocks.js:590
#: assets/blocks/blocks.js:926
#: assets/blocks/blocks.js:1110
msgid "Numeric"
msgstr "Numérico"

#: assets/blocks/blocks.js:591
#: assets/blocks/blocks.js:927
#: assets/blocks/blocks.js:1111
msgid "Binary"
msgstr "Binario"

#: assets/blocks/blocks.js:592
#: assets/blocks/blocks.js:928
#: assets/blocks/blocks.js:1112
msgid "String"
msgstr "Cadena"

#: assets/blocks/blocks.js:594
#: assets/blocks/blocks.js:930
#: assets/blocks/blocks.js:1114
msgid "Time"
msgstr "Tiempo"

#: assets/blocks/blocks.js:595
#: assets/blocks/blocks.js:931
#: assets/blocks/blocks.js:1115
msgid "Date and time"
msgstr "Fecha y hora"

#: assets/blocks/blocks.js:596
#: assets/blocks/blocks.js:932
#: assets/blocks/blocks.js:1116
msgid "Decimal number"
msgstr "Número decimal"

#: assets/blocks/blocks.js:597
#: assets/blocks/blocks.js:933
#: assets/blocks/blocks.js:1117
msgid "Signed number"
msgstr "Número firmado"

#: assets/blocks/blocks.js:598
#: assets/blocks/blocks.js:934
#: assets/blocks/blocks.js:1118
msgid "Unsigned number"
msgstr "Número no firmado"

#: assets/blocks/blocks.js:784
#: assets/blocks/blocks.js:1009
msgid "Query Settings"
msgstr "Configuración de consulta"

#: assets/blocks/blocks.js:840
msgid "Relation"
msgstr "Relación"

#: assets/blocks/blocks.js:1029
msgid "Values: integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "Valores: entero, -1 para mostrar todo, de forma predeterminada: \"Las páginas del blog se muestran como máximo\""

#: assets/blocks/blocks.js:1203
msgid "Select an accommodation type."
msgstr ""

