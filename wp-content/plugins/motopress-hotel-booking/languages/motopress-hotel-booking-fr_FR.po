# Copyright (C) 2025 MotoPress
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: hotel-booking-plugin\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/motopress-hotel-booking\n"
"Last-Translator: \n"
"Language-Team: French\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-19T19:58:50+00:00\n"
"PO-Revision-Date: 2025-03-05 20:50\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: motopress-hotel-booking\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Crowdin-Project: hotel-booking-plugin\n"
"X-Crowdin-Project-ID: 463550\n"
"X-Crowdin-Language: fr\n"
"X-Crowdin-File: motopress-hotel-booking.pot\n"
"X-Crowdin-File-ID: 44\n"
"Language: fr_FR\n"

#. Plugin Name of the plugin
#. translators: Name of the plugin, do not translate
#: motopress-hotel-booking.php
#: includes/script-managers/block-script-manager.php:27
msgid "Hotel Booking"
msgstr "Hotel Booking"

#. Plugin URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/products/hotel-booking/"
msgstr "https://motopress.com/produits/hotel-booking/"

#. Description of the plugin
#: motopress-hotel-booking.php
msgid "Manage your hotel booking services. Perfect for hotels, villas, guest houses, hostels, and apartments of all sizes."
msgstr "Gérez vos services de réservation d'hôtel."

#. Author of the plugin
#: motopress-hotel-booking.php
msgid "MotoPress"
msgstr "MotoPress"

#. Author URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/"
msgstr "https://motopress.com/"

#: functions.php:71
msgctxt "Post Status"
msgid "New"
msgstr "Nouveau"

#: functions.php:74
msgctxt "Post Status"
msgid "Auto Draft"
msgstr "Brouillon automatique"

#. translators: %s: URL to plugins.php page
#: functions.php:518
msgid "You are using two instances of Hotel Booking plugin at the same time, please <a href=\"%s\">deactivate one of them</a>."
msgstr "Vous utilisez deux instances du plugin Hotel Booking en même temps, veuillez <a href=\"%s\">désactiver l'une d'elles</a>."

#: functions.php:535
msgid "<a href=\"%s\">Upgrade to Premium</a> to enable this feature."
msgstr "<a href=\"%s\">Passez en Premium</a> pour activer cette fonction"

#: includes/actions-handler.php:100
#: includes/admin/sync-logs-list-table.php:91
#: includes/csv/csv-export-handler.php:33
#: includes/csv/csv-export-handler.php:51
#: includes/payments/gateways/stripe-gateway.php:560
#: includes/payments/gateways/stripe-gateway.php:572
#: includes/payments/gateways/stripe-gateway.php:631
msgid "Error"
msgstr "Erreur"

#: includes/admin/customers-list-table.php:143
#: includes/admin/menu-pages/rooms-generator-menu-page.php:84
#: includes/admin/sync-rooms-list-table.php:146
#: includes/post-types/room-type-cpt.php:354
#: templates/account/bookings.php:80
msgid "View"
msgstr "Voir"

#: includes/admin/customers-list-table.php:147
#: includes/admin/fields/abstract-complex-field.php:25
#: includes/admin/fields/rules-list-field.php:61
#: includes/admin/sync-rooms-list-table.php:147
msgid "Delete"
msgstr "Supprimer"

#: includes/admin/customers-list-table.php:212
#: includes/post-types/attributes-cpt.php:301
msgid "Name"
msgstr "Nom"

#: includes/admin/customers-list-table.php:213
#: includes/admin/menu-pages/customers-menu-page.php:207
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:122
#: includes/bundles/customer-bundle.php:110
#: includes/csv/bookings/bookings-exporter-helper.php:83
#: includes/post-types/booking-cpt.php:106
#: includes/post-types/payment-cpt.php:263
#: includes/views/shortcodes/checkout-view.php:618
#: templates/account/account-details.php:34
msgid "Email"
msgstr "Courriel"

#: includes/admin/customers-list-table.php:214
#: includes/admin/menus.php:72
#: includes/admin/menus.php:73
#: includes/post-types/booking-cpt.php:241
#: includes/shortcodes/account-shortcode.php:239
msgid "Bookings"
msgstr "Réservations"

#: includes/admin/customers-list-table.php:215
msgid "Date Registered"
msgstr "Date d'enregistrement"

#: includes/admin/customers-list-table.php:216
msgid "Last Active"
msgstr "Dernière Connexion"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:16
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:33
msgid "Terms"
msgstr "Termes"

#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:27
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:46
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:41
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:115
msgid "Created on:"
msgstr "Créée le :"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:88
msgid "Please add attribute in default language to configure terms."
msgstr "Veuillez ajouter un attribut dans la langue par défaut pour configurer les termes."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:98
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:116
msgid "Configure terms"
msgstr "Configurer les termes"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:20
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:107
#: includes/post-types/reserved-room-cpt.php:22
msgid "Reserved Accommodations"
msgstr "Logements réservés"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:21
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:66
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Update Booking"
msgstr "Mise à jour de la réservation"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:22
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:12
msgid "Logs"
msgstr "Connexions"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:54
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:125
msgid "Delete Permanently"
msgstr "Supprimer définitivement"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:58
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:127
msgid "Move to Trash"
msgstr "Mettre à la corbeille"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Create Booking"
msgstr "Créer une Réservation"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:85
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:98
msgid "Resend Email"
msgstr "Renvoyer E-mail"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:100
msgid "Send a copy of the Approved Booking email to the customer`s email address."
msgstr "Envoyez une copie de l'e-mail de Réservation Approuvée à l'adresse e-mail du client."

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:116
#: templates/edit-booking/edit-reserved-rooms.php:35
msgid "Edit Accommodations"
msgstr "Modifier Hébergements"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:125
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:85
#: includes/shortcodes/booking-confirmation-shortcode.php:298
msgid "Date:"
msgstr "Date :"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:130
msgid "Author:"
msgstr "Auteur :"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:145
#: includes/payments/gateways/stripe-gateway.php:528
msgid "Auto"
msgstr "Auto"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:155
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:89
msgid "Message:"
msgstr "Message :"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:227
msgid "Confirmation email has been sent to customer."
msgstr "L'e-mail de confirmation a été envoyé au client."

#: includes/admin/edit-cpt-pages/coupon-edit-cpt-page.php:14
msgid "Coupon code"
msgstr "Code de réduction"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:11
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:64
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Update Payment"
msgstr "Mettre à jour le paiement"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:44
msgid "Modified on:"
msgstr "Modifié le :"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Create Payment"
msgstr "Créer un paiement"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:47
msgid "Season Prices"
msgstr "Prix de saison"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:48
msgid "<code>Please select Accommodation Type and click Create Rate button to continue.</code>"
msgstr "<code>Veuillez sélectionner le type de logement et cliquer sur le bouton Créer un tarif pour continuer. </code>."

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:66
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:65
#: includes/views/loop-room-type-view.php:113
#: includes/views/single-room-type-view.php:205
#: template-functions.php:920
#: templates/create-booking/results/reserve-rooms.php:51
#: templates/widgets/rooms/room-content.php:77
#: templates/widgets/search-availability/search-form.php:78
msgid "Adults:"
msgstr "Adultes :"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:66
#: includes/views/loop-room-type-view.php:128
#: includes/views/single-room-type-view.php:220
#: template-functions.php:925
#: templates/create-booking/results/reserve-rooms.php:52
#: templates/widgets/rooms/room-content.php:91
#: templates/widgets/search-availability/search-form.php:103
msgid "Children:"
msgstr "Enfants :"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:70
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:63
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:110
#: includes/shortcodes/booking-confirmation-shortcode.php:306
#: includes/shortcodes/search-results-shortcode.php:770
#: includes/shortcodes/search-results-shortcode.php:921
#: templates/shortcodes/booking-details/booking-details.php:33
msgid "Total:"
msgstr "Total :"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:80
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Update Rate"
msgstr "Mettre à jour le taux"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:97
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
msgid "Active"
msgstr "Actif"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:98
#: includes/admin/groups/license-settings-group.php:61
msgid "Disabled"
msgstr "Désactivé"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Create Rate"
msgstr "Créer un prix"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:190
msgid "Duplicate Rate"
msgstr "Dupliquer le prix"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:12
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:36
#: includes/admin/menu-pages/booking-rules-menu-page.php:219
#: includes/admin/menu-pages/booking-rules-menu-page.php:264
#: includes/admin/menu-pages/booking-rules-menu-page.php:310
#: includes/admin/menu-pages/booking-rules-menu-page.php:356
#: includes/admin/menu-pages/booking-rules-menu-page.php:487
#: includes/admin/menu-pages/booking-rules-menu-page.php:533
#: includes/admin/menu-pages/booking-rules-menu-page.php:579
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:208
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:304
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:377
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:448
#: includes/post-types/room-cpt.php:31
#: includes/post-types/room-cpt.php:41
#: includes/wizard.php:103
msgid "Accommodations"
msgstr "Chambres"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:15
#: includes/post-types/attributes-cpt.php:54
#: includes/post-types/attributes-cpt.php:61
#: includes/post-types/attributes-cpt.php:65
msgid "Attributes"
msgstr "Attributs"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:18
msgid "Accommodation Reviews"
msgstr "Commentaires sur le logement"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:27
msgid "Allow guests to <a href=\"%s\" target=\"_blank\">submit star ratings and reviews</a> evaluating your accommodations."
msgstr "Autoriser les invités à <a href=\"%s\" target=\"_blank\">soumettre des évaluations et commentaires </a> sur votre hébergement."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:69
msgid "Number of Accommodations:"
msgstr "Nombre de chambres :"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:74
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:29
msgid "Count of real accommodations of this type in your hotel."
msgstr "Nombre réel de chambres de ce type dans votre hôtel."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:122
msgid "Total Accommodations:"
msgstr "Nombre de chambres total :"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:159
msgid "Show Accommodations"
msgstr "Montrer les chambres"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:139
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:18
#: includes/admin/menu-pages/rooms-generator-menu-page.php:146
#: includes/admin/menu-pages/rooms-generator-menu-page.php:150
msgid "Generate Accommodations"
msgstr "Générer les chambres"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:147
msgid "Active Accommodations:"
msgstr "Chambres actives:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:168
#: includes/post-types/room-cpt.php:93
msgid "Linked Accommodations"
msgstr "Hégergements liés"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:173
msgid "Link accommodations on the Edit Accommodation page to ensure bookings for one make any linked properties unavailable for the same dates."
msgstr "Liez les hébergements sur la page « Modifier l'hébergement » pour garantir que les réservations pour l'un rendent les propriétés liées indisponibles aux mêmes dates."

#: includes/admin/fields/abstract-complex-field.php:24
#: includes/admin/fields/rules-list-field.php:57
#: templates/edit-booking/add-room-popup.php:45
msgid "Add"
msgstr "Ajouter"

#: includes/admin/fields/amount-field.php:74
msgid "Per adult:"
msgstr "Par adulte :"

#: includes/admin/fields/amount-field.php:77
msgid "Per child:"
msgstr "Par enfant :"

#: includes/admin/fields/amount-field.php:198
msgid "Per adult: "
msgstr "Par adulte :"

#: includes/admin/fields/amount-field.php:200
msgid "Per child: "
msgstr "Par enfant :"

#: includes/admin/fields/complex-horizontal-field.php:71
#: includes/admin/fields/rules-list-field.php:62
#: templates/account/bookings.php:22
#: templates/account/bookings.php:79
#: templates/edit-booking/edit-reserved-rooms.php:47
msgid "Actions"
msgstr "Actions"

#: includes/admin/fields/complex-horizontal-field.php:111
msgid "Move up"
msgstr "Déplacer en haut"

#: includes/admin/fields/complex-horizontal-field.php:112
msgid "Move down"
msgstr "Déplacer en bas"

#: includes/admin/fields/complex-horizontal-field.php:113
msgid "Move to top"
msgstr "Déplacer complètement en haut"

#: includes/admin/fields/complex-horizontal-field.php:114
msgid "Move to bottom"
msgstr "Déplacer complètement en bas"

#: includes/admin/fields/complex-vertical-field.php:17
#: includes/admin/menu-pages/settings-menu-page.php:580
#: includes/settings/main-settings.php:25
#: includes/settings/main-settings.php:43
msgid "Default"
msgstr "Par défaut"

#: includes/admin/fields/dynamic-select-field.php:61
#: includes/admin/fields/page-select-field.php:16
#: includes/admin/menu-pages/customers-menu-page.php:260
#: includes/admin/menu-pages/rooms-generator-menu-page.php:38
#: includes/admin/menu-pages/settings-menu-page.php:420
#: includes/post-types/booking-cpt.php:122
#: includes/post-types/booking-cpt.php:179
#: includes/post-types/payment-cpt.php:231
#: includes/post-types/rate-cpt.php:31
#: includes/post-types/room-cpt.php:79
#: includes/views/shortcodes/checkout-view.php:250
#: includes/views/shortcodes/checkout-view.php:273
#: templates/account/account-details.php:51
#: templates/edit-booking/add-room-popup.php:30
#: templates/edit-booking/add-room-popup.php:38
msgid "— Select —"
msgstr "— Sélectionner —"

#: includes/admin/fields/install-plugin-field.php:33
msgid "Install & Activate"
msgstr "Installer & Activer"

#: includes/admin/fields/media-field.php:76
msgid "Add image"
msgstr "Ajouter image"

#: includes/admin/fields/media-field.php:76
msgid "Add gallery"
msgstr "Ajouter une galerie"

#: includes/admin/fields/media-field.php:77
msgid "Remove image"
msgstr "Supprimer image"

#: includes/admin/fields/media-field.php:77
msgid "Remove gallery"
msgstr "Supprimer la galerie"

#: includes/admin/fields/multiple-checkbox-field.php:88
#: template-functions.php:1088
msgid "Select all"
msgstr "séléctionner tout"

#: includes/admin/fields/multiple-checkbox-field.php:92
#: template-functions.php:1090
msgid "Unselect all"
msgstr "tout désélectionner"

#: includes/admin/fields/notes-list-field.php:23
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:68
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:33
#: includes/csv/bookings/bookings-exporter-helper.php:112
#: assets/blocks/blocks.js:593
#: assets/blocks/blocks.js:929
#: assets/blocks/blocks.js:1113
msgid "Date"
msgstr "Date"

#: includes/admin/fields/notes-list-field.php:33
msgid "Author"
msgstr "Auteur"

#: includes/admin/fields/rules-list-field.php:59
#: includes/admin/room-list-table.php:154
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:115
#: includes/bookings-calendar.php:613
#: includes/script-managers/admin-script-manager.php:97
msgid "Edit"
msgstr "Modifier"

#: includes/admin/fields/rules-list-field.php:60
#: includes/admin/sync-rooms-list-table.php:81
#: includes/ajax.php:951
#: includes/script-managers/admin-script-manager.php:98
msgid "Done"
msgstr "Terminé"

#: includes/admin/fields/rules-list-field.php:64
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:85
#: includes/admin/menu-pages/booking-rules-menu-page.php:180
#: includes/admin/menu-pages/booking-rules-menu-page.php:183
#: includes/admin/menu-pages/booking-rules-menu-page.php:407
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:211
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:307
#: includes/script-managers/admin-script-manager.php:95
msgid "All"
msgstr "Tout"

#: includes/admin/fields/rules-list-field.php:65
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:83
#: includes/post-types/coupon-cpt.php:81
#: includes/post-types/coupon-cpt.php:113
#: includes/post-types/coupon-cpt.php:158
#: includes/script-managers/admin-script-manager.php:96
msgid "None"
msgstr "Aucun"

#: includes/admin/fields/time-picker-field.php:13
msgid "HH:MM"
msgstr "HH:MM"

#: includes/admin/fields/total-price-field.php:18
msgid "Recalculate Total Price"
msgstr "Recalculer le prix total"

#: includes/admin/fields/variable-pricing-field.php:89
#: includes/views/booking-view.php:121
msgid "Nights"
msgstr "Nuits"

#: includes/admin/fields/variable-pricing-field.php:97
#: includes/script-managers/admin-script-manager.php:102
msgid "and more"
msgstr "et plus"

#: includes/admin/fields/variable-pricing-field.php:98
#: includes/admin/menu-pages/edit-booking/edit-control.php:95
#: includes/script-managers/admin-script-manager.php:101
#: includes/shortcodes/search-results-shortcode.php:1009
#: includes/views/booking-view.php:400
#: templates/edit-booking/edit-reserved-rooms.php:70
msgid "Remove"
msgstr "Supprimer"

#: includes/admin/fields/variable-pricing-field.php:104
msgid "Add length of stay"
msgstr "Ajouter la durée du séjour"

#: includes/admin/fields/variable-pricing-field.php:109
msgid "Base Occupancy"
msgstr "Occupation de base"

#: includes/admin/fields/variable-pricing-field.php:110
#: includes/admin/fields/variable-pricing-field.php:170
msgid "Price per night"
msgstr "Prix par nuit"

#: includes/admin/fields/variable-pricing-field.php:118
#: includes/admin/fields/variable-pricing-field.php:167
#: includes/emails/templaters/reserved-rooms-templater.php:175
#: includes/post-types/room-type-cpt.php:283
#: includes/views/booking-view.php:105
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:242
#: template-functions.php:765
#: templates/create-booking/search/search-form.php:94
#: templates/shortcodes/search/search-form.php:80
#: assets/blocks/blocks.js:147
msgid "Adults"
msgstr "Adultes"

#: includes/admin/fields/variable-pricing-field.php:122
#: includes/csv/bookings/bookings-exporter-helper.php:80
#: includes/emails/templaters/reserved-rooms-templater.php:179
#: includes/post-types/room-type-cpt.php:292
#: includes/views/booking-view.php:116
#: templates/create-booking/search/search-form.php:109
#: templates/shortcodes/search/search-form.php:103
#: assets/blocks/blocks.js:162
msgid "Children"
msgstr "Enfants"

#: includes/admin/fields/variable-pricing-field.php:130
msgid "Price per extra adult"
msgstr "Prix par adulte supplémentaire"

#: includes/admin/fields/variable-pricing-field.php:137
msgid "Price per extra child"
msgstr "Prix par enfant supplémentaire"

#: includes/admin/fields/variable-pricing-field.php:154
msgid "Enable variable pricing"
msgstr "Activer la tarification variable"

#: includes/admin/fields/variable-pricing-field.php:188
msgid "Add Variation"
msgstr "Ajouter une variation"

#: includes/admin/fields/variable-pricing-field.php:215
#: includes/admin/fields/variable-pricing-field.php:231
msgid "Remove variation"
msgstr "Supprimer la variation"

#: includes/admin/groups/license-settings-group.php:21
msgid "The License Key is required in order to get automatic plugin updates and support. You can manage your License Key in your personal account. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Learn more</a>."
msgstr "La clé de licence est requise pour obtenir des mises à jour et une prise en charge automatiques des plugins. Vous pouvez gérer votre clé de licence dans votre compte personnel. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>En savoir plus</a>."

#: includes/admin/groups/license-settings-group.php:28
msgid "License Key"
msgstr "Clé de licence"

#: includes/admin/groups/license-settings-group.php:42
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:62
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:22
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:28
#: includes/admin/menu-pages/create-booking/checkout-step.php:63
#: includes/admin/sync-logs-list-table.php:72
#: includes/admin/sync-rooms-list-table.php:127
#: includes/csv/bookings/bookings-exporter-helper.php:72
#: template-functions.php:977
#: templates/edit-booking/edit-reserved-rooms.php:46
msgid "Status"
msgstr "Statut"

#: includes/admin/groups/license-settings-group.php:49
msgid "Inactive"
msgstr "Inactif"

#: includes/admin/groups/license-settings-group.php:55
msgid "Valid until"
msgstr "Valable jusque"

#: includes/admin/groups/license-settings-group.php:57
msgid "Valid (Lifetime)"
msgstr "Valide (durée de vie)"

#: includes/admin/groups/license-settings-group.php:64
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:123
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:85
msgid "Expired"
msgstr "Expiré"

#: includes/admin/groups/license-settings-group.php:67
msgid "Invalid"
msgstr "Invalide"

#: includes/admin/groups/license-settings-group.php:71
msgid "Your License Key does not match the installed plugin. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>How to fix this.</a>"
msgstr "Votre clé de licence ne correspond pas au plugin installé. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>Comment résoudre ce problème.</a>"

#: includes/admin/groups/license-settings-group.php:74
msgid "Product ID is not valid"
msgstr "L'ID du produit n'est pas valide"

#: includes/admin/groups/license-settings-group.php:83
msgid "Action"
msgstr "Action"

#: includes/admin/groups/license-settings-group.php:90
msgid "Activate License"
msgstr "Activer la licence"

#: includes/admin/groups/license-settings-group.php:96
msgid "Deactivate License"
msgstr "Désactiver la licence"

#: includes/admin/groups/license-settings-group.php:103
msgid "Renew License"
msgstr "Renouveler la licence"

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:9
msgid "Attributes let you define extra accommodation data, such as location or type. You can use these attributes in the search availability form as advanced search filters."
msgstr "Les attributs vous permettent de définir des données d'hébergement supplémentaires, telles que l'emplacement ou le type. Vous pouvez utiliser ces attributs dans le formulaire de recherche de disponibilité comme filtres de recherche avancée."

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:72
msgid "This attribute refers to non-unique taxonomy - %1$s - which was already registered with attribute %2$s."
msgstr "Cet attribut fait référence à une taxonomie non unique - %1$s - qui était déjà enregistrée avec l'attribut %2$s."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:92
msgid "You cannot manage terms of trashed attributes."
msgstr "Vous ne pouvez pas gérer les termes des attributs mis au rebut."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:17
#: includes/admin/menu-pages/calendar-menu-page.php:31
#: includes/post-types/booking-cpt.php:246
msgid "New Booking"
msgstr "Nouvelle Réservation"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:61
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:26
#: includes/admin/menu-pages/shortcodes-menu-page.php:376
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:64
#: includes/csv/bookings/bookings-exporter-helper.php:71
#: includes/post-types/booking-cpt.php:42
#: includes/post-types/payment-cpt.php:150
msgid "ID"
msgstr "ID"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:63
msgid "Check-in / Check-out"
msgstr "Arrivée / Départ"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:64
#: includes/views/booking-view.php:107
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:244
#: template-functions.php:767
#: templates/shortcodes/search/search-form.php:82
msgid "Guests"
msgstr "Invités"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:65
#: templates/emails/admin-customer-cancelled-booking.php:23
#: templates/emails/admin-customer-confirmed-booking.php:23
#: templates/emails/admin-payment-confirmed-booking.php:30
#: templates/emails/admin-pending-booking.php:23
msgid "Customer Info"
msgstr "Informations client"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:66
#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:16
#: includes/post-types/rate-cpt.php:64
#: includes/post-types/service-cpt.php:132
#: includes/post-types/service-cpt.php:137
#: includes/views/single-service-view.php:18
#: includes/widgets/rooms-widget.php:201
#: assets/blocks/blocks.js:496
#: assets/blocks/blocks.js:547
#: assets/blocks/blocks.js:746
#: assets/blocks/blocks.js:883
#: assets/blocks/blocks.js:1067
#: assets/blocks/blocks.js:1275
msgid "Price"
msgstr "Prix"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:67
#: includes/admin/menu-pages/booking-rules-menu-page.php:402
#: includes/admin/room-list-table.php:93
#: includes/admin/sync-rooms-list-table.php:126
#: includes/bookings-calendar.php:829
#: includes/bookings-calendar.php:847
#: includes/csv/bookings/bookings-exporter-helper.php:77
#: includes/post-types/room-cpt.php:32
#: includes/post-types/room-cpt.php:74
#: includes/post-types/room-type-cpt.php:60
#: templates/edit-booking/add-room-popup.php:36
#: templates/edit-booking/edit-reserved-rooms.php:45
msgid "Accommodation"
msgstr "Chambre"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:121
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:83
msgid "Expire %s"
msgstr "Expire %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:138
#: includes/script-managers/admin-script-manager.php:99
msgid "Adults: "
msgstr "Adultes : "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:142
#: includes/script-managers/admin-script-manager.php:100
msgid "Children: "
msgstr "Enfants: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:184
msgid "%s night"
msgid_plural "%s nights"
msgstr[0] "%s nuit"
msgstr[1] "%s nuits"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:197
#: includes/bookings-calendar.php:1189
msgid "Summary: %s."
msgstr "Sommaires : %s."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:280
msgid "Paid: %s"
msgstr "Payé: %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:345
msgid "Set to %s"
msgstr "Défini sur %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:460
msgid "Booking status changed."
msgid_plural "%s booking statuses changed."
msgstr[0] "Le statut de réservation a changé."
msgstr[1] "%s statuts de réservation ont changé."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:506
msgid "All accommodation types"
msgstr "Tous les types d'hébergement"

#. translators: The number of imported bookings: "Imported <span>(11)</span>"
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:526
msgid "Imported %s"
msgstr "Importées %s"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:19
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:29
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:168
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:264
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:349
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:420
#: includes/post-types/coupon-cpt.php:93
#: includes/post-types/coupon-cpt.php:124
#: includes/post-types/coupon-cpt.php:169
#: includes/post-types/payment-cpt.php:182
#: includes/views/booking-view.php:126
#: includes/views/booking-view.php:172
#: includes/views/booking-view.php:208
#: includes/views/booking-view.php:266
#: includes/views/booking-view.php:297
#: includes/views/booking-view.php:350
#: template-functions.php:978
msgid "Amount"
msgstr "Montant"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:20
msgid "Uses"
msgstr "Utilisations"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:21
#: includes/post-types/coupon-cpt.php:187
msgid "Expiration Date"
msgstr "Date d'expiration"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:41
#: includes/views/edit-booking/checkout-view.php:115
#: template-functions.php:900
msgid "Accommodation:"
msgstr "Logement :"

#. translators: %s is a coupon amount per day
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:60
msgid "%s per day"
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:67
msgid "Service:"
msgstr "Service:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:88
msgid "Fee:"
msgstr "Frais:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:153
msgid "Note: the use of coupons is disabled in settings."
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:165
#: includes/admin/menu-pages/settings-menu-page.php:299
msgid "Enable the use of coupons."
msgstr "Activer l'utilisation des coupons."

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:27
#: includes/admin/menu-pages/customers-menu-page.php:299
msgid "Customer"
msgstr "Client"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:30
#: includes/post-types/booking-cpt.php:242
#: templates/account/bookings.php:18
#: templates/account/bookings.php:66
msgid "Booking"
msgstr "Réservation"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:31
#: includes/post-types/payment-cpt.php:159
msgid "Gateway"
msgstr "Passerelle"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:32
#: includes/post-types/payment-cpt.php:223
msgid "Transaction ID"
msgstr "ID de transaction"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:105
#: includes/admin/menu-pages/create-booking/booking-step.php:66
#: includes/bookings-calendar.php:606
#: includes/script-managers/admin-script-manager.php:103
msgid "Booking #%s"
msgstr "Réservation #%s"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:13
msgid "Rates are used to offer different prices of the same accommodation type depending on extra conditions, e.g. With Breakfast, With No Breakfast, Refundable etc. Guests will choose the preferable rate when submitting a booking request. Create one default rate if you have no price tiers. To add price variations for different periods - open a rate, add a season, and set the price."
msgstr ""

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:23
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:32
#: includes/admin/menu-pages/booking-rules-menu-page.php:393
#: includes/admin/menu-pages/rooms-generator-menu-page.php:34
#: includes/csv/bookings/bookings-exporter-helper.php:75
#: includes/post-types/rate-cpt.php:30
#: includes/post-types/room-cpt.php:84
#: includes/post-types/room-type-cpt.php:54
#: templates/create-booking/search/search-form.php:82
#: templates/edit-booking/add-room-popup.php:28
#: templates/edit-booking/edit-reserved-rooms.php:44
#: assets/blocks/blocks.js:282
#: assets/blocks/blocks.js:1202
#: assets/blocks/blocks.js:1424
#: assets/blocks/blocks.js:1506
msgid "Accommodation Type"
msgstr "Type de chambre"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:24
msgid "Season &#8212; Price"
msgstr "Prix &#8212; Saison"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:67
#: includes/post-types/rate-cpt.php:73
msgid "Add New Season Price"
msgstr "Ajouter Nouveau prix de saison"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:104
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:94
#: includes/post-types/season-cpt.php:71
msgid "Annually"
msgstr "Annuellement"

#. translators: %s: A date string such as "December 31, 2025".
#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:108
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:98
msgid "Annually until %s"
msgstr ""

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:201
msgid "Duplicate"
msgstr "Dupliquer"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:281
msgid "Rate was duplicated."
msgstr "Le taux a été dupliqué."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:13
msgid "These are real accommodations like rooms, apartments, houses, villas, beds (for hostels) etc."
msgstr "Ce sont les hébergements réels tels que les chambres, les appartements, les maisons, les villas, les lits (pour les auberges de jeunesse) et d'autres."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:77
#: includes/admin/menu-pages/reports-menu-page.php:129
#: includes/bookings-calendar.php:746
msgid "All Accommodation Types"
msgstr "Tous les types de chambres"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:11
msgid "These are not physical accommodations, but their types. E.g. standard double room. To specify the real number of existing accommodations, you'll need to use Generate Accommodations menu."
msgstr "Ce ne sont pas des logements physiques, mais leurs types. Par exemple. Chambre double standard. Pour spécifier le nombre réel de logements existants, vous devrez utiliser le menu Générer des logements."

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:34
#: includes/post-types/room-type-cpt.php:275
#: includes/post-types/room-type-cpt.php:302
#: templates/create-booking/results/reserve-rooms.php:36
msgid "Capacity"
msgstr "Capacité"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:35
msgid "Bed Type"
msgstr "Type de lits"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:68
#: includes/views/loop-room-type-view.php:152
#: includes/views/single-room-type-view.php:244
#: templates/widgets/rooms/room-content.php:167
msgid "Size:"
msgstr "Taille :"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:114
msgid "Active:"
msgstr "Actif :"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:13
msgid "Seasons are real periods of time, dates or days that come with different prices for accommodations. E.g. Winter 2018 ($120 per night), Christmas ($150 per night)."
msgstr "Les saisons sont des périodes réelles, des dates ou des jours qui contiennent des prix différents pour les logements. Par exemple l'Hiver 2018 (120 $ par nuit), Noël (150 $ par nuit)."

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:20
msgid "Start"
msgstr "Début"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:21
msgid "End"
msgstr "Fin"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:22
#: includes/admin/menu-pages/booking-rules-menu-page.php:210
#: includes/admin/menu-pages/booking-rules-menu-page.php:255
msgid "Days"
msgstr "Jours"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:23
#: includes/post-types/season-cpt.php:67
msgid "Repeat"
msgstr "Répétition"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:11
msgid "Services are extra offers that you can sell or give for free. E.g. Thai massage, transfer, babysitting. Guests can pre-order them when placing a booking."
msgstr "Les services sont des offres supplémentaires que vous pouvez vendre ou offrir gratuitement. Par exemple le massage thaïlandais, le transfert, le baby-sitting. Les invités peuvent les prévoir lors de la réservation."

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:17
#: includes/post-types/service-cpt.php:150
msgid "Periodicity"
msgstr "Périodicité"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:18
#: includes/post-types/service-cpt.php:206
msgid "Charge"
msgstr "Charge"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:41
#: includes/entities/service.php:193
#: includes/post-types/service-cpt.php:153
msgid "Per Day"
msgstr "Par jour"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:43
#: includes/post-types/service-cpt.php:154
msgid "Guest Choice"
msgstr "Choix des invités"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:45
#: includes/entities/service.php:197
#: includes/post-types/service-cpt.php:152
msgid "Once"
msgstr "une fois"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:203
#: includes/post-types/service-cpt.php:209
msgid "Per Guest"
msgstr "Par invité(e)"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:205
#: includes/post-types/service-cpt.php:208
msgid "Per Accommodation"
msgstr "Par chambre"

#: includes/admin/manage-tax-pages/facility-manage-tax-page.php:11
msgid "These are accommodation amenities, generally free ones. E.g. air-conditioning, wifi."
msgstr "Ils y sont les équipements d'accueil et de séjour, généralement gratuits. Par exemple l'air conditionné, wifi."

#: includes/admin/menu-pages/booking-rules-menu-page.php:34
msgid "Booking rules saved."
msgstr "Régles de réservation sauvegardées. "

#: includes/admin/menu-pages/booking-rules-menu-page.php:41
#: includes/admin/menu-pages/booking-rules-menu-page.php:602
#: includes/admin/menu-pages/booking-rules-menu-page.php:606
#: includes/admin/menu-pages/settings-menu-page.php:622
msgid "Booking Rules"
msgstr "Règles de réservation"

#: includes/admin/menu-pages/booking-rules-menu-page.php:90
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:70
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:118
#: templates/account/account-details.php:94
msgid "Save Changes"
msgstr "Sauvegarder changements"

#: includes/admin/menu-pages/booking-rules-menu-page.php:199
msgid "Check-in days"
msgstr "Jours d'arrivée"

#: includes/admin/menu-pages/booking-rules-menu-page.php:200
msgid "Guests can check in any day."
msgstr "Arrivée du client possible tous les jours."

#: includes/admin/menu-pages/booking-rules-menu-page.php:201
#: includes/admin/menu-pages/booking-rules-menu-page.php:246
#: includes/admin/menu-pages/booking-rules-menu-page.php:291
#: includes/admin/menu-pages/booking-rules-menu-page.php:337
#: includes/admin/menu-pages/booking-rules-menu-page.php:383
#: includes/admin/menu-pages/booking-rules-menu-page.php:468
#: includes/admin/menu-pages/booking-rules-menu-page.php:514
#: includes/admin/menu-pages/booking-rules-menu-page.php:560
msgid "Add rule"
msgstr "Ajouter une règle"

#: includes/admin/menu-pages/booking-rules-menu-page.php:229
#: includes/admin/menu-pages/booking-rules-menu-page.php:274
#: includes/admin/menu-pages/booking-rules-menu-page.php:320
#: includes/admin/menu-pages/booking-rules-menu-page.php:366
#: includes/admin/menu-pages/booking-rules-menu-page.php:497
#: includes/admin/menu-pages/booking-rules-menu-page.php:543
#: includes/admin/menu-pages/booking-rules-menu-page.php:589
#: includes/post-types/season-cpt.php:98
#: includes/post-types/season-cpt.php:108
msgid "Seasons"
msgstr "Saisons"

#: includes/admin/menu-pages/booking-rules-menu-page.php:244
msgid "Check-out days"
msgstr "Jours de départ"

#: includes/admin/menu-pages/booking-rules-menu-page.php:245
msgid "Guests can check out any day."
msgstr "Départ du client possible tous les jours."

#: includes/admin/menu-pages/booking-rules-menu-page.php:289
#: includes/admin/menu-pages/booking-rules-menu-page.php:300
msgid "Minimum stay"
msgstr "Durée de séjour minimum."

#: includes/admin/menu-pages/booking-rules-menu-page.php:290
msgid "There are no minimum stay rules."
msgstr "Il n'y a pas d'obligation de séjour minimum."

#: includes/admin/menu-pages/booking-rules-menu-page.php:301
#: includes/admin/menu-pages/booking-rules-menu-page.php:347
#: includes/admin/menu-pages/booking-rules-menu-page.php:478
#: includes/admin/menu-pages/booking-rules-menu-page.php:524
#: includes/admin/menu-pages/booking-rules-menu-page.php:570
msgid "nights"
msgstr "nuits"

#: includes/admin/menu-pages/booking-rules-menu-page.php:335
#: includes/admin/menu-pages/booking-rules-menu-page.php:346
msgid "Maximum stay"
msgstr "Durée de séjour maximum."

#: includes/admin/menu-pages/booking-rules-menu-page.php:336
msgid "There are no maximum stay rules."
msgstr "Il n'y a pas de d'obligation de séjour maximum."

#: includes/admin/menu-pages/booking-rules-menu-page.php:381
msgid "Block accommodation"
msgstr "Bloquer l'hébergement"

#: includes/admin/menu-pages/booking-rules-menu-page.php:382
msgid "There are no blocking accommodation rules."
msgstr "Il n'y a pas de règles de blocage de l'hébergement."

#: includes/admin/menu-pages/booking-rules-menu-page.php:414
#: includes/bookings-calendar.php:723
#: includes/bookings-calendar.php:817
msgid "From"
msgstr "De"

#: includes/admin/menu-pages/booking-rules-menu-page.php:424
msgid "Till"
msgstr "Jusqu'à"

#: includes/admin/menu-pages/booking-rules-menu-page.php:434
msgid "Restriction"
msgstr "Restriction"

#: includes/admin/menu-pages/booking-rules-menu-page.php:436
msgid "Not check-in rule marks the date as unavailable for check-in."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:437
msgid "Not check-out rule marks the date as unavailable for check-out."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:438
msgid "Not stay-in rule displays the date as blocked. This date is unavailable for check-in and check-out on the next date."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:439
msgid "Not stay-in with Not check-out rules completely block the selected date, additionally displaying the previous date as unavailable for check-in."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:444
#: includes/script-managers/public-script-manager.php:208
msgid "Not check-in"
msgstr "Pas de check-in"

#: includes/admin/menu-pages/booking-rules-menu-page.php:445
#: includes/script-managers/public-script-manager.php:209
msgid "Not check-out"
msgstr "Pas de check-out"

#: includes/admin/menu-pages/booking-rules-menu-page.php:446
#: includes/script-managers/public-script-manager.php:207
msgid "Not stay-in"
msgstr "Aucun séjour"

#: includes/admin/menu-pages/booking-rules-menu-page.php:454
msgid "Comment"
msgstr "Commentaire"

#: includes/admin/menu-pages/booking-rules-menu-page.php:466
#: includes/admin/menu-pages/booking-rules-menu-page.php:477
msgid "Minimum advance reservation"
msgstr "Réservation à l'avance minimum"

#: includes/admin/menu-pages/booking-rules-menu-page.php:467
msgid "There are no minimum advance reservation rules."
msgstr "Il n'y a pas de règle de réservation à l'avance."

#: includes/admin/menu-pages/booking-rules-menu-page.php:512
#: includes/admin/menu-pages/booking-rules-menu-page.php:523
msgid "Maximum advance reservation"
msgstr "Réservation à l'avance maximum"

#: includes/admin/menu-pages/booking-rules-menu-page.php:513
msgid "There are no maximum advance reservation rules."
msgstr "Il n'y a pas de règle de réservation à l'avance maximum."

#: includes/admin/menu-pages/booking-rules-menu-page.php:558
#: includes/admin/menu-pages/booking-rules-menu-page.php:569
msgid "Booking buffer"
msgstr "Réservation tampon"

#: includes/admin/menu-pages/booking-rules-menu-page.php:559
msgid "There are no booking buffer rules."
msgstr "Il n’y a pas de règle de réservation tampon."

#: includes/admin/menu-pages/calendar-menu-page.php:41
#: includes/admin/menu-pages/calendar-menu-page.php:69
msgid "Booking Calendar"
msgstr "Calendrier des réservations"

#: includes/admin/menu-pages/calendar-menu-page.php:65
msgid "Calendar"
msgstr "Calendrier"

#: includes/admin/menu-pages/create-booking-menu-page.php:135
#: includes/admin/menu-pages/create-booking-menu-page.php:169
#: includes/post-types/booking-cpt.php:244
msgid "Add New Booking"
msgstr "Ajouter une nouvelle réservation"

#: includes/admin/menu-pages/create-booking-menu-page.php:136
msgid "Clear Search Results"
msgstr "Effacer les résultats de la recherche"

#: includes/admin/menu-pages/create-booking-menu-page.php:184
#: includes/admin/menu-pages/edit-booking-menu-page.php:69
msgid "Note: booking rules are disabled in the plugin settings and are not taken into account."
msgstr "Remarque : les règles d'enregistrement sont désactivées dans la configuration du plugin et ne sont pas prises en compte."

#: includes/admin/menu-pages/create-booking/booking-step.php:50
#: includes/shortcodes/checkout-shortcode/step-booking.php:478
msgid "Unable to create booking. Please try again."
msgstr "Impossible de créer une réservation. Veuillez réessayer."

#: includes/admin/menu-pages/create-booking/booking-step.php:74
#: includes/shortcodes/checkout-shortcode/step-booking.php:107
msgid "Booking is blocked due to maintenance reason. Please try again later."
msgstr "La réservation est bloquée en raison de la maintenance. Veuillez réessayer plus tard."

#: includes/admin/menu-pages/create-booking/booking-step.php:121
#: includes/admin/menu-pages/create-booking/booking-step.php:275
#: includes/admin/menu-pages/create-booking/checkout-step.php:130
#: includes/admin/menu-pages/edit-booking/booking-control.php:34
#: includes/admin/menu-pages/edit-booking/checkout-control.php:61
#: includes/admin/menu-pages/edit-booking/summary-control.php:56
#: includes/shortcodes/checkout-shortcode/step-booking.php:183
#: includes/shortcodes/checkout-shortcode/step-booking.php:363
#: includes/shortcodes/checkout-shortcode/step-checkout.php:170
#: includes/utils/parse-utils.php:250
msgid "There are no accommodations selected for reservation."
msgstr "Il n'y a pas de logement sélectionné pour la réservation."

#: includes/admin/menu-pages/create-booking/booking-step.php:123
#: includes/admin/menu-pages/create-booking/booking-step.php:155
#: includes/admin/menu-pages/create-booking/checkout-step.php:132
#: includes/admin/menu-pages/create-booking/checkout-step.php:165
#: includes/admin/menu-pages/create-booking/checkout-step.php:196
#: includes/utils/parse-utils.php:210
#: includes/utils/parse-utils.php:285
#: includes/utils/parse-utils.php:305
msgid "Selected accommodations are not valid."
msgstr "Les hébergements sélectionnés ne sont pas valides."

#: includes/admin/menu-pages/create-booking/booking-step.php:150
#: includes/admin/menu-pages/create-booking/checkout-step.php:160
#: includes/admin/menu-pages/create-booking/step.php:191
#: includes/ajax.php:612
#: includes/shortcodes/checkout-shortcode/step-booking.php:200
#: includes/shortcodes/checkout-shortcode/step-booking.php:207
#: includes/shortcodes/checkout-shortcode/step-checkout.php:187
#: includes/shortcodes/checkout-shortcode/step-checkout.php:199
#: includes/utils/parse-utils.php:301
msgid "Accommodation Type is not valid."
msgstr "Le type d'hébergement n'est pas valide."

#: includes/admin/menu-pages/create-booking/booking-step.php:160
#: includes/admin/menu-pages/create-booking/booking-step.php:184
#: includes/ajax.php:623
#: includes/shortcodes/checkout-shortcode/step-booking.php:213
#: includes/shortcodes/checkout-shortcode/step-booking.php:231
#: includes/utils/parse-utils.php:322
msgid "Rate is not valid."
msgstr "Le tarif n'est pas valide."

#: includes/admin/menu-pages/create-booking/booking-step.php:189
#: includes/admin/menu-pages/create-booking/step.php:211
#: includes/admin/menu-pages/create-booking/step.php:215
#: includes/shortcodes/checkout-shortcode/step-booking.php:237
#: includes/shortcodes/search-results-shortcode.php:634
#: includes/utils/parse-utils.php:163
#: includes/utils/parse-utils.php:326
msgid "Adults number is not valid."
msgstr "Le nombre d'enfants n'est pas valide."

#: includes/admin/menu-pages/create-booking/booking-step.php:194
#: includes/admin/menu-pages/create-booking/step.php:235
#: includes/admin/menu-pages/create-booking/step.php:239
#: includes/ajax.php:500
#: includes/shortcodes/checkout-shortcode/step-booking.php:243
#: includes/shortcodes/search-results-shortcode.php:650
#: includes/utils/parse-utils.php:187
#: includes/utils/parse-utils.php:330
msgid "Children number is not valid."
msgstr "Le nombre d'enfants n'est pas valide."

#: includes/admin/menu-pages/create-booking/booking-step.php:199
#: includes/ajax.php:634
#: includes/shortcodes/checkout-shortcode/step-booking.php:248
#: includes/utils/parse-utils.php:334
msgid "The total number of guests is not valid."
msgstr "Le nombre total de personnes est invalide."

#: includes/admin/menu-pages/create-booking/booking-step.php:210
#: includes/admin/menu-pages/create-booking/checkout-step.php:181
#: includes/shortcodes/checkout-shortcode/step-booking.php:259
#: includes/shortcodes/checkout-shortcode/step-checkout.php:245
#: includes/utils/parse-utils.php:345
msgid "Selected dates do not meet booking rules for type %s"
msgstr "Les dates sélectionnées ne répondent pas aux règles de réservation pour le type %s"

#: includes/admin/menu-pages/create-booking/booking-step.php:263
#: includes/admin/menu-pages/create-booking/checkout-step.php:186
#: includes/utils/parse-utils.php:264
msgid "Accommodations are not available."
msgstr "Les logements ne sont pas disponibles."

#: includes/admin/menu-pages/create-booking/checkout-step.php:170
#: includes/shortcodes/checkout-shortcode/step-checkout.php:234
msgid "There are no rates for requested dates."
msgstr "Il n'y a pas de tarifs pour les dates demandées."

#: includes/admin/menu-pages/create-booking/results-step.php:211
#: includes/admin/menu-pages/settings-menu-page.php:542
#: includes/wizard.php:93
msgid "Search Results"
msgstr "Résultats de la recherche"

#: includes/admin/menu-pages/create-booking/search-step.php:47
#: includes/admin/menu-pages/create-booking/search-step.php:50
msgid "— Any —"
msgstr "— N'importe quel —"

#: includes/admin/menu-pages/create-booking/step.php:34
msgid "Search parameters are not set."
msgstr "Les paramètres de recherche ne sont pas définis."

#: includes/admin/menu-pages/create-booking/step.php:129
#: includes/ajax.php:438
#: includes/script-managers/public-script-manager.php:223
#: includes/shortcodes/checkout-shortcode/step.php:53
#: includes/shortcodes/search-results-shortcode.php:665
#: includes/utils/parse-utils.php:87
msgid "Check-in date is not valid."
msgstr "La date d'arrivée n'est pas valide."

#: includes/admin/menu-pages/create-booking/step.php:131
#: includes/shortcodes/checkout-shortcode/step.php:56
#: includes/shortcodes/search-results-shortcode.php:668
#: includes/utils/parse-utils.php:89
msgid "Check-in date cannot be earlier than today."
msgstr "La date d'arrivée ne peut être antérieure à aujourd'hui."

#: includes/admin/menu-pages/create-booking/step.php:157
#: includes/ajax.php:457
#: includes/script-managers/public-script-manager.php:224
#: includes/shortcodes/checkout-shortcode/step.php:90
#: includes/shortcodes/search-results-shortcode.php:686
#: includes/utils/parse-utils.php:120
msgid "Check-out date is not valid."
msgstr "La date de départ n'est pas valide."

#: includes/admin/menu-pages/create-booking/step.php:168
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:106
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:210
#: includes/shortcodes/checkout-shortcode/step.php:101
#: includes/shortcodes/search-results-shortcode.php:698
#: includes/utils/parse-utils.php:131
msgid "Nothing found. Please try again with different search parameters."
msgstr "Rien trouvé. Veuillez réessayer avec différents paramètres de recherche."

#: includes/admin/menu-pages/customers-menu-page.php:54
msgid "Sorry, you are not allowed to access this page."
msgstr "Désolé, vous n'êtes pas autorisé à accéder à cette page."

#: includes/admin/menu-pages/customers-menu-page.php:160
msgid "User ID"
msgstr "ID de l’utilisateur"

#: includes/admin/menu-pages/customers-menu-page.php:171
#: templates/account/account-details.php:30
msgid "Username"
msgstr "Nom d'utilisateur"

#: includes/admin/menu-pages/customers-menu-page.php:183
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:114
#: includes/bundles/customer-bundle.php:92
#: includes/csv/bookings/bookings-exporter-helper.php:81
#: includes/post-types/booking-cpt.php:90
#: includes/post-types/payment-cpt.php:247
#: includes/views/shortcodes/checkout-view.php:588
#: templates/account/account-details.php:22
msgid "First Name"
msgstr "Prénom"

#: includes/admin/menu-pages/customers-menu-page.php:195
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:118
#: includes/bundles/customer-bundle.php:101
#: includes/csv/bookings/bookings-exporter-helper.php:82
#: includes/post-types/booking-cpt.php:98
#: includes/post-types/payment-cpt.php:255
#: includes/views/shortcodes/checkout-view.php:603
#: templates/account/account-details.php:26
msgid "Last Name"
msgstr "Nom de famille"

#: includes/admin/menu-pages/customers-menu-page.php:219
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:126
#: includes/bundles/customer-bundle.php:119
#: includes/csv/bookings/bookings-exporter-helper.php:84
#: includes/post-types/booking-cpt.php:114
#: includes/post-types/payment-cpt.php:271
#: includes/views/shortcodes/checkout-view.php:633
#: templates/account/account-details.php:38
msgid "Phone"
msgstr "Téléphone"

#: includes/admin/menu-pages/customers-menu-page.php:231
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:134
#: includes/bundles/customer-bundle.php:137
#: includes/csv/bookings/bookings-exporter-helper.php:86
#: includes/post-types/booking-cpt.php:131
#: includes/views/shortcodes/checkout-view.php:675
#: templates/account/account-details.php:42
msgid "Address"
msgstr "Adresse"

#: includes/admin/menu-pages/customers-menu-page.php:243
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:142
#: includes/bundles/customer-bundle.php:155
#: includes/csv/bookings/bookings-exporter-helper.php:88
#: includes/post-types/booking-cpt.php:147
#: includes/post-types/payment-cpt.php:311
#: includes/views/shortcodes/checkout-view.php:705
#: templates/account/account-details.php:64
msgid "State / County"
msgstr "État / Département"

#: includes/admin/menu-pages/customers-menu-page.php:255
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:130
#: includes/csv/bookings/bookings-exporter-helper.php:85
#: includes/post-types/booking-cpt.php:123
#: includes/post-types/payment-cpt.php:279
#: templates/account/account-details.php:46
msgid "Country"
msgstr "Pays"

#: includes/admin/menu-pages/customers-menu-page.php:268
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:138
#: includes/bundles/customer-bundle.php:146
#: includes/csv/bookings/bookings-exporter-helper.php:87
#: includes/post-types/booking-cpt.php:139
#: includes/post-types/payment-cpt.php:303
#: includes/views/shortcodes/checkout-view.php:690
#: templates/account/account-details.php:68
msgid "City"
msgstr "Ville"

#: includes/admin/menu-pages/customers-menu-page.php:280
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:146
#: includes/bundles/customer-bundle.php:164
#: includes/csv/bookings/bookings-exporter-helper.php:89
#: includes/post-types/booking-cpt.php:155
#: includes/views/shortcodes/checkout-view.php:720
#: templates/account/account-details.php:72
msgid "Postcode"
msgstr "Code postal"

#: includes/admin/menu-pages/customers-menu-page.php:301
#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/i-cal-import-menu-page.php:162
#: includes/admin/menu-pages/i-cal-import-menu-page.php:209
#: includes/admin/menu-pages/i-cal-menu-page.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:40
#: includes/post-types/editable-cpt.php:95
msgid "Back"
msgstr "Retourner"

#: includes/admin/menu-pages/customers-menu-page.php:305
msgid "Edit User Profile"
msgstr "Modifier le profil utilisateur"

#: includes/admin/menu-pages/customers-menu-page.php:322
msgid "Customer data updated."
msgstr "Données clients mises à jour."

#: includes/admin/menu-pages/customers-menu-page.php:328
msgid "User account updated."
msgstr "Compte utilisateur mis à jour."

#: includes/admin/menu-pages/customers-menu-page.php:363
#: includes/admin/menu-pages/i-cal-menu-page.php:150
msgid "Update"
msgstr "Mettre à jour"

#: includes/admin/menu-pages/customers-menu-page.php:369
#: includes/admin/menu-pages/customers-menu-page.php:382
#: includes/admin/menu-pages/customers-menu-page.php:386
msgid "Customers"
msgstr "Clients"

#: includes/admin/menu-pages/edit-booking-menu-page.php:80
msgid "The booking is not set."
msgstr "La réservation n'est pas définie."

#: includes/admin/menu-pages/edit-booking-menu-page.php:88
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:49
msgid "The booking not found."
msgstr "La réservation n'a pas été trouvée."

#: includes/admin/menu-pages/edit-booking-menu-page.php:140
msgid "Edit Booking #%d"
msgstr "Modifier Réservation #%d"

#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/reports-menu-page.php:201
msgid "Cancel"
msgstr "annuler"

#: includes/admin/menu-pages/edit-booking-menu-page.php:227
#: includes/admin/menu-pages/edit-booking-menu-page.php:234
#: includes/post-types/booking-cpt.php:245
msgid "Edit Booking"
msgstr "Editer Réservation"

#: includes/admin/menu-pages/edit-booking/booking-control.php:18
#: includes/admin/menu-pages/edit-booking/checkout-control.php:35
#: includes/admin/menu-pages/edit-booking/edit-control.php:56
#: includes/admin/menu-pages/edit-booking/summary-control.php:31
msgid "You cannot edit the imported booking. Please update the source booking and resync your calendars."
msgstr "Vous ne pouvez pas modifier la réservation importée. Veuillez mettre à jour la réservation source et resynchroniser vos calendriers."

#: includes/admin/menu-pages/edit-booking/booking-control.php:22
#: includes/ajax-api/ajax-actions/abstract-ajax-api-action.php:142
#: includes/ajax.php:184
msgid "Request does not pass security verification. Please refresh the page and try one more time."
msgstr "La demande n'a pas passé la vérification de sécurité. Veuillez actualiser la page et réessayer."

#: includes/admin/menu-pages/edit-booking/booking-control.php:26
#: includes/admin/menu-pages/edit-booking/checkout-control.php:40
#: includes/admin/menu-pages/edit-booking/summary-control.php:33
#: includes/utils/parse-utils.php:233
msgid "Check-in date is not set."
msgstr "La date d'arrivée n'est pas définie."

#: includes/admin/menu-pages/edit-booking/booking-control.php:30
#: includes/admin/menu-pages/edit-booking/checkout-control.php:42
#: includes/admin/menu-pages/edit-booking/summary-control.php:35
#: includes/utils/parse-utils.php:235
msgid "Check-out date is not set."
msgstr "La date de départ n'est pas définie."

#: includes/admin/menu-pages/edit-booking/booking-control.php:72
msgid "Unable to update booking. Please try again."
msgstr "Impossible de mettre à jour la réservation. Veuillez réessayer."

#: includes/admin/menu-pages/edit-booking/booking-control.php:75
msgid "Booking was edited."
msgstr "La réservation a été modifiée."

#: includes/admin/menu-pages/edit-booking/edit-control.php:94
#: includes/script-managers/public-script-manager.php:203
#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Available"
msgstr "Disponible"

#: includes/admin/menu-pages/edit-booking/edit-control.php:96
#: templates/edit-booking/edit-reserved-rooms.php:71
msgid "Replace"
msgstr "Remplacer"

#: includes/admin/menu-pages/edit-booking/summary-control.php:148
msgid "— Add new —"
msgstr "— Ajouter nouveau —"

#: includes/admin/menu-pages/extensions-menu-page.php:137
#: includes/admin/menu-pages/extensions-menu-page.php:185
#: includes/admin/menu-pages/extensions-menu-page.php:190
#: includes/admin/menu-pages/settings-menu-page.php:1192
msgid "Extensions"
msgstr "Extensions"

#: includes/admin/menu-pages/extensions-menu-page.php:140
msgid "Extend the functionality of Hotel Booking plugin with the number of helpful addons for your custom purposes."
msgstr "Étendez les fonctionnalités du plugin de réservation d'hôtel avec de nombreux addons utiles pour vos besoins personnels."

#: includes/admin/menu-pages/extensions-menu-page.php:170
msgid "Get this Extension"
msgstr "Obtenir cette extension"

#: includes/admin/menu-pages/extensions-menu-page.php:178
msgid "No extensions found."
msgstr "Aucune extension trouvée."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:80
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:60
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:102
#: includes/i-cal/logs-handler.php:73
msgid "Abort Process"
msgstr "Abandonner le processus"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:81
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:61
msgid "Aborting..."
msgstr "Abandonner ..."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:161
#: includes/admin/menu-pages/i-cal-import-menu-page.php:210
#: includes/admin/menu-pages/i-cal-import-menu-page.php:224
#: includes/admin/room-list-table.php:156
msgid "Import Calendar"
msgstr "Importer le calendrier"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:166
msgid "Accommodation: %s"
msgstr "Logement : %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:169
msgid "Accommodation Type: %s"
msgstr "Type de logement : %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:176
msgid "Please be patient while the calendars are imported. You will be notified via this page when the process is completed."
msgstr "Soyez patient pendant l'importation des calendriers. Vous serez averti via cette page lorsque le processus sera terminé."

#: includes/admin/menu-pages/i-cal-menu-page.php:67
msgid "Accommodation updated."
msgstr "Hébergement mis à jour."

#: includes/admin/menu-pages/i-cal-menu-page.php:73
msgid "This calendar has already been imported for another accommodation."
msgstr "Ce calendrier a déjà été importé pour un autre logement."

#: includes/admin/menu-pages/i-cal-menu-page.php:103
msgid "Sync, Import and Export Calendars"
msgstr "Synchroniser, importer et exporter des calendriers"

#. translators: %s - room name. Example: "Comfort Triple 1"
#: includes/admin/menu-pages/i-cal-menu-page.php:113
msgid "Edit External Calendars of \"%s\""
msgstr "Modifier les calendriers externes de \"%s\""

#: includes/admin/menu-pages/i-cal-menu-page.php:122
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:101
msgid "Sync All External Calendars"
msgstr "Synchroniser tous les calendriers externes"

#: includes/admin/menu-pages/i-cal-menu-page.php:123
msgid "Sync your bookings across all online channels like Booking.com, TripAdvisor, Airbnb etc. via iCalendar file format."
msgstr "Synchronisez vos réservations sur tous les canaux en ligne comme Booking.com, TripAdvisor, Airbnb etc. via le format de fichier iCalendar."

#: includes/admin/menu-pages/i-cal-menu-page.php:219
msgid "Calendar URL"
msgstr "URL du calendrier"

#: includes/admin/menu-pages/i-cal-menu-page.php:225
msgid "Add New Calendar"
msgstr "Ajouter un nouveau calendrier"

#: includes/admin/menu-pages/i-cal-menu-page.php:233
#: includes/admin/menu-pages/i-cal-menu-page.php:237
msgid "Sync Calendars"
msgstr "Synchroniser les calendriers"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:62
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:103
#: includes/i-cal/logs-handler.php:83
msgid "Delete All Logs"
msgstr "Supprimer tous les journaux"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:63
msgid "Deleting..."
msgstr "Suppression ..."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:64
msgid "%d item"
msgstr "%d objet"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:65
msgid "%d items"
msgstr "%d objets"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:91
msgid "Calendars Synchronization Status"
msgstr "Statut de la synchronisation des calendriers"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:92
msgid "Here you can see synchronization status of your external calendars."
msgstr "Vous pouvez voir ici l'état de synchronisation de vos calendriers externes."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:134
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:148
msgid "Calendars Sync Status"
msgstr "État de la synchronisation des calendriers"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:151
msgid "Display calendars synchronization status."
msgstr "Afficher l'état de synchronisation des calendriers."

#: includes/admin/menu-pages/language-menu-page.php:10
#: includes/admin/menu-pages/language-menu-page.php:37
msgid "Language Guide"
msgstr "Guide linguistique"

#: includes/admin/menu-pages/language-menu-page.php:11
msgid "Default language"
msgstr "Langue par défaut"

#: includes/admin/menu-pages/language-menu-page.php:13
msgid "This plugin will display all system messages, labels, buttons in the language set in <em>General > Settings > Site Language</em>. If the plugin is not available in your language, you may <a href=\"%s\">contribute your translation</a>."
msgstr "Ce plugin affichera tous les messages du système, les étiquettes, les boutons de la langue dans <em>General > Settings > Site Language</em>. Si le plugin n'est pas disponible dans votre langue, vous pouvez <a href=\"%s\">contribuer votre traduction</a>."

#: includes/admin/menu-pages/language-menu-page.php:14
msgid "Custom translations and edits"
msgstr "Traductions et modifications personnalisées"

#: includes/admin/menu-pages/language-menu-page.php:15
msgid "You may customize plugin translation by editing the needed texts or adding your translation following these steps:"
msgstr "Vous pouvez personnaliser la traduction du plugin en éditant les textes nécessaires ou en ajoutant votre traduction en suivant les étapes suivantes:"

#: includes/admin/menu-pages/language-menu-page.php:17
msgid "Take the source file for your translations %s or needed translated locale."
msgstr "Prenez le fichier source pour vos traductions %s ou les paramètres régionaux nécessaires."

#: includes/admin/menu-pages/language-menu-page.php:18
msgid "Translate texts with any translation program like Poedit, Loco, Pootle etc."
msgstr "Traduisez des textes avec n'importe quel programme de traduction comme Poedit, Loco, Pootle etc."

#: includes/admin/menu-pages/language-menu-page.php:19
msgid "Put created .mo file with your translations into the folder %s. Where {lang} is ISO-639 language code and {country} is ISO-3166 country code. Example: Brazilian Portuguese file would be called motopress-hotel-booking-pt_BR.mo."
msgstr "Mettez le fichier .mo créé avec vos traductions dans le dossier %s. Où {lang} ISO-639 est le code delangue et {country} ISO-3166 est le code du pays. Par exemple : Le fichier du Portugais Brésilien serait appelé motopress-hotel-booking-pt_BR.mo."

#: includes/admin/menu-pages/language-menu-page.php:22
msgid "Multilingual content"
msgstr "Contenu multilingue"

#: includes/admin/menu-pages/language-menu-page.php:23
msgid "If your site is multilingual, you may use additional plugins to translate your added content into multiple languages allowing the site visitors to switch them."
msgstr "Si votre site est multilingue, vous pouvez utiliser des plugins supplémentaires pour traduire votre contenu ajouté en plusieurs langues permettant aux visiteurs du site de les changer."

#: includes/admin/menu-pages/language-menu-page.php:33
msgid "Language"
msgstr "Langue"

#: includes/admin/menu-pages/reports-menu-page.php:52
#: includes/admin/menu-pages/reports-menu-page.php:211
#: includes/admin/menu-pages/reports-menu-page.php:215
msgid "Reports"
msgstr "Rapports"

#: includes/admin/menu-pages/reports-menu-page.php:55
#: includes/admin/room-list-table.php:94
msgid "Export"
msgstr "Exporter"

#: includes/admin/menu-pages/reports-menu-page.php:132
#: includes/bookings-calendar.php:695
msgid "All Statuses"
msgstr "Tous les statuts"

#: includes/admin/menu-pages/reports-menu-page.php:138
msgid "Booking dates between"
msgstr "Dates de réservation entre"

#: includes/admin/menu-pages/reports-menu-page.php:139
msgid "Check-in date between"
msgstr "Date d'arrivée entre"

#: includes/admin/menu-pages/reports-menu-page.php:140
msgid "Check-out date between"
msgstr "Date de départ entre"

#: includes/admin/menu-pages/reports-menu-page.php:141
msgid "In-house between"
msgstr "disponible entre"

#: includes/admin/menu-pages/reports-menu-page.php:142
msgid "Date of reservation between"
msgstr "Date de réservation entre"

#: includes/admin/menu-pages/reports-menu-page.php:152
msgid "Export Bookings"
msgstr "Réservations à exporter"

#: includes/admin/menu-pages/reports-menu-page.php:164
msgid "Choose start date"
msgstr "Choisir la date de début"

#: includes/admin/menu-pages/reports-menu-page.php:165
msgid "Choose end date"
msgstr "Choisir la date de fin"

#: includes/admin/menu-pages/reports-menu-page.php:171
msgid "Also export imported bookings"
msgstr ""

#: includes/admin/menu-pages/reports-menu-page.php:175
msgid "Select columns to export"
msgstr "Sélectionner les colonnes à exporter"

#: includes/admin/menu-pages/reports-menu-page.php:185
msgid "Generate CSV"
msgstr "Générer CSV"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:25
msgid "Number of accommodations"
msgstr "Nombre de chambres"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:57
#: includes/payments/gateways/gateway.php:494
#: includes/widgets/rooms-widget.php:185
#: templates/create-booking/results/reserve-rooms.php:35
#: assets/blocks/blocks.js:436
#: assets/blocks/blocks.js:686
#: assets/blocks/blocks.js:1215
msgid "Title"
msgstr "Titre"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:61
msgid "Leave empty to use accommodation type title."
msgstr "Laisser vide pour utiliser le type de logement."

#: includes/admin/menu-pages/rooms-generator-menu-page.php:66
msgid "Generate"
msgstr "Générer"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:75
msgid "Accommodation generated."
msgid_plural "%s accommodations generated."
msgstr[0] "Logement généré."
msgstr[1] "%s logements générés."

#: includes/admin/menu-pages/settings-menu-page.php:113
msgid "General"
msgstr "Général"

#: includes/admin/menu-pages/settings-menu-page.php:116
msgid "Pages"
msgstr "Pages"

#: includes/admin/menu-pages/settings-menu-page.php:123
msgid "Search Results Page"
msgstr "Page de résultats de recherche"

#: includes/admin/menu-pages/settings-menu-page.php:124
msgid "Select page to display search results. Use search results shortcode on this page."
msgstr "Sélectionnez la page pour afficher les résultats de la recherche. Utilisez le shortcode des résultats de recherche sur cette page."

#: includes/admin/menu-pages/settings-menu-page.php:132
msgid "Checkout Page"
msgstr "Page de commande"

#: includes/admin/menu-pages/settings-menu-page.php:133
msgid "Select page user will be redirected to complete booking."
msgstr "Sélectionnez la page vers laquelle l'utilisateur sera redirigé quand la réservation sera complète."

#: includes/admin/menu-pages/settings-menu-page.php:141
msgid "Terms & Conditions"
msgstr "Conditions générales"

#: includes/admin/menu-pages/settings-menu-page.php:142
msgid "If you define a \"Terms\" page the customer will be asked if they accept them when checking out."
msgstr "Si vous définissez une page \"Termes et Conditions\", le client sera invité à indiquer s'il les accepte lors de la validation de sa commande."

#: includes/admin/menu-pages/settings-menu-page.php:150
msgid "Open the Terms & Conditions page in a new window"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:151
msgid "By enabling this option you can avoid errors related to displaying your terms & conditions inline for website pages created in page builders."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:159
msgid "My Account Page"
msgstr "Page Mon compte"

#: includes/admin/menu-pages/settings-menu-page.php:160
msgid "Select a page to display user account. Use the customer account shortcode on this page."
msgstr "Sélectionnez une page pour afficher votre compte utilisateur. Utilisez le shortcode du compte client sur cette page."

#: includes/admin/menu-pages/settings-menu-page.php:170
#: includes/admin/menu-pages/settings-menu-page.php:177
#: includes/post-types/payment-cpt.php:205
msgid "Currency"
msgstr "Devise"

#: includes/admin/menu-pages/settings-menu-page.php:186
msgid "Currency Position"
msgstr "Position de la devise"

#: includes/admin/menu-pages/settings-menu-page.php:195
msgid "Decimal Separator"
msgstr "Séparateur décimal"

#: includes/admin/menu-pages/settings-menu-page.php:204
msgid "Thousand Separator"
msgstr "Séparateur de milliers"

#: includes/admin/menu-pages/settings-menu-page.php:214
msgid "Number of Decimals"
msgstr "Nombre de décimales"

#: includes/admin/menu-pages/settings-menu-page.php:226
msgid "Misc"
msgstr "Divers"

#: includes/admin/menu-pages/settings-menu-page.php:233
msgid "Square Units"
msgstr "Unités carrées"

#: includes/admin/menu-pages/settings-menu-page.php:242
msgid "Datepicker Date Format"
msgstr "Datepicker Format de date"

#: includes/admin/menu-pages/settings-menu-page.php:251
#: includes/emails/templaters/email-templater.php:148
msgid "Check-out Time"
msgstr "Heure de départ"

#: includes/admin/menu-pages/settings-menu-page.php:259
#: includes/emails/templaters/email-templater.php:144
msgid "Check-in Time"
msgstr "Heure d'arrivée"

#: includes/admin/menu-pages/settings-menu-page.php:267
msgid "Bed Types"
msgstr "Types de lit"

#: includes/admin/menu-pages/settings-menu-page.php:274
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:155
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:251
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:338
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:409
#: includes/post-types/attributes-cpt.php:365
#: includes/post-types/coupon-cpt.php:79
#: includes/post-types/coupon-cpt.php:111
#: includes/post-types/coupon-cpt.php:156
msgid "Type"
msgstr "Type"

#: includes/admin/menu-pages/settings-menu-page.php:279
msgid "Add Bed Type"
msgstr "Ajouter un type de lit"

#: includes/admin/menu-pages/settings-menu-page.php:286
msgid "Show Lowest Price for"
msgstr "Afficher le prix le plus bas pour"

#: includes/admin/menu-pages/settings-menu-page.php:287
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:185
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:281
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:367
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:438
msgid "days"
msgstr "jours"

#: includes/admin/menu-pages/settings-menu-page.php:291
msgid "Lowest price of accommodation for selected number of days if check-in and check-out dates are not set. Example: set 0 to display today's lowest price, set 7 to display the lowest price for the next week."
msgstr "Le prix le plus bas du logement pour un nombre de jours choisi si les dates check-in et check-out ne sont pas indiquées. Par exemple: mettez 0 pour voir le prix le plus bas d'aujourd'hui, mettez 7 pour voir le prix le plus bas pour la semaine prochaine."

#: includes/admin/menu-pages/settings-menu-page.php:298
#: includes/post-types/coupon-cpt.php:288
msgid "Coupons"
msgstr "Coupons"

#: includes/admin/menu-pages/settings-menu-page.php:308
msgid "Default calendar view"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:309
msgid "Initial display format of the administrator bookings calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:317
msgid "Text on Checkout"
msgstr "Texte au moment de la commande"

#: includes/admin/menu-pages/settings-menu-page.php:318
msgid "This text will appear on the checkout page."
msgstr "Ce texte apparaîtra sur la page de paiement."

#: includes/admin/menu-pages/settings-menu-page.php:329
msgid "Disable Booking"
msgstr "Désactiver la réservation"

#: includes/admin/menu-pages/settings-menu-page.php:336
msgid "Hide reservation forms and buttons"
msgstr "Masquer les formulaires et les boutons de réservation"

#: includes/admin/menu-pages/settings-menu-page.php:345
msgid "Text instead of reservation form while booking is disabled"
msgstr "Texte au lieu du formulaire de réservation lors de la réservation est désactivé"

#: includes/admin/menu-pages/settings-menu-page.php:356
#: includes/admin/menu-pages/shortcodes-menu-page.php:510
#: includes/wizard.php:115
#: assets/blocks/blocks.js:1562
#: assets/blocks/blocks.js:1592
msgid "Booking Confirmation"
msgstr "Confirmation de réservation"

#: includes/admin/menu-pages/settings-menu-page.php:363
msgid "Confirmation Mode"
msgstr "Mode de confirmation"

#: includes/admin/menu-pages/settings-menu-page.php:365
msgid "By customer via email"
msgstr "Par client par e-mail"

#: includes/admin/menu-pages/settings-menu-page.php:366
msgid "By admin manually"
msgstr "Par admin manuellement"

#: includes/admin/menu-pages/settings-menu-page.php:367
msgid "Confirmation upon payment"
msgstr "Confirmation lors du paiement"

#: includes/admin/menu-pages/settings-menu-page.php:376
msgid "Booking Confirmed Page"
msgstr "Page de la Réservation Confirmée"

#: includes/admin/menu-pages/settings-menu-page.php:377
msgid "Page user will be redirected to once the booking is confirmed via email or by admin."
msgstr "Page sur laquelle est redirigé l'utilisateur une fois la réservation confirmée par email ou admin."

#: includes/admin/menu-pages/settings-menu-page.php:385
msgid "Approval Time for User"
msgstr "Temps d'approbation pour l'utilisateur"

#: includes/admin/menu-pages/settings-menu-page.php:386
msgid "Period of time in minutes the user is given to confirm booking via email. Unconfirmed bookings become Abandoned and accommodation status changes to Available."
msgstr "Période de temps en minutes dont dispose l'utilisateur confirmer la réservation par e-mail. Les réservations non confirmées sont abandonnées et l'hébergement devient disponible pour les autres clients."

#: includes/admin/menu-pages/settings-menu-page.php:396
msgid "Country of residence field is required for reservation."
msgstr "Le champ Pays de résidence est requis pour la réservation."

#: includes/admin/menu-pages/settings-menu-page.php:404
msgid "Full address fields are required for reservation."
msgstr "Les champs d'adresse complète sont requis pour la réservation."

#: includes/admin/menu-pages/settings-menu-page.php:412
msgid "Customer information is required when placing admin bookings."
msgstr "Les informations client sont requises lors de la création de réservations par l'admin."

#: includes/admin/menu-pages/settings-menu-page.php:421
msgid "Default Country on Checkout"
msgstr "Pays par défaut à la caisse"

#: includes/admin/menu-pages/settings-menu-page.php:429
#: includes/emails/templaters/email-templater.php:199
#: includes/post-types/booking-cpt.php:194
#: includes/views/shortcodes/checkout-view.php:477
msgid "Price Breakdown"
msgstr "Répartition du prix"

#: includes/admin/menu-pages/settings-menu-page.php:430
msgid "Price breakdown unfolded by default."
msgstr "La répartition des prix s'est déroulée par défaut."

#: includes/admin/menu-pages/settings-menu-page.php:439
msgid "Accounts"
msgstr "Comptes"

#: includes/admin/menu-pages/settings-menu-page.php:446
msgid "Account creation"
msgstr "Création de compte"

#: includes/admin/menu-pages/settings-menu-page.php:447
msgid "Automatically create an account for a user at checkout."
msgstr "Créez automatiquement un compte pour l'utilisateur à la caisse."

#: includes/admin/menu-pages/settings-menu-page.php:455
msgid "Allow customers to create an account during checkout."
msgstr "Permettre aux clients de créer un compte à la caisse."

#: includes/admin/menu-pages/settings-menu-page.php:463
msgid "Allow customers to log into their existing account during checkout."
msgstr "Permettre aux clients de se connecter à leur compte existant lors du passage à la caisse."

#: includes/admin/menu-pages/settings-menu-page.php:472
#: includes/upgrader.php:751
#: includes/wizard.php:164
msgid "Booking Cancellation"
msgstr "Annulation de réservation"

#: includes/admin/menu-pages/settings-menu-page.php:479
msgid "User can cancel booking via link provided inside email."
msgstr "L'utilisateur peut annuler la réservation via le lien fourni dans le courrier électronique."

#: includes/admin/menu-pages/settings-menu-page.php:487
msgid "Booking Cancelation Page"
msgstr "Page d'annulation de la réservation"

#: includes/admin/menu-pages/settings-menu-page.php:488
msgid "Page to confirm booking cancelation."
msgstr "Page de confirmation de l'annulation de la réservation."

#: includes/admin/menu-pages/settings-menu-page.php:496
msgid "Booking Canceled Page"
msgstr "Page de réservation annulée"

#: includes/admin/menu-pages/settings-menu-page.php:497
msgid "Page to redirect to after a booking is canceled."
msgstr "Page de renvoi après une réservation annulée."

#: includes/admin/menu-pages/settings-menu-page.php:506
msgid "Search Options"
msgstr "Options de recherche"

#: includes/admin/menu-pages/settings-menu-page.php:515
msgid "Max Adults"
msgstr "Nombre d'adultes maximum"

#: includes/admin/menu-pages/settings-menu-page.php:516
msgid "Maximum accommodation occupancy available in the Search Form."
msgstr "Occupation maximale de l'hébergement disponible dans le formulaire de recherche."

#: includes/admin/menu-pages/settings-menu-page.php:526
msgid "Max Children"
msgstr "Nombre d'enfants maximum"

#: includes/admin/menu-pages/settings-menu-page.php:534
msgid "Age of Child"
msgstr "Âge de l'enfant"

#: includes/admin/menu-pages/settings-menu-page.php:535
msgid "Optional description of the \"Children\" field."
msgstr "Description facultative du champ \"Enfants\"."

#: includes/admin/menu-pages/settings-menu-page.php:543
msgid "Limit search results based on the requested number of guests."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:551
msgid "Book button behavior on the search results page"
msgstr "Fonctionnement du bouton de réservation sur la page de résultats de la recherche"

#: includes/admin/menu-pages/settings-menu-page.php:552
msgid "Redirect to the checkout page immediately after successful addition to reservation."
msgstr "Rediriger directement sur la page de validation après l'ajout d'une réservation valide"

#: includes/admin/menu-pages/settings-menu-page.php:560
msgid "Recommendation"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:561
msgid "Enable search form to recommend the best set of accommodations according to a number of guests."
msgstr "Activer le formulaire de recherche pour recommander le meilleur logement selon le nombre d'invités."

#: includes/admin/menu-pages/settings-menu-page.php:570
msgid "Skip Search Results"
msgstr "Ignorer les résultats de recherche"

#: includes/admin/menu-pages/settings-menu-page.php:571
msgid "Skip search results page and enable direct booking from accommodation pages."
msgstr "Ignorer la page des résultats de recherche et permettre la réservation directe à partir des pages d'hébergement."

#: includes/admin/menu-pages/settings-menu-page.php:578
msgid "Direct Booking Form"
msgstr "Formulaire de réservation direct"

#: includes/admin/menu-pages/settings-menu-page.php:581
msgid "Show price for selected period"
msgstr "Afficher le prix pour la période sélectionnée"

#: includes/admin/menu-pages/settings-menu-page.php:582
msgid "Show price together with adults and children fields"
msgstr "Afficher le prix avec les champs adultes et enfants"

#: includes/admin/menu-pages/settings-menu-page.php:592
msgid "Enable \"adults\" and \"children\" options for my website (default)."
msgstr "Activer les options \"adultes\" et \"enfants\" pour mon site web (par défaut)."

#: includes/admin/menu-pages/settings-menu-page.php:593
msgid "Disable \"children\" option for my website (hide \"children\" field and use Guests label instead)."
msgstr "Désactivez l'option \"enfants\" pour mon site web (cachez le champ \"enfants\" et utilisez plutôt l'option \"Invités\")."

#: includes/admin/menu-pages/settings-menu-page.php:594
msgid "Disable \"adults\" and \"children\" options for my website."
msgstr "Désactivez les options \"adultes\" et \"enfants\" pour mon site web."

#: includes/admin/menu-pages/settings-menu-page.php:597
msgid "Guest Management"
msgstr "Gestion des invités"

#: includes/admin/menu-pages/settings-menu-page.php:598
msgid "Applies to frontend only."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:606
msgid "Hide \"adults\" and \"children\" fields within search availability forms."
msgstr "Masquer les champs \"adultes\" et \"enfants\" dans les formulaires de recherche de disponibilité."

#: includes/admin/menu-pages/settings-menu-page.php:614
msgid "Remember the user's selected number of guests until the checkout page."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:623
msgid "Do not apply booking rules for admin bookings."
msgstr "Ne pas appliquer les règles d'enregistrement pour les réservations de l'administration."

#: includes/admin/menu-pages/settings-menu-page.php:631
msgid "Display Options"
msgstr "Options d'affichage"

#: includes/admin/menu-pages/settings-menu-page.php:638
msgid "Display gallery images of accommodation page in lightbox."
msgstr "Afficher les images de galerie de la page du logement dans un lightbox."

#: includes/admin/menu-pages/settings-menu-page.php:645
#: includes/admin/menu-pages/shortcodes-menu-page.php:61
#: assets/blocks/blocks.js:250
#: assets/blocks/blocks.js:385
msgid "Availability Calendar"
msgstr "Calendrier des disponibilités"

#: includes/admin/menu-pages/settings-menu-page.php:646
#: includes/admin/menu-pages/shortcodes-menu-page.php:76
#: assets/blocks/blocks.js:307
msgid "Display per-night prices in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:654
#: includes/admin/menu-pages/shortcodes-menu-page.php:82
#: assets/blocks/blocks.js:318
msgid "Truncate per-night prices in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:662
#: includes/admin/menu-pages/shortcodes-menu-page.php:88
#: assets/blocks/blocks.js:329
msgid "Display the currency sign in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:672
msgid "Calendar Theme"
msgstr "Thème pour la page de Calendrier"

#: includes/admin/menu-pages/settings-menu-page.php:673
msgid "Select theme for an availability calendar."
msgstr "Sélectionnez un thème pour le Calendrier de disponibilité."

#: includes/admin/menu-pages/settings-menu-page.php:680
msgid "Template Mode"
msgstr "Mode modèle"

#: includes/admin/menu-pages/settings-menu-page.php:682
msgid "Developer Mode"
msgstr "Mode développeur"

#: includes/admin/menu-pages/settings-menu-page.php:683
msgid "Theme Mode"
msgstr "Mode thème"

#: includes/admin/menu-pages/settings-menu-page.php:685
msgid "Choose Theme Mode to display the content with the styles of your theme. Choose Developer Mode to control appearance of the content with custom page templates, actions and filters. This option can't be changed if your theme is initially integrated with the plugin."
msgstr "Choisissez le Mode Thème pour afficher le contenu avec les styles de votre thème. Choisissez le Mode Développeur pour contrôler l'apparence du contenu avec des modèles de page personnalisés, des actions et des filtres. Cette option ne peut pas être modifiée si votre thème est initialement intégré au plugin."

#: includes/admin/menu-pages/settings-menu-page.php:698
msgid "More Styles"
msgstr "Plus de Styles"

#: includes/admin/menu-pages/settings-menu-page.php:699
msgid "Extend the styling options of Hotel Booking plugin with the new free addon - Hotel Booking Styles."
msgstr "Étendez les options de style de l'extension Hotel Booking avec le nouvel addon gratuit - Hotel Booking Styles."

#: includes/admin/menu-pages/settings-menu-page.php:711
msgid "Calendars Synchronization"
msgstr "Synchronisation des calendriers"

#: includes/admin/menu-pages/settings-menu-page.php:718
msgid "Export admin blocks."
msgstr "Exporter les blocs d'administration."

#: includes/admin/menu-pages/settings-menu-page.php:726
msgid "Do not export imported bookings."
msgstr "N'exportez pas les réservations importées."

#: includes/admin/menu-pages/settings-menu-page.php:734
msgid "Export and import bookings with buffer time included."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:742
msgid "Minimize Logs"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:743
msgid "Enable the plugin to record only important messages."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:751
msgid "Calendars Synchronization Scheduler"
msgstr "Planificateur de synchronisation des calendriers"

#: includes/admin/menu-pages/settings-menu-page.php:762
msgid "Enable automatic external calendars synchronization"
msgstr "Activer la synchronisation automatique des calendriers externes"

#: includes/admin/menu-pages/settings-menu-page.php:771
msgid "Clock"
msgstr "Horloge"

#: includes/admin/menu-pages/settings-menu-page.php:772
msgid "Sync calendars at this time (UTC) or starting at this time every interval below."
msgstr "Synchroniser les calendriers à cette heure (UTC) ou à partir de cette heure tous les intervalles ci-dessous."

#: includes/admin/menu-pages/settings-menu-page.php:783
msgid "Interval"
msgstr "Intervalle"

#: includes/admin/menu-pages/settings-menu-page.php:785
#: includes/crons/cron-manager.php:102
msgid "Quarter an Hour"
msgstr "Quart d'heure"

#: includes/admin/menu-pages/settings-menu-page.php:786
#: includes/crons/cron-manager.php:107
msgid "Half an Hour"
msgstr "Demie heure"

#: includes/admin/menu-pages/settings-menu-page.php:787
msgid "Once Hourly"
msgstr "une fois par heure"

#: includes/admin/menu-pages/settings-menu-page.php:788
msgid "Twice Daily"
msgstr "Deux fois par jour"

#: includes/admin/menu-pages/settings-menu-page.php:789
msgid "Once Daily"
msgstr "une fois par jour"

#: includes/admin/menu-pages/settings-menu-page.php:800
msgid "Automatically delete sync logs older than"
msgstr "Suppression automatique des sync logs plus vieux que"

#: includes/admin/menu-pages/settings-menu-page.php:802
msgid "Day"
msgstr "Jour"

#: includes/admin/menu-pages/settings-menu-page.php:803
msgid "Week"
msgstr "Semaine"

#: includes/admin/menu-pages/settings-menu-page.php:804
#: includes/bookings-calendar.php:575
msgid "Month"
msgstr "Mois"

#: includes/admin/menu-pages/settings-menu-page.php:805
#: includes/bookings-calendar.php:576
msgid "Quarter"
msgstr "Trimestre"

#: includes/admin/menu-pages/settings-menu-page.php:806
msgid "Half a Year"
msgstr "Demie année"

#: includes/admin/menu-pages/settings-menu-page.php:807
msgid "Never Delete"
msgstr "Ne jamais effacer"

#: includes/admin/menu-pages/settings-menu-page.php:817
msgid "Block Editor"
msgstr "Éditeur de blocs"

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/admin/menu-pages/settings-menu-page.php:832
msgid "Enable block editor for \"%s\"."
msgstr "Activer l'éditeur de blocs pour \"%s\"."

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/post-types/coupon-cpt.php:59
#: includes/post-types/room-type-cpt.php:53
#: includes/post-types/room-type-cpt.php:64
#: includes/widgets/rooms-widget.php:21
msgid "Accommodation Types"
msgstr "Types de logement"

#: includes/admin/menu-pages/settings-menu-page.php:832
#: includes/csv/bookings/bookings-exporter-helper.php:97
#: includes/emails/templaters/reserved-rooms-templater.php:183
#: includes/post-types/coupon-cpt.php:136
#: includes/post-types/service-cpt.php:91
#: includes/post-types/service-cpt.php:101
#: includes/views/booking-view.php:202
msgid "Services"
msgstr "Services"

#: includes/admin/menu-pages/settings-menu-page.php:863
msgid "Admin Emails"
msgstr "E-mails d'administrateur"

#: includes/admin/menu-pages/settings-menu-page.php:876
msgid "Customer Emails"
msgstr "E-mails du client"

#: includes/admin/menu-pages/settings-menu-page.php:881
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the <a>Hotel Booking & Mailchimp Integration</a>."
msgstr "Transformez des clients occasionnels en clients fidèles en envoyant des campagnes marketing automatiques avec <a>Hotel Booking & Mailchimp Integration</a>."

#: includes/admin/menu-pages/settings-menu-page.php:884
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the Hotel Booking & Mailchimp Integration."
msgstr "Transformez des clients occasionnels en clients fidèles en envoyant des campagnes marketing automatiques avec Hotel Booking & Mailchimp Integration."

#: includes/admin/menu-pages/settings-menu-page.php:903
msgid "Cancellation Details Template"
msgstr "Modèle de page Détails d'annulation"

#: includes/admin/menu-pages/settings-menu-page.php:904
msgid "Used for %cancellation_details% tag."
msgstr "Utilisé pour la balise %cancellation_details%."

#: includes/admin/menu-pages/settings-menu-page.php:926
msgid "Email Settings"
msgstr "Paramètres d'e-mail"

#: includes/admin/menu-pages/settings-menu-page.php:931
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with <a>Hotel Booking Notifier</a>."
msgstr "Envoyez des notifications automatiques par e-mail, telles que les instructions de remise des clés, les règles de maison, avant et après l'arrivée/le départ avec <a>Hotel Booking Notifier</a>."

#: includes/admin/menu-pages/settings-menu-page.php:934
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with Hotel Booking Notifier."
msgstr "Envoyez des notifications automatiques par e-mail, telles que les instructions de remise des clés, les règles de maison, avant et après l'arrivée/le départ avec Hotel Booking Notifier."

#: includes/admin/menu-pages/settings-menu-page.php:942
msgid "Email Sender"
msgstr "Expéditeur d'e-mail"

#: includes/admin/menu-pages/settings-menu-page.php:949
msgid "Administrator Email"
msgstr "Adresse électronique de l'administrateur"

#: includes/admin/menu-pages/settings-menu-page.php:958
msgid "From Email"
msgstr "De l'email"

#: includes/admin/menu-pages/settings-menu-page.php:967
msgid "From Name"
msgstr "De Nom"

#: includes/admin/menu-pages/settings-menu-page.php:977
msgid "Logo URL"
msgstr "URL du logo"

#: includes/admin/menu-pages/settings-menu-page.php:987
msgid "Footer Text"
msgstr "Texte de pied de page"

#: includes/admin/menu-pages/settings-menu-page.php:997
msgid "Reserved Accommodation Details Template"
msgstr "Modèle de page Détails de logement réservé"

#: includes/admin/menu-pages/settings-menu-page.php:998
msgid "Used for %reserved_rooms_details% tag."
msgstr "Utilisé pour la balise %reserved_rooms_details%."

#: includes/admin/menu-pages/settings-menu-page.php:1011
msgid "Styles"
msgstr "Styles"

#: includes/admin/menu-pages/settings-menu-page.php:1018
msgid "Base Color"
msgstr "Couleur de base"

#: includes/admin/menu-pages/settings-menu-page.php:1027
msgid "Background Color"
msgstr "Couleur de fond"

#: includes/admin/menu-pages/settings-menu-page.php:1036
msgid "Body Background Color"
msgstr "Couleur du fond du corps"

#: includes/admin/menu-pages/settings-menu-page.php:1045
msgid "Body Text Color"
msgstr "Couleur du texte"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "Payment Gateways"
msgstr "Passerelles de paiement"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "General Settings"
msgstr "Paramètres généraux"

#: includes/admin/menu-pages/settings-menu-page.php:1071
msgid "Need more gateways? Use our Hotel Booking <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a> extension."
msgstr "Besoin de plus de passerelles ? Utilisez notre service de réservation d'hôtel <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a> extension."

#: includes/admin/menu-pages/settings-menu-page.php:1075
msgid "You may also email the <a href=\"%s\" target=\"_blank\">balance payment request</a> link to your guests."
msgstr "Vous pouvez également envoyer le lien <a href=\"%s\" target=\"_blank\">demande de paiement du solde </a> à vos invités."

#: includes/admin/menu-pages/settings-menu-page.php:1086
msgid "User Pays"
msgstr "Pays de l'utilisateur"

#: includes/admin/menu-pages/settings-menu-page.php:1088
msgid "Full Amount"
msgstr "Montant total"

#: includes/admin/menu-pages/settings-menu-page.php:1089
#: includes/views/booking-view.php:463
msgid "Deposit"
msgstr "Acompte"

#: includes/admin/menu-pages/settings-menu-page.php:1098
msgid "Deposit Type"
msgstr "Type d'acompte"

#: includes/admin/menu-pages/settings-menu-page.php:1100
#: includes/post-types/coupon-cpt.php:115
#: includes/post-types/coupon-cpt.php:160
msgid "Fixed"
msgstr "Fixé"

#: includes/admin/menu-pages/settings-menu-page.php:1101
msgid "Percent"
msgstr "Pourcentage"

#: includes/admin/menu-pages/settings-menu-page.php:1110
msgid "Deposit Amount"
msgstr "Montant de l'acompte"

#: includes/admin/menu-pages/settings-menu-page.php:1121
msgid "Deposit Time Frame (days)"
msgstr "Délai de dépôt (jours)"

#: includes/admin/menu-pages/settings-menu-page.php:1122
msgid "Apply deposit to bookings made in at least the selected number of days prior to the check-in date. Otherwise, the full amount is charged."
msgstr "Appliquer l’acompte aux réservations effectuées à au moins le nombre de jours sélectionné avant la date d'arrivée. Sinon, le montant total est facturé."

#: includes/admin/menu-pages/settings-menu-page.php:1133
msgid "Force Secure Checkout"
msgstr "Activer le paiement sécurisé"

#: includes/admin/menu-pages/settings-menu-page.php:1135
msgid "Force SSL (HTTPS) on the checkout pages. You must have an SSL certificate installed to use this option."
msgstr "SSL active (HTTPS) sur les pages de paiement. Vous devez disposer d'un certificat SSL pour utiliser cette option."

#: includes/admin/menu-pages/settings-menu-page.php:1142
msgid "Reservation Received Page"
msgstr "Page de la Réservation Reçue"

#: includes/admin/menu-pages/settings-menu-page.php:1150
msgid "Failed Transaction Page"
msgstr "Page Échec de la transaction"

#: includes/admin/menu-pages/settings-menu-page.php:1158
msgid "Default Gateway"
msgstr "Passerelle par défaut"

#: includes/admin/menu-pages/settings-menu-page.php:1172
#: includes/payments/gateways/bank-gateway.php:127
msgid "Pending Payment Time"
msgstr "Délai de paiement"

#: includes/admin/menu-pages/settings-menu-page.php:1173
msgid "Period of time in minutes the user is given to complete payment. Unpaid bookings become Abandoned and accommodation status changes to Available."
msgstr "Période de temps en minutes dont dispose l'utilisateur pour effectuer le paiement. Les réservations non payées sont abandonnées et l'hébergement devient disponible pour les autres clients."

#: includes/admin/menu-pages/settings-menu-page.php:1195
msgid "Install <a href=\"%s\" target=\"_blank\">Hotel Booking addons</a> to manage their settings."
msgstr "Installez <a href=\"%s\" target=\"_blank\">Hotel Booking addons</a> pour gérer leurs paramètres."

#: includes/admin/menu-pages/settings-menu-page.php:1197
msgid "Install Hotel Booking addons to manage their settings."
msgstr "Installez les modules complémentaires de réservation d'hôtel pour gérer leurs paramètres."

#: includes/admin/menu-pages/settings-menu-page.php:1214
msgid "Advanced"
msgstr "Avancé"

#: includes/admin/menu-pages/settings-menu-page.php:1226
#: includes/admin/menu-pages/settings-menu-page.php:1228
msgid "License"
msgstr "Licence"

#: includes/admin/menu-pages/settings-menu-page.php:1305
msgid "Settings saved."
msgstr "Paramètres sauvegardés."

#: includes/admin/menu-pages/settings-menu-page.php:1344
msgid "<strong>Note:</strong> Payment methods will appear on the checkout page only when Confirmation Upon Payment is enabled in Accommodation > Settings > General > Confirmation Mode."
msgstr "<strong>Note:</strong> Les méthodes de paiement n'apparaîtront sur la page de paiement que lorsque la fonction Confirmation sur paiement est activée dans Hébergement > Paramètres > Général > Mode confirmation."

#: includes/admin/menu-pages/settings-menu-page.php:1432
#: includes/admin/menu-pages/settings-menu-page.php:1436
#: assets/blocks/blocks.js:141
#: assets/blocks/blocks.js:276
#: assets/blocks/blocks.js:429
#: assets/blocks/blocks.js:679
#: assets/blocks/blocks.js:1196
#: assets/blocks/blocks.js:1419
#: assets/blocks/blocks.js:1501
msgid "Settings"
msgstr "Paramètres"

#: includes/admin/menu-pages/shortcodes-menu-page.php:21
#: assets/blocks/blocks.js:117
msgid "Availability Search Form"
msgstr "Formulaire de recherche de disponibilité"

#: includes/admin/menu-pages/shortcodes-menu-page.php:22
msgid "Display search form."
msgstr "Afficher le formulaire de recherche."

#: includes/admin/menu-pages/shortcodes-menu-page.php:25
#: assets/blocks/blocks.js:148
msgid "The number of adults presetted in the search form."
msgstr "Le nombre d'adultes prédéfini dans le formulaire de recherche."

#: includes/admin/menu-pages/shortcodes-menu-page.php:30
#: assets/blocks/blocks.js:163
msgid "The number of children presetted in the search form."
msgstr "Le nombre d'enfants prédéfini dans le formulaire de recherche."

#: includes/admin/menu-pages/shortcodes-menu-page.php:35
msgid "Check-in date presetted in the search form."
msgstr "Date d'arrivée prédéfinie dans le formulaire de recherche."

#: includes/admin/menu-pages/shortcodes-menu-page.php:36
#: includes/admin/menu-pages/shortcodes-menu-page.php:41
msgid "date in format %s"
msgstr "date au format %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:40
msgid "Check-out date presetted in the search form."
msgstr "Date de départ prédéfinie dans le formulaire de recherche."

#: includes/admin/menu-pages/shortcodes-menu-page.php:45
#: assets/blocks/blocks.js:201
msgid "Custom attributes for advanced search."
msgstr "Attributs personnalisés pour la recherche avancée."

#: includes/admin/menu-pages/shortcodes-menu-page.php:46
#: assets/blocks/blocks.js:202
msgid "Comma-separated slugs of attributes."
msgstr "Slugs séparés par des virgules."

#: includes/admin/menu-pages/shortcodes-menu-page.php:50
#: includes/admin/menu-pages/shortcodes-menu-page.php:94
#: includes/admin/menu-pages/shortcodes-menu-page.php:180
#: includes/admin/menu-pages/shortcodes-menu-page.php:259
#: includes/admin/menu-pages/shortcodes-menu-page.php:361
#: includes/admin/menu-pages/shortcodes-menu-page.php:422
#: includes/admin/menu-pages/shortcodes-menu-page.php:450
#: includes/admin/menu-pages/shortcodes-menu-page.php:470
#: includes/admin/menu-pages/shortcodes-menu-page.php:494
#: includes/admin/menu-pages/shortcodes-menu-page.php:514
#: includes/admin/menu-pages/shortcodes-menu-page.php:530
#: includes/admin/menu-pages/shortcodes-menu-page.php:546
msgid "Custom CSS class for shortcode wrapper"
msgstr "Classe CSS personnalisée pour l'enveloppe shortcode"

#: includes/admin/menu-pages/shortcodes-menu-page.php:51
#: includes/admin/menu-pages/shortcodes-menu-page.php:95
#: includes/admin/menu-pages/shortcodes-menu-page.php:181
#: includes/admin/menu-pages/shortcodes-menu-page.php:260
#: includes/admin/menu-pages/shortcodes-menu-page.php:362
#: includes/admin/menu-pages/shortcodes-menu-page.php:423
#: includes/admin/menu-pages/shortcodes-menu-page.php:451
#: includes/admin/menu-pages/shortcodes-menu-page.php:471
#: includes/admin/menu-pages/shortcodes-menu-page.php:495
#: includes/admin/menu-pages/shortcodes-menu-page.php:515
#: includes/admin/menu-pages/shortcodes-menu-page.php:531
#: includes/admin/menu-pages/shortcodes-menu-page.php:547
msgid "whitespace separated css classes"
msgstr "classes de css séparées par des espaces vides"

#: includes/admin/menu-pages/shortcodes-menu-page.php:65
#: includes/admin/menu-pages/shortcodes-menu-page.php:465
#: includes/admin/menu-pages/shortcodes-menu-page.php:489
#: includes/csv/bookings/bookings-exporter-helper.php:76
#: includes/emails/templaters/reserved-rooms-templater.php:187
msgid "Accommodation Type ID"
msgstr "ID du type d'hébergement"

#: includes/admin/menu-pages/shortcodes-menu-page.php:66
#: includes/admin/menu-pages/shortcodes-menu-page.php:466
msgid "ID of Accommodation Type to check availability."
msgstr "ID du type d'hébergement pour vérifier la disponibilité."

#: includes/admin/menu-pages/shortcodes-menu-page.php:67
#: includes/admin/menu-pages/shortcodes-menu-page.php:378
#: includes/admin/menu-pages/shortcodes-menu-page.php:467
#: includes/admin/menu-pages/shortcodes-menu-page.php:491
msgid "integer number"
msgstr "nombre entier"

#: includes/admin/menu-pages/shortcodes-menu-page.php:70
#: assets/blocks/blocks.js:295
msgid "How many months to show."
msgstr "Combien de mois à afficher."

#: includes/admin/menu-pages/shortcodes-menu-page.php:72
#: assets/blocks/blocks.js:296
msgid "Set the number of columns or the number of rows and columns separated by comma. Example: \"3\" or \"2,3\""
msgstr "Définissez le nombre de colonnes ou le nombre de lignes et de colonnes séparées par des virgules. Exemple : \"3\" ou \"2,3\""

#: includes/admin/menu-pages/shortcodes-menu-page.php:106
#: assets/blocks/blocks.js:251
msgid "Display availability calendar of the current accommodation type or by ID."
msgstr "Afficher le calendrier des disponibilités de l'hébergement actuel par type ou par ID."

#: includes/admin/menu-pages/shortcodes-menu-page.php:111
#: assets/blocks/blocks.js:397
#: assets/blocks/blocks.js:630
msgid "Availability Search Results"
msgstr "Résultats de recherche"

#: includes/admin/menu-pages/shortcodes-menu-page.php:112
#: assets/blocks/blocks.js:398
msgid "Display listing of accommodation types that meet the search criteria."
msgstr "Afficher la liste des types d'hébergement qui correspondent aux critères de recherche."

#: includes/admin/menu-pages/shortcodes-menu-page.php:115
#: includes/admin/menu-pages/shortcodes-menu-page.php:212
#: includes/admin/menu-pages/shortcodes-menu-page.php:381
#: assets/blocks/blocks.js:437
#: assets/blocks/blocks.js:687
#: assets/blocks/blocks.js:1216
msgid "Whether to display title of the accommodation type."
msgstr "Si vous souhaitez indiquer le titre du type d'hébergement."

#: includes/admin/menu-pages/shortcodes-menu-page.php:120
#: includes/admin/menu-pages/shortcodes-menu-page.php:217
#: includes/admin/menu-pages/shortcodes-menu-page.php:386
#: assets/blocks/blocks.js:449
#: assets/blocks/blocks.js:699
#: assets/blocks/blocks.js:1228
msgid "Whether to display featured image of the accommodation type."
msgstr "Si vous souhaitez afficher l'image du type d'hébergement."

#: includes/admin/menu-pages/shortcodes-menu-page.php:125
#: includes/admin/menu-pages/shortcodes-menu-page.php:222
#: includes/admin/menu-pages/shortcodes-menu-page.php:391
#: assets/blocks/blocks.js:461
#: assets/blocks/blocks.js:711
#: assets/blocks/blocks.js:1240
msgid "Whether to display gallery of the accommodation type."
msgstr "Si vous souhaitez afficher la galerie du type d'hébergement."

#: includes/admin/menu-pages/shortcodes-menu-page.php:130
#: includes/admin/menu-pages/shortcodes-menu-page.php:227
#: includes/admin/menu-pages/shortcodes-menu-page.php:396
#: assets/blocks/blocks.js:473
#: assets/blocks/blocks.js:723
#: assets/blocks/blocks.js:1252
msgid "Whether to display excerpt (short description) of the accommodation type."
msgstr "Si vous souhaitez afficher un extrait (description brève) du type d'hébergement."

#: includes/admin/menu-pages/shortcodes-menu-page.php:135
#: includes/admin/menu-pages/shortcodes-menu-page.php:232
#: includes/admin/menu-pages/shortcodes-menu-page.php:401
#: assets/blocks/blocks.js:485
#: assets/blocks/blocks.js:735
#: assets/blocks/blocks.js:1264
msgid "Whether to display details of the accommodation type."
msgstr "Si vous souhaitez afficher les détails du type d'hébergement."

#: includes/admin/menu-pages/shortcodes-menu-page.php:140
#: includes/admin/menu-pages/shortcodes-menu-page.php:237
#: includes/admin/menu-pages/shortcodes-menu-page.php:406
#: includes/admin/menu-pages/shortcodes-menu-page.php:427
#: assets/blocks/blocks.js:497
#: assets/blocks/blocks.js:747
#: assets/blocks/blocks.js:1276
msgid "Whether to display price of the accommodation type."
msgstr "Indiquer le prix du type d'hébergement."

#: includes/admin/menu-pages/shortcodes-menu-page.php:145
#: includes/admin/menu-pages/shortcodes-menu-page.php:242
#: includes/admin/menu-pages/shortcodes-menu-page.php:411
msgid "Show View Details button"
msgstr "Afficher le bouton Afficher les détails"

#: includes/admin/menu-pages/shortcodes-menu-page.php:146
#: includes/admin/menu-pages/shortcodes-menu-page.php:243
#: includes/admin/menu-pages/shortcodes-menu-page.php:412
#: assets/blocks/blocks.js:509
#: assets/blocks/blocks.js:759
#: assets/blocks/blocks.js:1288
msgid "Whether to display \"View Details\" button with the link to accommodation type."
msgstr "Pour afficher le bouton \"Afficher les détails\" avec le lien vers le type d'hébergement."

#: includes/admin/menu-pages/shortcodes-menu-page.php:151
#: includes/admin/menu-pages/shortcodes-menu-page.php:284
#: includes/admin/menu-pages/shortcodes-menu-page.php:332
msgid "Sort by."
msgstr "Trier par."

#: includes/admin/menu-pages/shortcodes-menu-page.php:153
#: includes/admin/menu-pages/shortcodes-menu-page.php:173
#: includes/admin/menu-pages/shortcodes-menu-page.php:286
#: includes/admin/menu-pages/shortcodes-menu-page.php:306
#: includes/admin/menu-pages/shortcodes-menu-page.php:334
#: includes/admin/menu-pages/shortcodes-menu-page.php:354
msgid "%1$s. See the <a href=\"%2$s\" target=\"_blank\">full list</a>."
msgstr "%1$s. Voir le <a href=\"%2$s\" target=\"_blank\">liste complète</a>."

#: includes/admin/menu-pages/shortcodes-menu-page.php:160
#: includes/admin/menu-pages/shortcodes-menu-page.php:293
#: includes/admin/menu-pages/shortcodes-menu-page.php:341
msgid "Designates the ascending or descending order of sorting."
msgstr "Désigne l'ordre croissant ou décroissant du tri."

#: includes/admin/menu-pages/shortcodes-menu-page.php:162
#: includes/admin/menu-pages/shortcodes-menu-page.php:295
#: includes/admin/menu-pages/shortcodes-menu-page.php:343
msgid "ASC - from lowest to highest values (1, 2, 3). DESC - from highest to lowest values (3, 2, 1)."
msgstr "ASC - de la valeur la plus basse à la plus haute (1, 2, 3). DESC - de la valeur la plus élevée à la plus faible (3, 2, 1)."

#: includes/admin/menu-pages/shortcodes-menu-page.php:166
#: includes/admin/menu-pages/shortcodes-menu-page.php:299
#: includes/admin/menu-pages/shortcodes-menu-page.php:347
#: assets/blocks/blocks.js:574
#: assets/blocks/blocks.js:910
#: assets/blocks/blocks.js:1094
msgid "Custom field name. Required if \"orderby\" is one of the \"meta_value\", \"meta_value_num\" or \"meta_value_*\"."
msgstr "Nom du champ personnalisé. Requis si \"orderby\" est l'une des \"meta_value\", \"meta_value_num\" ou \"meta_value_*\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:167
#: includes/admin/menu-pages/shortcodes-menu-page.php:300
#: includes/admin/menu-pages/shortcodes-menu-page.php:348
msgid "custom field name"
msgstr "nom de champ personnalisé"

#: includes/admin/menu-pages/shortcodes-menu-page.php:168
#: includes/admin/menu-pages/shortcodes-menu-page.php:177
#: includes/admin/menu-pages/shortcodes-menu-page.php:301
#: includes/admin/menu-pages/shortcodes-menu-page.php:310
#: includes/admin/menu-pages/shortcodes-menu-page.php:349
#: includes/admin/menu-pages/shortcodes-menu-page.php:358
#: includes/admin/menu-pages/shortcodes-menu-page.php:645
msgid "empty string"
msgstr "chaîne vide"

#: includes/admin/menu-pages/shortcodes-menu-page.php:171
#: includes/admin/menu-pages/shortcodes-menu-page.php:304
#: includes/admin/menu-pages/shortcodes-menu-page.php:352
msgid "Specified type of the custom field. Can be used in conjunction with orderby=\"meta_value\"."
msgstr "Type spécifié du champ personnalisé. Peut être utilisé avec orderby=\"meta_value\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:185
msgid "Sort by. Use \"orderby\" insted."
msgstr "Trier par. Utilisez plustôt \"orderby\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:191
#: includes/admin/menu-pages/shortcodes-menu-page.php:248
msgid "Show Book button"
msgstr "Afficher le bouton Réserver"

#: includes/admin/menu-pages/shortcodes-menu-page.php:192
#: includes/admin/menu-pages/shortcodes-menu-page.php:249
#: includes/admin/menu-pages/shortcodes-menu-page.php:417
#: assets/blocks/blocks.js:771
#: assets/blocks/blocks.js:1300
msgid "Whether to display Book button."
msgstr "Afficher le bouton Réserver."

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "NOTE:"
msgstr "REMARQUE :"

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
msgid "Use only on page that you set as Search Results Page in <a href=\"%s\">Settings</a>"
msgstr "Utilisez uniquement la page que vous avez définie comme page de résultats de recherche dans <a href=\"%s\">Paramètres</a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:209
#: assets/blocks/blocks.js:642
msgid "Accommodation Types Listing"
msgstr "Liste des Types d'hébergement"

#: includes/admin/menu-pages/shortcodes-menu-page.php:254
#: includes/admin/menu-pages/shortcodes-menu-page.php:327
#: assets/blocks/blocks.js:804
#: assets/blocks/blocks.js:1028
msgid "Count per page"
msgstr "Nombre par page"

#: includes/admin/menu-pages/shortcodes-menu-page.php:255
#: assets/blocks/blocks.js:805
msgid "integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "valeurs, -1 pour tout afficher, par défaut : \"Les pages du blog montrent au maximum\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:264
#: assets/blocks/blocks.js:817
msgid "IDs of categories that will be shown."
msgstr "IDs des catégories qui seront montrés."

#: includes/admin/menu-pages/shortcodes-menu-page.php:265
#: includes/admin/menu-pages/shortcodes-menu-page.php:270
#: includes/admin/menu-pages/shortcodes-menu-page.php:275
#: includes/admin/menu-pages/shortcodes-menu-page.php:323
#: assets/blocks/blocks.js:1017
msgid "Comma-separated IDs."
msgstr "IDs séparés par des virgules."

#: includes/admin/menu-pages/shortcodes-menu-page.php:269
#: assets/blocks/blocks.js:829
msgid "IDs of tags that will be shown."
msgstr "IDs des étiquettes qui seront montrés."

#: includes/admin/menu-pages/shortcodes-menu-page.php:274
#: assets/blocks/blocks.js:793
msgid "IDs of accommodations that will be shown."
msgstr "IDs des logements qui seront montrés."

#: includes/admin/menu-pages/shortcodes-menu-page.php:279
#: assets/blocks/blocks.js:841
msgid "Logical relationship between each taxonomy when there is more than one."
msgstr "Relation logique entre chaque taxonomie lorsqu'il y en a plus d'une."

#: includes/admin/menu-pages/shortcodes-menu-page.php:319
#: assets/blocks/blocks.js:983
msgid "Services Listing"
msgstr "Liste des services"

#: includes/admin/menu-pages/shortcodes-menu-page.php:322
#: assets/blocks/blocks.js:792
msgid "IDs"
msgstr "Identifiants"

#: includes/admin/menu-pages/shortcodes-menu-page.php:324
#: assets/blocks/blocks.js:1016
msgid "IDs of services that will be shown. "
msgstr "ID des services qui seront affichés. "

#: includes/admin/menu-pages/shortcodes-menu-page.php:368
msgid "Show All Services"
msgstr "Afficher tous les services"

#: includes/admin/menu-pages/shortcodes-menu-page.php:373
#: assets/blocks/blocks.js:1167
#: assets/blocks/blocks.js:1344
msgid "Single Accommodation Type"
msgstr "Type d'hébergement simple"

#: includes/admin/menu-pages/shortcodes-menu-page.php:377
msgid "ID of accommodation type to display."
msgstr "ID du type de logement à afficher."

#: includes/admin/menu-pages/shortcodes-menu-page.php:441
msgid "Display accommodation type with title and image."
msgstr "Afficher le type d'hébergement avec titre et image."

#: includes/admin/menu-pages/shortcodes-menu-page.php:446
#: assets/blocks/blocks.js:1356
#: assets/blocks/blocks.js:1386
msgid "Checkout Form"
msgstr "Formulaire de commande"

#: includes/admin/menu-pages/shortcodes-menu-page.php:447
#: assets/blocks/blocks.js:1357
msgid "Display checkout form."
msgstr "Afficher le formulaire de commande."

#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "Use only on page that you set as Checkout Page in <a href=\"%s\">Settings</a>"
msgstr "Utilisez uniquement la page que vous avez définie comme page de paiement dans <a href=\"%s\"> Paramètres </a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:462
#: assets/blocks/blocks.js:1398
#: assets/blocks/blocks.js:1468
msgid "Booking Form"
msgstr "Formulaire de reservation"

#: includes/admin/menu-pages/shortcodes-menu-page.php:481
msgid "Show Booking Form for Accommodation Type with id 777"
msgstr "Afficher le formulaire de réservation pour le type d'hébergement avec l'identifiant 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:486
#: assets/blocks/blocks.js:1480
#: assets/blocks/blocks.js:1550
msgid "Accommodation Rates List"
msgstr "Liste des tarifs d'hébergement"

#: includes/admin/menu-pages/shortcodes-menu-page.php:490
#: assets/blocks/blocks.js:1204
msgid "ID of accommodation type."
msgstr "ID du type d'hébergement."

#: includes/admin/menu-pages/shortcodes-menu-page.php:505
msgid "Show Accommodation Rates List for accommodation type with id 777"
msgstr "Afficher la liste des tarifs pour le logement pour type de logement avec id 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:511
#: assets/blocks/blocks.js:1563
msgid "Display booking and payment details."
msgstr "Afficher les détails de la réservation et du paiement."

#: includes/admin/menu-pages/shortcodes-menu-page.php:521
msgid "Use this shortcode on Booking Confirmed and Reservation Received pages"
msgstr "Utilisez ce code court sur les pages de Réservation Confirmée et Réservation Reçue"

#: includes/admin/menu-pages/shortcodes-menu-page.php:526
msgid "Booking Cancelation"
msgstr "Annulation de réservation"

#: includes/admin/menu-pages/shortcodes-menu-page.php:527
msgid "Display booking cancelation details."
msgstr "Afficher les détails de l'annulation de la réservation."

#: includes/admin/menu-pages/shortcodes-menu-page.php:537
msgid "Use this shortcode on the Booking Cancelation page"
msgstr "Utilisez ce shortcode sur la page d'annulation de la réservation"

#: includes/admin/menu-pages/shortcodes-menu-page.php:542
msgid "Customer Account"
msgstr "Compte client"

#: includes/admin/menu-pages/shortcodes-menu-page.php:543
msgid "Display log in form or customer account area."
msgstr "Afficher la page de connexion ou la section du compte client."

#: includes/admin/menu-pages/shortcodes-menu-page.php:553
msgid "Use this shortcode to create the My Account page."
msgstr "Utilisez ce shortcode pour créer la page Mon compte."

#: includes/admin/menu-pages/shortcodes-menu-page.php:565
#: includes/admin/menu-pages/shortcodes-menu-page.php:699
#: includes/admin/menu-pages/shortcodes-menu-page.php:703
msgid "Shortcodes"
msgstr "Shortcodes"

#: includes/admin/menu-pages/shortcodes-menu-page.php:569
msgid "Shortcode"
msgstr "Shortcode"

#: includes/admin/menu-pages/shortcodes-menu-page.php:570
#: includes/post-types/attributes-cpt.php:307
msgid "Parameters"
msgstr "Paramètres"

#: includes/admin/menu-pages/shortcodes-menu-page.php:571
msgid "Example"
msgstr "Exemple"

#: includes/admin/menu-pages/shortcodes-menu-page.php:603
#: includes/admin/menu-pages/shortcodes-menu-page.php:625
msgid "Deprecated since %s"
msgstr "Déconseillé depuis %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:635
msgid "Values:"
msgstr "Valeurs:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:640
msgid "Default:"
msgstr "Défaut:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:687
msgid "Optional."
msgstr "Facultatif."

#: includes/admin/menu-pages/shortcodes-menu-page.php:695
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:30
#: includes/payments/gateways/gateway.php:365
#: includes/views/shortcodes/checkout-view.php:247
#: includes/views/shortcodes/checkout-view.php:270
#: includes/views/shortcodes/checkout-view.php:538
#: includes/views/shortcodes/checkout-view.php:572
#: templates/account/account-details.php:34
msgid "Required"
msgstr "Champs obligatoires"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:34
msgid "Taxes and fees saved."
msgstr "Taxes et frais enregistré"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:41
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:459
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:463
msgid "Taxes & Fees"
msgstr "Taxes et frais"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:136
#: includes/csv/bookings/bookings-exporter-helper.php:102
#: includes/views/booking-view.php:296
msgid "Fees"
msgstr "Frais"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:137
msgid "No fees have been created yet."
msgstr "Aucun frais n'a encore été créé."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:138
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:234
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:321
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:392
#: includes/post-types/booking-cpt.php:219
msgid "Add new"
msgstr "Ajouter nouveau"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:146
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:242
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:329
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:400
msgid "Label"
msgstr "Étiquettes"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:147
msgid "New fee"
msgstr "nouvelle taxe"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:158
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:254
msgid "Per guest / per day"
msgstr "Par personne / par jour"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:159
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:255
msgid "Per accommodation / per day"
msgstr "Par logement / par jour"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:160
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:256
msgid "Per accommodation (%)"
msgstr "Par logement (%)"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:182
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:278
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:364
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:435
msgid "Limit"
msgstr "Limite"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:183
msgid "How often this fee is charged. Set 0 to charge each day of the stay period. Set 1 to charge once."
msgstr "La fréquence à laquelle ces frais sont facturés. Choisissez 0 pour créer une facture pour chaque jour de la période de séjour. Choisissez 1 pour facturer une fois."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:197
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:200
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:293
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:296
msgid "Include"
msgstr "Inclure"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:198
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:294
msgid "Show accommodation rate with this charge included"
msgstr "Afficher le montant de l'hébergement incluant cette charge"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:232
#: includes/csv/bookings/bookings-exporter-helper.php:95
#: includes/views/booking-view.php:171
msgid "Accommodation Taxes"
msgstr "Taxes d'hébergement"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:233
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:320
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:391
msgid "No taxes have been created yet."
msgstr "Aucune taxe n'a encore été créée."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:243
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:330
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:401
msgid "New tax"
msgstr "Nouvelle taxe"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:279
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:365
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:436
msgid "Limit of days the fee is charged. Set 0 to charge each day of stay period. Set 1 to charge once."
msgstr "Limite de jours de facturation des frais. Choisissez 0 pour créer une facture pour chaque jour de la période de séjour. Choisissez 1 pour facturer une fois."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:319
#: includes/views/booking-view.php:265
msgid "Service Taxes"
msgstr "taxe de service"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:341
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:412
#: includes/post-types/coupon-cpt.php:114
#: includes/post-types/coupon-cpt.php:159
msgid "Percentage"
msgstr "pourcentage"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:390
#: includes/views/booking-view.php:349
msgid "Fee Taxes"
msgstr "Taxes d'honoraires"

#: includes/admin/room-list-table.php:95
msgid "External Calendars"
msgstr "Calendriers externes"

#: includes/admin/room-list-table.php:157
#: includes/admin/room-list-table.php:211
msgid "Sync External Calendars"
msgstr "Synchroniser les calendriers externes"

#: includes/admin/room-list-table.php:163
#: includes/admin/sync-rooms-list-table.php:65
msgctxt "Placeholder for empty accommodation title"
msgid "(no title)"
msgstr "(sans-titre)"

#: includes/admin/room-list-table.php:185
msgid "Download Calendar"
msgstr "Télécharger le calendrier"

#: includes/admin/sync-logs-list-table.php:73
msgid "Message"
msgstr "Message"

#: includes/admin/sync-logs-list-table.php:82
msgid "Success"
msgstr "Réussite"

#: includes/admin/sync-logs-list-table.php:85
msgid "Info"
msgstr "Information"

#: includes/admin/sync-logs-list-table.php:88
msgid "Warning"
msgstr "avertissement"

#: includes/admin/sync-rooms-list-table.php:71
msgctxt "This is date and time format 31/12/2017 - 23:59:59"
msgid "d/m/Y - H:i:s"
msgstr "d/m/Y - H:i:s"

#: includes/admin/sync-rooms-list-table.php:75
#: includes/ajax.php:945
msgid "Waiting"
msgstr "En attente"

#: includes/admin/sync-rooms-list-table.php:78
#: includes/ajax.php:948
msgid "Processing"
msgstr "En traitement"

#: includes/admin/sync-rooms-list-table.php:128
msgctxt "Total number of processed bookings"
msgid "Total"
msgstr "Total"

#: includes/admin/sync-rooms-list-table.php:129
msgid "Succeed"
msgstr "Réussi"

#: includes/admin/sync-rooms-list-table.php:130
msgid "Skipped"
msgstr "Ignoré"

#: includes/admin/sync-rooms-list-table.php:131
msgid "Failed"
msgstr "Échoué"

#: includes/admin/sync-rooms-list-table.php:132
msgid "Removed"
msgstr "Supprimée"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:58
msgid "Copying to clipboard failed. Please press Ctrl/Cmd+C to copy."
msgstr "Échec de la copie dans le presse-papiers. Veuillez appuyer sur Ctrl/Cmd+C pour copier."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:80
msgid "Description is missing."
msgstr "Description manquante."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:83
msgid "User is missing."
msgstr "Utilisateur manquant."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:86
msgid "Permission is missing."
msgstr "Autorisation manquante."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:102
msgid "You do not have permission to assign API Keys to the selected user."
msgstr "Vous n'avez pas l'autorisation d'attribuer des clés d'API à l'utilisateur sélectionné."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:132
msgid "API Key updated successfully."
msgstr "Clé API mise à jour avec succès."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:164
msgid "API Key generated successfully. Make sure to copy your new keys now as the secret key will be hidden once you leave this page."
msgstr "Clé API générée avec succès. N'oubliez pas de copier vos nouvelles clés immédiatement, car la clé secrète sera masquée lorsque vous quitterez cette page."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:176
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:116
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:119
msgid "Revoke key"
msgstr "Révoquer la clé"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:46
msgid "No keys found."
msgstr "Aucune clé trouvée."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:57
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:22
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:95
#: includes/payments/gateways/gateway.php:504
#: includes/post-types/coupon-cpt.php:37
#: includes/post-types/rate-cpt.php:81
msgid "Description"
msgstr "Description"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:58
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:81
msgid "Consumer key ending in"
msgstr "Clé d'utilisateur se terminant par"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:59
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:36
msgid "User"
msgstr "Utilisateur"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:60
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:55
msgid "Permissions"
msgstr "Autorisation"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:61
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:89
msgid "Last access"
msgstr "Dernier accès"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:99
msgid "API key"
msgstr "Clé API"

#. translators: %d: API key ID.
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:111
msgid "ID: %d"
msgstr "ID: %d"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:126
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:227
msgid "Revoke"
msgstr "Révoquer"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:182
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:65
msgid "Read"
msgstr "Lu"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:183
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:66
msgid "Write"
msgstr "Écrire"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:184
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:67
msgid "Read/Write"
msgstr "Lire/Écrire"

#. translators: 1: last access date 2: last access time
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:205
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:96
msgid "%1$s at %2$s"
msgstr "%1$s à %2$s"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:213
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:100
msgid "Unknown"
msgstr "Inconnu"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:68
msgid "You do not have permission to edit this API Key"
msgstr "Vous n'avez pas le droit d'éditer cet utilisateur"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:89
msgid "REST API"
msgstr "API REST"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:91
msgid "Add key"
msgstr "Ajouter une clé"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:176
msgid "You do not have permission to revoke this API Key"
msgstr "Vous n'avez pas les autorisations nécessaires pour révoquer cette clé API"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:191
msgid "You do not have permission to edit API Keys"
msgstr "Vous n'avez pas le droit de modifier les clés d'API"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:211
msgid "You do not have permission to revoke API Keys"
msgstr "Vous n'êtes pas autorisé à révoquer les clés d'API"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:13
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:114
msgid "Generate API key"
msgstr "Générer une clé API"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:25
msgid "Friendly name for identifying this key."
msgstr "Nom convivial pour identifier cette clé."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:39
msgid "Owner of these keys."
msgstr "Propriétaire des clés."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:58
msgid "Access type of these keys."
msgstr "Type d'accès aux clés."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:148
msgid "Consumer key"
msgstr "Clef d'utilisateur"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copied!"
msgstr "Copié!"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copy"
msgstr "Copier"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:156
msgid "Consumer secret"
msgstr "Secret de l'utilisateur"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:164
msgid "QR Code"
msgstr "Code QR"

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:33
#: includes/ajax-api/ajax-actions/update-booking-notes.php:52
#: includes/ajax.php:344
#: includes/ajax.php:388
#: includes/csv/bookings/bookings-query.php:85
msgid "Please complete all required fields and try again."
msgstr "Veuillez remplir tous les champs obligatoires et réessayer."

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:55
msgid "Sorry, the minimum allowed payment amount is %s to use this payment method."
msgstr "Désolé, le montant de paiement minimal autorisé est %s pour utiliser ce mode de paiement."

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:60
#: includes/post-types/booking-cpt.php:35
msgid "Booking Information"
msgstr "Informations sur la réservation"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:68
#: includes/emails/templaters/email-templater.php:136
#: includes/post-types/booking-cpt.php:51
#: template-functions.php:706
#: template-functions.php:710
#: templates/create-booking/search/search-form.php:53
#: templates/edit-booking/edit-dates.php:33
#: templates/shortcodes/search/search-form.php:43
#: templates/widgets/search-availability/search-form.php:43
#: assets/blocks/blocks.js:177
msgid "Check-in Date"
msgstr "Date d'arrivée"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:72
#: includes/emails/templaters/email-templater.php:140
#: includes/post-types/booking-cpt.php:60
#: template-functions.php:715
#: template-functions.php:719
#: templates/create-booking/search/search-form.php:73
#: templates/edit-booking/edit-dates.php:42
#: templates/shortcodes/search/search-form.php:63
#: templates/widgets/search-availability/search-form.php:62
#: assets/blocks/blocks.js:189
msgid "Check-out Date"
msgstr "Date de départ"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:91
msgid "Summary"
msgstr "Sommaire"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:99
msgid "Source"
msgstr "Source"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:110
#: includes/post-types/booking-cpt.php:83
#: templates/emails/customer-approved-booking.php:31
#: templates/emails/customer-cancelled-booking.php:29
#: templates/emails/customer-confirmation-booking.php:35
#: templates/emails/customer-pending-booking.php:32
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:30
msgid "Customer Information"
msgstr "Informations client"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:150
#: includes/csv/bookings/bookings-exporter-helper.php:90
#: includes/emails/templaters/email-templater.php:189
#: includes/post-types/booking-cpt.php:164
msgid "Customer Note"
msgstr "Note du client"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:156
#: includes/post-types/booking-cpt.php:171
msgid "Additional Information"
msgstr "Informations supplémentaires"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:160
#: includes/csv/bookings/bookings-exporter-helper.php:107
#: includes/post-types/booking-cpt.php:178
#: includes/post-types/coupon-cpt.php:289
msgid "Coupon"
msgstr "Coupon"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:164
#: includes/post-types/booking-cpt.php:187
msgid "Total Booking Price"
msgstr "Prix de réservation total"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:173
#: includes/bundles/customer-bundle.php:173
#: includes/post-types/booking-cpt.php:201
#: includes/views/shortcodes/checkout-view.php:735
msgid "Notes"
msgstr "Remarques"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:192
msgid "%1$s on %2$s"
msgstr "%1$s sur %2$s"

#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:185
#: template-functions.php:88
msgid "Based on your search parameters"
msgstr "Selon vos paramètres de recherche"

#: includes/ajax.php:245
msgid "No bookings found for your request."
msgstr "Aucune réservation trouvée pour votre demande."

#: includes/ajax.php:252
msgid "Uploads directory is not writable."
msgstr "Le répertoire des téléchargements n'est pas accessible en écriture."

#: includes/ajax.php:314
msgid "No enough data"
msgstr "Pas assez de données"

#: includes/ajax.php:332
#: includes/script-managers/admin-script-manager.php:94
msgid "An error has occurred"
msgstr "Une erreur est survenue"

#: includes/ajax.php:372
#: includes/script-managers/public-script-manager.php:199
msgid "An error has occurred, please try again later."
msgstr "Une erreur est survenue. Veuillez réessayer plus tard."

#: includes/ajax.php:473
msgid "The number of adults is not valid."
msgstr "Le nombre d’adultes n’est pas valide."

#: includes/ajax.php:477
msgid "The number of guests is not valid."
msgstr "Le nombre d’invités n’est pas valide."

#: includes/ajax.php:519
#: includes/ajax.php:593
msgid "An error has occurred. Please try again later."
msgstr "Une erreur est survenue. Veuillez réessayer plus tard."

#: includes/ajax.php:750
msgid "Chosen payment method is not available. Please refresh the page and try one more time."
msgstr "Le mode de paiement choisi n'est pas disponible. Veuillez actualiser la page et essayez une fois de plus."

#: includes/ajax.php:832
msgid "Coupon applied successfully."
msgstr "Coupon appliqué avec succès."

#: includes/ajax.php:838
#: includes/entities/coupon.php:366
msgid "Coupon is not valid."
msgstr "Le coupon n'est pas valide."

#: includes/ajax.php:1046
msgid "You do not have permission to do this action."
msgstr "Vous n’avez pas les autorisations nécessaires pour effectuer cette action."

#: includes/attribute-functions.php:164
#: includes/post-types/attributes-cpt.php:137
#: includes/post-types/attributes-cpt.php:149
#: includes/post-types/attributes-cpt.php:345
msgctxt "Not selected value in the search form."
msgid "&mdash;"
msgstr "&mdash;"

#: includes/bookings-calendar.php:577
msgid "Year"
msgstr "Année"

#: includes/bookings-calendar.php:578
#: includes/post-types/attributes-cpt.php:298
#: includes/reports/report-filters.php:94
msgid "Custom"
msgstr "Personnalisé"

#: includes/bookings-calendar.php:608
#: includes/post-types/booking-cpt/statuses.php:102
#: includes/reports/data/report-earnings-by-dates-data.php:28
msgctxt "Booking status"
msgid "Confirmed"
msgstr "Confirmée"

#: includes/bookings-calendar.php:645
#: includes/reports/abstract-report.php:46
#: includes/reports/earnings-report.php:361
msgid "Show"
msgstr "Montrer"

#: includes/bookings-calendar.php:649
#: templates/create-booking/search/search-form.php:131
#: templates/shortcodes/search/search-form.php:138
#: templates/widgets/search-availability/search-form.php:142
msgid "Search"
msgstr "Chercher"

#: includes/bookings-calendar.php:652
#: includes/bookings-calendar.php:654
#: includes/bookings-calendar.php:697
#: includes/script-managers/public-script-manager.php:200
msgid "Booked"
msgstr "Réservé"

#: includes/bookings-calendar.php:657
#: includes/bookings-calendar.php:659
#: includes/bookings-calendar.php:698
#: includes/script-managers/public-script-manager.php:202
msgid "Pending"
msgstr "En attente"

#: includes/bookings-calendar.php:662
#: includes/bookings-calendar.php:664
msgid "External"
msgstr "Externe"

#: includes/bookings-calendar.php:667
#: includes/bookings-calendar.php:669
#: includes/bookings-calendar.php:1133
msgid "Blocked"
msgstr "Bloqué"

#: includes/bookings-calendar.php:687
msgid "Search results for accommodations that have bookings with status \"%s\" from %s until %s"
msgstr "Résultats de la recherche pour les logements qui ont des réservations avec l'état \"%s\" de %s jusqu'à %s"

#: includes/bookings-calendar.php:696
msgid "Free"
msgstr "Gratuit"

#: includes/bookings-calendar.php:699
msgid "Locked (Booked or Pending)"
msgstr "Bloqué (réservé ou en attente)"

#: includes/bookings-calendar.php:729
#: includes/bookings-calendar.php:819
msgid "Until"
msgstr "Jusqu'à"

#: includes/bookings-calendar.php:775
msgid "Period:"
msgstr "Période :"

#: includes/bookings-calendar.php:782
msgid "&lt; Prev"
msgstr "&lt; Précédent"

#: includes/bookings-calendar.php:799
msgid "Next &gt;"
msgstr "Suivant &gt;"

#: includes/bookings-calendar.php:874
msgid "No accommodations found."
msgstr "Aucun logement trouvé."

#: includes/bookings-calendar.php:1123
msgid "Check-out #%d"
msgstr "Départ #%d"

#: includes/bookings-calendar.php:1127
msgid "Check-in #%d"
msgstr "Enregistrement #%d"

#: includes/bookings-calendar.php:1131
msgid "Booking #%d"
msgstr "Réservation #%d"

#: includes/bookings-calendar.php:1136
#: includes/bookings-calendar.php:1140
#: includes/script-managers/public-script-manager.php:201
msgid "Buffer time."
msgstr "Temps tampon."

#: includes/bookings-calendar.php:1143
msgctxt "Availability"
msgid "Free"
msgstr "Disponible"

#: includes/bookings-calendar.php:1172
#: templates/emails/reserved-room-details.php:15
msgid "Adults: %s"
msgstr "Adultes : %s"

#: includes/bookings-calendar.php:1176
#: templates/emails/reserved-room-details.php:17
msgid "Children: %s"
msgstr "Enfants : %s"

#: includes/bookings-calendar.php:1183
msgid "Booking imported with UID %s."
msgstr "Réservation importée avec UID %s."

#: includes/bookings-calendar.php:1185
msgid "Imported booking."
msgstr "Réservation importée."

#: includes/bookings-calendar.php:1193
msgid "Description: %s."
msgstr "Description : %s."

#: includes/bookings-calendar.php:1197
msgid "Source: %s."
msgstr "Source : %s."

#: includes/bundles/countries-bundle.php:16
msgid "Afghanistan"
msgstr "Afghanistan"

#: includes/bundles/countries-bundle.php:17
msgid "&#197;land Islands"
msgstr "Åland"

#: includes/bundles/countries-bundle.php:18
msgid "Albania"
msgstr "Albanie"

#: includes/bundles/countries-bundle.php:19
msgid "Algeria"
msgstr "Algérie"

#: includes/bundles/countries-bundle.php:20
msgid "American Samoa"
msgstr "Samoa américaines"

#: includes/bundles/countries-bundle.php:21
msgid "Andorra"
msgstr "Andorre"

#: includes/bundles/countries-bundle.php:22
msgid "Angola"
msgstr "Angola"

#: includes/bundles/countries-bundle.php:23
msgid "Anguilla"
msgstr "Anguilla"

#: includes/bundles/countries-bundle.php:24
msgid "Antarctica"
msgstr "Antarctique"

#: includes/bundles/countries-bundle.php:25
msgid "Antigua and Barbuda"
msgstr "Antigua-et-Barbuda"

#: includes/bundles/countries-bundle.php:26
msgid "Argentina"
msgstr "Argentine"

#: includes/bundles/countries-bundle.php:27
msgid "Armenia"
msgstr "Arménie"

#: includes/bundles/countries-bundle.php:28
msgid "Aruba"
msgstr "Aruba"

#: includes/bundles/countries-bundle.php:29
msgid "Australia"
msgstr "Australie"

#: includes/bundles/countries-bundle.php:30
msgid "Austria"
msgstr "Autriche"

#: includes/bundles/countries-bundle.php:31
msgid "Azerbaijan"
msgstr "Azerbaïdjan"

#: includes/bundles/countries-bundle.php:32
msgid "Bahamas"
msgstr "Bahamas"

#: includes/bundles/countries-bundle.php:33
msgid "Bahrain"
msgstr "Bahreïn"

#: includes/bundles/countries-bundle.php:34
msgid "Bangladesh"
msgstr "Bangladesh"

#: includes/bundles/countries-bundle.php:35
msgid "Barbados"
msgstr "Barbade"

#: includes/bundles/countries-bundle.php:36
msgid "Belarus"
msgstr "Biélorussie"

#: includes/bundles/countries-bundle.php:37
msgid "Belgium"
msgstr "Belgique"

#: includes/bundles/countries-bundle.php:38
msgid "Belau"
msgstr "Belau"

#: includes/bundles/countries-bundle.php:39
msgid "Belize"
msgstr "Bélize"

#: includes/bundles/countries-bundle.php:40
msgid "Benin"
msgstr "Bénin"

#: includes/bundles/countries-bundle.php:41
msgid "Bermuda"
msgstr "Bermudes"

#: includes/bundles/countries-bundle.php:42
msgid "Bhutan"
msgstr "Bhoutan"

#: includes/bundles/countries-bundle.php:43
msgid "Bolivia"
msgstr "Bolivie"

#: includes/bundles/countries-bundle.php:44
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Bonaire, Saint-Eustache et Saba"

#: includes/bundles/countries-bundle.php:45
msgid "Bosnia and Herzegovina"
msgstr "Bosnie-Herzégovine"

#: includes/bundles/countries-bundle.php:46
msgid "Botswana"
msgstr "Bostwana"

#: includes/bundles/countries-bundle.php:47
msgid "Bouvet Island"
msgstr "Île Bouvet"

#: includes/bundles/countries-bundle.php:48
msgid "Brazil"
msgstr "Brésil"

#: includes/bundles/countries-bundle.php:49
msgid "British Indian Ocean Territory"
msgstr "Territoire britannique de l'océan Indien"

#: includes/bundles/countries-bundle.php:50
msgid "British Virgin Islands"
msgstr "Îles Vierges britanniques"

#: includes/bundles/countries-bundle.php:51
msgid "Brunei"
msgstr "Brunéi"

#: includes/bundles/countries-bundle.php:52
msgid "Bulgaria"
msgstr "Bulgarie"

#: includes/bundles/countries-bundle.php:53
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: includes/bundles/countries-bundle.php:54
msgid "Burundi"
msgstr "Burundi"

#: includes/bundles/countries-bundle.php:55
msgid "Cambodia"
msgstr "Cambodge"

#: includes/bundles/countries-bundle.php:56
msgid "Cameroon"
msgstr "Cameroun"

#: includes/bundles/countries-bundle.php:57
msgid "Canada"
msgstr "Canada"

#: includes/bundles/countries-bundle.php:58
msgid "Cape Verde"
msgstr "Cap-Vert"

#: includes/bundles/countries-bundle.php:59
msgid "Cayman Islands"
msgstr "Îles Caïmans"

#: includes/bundles/countries-bundle.php:60
msgid "Central African Republic"
msgstr "République centrafricaine"

#: includes/bundles/countries-bundle.php:61
msgid "Chad"
msgstr "Tchad"

#: includes/bundles/countries-bundle.php:62
msgid "Chile"
msgstr "Chili"

#: includes/bundles/countries-bundle.php:63
msgid "China"
msgstr "Chine"

#: includes/bundles/countries-bundle.php:64
msgid "Christmas Island"
msgstr "L'île de noël"

#: includes/bundles/countries-bundle.php:65
msgid "Cocos (Keeling) Islands"
msgstr "Îles Cocos (Keeling)"

#: includes/bundles/countries-bundle.php:66
msgid "Colombia"
msgstr "Colombie"

#: includes/bundles/countries-bundle.php:67
msgid "Comoros"
msgstr "Comores"

#: includes/bundles/countries-bundle.php:68
msgid "Congo (Brazzaville)"
msgstr "Congo - Brazzaville"

#: includes/bundles/countries-bundle.php:69
msgid "Congo (Kinshasa)"
msgstr "Congo - Kinshasa"

#: includes/bundles/countries-bundle.php:70
msgid "Cook Islands"
msgstr "Îles Cook"

#: includes/bundles/countries-bundle.php:71
msgid "Costa Rica"
msgstr "Costa Rica"

#: includes/bundles/countries-bundle.php:72
msgid "Croatia"
msgstr "Croatie"

#: includes/bundles/countries-bundle.php:73
msgid "Cuba"
msgstr "Cuba"

#: includes/bundles/countries-bundle.php:74
msgid "Cura&ccedil;ao"
msgstr "Curaçao"

#: includes/bundles/countries-bundle.php:75
msgid "Cyprus"
msgstr "Chypre"

#: includes/bundles/countries-bundle.php:76
msgid "Czech Republic"
msgstr "République Tchèque"

#: includes/bundles/countries-bundle.php:77
msgid "Denmark"
msgstr "Danemark"

#: includes/bundles/countries-bundle.php:78
msgid "Djibouti"
msgstr "Djibouti"

#: includes/bundles/countries-bundle.php:79
msgid "Dominica"
msgstr "Dominique"

#: includes/bundles/countries-bundle.php:80
msgid "Dominican Republic"
msgstr "République Dominicaine"

#: includes/bundles/countries-bundle.php:81
msgid "Ecuador"
msgstr "Équateur"

#: includes/bundles/countries-bundle.php:82
msgid "Egypt"
msgstr "Egypte"

#: includes/bundles/countries-bundle.php:83
msgid "El Salvador"
msgstr "El Salvador"

#: includes/bundles/countries-bundle.php:84
msgid "Equatorial Guinea"
msgstr "Guinée Équatoriale"

#: includes/bundles/countries-bundle.php:85
msgid "Eritrea"
msgstr "Érythrée"

#: includes/bundles/countries-bundle.php:86
msgid "Estonia"
msgstr "Estonie"

#: includes/bundles/countries-bundle.php:87
msgid "Ethiopia"
msgstr "Ethiopie"

#: includes/bundles/countries-bundle.php:88
msgid "Falkland Islands"
msgstr "Îles Falkland"

#: includes/bundles/countries-bundle.php:89
msgid "Faroe Islands"
msgstr "Îles Féroé"

#: includes/bundles/countries-bundle.php:90
msgid "Fiji"
msgstr "Fidji"

#: includes/bundles/countries-bundle.php:91
msgid "Finland"
msgstr "Finlande"

#: includes/bundles/countries-bundle.php:92
msgid "France"
msgstr "France"

#: includes/bundles/countries-bundle.php:93
msgid "French Guiana"
msgstr "Guinée Française"

#: includes/bundles/countries-bundle.php:94
msgid "French Polynesia"
msgstr "Polynésie française"

#: includes/bundles/countries-bundle.php:95
msgid "French Southern Territories"
msgstr "Territoires du Sud français"

#: includes/bundles/countries-bundle.php:96
msgid "Gabon"
msgstr "Gabon"

#: includes/bundles/countries-bundle.php:97
msgid "Gambia"
msgstr "Gambie"

#: includes/bundles/countries-bundle.php:98
msgid "Georgia"
msgstr "Géorgie"

#: includes/bundles/countries-bundle.php:99
msgid "Germany"
msgstr "Allemagne"

#: includes/bundles/countries-bundle.php:100
msgid "Ghana"
msgstr "Ghana"

#: includes/bundles/countries-bundle.php:101
msgid "Gibraltar"
msgstr "Gibraltar"

#: includes/bundles/countries-bundle.php:102
msgid "Greece"
msgstr "Grèce"

#: includes/bundles/countries-bundle.php:103
msgid "Greenland"
msgstr "Groenland"

#: includes/bundles/countries-bundle.php:104
msgid "Grenada"
msgstr "Grenade"

#: includes/bundles/countries-bundle.php:105
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: includes/bundles/countries-bundle.php:106
msgid "Guam"
msgstr "Guam"

#: includes/bundles/countries-bundle.php:107
msgid "Guatemala"
msgstr "Guatémala"

#: includes/bundles/countries-bundle.php:108
msgid "Guernsey"
msgstr "Guernesey"

#: includes/bundles/countries-bundle.php:109
msgid "Guinea"
msgstr "Guinée"

#: includes/bundles/countries-bundle.php:110
msgid "Guinea-Bissau"
msgstr "Guinée-Bissau"

#: includes/bundles/countries-bundle.php:111
msgid "Guyana"
msgstr "Guyane"

#: includes/bundles/countries-bundle.php:112
msgid "Haiti"
msgstr "Haïti"

#: includes/bundles/countries-bundle.php:113
msgid "Heard Island and McDonald Islands"
msgstr "Îles Heard et McDonald"

#: includes/bundles/countries-bundle.php:114
msgid "Honduras"
msgstr "Honduras"

#: includes/bundles/countries-bundle.php:115
msgid "Hong Kong"
msgstr "Hong Kong"

#: includes/bundles/countries-bundle.php:116
msgid "Hungary"
msgstr "Hongrie"

#: includes/bundles/countries-bundle.php:117
msgid "Iceland"
msgstr "Islande"

#: includes/bundles/countries-bundle.php:118
msgid "India"
msgstr "Inde"

#: includes/bundles/countries-bundle.php:119
msgid "Indonesia"
msgstr "Indonésie"

#: includes/bundles/countries-bundle.php:120
msgid "Iran"
msgstr "Iran"

#: includes/bundles/countries-bundle.php:121
msgid "Iraq"
msgstr "Irak"

#: includes/bundles/countries-bundle.php:122
msgid "Ireland"
msgstr "Irlande"

#: includes/bundles/countries-bundle.php:123
msgid "Isle of Man"
msgstr "Île de Man"

#: includes/bundles/countries-bundle.php:124
msgid "Israel"
msgstr "Israël"

#: includes/bundles/countries-bundle.php:125
msgid "Italy"
msgstr "Italie"

#: includes/bundles/countries-bundle.php:126
msgid "Ivory Coast"
msgstr "Côte d'Ivoire"

#: includes/bundles/countries-bundle.php:127
msgid "Jamaica"
msgstr "Jamaïque"

#: includes/bundles/countries-bundle.php:128
msgid "Japan"
msgstr "Japon"

#: includes/bundles/countries-bundle.php:129
msgid "Jersey"
msgstr "Jersey"

#: includes/bundles/countries-bundle.php:130
msgid "Jordan"
msgstr "Jordanie"

#: includes/bundles/countries-bundle.php:131
msgid "Kazakhstan"
msgstr "Kazakhstan"

#: includes/bundles/countries-bundle.php:132
msgid "Kenya"
msgstr "Kenya"

#: includes/bundles/countries-bundle.php:133
msgid "Kiribati"
msgstr "Kiribati"

#: includes/bundles/countries-bundle.php:134
msgid "Kuwait"
msgstr "Koweit"

#: includes/bundles/countries-bundle.php:135
msgid "Kyrgyzstan"
msgstr "Kirghizistan"

#: includes/bundles/countries-bundle.php:136
msgid "Laos"
msgstr "Laos"

#: includes/bundles/countries-bundle.php:137
msgid "Latvia"
msgstr "Lettonie"

#: includes/bundles/countries-bundle.php:138
msgid "Lebanon"
msgstr "Liban"

#: includes/bundles/countries-bundle.php:139
msgid "Lesotho"
msgstr "Lesotho"

#: includes/bundles/countries-bundle.php:140
msgid "Liberia"
msgstr "Libéria"

#: includes/bundles/countries-bundle.php:141
msgid "Libya"
msgstr "Libye"

#: includes/bundles/countries-bundle.php:142
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: includes/bundles/countries-bundle.php:143
msgid "Lithuania"
msgstr "Lituanie"

#: includes/bundles/countries-bundle.php:144
msgid "Luxembourg"
msgstr "Luxembourg"

#: includes/bundles/countries-bundle.php:145
msgid "Macao S.A.R., China"
msgstr "RAS de Macao de la République populaire de Chine"

#: includes/bundles/countries-bundle.php:146
msgid "Macedonia"
msgstr "Macédoine"

#: includes/bundles/countries-bundle.php:147
msgid "Madagascar"
msgstr "Madagascar"

#: includes/bundles/countries-bundle.php:148
msgid "Malawi"
msgstr "Malawi"

#: includes/bundles/countries-bundle.php:149
msgid "Malaysia"
msgstr "Malaisie"

#: includes/bundles/countries-bundle.php:150
msgid "Maldives"
msgstr "Maldives"

#: includes/bundles/countries-bundle.php:151
msgid "Mali"
msgstr "Mali"

#: includes/bundles/countries-bundle.php:152
msgid "Malta"
msgstr "Malte"

#: includes/bundles/countries-bundle.php:153
msgid "Marshall Islands"
msgstr "Iles Marshall"

#: includes/bundles/countries-bundle.php:154
msgid "Martinique"
msgstr "Martinique"

#: includes/bundles/countries-bundle.php:155
msgid "Mauritania"
msgstr "Mauritanie"

#: includes/bundles/countries-bundle.php:156
msgid "Mauritius"
msgstr "Ile Maurice"

#: includes/bundles/countries-bundle.php:157
msgid "Mayotte"
msgstr "Mayotte"

#: includes/bundles/countries-bundle.php:158
msgid "Mexico"
msgstr "Mexique"

#: includes/bundles/countries-bundle.php:159
msgid "Micronesia"
msgstr "Micronésie"

#: includes/bundles/countries-bundle.php:160
msgid "Moldova"
msgstr "Moldavie"

#: includes/bundles/countries-bundle.php:161
msgid "Monaco"
msgstr "Monaco"

#: includes/bundles/countries-bundle.php:162
msgid "Mongolia"
msgstr "Mongolie"

#: includes/bundles/countries-bundle.php:163
msgid "Montenegro"
msgstr "Monténégro"

#: includes/bundles/countries-bundle.php:164
msgid "Montserrat"
msgstr "Montserrat"

#: includes/bundles/countries-bundle.php:165
msgid "Morocco"
msgstr "Maroc"

#: includes/bundles/countries-bundle.php:166
msgid "Mozambique"
msgstr "Mozambique"

#: includes/bundles/countries-bundle.php:167
msgid "Myanmar"
msgstr "Myanmar"

#: includes/bundles/countries-bundle.php:168
msgid "Namibia"
msgstr "Namibie"

#: includes/bundles/countries-bundle.php:169
msgid "Nauru"
msgstr "Nauru"

#: includes/bundles/countries-bundle.php:170
msgid "Nepal"
msgstr "Népal"

#: includes/bundles/countries-bundle.php:171
msgid "Netherlands"
msgstr "Pays-Bas"

#: includes/bundles/countries-bundle.php:172
msgid "New Caledonia"
msgstr "Nouvelle Calédonie"

#: includes/bundles/countries-bundle.php:173
msgid "New Zealand"
msgstr "Nouvelle-Zélande"

#: includes/bundles/countries-bundle.php:174
msgid "Nicaragua"
msgstr "Nicaragua"

#: includes/bundles/countries-bundle.php:175
msgid "Niger"
msgstr "Niger"

#: includes/bundles/countries-bundle.php:176
msgid "Nigeria"
msgstr "Nigéria"

#: includes/bundles/countries-bundle.php:177
msgid "Niue"
msgstr "Niue"

#: includes/bundles/countries-bundle.php:178
msgid "Norfolk Island"
msgstr "Île Norfolk"

#: includes/bundles/countries-bundle.php:179
msgid "Northern Mariana Islands"
msgstr "Îles Mariannes du Nord"

#: includes/bundles/countries-bundle.php:180
msgid "North Korea"
msgstr "Corée du Nord"

#: includes/bundles/countries-bundle.php:181
msgid "Norway"
msgstr "Norvège"

#: includes/bundles/countries-bundle.php:182
msgid "Oman"
msgstr "Oman"

#: includes/bundles/countries-bundle.php:183
msgid "Pakistan"
msgstr "Pakistan"

#: includes/bundles/countries-bundle.php:184
msgid "Palestinian Territory"
msgstr "Territoire Palestinien"

#: includes/bundles/countries-bundle.php:185
msgid "Panama"
msgstr "Panama"

#: includes/bundles/countries-bundle.php:186
msgid "Papua New Guinea"
msgstr "Papouasie Nouvelle Guinée"

#: includes/bundles/countries-bundle.php:187
msgid "Paraguay"
msgstr "Paraguay"

#: includes/bundles/countries-bundle.php:188
#: includes/settings/main-settings.php:37
msgid "Peru"
msgstr "Pérou"

#: includes/bundles/countries-bundle.php:189
msgid "Philippines"
msgstr "Philippines"

#: includes/bundles/countries-bundle.php:190
msgid "Pitcairn"
msgstr "Pitcairn"

#: includes/bundles/countries-bundle.php:191
msgid "Poland"
msgstr "Pologne"

#: includes/bundles/countries-bundle.php:192
msgid "Portugal"
msgstr "Portugal"

#: includes/bundles/countries-bundle.php:193
msgid "Puerto Rico"
msgstr "Porto Rico"

#: includes/bundles/countries-bundle.php:194
msgid "Qatar"
msgstr "Qatar"

#: includes/bundles/countries-bundle.php:195
msgid "Reunion"
msgstr "Réunion"

#: includes/bundles/countries-bundle.php:196
msgid "Romania"
msgstr "Roumanie"

#: includes/bundles/countries-bundle.php:197
msgid "Russia"
msgstr "Russie"

#: includes/bundles/countries-bundle.php:198
msgid "Rwanda"
msgstr "Rwanda"

#: includes/bundles/countries-bundle.php:199
msgid "Saint Barth&eacute;lemy"
msgstr "Saint-Barthélemy"

#: includes/bundles/countries-bundle.php:200
msgid "Saint Helena"
msgstr "Sainte-Hélène"

#: includes/bundles/countries-bundle.php:201
msgid "Saint Kitts and Nevis"
msgstr "Saint-Christophe-et-Niévès"

#: includes/bundles/countries-bundle.php:202
msgid "Saint Lucia"
msgstr "Sainte-Lucie"

#: includes/bundles/countries-bundle.php:203
msgid "Saint Martin (French part)"
msgstr "Saint Martin (partie française)"

#: includes/bundles/countries-bundle.php:204
msgid "Saint Martin (Dutch part)"
msgstr "Saint Martin (partie néerlandaise)"

#: includes/bundles/countries-bundle.php:205
msgid "Saint Pierre and Miquelon"
msgstr "Saint Pierre et Miquelon"

#: includes/bundles/countries-bundle.php:206
msgid "Saint Vincent and the Grenadines"
msgstr "Saint-Vincent-et-les-Grenadines"

#: includes/bundles/countries-bundle.php:207
msgid "San Marino"
msgstr "Saint-Marin"

#: includes/bundles/countries-bundle.php:208
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "São Tomé-et-Príncipe"

#: includes/bundles/countries-bundle.php:209
msgid "Saudi Arabia"
msgstr "Arabie Saoudite"

#: includes/bundles/countries-bundle.php:210
msgid "Senegal"
msgstr "Sénégal"

#: includes/bundles/countries-bundle.php:211
msgid "Serbia"
msgstr "Serbie"

#: includes/bundles/countries-bundle.php:212
msgid "Seychelles"
msgstr "Seychelles"

#: includes/bundles/countries-bundle.php:213
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: includes/bundles/countries-bundle.php:214
msgid "Singapore"
msgstr "Singapour"

#: includes/bundles/countries-bundle.php:215
msgid "Slovakia"
msgstr "Slovaquie"

#: includes/bundles/countries-bundle.php:216
msgid "Slovenia"
msgstr "Slovénie"

#: includes/bundles/countries-bundle.php:217
msgid "Solomon Islands"
msgstr "Les îles Salomon"

#: includes/bundles/countries-bundle.php:218
msgid "Somalia"
msgstr "Somalie"

#: includes/bundles/countries-bundle.php:219
msgid "South Africa"
msgstr "Afrique du Sud"

#: includes/bundles/countries-bundle.php:220
msgid "South Georgia/Sandwich Islands"
msgstr "Géorgie du Sud / Îles Sandwich"

#: includes/bundles/countries-bundle.php:221
msgid "South Korea"
msgstr "Corée du Sud"

#: includes/bundles/countries-bundle.php:222
msgid "South Sudan"
msgstr "Soudan du sud"

#: includes/bundles/countries-bundle.php:223
msgid "Spain"
msgstr "Espagne"

#: includes/bundles/countries-bundle.php:224
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: includes/bundles/countries-bundle.php:225
msgid "Sudan"
msgstr "Soudan"

#: includes/bundles/countries-bundle.php:226
msgid "Suriname"
msgstr "Suriname"

#: includes/bundles/countries-bundle.php:227
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard et Jan Mayen"

#: includes/bundles/countries-bundle.php:228
msgid "Swaziland"
msgstr "Swaziland"

#: includes/bundles/countries-bundle.php:229
msgid "Sweden"
msgstr "Suède"

#: includes/bundles/countries-bundle.php:230
msgid "Switzerland"
msgstr "Suisse"

#: includes/bundles/countries-bundle.php:231
msgid "Syria"
msgstr "Syrie"

#: includes/bundles/countries-bundle.php:232
msgid "Taiwan"
msgstr "Taïwan"

#: includes/bundles/countries-bundle.php:233
msgid "Tajikistan"
msgstr "Tadjikistan"

#: includes/bundles/countries-bundle.php:234
msgid "Tanzania"
msgstr "Tanzanie"

#: includes/bundles/countries-bundle.php:235
msgid "Thailand"
msgstr "Thaïlande"

#: includes/bundles/countries-bundle.php:236
msgid "Timor-Leste"
msgstr "Timor-Oriental"

#: includes/bundles/countries-bundle.php:237
msgid "Togo"
msgstr "Togo"

#: includes/bundles/countries-bundle.php:238
msgid "Tokelau"
msgstr "Tokélaou"

#: includes/bundles/countries-bundle.php:239
msgid "Tonga"
msgstr "Tonga"

#: includes/bundles/countries-bundle.php:240
msgid "Trinidad and Tobago"
msgstr "Trinité et Tobago"

#: includes/bundles/countries-bundle.php:241
msgid "Tunisia"
msgstr "Tunisie"

#: includes/bundles/countries-bundle.php:242
msgid "Turkey"
msgstr "Turquie"

#: includes/bundles/countries-bundle.php:243
msgid "Turkmenistan"
msgstr "Turkménistan"

#: includes/bundles/countries-bundle.php:244
msgid "Turks and Caicos Islands"
msgstr "Îles Turques et Caïques"

#: includes/bundles/countries-bundle.php:245
msgid "Tuvalu"
msgstr "Tuvalu"

#: includes/bundles/countries-bundle.php:246
msgid "Uganda"
msgstr "Ouganda"

#: includes/bundles/countries-bundle.php:247
msgid "Ukraine"
msgstr "Ukraine"

#: includes/bundles/countries-bundle.php:248
msgid "United Arab Emirates"
msgstr "Emirats Arabes Unis"

#: includes/bundles/countries-bundle.php:249
msgid "United Kingdom (UK)"
msgstr "Royaume-Uni"

#: includes/bundles/countries-bundle.php:250
msgid "United States (US)"
msgstr "États-Unis"

#: includes/bundles/countries-bundle.php:251
msgid "United States (US) Minor Outlying Islands"
msgstr "Îles mineures éloignées des États-Unis"

#: includes/bundles/countries-bundle.php:252
msgid "United States (US) Virgin Islands"
msgstr "États-Unis Îles Vierges"

#: includes/bundles/countries-bundle.php:253
msgid "Uruguay"
msgstr "Uruguay"

#: includes/bundles/countries-bundle.php:254
msgid "Uzbekistan"
msgstr "Ouzbékistan"

#: includes/bundles/countries-bundle.php:255
msgid "Vanuatu"
msgstr "Vanuatu"

#: includes/bundles/countries-bundle.php:256
msgid "Vatican"
msgstr "Vatican"

#: includes/bundles/countries-bundle.php:257
msgid "Venezuela"
msgstr "Venezuela"

#: includes/bundles/countries-bundle.php:258
msgid "Vietnam"
msgstr "Vietnam"

#: includes/bundles/countries-bundle.php:259
msgid "Wallis and Futuna"
msgstr "Wallis et Futuna"

#: includes/bundles/countries-bundle.php:260
msgid "Western Sahara"
msgstr "Sahara occidental"

#: includes/bundles/countries-bundle.php:261
msgid "Samoa"
msgstr "Samoa"

#: includes/bundles/countries-bundle.php:262
msgid "Yemen"
msgstr "Yémen"

#: includes/bundles/countries-bundle.php:263
msgid "Zambia"
msgstr "Zambie"

#: includes/bundles/countries-bundle.php:264
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: includes/bundles/currency-bundle.php:17
msgid "Euro"
msgstr "Euro"

#: includes/bundles/currency-bundle.php:18
msgid "United States (US) dollar"
msgstr "Dollar des États-Unis (US)"

#: includes/bundles/currency-bundle.php:19
msgid "Pound sterling"
msgstr "Livres sterling"

#: includes/bundles/currency-bundle.php:20
msgid "United Arab Emirates dirham"
msgstr "Dirham des Émirats arabes unis"

#: includes/bundles/currency-bundle.php:21
msgid "Afghan afghani"
msgstr "Afghani afghan"

#: includes/bundles/currency-bundle.php:22
msgid "Albanian lek"
msgstr "Lek albanais"

#: includes/bundles/currency-bundle.php:23
msgid "Armenian dram"
msgstr "Dram"

#: includes/bundles/currency-bundle.php:24
msgid "Netherlands Antillean guilder"
msgstr "Florin des Antilles néerlandaises"

#: includes/bundles/currency-bundle.php:25
msgid "Angolan kwanza"
msgstr "Kwanza"

#: includes/bundles/currency-bundle.php:26
msgid "Argentine peso"
msgstr "Peso argentin"

#: includes/bundles/currency-bundle.php:27
msgid "Australian dollar"
msgstr "Dollars australiens"

#: includes/bundles/currency-bundle.php:28
msgid "Aruban florin"
msgstr "Florin arubais"

#: includes/bundles/currency-bundle.php:29
msgid "Azerbaijani manat"
msgstr "Manat azerbaïdjanais"

#: includes/bundles/currency-bundle.php:30
msgid "Bosnia and Herzegovina convertible mark"
msgstr "Mark convertible"

#: includes/bundles/currency-bundle.php:31
msgid "Barbadian dollar"
msgstr "Dollar barbadien"

#: includes/bundles/currency-bundle.php:32
msgid "Bangladeshi taka"
msgstr "Taka bangladais"

#: includes/bundles/currency-bundle.php:33
msgid "Bulgarian lev"
msgstr "Lev bulgare"

#: includes/bundles/currency-bundle.php:34
msgid "Bahraini dinar"
msgstr "Dinar bahreïni"

#: includes/bundles/currency-bundle.php:35
msgid "Burundian franc"
msgstr "Franc burundais"

#: includes/bundles/currency-bundle.php:36
msgid "Bermudian dollar"
msgstr "Dollar des Bermudes"

#: includes/bundles/currency-bundle.php:37
msgid "Brunei dollar"
msgstr "Dollar de Brunei"

#: includes/bundles/currency-bundle.php:38
msgid "Bolivian boliviano"
msgstr "Boliviano bolivien"

#: includes/bundles/currency-bundle.php:39
msgid "Brazilian real"
msgstr "Réal brésilien"

#: includes/bundles/currency-bundle.php:40
msgid "Bahamian dollar"
msgstr "Dollar des Bahamas"

#: includes/bundles/currency-bundle.php:41
msgid "Bitcoin"
msgstr "Bitcoin"

#: includes/bundles/currency-bundle.php:42
msgid "Bhutanese ngultrum"
msgstr "Ngultrum"

#: includes/bundles/currency-bundle.php:43
msgid "Botswana pula"
msgstr "Pula"

#: includes/bundles/currency-bundle.php:44
msgid "Belarusian ruble (old)"
msgstr "Rouble biélorusse (ancien)"

#: includes/bundles/currency-bundle.php:45
msgid "Belarusian ruble"
msgstr "Rouble biélorusse"

#: includes/bundles/currency-bundle.php:46
msgid "Belize dollar"
msgstr "Dollar de Belize"

#: includes/bundles/currency-bundle.php:47
msgid "Canadian dollar"
msgstr "Dollars canadiens"

#: includes/bundles/currency-bundle.php:48
msgid "Congolese franc"
msgstr "Franc congolais"

#: includes/bundles/currency-bundle.php:49
msgid "Swiss franc"
msgstr "Franc suisse"

#: includes/bundles/currency-bundle.php:50
msgid "Chilean peso"
msgstr "Peso chilien"

#: includes/bundles/currency-bundle.php:51
msgid "Chinese yuan"
msgstr "Yuan chinois"

#: includes/bundles/currency-bundle.php:52
msgid "Colombian peso"
msgstr "Peso colombien"

#: includes/bundles/currency-bundle.php:53
msgid "Costa Rican col&oacute;n"
msgstr "Colon du Costa Rica"

#: includes/bundles/currency-bundle.php:54
msgid "Cuban convertible peso"
msgstr "Peso cubain convertible"

#: includes/bundles/currency-bundle.php:55
msgid "Cuban peso"
msgstr "Peso Cubain"

#: includes/bundles/currency-bundle.php:56
msgid "Cape Verdean escudo"
msgstr "Escudo cap-verdien"

#: includes/bundles/currency-bundle.php:57
msgid "Czech koruna"
msgstr "Couronne tchèque"

#: includes/bundles/currency-bundle.php:58
msgid "Djiboutian franc"
msgstr "Franc Djibouti"

#: includes/bundles/currency-bundle.php:59
msgid "Danish krone"
msgstr "Couronne danoise"

#: includes/bundles/currency-bundle.php:60
msgid "Dominican peso"
msgstr "Peso dominicain"

#: includes/bundles/currency-bundle.php:61
msgid "Algerian dinar"
msgstr "Dinar algérien"

#: includes/bundles/currency-bundle.php:62
msgid "Egyptian pound"
msgstr "Livre égyptienne"

#: includes/bundles/currency-bundle.php:63
msgid "Eritrean nakfa"
msgstr "Nakfa érythréen"

#: includes/bundles/currency-bundle.php:64
msgid "Ethiopian birr"
msgstr "Birr"

#: includes/bundles/currency-bundle.php:65
msgid "Fijian dollar"
msgstr "Dollar de Fidji"

#: includes/bundles/currency-bundle.php:66
msgid "Falkland Islands pound"
msgstr "Livre des Îles Malouines"

#: includes/bundles/currency-bundle.php:67
msgid "Georgian lari"
msgstr "Lari"

#: includes/bundles/currency-bundle.php:68
msgid "Guernsey pound"
msgstr "Livre de Guernesey"

#: includes/bundles/currency-bundle.php:69
msgid "Ghana cedi"
msgstr "Cedi"

#: includes/bundles/currency-bundle.php:70
msgid "Gibraltar pound"
msgstr "Livre de Gibraltar"

#: includes/bundles/currency-bundle.php:71
msgid "Gambian dalasi"
msgstr "Dalasi"

#: includes/bundles/currency-bundle.php:72
msgid "Guinean franc"
msgstr "Franc guinéen"

#: includes/bundles/currency-bundle.php:73
msgid "Guatemalan quetzal"
msgstr "Quetzal"

#: includes/bundles/currency-bundle.php:74
msgid "Guyanese dollar"
msgstr "Dollar guyanien"

#: includes/bundles/currency-bundle.php:75
msgid "Hong Kong dollar"
msgstr "Dollar de Hong Kong"

#: includes/bundles/currency-bundle.php:76
msgid "Honduran lempira"
msgstr "Lempira"

#: includes/bundles/currency-bundle.php:77
msgid "Croatian kuna"
msgstr "Kuna croate"

#: includes/bundles/currency-bundle.php:78
msgid "Haitian gourde"
msgstr "Gourde"

#: includes/bundles/currency-bundle.php:79
msgid "Hungarian forint"
msgstr "Forint hongrois"

#: includes/bundles/currency-bundle.php:80
msgid "Indonesian rupiah"
msgstr "Roupie indonésienne"

#: includes/bundles/currency-bundle.php:81
msgid "Israeli new shekel"
msgstr "Shekel"

#: includes/bundles/currency-bundle.php:82
msgid "Manx pound"
msgstr "Livre mannoise"

#: includes/bundles/currency-bundle.php:83
msgid "Indian rupee"
msgstr "Roupie indienne"

#: includes/bundles/currency-bundle.php:84
msgid "Iraqi dinar"
msgstr "Dinar irakien"

#: includes/bundles/currency-bundle.php:85
msgid "Iranian rial"
msgstr "Rial iranien"

#: includes/bundles/currency-bundle.php:86
msgid "Iranian toman"
msgstr "Rial iranien"

#: includes/bundles/currency-bundle.php:87
msgid "Icelandic kr&oacute;na"
msgstr "Couronne islandaise"

#: includes/bundles/currency-bundle.php:88
msgid "Jersey pound"
msgstr "Livre de Jersey"

#: includes/bundles/currency-bundle.php:89
msgid "Jamaican dollar"
msgstr "Dollar jamaïcain"

#: includes/bundles/currency-bundle.php:90
msgid "Jordanian dinar"
msgstr "Dinar jordanien"

#: includes/bundles/currency-bundle.php:91
msgid "Japanese yen"
msgstr "Yen japonais"

#: includes/bundles/currency-bundle.php:92
msgid "Kenyan shilling"
msgstr "Shilling kenyan"

#: includes/bundles/currency-bundle.php:93
msgid "Kyrgyzstani som"
msgstr "Som Kirghiz"

#: includes/bundles/currency-bundle.php:94
msgid "Cambodian riel"
msgstr "Riel"

#: includes/bundles/currency-bundle.php:95
msgid "Comorian franc"
msgstr "Franc comorien"

#: includes/bundles/currency-bundle.php:96
msgid "North Korean won"
msgstr "won nord-coréen"

#: includes/bundles/currency-bundle.php:97
msgid "South Korean won"
msgstr "Won sud-coréen"

#: includes/bundles/currency-bundle.php:98
msgid "Kuwaiti dinar"
msgstr "Dinar koweïtien"

#: includes/bundles/currency-bundle.php:99
msgid "Cayman Islands dollar"
msgstr "Dollar des îles Caïmans"

#: includes/bundles/currency-bundle.php:100
msgid "Kazakhstani tenge"
msgstr "Tenge kazakh"

#: includes/bundles/currency-bundle.php:101
msgid "Lao kip"
msgstr "Kip laotien"

#: includes/bundles/currency-bundle.php:102
msgid "Lebanese pound"
msgstr "Livre libanaise"

#: includes/bundles/currency-bundle.php:103
msgid "Sri Lankan rupee"
msgstr "Roupie srilankaise"

#: includes/bundles/currency-bundle.php:104
msgid "Liberian dollar"
msgstr "dollar libérien"

#: includes/bundles/currency-bundle.php:105
msgid "Lesotho loti"
msgstr "Loti du Lesotho"

#: includes/bundles/currency-bundle.php:106
msgid "Libyan dinar"
msgstr "Dinar libyen"

#: includes/bundles/currency-bundle.php:107
msgid "Moroccan dirham"
msgstr "Diram marocain"

#: includes/bundles/currency-bundle.php:108
msgid "Moldovan leu"
msgstr "Leu moldave"

#: includes/bundles/currency-bundle.php:109
msgid "Malagasy ariary"
msgstr "Ariary malgache"

#: includes/bundles/currency-bundle.php:110
msgid "Macedonian denar"
msgstr "Denar macédonien"

#: includes/bundles/currency-bundle.php:111
msgid "Burmese kyat"
msgstr "Kyat birman"

#: includes/bundles/currency-bundle.php:112
msgid "Mongolian t&ouml;gr&ouml;g"
msgstr "Tugrik mongol"

#: includes/bundles/currency-bundle.php:113
msgid "Macanese pataca"
msgstr "Pataca de Macao"

#: includes/bundles/currency-bundle.php:114
msgid "Mauritanian ouguiya"
msgstr "Ouguiya mauritanienne"

#: includes/bundles/currency-bundle.php:115
msgid "Mauritian rupee"
msgstr "Roupie mauricienne"

#: includes/bundles/currency-bundle.php:116
msgid "Maldivian rufiyaa"
msgstr "Rufiyaa maldivienne"

#: includes/bundles/currency-bundle.php:117
msgid "Malawian kwacha"
msgstr "Kwacha malawien"

#: includes/bundles/currency-bundle.php:118
msgid "Mexican peso"
msgstr "Peso mexicain"

#: includes/bundles/currency-bundle.php:119
msgid "Malaysian ringgit"
msgstr "Ringgits malaisiens"

#: includes/bundles/currency-bundle.php:120
msgid "Mozambican metical"
msgstr "Metical mozambicain"

#: includes/bundles/currency-bundle.php:121
msgid "Namibian dollar"
msgstr "Dollar namibien"

#: includes/bundles/currency-bundle.php:122
msgid "Nigerian naira"
msgstr "Naira nigérienne"

#: includes/bundles/currency-bundle.php:123
msgid "Nicaraguan c&oacute;rdoba"
msgstr "Cordoba nicaraguayen"

#: includes/bundles/currency-bundle.php:124
msgid "Norwegian krone"
msgstr "Couronne norvégienne"

#: includes/bundles/currency-bundle.php:125
msgid "Nepalese rupee"
msgstr "Roupie népalaise"

#: includes/bundles/currency-bundle.php:126
msgid "New Zealand dollar"
msgstr "Dollar néo-zélandais"

#: includes/bundles/currency-bundle.php:127
msgid "Omani rial"
msgstr "Rial d'Oman"

#: includes/bundles/currency-bundle.php:128
msgid "Panamanian balboa"
msgstr "Balboa panaméen"

#: includes/bundles/currency-bundle.php:129
msgid "Sol"
msgstr "Sol"

#: includes/bundles/currency-bundle.php:130
msgid "Papua New Guinean kina"
msgstr "Kina de Papouasie Nouvelle-Guinée"

#: includes/bundles/currency-bundle.php:131
msgid "Philippine peso"
msgstr "Peso philippin"

#: includes/bundles/currency-bundle.php:132
msgid "Pakistani rupee"
msgstr "Roupie pakistanaise"

#: includes/bundles/currency-bundle.php:133
msgid "Polish z&#x142;oty"
msgstr "Z&#x142;oty polonais"

#: includes/bundles/currency-bundle.php:134
msgid "Transnistrian ruble"
msgstr "Rouble de Transnistrie"

#: includes/bundles/currency-bundle.php:135
msgid "Paraguayan guaran&iacute;"
msgstr "Guarani paraguayen"

#: includes/bundles/currency-bundle.php:136
msgid "Qatari riyal"
msgstr "Riyal qatari"

#: includes/bundles/currency-bundle.php:137
msgid "Romanian leu"
msgstr "Leu roumain"

#: includes/bundles/currency-bundle.php:138
msgid "Serbian dinar"
msgstr "Dinar serbe"

#: includes/bundles/currency-bundle.php:139
msgid "Russian ruble"
msgstr "Rouble russe"

#: includes/bundles/currency-bundle.php:140
msgid "Rwandan franc"
msgstr "Franc rwandais"

#: includes/bundles/currency-bundle.php:141
msgid "Saudi riyal"
msgstr "Riyal saoudien"

#: includes/bundles/currency-bundle.php:142
msgid "Solomon Islands dollar"
msgstr "Dollar des îles Salomons"

#: includes/bundles/currency-bundle.php:143
msgid "Seychellois rupee"
msgstr "Roupie seychelloise"

#: includes/bundles/currency-bundle.php:144
msgid "Sudanese pound"
msgstr "Livre soudanaise"

#: includes/bundles/currency-bundle.php:145
msgid "Swedish krona"
msgstr "Couronne suédoise"

#: includes/bundles/currency-bundle.php:146
msgid "Singapore dollar"
msgstr "Dollar de Singapour"

#: includes/bundles/currency-bundle.php:147
msgid "Saint Helena pound"
msgstr "Livre de Sainte Hélène"

#: includes/bundles/currency-bundle.php:148
msgid "Sierra Leonean leone"
msgstr "Leone sierra-léonais"

#: includes/bundles/currency-bundle.php:149
msgid "Somali shilling"
msgstr "Shilling somalien"

#: includes/bundles/currency-bundle.php:150
msgid "Surinamese dollar"
msgstr "Dollar surinamien"

#: includes/bundles/currency-bundle.php:151
msgid "South Sudanese pound"
msgstr "Livre sud-soudanaise"

#: includes/bundles/currency-bundle.php:152
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe dobra"
msgstr "Dobra de Sao Tome"

#: includes/bundles/currency-bundle.php:153
msgid "Syrian pound"
msgstr "Livre syrienne"

#: includes/bundles/currency-bundle.php:154
msgid "Swazi lilangeni"
msgstr "Lilangeni swazi"

#: includes/bundles/currency-bundle.php:155
msgid "Thai baht"
msgstr "Baht thaïlandais"

#: includes/bundles/currency-bundle.php:156
msgid "Tajikistani somoni"
msgstr "Somoni tadjik"

#: includes/bundles/currency-bundle.php:157
msgid "Turkmenistan manat"
msgstr "Manat turkmène"

#: includes/bundles/currency-bundle.php:158
msgid "Tunisian dinar"
msgstr "Dinar tunisien"

#: includes/bundles/currency-bundle.php:159
msgid "Tongan pa&#x2bb;anga"
msgstr "Pa'anga de Tonga"

#: includes/bundles/currency-bundle.php:160
msgid "Turkish lira"
msgstr "Lira turque"

#: includes/bundles/currency-bundle.php:161
msgid "Trinidad and Tobago dollar"
msgstr "Dollar de Trinidad and Tobago"

#: includes/bundles/currency-bundle.php:162
msgid "New Taiwan dollar"
msgstr "Nouveau dollar taïwanais"

#: includes/bundles/currency-bundle.php:163
msgid "Tanzanian shilling"
msgstr "Shilling tanzanien"

#: includes/bundles/currency-bundle.php:164
msgid "Ukrainian hryvnia"
msgstr "Hryvnia ukrainienne"

#: includes/bundles/currency-bundle.php:165
msgid "Ugandan shilling"
msgstr "Shilling ougandais"

#: includes/bundles/currency-bundle.php:166
msgid "Uruguayan peso"
msgstr "Peso uruguayen"

#: includes/bundles/currency-bundle.php:167
msgid "Uzbekistani som"
msgstr "Som ouzbek"

#: includes/bundles/currency-bundle.php:168
msgid "Venezuelan bol&iacute;var"
msgstr "Bolivar vénézuélien "

#: includes/bundles/currency-bundle.php:169
msgid "Bol&iacute;var soberano"
msgstr "Bolivar souverain"

#: includes/bundles/currency-bundle.php:170
msgid "Vietnamese &#x111;&#x1ed3;ng"
msgstr "Dong vietnamien"

#: includes/bundles/currency-bundle.php:171
msgid "Vanuatu vatu"
msgstr "Vatu"

#: includes/bundles/currency-bundle.php:172
msgid "Samoan t&#x101;l&#x101;"
msgstr "Tala samoan"

#: includes/bundles/currency-bundle.php:173
msgid "Central African CFA franc"
msgstr "Franc CFA (CEMAC)"

#: includes/bundles/currency-bundle.php:174
msgid "East Caribbean dollar"
msgstr "Dollar des Caraïbes orientales"

#: includes/bundles/currency-bundle.php:175
msgid "West African CFA franc"
msgstr "Franc CFA ouest-africain"

#: includes/bundles/currency-bundle.php:176
msgid "CFP franc"
msgstr "franc CFP"

#: includes/bundles/currency-bundle.php:177
msgid "Yemeni rial"
msgstr "rial yéménite"

#: includes/bundles/currency-bundle.php:178
msgid "South African rand"
msgstr "Rand sud-africain"

#: includes/bundles/currency-bundle.php:179
msgid "Zambian kwacha"
msgstr "kwacha zambien"

#: includes/bundles/currency-bundle.php:358
msgid "Before"
msgstr "Avant"

#: includes/bundles/currency-bundle.php:359
msgid "After"
msgstr "Après"

#: includes/bundles/currency-bundle.php:360
msgid "Before with space"
msgstr "Avant avec de l'espace"

#: includes/bundles/currency-bundle.php:361
msgid "After with space"
msgstr "Après avec de l'espace"

#: includes/bundles/customer-bundle.php:97
msgid "First name is required."
msgstr "Le prénom est requis."

#: includes/bundles/customer-bundle.php:106
msgid "Last name is required."
msgstr "Le nom de famille est requis."

#: includes/bundles/customer-bundle.php:115
msgid "Email is required."
msgstr "Le courrier électronique est requis."

#: includes/bundles/customer-bundle.php:124
msgid "Phone is required."
msgstr "Le numéro de téléphone est requis."

#: includes/bundles/customer-bundle.php:128
#: includes/views/shortcodes/checkout-view.php:650
msgid "Country of residence"
msgstr "Pays de résidence"

#: includes/bundles/customer-bundle.php:133
msgid "Country is required."
msgstr "Pays est requis."

#: includes/bundles/customer-bundle.php:142
msgid "Address is required."
msgstr "Adresse est requise."

#: includes/bundles/customer-bundle.php:151
msgid "City is required."
msgstr "Une ville est requise."

#: includes/bundles/customer-bundle.php:160
msgid "State is required."
msgstr "L'état est requis."

#: includes/bundles/customer-bundle.php:169
msgid "Postcode is required."
msgstr "Code postal est requis."

#: includes/bundles/customer-bundle.php:178
msgid "Note is required."
msgstr "Une note est requise."

#: includes/bundles/units-bundle.php:16
msgid "Square Meter"
msgstr "Mètre carré"

#: includes/bundles/units-bundle.php:17
msgid "Square Foot"
msgstr "Pieds carrés"

#: includes/bundles/units-bundle.php:18
msgid "Square Yard"
msgstr "Mètre carré"

#: includes/bundles/units-bundle.php:21
msgid "m²"
msgstr "m²"

#: includes/bundles/units-bundle.php:22
msgid "ft²"
msgstr "ft²"

#: includes/bundles/units-bundle.php:23
msgid "yd²"
msgstr "yd²"

#: includes/core/helpers/price-helper.php:57
msgctxt "Zero price"
msgid "Free"
msgstr "Gratuit"

#. translators: Price per one night. Example: $99 per night
#: includes/core/helpers/price-helper.php:144
msgctxt "Price per one night. Example: $99 per night"
msgid "per night"
msgstr "par nuit"

#. translators: Price for X nights. Example: $99 for 2 nights, $99 for 21 nights
#: includes/core/helpers/price-helper.php:156
msgctxt "Price for X nights. Example: $99 for 2 nights, $99 for 21 nights"
msgid "for %d nights"
msgid_plural "for %d nights"
msgstr[0] "par nuit"
msgstr[1] "pour %d nuits"

#: includes/crons/cron-manager.php:112
msgid "User Approval Time setted in Hotel Booking Settings"
msgstr "Temps d'approbation des utilisateurs réglé dans les paramètres de réservation d'hôtel"

#: includes/crons/cron-manager.php:117
msgid "Pending Payment Time set in Hotel Booking Settings"
msgstr "Temps de paiement en attente réglé dans les paramètres de réservation d'hôtel"

#: includes/crons/cron-manager.php:122
msgid "Interval for automatic cleaning of synchronization logs."
msgstr "Intervalle de nettoyage automatique des logs de synchronisation."

#: includes/crons/cron-manager.php:127
msgid "Once a week"
msgstr "Une fois par semaine"

#: includes/csv/bookings/bookings-exporter-helper.php:73
#: templates/account/bookings.php:19
#: templates/account/bookings.php:70
#: templates/create-booking/search/search-form.php:42
#: templates/edit-booking/edit-dates.php:29
#: templates/shortcodes/search/search-form.php:35
msgid "Check-in"
msgstr "Arrivée"

#: includes/csv/bookings/bookings-exporter-helper.php:74
#: templates/account/bookings.php:20
#: templates/account/bookings.php:73
#: templates/create-booking/search/search-form.php:62
#: templates/edit-booking/edit-dates.php:38
#: templates/shortcodes/search/search-form.php:55
msgid "Check-out"
msgstr "Départ"

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/csv/bookings/bookings-exporter-helper.php:78
#: includes/post-types/rate-cpt.php:104
msgid "Rate"
msgstr "Taux"

#: includes/csv/bookings/bookings-exporter-helper.php:79
msgid "Adults/Guests"
msgstr "Adultes/Invités"

#: includes/csv/bookings/bookings-exporter-helper.php:91
#: includes/emails/templaters/reserved-rooms-templater.php:223
#: includes/views/edit-booking/checkout-view.php:164
#: includes/views/shortcodes/checkout-view.php:291
msgid "Full Guest Name"
msgstr "Nom complet du client"

#: includes/csv/bookings/bookings-exporter-helper.php:92
#: includes/views/booking-view.php:141
msgid "Accommodation Subtotal"
msgstr "Logement sous-total"

#: includes/csv/bookings/bookings-exporter-helper.php:93
#: includes/post-types/coupon-cpt.php:72
#: includes/views/booking-view.php:150
msgid "Accommodation Discount"
msgstr "Rabais sur l’hébergement"

#: includes/csv/bookings/bookings-exporter-helper.php:94
#: includes/views/booking-view.php:160
msgid "Accommodation Total"
msgstr "Total pour l'hébergement"

#: includes/csv/bookings/bookings-exporter-helper.php:96
#: includes/views/booking-view.php:186
msgid "Accommodation Taxes Total"
msgstr "Total des taxes sur l’hébergement"

#: includes/csv/bookings/bookings-exporter-helper.php:98
#: includes/views/booking-view.php:225
msgid "Services Subtotal"
msgstr "Sous-total des services"

#: includes/csv/bookings/bookings-exporter-helper.php:99
#: includes/views/booking-view.php:236
msgid "Services Discount"
msgstr "Rabais sur les services"

#: includes/csv/bookings/bookings-exporter-helper.php:100
#: includes/views/booking-view.php:248
msgid "Services Total"
msgstr "Total des frais de service"

#: includes/csv/bookings/bookings-exporter-helper.php:101
#: includes/views/booking-view.php:280
msgid "Service Taxes Total"
msgstr "Total des taxes sur les services"

#: includes/csv/bookings/bookings-exporter-helper.php:103
#: includes/views/booking-view.php:312
msgid "Fees Subtotal"
msgstr "Frais Sous-total"

#: includes/csv/bookings/bookings-exporter-helper.php:104
#: includes/views/booking-view.php:321
msgid "Fees Discount"
msgstr "Rabais sur les frais"

#: includes/csv/bookings/bookings-exporter-helper.php:105
#: includes/views/booking-view.php:331
msgid "Fees Total"
msgstr "Total des frais"

#: includes/csv/bookings/bookings-exporter-helper.php:106
#: includes/views/booking-view.php:364
msgid "Fee Taxes Total"
msgstr "Total des taxes sur les frais"

#: includes/csv/bookings/bookings-exporter-helper.php:108
msgid "Discount"
msgstr "remise"

#: includes/csv/bookings/bookings-exporter-helper.php:109
#: includes/views/booking-view.php:451
#: templates/account/bookings.php:21
#: templates/account/bookings.php:76
msgid "Total"
msgstr "Total"

#: includes/csv/bookings/bookings-exporter-helper.php:110
msgid "Paid"
msgstr "Payé"

#: includes/csv/bookings/bookings-exporter-helper.php:111
#: includes/post-types/payment-cpt.php:129
#: includes/shortcodes/booking-confirmation-shortcode.php:284
msgid "Payment Details"
msgstr "Détails de paiement"

#: includes/csv/bookings/bookings-query.php:92
msgid "Please select columns to export."
msgstr "Veuillez sélectionner les colonnes à exporter."

#: includes/csv/csv-export-handler.php:32
#: includes/payments/gateways/stripe-gateway.php:559
msgid "Nonce verification failed."
msgstr "La vérification de nonce a échoué."

#: includes/csv/csv-export-handler.php:50
msgid "The file does not exist."
msgstr "Le fichier n'existe pas."

#: includes/emails/abstract-email.php:441
msgid "Disable this email notification"
msgstr "Désactiver cette notification par courrier électronique"

#: includes/emails/abstract-email.php:449
msgid "Subject"
msgstr "Sujet"

#: includes/emails/abstract-email.php:461
msgid "Header"
msgstr "Entête"

#: includes/emails/abstract-email.php:473
msgid "Email Template"
msgstr "Modèle d'e-mail"

#: includes/emails/abstract-email.php:570
msgid "\"%s\" email will not be sent: there is no customer email in the booking."
msgstr "\"%s\" email ne sera pas envoyé: il n'y a pas de mail client dans la réservation."

#: includes/emails/abstract-email.php:594
msgid "Deprecated tags in header of %s"
msgstr "Balises supprimées dans l'en-tête de %s"

#: includes/emails/abstract-email.php:597
msgid "Deprecated tags in subject of %s"
msgstr "Balises supprimées dans le sujet de %s"

#: includes/emails/abstract-email.php:600
msgid "Deprecated tags in template of %s"
msgstr "Étiquettes supprimées dans le modèle de %s"

#: includes/emails/booking/admin/base-email.php:37
msgid "Recipients"
msgstr "Bénéficiaires"

#: includes/emails/booking/admin/base-email.php:40
msgid "You can use multiple comma-separated emails"
msgstr "Vous pouvez utiliser plusieurs courriels séparés par des virgules"

#: includes/emails/booking/admin/base-email.php:89
msgid "\"%s\" mail was sent to admin."
msgstr "Le courrier \"%s' a été envoyé à l'admin."

#: includes/emails/booking/admin/base-email.php:93
msgid "\"%s\" mail sending to admin is failed."
msgstr "L'envoi du courrier \"%s\" à l'admin échoue."

#: includes/emails/booking/admin/cancelled-email.php:8
msgid "Booking Cancelled"
msgstr "Réservation annulée"

#: includes/emails/booking/admin/cancelled-email.php:12
msgid "%site_title% - Booking #%booking_id% Cancelled"
msgstr "%site_title% - La réservation #%booking_id% est annulée"

#: includes/emails/booking/admin/cancelled-email.php:16
msgid "Email that will be sent to Admin when customer cancels booking."
msgstr "Email qui sera envoyé à l'Administrateur lorsque le client annule la réservation."

#: includes/emails/booking/admin/cancelled-email.php:20
#: includes/emails/booking/customer/cancelled-email.php:20
msgid "Cancelled Booking Email"
msgstr "Email d'annulation de réservation"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:8
#: includes/emails/booking/admin/confirmed-email.php:8
#: includes/wizard.php:134
msgid "Booking Confirmed"
msgstr "Réservation confirmée"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:12
#: includes/emails/booking/admin/confirmed-email.php:12
msgid "%site_title% - Booking #%booking_id% Confirmed"
msgstr "%site_title% - Réservation #%booking_id% confirmée"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:16
msgid "Email that will be sent to Admin when payment is completed."
msgstr "Courriel qui sera envoyé à l'administrateur quand le paiement sera terminé."

#: includes/emails/booking/admin/confirmed-by-payment-email.php:20
msgid "Approved Booking Email (via payment)"
msgstr "Courriel de la réservation approuvée (via le paiement)"

#: includes/emails/booking/admin/confirmed-email.php:16
msgid "Email that will be sent to Admin when customer confirms booking."
msgstr "Email qui sera envoyé à Admin lorsque le client confirmera la réservation."

#: includes/emails/booking/admin/confirmed-email.php:20
#: includes/emails/booking/customer/approved-email.php:20
msgid "Approved Booking Email"
msgstr "Email de la réservation approuvée"

#: includes/emails/booking/admin/pending-email.php:8
msgid "Confirm new booking"
msgstr "Confirmer la nouvelle réservation"

#: includes/emails/booking/admin/pending-email.php:12
msgid "%site_title% - New booking #%booking_id%"
msgstr "%site_title% - Nouvelle réservation #%booking_id%"

#: includes/emails/booking/admin/pending-email.php:16
msgid "Email that will be sent to administrator after booking is placed."
msgstr "Email qui sera envoyé à l'administrateur après la réservation."

#: includes/emails/booking/admin/pending-email.php:20
msgid "Pending Booking Email"
msgstr "Email de réservation en attente"

#: includes/emails/booking/customer/approved-email.php:8
msgid "Your booking is approved"
msgstr "Votre réservation est approuvée"

#: includes/emails/booking/customer/approved-email.php:12
msgid "%site_title% - Your booking #%booking_id% is approved"
msgstr "%site_title% - Votre réservation #%booking_id% est approuvée"

#: includes/emails/booking/customer/approved-email.php:16
msgid "Email that will be sent to customer when booking is approved."
msgstr "Email qui sera envoyé au client quand la réservation est approuvée."

#: includes/emails/booking/customer/base-email.php:55
msgid "\"%s\" mail was sent to customer."
msgstr "Le courrier \"%s\" a été envoyé au client."

#: includes/emails/booking/customer/base-email.php:59
msgid "\"%s\" mail sending is failed."
msgstr "\"%s\" envoi du courrier électronique a échoué."

#: includes/emails/booking/customer/cancelled-email.php:8
msgid "Your booking is cancelled"
msgstr "Votre réservation est annulée"

#: includes/emails/booking/customer/cancelled-email.php:12
msgid "%site_title% - Your booking #%booking_id% is cancelled"
msgstr "%site_title% - Votre réservation #%booking_id% est annulée"

#: includes/emails/booking/customer/cancelled-email.php:16
msgid "Email that will be sent to customer when booking is cancelled."
msgstr "Email qui sera envoyé au client lors de l'annulation de la réservation."

#: includes/emails/booking/customer/confirmation-email.php:8
msgid "Confirm your booking"
msgstr "Confirmez votre réservation"

#: includes/emails/booking/customer/confirmation-email.php:12
msgid "%site_title% - Confirm your booking #%booking_id%"
msgstr "%site_title% - Confirmez votre réservation #%booking_id%"

#: includes/emails/booking/customer/confirmation-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Customer confirmation via email."
msgstr "Cet e-mail est envoyé lorsque \"Mode de confirmation de réservation\" est réglé sur \"Confirmation du client par e-mail\"."

#: includes/emails/booking/customer/confirmation-email.php:17
#: includes/emails/booking/customer/direct-bank-transfer-email.php:43
#: includes/emails/booking/customer/pending-email.php:17
msgid "Email that will be sent to customer after booking is placed."
msgstr "Email qui sera envoyé au client après la réservation."

#: includes/emails/booking/customer/confirmation-email.php:21
msgid "New Booking Email (Confirmation by User)"
msgstr "Nouvel e-mail de réservation (Confirmation par utilisateur)"

#: includes/emails/booking/customer/direct-bank-transfer-email.php:35
msgid "Pay for your booking"
msgstr "Payer pour votre réservation"

#: includes/emails/booking/customer/direct-bank-transfer-email.php:39
msgid "%site_title% - Pay for your booking #%booking_id%"
msgstr ""

#: includes/emails/booking/customer/direct-bank-transfer-email.php:47
msgid "Payment Instructions Email"
msgstr ""

#: includes/emails/booking/customer/pending-email.php:8
msgid "Your booking is placed"
msgstr "Votre réservation est effectuée"

#: includes/emails/booking/customer/pending-email.php:12
msgid "%site_title% - Booking #%booking_id% is placed"
msgstr "%site_title% - Votre réservation #%booking_id% est effectuée"

#: includes/emails/booking/customer/pending-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Admin confirmation."
msgstr "Cet e-mail est envoyé lorsque \"Confirmation du mode de réservation\" est défini sur Confirmation de l'administrateur."

#: includes/emails/booking/customer/pending-email.php:21
msgid "New Booking Email (Confirmation by Admin)"
msgstr "Nouvel e-mail de réservation (Confirmation par Admin)"

#: includes/emails/booking/customer/registration-email.php:8
msgid "Welcome"
msgstr "Bienvenue"

#: includes/emails/booking/customer/registration-email.php:12
msgid "%site_title% - account details"
msgstr "%site_title% - détails du compte"

#: includes/emails/booking/customer/registration-email.php:16
msgid "Email that will be sent to a customer after they registered on your site."
msgstr "Courriel à envoyer au client après son inscription sur votre site."

#: includes/emails/booking/customer/registration-email.php:20
msgid "Customer Registration Email"
msgstr "Email d'enregistrement de l'utilisateur"

#: includes/emails/templaters/abstract-templater.php:77
msgid "Email Tags"
msgstr ""

#: includes/emails/templaters/abstract-templater.php:87
#: includes/emails/templaters/abstract-templater.php:89
msgid "Deprecated."
msgstr "Obsolète."

#: includes/emails/templaters/abstract-templater.php:101
msgid "none"
msgstr "aucun"

#: includes/emails/templaters/cancellation-booking-templater.php:50
msgid "User Cancellation Link"
msgstr "Lien d'annulation de l'utilisateur"

#: includes/emails/templaters/email-templater.php:109
msgid "Site title (set in Settings > General)"
msgstr "Titre du site (défini dans Paramètres> Général)"

#: includes/emails/templaters/email-templater.php:124
#: includes/post-types/payment-cpt.php:232
msgid "Booking ID"
msgstr "ID de réservation"

#: includes/emails/templaters/email-templater.php:128
msgid "Booking Edit Link"
msgstr "Lien pour édition de la réservation"

#: includes/emails/templaters/email-templater.php:132
msgid "Booking Total Price"
msgstr "Prix total de la réservation"

#: includes/emails/templaters/email-templater.php:153
#: includes/emails/templaters/email-templater.php:296
msgid "Customer First Name"
msgstr "Prénom du client"

#: includes/emails/templaters/email-templater.php:157
#: includes/emails/templaters/email-templater.php:300
msgid "Customer Last Name"
msgstr "Nom du client"

#: includes/emails/templaters/email-templater.php:161
#: includes/emails/templaters/email-templater.php:304
msgid "Customer Email"
msgstr "Email du client"

#: includes/emails/templaters/email-templater.php:165
#: includes/emails/templaters/email-templater.php:308
msgid "Customer Phone"
msgstr "Téléphone du client"

#: includes/emails/templaters/email-templater.php:169
#: includes/emails/templaters/email-templater.php:312
msgid "Customer Country"
msgstr "Pays du client"

#: includes/emails/templaters/email-templater.php:173
#: includes/emails/templaters/email-templater.php:316
msgid "Customer Address"
msgstr "Adresse du client"

#: includes/emails/templaters/email-templater.php:177
#: includes/emails/templaters/email-templater.php:320
msgid "Customer City"
msgstr "Ville du client"

#: includes/emails/templaters/email-templater.php:181
#: includes/emails/templaters/email-templater.php:324
msgid "Customer State/County"
msgstr "Etat/pays du client"

#: includes/emails/templaters/email-templater.php:185
#: includes/emails/templaters/email-templater.php:328
msgid "Customer Postcode"
msgstr "Code postal du client"

#: includes/emails/templaters/email-templater.php:194
msgid "Reserved Accommodations Details"
msgstr "Détails des hébergements réservés"

#: includes/emails/templaters/email-templater.php:216
#: includes/views/create-booking/checkout-view.php:15
#: includes/views/shortcodes/checkout-view.php:164
#: templates/shortcodes/booking-details/booking-details.php:18
msgid "Booking Details"
msgstr "Détails de réservation"

#: includes/emails/templaters/email-templater.php:230
msgid "Confirmation Link"
msgstr "Lien de confirmation"

#: includes/emails/templaters/email-templater.php:234
msgid "Confirmation Link Expiration Time ( UTC )"
msgstr "Heure d'expiration du lien de confirmation (UTC)"

#: includes/emails/templaters/email-templater.php:248
msgid "Cancellation Details (if enabled)"
msgstr "Détails d'annulation (si disponoble)"

#: includes/emails/templaters/email-templater.php:262
msgid "The total amount of payment"
msgstr ""

#: includes/emails/templaters/email-templater.php:266
msgid "The unique ID of payment"
msgstr "Identifiant unique de paiement"

#: includes/emails/templaters/email-templater.php:270
msgid "The method of payment"
msgstr "Méthode de paiement"

#: includes/emails/templaters/email-templater.php:274
msgid "Payment instructions"
msgstr "Instructions de paiement"

#: includes/emails/templaters/email-templater.php:288
msgid "User login"
msgstr "Connexion de l'utilisateur"

#: includes/emails/templaters/email-templater.php:292
msgid "User password"
msgstr "Mot de passe utilisateur"

#: includes/emails/templaters/email-templater.php:332
msgid "Link to My Account page"
msgstr "Lien vers la page Mon compte"

#: includes/emails/templaters/email-templater.php:562
#: includes/upgrader.php:868
#: includes/wizard.php:213
msgid "My Account"
msgstr "Mon Compte"

#: includes/emails/templaters/reserved-rooms-templater.php:191
msgid "Accommodation Type Link"
msgstr "Lien du type de logement"

#: includes/emails/templaters/reserved-rooms-templater.php:195
msgid "Accommodation Type Title"
msgstr "Titre du type de logement"

#: includes/emails/templaters/reserved-rooms-templater.php:199
msgid "Accommodation Title"
msgstr ""

#: includes/emails/templaters/reserved-rooms-templater.php:203
msgid "Accommodation Type Categories"
msgstr "Catégories de types de logement"

#: includes/emails/templaters/reserved-rooms-templater.php:207
msgid "Accommodation Type Bed"
msgstr "Type de lit"

#: includes/emails/templaters/reserved-rooms-templater.php:211
msgid "Accommodation Rate Title"
msgstr "Titre du tarif"

#: includes/emails/templaters/reserved-rooms-templater.php:215
msgid "Accommodation Rate Description"
msgstr "Description du tarif d'hébergement"

#: includes/emails/templaters/reserved-rooms-templater.php:219
msgid "Sequential Number of Accommodation"
msgstr "Nombre d'hébergement séquentiel"

#: includes/entities/coupon.php:370
msgid "This coupon has expired."
msgstr "Ce coupon a expiré."

#: includes/entities/coupon.php:374
msgid "Sorry, this coupon is not applicable to your booking contents."
msgstr "Désolé, ce coupon ne s'applique pas au contenu de votre réservation."

#: includes/entities/coupon.php:378
msgid "Coupon usage limit has been reached."
msgstr "La limite d'utilisation du coupon a été atteinte."

#: includes/entities/reserved-service.php:98
msgid " &#215; %d night"
msgid_plural " &#215; %d nights"
msgstr[0] " &#215; %d nuit"
msgstr[1] " &#215; %d nuits"

#: includes/entities/reserved-service.php:103
#: includes/shortcodes/search-results-shortcode.php:904
msgid "%d adult"
msgid_plural "%d adults"
msgstr[0] "%d adulte"
msgstr[1] "%d adultes"

#: includes/entities/reserved-service.php:105
#: includes/shortcodes/search-results-shortcode.php:896
#: includes/shortcodes/search-results-shortcode.php:900
msgid "%d guest"
msgid_plural "%d guests"
msgstr[0] "%d invité"
msgstr[1] "%d invités"

#: includes/entities/reserved-service.php:110
msgid " &#215; %d time"
msgid_plural " &#215; %d times"
msgstr[0] " &#215; %d fois"
msgstr[1] " &#215; %d fois"

#: includes/entities/service.php:195
msgid "Per Instance"
msgstr "Par instance"

#: includes/i-cal/background-processes/background-synchronizer.php:34
msgid "Maximum execution time is set to %d seconds."
msgstr "La durée d'exécution maximale est définie sur %d secondes."

#: includes/i-cal/background-processes/background-synchronizer.php:80
msgid "%d URL pulled for parsing."
msgid_plural "%d URLs pulled for parsing."
msgstr[0] "%d URL tirée pour l'analyse."
msgstr[1] "%d URLs tirées pour l'analyse."

#: includes/i-cal/background-processes/background-synchronizer.php:82
msgid "Skipped. No URLs found for parsing."
msgstr "Ignoré. Aucune URL trouvée pour l'analyse."

#: includes/i-cal/background-processes/background-uploader.php:64
msgid "Cannot read uploaded file"
msgstr "Impossible de lire le fichier importé"

#: includes/i-cal/background-processes/background-worker.php:327
msgctxt "%s - calendar URI or calendar filename"
msgid "%1$d event found in calendar %2$s"
msgid_plural "%1$d events found in calendar %2$s"
msgstr[0] "%1$d événement trouvé dans le calendrier %2$s"
msgstr[1] "%1$d événements trouvés dans le calendrier %2$s"

#: includes/i-cal/background-processes/background-worker.php:357
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar source is empty (%s)"
msgstr "La source du calendrier (%s) est vide"

#: includes/i-cal/background-processes/background-worker.php:370
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar file is not empty, but there are no events in %s"
msgstr "Le fichier de calendrier n'est pas vide, mais il n'y a aucun événement dans %s"

#: includes/i-cal/background-processes/background-worker.php:403
msgid "We will need to check %d previous booking after importing and remove it if the booking is outdated."
msgid_plural "We will need to check %d previous bookings after importing and remove the outdated ones."
msgstr[0] "Nous devrons vérifier %d la réservation précédente après l'importation et l'enlever si la réservation est périmée."
msgstr[1] "Nous devrons vérifier %d les réservations précédentes après l'importation et les enlever si la réservation est périmée."

#: includes/i-cal/background-processes/background-worker.php:425
msgid "Error while loading calendar (%1$s): %2$s"
msgstr "Erreur lors du chargement du calendrier (%1$s): %2$s"

#: includes/i-cal/background-processes/background-worker.php:427
msgctxt "%s - error description"
msgid "Parse error. %s"
msgstr "Erreur d'analyse. %s"

#: includes/i-cal/background-processes/background-worker.php:468
msgid "Skipped. Outdated booking #%d already removed."
msgstr "Ignoré. Réservation #%d dépassé déjà supprimée."

#: includes/i-cal/background-processes/background-worker.php:475
msgid "Skipped. Booking #%d updated with new data."
msgstr "Ignoré. Réservation %d mis à jour avec de nouvelles données."

#: includes/i-cal/background-processes/background-worker.php:497
msgid "The outdated booking #%d has been removed."
msgstr "La réservation désuète #%d a été supprimée."

#: includes/i-cal/importer.php:104
msgid "Skipped. Event from %1$s to %2$s has passed."
msgstr "Événement ignoré. L'événement de %1$s à %2$s est passé."

#: includes/i-cal/importer.php:120
msgid "New booking #%1$d. The dates from %2$s to %3$s are now blocked."
msgstr "Nouvelle réservation #%1$d. Les dates de %2$s à %3$s sont maintenant bloquées."

#: includes/i-cal/importer.php:140
msgid "Success. Booking #%d updated with new data."
msgstr "Valider. Réservation #%d mise à jour avec de nouvelles données.         "

#: includes/i-cal/importer.php:148
msgid "Skipped. The dates from %1$s to %2$s are already blocked."
msgstr "Annulé. Les dates de %1$s à %2$s sont déjà bloquées."

#: includes/i-cal/importer.php:164
msgid "Success. Booking #%1$d updated with new data. Removed %2$d outdated booking."
msgid_plural "Success. Booking #%1$d updated with new data. Removed %2$d outdated bookings."
msgstr[0] "Validé. Réservation #%1$d mise à jour avec de nouvelles données. Supprimé %2$d réservation périmée."
msgstr[1] "Validé. Réservation #%1$d mise à jour avec de nouvelles données. Supprimés %2$d réservations périmées."

#: includes/i-cal/importer.php:166
msgid "Success. Booking #%1$d updated with new data."
msgstr "Réussi. Réservation #%1$d mise à jour avec de nouvelles données."

#: includes/i-cal/importer.php:177
msgid "Cannot import new event. Dates from %1$s to %2$s are partially blocked by booking %3$s."
msgid_plural "Cannot import new event. Dates from %1$s to %2$s are partially blocked by bookings %3$s."
msgstr[0] "Impossible d'importer un nouvel événement. Les dates de %1$s à %2$s sont partiellement bloquées par %3$s."
msgstr[1] "Impossible d'importer un nouvel événement. Les dates de %1$s à %2$s sont partiellement bloquées par %3$s."

#: includes/i-cal/importer.php:233
msgid "Booking imported with UID %1$s.<br />Summary: %2$s.<br />Description: %3$s.<br />Source: %4$s."
msgstr "Réservation importée avec UID %1$s.<br />Sommaire : %2$s.<br />Description : %3$s.<br />Source : %4$s."

#: includes/i-cal/logs-handler.php:25
msgid "Process Information"
msgstr "Traitement de l'information"

#: includes/i-cal/logs-handler.php:35
msgid "Total bookings: %s"
msgstr "Total des réservations : %s"

#: includes/i-cal/logs-handler.php:37
msgid "Success bookings: %s"
msgstr "Réservations réussies : %s"

#: includes/i-cal/logs-handler.php:39
msgid "Skipped bookings: %s"
msgstr "Réservations ignorées : %s"

#: includes/i-cal/logs-handler.php:41
msgid "Failed bookings: %s"
msgstr "Réservations échouées : %s"

#: includes/i-cal/logs-handler.php:43
msgid "Removed bookings: %s"
msgstr "Réservations supprimées : %s"

#: includes/i-cal/logs-handler.php:87
msgid "Expand All"
msgstr "Développer tout"

#: includes/i-cal/logs-handler.php:91
msgid "Collapse All"
msgstr "Tout réduire"

#: includes/i-cal/logs-handler.php:138
msgid "All done! %1$d booking was successfully added."
msgid_plural "All done! %1$d bookings were successfully added."
msgstr[0] "Terminé! %1$d réservation a été ajoutée avec succès."
msgstr[1] "Terminé! %1$d réservations ont été ajoutées avec succès."

#: includes/i-cal/logs-handler.php:139
msgid " There was %2$d failure."
msgid_plural " There were %2$d failures."
msgstr[0] " Il y a eu un échec de %2$d."
msgstr[1] " Il y a eu des échecs de %2$d."

#: includes/license-notice.php:87
#: includes/license-notice.php:160
msgid "Your License Key is not active. Please, <a href=\"%s\">activate your License Key</a> to get plugin updates."
msgstr ""

#: includes/license-notice.php:152
msgid "Dismiss "
msgstr "Révoquer "

#: includes/linked-rooms.php:31
msgid "Blocked because the linked accommodation is booked"
msgstr ""

#: includes/notices.php:138
#: includes/notices.php:156
#: includes/wizard.php:33
msgid "Hotel Booking Plugin"
msgstr "Plugin de réservation d'hôtel"

#: includes/notices.php:139
msgid "Your database is being updated in the background."
msgstr "Votre base de données est mise à jour en arrière-plan."

#: includes/notices.php:141
msgid "Taking a while? Click here to run it now."
msgstr "Cela prend un certain temps ? Cliquez ici pour le faire marcher maintenant."

#: includes/notices.php:157
msgid "Add \"Booking Confirmation\" shortcode to your \"Booking Confirmed\" and \"Reservation Received\" pages to show more details about booking or payment.<br/>Click \"Update Pages\" to apply all changes automatically or skip this notice and add \"Booking Confirmation\" shortcode manually.<br/><b><em>This action will replace the whole content of the pages.</em></b>"
msgstr "Ajoutez le code court « Confirmation de réservation » à vos pages « Réservation confirmée » et « Réservation reçue » pour afficher plus de détails sur la réservation ou le paiement.<br/>Cliquez sur « Mettre à jour les pages » pour appliquer toutes les modifications automatiquement ou ignorez cet action et ajoutez le code court « Confirmation de réservation » manuellement.<br/><b><em>Cette action remplacera tout le contenu des pages.</em></b>"

#: includes/notices.php:159
msgid "Update Pages"
msgstr "Mise à jour des pages"

#: includes/notices.php:161
#: includes/wizard.php:36
msgid "Skip"
msgstr "Sauter"

#: includes/payments/gateways/bank-gateway.php:82
#: includes/payments/gateways/bank-gateway.php:91
msgid "Direct Bank Transfer"
msgstr "Virement Bancaire"

#: includes/payments/gateways/bank-gateway.php:92
msgid "Make your payment directly into our bank account. Please use your Booking ID as the payment reference."
msgstr "Effectuez votre paiement directement sur notre compte bancaire. Merci d'utiliser votre ID de réservation comme référence du paiement."

#: includes/payments/gateways/bank-gateway.php:118
msgid "Enable Auto-Abandonment"
msgstr ""

#: includes/payments/gateways/bank-gateway.php:119
msgid "Automatically abandon bookings and release reserved slots if payment is not received within a specified time period. You need to manually set the status of paid payments to Completed to avoid automatic abandonment."
msgstr ""

#: includes/payments/gateways/bank-gateway.php:128
msgid "Period of time in hours a user has to pay for a booking. Unpaid bookings become abandoned, and accommodations become available for others."
msgstr ""

#: includes/payments/gateways/beanstream-gateway.php:54
msgid "Beanstream/Bambora"
msgstr "Beanstream/Bambora"

#: includes/payments/gateways/beanstream-gateway.php:59
#: includes/payments/gateways/braintree-gateway.php:149
#: includes/payments/gateways/paypal-gateway.php:72
#: includes/payments/gateways/two-checkout-gateway.php:70
msgid "Use the card number %1$s with CVC %2$s and a valid expiration date to test a payment."
msgstr "Utilisez le numéro de carte %1$s avec CVV %2$s et une date d'expiration valide pour tester le paiement."

#: includes/payments/gateways/beanstream-gateway.php:66
msgid "Pay by Card (Beanstream)"
msgstr "Payer par carte (Beanstream)"

#: includes/payments/gateways/beanstream-gateway.php:67
msgid "Pay with your credit card via Beanstream."
msgstr "Payez avec votre carte de crédit via Beanstream."

#: includes/payments/gateways/beanstream-gateway.php:85
#: includes/payments/gateways/braintree-gateway.php:194
#: includes/payments/gateways/stripe-gateway.php:226
msgid "%1$s is enabled, but the <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "%1$s est activé, mais l'option <a href=\"%2$s\">Force Secure Checkout</a> est désactivée. Activez SSL et assurez-vous que votre serveur possède un certificat SSL valide. Autrement, %1$s ne fonctionnera que dans le mode test."

#: includes/payments/gateways/beanstream-gateway.php:87
#: includes/payments/gateways/braintree-gateway.php:196
#: includes/payments/gateways/stripe-gateway.php:228
msgid "The <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "Le stripe est activé, but the mais l'option<a href=\"%2$s\">Force Secure Checkout</a> est désactivée. Activez SSL et assurez-vous que votre serveur dispose d'un certificat SSL valide. Sinon, %1$s ne fonctionnera qu'en mode Test."

#: includes/payments/gateways/beanstream-gateway.php:90
msgid "Beanstream"
msgstr "Beanstream"

#: includes/payments/gateways/beanstream-gateway.php:103
#: includes/payments/gateways/braintree-gateway.php:212
msgid "Merchant ID"
msgstr "ID du commerçant"

#: includes/payments/gateways/beanstream-gateway.php:105
msgid "Your Merchant ID can be found in the top-right corner of the screen after logging in to the Beanstream Back Office"
msgstr "Votre ID de marchand se trouve dans le coin supérieur droit de l'écran après l'ouverture de session dans le Beanstream Back Office"

#: includes/payments/gateways/beanstream-gateway.php:112
msgid "Payments Passcode"
msgstr "Code de paiement"

#: includes/payments/gateways/beanstream-gateway.php:114
msgid "To generate the passcode, navigate to Administration > Account Settings > Order Settings in the sidebar, then scroll to Payment Gateway > Security/Authentication"
msgstr "Pour générer le code d'accès, accédez à Administration> Paramètres de compte> Paramètres de commande dans la barre latérale, puis faites défiler jusqu'à Passerelle de paiement> Sécurité / Authentification"

#: includes/payments/gateways/beanstream-gateway.php:163
msgid "Beanstream Payment Error: %s"
msgstr "Erreur de paiement Beanstream : %s"

#: includes/payments/gateways/beanstream-gateway.php:201
msgid "Payment single use token is required."
msgstr "Un jeton de paiement à usage unique est requis."

#: includes/payments/gateways/braintree-gateway.php:142
#: includes/payments/gateways/braintree-gateway.php:199
msgid "Braintree"
msgstr "Braintree"

#: includes/payments/gateways/braintree-gateway.php:155
#: includes/payments/gateways/stripe-gateway.php:86
msgid "Webhooks Destination URL: %s"
msgstr "URL de destination Webhooks : %s"

#: includes/payments/gateways/braintree-gateway.php:168
msgid "Pay by Card (Braintree)"
msgstr "Payer par carte (Braintree)"

#: includes/payments/gateways/braintree-gateway.php:169
msgid "Pay with your credit card via Braintree."
msgstr "Payez avec votre carte de crédit via Braintree."

#: includes/payments/gateways/braintree-gateway.php:189
msgid "Braintree gateway cannot be enabled due to some problems: %s"
msgstr "La passerelle Braintree ne peut pas être activée en raison de certains problèmes : %s"

#: includes/payments/gateways/braintree-gateway.php:214
msgid "In your Braintree account select Account > My User > View Authorizations."
msgstr "Dans votre compte Braintree, sélectionnez Compte> Mon utilisateur> Afficher les autorisations."

#: includes/payments/gateways/braintree-gateway.php:221
#: includes/payments/gateways/stripe-gateway.php:284
msgid "Public Key"
msgstr "Clé publique"

#: includes/payments/gateways/braintree-gateway.php:229
msgid "Private Key"
msgstr "Clé privée"

#: includes/payments/gateways/braintree-gateway.php:237
msgid "Merchant Account ID"
msgstr "ID du compte marchand"

#: includes/payments/gateways/braintree-gateway.php:238
msgid "In case the site currency differs from default currency in your Braintree account, you can set specific merchant account to avoid <a href=\"%s\">complications with currencty conversions</a>. Otherwise leave the field empty."
msgstr "Au cas où la devise du site diffère de la devise par défaut dans votre compte Braintree, vous pouvez définir un compte marchand particulier pour éviter des<a href=\"%s\"> complications avec les conversions courantes </a>. Sinon laissez le champ vide."

#: includes/payments/gateways/braintree-gateway.php:293
msgid "Braintree submitted for settlement (Transaction ID: %s)"
msgstr "Braintree soumis pour règlement (ID de transaction : %s)"

#: includes/payments/gateways/braintree-gateway.php:303
msgid "Braintree Payment Error: %s"
msgstr "Erreur de paiement Braintree : %s"

#: includes/payments/gateways/braintree-gateway.php:330
msgid "Payment method nonce is required."
msgstr "La méthode de paiement non nécessaire est requise."

#: includes/payments/gateways/braintree/webhook-listener.php:116
msgid "Payment dispute opened"
msgstr "Litige de paiement ouvert"

#: includes/payments/gateways/braintree/webhook-listener.php:121
msgid "Payment dispute lost"
msgstr "Litige de paiement perdu"

#: includes/payments/gateways/braintree/webhook-listener.php:126
msgid "Payment dispute won"
msgstr "Litige de paiement gagné"

#: includes/payments/gateways/braintree/webhook-listener.php:143
msgid "Payment refunded in Braintree"
msgstr "Paiement remboursé à Braintree"

#: includes/payments/gateways/braintree/webhook-listener.php:147
msgid "Braintree transaction voided"
msgstr "La transaction Braintree a été annulée"

#: includes/payments/gateways/cash-gateway.php:45
#: includes/payments/gateways/cash-gateway.php:53
msgid "Pay on Arrival"
msgstr "Paiement à l'arrivée"

#: includes/payments/gateways/cash-gateway.php:54
msgid "Pay with cash on arrival."
msgstr "Payez en liquide à l'arrivée."

#: includes/payments/gateways/gateway.php:301
msgid "%s is a required field."
msgstr "%s est un champ obligatoire."

#: includes/payments/gateways/gateway.php:314
msgid "%s is not a valid email address."
msgstr "%s n'est pas une adresse e-mail valide."

#. translators: %s is the payment gateway title.
#: includes/payments/gateways/gateway.php:472
msgid "Enable \"%s\""
msgstr "Activé \"%s\""

#: includes/payments/gateways/gateway.php:482
msgid "Test Mode"
msgstr "Mode d'essai"

#: includes/payments/gateways/gateway.php:483
msgid "Enable Sandbox Mode"
msgstr "Activer le mode Sandbox"

#: includes/payments/gateways/gateway.php:485
msgid "Sandbox can be used to test payments."
msgstr "Sandbox peut être utilisé pour tester les paiements."

#: includes/payments/gateways/gateway.php:496
msgid "Payment method title that the customer will see on your website."
msgstr "Titre du mode de paiement que le client verra sur votre site Web."

#: includes/payments/gateways/gateway.php:506
msgid "Payment method description that the customer will see on your website."
msgstr "Description de la méthode de paiement que le client verra sur votre site Web."

#: includes/payments/gateways/gateway.php:516
msgid "Instructions"
msgstr "Instructions"

#: includes/payments/gateways/gateway.php:518
msgid "Instructions for a customer on how to complete the payment."
msgstr "Instructions pour le client sur la façon d'effectuer le paiement."

#: includes/payments/gateways/gateway.php:543
msgid "Reservation #%d"
msgstr "Réservation #%d"

#: includes/payments/gateways/gateway.php:545
msgid "Accommodation(s) reservation"
msgstr "Réservation (s) d'hébergement"

#: includes/payments/gateways/manual-gateway.php:14
#: includes/payments/gateways/manual-gateway.php:19
msgid "Manual Payment"
msgstr "Paiement manuel"

#: includes/payments/gateways/paypal-gateway.php:67
#: includes/payments/gateways/paypal-gateway.php:80
msgid "PayPal"
msgstr "PayPal"

#: includes/payments/gateways/paypal-gateway.php:81
msgid "Pay via PayPal"
msgstr "Payer via PayPal"

#: includes/payments/gateways/paypal-gateway.php:117
msgid "Paypal Business Email"
msgstr "Courriel d'affaires Paypal"

#: includes/payments/gateways/paypal-gateway.php:125
msgid "Disable IPN Verification"
msgstr "Désactiver la vérification IPN"

#: includes/payments/gateways/paypal-gateway.php:127
msgid "Specify an IPN listener for a specific payment instead of the listeners specified in your PayPal Profile."
msgstr "Indiquez un écouteur IPN pour un paiement spécifique au lieu des auditeurs spécifiés dans votre profil PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:164
msgid "Payment %s via IPN."
msgstr "Paiement %s via IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:183
msgid "Payment failed due to invalid PayPal business email."
msgstr "Le paiement a échoué en raison d'un courrier électronique d'entreprise PayPal non valide."

#: includes/payments/gateways/paypal/ipn-listener.php:200
msgid "Payment failed due to invalid currency in PayPal IPN."
msgstr "Le paiement a échoué en raison d'une devise non valide dans PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:215
msgid "Payment failed due to invalid amount in PayPal IPN."
msgstr "Le paiement a échoué en raison d'un montant non valide dans PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:230
msgid "Payment failed due to invalid purchase key in PayPal IPN."
msgstr "Le paiement a échoué en raison d'une clé d'achat non valide dans PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:302
msgid "Payment made via eCheck and will clear automatically in 5-8 days."
msgstr "Paiement effectué via eCheck et s'effacera automatiquement dans 5-8 jours."

#: includes/payments/gateways/paypal/ipn-listener.php:307
msgid "Payment requires a confirmed customer address and must be accepted manually through PayPal."
msgstr "Le paiement exige une adresse confirmée et doit être accepté manuellement via PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:312
msgid "Payment must be accepted manually through PayPal due to international account regulations."
msgstr "Le paiement doit être accepté manuellement via PayPal en raison des règlements des comptes internationaux."

#: includes/payments/gateways/paypal/ipn-listener.php:317
msgid "Payment received in non-shop currency and must be accepted manually through PayPal."
msgstr "Les paiements reçus en monnaie non commerciale doivent être acceptés manuellement via PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:323
msgid "Payment is being reviewed by PayPal staff as high-risk or in possible violation of government regulations."
msgstr "Le paiement est examiné par le personnel de PayPal comme étant à haut risque ou en violation possible des règlements gouvernementaux."

#: includes/payments/gateways/paypal/ipn-listener.php:328
msgid "Payment was sent to unconfirmed or non-registered email address."
msgstr "Le paiement a été envoyé à une adresse électronique non confirmée ou non enregistrée."

#: includes/payments/gateways/paypal/ipn-listener.php:333
msgid "PayPal account must be upgraded before this payment can be accepted."
msgstr "Le compte PayPal doit être mis à niveau avant que ce paiement ne soit accepté."

#: includes/payments/gateways/paypal/ipn-listener.php:338
msgid "PayPal account is not verified. Verify account in order to accept this payment."
msgstr "Le compte PayPal n'est pas vérifié. Vérifiez le compte afin d'accepter ce paiement."

#: includes/payments/gateways/paypal/ipn-listener.php:343
msgid "Payment is pending for unknown reasons. Contact PayPal support for assistance."
msgstr "Le paiement est en attente pour des raisons inconnues. Contactez le support PayPal pour obtenir de l'aide."

#: includes/payments/gateways/paypal/ipn-listener.php:363
msgid "Partial PayPal refund processed: %s"
msgstr "Paiement PayPal partiel traité : %s"

#: includes/payments/gateways/paypal/ipn-listener.php:370
msgid "PayPal Payment #%s Refunded for reason: %s"
msgstr "Paiement PayPal #%s Remboursé pour raison : %s"

#: includes/payments/gateways/paypal/ipn-listener.php:374
msgid "PayPal Refund Transaction ID: %s"
msgstr "ID de transaction de remboursement PayPal : %s"

#: includes/payments/gateways/stripe-gateway.php:149
#: includes/payments/gateways/stripe-gateway.php:231
msgid "Stripe"
msgstr "Stripe"

#: includes/payments/gateways/stripe-gateway.php:186
msgid "Use the card number %1$s with CVC %2$s, a valid expiration date and random 5-digit ZIP-code to test a payment."
msgstr "Utilisez le numéro de carte %1$s avec le CVC %2$s, une date d'expiration valide et un code postal aléatoire à 5 chiffres pour tester un paiement."

#: includes/payments/gateways/stripe-gateway.php:202
msgid "Pay by Card (Stripe)"
msgstr "Payer par carte (Stripe)"

#: includes/payments/gateways/stripe-gateway.php:203
msgid "Pay with your credit card via Stripe."
msgstr "Payez avec votre carte de crédit via Stripe."

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:240
#: includes/payments/gateways/stripe-gateway.php:259
#: includes/payments/gateways/stripe-gateway.php:688
msgid "Bancontact"
msgstr "Bancontact"

#: includes/payments/gateways/stripe-gateway.php:241
#: includes/payments/gateways/stripe-gateway.php:260
#: includes/payments/gateways/stripe-gateway.php:689
msgid "iDEAL"
msgstr "iDEAL"

#: includes/payments/gateways/stripe-gateway.php:242
#: includes/payments/gateways/stripe-gateway.php:261
#: includes/payments/gateways/stripe-gateway.php:690
msgid "Giropay"
msgstr "Giropay"

#: includes/payments/gateways/stripe-gateway.php:243
#: includes/payments/gateways/stripe-gateway.php:262
#: includes/payments/gateways/stripe-gateway.php:691
msgid "SEPA Direct Debit"
msgstr "Prélèvement SEPA"

#. translators: %1$s - name of payment method, %2$s - currency codes
#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:244
#: includes/payments/gateways/stripe-gateway.php:269
#: includes/payments/gateways/stripe-gateway.php:276
#: includes/payments/gateways/stripe-gateway.php:692
msgid "Klarna"
msgstr ""

#. translators: %s - currency codes
#: includes/payments/gateways/stripe-gateway.php:252
msgid "The %s currency is selected in the main settings."
msgstr ""

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:258
msgid "%1$s support the following currencies: %2$s."
msgstr ""

#. translators: %1$s - name of payment method, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:268
msgid "%1$s supports: %2$s."
msgstr ""

#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:275
msgid "%s special restrictions."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:293
msgid "Secret Key"
msgstr "Clé secrète"

#: includes/payments/gateways/stripe-gateway.php:301
msgid "Webhook Secret"
msgstr "Secret du Webhook"

#: includes/payments/gateways/stripe-gateway.php:310
msgid "Payment Methods"
msgstr "Méthodes de Paiement"

#: includes/payments/gateways/stripe-gateway.php:311
msgid "Card Payments"
msgstr "Cartes de Paiement"

#: includes/payments/gateways/stripe-gateway.php:322
msgid "Checkout Locale"
msgstr "Page de paiement"

#: includes/payments/gateways/stripe-gateway.php:325
msgid "Display Checkout in the user's preferred language, if available."
msgstr "Affichez Checkout dans la langue préférée de l'utilisateur, si disponible."

#: includes/payments/gateways/stripe-gateway.php:395
msgid "The payment method is not selected."
msgstr "La méthode de paiement n'est pas sélectionnée."

#: includes/payments/gateways/stripe-gateway.php:401
msgid "PaymentIntent ID is not set."
msgstr ""

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:430
msgid "Payment for PaymentIntent %s succeeded."
msgstr "Payement du Payment Intent %s réussi."

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:436
msgid "Payment for PaymentIntent %s is processing."
msgstr "Paiement du Payment Intent %s en cours de traitement."

#. translators: %1$s - Stripe PaymentIntent ID, %2$s - Stripe error message text
#: includes/payments/gateways/stripe-gateway.php:457
msgid "Failed to process PaymentIntent %1$s. %2$s"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:474
msgid "Can't charge the payment again: payment's flow already completed."
msgstr "Impossible de débiter à nouveau le paiement : le flux de paiement est déjà réalisé."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:496
msgid "Charge %s succeeded."
msgstr "Facturation %s réussie."

#. translators: %1$s - Stripe Charge ID; %2$s - payment price
#: includes/payments/gateways/stripe-gateway.php:503
msgid "Charge %1$s for %2$s created."
msgstr "Facture %1$s pour %2$s créés."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:508
msgid "Charge %s failed."
msgstr "Échec %s de la facturation."

#: includes/payments/gateways/stripe-gateway.php:515
msgid "Charge error. %s"
msgstr "Erreur de facturation. %s"

#: includes/payments/gateways/stripe-gateway.php:529
msgid "Argentinean"
msgstr "Argentin"

#: includes/payments/gateways/stripe-gateway.php:530
msgid "Simplified Chinese"
msgstr "Chinois simplifié"

#: includes/payments/gateways/stripe-gateway.php:531
msgid "Danish"
msgstr "Danois"

#: includes/payments/gateways/stripe-gateway.php:532
msgid "Dutch"
msgstr "Hollandais"

#: includes/payments/gateways/stripe-gateway.php:533
msgid "English"
msgstr "Anglais"

#: includes/payments/gateways/stripe-gateway.php:534
msgid "Finnish"
msgstr "Finnois"

#: includes/payments/gateways/stripe-gateway.php:535
msgid "French"
msgstr "Français"

#: includes/payments/gateways/stripe-gateway.php:536
msgid "German"
msgstr "Allemand"

#: includes/payments/gateways/stripe-gateway.php:537
msgid "Italian"
msgstr "Italien"

#: includes/payments/gateways/stripe-gateway.php:538
msgid "Japanese"
msgstr "Japonais"

#: includes/payments/gateways/stripe-gateway.php:539
msgid "Norwegian"
msgstr "Norvégien"

#: includes/payments/gateways/stripe-gateway.php:540
msgid "Polish"
msgstr "Polonais"

#: includes/payments/gateways/stripe-gateway.php:541
msgid "Russian"
msgstr "Russe"

#: includes/payments/gateways/stripe-gateway.php:542
msgid "Spanish"
msgstr "Espanol"

#: includes/payments/gateways/stripe-gateway.php:543
msgid "Swedish"
msgstr "Suédois"

#: includes/payments/gateways/stripe-gateway.php:571
msgid "PaymentIntent ID is missing."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:687
msgid "Card"
msgstr "Carte"

#: includes/payments/gateways/stripe-gateway.php:694
msgid "Credit or debit card"
msgstr "Carte de crédit ou de débit"

#: includes/payments/gateways/stripe-gateway.php:695
msgid "IBAN"
msgstr "IBAN"

#: includes/payments/gateways/stripe-gateway.php:696
msgid "Select iDEAL Bank"
msgstr "Sélectionnez iDEAL Bank"

#: includes/payments/gateways/stripe-gateway.php:698
msgid "You will be redirected to a secure page to complete the payment."
msgstr "Vous allez être redirigé vers une page sécurisée pour terminer le paiement."

#: includes/payments/gateways/stripe-gateway.php:699
msgid "By providing your IBAN and confirming this payment, you are authorizing this merchant and Stripe, our payment service provider, to send instructions to your bank to debit your account and your bank to debit your account in accordance with those instructions. You are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited."
msgstr "En fournissant votre IBAN et en confirmant ce paiement, vous autorisez ce marchand et Stripe, son prestataire de services de paiement, à envoyer des instructions à votre banque pour débiter votre compte et autorisez votre banque à débiter votre compte conformément à ces instructions. Vous avez droit à un remboursement de votre banque selon les termes et conditions de votre accord avec votre banque. Un remboursement doit être réclamé dans les 8 semaines à compter de la date à laquelle votre compte a été débité."

#. translators: %s - payment method type code like: card
#: includes/payments/gateways/stripe/stripe-api.php:172
msgid "Could not create PaymentIntent for a not allowed payment type: %s"
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:163
msgid "Webhook received. Payment %s was cancelled by the customer."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:174
msgid "Webhook received. Payment %s failed and couldn't be processed."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:200
msgid "Webhook received. Payment %s was successfully processed."
msgstr ""

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:215
msgid "Webhook received. Charge %s succeeded."
msgstr "Webhook reçu. Le prélèvement %s a réussi."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:227
msgid "Webhook received. Charge %s failed."
msgstr "Webhook reçu. Le prélèvement %s a échoué."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:238
msgid "Webhook received. The source %s is chargeable."
msgstr "Webhook reçu. La source %s est payante."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:252
msgid "Webhook received. Payment source %s was cancelled by customer."
msgstr "Webhook reçu. La source de paiement %s a été annulée par le client."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:263
msgid "Webhook received. Payment source %s failed and couldn't be processed."
msgstr "Webhook reçu. La source de paiement %s a été rejetée et n'a pas pu être traitée."

#: includes/payments/gateways/test-gateway.php:46
#: includes/payments/gateways/test-gateway.php:52
msgid "Test Payment"
msgstr "Paiement d'essai"

#: includes/payments/gateways/two-checkout-gateway.php:65
#: includes/payments/gateways/two-checkout-gateway.php:101
msgid "2Checkout"
msgstr "2Checkout"

#: includes/payments/gateways/two-checkout-gateway.php:88
msgid "To setup the callback process for 2Checkout to automatically mark payments completed, you will need to"
msgstr "Pour configurer le processus de rappel pour 2Checkout pour marquer automatiquement les paiements effectués, vous devrez"

#: includes/payments/gateways/two-checkout-gateway.php:90
msgid "Login to your 2Checkout account and click the Notifications tab"
msgstr "Connectez-vous à votre compte 2Checkout et cliquez sur l'onglet Notifications"

#: includes/payments/gateways/two-checkout-gateway.php:91
msgid "Click Enable All Notifications"
msgstr "Cliquez sur Activer toutes les notifications"

#: includes/payments/gateways/two-checkout-gateway.php:92
msgid "In the Global URL field, enter the url %s"
msgstr "Dans le champ Global URL, entrez l'url %s"

#: includes/payments/gateways/two-checkout-gateway.php:93
msgid "Click Apply"
msgstr "Cliquez sur Appliquer"

#: includes/payments/gateways/two-checkout-gateway.php:189
msgid "Account Number"
msgstr "Numéro de compte"

#: includes/payments/gateways/two-checkout-gateway.php:197
msgid "Secret Word"
msgstr "Mot secret"

#: includes/payments/gateways/two-checkout/ins-listener.php:68
msgid "2Checkout \"Order Created\" notification received."
msgstr "2Checkout reçu la notification \"Commande créée\"."

#: includes/payments/gateways/two-checkout/ins-listener.php:73
msgid "Payment refunded in 2Checkout"
msgstr "Paiement remboursé en 2Checkout"

#: includes/payments/gateways/two-checkout/ins-listener.php:80
msgid "2Checkout fraud review passed"
msgstr "2Checkout vérification de fraude passée"

#: includes/payments/gateways/two-checkout/ins-listener.php:83
msgid "2Checkout fraud review failed"
msgstr "2Checkout vérification de fraude échouée"

#: includes/payments/gateways/two-checkout/ins-listener.php:86
msgid "2Checkout fraud review in progress"
msgstr "2Checkout vérifications de fraude en cours"

#: includes/post-types/attributes-cpt.php:55
msgid "Attribute"
msgstr "Caractéristique"

#: includes/post-types/attributes-cpt.php:56
msgctxt "Add New Attribute"
msgid "Add New"
msgstr "Ajouter un nouveau"

#: includes/post-types/attributes-cpt.php:57
msgid "Add New Attribute"
msgstr "Ajouter un nouvel attribut"

#: includes/post-types/attributes-cpt.php:58
msgid "Edit Attribute"
msgstr "Modifier l'attribut"

#: includes/post-types/attributes-cpt.php:59
msgid "New Attribute"
msgstr "Nouvel attribut"

#: includes/post-types/attributes-cpt.php:60
msgid "View Attribute"
msgstr "Afficher l'attribut"

#: includes/post-types/attributes-cpt.php:62
msgid "Search Attribute"
msgstr "Rechercher l'attribut"

#: includes/post-types/attributes-cpt.php:63
msgid "No Attributes found"
msgstr "Aucun attribut trouvé"

#: includes/post-types/attributes-cpt.php:64
msgid "No Attributes found in Trash"
msgstr "Aucun attribut trouvé dans Corbeille"

#: includes/post-types/attributes-cpt.php:66
msgid "Insert into attribute description"
msgstr "Insérer dans la description de l'attribut"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:157
msgid "Search %s"
msgstr "Rechercher %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:159
msgid "All %s"
msgstr "Tous %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:161
msgid "Edit %s"
msgstr "Éditer %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:163
msgid "Update %s"
msgstr "Mise à jour %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:165
msgid "Add new %s"
msgstr "Ajouter nouveau %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:167
msgid "New %s"
msgstr "Nouveau %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:169
msgid "No &quot;%s&quot; found"
msgstr "&quot;%s&quot; non trouvé"

#: includes/post-types/attributes-cpt.php:302
msgid "Name (numeric)"
msgstr "Nom (numérique)"

#: includes/post-types/attributes-cpt.php:303
msgid "Term ID"
msgstr "Terme ID"

#: includes/post-types/attributes-cpt.php:314
msgid "Enable Archives"
msgstr "Activer les archives"

#: includes/post-types/attributes-cpt.php:315
msgid "Link the attribute to an archive page with all accommodation types that have this attribute."
msgstr "Lier l'attribut à une page d'archive avec tous les types d'hébergement qui ont cet attribut."

#: includes/post-types/attributes-cpt.php:324
msgid "Visible in Details"
msgstr "Visible dans Détails"

#: includes/post-types/attributes-cpt.php:325
msgid "Display the attribute in details section of an accommodation type."
msgstr "Affiche l'attribut dans la section Détails d'un type de logement."

#: includes/post-types/attributes-cpt.php:334
msgid "Default Sort Order"
msgstr "Ordre de tri par défaut"

#: includes/post-types/attributes-cpt.php:344
msgid "Default Text"
msgstr "Texte par défaut"

#: includes/post-types/attributes-cpt.php:355
msgid "Select"
msgstr "Sélectionnez"

#: includes/post-types/booking-cpt.php:75
#: templates/edit-booking/edit-dates.php:24
msgid "Edit Dates"
msgstr "Modifier Dates"

#: includes/post-types/booking-cpt.php:215
msgid "Note"
msgstr "Note"

#: includes/post-types/booking-cpt.php:243
msgctxt "Add New Booking"
msgid "Add New Booking"
msgstr "Ajouter une nouvelle réservation"

#: includes/post-types/booking-cpt.php:247
#: templates/emails/admin-customer-cancelled-booking.php:16
#: templates/emails/admin-customer-confirmed-booking.php:16
#: templates/emails/admin-payment-confirmed-booking.php:16
#: templates/emails/admin-pending-booking.php:16
#: templates/emails/customer-approved-booking.php:24
#: templates/emails/customer-pending-booking.php:26
msgid "View Booking"
msgstr "Voir réservation"

#: includes/post-types/booking-cpt.php:248
msgid "Search Booking"
msgstr "Chercher Réservation"

#: includes/post-types/booking-cpt.php:249
msgid "No bookings found"
msgstr "Aucune réservation trouvée"

#: includes/post-types/booking-cpt.php:250
msgid "No bookings found in Trash"
msgstr "Aucune réservation trouvée dans la Corbeille"

#: includes/post-types/booking-cpt.php:251
msgid "All Bookings"
msgstr "Toutes les réservations"

#: includes/post-types/booking-cpt.php:252
msgid "Insert into booking description"
msgstr "Insérer dans la description de la réservation"

#: includes/post-types/booking-cpt.php:253
msgid "Uploaded to this booking"
msgstr "Téléchargé à cette réservation"

#: includes/post-types/booking-cpt/statuses.php:58
msgctxt "Booking status"
msgid "Pending User Confirmation"
msgstr "Confirmation d'utilisateur en attente"

#: includes/post-types/booking-cpt/statuses.php:63
msgid "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgid_plural "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgstr[0] "Confirmation d'utilisateur en attente <span class=\"count\">(%s)</span>"
msgstr[1] "Confirmation des utilisateurs en attente <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:69
msgctxt "Booking status"
msgid "Pending Payment"
msgstr "Paiement en attente"

#: includes/post-types/booking-cpt/statuses.php:74
msgid "Pending Payment <span class=\"count\">(%s)</span>"
msgid_plural "Pending Payment <span class=\"count\">(%s)</span>"
msgstr[0] "Paiement en attente <span class=\"count\">(%s)</span>"
msgstr[1] "Paiements en attente <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:80
msgctxt "Booking status"
msgid "Pending Admin"
msgstr "En attente d'administration"

#: includes/post-types/booking-cpt/statuses.php:85
msgid "Pending Admin <span class=\"count\">(%s)</span>"
msgid_plural "Pending Admin <span class=\"count\">(%s)</span>"
msgstr[0] "En attente d'administration <span class=\"count\">(%s)</span>"
msgstr[1] "En attente d'administration <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:91
#: includes/reports/data/report-earnings-by-dates-data.php:31
msgctxt "Booking status"
msgid "Abandoned"
msgstr "Abandonnée"

#: includes/post-types/booking-cpt/statuses.php:96
#: includes/post-types/payment-cpt/statuses.php:83
msgid "Abandoned <span class=\"count\">(%s)</span>"
msgid_plural "Abandoned <span class=\"count\">(%s)</span>"
msgstr[0] "Abandonnée <span class=\"count\">(%s)</span>"
msgstr[1] "Abandonnées <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:107
msgid "Confirmed <span class=\"count\">(%s)</span>"
msgid_plural "Confirmed <span class=\"count\">(%s)</span>"
msgstr[0] "Confirmée <span class=\"count\">(%s)</span>"
msgstr[1] "Confirmées <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:113
#: includes/reports/data/report-earnings-by-dates-data.php:30
msgctxt "Booking status"
msgid "Cancelled"
msgstr "Annulée"

#: includes/post-types/booking-cpt/statuses.php:118
#: includes/post-types/payment-cpt/statuses.php:116
msgid "Cancelled <span class=\"count\">(%s)</span>"
msgid_plural "Cancelled <span class=\"count\">(%s)</span>"
msgstr[0] "Annulée <span class=\"count\">(%s)</span>"
msgstr[1] "Annulées <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:180
#: includes/post-types/payment-cpt/statuses.php:213
msgid "Status changed from %s to %s."
msgstr "Le statut a changé de %s à %s."

#: includes/post-types/coupon-cpt.php:45
msgid "A brief description to remind you what this code is for."
msgstr ""

#: includes/post-types/coupon-cpt.php:52
msgid "Conditions"
msgstr ""

#: includes/post-types/coupon-cpt.php:60
msgid "Apply a coupon code to selected accommodations in a booking. Leave blank to apply to all accommodations."
msgstr ""

#: includes/post-types/coupon-cpt.php:82
msgid "Percentage discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:83
msgid "Fixed discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:84
msgid "Fixed discount on daily/nightly price"
msgstr ""

#: includes/post-types/coupon-cpt.php:94
#: includes/post-types/coupon-cpt.php:125
#: includes/post-types/coupon-cpt.php:170
msgid "Enter percent or fixed amount according to selected type."
msgstr "Entrez le pourcentage ou le montant fixe selon le type sélectionné."

#: includes/post-types/coupon-cpt.php:104
msgid "Service Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:137
msgid "Apply a coupon code to selected services in a booking. Leave blank to apply to all services."
msgstr ""

#: includes/post-types/coupon-cpt.php:149
msgid "Fee Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:180
msgid "Usage Restrictions"
msgstr ""

#: includes/post-types/coupon-cpt.php:195
msgid "Check-in After"
msgstr "Arrivée après"

#: includes/post-types/coupon-cpt.php:203
msgid "Check-out Before"
msgstr "Départ avant"

#: includes/post-types/coupon-cpt.php:211
msgid "Min days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:212
msgid "For early bird discount. The coupon code applies if a booking is made in a minimum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:222
msgid "Max days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:223
msgid "For last minute discount. The coupon code applies if a booking is made in a maximum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:233
msgid "Minimum Days"
msgstr "Nombre de jours minimum"

#: includes/post-types/coupon-cpt.php:243
msgid "Maximum Days"
msgstr "Nombre de jours maximum"

#: includes/post-types/coupon-cpt.php:253
msgid "Usage Limit"
msgstr "Limite d'utilisation"

#: includes/post-types/coupon-cpt.php:263
msgid "Usage Count"
msgstr "Nombre d'utilisations"

#: includes/post-types/coupon-cpt.php:290
msgctxt "Add New Coupon"
msgid "Add New"
msgstr "Ajouter"

#: includes/post-types/coupon-cpt.php:291
msgid "Add New Coupon"
msgstr "Ajouter un nouveau coupon"

#: includes/post-types/coupon-cpt.php:292
msgid "Edit Coupon"
msgstr "Modifier le coupon"

#: includes/post-types/coupon-cpt.php:293
msgid "New Coupon"
msgstr "Nouveau coupon"

#: includes/post-types/coupon-cpt.php:294
msgid "View Coupon"
msgstr "Voir le coupon"

#: includes/post-types/coupon-cpt.php:295
msgid "Search Coupon"
msgstr "Rechercher un coupon"

#: includes/post-types/coupon-cpt.php:296
msgid "No coupons found"
msgstr "Aucun coupon trouvé"

#: includes/post-types/coupon-cpt.php:297
msgid "No coupons found in Trash"
msgstr "Aucun coupon trouvé dans la corbeille"

#: includes/post-types/coupon-cpt.php:298
msgid "All Coupons"
msgstr "Tous les coupons"

#: includes/post-types/payment-cpt.php:37
msgid "Payment History"
msgstr "Historique de paiement"

#: includes/post-types/payment-cpt.php:38
msgid "Payment"
msgstr "Paiement"

#: includes/post-types/payment-cpt.php:39
msgctxt "Add New Payment"
msgid "Add New"
msgstr "Ajouter un nouveau"

#: includes/post-types/payment-cpt.php:40
msgid "Add New Payment"
msgstr "Ajouter un nouveau paiement"

#: includes/post-types/payment-cpt.php:41
msgid "Edit Payment"
msgstr "Editer paiement"

#: includes/post-types/payment-cpt.php:42
msgid "New Payment"
msgstr "Nouveau paiement"

#: includes/post-types/payment-cpt.php:43
msgid "View Payment"
msgstr "Voir paiement"

#: includes/post-types/payment-cpt.php:44
msgid "Search Payment"
msgstr "Rechercher paiement"

#: includes/post-types/payment-cpt.php:45
msgid "No payments found"
msgstr "Aucun paiement trouvé"

#: includes/post-types/payment-cpt.php:46
msgid "No payments found in Trash"
msgstr "Aucun paiement trouvé dans la Corbeille"

#: includes/post-types/payment-cpt.php:47
msgid "Payments"
msgstr "Paiements"

#: includes/post-types/payment-cpt.php:48
msgid "Insert into payment description"
msgstr "Insérer dans la description de paiement"

#: includes/post-types/payment-cpt.php:49
msgid "Uploaded to this payment"
msgstr "Envoyé à ce paiement"

#: includes/post-types/payment-cpt.php:54
msgid "Payments."
msgstr "Paiements."

#: includes/post-types/payment-cpt.php:169
msgid "Gateway Mode"
msgstr "Mode de passerelle"

#: includes/post-types/payment-cpt.php:171
msgid "Sandbox"
msgstr "Mode bac à sable"

#: includes/post-types/payment-cpt.php:172
msgid "Live"
msgstr "En direct"

#: includes/post-types/payment-cpt.php:194
msgid "Fee"
msgstr "Frais"

#: includes/post-types/payment-cpt.php:215
msgid "Payment Type"
msgstr "Type de paiement"

#: includes/post-types/payment-cpt.php:240
msgid "Billing Info"
msgstr "Informations de facturation"

#: includes/post-types/payment-cpt.php:287
msgid "Address 1"
msgstr "Adresse 1"

#: includes/post-types/payment-cpt.php:295
msgid "Address 2"
msgstr "Adresse 2"

#: includes/post-types/payment-cpt.php:319
msgid "Postal Code (ZIP)"
msgstr "Code Postal (ZIP)"

#: includes/post-types/payment-cpt/statuses.php:45
msgctxt "Payment status"
msgid "Pending"
msgstr "En attente"

#: includes/post-types/payment-cpt/statuses.php:50
msgid "Pending <span class=\"count\">(%s)</span>"
msgid_plural "Pending <span class=\"count\">(%s)</span>"
msgstr[0] "En attente <span class=\"count\">(%s)</span>"
msgstr[1] "En attente <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:56
msgctxt "Payment status"
msgid "Completed"
msgstr "Terminé"

#: includes/post-types/payment-cpt/statuses.php:61
msgid "Completed <span class=\"count\">(%s)</span>"
msgid_plural "Completed <span class=\"count\">(%s)</span>"
msgstr[0] "Terminé <span class=\"count\">(%s)</span>"
msgstr[1] "Terminé <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:67
msgctxt "Payment status"
msgid "Failed"
msgstr "Échoué"

#: includes/post-types/payment-cpt/statuses.php:72
msgid "Failed <span class=\"count\">(%s)</span>"
msgid_plural "Failed <span class=\"count\">(%s)</span>"
msgstr[0] "Échoué <span class=\"count\">(%s)</span>"
msgstr[1] "Échoué <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:78
msgctxt "Payment status"
msgid "Abandoned"
msgstr "Abandonné"

#: includes/post-types/payment-cpt/statuses.php:89
msgctxt "Payment status"
msgid "On Hold"
msgstr "En attente"

#: includes/post-types/payment-cpt/statuses.php:94
msgid "On Hold <span class=\"count\">(%s)</span>"
msgid_plural "On Hold <span class=\"count\">(%s)</span>"
msgstr[0] "En attente <span class=\"count\">(%s)</span>"
msgstr[1] "En attente <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:100
msgctxt "Payment status"
msgid "Refunded"
msgstr "Remboursé"

#: includes/post-types/payment-cpt/statuses.php:105
msgid "Refunded <span class=\"count\">(%s)</span>"
msgid_plural "Refunded <span class=\"count\">(%s)</span>"
msgstr[0] "Remboursé <span class=\"count\">(%s)</span>"
msgstr[1] "Remboursés<span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:111
msgctxt "Payment status"
msgid "Cancelled"
msgstr "Annulée"

#: includes/post-types/payment-cpt/statuses.php:180
msgid "Payment (#%s) for this booking is on hold"
msgstr "Le paiement (#%s) pour cette réservation est en attente"

#: includes/post-types/rate-cpt.php:23
msgid "Rate Info"
msgstr "Informations sur les taux"

#: includes/post-types/rate-cpt.php:49
#: includes/post-types/season-cpt.php:99
msgid "Season"
msgstr "Saison"

#: includes/post-types/rate-cpt.php:72
msgid "Move price to top to set higher priority."
msgstr "Déplacer le prix vers le haut pour définir une priorité plus élevée."

#: includes/post-types/rate-cpt.php:84
msgid "Will be displayed on the checkout page."
msgstr "Sera affiché sur la page de commande."

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/post-types/rate-cpt.php:103
#: includes/post-types/rate-cpt.php:113
msgid "Rates"
msgstr "Tarifs"

#: includes/post-types/rate-cpt.php:105
msgctxt "Add New Rate"
msgid "Add New"
msgstr "Ajouter un nouveau"

#: includes/post-types/rate-cpt.php:106
msgid "Add New Rate"
msgstr "Ajouter un nouveau tarif"

#: includes/post-types/rate-cpt.php:107
msgid "Edit Rate"
msgstr "Editer Note"

#: includes/post-types/rate-cpt.php:108
msgid "New Rate"
msgstr "Nouvelle Note"

#: includes/post-types/rate-cpt.php:109
msgid "View Rate"
msgstr "Voir note"

#: includes/post-types/rate-cpt.php:110
msgid "Search Rate"
msgstr "Recherche Note"

#: includes/post-types/rate-cpt.php:111
msgid "No rates found"
msgstr "Aucun taux trouvé"

#: includes/post-types/rate-cpt.php:112
msgid "No rates found in Trash"
msgstr "Aucun taux trouvé dans Corbeille"

#: includes/post-types/rate-cpt.php:114
msgid "Insert into rate description"
msgstr "Insérer dans la description du tarif"

#: includes/post-types/rate-cpt.php:115
msgid "Uploaded to this rate"
msgstr "Téléchargé à ce taux"

#: includes/post-types/rate-cpt.php:120
msgid "This is where you can add new rates."
msgstr "C'est ici que vous pouvez ajouter de nouveaux tarifs."

#: includes/post-types/reserved-room-cpt.php:23
msgid "Reserved Accommodation"
msgstr "Logement réservé"

#: includes/post-types/room-cpt.php:33
msgctxt "Add New Accommodation"
msgid "Add New"
msgstr "Ajouter un nouveau"

#: includes/post-types/room-cpt.php:34
msgid "Add New Accommodation"
msgstr "Ajouter un nouveau logement"

#: includes/post-types/room-cpt.php:35
msgid "Edit Accommodation"
msgstr "Editer logement"

#: includes/post-types/room-cpt.php:36
msgid "New Accommodation"
msgstr "Nouveau logement"

#: includes/post-types/room-cpt.php:37
msgid "View Accommodation"
msgstr "Voir logement"

#: includes/post-types/room-cpt.php:38
msgid "Search Accommodation"
msgstr "Rechercher logement"

#: includes/post-types/room-cpt.php:39
#: templates/create-booking/results/rooms-found.php:21
#: templates/shortcodes/search-results/results-info.php:19
msgid "No accommodations found"
msgstr "Aucun logement trouvé"

#: includes/post-types/room-cpt.php:40
msgid "No accommodations found in Trash"
msgstr "Aucun logement trouvé dans la Corbeille"

#: includes/post-types/room-cpt.php:42
msgid "Insert into accommodation description"
msgstr "Insérer dans la description de logement"

#: includes/post-types/room-cpt.php:43
msgid "Uploaded to this accommodation"
msgstr "Ajouté à ce logement"

#: includes/post-types/room-cpt.php:48
msgid "This is where you can add new accommodations to your hotel."
msgstr "C'est là que vous pouvez ajouter de nouveaux types de logement à votre hôtel."

#: includes/post-types/room-cpt.php:106
msgid "Automatically block current accommodation when the selected ones are booked"
msgstr ""

#: includes/post-types/room-type-cpt.php:55
msgctxt "Add New Accommodation Type"
msgid "Add Accommodation Type"
msgstr "Ajouter un nouveau type de logement"

#: includes/post-types/room-type-cpt.php:56
msgid "Add New Accommodation Type"
msgstr "Ajouter un nouveau type de logement"

#: includes/post-types/room-type-cpt.php:57
msgid "Edit Accommodation Type"
msgstr "Editer le type de logement"

#: includes/post-types/room-type-cpt.php:58
msgid "New Accommodation Type"
msgstr "Nouveau type de logement"

#: includes/post-types/room-type-cpt.php:59
msgid "View Accommodation Type"
msgstr "Voir le type de logement"

#: includes/post-types/room-type-cpt.php:61
msgid "Search Accommodation Type"
msgstr "Rechercher un type de logement"

#: includes/post-types/room-type-cpt.php:62
msgid "No Accommodation types found"
msgstr "Aucun type de logement trouvé"

#: includes/post-types/room-type-cpt.php:63
msgid "No Accommodation types found in Trash"
msgstr "Aucun type de logement trouvé dans la Corbeille"

#: includes/post-types/room-type-cpt.php:65
msgid "Insert into accommodation type description"
msgstr "Insérer dans la description du type de logement"

#: includes/post-types/room-type-cpt.php:66
msgid "Uploaded to this accommodation type"
msgstr "Téléchargé à ce type de logement"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:83
msgctxt "slug"
msgid "accommodation"
msgstr "accommodation"

#: includes/post-types/room-type-cpt.php:108
msgid "Accommodation Categories"
msgstr "Catégories d'hébergement"

#: includes/post-types/room-type-cpt.php:109
msgid "Accommodation Category"
msgstr "Catégorie d'hébergement"

#: includes/post-types/room-type-cpt.php:110
msgid "Search Accommodation Categories"
msgstr "Rechercher les catégories d'hébergement"

#: includes/post-types/room-type-cpt.php:111
msgid "Popular Accommodation Categories"
msgstr "Catégories d'hébergement populaires"

#: includes/post-types/room-type-cpt.php:112
msgid "All Accommodation Categories"
msgstr "Toutes les catégories d'hébergement"

#: includes/post-types/room-type-cpt.php:113
msgid "Parent Accommodation Category"
msgstr "Catégorie d'hébergement parente"

#: includes/post-types/room-type-cpt.php:114
msgid "Parent Accommodation Category:"
msgstr "Catégorie d'hébergement parente :"

#: includes/post-types/room-type-cpt.php:115
msgid "Edit Accommodation Category"
msgstr "Modifier la catégorie d'hébergement"

#: includes/post-types/room-type-cpt.php:116
msgid "Update Accommodation Category"
msgstr "Mise à jour de la catégorie d'hébergement"

#: includes/post-types/room-type-cpt.php:117
msgid "Add New Accommodation Category"
msgstr "Ajouter une nouvelle catégorie d'hébergement"

#: includes/post-types/room-type-cpt.php:118
msgid "New Accommodation Category Name"
msgstr "Nom de la nouvelle catégorie d'hébergement"

#: includes/post-types/room-type-cpt.php:119
msgid "Separate categories with commas"
msgstr "Séparez les catégories par des virgules"

#: includes/post-types/room-type-cpt.php:120
msgid "Add or remove categories"
msgstr "Ajouter ou supprimer des catégories"

#: includes/post-types/room-type-cpt.php:121
msgid "Choose from the most used categories"
msgstr "Choisir parmi les catégories les plus utilisées"

#: includes/post-types/room-type-cpt.php:122
msgid "No categories found."
msgstr "Aucune catégorie trouvée."

#: includes/post-types/room-type-cpt.php:123
#: assets/blocks/blocks.js:816
msgid "Categories"
msgstr "Catégories"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:138
msgctxt "slug"
msgid "accommodation-category"
msgstr "accommodation-category"

#: includes/post-types/room-type-cpt.php:163
msgid "Accommodation Tags"
msgstr "Étiquettes d'hébergement"

#: includes/post-types/room-type-cpt.php:164
msgid "Accommodation Tag"
msgstr "Étiquette d'hébergement"

#: includes/post-types/room-type-cpt.php:165
msgid "Search Accommodation Tags"
msgstr "rechercher les étiquettes d'hébergement"

#: includes/post-types/room-type-cpt.php:166
msgid "Popular Accommodation Tags"
msgstr "Étiquettes d'hébergement populaires"

#: includes/post-types/room-type-cpt.php:167
msgid "All Accommodation Tags"
msgstr "Toutes les étiquettes d'hébergement"

#: includes/post-types/room-type-cpt.php:168
msgid "Parent Accommodation Tag"
msgstr "Étiquette d'hébergement parentes"

#: includes/post-types/room-type-cpt.php:169
msgid "Parent Accommodation Tag:"
msgstr "Étiquette d'hébergement parentes:"

#: includes/post-types/room-type-cpt.php:170
msgid "Edit Accommodation Tag"
msgstr "Modifier l'étiquette de l'hébergement"

#: includes/post-types/room-type-cpt.php:171
msgid "Update Accommodation Tag"
msgstr "Mettre à jour l'étiquette d'hébergement"

#: includes/post-types/room-type-cpt.php:172
msgid "Add New Accommodation Tag"
msgstr "Ajouter une nouvelle étiquette d'hébergement"

#: includes/post-types/room-type-cpt.php:173
msgid "New Accommodation Tag Name"
msgstr "Nouveau nom de l'étiquette d'hébergement"

#: includes/post-types/room-type-cpt.php:174
msgid "Separate tags with commas"
msgstr "Etiquettes séparées par des virgules"

#: includes/post-types/room-type-cpt.php:175
msgid "Add or remove tags"
msgstr "Ajouter ou supprimer des étiquettes"

#: includes/post-types/room-type-cpt.php:176
msgid "Choose from the most used tags"
msgstr "Choisissez parmi les étiquettes les plus utilisées"

#: includes/post-types/room-type-cpt.php:177
msgid "No tags found."
msgstr "Aucune étiquette trouvée."

#: includes/post-types/room-type-cpt.php:178
#: assets/blocks/blocks.js:828
msgid "Tags"
msgstr "Étiquette"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:192
msgctxt "slug"
msgid "accommodation-tag"
msgstr "accommodation-tag"

#: includes/post-types/room-type-cpt.php:217
#: includes/post-types/room-type-cpt.php:232
msgid "Amenities"
msgstr "Equipements"

#: includes/post-types/room-type-cpt.php:218
msgid "Amenity"
msgstr "Equipement"

#: includes/post-types/room-type-cpt.php:219
msgid "Search Amenities"
msgstr "Rechercher un équipement"

#: includes/post-types/room-type-cpt.php:220
msgid "Popular Amenities"
msgstr "Equipements populaires"

#: includes/post-types/room-type-cpt.php:221
msgid "All Amenities"
msgstr "Tous les équipements"

#: includes/post-types/room-type-cpt.php:222
msgid "Parent Amenity"
msgstr "Équipement parent"

#: includes/post-types/room-type-cpt.php:223
msgid "Parent Amenity:"
msgstr "Équipement parent :"

#: includes/post-types/room-type-cpt.php:224
msgid "Edit Amenity"
msgstr "Modifier l'équipement"

#: includes/post-types/room-type-cpt.php:225
msgid "Update Amenity"
msgstr "Mettre à jour l'équipement"

#: includes/post-types/room-type-cpt.php:226
msgid "Add New Amenity"
msgstr "Ajouter un nouvel équipement"

#: includes/post-types/room-type-cpt.php:227
msgid "New Amenity Name"
msgstr "Nom du nouvel équipement"

#: includes/post-types/room-type-cpt.php:228
msgid "Separate amenities with commas"
msgstr "Séparer les installations par des virgules"

#: includes/post-types/room-type-cpt.php:229
msgid "Add or remove amenities"
msgstr "Ajouter ou supprimer des équipements"

#: includes/post-types/room-type-cpt.php:230
msgid "Choose from the most used amenities"
msgstr "Choisir parmi les équipements les plus utilisés"

#: includes/post-types/room-type-cpt.php:231
msgid "No amenities found."
msgstr "Aucun équipement trouvé."

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:247
msgctxt "slug"
msgid "accommodation-facility"
msgstr "accommodation-facility"

#: includes/post-types/room-type-cpt.php:293
msgid "State the age or disable children in <a href=\"%s\">settings</a>."
msgstr "Fixez l'âge ou désactiver les enfants dans <a href=\"%s\">paramètres</a>."

#: includes/post-types/room-type-cpt.php:303
msgid "Leave this option empty to calculate total capacity automatically to meet the exact number of adults AND children set above. This is the default behavior. Configure this option to allow any variations of adults OR children set above at checkout so that in total it meets the limit of manually set \"Capacity\". For example, configuration \"adults:5\", \"children:4\", \"capacity:5\" means the property can accommodate up to 5 adults, up to 4 children, but up to 5 guests in total (not 9)."
msgstr "Laissez cette option vide pour calculer automatiquement la capacité totale pour atteindre le nombre exact d'adultes ET d'enfants défini ci-dessus. Ceci est le fonctionnement par défaut. Configurez cette option pour autoriser toutes les variations d'adultes OU d'enfants définies ci-dessus à la caisse afin qu'au total, elle respecte la limite de \"Capacité\" définie manuellement. Par exemple, la configuration \"adultes:5\", \"enfants:4\", \"capacité:5\" signifie que la propriété peut accueillir jusqu'à 5 adultes, jusqu'à 4 enfants, mais jusqu'à 5 personnes au total (et non pas 9)."

#: includes/post-types/room-type-cpt.php:313
msgid "Base Adults Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:314
#: includes/post-types/room-type-cpt.php:325
msgid "An optional starting value used when creating seasonal prices in the Rates menu."
msgstr ""

#: includes/post-types/room-type-cpt.php:324
msgid "Base Children Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:334
msgid "Other"
msgstr "Autre"

#: includes/post-types/room-type-cpt.php:340
msgid "Size, %s"
msgstr "Taille, %s"

#: includes/post-types/room-type-cpt.php:341
msgid "Leave blank to hide."
msgstr "Laissez vide pour masquer."

#: includes/post-types/room-type-cpt.php:355
msgid "City view, seaside, swimming pool etc."
msgstr "Vue sur la ville, mer, piscine, etc."

#: includes/post-types/room-type-cpt.php:366
msgid "Bed type"
msgstr "Type de lit"

#: includes/post-types/room-type-cpt.php:369
msgid "Set bed types list in <a href=\"%link%\" target=\"_blank\">settings</a>."
msgstr "Définir la liste des types de lits dans <a href=\"%link%\" target=\"_blank\">les paramètres</a>."

#: includes/post-types/room-type-cpt.php:379
msgid "Photo Gallery"
msgstr "Galerie de photos"

#: includes/post-types/room-type-cpt.php:390
#: includes/post-types/room-type-cpt.php:395
msgid "Available Services"
msgstr "Services disponibles"

#: includes/post-types/season-cpt.php:25
msgid "Season Info"
msgstr "Informations sur la saison"

#: includes/post-types/season-cpt.php:32
#: includes/reports/earnings-report.php:332
msgid "Start date"
msgstr "Date de début"

#: includes/post-types/season-cpt.php:45
#: includes/reports/earnings-report.php:344
msgid "End date"
msgstr "Date de clôture"

#: includes/post-types/season-cpt.php:55
msgid "Applied for days"
msgstr "Appliqué pendant des jours"

#: includes/post-types/season-cpt.php:59
msgid "Hold Ctrl / Cmd to select multiple."
msgstr "Maintenez la touche Ctrl / Cmd enfoncée pour sélectionner plusieurs éléments."

#: includes/post-types/season-cpt.php:68
msgid "Annual repeats begin on the Start date of the season, for one year from the current date."
msgstr ""

#: includes/post-types/season-cpt.php:70
msgid "Does not repeat"
msgstr "Ne se répète pas"

#: includes/post-types/season-cpt.php:81
msgid "Repeat until date"
msgstr ""

#: includes/post-types/season-cpt.php:100
msgctxt "Add New Season"
msgid "Add New"
msgstr "Ajouter une nouvelle"

#: includes/post-types/season-cpt.php:101
msgid "Add New Season"
msgstr "Ajouter une nouvelle saison"

#: includes/post-types/season-cpt.php:102
msgid "Edit Season"
msgstr "Editer une saison"

#: includes/post-types/season-cpt.php:103
msgid "New Season"
msgstr "Nouvelle saison"

#: includes/post-types/season-cpt.php:104
msgid "View Season"
msgstr "Voir une saison"

#: includes/post-types/season-cpt.php:105
msgid "Search Season"
msgstr "Recherche Saison"

#: includes/post-types/season-cpt.php:106
msgid "No seasons found"
msgstr "Aucune saison trouvée"

#: includes/post-types/season-cpt.php:107
msgid "No seasons found in Trash"
msgstr "Aucune saison trouvée dans la Corbeille"

#: includes/post-types/season-cpt.php:109
msgid "Insert into season description"
msgstr "Insérer dans la description de la saison"

#: includes/post-types/season-cpt.php:110
msgid "Uploaded to this season"
msgstr "Téléchargé à cette saison"

#: includes/post-types/season-cpt.php:115
msgid "This is where you can add new seasons."
msgstr "C'est là que vous pouvez ajouter de nouvelles saisons."

#: includes/post-types/service-cpt.php:92
#: includes/views/booking-view.php:206
msgid "Service"
msgstr "Service"

#: includes/post-types/service-cpt.php:93
msgctxt "Add New Service"
msgid "Add New"
msgstr "Ajouter un nouveau"

#: includes/post-types/service-cpt.php:94
msgid "Add New Service"
msgstr "Ajouter un nouveau service"

#: includes/post-types/service-cpt.php:95
msgid "Edit Service"
msgstr "Editer un service"

#: includes/post-types/service-cpt.php:96
msgid "New Service"
msgstr "Nouveau service"

#: includes/post-types/service-cpt.php:97
msgid "View Service"
msgstr "Voir un service"

#: includes/post-types/service-cpt.php:98
msgid "Search Service"
msgstr "Rechercher un service"

#: includes/post-types/service-cpt.php:99
msgid "No services found"
msgstr "Aucun service trouvé"

#: includes/post-types/service-cpt.php:100
msgid "No services found in Trash"
msgstr "Aucun service trouvé dans la Corbeille"

#: includes/post-types/service-cpt.php:102
msgid "Insert into service description"
msgstr "Insérer dans la description de service"

#: includes/post-types/service-cpt.php:103
msgid "Uploaded to this service"
msgstr "Téléchargé à ce service"

#. translators: do not translate
#: includes/post-types/service-cpt.php:120
msgctxt "slug"
msgid "services"
msgstr "services"

#: includes/post-types/service-cpt.php:156
msgid "How many times the customer will be charged."
msgstr "Combien de fois le client sera facturé."

#: includes/post-types/service-cpt.php:167
msgid "Minimum"
msgstr "Minimum"

#: includes/post-types/service-cpt.php:181
msgid "Maximum"
msgstr "Maximum"

#: includes/post-types/service-cpt.php:193
msgid "Empty means unlimited"
msgstr "Vide signifie illimité"

#: includes/reports/data/report-earnings-by-dates-data.php:29
msgctxt "Booking status"
msgid "Pending"
msgstr "En attente"

#: includes/reports/earnings-report.php:91
msgid "Total Sales"
msgstr "Total des Ventes"

#: includes/reports/earnings-report.php:94
msgid "Total Without Taxes"
msgstr "Total hors-taxe"

#: includes/reports/earnings-report.php:97
msgid "Total Fees"
msgstr "Total des frais"

#: includes/reports/earnings-report.php:100
msgid "Total Services"
msgstr "Total des services"

#: includes/reports/earnings-report.php:103
msgid "Total Discounts"
msgstr "Total des réductions"

#: includes/reports/earnings-report.php:106
msgid "Total Bookings"
msgstr "Total des réservations"

#: includes/reports/earnings-report.php:289
#: includes/reports/report-filters.php:38
msgid "Revenue"
msgstr ""

#: includes/reports/earnings-report.php:352
#: includes/views/create-booking/checkout-view.php:56
#: includes/views/shortcodes/checkout-view.php:90
msgid "Apply"
msgstr "Appliquer"

#: includes/reports/earnings-report.php:496
msgid "From %s to %s"
msgstr "De %s à %s"

#: includes/reports/report-filters.php:61
msgid "Today"
msgstr "Aujourd’hui"

#: includes/reports/report-filters.php:64
msgid "Yesterday"
msgstr "Hier"

#: includes/reports/report-filters.php:67
msgid "This week"
msgstr "Cette semaine"

#: includes/reports/report-filters.php:70
msgid "Last week"
msgstr "Semaine dernière"

#: includes/reports/report-filters.php:73
msgid "Last 30 days"
msgstr "30 derniers jours"

#: includes/reports/report-filters.php:76
msgid "This month"
msgstr "Ce mois"

#: includes/reports/report-filters.php:79
msgid "Last month"
msgstr "Dernier mois"

#: includes/reports/report-filters.php:82
msgid "This quarter"
msgstr "Ce trimestre"

#: includes/reports/report-filters.php:85
msgid "Last quarter"
msgstr "Dernier trimestre"

#: includes/reports/report-filters.php:88
msgid "This year"
msgstr "Cette année"

#: includes/reports/report-filters.php:91
msgid "Last year"
msgstr "L'année dernière"

#. translators: %s - original Rate title
#: includes/repositories/rate-repository.php:195
msgid "%s - copy"
msgstr "%s - copier"

#: includes/script-managers/admin-script-manager.php:92
msgid "Accommodation Type Gallery"
msgstr "Galerie du type d'hébergement"

#: includes/script-managers/admin-script-manager.php:93
msgid "Add Gallery To Accommodation Type"
msgstr "Ajouter une galerie au type d'hébergement"

#: includes/script-managers/admin-script-manager.php:105
msgid "Display imported bookings."
msgstr "Afficher les réservations importées."

#: includes/script-managers/admin-script-manager.php:106
msgid "Processing..."
msgstr "Traitement..."

#: includes/script-managers/admin-script-manager.php:107
msgid "Cancelling..."
msgstr "Suppression..."

#: includes/script-managers/admin-script-manager.php:108
msgid "Want to delete?"
msgstr "Envie de supprimer ?"

#: includes/script-managers/public-script-manager.php:204
msgid "Not available"
msgstr "Indisponible"

#: includes/script-managers/public-script-manager.php:205
msgid "This is earlier than allowed by our advance reservation rules."
msgstr "C'est plus tôt que ne le permettent nos règles de réservation à l'avance."

#: includes/script-managers/public-script-manager.php:206
msgid "This is later than allowed by our advance reservation rules."
msgstr "C'est plus tard que ne le permettent nos règles de réservation à l'avance."

#: includes/script-managers/public-script-manager.php:210
msgid "Day in the past"
msgstr "Jour dans le passé"

#: includes/script-managers/public-script-manager.php:211
msgid "Check-in date"
msgstr "Date d'arrivée"

#: includes/script-managers/public-script-manager.php:212
msgid "Less than min days stay"
msgstr "Séjour de moins de min jours"

#: includes/script-managers/public-script-manager.php:213
msgid "More than max days stay"
msgstr "Séjour de plus de max jours"

#: includes/script-managers/public-script-manager.php:215
msgid "Later than max date for current check-in date"
msgstr "Plus tard que la date maximale pour la date d'arrivée courante"

#: includes/script-managers/public-script-manager.php:216
msgid "Rules:"
msgstr "Règles :"

#: includes/script-managers/public-script-manager.php:217
msgid "Tokenisation failed: %s"
msgstr "Défaillance de la tentative : %s"

#: includes/script-managers/public-script-manager.php:218
#: includes/script-managers/public-script-manager.php:219
msgid "%1$d &times; &ldquo;%2$s&rdquo; has been added to your reservation."
msgid_plural "%1$d &times; &ldquo;%2$s&rdquo; have been added to your reservation."
msgstr[0] "%1$d &times; &ldquo;%2$s&rdquo; a été ajouté à votre réservation."
msgstr[1] "%1$d &times; &ldquo;%2$s&rdquo; ont été ajoutés à votre réservation."

#: includes/script-managers/public-script-manager.php:220
#: includes/script-managers/public-script-manager.php:221
msgid "%s accommodation selected."
msgid_plural "%s accommodations selected."
msgstr[0] "%s logement sélectionné"
msgstr[1] "%s logements sélectionnés"

#: includes/script-managers/public-script-manager.php:222
msgid "Coupon code is empty."
msgstr "Le code de coupon est vide."

#: includes/script-managers/public-script-manager.php:225
msgid "Select dates"
msgstr ""

#: includes/settings/main-settings.php:26
msgid "Dark Blue"
msgstr "Bleu foncé"

#: includes/settings/main-settings.php:27
msgid "Dark Green"
msgstr "Vert foncé"

#: includes/settings/main-settings.php:28
msgid "Dark Red"
msgstr "Rouge foncé"

#: includes/settings/main-settings.php:29
msgid "Grayscale"
msgstr "Niveaux de gris"

#: includes/settings/main-settings.php:30
msgid "Light Blue"
msgstr "Bleu clair"

#: includes/settings/main-settings.php:31
msgid "Light Coral"
msgstr "Corail clair"

#: includes/settings/main-settings.php:32
msgid "Light Green"
msgstr "Vert clair"

#: includes/settings/main-settings.php:33
msgid "Light Yellow"
msgstr "Jaune clair"

#: includes/settings/main-settings.php:34
msgid "Minimal Blue"
msgstr "Bleu minimal"

#: includes/settings/main-settings.php:35
msgid "Minimal Orange"
msgstr "Orange minimale"

#: includes/settings/main-settings.php:36
msgid "Minimal"
msgstr "Minimal"

#: includes/settings/main-settings.php:38
msgid "Sky Blue"
msgstr "Bleu ciel"

#: includes/settings/main-settings.php:39
msgid "Slate Blue"
msgstr "Bleu ardoise"

#: includes/settings/main-settings.php:40
msgid "Turquoise"
msgstr "Turquoise"

#: includes/shortcodes/account-shortcode.php:212
#: includes/views/shortcodes/checkout-view.php:22
msgid "Invalid login or password."
msgstr ""

#: includes/shortcodes/account-shortcode.php:221
msgid "Account data updated."
msgstr "Données du compte actualisées."

#: includes/shortcodes/account-shortcode.php:227
msgid "Password changed."
msgstr "Le mot de passe a été modifié."

#: includes/shortcodes/account-shortcode.php:238
msgid "Dashboard"
msgstr "Tableau de bord"

#: includes/shortcodes/account-shortcode.php:240
msgid "Account"
msgstr "Compte"

#: includes/shortcodes/account-shortcode.php:241
msgid "Logout"
msgstr "Déconnexion"

#: includes/shortcodes/account-shortcode.php:279
msgid "Passwords do not match."
msgstr "Les Mots de passe ne correspondent pas."

#: includes/shortcodes/account-shortcode.php:282
msgid "Please, provide a valid current password."
msgstr "Merci d'entrer un mot de passe valide."

#: includes/shortcodes/account-shortcode.php:301
#: includes/views/shortcodes/checkout-view.php:54
msgid "Lost your password?"
msgstr ""

#: includes/shortcodes/booking-confirmation-shortcode.php:294
msgid "Payment:"
msgstr "Règlement :"

#: includes/shortcodes/booking-confirmation-shortcode.php:302
msgid "Payment Method:"
msgstr "Moyen de paiement :"

#: includes/shortcodes/booking-confirmation-shortcode.php:315
#: templates/shortcodes/booking-details/booking-details.php:42
msgid "Status:"
msgstr "Statut:"

#: includes/shortcodes/checkout-shortcode.php:196
msgid "Bookings are disabled in the settings."
msgstr ""

#: includes/shortcodes/checkout-shortcode/step-booking.php:151
msgid "Checkout data is not valid."
msgstr "Les données de paiement ne sont pas valides."

#: includes/shortcodes/checkout-shortcode/step-booking.php:449
msgid "Payment method is not valid."
msgstr "Le mode de paiement n'est pas valide."

#: includes/shortcodes/checkout-shortcode/step-checkout.php:193
msgid "Accommodation count is not valid."
msgstr "Le nombre de logements n'est pas valide."

#: includes/shortcodes/checkout-shortcode/step.php:110
msgid "Accommodation is already booked."
msgstr "L'hôtel est déjà réservé."

#: includes/shortcodes/checkout-shortcode/step.php:120
#: includes/shortcodes/checkout-shortcode/step.php:129
#: includes/shortcodes/checkout-shortcode/step.php:138
msgid "Reservation submitted"
msgstr "Réservation soumise"

#: includes/shortcodes/checkout-shortcode/step.php:121
msgid "Details of your reservation have just been sent to you in a confirmation email. Please check your inbox to complete booking."
msgstr "Les détails de votre réservation sont envoyés dans un e-mail de confirmation. Veuillez vérifier votre boîte de réception pour compléter la réservation."

#: includes/shortcodes/checkout-shortcode/step.php:130
#: includes/shortcodes/checkout-shortcode/step.php:139
msgid "We received your booking request. Once it is confirmed we will notify you via email."
msgstr "Nous avons reçu votre demande de réservation. Une fois confirmée, nous vous en informerons par courriel."

#: includes/shortcodes/room-rates-shortcode.php:104
#: template-functions.php:31
msgid "Choose dates to see relevant prices"
msgstr "Choisissez les dates pour voir les prix pertinents"

#: includes/shortcodes/search-results-shortcode.php:766
msgid "Select from available accommodations."
msgstr "Choisissez parmi les logements disponibles."

#: includes/shortcodes/search-results-shortcode.php:775
#: includes/shortcodes/search-results-shortcode.php:1013
#: template-functions.php:843
msgid "Confirm Reservation"
msgstr "Confirmer la réservation"

#: includes/shortcodes/search-results-shortcode.php:804
msgid "Recommended for %d adult"
msgid_plural "Recommended for %d adults"
msgstr[0] "Recommandé pour %d adulte"
msgstr[1] "Recommandé pour %d adultes"

#: includes/shortcodes/search-results-shortcode.php:806
msgid " and %d child"
msgid_plural " and %d children"
msgstr[0] " et %d enfant"
msgstr[1] " et %d enfants"

#: includes/shortcodes/search-results-shortcode.php:809
msgid "Recommended for %d guest"
msgid_plural "Recommended for %d guests"
msgstr[0] "Recommandé pour %d invité"
msgstr[1] "Recommandé pour %d invités"

#: includes/shortcodes/search-results-shortcode.php:891
msgid "Max occupancy:"
msgstr "Capacité maximale :"

#: includes/shortcodes/search-results-shortcode.php:910
msgid "%d child"
msgid_plural "%d children"
msgstr[0] "%d enfant"
msgstr[1] "%d enfants"

#: includes/shortcodes/search-results-shortcode.php:945
#: templates/create-booking/results/reserve-rooms.php:76
msgid "Reserve"
msgstr "Réserver"

#: includes/shortcodes/search-results-shortcode.php:1002
msgid "of %d accommodation available."
msgid_plural "of %d accommodations available."
msgstr[0] "de %d logement disponible."
msgstr[1] "de %d logements disponibles."

#. translators: Verb. To book an accommodation.
#: includes/shortcodes/search-results-shortcode.php:1012
#: template-functions.php:531
#: template-functions.php:544
msgid "Book"
msgstr "Réserver"

#: includes/users-and-roles/customers.php:290
#: includes/users-and-roles/customers.php:383
msgid "Please, provide a valid email."
msgstr "Veuillez fournir un email valide."

#: includes/users-and-roles/customers.php:318
msgid "Could not create a customer."
msgstr "Impossible de créer un utilisateur."

#: includes/users-and-roles/customers.php:379
msgid "Could not retrieve a customer."
msgstr "Impossible de supprimer un utilisateur."

#: includes/users-and-roles/customers.php:531
#: includes/users-and-roles/customers.php:577
msgid "Please, provide a valid Customer ID."
msgstr "Veuillez fournir un ID utilisateur valide."

#: includes/users-and-roles/customers.php:563
msgid "A database error."
msgstr "Erreur de base de données."

#: includes/users-and-roles/customers.php:583
msgid "No customer was deleted."
msgstr "Aucun utilisateur n'a été supprimé."

#: includes/users-and-roles/customers.php:694
#: includes/views/shortcodes/checkout-view.php:31
msgid "An account with this email already exists. Please, log in."
msgstr "Un compte associé à cette adresse e-mail existe déjà, veuillez vous connecter."

#: includes/users-and-roles/roles.php:34
msgid "Hotel Manager"
msgstr "Gérant de l'hôtel"

#: includes/users-and-roles/roles.php:41
msgid "Hotel Worker"
msgstr "Employé d'hôtel"

#: includes/users-and-roles/roles.php:48
msgid "Hotel Customer"
msgstr "Client de l'hôtel"

#: includes/users-and-roles/user.php:54
msgid "Please provide a valid email address."
msgstr "Veuillez fournir une adresse électronique valide."

#: includes/users-and-roles/user.php:69
msgid "Please enter a valid account username."
msgstr "Veuillez saisir un nom d'utilisateur valide pour votre compte."

#: includes/users-and-roles/user.php:73
msgid "An account is already registered with that username. Please choose another."
msgstr "Un compte a déjà été enregistré avec ce nom d'utilisateur. Veuillez en choisir un autre."

#: includes/users-and-roles/user.php:81
msgid "Please enter an account password."
msgstr "Veuillez saisir le mot de passe de votre compte."

#: includes/utils/date-utils.php:145
msgid "Sunday"
msgstr "Dimanche"

#: includes/utils/date-utils.php:146
msgid "Monday"
msgstr "Lundi"

#: includes/utils/date-utils.php:147
msgid "Tuesday"
msgstr "Mardi"

#: includes/utils/date-utils.php:148
msgid "Wednesday"
msgstr "Mercredi"

#: includes/utils/date-utils.php:149
msgid "Thursday"
msgstr "Jeudi"

#: includes/utils/date-utils.php:150
msgid "Friday"
msgstr "Vendredi"

#: includes/utils/date-utils.php:151
msgid "Saturday"
msgstr "Samedi"

#: includes/utils/parse-utils.php:135
msgid "Check-out date cannot be earlier than check-in date."
msgstr "La date de départ ne peut être antérieure à la date d'arrivée."

#: includes/utils/parse-utils.php:159
msgid "Adults number is not valid"
msgstr "Le nombre d'adultes est invalide"

#: includes/utils/parse-utils.php:183
msgid "Children number is not valid"
msgstr "Le nombre d'enfants est invalide"

#: includes/utils/taxes-and-fees-utils.php:27
msgctxt "Text about taxes and fees below the price."
msgid " (+taxes and fees)"
msgstr " (+taxes et frais)"

#. translators: %s is a tax value
#: includes/utils/taxes-and-fees-utils.php:56
msgctxt "Text about taxes and fees below the price."
msgid " (+%s taxes and fees)"
msgstr " (+%s taxes et frais)"

#: includes/utils/taxes-and-fees-utils.php:84
msgctxt "Text about taxes and fees below the price."
msgid " (includes taxes and fees)"
msgstr " (taxes et frais inclus)"

#: includes/views/booking-view.php:79
msgctxt "Accommodation type in price breakdown table. Example: #1 Double Room"
msgid "#%d %s"
msgstr "#%d %s"

#: includes/views/booking-view.php:82
msgid "Expand"
msgstr "Développer"

#: includes/views/booking-view.php:91
#: includes/views/edit-booking/checkout-view.php:209
msgid "Rate: %s"
msgstr "Note : %s"

#: includes/views/booking-view.php:125
msgid "Dates"
msgstr "Dates"

#: includes/views/booking-view.php:207
#: includes/views/loop-room-type-view.php:39
#: includes/views/single-room-type-view.php:131
#: includes/widgets/rooms-widget.php:197
#: assets/blocks/blocks.js:484
#: assets/blocks/blocks.js:734
#: assets/blocks/blocks.js:1263
msgid "Details"
msgstr "Détails"

#: includes/views/booking-view.php:380
msgid "Subtotal"
msgstr "Sous-total"

#: includes/views/booking-view.php:393
msgid "Coupon: %s"
msgstr "Coupon : %s"

#: includes/views/booking-view.php:412
msgid "Subtotal (excl. taxes)"
msgstr "Sous-total (hors taxes)"

#: includes/views/booking-view.php:422
msgid "Taxes"
msgstr "Taxes"

#: includes/views/create-booking/checkout-view.php:55
#: includes/views/shortcodes/checkout-view.php:89
msgid "Coupon Code:"
msgstr "Code promo :"

#: includes/views/edit-booking/checkout-view.php:25
msgid "New Booking Details"
msgstr "Détails de la nouvelle réservation"

#: includes/views/edit-booking/checkout-view.php:43
msgid "Original Booking Details"
msgstr "Détails de la réservation d'origine"

#: includes/views/edit-booking/checkout-view.php:154
#: includes/views/shortcodes/checkout-view.php:269
#: template-functions.php:794
#: templates/create-booking/search/search-form.php:111
#: templates/shortcodes/search/search-form.php:105
msgid "Children %s"
msgstr "Enfants %s "

#: includes/views/edit-booking/checkout-view.php:232
#: templates/emails/reserved-room-details.php:30
msgid "Additional Services"
msgstr "Services supplémentaires"

#: includes/views/edit-booking/checkout-view.php:249
#: includes/views/reserved-room-view.php:26
#: template-functions.php:937
msgid "x %d guest"
msgid_plural "x %d guests"
msgstr[0] "x %d invité(e)"
msgstr[1] "x %d invité(e)s"

#: includes/views/edit-booking/checkout-view.php:253
#: includes/views/reserved-room-view.php:31
#: template-functions.php:940
msgid "x %d time"
msgid_plural "x %d times"
msgstr[0] "x %d fois"
msgstr[1] "x %d fois"

#: includes/views/global-view.php:53
msgid "Accommodation pagination"
msgstr "Mise en page des logements"

#: includes/views/global-view.php:56
msgid "Services pagination"
msgstr "Mise en page des services"

#: includes/views/loop-room-type-view.php:55
#: includes/views/single-room-type-view.php:147
#: templates/widgets/rooms/room-content.php:99
msgid "Categories:"
msgstr "Catégories :"

#: includes/views/loop-room-type-view.php:67
#: includes/views/single-room-type-view.php:159
#: templates/widgets/rooms/room-content.php:129
msgid "Amenities:"
msgstr "Équipements :"

#: includes/views/loop-room-type-view.php:97
#: includes/views/loop-room-type-view.php:115
#: includes/views/single-room-type-view.php:189
#: includes/views/single-room-type-view.php:207
#: templates/widgets/rooms/room-content.php:67
#: templates/widgets/rooms/room-content.php:79
#: templates/widgets/search-availability/search-form.php:80
msgid "Guests:"
msgstr "Invités:"

#: includes/views/loop-room-type-view.php:140
#: includes/views/single-room-type-view.php:232
#: templates/widgets/rooms/room-content.php:175
msgid "Bed Type:"
msgstr "Type de lit :"

#: includes/views/loop-room-type-view.php:164
#: includes/views/single-room-type-view.php:256
#: templates/widgets/rooms/room-content.php:159
msgid "View:"
msgstr "Vue :"

#: includes/views/loop-room-type-view.php:184
#: includes/views/single-room-type-view.php:276
#: template-functions.php:839
#: templates/widgets/rooms/room-content.php:227
msgid "Prices start at:"
msgstr "Prix à partir de:"

#: includes/views/loop-service-view.php:46
msgid "Price:"
msgstr "Prix :"

#: includes/views/shortcodes/checkout-view.php:49
msgid "Returning customer?"
msgstr "Un ancien client ?"

#: includes/views/shortcodes/checkout-view.php:50
msgid "Click here to log in"
msgstr "Cliquez ici pour vous connecter"

#. translators: 1 - username;
#: includes/views/shortcodes/checkout-view.php:70
#: templates/account/dashboard.php:29
msgid "Hello %1$s (not %1$s? <a href=\"%2$s\">Log out</a>)."
msgstr "Bonjour %1$s (pas %1$s? <a href=\"%2$s\">Déconnexion</a>)."

#: includes/views/shortcodes/checkout-view.php:182
msgid "Accommodation #%d"
msgstr "Logement #%d"

#: includes/views/shortcodes/checkout-view.php:186
msgid "Accommodation Type:"
msgstr "Type d'hébergement :"

#: includes/views/shortcodes/checkout-view.php:324
msgid "Choose Rate"
msgstr "Choisir une note"

#: includes/views/shortcodes/checkout-view.php:397
msgid "Choose Additional Services"
msgstr "Choisir des services supplémentaires"

#: includes/views/shortcodes/checkout-view.php:429
msgid "for "
msgstr "pour "

#: includes/views/shortcodes/checkout-view.php:442
msgctxt "Example: Breakfast for X guest(s)"
msgid " guest(s)"
msgstr "invité(e)(s)"

#: includes/views/shortcodes/checkout-view.php:464
msgid "time(s)"
msgstr "fois"

#: includes/views/shortcodes/checkout-view.php:533
msgctxt "I've read and accept the terms & conditions"
msgid "terms & conditions"
msgstr "conditions générales de vente"

#: includes/views/shortcodes/checkout-view.php:536
msgctxt "I've read and accept the <tag>terms & conditions</tag>"
msgid "I've read and accept the %s"
msgstr "J'ai lu et j'accepte les %s"

#: includes/views/shortcodes/checkout-view.php:579
msgid "Your Information"
msgstr "Vos informations"

#: includes/views/shortcodes/checkout-view.php:582
#: template-functions.php:696
#: templates/widgets/search-availability/search-form.php:24
msgid "Required fields are followed by %s"
msgstr "Les champs obligatoires sont suivis de %s"

#: includes/views/shortcodes/checkout-view.php:758
msgid "Create an account"
msgstr "Créer un compte"

#: includes/views/shortcodes/checkout-view.php:775
msgid "Payment Method"
msgstr "Mode de paiement"

#: includes/views/shortcodes/checkout-view.php:780
msgid "Sorry, it seems that there are no available payment methods."
msgstr "Désolé, il semble qu'il n'existe pas de méthodes de paiement disponibles."

#: includes/views/shortcodes/checkout-view.php:874
#: templates/emails/admin-customer-cancelled-booking.php:32
#: templates/emails/admin-customer-confirmed-booking.php:32
#: templates/emails/admin-payment-confirmed-booking.php:39
#: templates/emails/admin-pending-booking.php:32
#: templates/emails/customer-approved-booking.php:28
#: templates/emails/customer-cancelled-booking.php:26
#: templates/emails/customer-confirmation-booking.php:32
#: templates/emails/customer-pending-booking.php:30
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:27
msgid "Total Price:"
msgstr "Prix total :"

#: includes/views/shortcodes/checkout-view.php:886
msgid "Deposit:"
msgstr "Acompte:"

#: includes/views/shortcodes/checkout-view.php:906
#: templates/shortcodes/booking-details/booking-details.php:25
#: templates/widgets/search-availability/search-form.php:35
msgid "Check-in:"
msgstr "Arrivée :"

#: includes/views/shortcodes/checkout-view.php:913
msgctxt "from 10:00 am"
msgid "from"
msgstr "à partir de"

#: includes/views/shortcodes/checkout-view.php:929
#: templates/shortcodes/booking-details/booking-details.php:29
#: templates/widgets/search-availability/search-form.php:54
msgid "Check-out:"
msgstr "Départ :"

#: includes/views/shortcodes/checkout-view.php:936
msgctxt "until 10:00 am"
msgid "until"
msgstr "jusqu'à"

#: includes/views/shortcodes/checkout-view.php:1012
#: templates/create-booking/checkout/checkout-form.php:42
msgid "Book Now"
msgstr "Réserver maintenant"

#: includes/views/single-room-type-view.php:127
msgid "Availability"
msgstr "Disponibilité"

#: includes/views/single-room-type-view.php:292
msgid "Reservation Form"
msgstr "Formulaire de réservation"

#: includes/widgets/rooms-widget.php:24
msgid "Display Accommodation Types"
msgstr "Afficher les types de logement"

#: includes/widgets/rooms-widget.php:169
#: includes/widgets/search-availability-widget.php:236
msgid "Title:"
msgstr "Titre :"

#: includes/widgets/rooms-widget.php:189
#: assets/blocks/blocks.js:448
#: assets/blocks/blocks.js:698
#: assets/blocks/blocks.js:1227
msgid "Featured Image"
msgstr "Image en vitrine"

#: includes/widgets/rooms-widget.php:193
#: assets/blocks/blocks.js:472
#: assets/blocks/blocks.js:722
#: assets/blocks/blocks.js:1251
msgid "Excerpt (short description)"
msgstr "Afficher l'extrait (description courte)"

#: includes/widgets/rooms-widget.php:205
#: assets/blocks/blocks.js:770
#: assets/blocks/blocks.js:1299
msgid "Book Button"
msgstr "bouton Livre"

#: includes/widgets/search-availability-widget.php:50
#: includes/wizard.php:84
msgid "Search Availability"
msgstr "Recherche Disponibilité"

#: includes/widgets/search-availability-widget.php:53
msgid "Search Availability Form"
msgstr "Formulaire de recherche de disponibilités"

#: includes/widgets/search-availability-widget.php:240
msgid "Check-in Date:"
msgstr "Date d'arrivée :"

#: includes/widgets/search-availability-widget.php:241
#: includes/widgets/search-availability-widget.php:246
msgctxt "Date format tip"
msgid "Preset date. Formatted as %s"
msgstr "Date. Formaté comme %s"

#: includes/widgets/search-availability-widget.php:244
msgid "Check-out Date:"
msgstr "Date de départ :"

#: includes/widgets/search-availability-widget.php:249
msgid "Preset Adults:"
msgstr "Adultes :"

#: includes/widgets/search-availability-widget.php:257
msgid "Preset Children:"
msgstr "Enfants :"

#: includes/widgets/search-availability-widget.php:265
msgid "Attributes:"
msgstr "Caractéristiques :"

#: includes/wizard.php:34
msgid "Booking Confirmation and Search Results pages are required to handle bookings. Press \"Install Pages\" button to create and set up these pages. Dismiss this notice if you already installed them."
msgstr "Les pages Checkout et Search Results sont nécessaires pour gérer les réservations. Appuyez sur le bouton \"Installer les pages\" pour créer et configurer ces pages. Ignorez cet avis si vous les avez déjà installés."

#: includes/wizard.php:35
msgid "Install Pages"
msgstr "Installer des pages"

#: includes/wizard.php:147
msgid "Booking Canceled"
msgstr "Réservation annulée"

#: includes/wizard.php:148
msgid "Your reservation is canceled."
msgstr "Votre réservation est annulée."

#: includes/wizard.php:183
msgid "Reservation Received"
msgstr "Réservation reçue."

#: includes/wizard.php:196
msgid "Transaction Failed"
msgstr "Échec de la transaction"

#: includes/wizard.php:197
msgid "Unfortunately, your transaction cannot be completed at this time. Please try again or contact us."
msgstr "Malheureusement, votre transaction ne peut être complétée pour le moment. Veuillez réessayer ou nous contacter."

#: plugin.php:1100
msgid "Prices start at: %s"
msgstr "Les prix commencent à : %s"

#: template-functions.php:563
msgid "View Details"
msgstr "Voir les détails"

#: template-functions.php:593
#: template-functions.php:652
msgid "Accommodation %s not found."
msgstr ""

#: template-functions.php:707
#: template-functions.php:716
#: templates/create-booking/search/search-form.php:43
#: templates/create-booking/search/search-form.php:63
#: templates/edit-booking/edit-dates.php:30
#: templates/edit-booking/edit-dates.php:39
#: templates/shortcodes/search/search-form.php:36
#: templates/shortcodes/search/search-form.php:56
#: templates/widgets/search-availability/search-form.php:36
#: templates/widgets/search-availability/search-form.php:55
msgctxt "Date format tip"
msgid "Formatted as %s"
msgstr "Formaté comme %s"

#: template-functions.php:831
msgid "Reserve %1$s of %2$s available accommodations."
msgstr "Réserve %1$s de %2$s de logements disponibles."

#: template-functions.php:835
msgid "%s is available for selected dates."
msgstr "%s est disponible pour les dates sélectionnées."

#: template-functions.php:849
#: templates/edit-booking/edit-dates.php:46
msgid "Check Availability"
msgstr "Vérifier la disponibilité"

#: template-functions.php:910
msgid "Rate:"
msgstr "Tarif :"

#: template-functions.php:930
msgid "Services:"
msgstr "Services :"

#: template-functions.php:953
msgid "Guest:"
msgstr "Invités:"

#: template-functions.php:976
msgid "Payment ID"
msgstr "Identifiant de paiement"

#: template-functions.php:1008
msgid "Total Paid"
msgstr "Payé au total"

#: template-functions.php:1017
msgid "To Pay"
msgstr "Solde à payer"

#: template-functions.php:1042
msgid "Add Payment Manually"
msgstr "Ajouter le Paiement Manuellement"

#: templates/account/account-details.php:78
msgid "Change Password"
msgstr "Changer de mot de passe"

#: templates/account/account-details.php:81
msgid "Old Password"
msgstr "Ancien mot de passe"

#: templates/account/account-details.php:85
msgid "New Password"
msgstr "Nouveau mot de passe"

#: templates/account/account-details.php:89
msgid "Confirm New Password"
msgstr "Confirmer le nouveau mot de passe"

#: templates/account/account-details.php:99
msgid "You are not allowed to access this page."
msgstr "Vous n'êtes pas autorisé à accéder à cette page."

#: templates/account/bookings.php:116
#: templates/account/bookings.php:121
msgid "No bookings found."
msgstr "Aucune réservation trouvée."

#: templates/account/dashboard.php:41
msgid "From your account dashboard you can view <a href=\"%1$s\">your recent bookings</a> or edit your <a href=\"%2$s\">password and account details</a>."
msgstr "Depuis le tableau de bord de votre compte, vous pouvez voir <a href=\"%1$s\">vos dernières réservations</a> ou modifier votre <a href=\"%2$s\">mot de passe et les détails de votre compte</a>."

#: templates/create-booking/results/reserve-rooms.php:37
msgid "Base price"
msgstr "Prix de base "

#: templates/create-booking/results/rooms-found.php:19
#: templates/shortcodes/search-results/results-info.php:17
msgid "%s accommodation found"
msgid_plural "%s accommodations found"
msgstr[0] "%s chambre trouvée"
msgstr[1] "%s chambres trouvées"

#: templates/create-booking/results/rooms-found.php:24
#: templates/shortcodes/search-results/results-info.php:21
msgid " from %s - till %s"
msgstr " du %s - au %s"

#: templates/edit-booking/add-room-popup.php:24
#: templates/edit-booking/edit-reserved-rooms.php:36
msgid "Add Accommodation"
msgstr "Ajouter Hébergement"

#: templates/edit-booking/checkout-form.php:28
msgid "Save"
msgstr "Sauvegarder"

#: templates/edit-booking/edit-dates.php:25
msgid "Choose new dates to check availability of reserved accommodations in the original booking."
msgstr "Choisissez de nouvelles dates pour vérifier la disponibilité des hébergements réservés dans la réservation d'origine."

#: templates/edit-booking/edit-reserved-rooms.php:39
msgid "Add, remove or replace accommodations in the original booking."
msgstr "Ajouter, supprimer ou remplacer les hébergements dans la réservation d’origine."

#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Not Available"
msgstr "Indisponible"

#: templates/edit-booking/edit-reserved-rooms.php:79
#: templates/edit-booking/summary-table.php:65
msgid "Continue"
msgstr "Continuez"

#: templates/edit-booking/summary-table.php:26
msgid "Choose how to associate data"
msgstr "Choisissez comment associer les données"

#: templates/edit-booking/summary-table.php:27
msgid "Use Source Accommodation to assign pre-filled booking information available in the original booking, e.g., full guest name, selected rate, services, etc."
msgstr "Utiliser l'Hébergement Source pour attribuer les informations de réservation pré-remplies disponibles dans la réservation d'origine, par exemple le nom complet du client, le tarif sélectionné, les services, etc."

#: templates/edit-booking/summary-table.php:32
msgid "Source accommodation"
msgstr "Hébergement source"

#: templates/edit-booking/summary-table.php:34
msgid "Target accommodation"
msgstr "Hébergement cible"

#: templates/emails/admin-customer-cancelled-booking.php:15
msgid "Booking #%s is cancelled by customer."
msgstr "La réservation #%s est annulé par le client."

#: templates/emails/admin-customer-cancelled-booking.php:17
#: templates/emails/admin-customer-confirmed-booking.php:17
#: templates/emails/admin-payment-confirmed-booking.php:24
#: templates/emails/admin-pending-booking.php:17
#: templates/emails/customer-approved-booking.php:17
#: templates/emails/customer-cancelled-booking.php:18
#: templates/emails/customer-confirmation-booking.php:24
#: templates/emails/customer-pending-booking.php:19
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:19
msgid "Details of booking"
msgstr "Détails de réservation"

#: templates/emails/admin-customer-cancelled-booking.php:18
#: templates/emails/admin-customer-confirmed-booking.php:18
#: templates/emails/admin-payment-confirmed-booking.php:25
#: templates/emails/admin-pending-booking.php:18
#: templates/emails/customer-approved-booking.php:20
#: templates/emails/customer-cancelled-booking.php:21
#: templates/emails/customer-confirmation-booking.php:27
#: templates/emails/customer-pending-booking.php:22
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:22
msgid "Check-in: %1$s, from %2$s"
msgstr "Enregistrement : %1$s, du %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:20
#: templates/emails/admin-customer-confirmed-booking.php:20
#: templates/emails/admin-payment-confirmed-booking.php:27
#: templates/emails/admin-pending-booking.php:20
#: templates/emails/customer-approved-booking.php:22
#: templates/emails/customer-cancelled-booking.php:23
#: templates/emails/customer-confirmation-booking.php:29
#: templates/emails/customer-pending-booking.php:24
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:24
msgid "Check-out: %1$s, until %2$s"
msgstr "Départ: %1$s, jusqu'à %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:24
#: templates/emails/admin-customer-confirmed-booking.php:24
#: templates/emails/admin-payment-confirmed-booking.php:31
#: templates/emails/admin-pending-booking.php:24
#: templates/emails/customer-approved-booking.php:32
#: templates/emails/customer-cancelled-booking.php:30
#: templates/emails/customer-confirmation-booking.php:36
#: templates/emails/customer-pending-booking.php:33
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:31
msgid "Name: %1$s %2$s"
msgstr "Nom : %1$s %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:26
#: templates/emails/admin-customer-confirmed-booking.php:26
#: templates/emails/admin-payment-confirmed-booking.php:33
#: templates/emails/admin-pending-booking.php:26
#: templates/emails/customer-approved-booking.php:34
#: templates/emails/customer-cancelled-booking.php:32
#: templates/emails/customer-confirmation-booking.php:38
#: templates/emails/customer-pending-booking.php:35
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:33
msgid "Email: %s"
msgstr "Email : %s"

#: templates/emails/admin-customer-cancelled-booking.php:28
#: templates/emails/admin-customer-confirmed-booking.php:28
#: templates/emails/admin-payment-confirmed-booking.php:35
#: templates/emails/admin-pending-booking.php:28
#: templates/emails/customer-approved-booking.php:36
#: templates/emails/customer-cancelled-booking.php:34
#: templates/emails/customer-confirmation-booking.php:40
#: templates/emails/customer-pending-booking.php:37
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:35
msgid "Phone: %s"
msgstr "Téléphone : %s"

#: templates/emails/admin-customer-cancelled-booking.php:30
#: templates/emails/admin-customer-confirmed-booking.php:30
#: templates/emails/admin-payment-confirmed-booking.php:37
#: templates/emails/admin-pending-booking.php:30
#: templates/emails/customer-approved-booking.php:38
#: templates/emails/customer-cancelled-booking.php:36
#: templates/emails/customer-confirmation-booking.php:42
#: templates/emails/customer-pending-booking.php:39
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:37
msgid "Note: %s"
msgstr "Remarque : %s"

#: templates/emails/admin-customer-confirmed-booking.php:15
msgid "Booking #%s is confirmed by customer."
msgstr "Réservation #%s est confirmée par le client."

#: templates/emails/admin-payment-confirmed-booking.php:15
msgid "Booking #%s is confirmed by payment."
msgstr "La réservation est #%s confirmée par le paiement."

#: templates/emails/admin-payment-confirmed-booking.php:17
msgid "Details of payment"
msgstr "Détails de paiement"

#: templates/emails/admin-payment-confirmed-booking.php:18
msgid "Payment ID: #%s"
msgstr "Identifiant de paiement: #%s"

#: templates/emails/admin-payment-confirmed-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:16
msgid "Amount: %s"
msgstr "Quantité: %s"

#: templates/emails/admin-payment-confirmed-booking.php:22
msgid "Method: %s"
msgstr "Méthode: %s"

#: templates/emails/admin-pending-booking.php:15
msgid "Booking #%s is pending for Administrator approval."
msgstr "La réservation #%s est en attente d'approbation par l'administrateur."

#: templates/emails/cancellation-details.php:14
msgid "Click the link below to cancel your booking."
msgstr "Cliquez sur le lien ci-dessous pour annuler votre réservation."

#: templates/emails/cancellation-details.php:16
msgid "Cancel your booking"
msgstr "Annuler votre réservation"

#: templates/emails/customer-approved-booking.php:15
msgid "Dear %1$s %2$s, your reservation is approved!"
msgstr "Cher %1$s %2$s, votre réservation est approuvée !"

#: templates/emails/customer-approved-booking.php:18
#: templates/emails/customer-cancelled-booking.php:19
#: templates/emails/customer-confirmation-booking.php:25
#: templates/emails/customer-pending-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:20
msgid "ID: #%s"
msgstr "ID : #%s"

#: templates/emails/customer-approved-booking.php:41
#: templates/emails/customer-cancelled-booking.php:38
#: templates/emails/customer-confirmation-booking.php:44
#: templates/emails/customer-pending-booking.php:41
#: templates/emails/customer-registration.php:26
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:39
msgid "Thank you!"
msgstr "Merci !"

#: templates/emails/customer-cancelled-booking.php:15
msgid "Dear %1$s %2$s, your reservation is cancelled!"
msgstr "Cher %1$s %2$s, votre réservation est annulée !"

#: templates/emails/customer-confirmation-booking.php:14
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:12
msgid "Dear %1$s %2$s, we received your request for reservation."
msgstr "Cher %1$s %2$s, nous avons reçu votre demande de réservation."

#: templates/emails/customer-confirmation-booking.php:16
msgid "Click the link below to confirm your booking."
msgstr "Cliquez sur le lien ci-dessous pour confirmer votre réservation."

#: templates/emails/customer-confirmation-booking.php:18
msgid "Confirm"
msgstr "Confirmer"

#: templates/emails/customer-confirmation-booking.php:20
msgid "Note: link expires on"
msgstr "Remarque : le lien expire le"

#: templates/emails/customer-confirmation-booking.php:20
msgid "UTC"
msgstr "UTC"

#: templates/emails/customer-confirmation-booking.php:22
msgid "If you did not place this booking, please ignore this email."
msgstr "Si vous n'avez pas effectué cette réservation, veuillez ignorer cet e-mail."

#: templates/emails/customer-pending-booking.php:15
msgid "Dear %1$s %2$s, your reservation is pending."
msgstr "Cher %1$s %2$s, votre réservation est en attente."

#: templates/emails/customer-pending-booking.php:17
msgid "We will notify you by email once it is confirmed by our staff."
msgstr "Nous vous en informerons par e-mail une fois qu'elle sera confirmée par notre personnel."

#: templates/emails/customer-registration.php:15
msgid "Hi %1$s %2$s,"
msgstr "Bonjour %1$s %2$s,"

#: templates/emails/customer-registration.php:17
msgid "Thanks for creating an account on %1$s."
msgstr "Merci d'avoir créé un compte sur %1$s."

#: templates/emails/customer-registration.php:19
msgid "You Account Details"
msgstr "Détails de votre compte"

#: templates/emails/customer-registration.php:20
msgid "Login: %s"
msgstr "Identifiant : %s"

#: templates/emails/customer-registration.php:21
msgid "Password: %s"
msgstr "Mot de passe : %s"

#: templates/emails/customer-registration.php:22
msgid "Log in here: %s"
msgstr "Se connecter ici : %s"

#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:14
msgid "To confirm your booking, please follow the instructions below for payment."
msgstr ""

#: templates/emails/reserved-room-details.php:14
msgid "Accommodation #%s"
msgstr "Logement #%s"

#: templates/emails/reserved-room-details.php:21
msgid "Accommodation: <a href=\"%1$s\">%2$s</a>"
msgstr "Hébergement : <a href=\"%1$s\">%2$s</a>"

#: templates/emails/reserved-room-details.php:24
msgid "Accommodation Rate: %s"
msgstr "Note d'hôtel : %s"

#: templates/emails/reserved-room-details.php:28
msgid "Bed Type: %s"
msgstr "Type de lit : %s"

#: templates/required-fields-tip.php:8
msgid "Required fields are followed by"
msgstr "Les champs obligatoires sont suivis de"

#: templates/shortcodes/booking-cancellation/already-cancelled.php:7
msgid "Booking is already canceled."
msgstr "La réservation a déjà été annulée."

#: templates/shortcodes/booking-cancellation/booking-cancellation-button.php:15
msgid "Cancel Booking"
msgstr "Annuler la réservation"

#: templates/shortcodes/booking-cancellation/invalid-request.php:7
#: templates/shortcodes/booking-confirmation/invalid-request.php:7
msgid "Invalid request."
msgstr "Demande non valide."

#: templates/shortcodes/booking-cancellation/not-possible.php:7
msgid "Cancelation of your booking is not possible for some reason. Please contact the website administrator."
msgstr "Pour une raison inconnue, l'annulation de votre réservation n'est pas possible. Veuillez contacter l'administrateur du site web."

#: templates/shortcodes/booking-confirmation/already-confirmed.php:7
msgid "Booking is already confirmed."
msgstr "La réservation est déjà confirmée."

#: templates/shortcodes/booking-confirmation/confirmed.php:7
msgid "Your booking is confirmed. Thank You!"
msgstr "Votre réservation est confirmée. Merci !"

#: templates/shortcodes/booking-confirmation/expired.php:7
msgid "Your booking request is expired. Please start a new booking request."
msgstr "Votre demande de réservation a expiré. Veuillez déposer une nouvelle demande de réservation."

#: templates/shortcodes/booking-confirmation/not-possible.php:7
msgid "Confirmation of your booking request is not possible for some reason. Please start a new booking request."
msgstr "La confirmation de votre demande de réservation est impossible pour une raison quelconque. Veuillez déposer une nouvelle demande de réservation."

#: templates/shortcodes/booking-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received and confirmed."
msgstr "Nous avons le plaisir de vous informer que votre demande de réservation a été reçue et confirmée."

#: templates/shortcodes/booking-details/booking-details.php:21
msgid "Booking:"
msgstr "Réservation :"

#: templates/shortcodes/booking-details/booking-details.php:47
msgid "Details:"
msgstr "Détails :"

#: templates/shortcodes/payment-confirmation/completed.php:11
msgid "Thank you for your payment. Your transaction has been completed."
msgstr "Merci pour votre règlement. Votre transaction a bien été effectuée."

#: templates/shortcodes/payment-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received."
msgstr "Nous vous informons que votre demande de réservation a bien été réceptionnée."

#: templates/shortcodes/room-rates/rate-content.php:17
msgid "from %s"
msgstr "de %s"

#: templates/shortcodes/rooms/not-found.php:7
msgid "No accommodations matching criteria."
msgstr "Aucune chambre correspondant aux critères."

#: templates/shortcodes/services/not-found.php:7
msgid "No services matched criteria."
msgstr "Aucun critère de service correspondant."

#: templates/widgets/rooms/not-found.php:6
msgid "Nothing found."
msgstr "Rien n'a été trouvé."

#: templates/widgets/search-availability/search-form.php:105
msgid "Children %s:"
msgstr "Enfants %s :"

#: assets/blocks/blocks.js:178
#: assets/blocks/blocks.js:190
msgid "Preset date. Formatted as %s"
msgstr "Date prédéfinie. Formaté en %s"

#: assets/blocks/blocks.js:283
#: assets/blocks/blocks.js:1425
#: assets/blocks/blocks.js:1507
msgid "Select an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:284
#: assets/blocks/blocks.js:1426
#: assets/blocks/blocks.js:1508
msgid "ID of an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:460
#: assets/blocks/blocks.js:710
#: assets/blocks/blocks.js:1239
msgid "Gallery"
msgstr "galerie"

#: assets/blocks/blocks.js:508
#: assets/blocks/blocks.js:758
#: assets/blocks/blocks.js:1287
msgid "View Button"
msgstr "bouton de visualisation"

#: assets/blocks/blocks.js:522
#: assets/blocks/blocks.js:558
#: assets/blocks/blocks.js:858
#: assets/blocks/blocks.js:894
#: assets/blocks/blocks.js:1042
#: assets/blocks/blocks.js:1078
msgid "Order"
msgstr "commande"

#: assets/blocks/blocks.js:530
#: assets/blocks/blocks.js:866
#: assets/blocks/blocks.js:1050
msgid "Order By"
msgstr "commander par"

#: assets/blocks/blocks.js:533
#: assets/blocks/blocks.js:869
#: assets/blocks/blocks.js:1053
msgid "No order"
msgstr "pas de commande"

#: assets/blocks/blocks.js:534
#: assets/blocks/blocks.js:870
#: assets/blocks/blocks.js:1054
msgid "Post ID"
msgstr "Numéro de poste"

#: assets/blocks/blocks.js:535
#: assets/blocks/blocks.js:871
#: assets/blocks/blocks.js:1055
msgid "Post author"
msgstr "Auteur du message"

#: assets/blocks/blocks.js:536
#: assets/blocks/blocks.js:872
#: assets/blocks/blocks.js:1056
msgid "Post title"
msgstr "Titre du poste"

#: assets/blocks/blocks.js:537
#: assets/blocks/blocks.js:873
#: assets/blocks/blocks.js:1057
msgid "Post name (post slug)"
msgstr "Nom du message (poste slug)"

#: assets/blocks/blocks.js:538
#: assets/blocks/blocks.js:874
#: assets/blocks/blocks.js:1058
msgid "Post date"
msgstr "Date de publication"

#: assets/blocks/blocks.js:539
#: assets/blocks/blocks.js:875
#: assets/blocks/blocks.js:1059
msgid "Last modified date"
msgstr "Date de la dernière modification"

#: assets/blocks/blocks.js:540
#: assets/blocks/blocks.js:876
#: assets/blocks/blocks.js:1060
msgid "Parent ID"
msgstr "numéro du parent"

#: assets/blocks/blocks.js:541
#: assets/blocks/blocks.js:877
#: assets/blocks/blocks.js:1061
msgid "Random order"
msgstr "Ordre aléatoire"

#: assets/blocks/blocks.js:542
#: assets/blocks/blocks.js:878
#: assets/blocks/blocks.js:1062
msgid "Number of comments"
msgstr "Nombre de commentaires"

#: assets/blocks/blocks.js:543
#: assets/blocks/blocks.js:879
#: assets/blocks/blocks.js:1063
msgid "Relevance"
msgstr "Pertinence"

#: assets/blocks/blocks.js:544
#: assets/blocks/blocks.js:880
#: assets/blocks/blocks.js:1064
msgid "Page order"
msgstr "Ordre des pages"

#: assets/blocks/blocks.js:545
#: assets/blocks/blocks.js:881
#: assets/blocks/blocks.js:1065
msgid "Meta value"
msgstr "Valeur Meta"

#: assets/blocks/blocks.js:546
#: assets/blocks/blocks.js:882
#: assets/blocks/blocks.js:1066
msgid "Numeric meta value"
msgstr "Meta value numérique "

#: assets/blocks/blocks.js:561
#: assets/blocks/blocks.js:897
#: assets/blocks/blocks.js:1081
msgid "Ascending (1, 2, 3)"
msgstr "Croissant (1, 2, 3)"

#: assets/blocks/blocks.js:562
#: assets/blocks/blocks.js:898
#: assets/blocks/blocks.js:1082
msgid "Descending (3, 2, 1)"
msgstr "Décroissant (3, 2, 1)"

#: assets/blocks/blocks.js:573
#: assets/blocks/blocks.js:909
#: assets/blocks/blocks.js:1093
msgid "Meta Name"
msgstr "Meta Name"

#: assets/blocks/blocks.js:585
#: assets/blocks/blocks.js:921
#: assets/blocks/blocks.js:1105
msgid "Meta Type"
msgstr "Type Méta"

#: assets/blocks/blocks.js:586
#: assets/blocks/blocks.js:922
#: assets/blocks/blocks.js:1106
msgid "Specified type of the custom field. Can be used in conjunction with \"orderby\" = \"meta_value\"."
msgstr "Type spécifié du champ personnalisé. Peut être utilisé avec \"orderby\" = \"meta_value\"."

#: assets/blocks/blocks.js:589
#: assets/blocks/blocks.js:925
#: assets/blocks/blocks.js:1109
msgid "Any"
msgstr "n'importe quel"

#: assets/blocks/blocks.js:590
#: assets/blocks/blocks.js:926
#: assets/blocks/blocks.js:1110
msgid "Numeric"
msgstr "numérique"

#: assets/blocks/blocks.js:591
#: assets/blocks/blocks.js:927
#: assets/blocks/blocks.js:1111
msgid "Binary"
msgstr "Binaire"

#: assets/blocks/blocks.js:592
#: assets/blocks/blocks.js:928
#: assets/blocks/blocks.js:1112
msgid "String"
msgstr "Chaîne"

#: assets/blocks/blocks.js:594
#: assets/blocks/blocks.js:930
#: assets/blocks/blocks.js:1114
msgid "Time"
msgstr "durée"

#: assets/blocks/blocks.js:595
#: assets/blocks/blocks.js:931
#: assets/blocks/blocks.js:1115
msgid "Date and time"
msgstr "Date et heure"

#: assets/blocks/blocks.js:596
#: assets/blocks/blocks.js:932
#: assets/blocks/blocks.js:1116
msgid "Decimal number"
msgstr "Nombre décimal"

#: assets/blocks/blocks.js:597
#: assets/blocks/blocks.js:933
#: assets/blocks/blocks.js:1117
msgid "Signed number"
msgstr "Nombre signé"

#: assets/blocks/blocks.js:598
#: assets/blocks/blocks.js:934
#: assets/blocks/blocks.js:1118
msgid "Unsigned number"
msgstr "Nombre non signé"

#: assets/blocks/blocks.js:784
#: assets/blocks/blocks.js:1009
msgid "Query Settings"
msgstr "Paramètres de Query"

#: assets/blocks/blocks.js:840
msgid "Relation"
msgstr "relation"

#: assets/blocks/blocks.js:1029
msgid "Values: integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "Mettre: -1 pour tout afficher, par défaut : \"Les pages des blogs s'affichent tout au plus.\"."

#: assets/blocks/blocks.js:1203
msgid "Select an accommodation type."
msgstr ""

