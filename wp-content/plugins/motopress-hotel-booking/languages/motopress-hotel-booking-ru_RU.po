# Copyright (C) 2025 MotoPress
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: hotel-booking-plugin\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/motopress-hotel-booking\n"
"Last-Translator: \n"
"Language-Team: Russian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-19T19:58:50+00:00\n"
"PO-Revision-Date: 2025-03-05 20:50\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: motopress-hotel-booking\n"
"Plural-Forms: nplurals=4; plural=((n%10==1 && n%100!=11) ? 0 : ((n%10 >= 2 && n%10 <=4 && (n%100 < 12 || n%100 > 14)) ? 1 : ((n%10 == 0 || (n%10 >= 5 && n%10 <=9)) || (n%100 >= 11 && n%100 <= 14)) ? 2 : 3));\n"
"X-Crowdin-Project: hotel-booking-plugin\n"
"X-Crowdin-Project-ID: 463550\n"
"X-Crowdin-Language: ru\n"
"X-Crowdin-File: motopress-hotel-booking.pot\n"
"X-Crowdin-File-ID: 44\n"
"Language: ru_RU\n"

#. Plugin Name of the plugin
#. translators: Name of the plugin, do not translate
#: motopress-hotel-booking.php
#: includes/script-managers/block-script-manager.php:27
msgid "Hotel Booking"
msgstr "Hotel Booking"

#. Plugin URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/products/hotel-booking/"
msgstr "https://motopress.com/products/hotel-booking/"

#. Description of the plugin
#: motopress-hotel-booking.php
msgid "Manage your hotel booking services. Perfect for hotels, villas, guest houses, hostels, and apartments of all sizes."
msgstr "Управляйте гостиничным бизнесом, предоставляя услугу онлайн-бронирования. Отлично подходит для отелей, хостелов, вилл и гостевых домов любого размера."

#. Author of the plugin
#: motopress-hotel-booking.php
msgid "MotoPress"
msgstr "MotoPress"

#. Author URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/"
msgstr "https://motopress.com/"

#: functions.php:71
msgctxt "Post Status"
msgid "New"
msgstr "Новый"

#: functions.php:74
msgctxt "Post Status"
msgid "Auto Draft"
msgstr "Авточерновик"

#. translators: %s: URL to plugins.php page
#: functions.php:518
msgid "You are using two instances of Hotel Booking plugin at the same time, please <a href=\"%s\">deactivate one of them</a>."
msgstr "Вы одновременно используете два плагина Hotel Booking, <a href=\"%s\">деактивируйте один из них</a>."

#: functions.php:535
msgid "<a href=\"%s\">Upgrade to Premium</a> to enable this feature."
msgstr "<a href=\"%s\">Перейдите на премиум-версию</a>, чтобы воспользоваться этой функцией."

#: includes/actions-handler.php:100
#: includes/admin/sync-logs-list-table.php:91
#: includes/csv/csv-export-handler.php:33
#: includes/csv/csv-export-handler.php:51
#: includes/payments/gateways/stripe-gateway.php:560
#: includes/payments/gateways/stripe-gateway.php:572
#: includes/payments/gateways/stripe-gateway.php:631
msgid "Error"
msgstr "Ошибка"

#: includes/admin/customers-list-table.php:143
#: includes/admin/menu-pages/rooms-generator-menu-page.php:84
#: includes/admin/sync-rooms-list-table.php:146
#: includes/post-types/room-type-cpt.php:354
#: templates/account/bookings.php:80
msgid "View"
msgstr "Просмотр"

#: includes/admin/customers-list-table.php:147
#: includes/admin/fields/abstract-complex-field.php:25
#: includes/admin/fields/rules-list-field.php:61
#: includes/admin/sync-rooms-list-table.php:147
msgid "Delete"
msgstr "Удалить"

#: includes/admin/customers-list-table.php:212
#: includes/post-types/attributes-cpt.php:301
msgid "Name"
msgstr "Имя"

#: includes/admin/customers-list-table.php:213
#: includes/admin/menu-pages/customers-menu-page.php:207
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:122
#: includes/bundles/customer-bundle.php:110
#: includes/csv/bookings/bookings-exporter-helper.php:83
#: includes/post-types/booking-cpt.php:106
#: includes/post-types/payment-cpt.php:263
#: includes/views/shortcodes/checkout-view.php:618
#: templates/account/account-details.php:34
msgid "Email"
msgstr "Эл. почта"

#: includes/admin/customers-list-table.php:214
#: includes/admin/menus.php:72
#: includes/admin/menus.php:73
#: includes/post-types/booking-cpt.php:241
#: includes/shortcodes/account-shortcode.php:239
msgid "Bookings"
msgstr "Бронирования"

#: includes/admin/customers-list-table.php:215
msgid "Date Registered"
msgstr "Дата регистрации"

#: includes/admin/customers-list-table.php:216
msgid "Last Active"
msgstr "Последняя активность"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:16
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:33
msgid "Terms"
msgstr "Варианты"

#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:27
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:46
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:41
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:115
msgid "Created on:"
msgstr "Создано:"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:88
msgid "Please add attribute in default language to configure terms."
msgstr "Чтобы настроить варианты, добавьте атрибут на языке, установленном по умолчанию."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:98
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:116
msgid "Configure terms"
msgstr "Настроить варианты "

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:20
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:107
#: includes/post-types/reserved-room-cpt.php:22
msgid "Reserved Accommodations"
msgstr "Зарезервированные варианты размещения"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:21
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:66
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Update Booking"
msgstr "Обновить бронирование"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:22
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:12
msgid "Logs"
msgstr "Логи"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:54
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:125
msgid "Delete Permanently"
msgstr "Удалить навсегда"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:58
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:127
msgid "Move to Trash"
msgstr "Удалить (в корзину)"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Create Booking"
msgstr "Создать бронирование"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:85
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:98
msgid "Resend Email"
msgstr "Отправить письмо повторно"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:100
msgid "Send a copy of the Approved Booking email to the customer`s email address."
msgstr "Отправьте копию подтвержденного бронирования на адрес электронной почты клиента."

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:116
#: templates/edit-booking/edit-reserved-rooms.php:35
msgid "Edit Accommodations"
msgstr "Изменить варианты размещения"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:125
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:85
#: includes/shortcodes/booking-confirmation-shortcode.php:298
msgid "Date:"
msgstr "Дата:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:130
msgid "Author:"
msgstr "Автор:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:145
#: includes/payments/gateways/stripe-gateway.php:528
msgid "Auto"
msgstr "Автоматически"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:155
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:89
msgid "Message:"
msgstr "Сообщение:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:227
msgid "Confirmation email has been sent to customer."
msgstr "Письмо с подтверждением было отправлено клиенту."

#: includes/admin/edit-cpt-pages/coupon-edit-cpt-page.php:14
msgid "Coupon code"
msgstr "Код купона"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:11
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:64
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Update Payment"
msgstr "Обновить платёж"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:44
msgid "Modified on:"
msgstr "Изменено:"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Create Payment"
msgstr "Создать платёж"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:47
msgid "Season Prices"
msgstr "Цены сезона"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:48
msgid "<code>Please select Accommodation Type and click Create Rate button to continue.</code>"
msgstr "<code>Выберите Тип размещения и нажмите кнопку «Создать тариф», чтобы продолжить.</code>"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:66
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:65
#: includes/views/loop-room-type-view.php:113
#: includes/views/single-room-type-view.php:205
#: template-functions.php:920
#: templates/create-booking/results/reserve-rooms.php:51
#: templates/widgets/rooms/room-content.php:77
#: templates/widgets/search-availability/search-form.php:78
msgid "Adults:"
msgstr "Взрослых:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:66
#: includes/views/loop-room-type-view.php:128
#: includes/views/single-room-type-view.php:220
#: template-functions.php:925
#: templates/create-booking/results/reserve-rooms.php:52
#: templates/widgets/rooms/room-content.php:91
#: templates/widgets/search-availability/search-form.php:103
msgid "Children:"
msgstr "Детей:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:70
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:63
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:110
#: includes/shortcodes/booking-confirmation-shortcode.php:306
#: includes/shortcodes/search-results-shortcode.php:770
#: includes/shortcodes/search-results-shortcode.php:921
#: templates/shortcodes/booking-details/booking-details.php:33
msgid "Total:"
msgstr "Итого:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:80
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Update Rate"
msgstr "Обновить тариф"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:97
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
msgid "Active"
msgstr "Активно"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:98
#: includes/admin/groups/license-settings-group.php:61
msgid "Disabled"
msgstr "Отключено"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Create Rate"
msgstr "Создать тариф"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:190
msgid "Duplicate Rate"
msgstr "Дублировать тариф"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:12
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:36
#: includes/admin/menu-pages/booking-rules-menu-page.php:219
#: includes/admin/menu-pages/booking-rules-menu-page.php:264
#: includes/admin/menu-pages/booking-rules-menu-page.php:310
#: includes/admin/menu-pages/booking-rules-menu-page.php:356
#: includes/admin/menu-pages/booking-rules-menu-page.php:487
#: includes/admin/menu-pages/booking-rules-menu-page.php:533
#: includes/admin/menu-pages/booking-rules-menu-page.php:579
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:208
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:304
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:377
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:448
#: includes/post-types/room-cpt.php:31
#: includes/post-types/room-cpt.php:41
#: includes/wizard.php:103
msgid "Accommodations"
msgstr "Варианты размещения"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:15
#: includes/post-types/attributes-cpt.php:54
#: includes/post-types/attributes-cpt.php:61
#: includes/post-types/attributes-cpt.php:65
msgid "Attributes"
msgstr "Атрибуты"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:18
msgid "Accommodation Reviews"
msgstr "Отзывы гостей"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:27
msgid "Allow guests to <a href=\"%s\" target=\"_blank\">submit star ratings and reviews</a> evaluating your accommodations."
msgstr "Разрешите гостям <a href=\"%s\" target=\"_blank\">оставлять оценки и отзывы</a> на Ваши услуги и номера."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:69
msgid "Number of Accommodations:"
msgstr "Количество вариантов размещения:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:74
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:29
msgid "Count of real accommodations of this type in your hotel."
msgstr "Количество реальных номеров/комнат этого типа размещения в Вашем отеле."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:122
msgid "Total Accommodations:"
msgstr "Всего вариантов размещения:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:159
msgid "Show Accommodations"
msgstr "Показать варианты размещения"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:139
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:18
#: includes/admin/menu-pages/rooms-generator-menu-page.php:146
#: includes/admin/menu-pages/rooms-generator-menu-page.php:150
msgid "Generate Accommodations"
msgstr "Сгенерировать варианты размещения"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:147
msgid "Active Accommodations:"
msgstr "Активные варианты размещения:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:168
#: includes/post-types/room-cpt.php:93
msgid "Linked Accommodations"
msgstr ""

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:173
msgid "Link accommodations on the Edit Accommodation page to ensure bookings for one make any linked properties unavailable for the same dates."
msgstr ""

#: includes/admin/fields/abstract-complex-field.php:24
#: includes/admin/fields/rules-list-field.php:57
#: templates/edit-booking/add-room-popup.php:45
msgid "Add"
msgstr "Добавить"

#: includes/admin/fields/amount-field.php:74
msgid "Per adult:"
msgstr "За взрослого:"

#: includes/admin/fields/amount-field.php:77
msgid "Per child:"
msgstr "За ребёнка:"

#: includes/admin/fields/amount-field.php:198
msgid "Per adult: "
msgstr "За взрослого: "

#: includes/admin/fields/amount-field.php:200
msgid "Per child: "
msgstr "За ребёнка: "

#: includes/admin/fields/complex-horizontal-field.php:71
#: includes/admin/fields/rules-list-field.php:62
#: templates/account/bookings.php:22
#: templates/account/bookings.php:79
#: templates/edit-booking/edit-reserved-rooms.php:47
msgid "Actions"
msgstr "Действия"

#: includes/admin/fields/complex-horizontal-field.php:111
msgid "Move up"
msgstr ""

#: includes/admin/fields/complex-horizontal-field.php:112
msgid "Move down"
msgstr ""

#: includes/admin/fields/complex-horizontal-field.php:113
msgid "Move to top"
msgstr ""

#: includes/admin/fields/complex-horizontal-field.php:114
msgid "Move to bottom"
msgstr ""

#: includes/admin/fields/complex-vertical-field.php:17
#: includes/admin/menu-pages/settings-menu-page.php:580
#: includes/settings/main-settings.php:25
#: includes/settings/main-settings.php:43
msgid "Default"
msgstr "По умолчанию"

#: includes/admin/fields/dynamic-select-field.php:61
#: includes/admin/fields/page-select-field.php:16
#: includes/admin/menu-pages/customers-menu-page.php:260
#: includes/admin/menu-pages/rooms-generator-menu-page.php:38
#: includes/admin/menu-pages/settings-menu-page.php:420
#: includes/post-types/booking-cpt.php:122
#: includes/post-types/booking-cpt.php:179
#: includes/post-types/payment-cpt.php:231
#: includes/post-types/rate-cpt.php:31
#: includes/post-types/room-cpt.php:79
#: includes/views/shortcodes/checkout-view.php:250
#: includes/views/shortcodes/checkout-view.php:273
#: templates/account/account-details.php:51
#: templates/edit-booking/add-room-popup.php:30
#: templates/edit-booking/add-room-popup.php:38
msgid "— Select —"
msgstr "— Выбрать —"

#: includes/admin/fields/install-plugin-field.php:33
msgid "Install & Activate"
msgstr "Установить и активировать"

#: includes/admin/fields/media-field.php:76
msgid "Add image"
msgstr "Добавить изображение"

#: includes/admin/fields/media-field.php:76
msgid "Add gallery"
msgstr "Добавить галерею"

#: includes/admin/fields/media-field.php:77
msgid "Remove image"
msgstr "Удалить изображение"

#: includes/admin/fields/media-field.php:77
msgid "Remove gallery"
msgstr "Удалить галерею"

#: includes/admin/fields/multiple-checkbox-field.php:88
#: template-functions.php:1088
msgid "Select all"
msgstr "Выбрать все"

#: includes/admin/fields/multiple-checkbox-field.php:92
#: template-functions.php:1090
msgid "Unselect all"
msgstr "Снять все"

#: includes/admin/fields/notes-list-field.php:23
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:68
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:33
#: includes/csv/bookings/bookings-exporter-helper.php:112
#: assets/blocks/blocks.js:593
#: assets/blocks/blocks.js:929
#: assets/blocks/blocks.js:1113
msgid "Date"
msgstr "Дата"

#: includes/admin/fields/notes-list-field.php:33
msgid "Author"
msgstr "Автор"

#: includes/admin/fields/rules-list-field.php:59
#: includes/admin/room-list-table.php:154
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:115
#: includes/bookings-calendar.php:613
#: includes/script-managers/admin-script-manager.php:97
msgid "Edit"
msgstr "Изменить"

#: includes/admin/fields/rules-list-field.php:60
#: includes/admin/sync-rooms-list-table.php:81
#: includes/ajax.php:951
#: includes/script-managers/admin-script-manager.php:98
msgid "Done"
msgstr "Готово"

#: includes/admin/fields/rules-list-field.php:64
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:85
#: includes/admin/menu-pages/booking-rules-menu-page.php:180
#: includes/admin/menu-pages/booking-rules-menu-page.php:183
#: includes/admin/menu-pages/booking-rules-menu-page.php:407
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:211
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:307
#: includes/script-managers/admin-script-manager.php:95
msgid "All"
msgstr "Все"

#: includes/admin/fields/rules-list-field.php:65
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:83
#: includes/post-types/coupon-cpt.php:81
#: includes/post-types/coupon-cpt.php:113
#: includes/post-types/coupon-cpt.php:158
#: includes/script-managers/admin-script-manager.php:96
msgid "None"
msgstr "Нет"

#: includes/admin/fields/time-picker-field.php:13
msgid "HH:MM"
msgstr "HH:MM"

#: includes/admin/fields/total-price-field.php:18
msgid "Recalculate Total Price"
msgstr "Пересчитать итоговую стоимость"

#: includes/admin/fields/variable-pricing-field.php:89
#: includes/views/booking-view.php:121
msgid "Nights"
msgstr "Сутки"

#: includes/admin/fields/variable-pricing-field.php:97
#: includes/script-managers/admin-script-manager.php:102
msgid "and more"
msgstr "и более"

#: includes/admin/fields/variable-pricing-field.php:98
#: includes/admin/menu-pages/edit-booking/edit-control.php:95
#: includes/script-managers/admin-script-manager.php:101
#: includes/shortcodes/search-results-shortcode.php:1009
#: includes/views/booking-view.php:400
#: templates/edit-booking/edit-reserved-rooms.php:70
msgid "Remove"
msgstr "Удалить"

#: includes/admin/fields/variable-pricing-field.php:104
msgid "Add length of stay"
msgstr "Добавить продолжительность пребывания"

#: includes/admin/fields/variable-pricing-field.php:109
msgid "Base Occupancy"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:110
#: includes/admin/fields/variable-pricing-field.php:170
msgid "Price per night"
msgstr "Цена за сутки"

#: includes/admin/fields/variable-pricing-field.php:118
#: includes/admin/fields/variable-pricing-field.php:167
#: includes/emails/templaters/reserved-rooms-templater.php:175
#: includes/post-types/room-type-cpt.php:283
#: includes/views/booking-view.php:105
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:242
#: template-functions.php:765
#: templates/create-booking/search/search-form.php:94
#: templates/shortcodes/search/search-form.php:80
#: assets/blocks/blocks.js:147
msgid "Adults"
msgstr "Взрослые"

#: includes/admin/fields/variable-pricing-field.php:122
#: includes/csv/bookings/bookings-exporter-helper.php:80
#: includes/emails/templaters/reserved-rooms-templater.php:179
#: includes/post-types/room-type-cpt.php:292
#: includes/views/booking-view.php:116
#: templates/create-booking/search/search-form.php:109
#: templates/shortcodes/search/search-form.php:103
#: assets/blocks/blocks.js:162
msgid "Children"
msgstr "Дети"

#: includes/admin/fields/variable-pricing-field.php:130
msgid "Price per extra adult"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:137
msgid "Price per extra child"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:154
msgid "Enable variable pricing"
msgstr "Включить вариативную цену"

#: includes/admin/fields/variable-pricing-field.php:188
msgid "Add Variation"
msgstr "Добавить вариацию"

#: includes/admin/fields/variable-pricing-field.php:215
#: includes/admin/fields/variable-pricing-field.php:231
msgid "Remove variation"
msgstr "Удалить вариацию"

#: includes/admin/groups/license-settings-group.php:21
msgid "The License Key is required in order to get automatic plugin updates and support. You can manage your License Key in your personal account. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Learn more</a>."
msgstr "Лицензионный ключ необходим для того, чтобы получать автоматические обновления плагина и техническую поддержку. Вы можете получить доступ к Вашему лицензионному ключу в персональной учётной записи. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Узнать больше</a>."

#: includes/admin/groups/license-settings-group.php:28
msgid "License Key"
msgstr "Ключ лицензии"

#: includes/admin/groups/license-settings-group.php:42
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:62
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:22
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:28
#: includes/admin/menu-pages/create-booking/checkout-step.php:63
#: includes/admin/sync-logs-list-table.php:72
#: includes/admin/sync-rooms-list-table.php:127
#: includes/csv/bookings/bookings-exporter-helper.php:72
#: template-functions.php:977
#: templates/edit-booking/edit-reserved-rooms.php:46
msgid "Status"
msgstr "Статус"

#: includes/admin/groups/license-settings-group.php:49
msgid "Inactive"
msgstr "Неактивный"

#: includes/admin/groups/license-settings-group.php:55
msgid "Valid until"
msgstr "Действительный до"

#: includes/admin/groups/license-settings-group.php:57
msgid "Valid (Lifetime)"
msgstr "Действительный (бессрочно)"

#: includes/admin/groups/license-settings-group.php:64
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:123
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:85
msgid "Expired"
msgstr "Срок истек"

#: includes/admin/groups/license-settings-group.php:67
msgid "Invalid"
msgstr "Недействительный"

#: includes/admin/groups/license-settings-group.php:71
msgid "Your License Key does not match the installed plugin. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>How to fix this.</a>"
msgstr "Ключ Вашей лицензии не соответствует установленному плагину <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>Как это исправить.</a>"

#: includes/admin/groups/license-settings-group.php:74
msgid "Product ID is not valid"
msgstr "ID продукта неверное"

#: includes/admin/groups/license-settings-group.php:83
msgid "Action"
msgstr "Действие"

#: includes/admin/groups/license-settings-group.php:90
msgid "Activate License"
msgstr "Активировать лицензию"

#: includes/admin/groups/license-settings-group.php:96
msgid "Deactivate License"
msgstr "Деактивировать лицензию"

#: includes/admin/groups/license-settings-group.php:103
msgid "Renew License"
msgstr "Обновить лицензию"

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:9
msgid "Attributes let you define extra accommodation data, such as location or type. You can use these attributes in the search availability form as advanced search filters."
msgstr "Атрибуты позволяют указать дополнительные данные о размещении, такие как местоположение или тип. Атрибуты можно использовать в форме проверки наличия мест в качестве фильтров расширенного поиска."

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:72
msgid "This attribute refers to non-unique taxonomy - %1$s - which was already registered with attribute %2$s."
msgstr "Этот атрибут относится к неуникальной таксономии %1$s, которая уже зарегистрирована с атрибутом %2$s."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:92
msgid "You cannot manage terms of trashed attributes."
msgstr "Вы не можете редактировать варианты удаленных атрибутов."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:17
#: includes/admin/menu-pages/calendar-menu-page.php:31
#: includes/post-types/booking-cpt.php:246
msgid "New Booking"
msgstr "Новое бронирование"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:61
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:26
#: includes/admin/menu-pages/shortcodes-menu-page.php:376
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:64
#: includes/csv/bookings/bookings-exporter-helper.php:71
#: includes/post-types/booking-cpt.php:42
#: includes/post-types/payment-cpt.php:150
msgid "ID"
msgstr "ID"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:63
msgid "Check-in / Check-out"
msgstr "Дата заезда / Дата отъезда"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:64
#: includes/views/booking-view.php:107
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:244
#: template-functions.php:767
#: templates/shortcodes/search/search-form.php:82
msgid "Guests"
msgstr "Гости"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:65
#: templates/emails/admin-customer-cancelled-booking.php:23
#: templates/emails/admin-customer-confirmed-booking.php:23
#: templates/emails/admin-payment-confirmed-booking.php:30
#: templates/emails/admin-pending-booking.php:23
msgid "Customer Info"
msgstr "Информация о клиенте"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:66
#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:16
#: includes/post-types/rate-cpt.php:64
#: includes/post-types/service-cpt.php:132
#: includes/post-types/service-cpt.php:137
#: includes/views/single-service-view.php:18
#: includes/widgets/rooms-widget.php:201
#: assets/blocks/blocks.js:496
#: assets/blocks/blocks.js:547
#: assets/blocks/blocks.js:746
#: assets/blocks/blocks.js:883
#: assets/blocks/blocks.js:1067
#: assets/blocks/blocks.js:1275
msgid "Price"
msgstr "Цена"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:67
#: includes/admin/menu-pages/booking-rules-menu-page.php:402
#: includes/admin/room-list-table.php:93
#: includes/admin/sync-rooms-list-table.php:126
#: includes/bookings-calendar.php:829
#: includes/bookings-calendar.php:847
#: includes/csv/bookings/bookings-exporter-helper.php:77
#: includes/post-types/room-cpt.php:32
#: includes/post-types/room-cpt.php:74
#: includes/post-types/room-type-cpt.php:60
#: templates/edit-booking/add-room-popup.php:36
#: templates/edit-booking/edit-reserved-rooms.php:45
msgid "Accommodation"
msgstr "Размещение"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:121
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:83
msgid "Expire %s"
msgstr "Истекает срок %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:138
#: includes/script-managers/admin-script-manager.php:99
msgid "Adults: "
msgstr "Взрослых: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:142
#: includes/script-managers/admin-script-manager.php:100
msgid "Children: "
msgstr "Детей: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:184
msgid "%s night"
msgid_plural "%s nights"
msgstr[0] "%s сутки"
msgstr[1] "%s суток"
msgstr[2] "%s суток"
msgstr[3] ""

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:197
#: includes/bookings-calendar.php:1189
msgid "Summary: %s."
msgstr "Итог: %s."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:280
msgid "Paid: %s"
msgstr "Оплачено: %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:345
msgid "Set to %s"
msgstr "Установить %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:460
msgid "Booking status changed."
msgid_plural "%s booking statuses changed."
msgstr[0] "Статус бронирования изменён."
msgstr[1] "%s статуса бронирования изменено."
msgstr[2] "%s статусов бронирования изменено."
msgstr[3] ""

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:506
msgid "All accommodation types"
msgstr "Все типы размещения"

#. translators: The number of imported bookings: "Imported <span>(11)</span>"
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:526
msgid "Imported %s"
msgstr "Импортировано %s"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:19
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:29
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:168
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:264
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:349
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:420
#: includes/post-types/coupon-cpt.php:93
#: includes/post-types/coupon-cpt.php:124
#: includes/post-types/coupon-cpt.php:169
#: includes/post-types/payment-cpt.php:182
#: includes/views/booking-view.php:126
#: includes/views/booking-view.php:172
#: includes/views/booking-view.php:208
#: includes/views/booking-view.php:266
#: includes/views/booking-view.php:297
#: includes/views/booking-view.php:350
#: template-functions.php:978
msgid "Amount"
msgstr "Сумма"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:20
msgid "Uses"
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:21
#: includes/post-types/coupon-cpt.php:187
msgid "Expiration Date"
msgstr "Дата окончания"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:41
#: includes/views/edit-booking/checkout-view.php:115
#: template-functions.php:900
msgid "Accommodation:"
msgstr "Размещение:"

#. translators: %s is a coupon amount per day
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:60
msgid "%s per day"
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:67
msgid "Service:"
msgstr "Услуга:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:88
msgid "Fee:"
msgstr "Сбор:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:153
msgid "Note: the use of coupons is disabled in settings."
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:165
#: includes/admin/menu-pages/settings-menu-page.php:299
msgid "Enable the use of coupons."
msgstr "Включить использование купонов."

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:27
#: includes/admin/menu-pages/customers-menu-page.php:299
msgid "Customer"
msgstr "Клиент"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:30
#: includes/post-types/booking-cpt.php:242
#: templates/account/bookings.php:18
#: templates/account/bookings.php:66
msgid "Booking"
msgstr "Бронирование"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:31
#: includes/post-types/payment-cpt.php:159
msgid "Gateway"
msgstr "Способ оплаты"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:32
#: includes/post-types/payment-cpt.php:223
msgid "Transaction ID"
msgstr "ID транзакции"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:105
#: includes/admin/menu-pages/create-booking/booking-step.php:66
#: includes/bookings-calendar.php:606
#: includes/script-managers/admin-script-manager.php:103
msgid "Booking #%s"
msgstr "Бронирование #%s"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:13
msgid "Rates are used to offer different prices of the same accommodation type depending on extra conditions, e.g. With Breakfast, With No Breakfast, Refundable etc. Guests will choose the preferable rate when submitting a booking request. Create one default rate if you have no price tiers. To add price variations for different periods - open a rate, add a season, and set the price."
msgstr ""

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:23
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:32
#: includes/admin/menu-pages/booking-rules-menu-page.php:393
#: includes/admin/menu-pages/rooms-generator-menu-page.php:34
#: includes/csv/bookings/bookings-exporter-helper.php:75
#: includes/post-types/rate-cpt.php:30
#: includes/post-types/room-cpt.php:84
#: includes/post-types/room-type-cpt.php:54
#: templates/create-booking/search/search-form.php:82
#: templates/edit-booking/add-room-popup.php:28
#: templates/edit-booking/edit-reserved-rooms.php:44
#: assets/blocks/blocks.js:282
#: assets/blocks/blocks.js:1202
#: assets/blocks/blocks.js:1424
#: assets/blocks/blocks.js:1506
msgid "Accommodation Type"
msgstr "Тип размещения"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:24
msgid "Season &#8212; Price"
msgstr "Сезон &#8212; Цена"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:67
#: includes/post-types/rate-cpt.php:73
msgid "Add New Season Price"
msgstr "Добавить новую цену сезона"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:104
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:94
#: includes/post-types/season-cpt.php:71
msgid "Annually"
msgstr ""

#. translators: %s: A date string such as "December 31, 2025".
#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:108
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:98
msgid "Annually until %s"
msgstr ""

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:201
msgid "Duplicate"
msgstr "Дублировать"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:281
msgid "Rate was duplicated."
msgstr "Тариф продублирован."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:13
msgid "These are real accommodations like rooms, apartments, houses, villas, beds (for hostels) etc."
msgstr "Это реальные варианты размещения, например: комнаты, квартиры, дома, виллы, кровати в хостеле и т. д."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:77
#: includes/admin/menu-pages/reports-menu-page.php:129
#: includes/bookings-calendar.php:746
msgid "All Accommodation Types"
msgstr "Все типы вариантов размещения"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:11
msgid "These are not physical accommodations, but their types. E.g. standard double room. To specify the real number of existing accommodations, you'll need to use Generate Accommodations menu."
msgstr "Это не реальные варианты размещения (номера/квартиры), а их типы. Например, стандартный двухместный номер. Для того, чтобы указать реальное количество вариантов размещения, Вам необходимо использовать меню \"Сгенерировать варианты размещения\"."

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:34
#: includes/post-types/room-type-cpt.php:275
#: includes/post-types/room-type-cpt.php:302
#: templates/create-booking/results/reserve-rooms.php:36
msgid "Capacity"
msgstr "Вместимость"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:35
msgid "Bed Type"
msgstr "Тип кровати"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:68
#: includes/views/loop-room-type-view.php:152
#: includes/views/single-room-type-view.php:244
#: templates/widgets/rooms/room-content.php:167
msgid "Size:"
msgstr "Размер:"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:114
msgid "Active:"
msgstr "Активные:"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:13
msgid "Seasons are real periods of time, dates or days that come with different prices for accommodations. E.g. Winter 2018 ($120 per night), Christmas ($150 per night)."
msgstr "Сезоны - это реальные периоды времени (дни или даты), которые определяют изменение цен для типов размещения. Например, сезон \"Зима 2018\" (1000 руб за ночь), сезон \"Рождество\" (1500 руб за ночь)."

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:20
msgid "Start"
msgstr "Начало"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:21
msgid "End"
msgstr "Окончание"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:22
#: includes/admin/menu-pages/booking-rules-menu-page.php:210
#: includes/admin/menu-pages/booking-rules-menu-page.php:255
msgid "Days"
msgstr "Дни"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:23
#: includes/post-types/season-cpt.php:67
msgid "Repeat"
msgstr "Повторить"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:11
msgid "Services are extra offers that you can sell or give for free. E.g. Thai massage, transfer, babysitting. Guests can pre-order them when placing a booking."
msgstr "Услуги - это сервисы и предложения, которые Вы можете продавать дополнительно или предлагать бесплатно. Например, сеанс массажа, трансфер, услуги няни. Гости могут добавлять их к своему заказу во время бронирования."

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:17
#: includes/post-types/service-cpt.php:150
msgid "Periodicity"
msgstr "Частота"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:18
#: includes/post-types/service-cpt.php:206
msgid "Charge"
msgstr "Взимать плату"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:41
#: includes/entities/service.php:193
#: includes/post-types/service-cpt.php:153
msgid "Per Day"
msgstr "За сутки"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:43
#: includes/post-types/service-cpt.php:154
msgid "Guest Choice"
msgstr "Выбор гостя"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:45
#: includes/entities/service.php:197
#: includes/post-types/service-cpt.php:152
msgid "Once"
msgstr "Одноразовый платёж"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:203
#: includes/post-types/service-cpt.php:209
msgid "Per Guest"
msgstr "За гостя"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:205
#: includes/post-types/service-cpt.php:208
msgid "Per Accommodation"
msgstr "С комнаты/номера"

#: includes/admin/manage-tax-pages/facility-manage-tax-page.php:11
msgid "These are accommodation amenities, generally free ones. E.g. air-conditioning, wifi."
msgstr "Это удобства в номере, обычно бесплатные (например: кондиционер, WiFi)."

#: includes/admin/menu-pages/booking-rules-menu-page.php:34
msgid "Booking rules saved."
msgstr "Правила бронирования сохранены."

#: includes/admin/menu-pages/booking-rules-menu-page.php:41
#: includes/admin/menu-pages/booking-rules-menu-page.php:602
#: includes/admin/menu-pages/booking-rules-menu-page.php:606
#: includes/admin/menu-pages/settings-menu-page.php:622
msgid "Booking Rules"
msgstr "Правила бронирования"

#: includes/admin/menu-pages/booking-rules-menu-page.php:90
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:70
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:118
#: templates/account/account-details.php:94
msgid "Save Changes"
msgstr "Сохранить изменения"

#: includes/admin/menu-pages/booking-rules-menu-page.php:199
msgid "Check-in days"
msgstr "Дни заселения"

#: includes/admin/menu-pages/booking-rules-menu-page.php:200
msgid "Guests can check in any day."
msgstr "Гости могут заселяться в любой день."

#: includes/admin/menu-pages/booking-rules-menu-page.php:201
#: includes/admin/menu-pages/booking-rules-menu-page.php:246
#: includes/admin/menu-pages/booking-rules-menu-page.php:291
#: includes/admin/menu-pages/booking-rules-menu-page.php:337
#: includes/admin/menu-pages/booking-rules-menu-page.php:383
#: includes/admin/menu-pages/booking-rules-menu-page.php:468
#: includes/admin/menu-pages/booking-rules-menu-page.php:514
#: includes/admin/menu-pages/booking-rules-menu-page.php:560
msgid "Add rule"
msgstr "Добавить правило"

#: includes/admin/menu-pages/booking-rules-menu-page.php:229
#: includes/admin/menu-pages/booking-rules-menu-page.php:274
#: includes/admin/menu-pages/booking-rules-menu-page.php:320
#: includes/admin/menu-pages/booking-rules-menu-page.php:366
#: includes/admin/menu-pages/booking-rules-menu-page.php:497
#: includes/admin/menu-pages/booking-rules-menu-page.php:543
#: includes/admin/menu-pages/booking-rules-menu-page.php:589
#: includes/post-types/season-cpt.php:98
#: includes/post-types/season-cpt.php:108
msgid "Seasons"
msgstr "Сезоны"

#: includes/admin/menu-pages/booking-rules-menu-page.php:244
msgid "Check-out days"
msgstr "Дни выселения"

#: includes/admin/menu-pages/booking-rules-menu-page.php:245
msgid "Guests can check out any day."
msgstr "Гости могут выселяться в любой день."

#: includes/admin/menu-pages/booking-rules-menu-page.php:289
#: includes/admin/menu-pages/booking-rules-menu-page.php:300
msgid "Minimum stay"
msgstr "Минимальное пребывание"

#: includes/admin/menu-pages/booking-rules-menu-page.php:290
msgid "There are no minimum stay rules."
msgstr "Нет ограничений минимального пребывания."

#: includes/admin/menu-pages/booking-rules-menu-page.php:301
#: includes/admin/menu-pages/booking-rules-menu-page.php:347
#: includes/admin/menu-pages/booking-rules-menu-page.php:478
#: includes/admin/menu-pages/booking-rules-menu-page.php:524
#: includes/admin/menu-pages/booking-rules-menu-page.php:570
msgid "nights"
msgstr "сутки(суток)"

#: includes/admin/menu-pages/booking-rules-menu-page.php:335
#: includes/admin/menu-pages/booking-rules-menu-page.php:346
msgid "Maximum stay"
msgstr "Максимальное пребывание"

#: includes/admin/menu-pages/booking-rules-menu-page.php:336
msgid "There are no maximum stay rules."
msgstr "Нет ограничений максимального пребывания."

#: includes/admin/menu-pages/booking-rules-menu-page.php:381
msgid "Block accommodation"
msgstr "Блокировка размещения"

#: includes/admin/menu-pages/booking-rules-menu-page.php:382
msgid "There are no blocking accommodation rules."
msgstr "Нет правил блокировки размещения."

#: includes/admin/menu-pages/booking-rules-menu-page.php:414
#: includes/bookings-calendar.php:723
#: includes/bookings-calendar.php:817
msgid "From"
msgstr "От"

#: includes/admin/menu-pages/booking-rules-menu-page.php:424
msgid "Till"
msgstr "До"

#: includes/admin/menu-pages/booking-rules-menu-page.php:434
msgid "Restriction"
msgstr "Ограничение"

#: includes/admin/menu-pages/booking-rules-menu-page.php:436
msgid "Not check-in rule marks the date as unavailable for check-in."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:437
msgid "Not check-out rule marks the date as unavailable for check-out."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:438
msgid "Not stay-in rule displays the date as blocked. This date is unavailable for check-in and check-out on the next date."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:439
msgid "Not stay-in with Not check-out rules completely block the selected date, additionally displaying the previous date as unavailable for check-in."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:444
#: includes/script-managers/public-script-manager.php:208
msgid "Not check-in"
msgstr "Заселение запрещено"

#: includes/admin/menu-pages/booking-rules-menu-page.php:445
#: includes/script-managers/public-script-manager.php:209
msgid "Not check-out"
msgstr "Выселение запрещено"

#: includes/admin/menu-pages/booking-rules-menu-page.php:446
#: includes/script-managers/public-script-manager.php:207
msgid "Not stay-in"
msgstr "Пребывание запрещено"

#: includes/admin/menu-pages/booking-rules-menu-page.php:454
msgid "Comment"
msgstr "Комментарий"

#: includes/admin/menu-pages/booking-rules-menu-page.php:466
#: includes/admin/menu-pages/booking-rules-menu-page.php:477
msgid "Minimum advance reservation"
msgstr "Минимальное предварительное бронирование"

#: includes/admin/menu-pages/booking-rules-menu-page.php:467
msgid "There are no minimum advance reservation rules."
msgstr "Правил минимального предварительного бронирования не существует."

#: includes/admin/menu-pages/booking-rules-menu-page.php:512
#: includes/admin/menu-pages/booking-rules-menu-page.php:523
msgid "Maximum advance reservation"
msgstr "Максимальное предварительное бронирование"

#: includes/admin/menu-pages/booking-rules-menu-page.php:513
msgid "There are no maximum advance reservation rules."
msgstr "Правил максимального предварительного бронирования не существует."

#: includes/admin/menu-pages/booking-rules-menu-page.php:558
#: includes/admin/menu-pages/booking-rules-menu-page.php:569
msgid "Booking buffer"
msgstr "Буфер бронирования"

#: includes/admin/menu-pages/booking-rules-menu-page.php:559
msgid "There are no booking buffer rules."
msgstr "Нет правил буфера бронирования."

#: includes/admin/menu-pages/calendar-menu-page.php:41
#: includes/admin/menu-pages/calendar-menu-page.php:69
msgid "Booking Calendar"
msgstr "Календарь бронирований"

#: includes/admin/menu-pages/calendar-menu-page.php:65
msgid "Calendar"
msgstr "Календарь"

#: includes/admin/menu-pages/create-booking-menu-page.php:135
#: includes/admin/menu-pages/create-booking-menu-page.php:169
#: includes/post-types/booking-cpt.php:244
msgid "Add New Booking"
msgstr "Добавить новое бронирование"

#: includes/admin/menu-pages/create-booking-menu-page.php:136
msgid "Clear Search Results"
msgstr "Очистить результаты поиска"

#: includes/admin/menu-pages/create-booking-menu-page.php:184
#: includes/admin/menu-pages/edit-booking-menu-page.php:69
msgid "Note: booking rules are disabled in the plugin settings and are not taken into account."
msgstr "Примечание: правила бронирования отключены в настройках плагина и не принимаются во внимание."

#: includes/admin/menu-pages/create-booking/booking-step.php:50
#: includes/shortcodes/checkout-shortcode/step-booking.php:478
msgid "Unable to create booking. Please try again."
msgstr "Невозможно создать бронирование. Пожалуйста, попробуйте еще раз."

#: includes/admin/menu-pages/create-booking/booking-step.php:74
#: includes/shortcodes/checkout-shortcode/step-booking.php:107
msgid "Booking is blocked due to maintenance reason. Please try again later."
msgstr "Бронирование недоступно по причине ремонта. Пожалуйста, попробуйте позже."

#: includes/admin/menu-pages/create-booking/booking-step.php:121
#: includes/admin/menu-pages/create-booking/booking-step.php:275
#: includes/admin/menu-pages/create-booking/checkout-step.php:130
#: includes/admin/menu-pages/edit-booking/booking-control.php:34
#: includes/admin/menu-pages/edit-booking/checkout-control.php:61
#: includes/admin/menu-pages/edit-booking/summary-control.php:56
#: includes/shortcodes/checkout-shortcode/step-booking.php:183
#: includes/shortcodes/checkout-shortcode/step-booking.php:363
#: includes/shortcodes/checkout-shortcode/step-checkout.php:170
#: includes/utils/parse-utils.php:250
msgid "There are no accommodations selected for reservation."
msgstr "Не выбраны варианты размещения для бронирования."

#: includes/admin/menu-pages/create-booking/booking-step.php:123
#: includes/admin/menu-pages/create-booking/booking-step.php:155
#: includes/admin/menu-pages/create-booking/checkout-step.php:132
#: includes/admin/menu-pages/create-booking/checkout-step.php:165
#: includes/admin/menu-pages/create-booking/checkout-step.php:196
#: includes/utils/parse-utils.php:210
#: includes/utils/parse-utils.php:285
#: includes/utils/parse-utils.php:305
msgid "Selected accommodations are not valid."
msgstr "Выбранные номера недействительны."

#: includes/admin/menu-pages/create-booking/booking-step.php:150
#: includes/admin/menu-pages/create-booking/checkout-step.php:160
#: includes/admin/menu-pages/create-booking/step.php:191
#: includes/ajax.php:612
#: includes/shortcodes/checkout-shortcode/step-booking.php:200
#: includes/shortcodes/checkout-shortcode/step-booking.php:207
#: includes/shortcodes/checkout-shortcode/step-checkout.php:187
#: includes/shortcodes/checkout-shortcode/step-checkout.php:199
#: includes/utils/parse-utils.php:301
msgid "Accommodation Type is not valid."
msgstr "Тип размещения недействителен."

#: includes/admin/menu-pages/create-booking/booking-step.php:160
#: includes/admin/menu-pages/create-booking/booking-step.php:184
#: includes/ajax.php:623
#: includes/shortcodes/checkout-shortcode/step-booking.php:213
#: includes/shortcodes/checkout-shortcode/step-booking.php:231
#: includes/utils/parse-utils.php:322
msgid "Rate is not valid."
msgstr "Тариф недействителен."

#: includes/admin/menu-pages/create-booking/booking-step.php:189
#: includes/admin/menu-pages/create-booking/step.php:211
#: includes/admin/menu-pages/create-booking/step.php:215
#: includes/shortcodes/checkout-shortcode/step-booking.php:237
#: includes/shortcodes/search-results-shortcode.php:634
#: includes/utils/parse-utils.php:163
#: includes/utils/parse-utils.php:326
msgid "Adults number is not valid."
msgstr "Количество взрослых неверно."

#: includes/admin/menu-pages/create-booking/booking-step.php:194
#: includes/admin/menu-pages/create-booking/step.php:235
#: includes/admin/menu-pages/create-booking/step.php:239
#: includes/ajax.php:500
#: includes/shortcodes/checkout-shortcode/step-booking.php:243
#: includes/shortcodes/search-results-shortcode.php:650
#: includes/utils/parse-utils.php:187
#: includes/utils/parse-utils.php:330
msgid "Children number is not valid."
msgstr "Количество детей неверно."

#: includes/admin/menu-pages/create-booking/booking-step.php:199
#: includes/ajax.php:634
#: includes/shortcodes/checkout-shortcode/step-booking.php:248
#: includes/utils/parse-utils.php:334
msgid "The total number of guests is not valid."
msgstr "Общее количество гостей неверно."

#: includes/admin/menu-pages/create-booking/booking-step.php:210
#: includes/admin/menu-pages/create-booking/checkout-step.php:181
#: includes/shortcodes/checkout-shortcode/step-booking.php:259
#: includes/shortcodes/checkout-shortcode/step-checkout.php:245
#: includes/utils/parse-utils.php:345
msgid "Selected dates do not meet booking rules for type %s"
msgstr "Выбранные даты не соответствуют правилам бронирования для типа %s"

#: includes/admin/menu-pages/create-booking/booking-step.php:263
#: includes/admin/menu-pages/create-booking/checkout-step.php:186
#: includes/utils/parse-utils.php:264
msgid "Accommodations are not available."
msgstr "Размещение недоступно."

#: includes/admin/menu-pages/create-booking/checkout-step.php:170
#: includes/shortcodes/checkout-shortcode/step-checkout.php:234
msgid "There are no rates for requested dates."
msgstr "Нет тарифов для выбранных дат."

#: includes/admin/menu-pages/create-booking/results-step.php:211
#: includes/admin/menu-pages/settings-menu-page.php:542
#: includes/wizard.php:93
msgid "Search Results"
msgstr "Результаты поиска"

#: includes/admin/menu-pages/create-booking/search-step.php:47
#: includes/admin/menu-pages/create-booking/search-step.php:50
msgid "— Any —"
msgstr "— Любой —"

#: includes/admin/menu-pages/create-booking/step.php:34
msgid "Search parameters are not set."
msgstr "Параметры поиска не заданы."

#: includes/admin/menu-pages/create-booking/step.php:129
#: includes/ajax.php:438
#: includes/script-managers/public-script-manager.php:223
#: includes/shortcodes/checkout-shortcode/step.php:53
#: includes/shortcodes/search-results-shortcode.php:665
#: includes/utils/parse-utils.php:87
msgid "Check-in date is not valid."
msgstr "Дата заезда неверна."

#: includes/admin/menu-pages/create-booking/step.php:131
#: includes/shortcodes/checkout-shortcode/step.php:56
#: includes/shortcodes/search-results-shortcode.php:668
#: includes/utils/parse-utils.php:89
msgid "Check-in date cannot be earlier than today."
msgstr "Дата заезда не может быть раньше, чем сегодня."

#: includes/admin/menu-pages/create-booking/step.php:157
#: includes/ajax.php:457
#: includes/script-managers/public-script-manager.php:224
#: includes/shortcodes/checkout-shortcode/step.php:90
#: includes/shortcodes/search-results-shortcode.php:686
#: includes/utils/parse-utils.php:120
msgid "Check-out date is not valid."
msgstr "Дата отъезда неверна."

#: includes/admin/menu-pages/create-booking/step.php:168
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:106
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:210
#: includes/shortcodes/checkout-shortcode/step.php:101
#: includes/shortcodes/search-results-shortcode.php:698
#: includes/utils/parse-utils.php:131
msgid "Nothing found. Please try again with different search parameters."
msgstr "Ничего не найдено. Пожалуйста, попробуйте задать другие параметры поиска."

#: includes/admin/menu-pages/customers-menu-page.php:54
msgid "Sorry, you are not allowed to access this page."
msgstr "Извините, вам не разрешен доступ к этой странице."

#: includes/admin/menu-pages/customers-menu-page.php:160
msgid "User ID"
msgstr "ID пользователя"

#: includes/admin/menu-pages/customers-menu-page.php:171
#: templates/account/account-details.php:30
msgid "Username"
msgstr "Имя пользователя"

#: includes/admin/menu-pages/customers-menu-page.php:183
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:114
#: includes/bundles/customer-bundle.php:92
#: includes/csv/bookings/bookings-exporter-helper.php:81
#: includes/post-types/booking-cpt.php:90
#: includes/post-types/payment-cpt.php:247
#: includes/views/shortcodes/checkout-view.php:588
#: templates/account/account-details.php:22
msgid "First Name"
msgstr "Имя"

#: includes/admin/menu-pages/customers-menu-page.php:195
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:118
#: includes/bundles/customer-bundle.php:101
#: includes/csv/bookings/bookings-exporter-helper.php:82
#: includes/post-types/booking-cpt.php:98
#: includes/post-types/payment-cpt.php:255
#: includes/views/shortcodes/checkout-view.php:603
#: templates/account/account-details.php:26
msgid "Last Name"
msgstr "Фамилия"

#: includes/admin/menu-pages/customers-menu-page.php:219
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:126
#: includes/bundles/customer-bundle.php:119
#: includes/csv/bookings/bookings-exporter-helper.php:84
#: includes/post-types/booking-cpt.php:114
#: includes/post-types/payment-cpt.php:271
#: includes/views/shortcodes/checkout-view.php:633
#: templates/account/account-details.php:38
msgid "Phone"
msgstr "Телефон"

#: includes/admin/menu-pages/customers-menu-page.php:231
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:134
#: includes/bundles/customer-bundle.php:137
#: includes/csv/bookings/bookings-exporter-helper.php:86
#: includes/post-types/booking-cpt.php:131
#: includes/views/shortcodes/checkout-view.php:675
#: templates/account/account-details.php:42
msgid "Address"
msgstr "Адрес"

#: includes/admin/menu-pages/customers-menu-page.php:243
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:142
#: includes/bundles/customer-bundle.php:155
#: includes/csv/bookings/bookings-exporter-helper.php:88
#: includes/post-types/booking-cpt.php:147
#: includes/post-types/payment-cpt.php:311
#: includes/views/shortcodes/checkout-view.php:705
#: templates/account/account-details.php:64
msgid "State / County"
msgstr "Область/регион"

#: includes/admin/menu-pages/customers-menu-page.php:255
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:130
#: includes/csv/bookings/bookings-exporter-helper.php:85
#: includes/post-types/booking-cpt.php:123
#: includes/post-types/payment-cpt.php:279
#: templates/account/account-details.php:46
msgid "Country"
msgstr "Страна"

#: includes/admin/menu-pages/customers-menu-page.php:268
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:138
#: includes/bundles/customer-bundle.php:146
#: includes/csv/bookings/bookings-exporter-helper.php:87
#: includes/post-types/booking-cpt.php:139
#: includes/post-types/payment-cpt.php:303
#: includes/views/shortcodes/checkout-view.php:690
#: templates/account/account-details.php:68
msgid "City"
msgstr "Город"

#: includes/admin/menu-pages/customers-menu-page.php:280
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:146
#: includes/bundles/customer-bundle.php:164
#: includes/csv/bookings/bookings-exporter-helper.php:89
#: includes/post-types/booking-cpt.php:155
#: includes/views/shortcodes/checkout-view.php:720
#: templates/account/account-details.php:72
msgid "Postcode"
msgstr "Индекс"

#: includes/admin/menu-pages/customers-menu-page.php:301
#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/i-cal-import-menu-page.php:162
#: includes/admin/menu-pages/i-cal-import-menu-page.php:209
#: includes/admin/menu-pages/i-cal-menu-page.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:40
#: includes/post-types/editable-cpt.php:95
msgid "Back"
msgstr "Назад"

#: includes/admin/menu-pages/customers-menu-page.php:305
msgid "Edit User Profile"
msgstr "Изменить профиль пользователя"

#: includes/admin/menu-pages/customers-menu-page.php:322
msgid "Customer data updated."
msgstr "Данные клиента обновлены."

#: includes/admin/menu-pages/customers-menu-page.php:328
msgid "User account updated."
msgstr "Учетная запись обновлена."

#: includes/admin/menu-pages/customers-menu-page.php:363
#: includes/admin/menu-pages/i-cal-menu-page.php:150
msgid "Update"
msgstr "Обновить"

#: includes/admin/menu-pages/customers-menu-page.php:369
#: includes/admin/menu-pages/customers-menu-page.php:382
#: includes/admin/menu-pages/customers-menu-page.php:386
msgid "Customers"
msgstr "Клиенты"

#: includes/admin/menu-pages/edit-booking-menu-page.php:80
msgid "The booking is not set."
msgstr "Бронирование не установлено."

#: includes/admin/menu-pages/edit-booking-menu-page.php:88
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:49
msgid "The booking not found."
msgstr "Бронирование не найдено."

#: includes/admin/menu-pages/edit-booking-menu-page.php:140
msgid "Edit Booking #%d"
msgstr "Редактировать бронирование #%d"

#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/reports-menu-page.php:201
msgid "Cancel"
msgstr "Отменить"

#: includes/admin/menu-pages/edit-booking-menu-page.php:227
#: includes/admin/menu-pages/edit-booking-menu-page.php:234
#: includes/post-types/booking-cpt.php:245
msgid "Edit Booking"
msgstr "Редактировать бронирование"

#: includes/admin/menu-pages/edit-booking/booking-control.php:18
#: includes/admin/menu-pages/edit-booking/checkout-control.php:35
#: includes/admin/menu-pages/edit-booking/edit-control.php:56
#: includes/admin/menu-pages/edit-booking/summary-control.php:31
msgid "You cannot edit the imported booking. Please update the source booking and resync your calendars."
msgstr "Вы не можете редактировать импортированное бронирование. Пожалуйста, обновите первоначальное бронирование и повторно синхронизируйте календари."

#: includes/admin/menu-pages/edit-booking/booking-control.php:22
#: includes/ajax-api/ajax-actions/abstract-ajax-api-action.php:142
#: includes/ajax.php:184
msgid "Request does not pass security verification. Please refresh the page and try one more time."
msgstr "Запрос не прошёл проверку безопасности. Пожалуйста, обновите страницу и попробуйте ещё раз."

#: includes/admin/menu-pages/edit-booking/booking-control.php:26
#: includes/admin/menu-pages/edit-booking/checkout-control.php:40
#: includes/admin/menu-pages/edit-booking/summary-control.php:33
#: includes/utils/parse-utils.php:233
msgid "Check-in date is not set."
msgstr "Не указана дата заезда."

#: includes/admin/menu-pages/edit-booking/booking-control.php:30
#: includes/admin/menu-pages/edit-booking/checkout-control.php:42
#: includes/admin/menu-pages/edit-booking/summary-control.php:35
#: includes/utils/parse-utils.php:235
msgid "Check-out date is not set."
msgstr "Не указана дата отъезда."

#: includes/admin/menu-pages/edit-booking/booking-control.php:72
msgid "Unable to update booking. Please try again."
msgstr "Невозможно обновить бронирование. Попробуйте еще раз."

#: includes/admin/menu-pages/edit-booking/booking-control.php:75
msgid "Booking was edited."
msgstr "Бронирование отредактировано"

#: includes/admin/menu-pages/edit-booking/edit-control.php:94
#: includes/script-managers/public-script-manager.php:203
#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Available"
msgstr "Доступно"

#: includes/admin/menu-pages/edit-booking/edit-control.php:96
#: templates/edit-booking/edit-reserved-rooms.php:71
msgid "Replace"
msgstr "Заменить"

#: includes/admin/menu-pages/edit-booking/summary-control.php:148
msgid "— Add new —"
msgstr "— Добавить новое —"

#: includes/admin/menu-pages/extensions-menu-page.php:137
#: includes/admin/menu-pages/extensions-menu-page.php:185
#: includes/admin/menu-pages/extensions-menu-page.php:190
#: includes/admin/menu-pages/settings-menu-page.php:1192
msgid "Extensions"
msgstr "Дополнения"

#: includes/admin/menu-pages/extensions-menu-page.php:140
msgid "Extend the functionality of Hotel Booking plugin with the number of helpful addons for your custom purposes."
msgstr "Расширьте функциональность плагина Hotel Booking с помощью многоцелевых дополнений для Ваших нужд."

#: includes/admin/menu-pages/extensions-menu-page.php:170
msgid "Get this Extension"
msgstr "Получить это расширение."

#: includes/admin/menu-pages/extensions-menu-page.php:178
msgid "No extensions found."
msgstr "Расширения не найдены. "

#: includes/admin/menu-pages/i-cal-import-menu-page.php:80
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:60
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:102
#: includes/i-cal/logs-handler.php:73
msgid "Abort Process"
msgstr "Прервать процесс"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:81
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:61
msgid "Aborting..."
msgstr "Прерываем..."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:161
#: includes/admin/menu-pages/i-cal-import-menu-page.php:210
#: includes/admin/menu-pages/i-cal-import-menu-page.php:224
#: includes/admin/room-list-table.php:156
msgid "Import Calendar"
msgstr "Импортировать календарь"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:166
msgid "Accommodation: %s"
msgstr "Вариант размещения: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:169
msgid "Accommodation Type: %s"
msgstr "Тип размещения: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:176
msgid "Please be patient while the calendars are imported. You will be notified via this page when the process is completed."
msgstr "Пожалуйста, подождите пока календари импортируются. Вы будете уведомлены на этой странице о завершении процесса."

#: includes/admin/menu-pages/i-cal-menu-page.php:67
msgid "Accommodation updated."
msgstr "Вариант размещения обновлён."

#: includes/admin/menu-pages/i-cal-menu-page.php:73
msgid "This calendar has already been imported for another accommodation."
msgstr "Этот календарь уже был импортирован для другого жилья."

#: includes/admin/menu-pages/i-cal-menu-page.php:103
msgid "Sync, Import and Export Calendars"
msgstr "Синхронизируйте, импортируйте и экспортируйте календари"

#. translators: %s - room name. Example: "Comfort Triple 1"
#: includes/admin/menu-pages/i-cal-menu-page.php:113
msgid "Edit External Calendars of \"%s\""
msgstr "Редактировать внешние календари \"%s\""

#: includes/admin/menu-pages/i-cal-menu-page.php:122
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:101
msgid "Sync All External Calendars"
msgstr "Синхронизировать все внешние календари"

#: includes/admin/menu-pages/i-cal-menu-page.php:123
msgid "Sync your bookings across all online channels like Booking.com, TripAdvisor, Airbnb etc. via iCalendar file format."
msgstr "Синхронизируйте все бронирования с каналами типа Booking.com, TripAdvisor, Airbnb, и т.д. с помощью iCalendar."

#: includes/admin/menu-pages/i-cal-menu-page.php:219
msgid "Calendar URL"
msgstr "URL календаря"

#: includes/admin/menu-pages/i-cal-menu-page.php:225
msgid "Add New Calendar"
msgstr "Добавить новый календарь"

#: includes/admin/menu-pages/i-cal-menu-page.php:233
#: includes/admin/menu-pages/i-cal-menu-page.php:237
msgid "Sync Calendars"
msgstr "Синхронизировать календари"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:62
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:103
#: includes/i-cal/logs-handler.php:83
msgid "Delete All Logs"
msgstr "Удалить все логи"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:63
msgid "Deleting..."
msgstr "Удаление..."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:64
msgid "%d item"
msgstr "%d элемент"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:65
msgid "%d items"
msgstr "%d элементов"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:91
msgid "Calendars Synchronization Status"
msgstr "Статус синхронизации календарей"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:92
msgid "Here you can see synchronization status of your external calendars."
msgstr "Здесь вы можете увидеть статус синхронизации ваших внешних календарей."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:134
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:148
msgid "Calendars Sync Status"
msgstr "Статус синхронизации календарей"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:151
msgid "Display calendars synchronization status."
msgstr "Отобразить статус синхронизации календарей."

#: includes/admin/menu-pages/language-menu-page.php:10
#: includes/admin/menu-pages/language-menu-page.php:37
msgid "Language Guide"
msgstr "Руководство по языкам"

#: includes/admin/menu-pages/language-menu-page.php:11
msgid "Default language"
msgstr "Язык по умолчанию"

#: includes/admin/menu-pages/language-menu-page.php:13
msgid "This plugin will display all system messages, labels, buttons in the language set in <em>General > Settings > Site Language</em>. If the plugin is not available in your language, you may <a href=\"%s\">contribute your translation</a>."
msgstr "Этот плагин будет отображать все системные сообщения, названия и кнопки на языке, установленном в <em>Общие> Настройки >Язык сайта</em>. Если плагин недоступен на Вашем языке, Вы можете <a href=\"%s\">сделать свой перевод</a>."

#: includes/admin/menu-pages/language-menu-page.php:14
msgid "Custom translations and edits"
msgstr "Ваши переводы и изменения"

#: includes/admin/menu-pages/language-menu-page.php:15
msgid "You may customize plugin translation by editing the needed texts or adding your translation following these steps:"
msgstr "Вы можете отредактировать тексты плагина соответственно Вашему типу жилья или добавить Вашу версию перевода следующим образом:"

#: includes/admin/menu-pages/language-menu-page.php:17
msgid "Take the source file for your translations %s or needed translated locale."
msgstr "Возьмите исходный файл для перевода (на английском) %s или необходимую уже переведённую версию."

#: includes/admin/menu-pages/language-menu-page.php:18
msgid "Translate texts with any translation program like Poedit, Loco, Pootle etc."
msgstr "Переведите тексты с помощью любой программы для переводов, например, Poedit, Loco, Pootle, и т.д."

#: includes/admin/menu-pages/language-menu-page.php:19
msgid "Put created .mo file with your translations into the folder %s. Where {lang} is ISO-639 language code and {country} is ISO-3166 country code. Example: Brazilian Portuguese file would be called motopress-hotel-booking-pt_BR.mo."
msgstr "Загрузите созданный .mo файл с Вашим переводом в папку %s. Где {lang} это ISO-639 код языка и {country} это ISO-3166 код страны. Например, файл с португальским языком Бразилии будет называться motopress-hotel-booking-pt_BR.mo."

#: includes/admin/menu-pages/language-menu-page.php:22
msgid "Multilingual content"
msgstr "Мультиязычный сайт"

#: includes/admin/menu-pages/language-menu-page.php:23
msgid "If your site is multilingual, you may use additional plugins to translate your added content into multiple languages allowing the site visitors to switch them."
msgstr "Если Ваш сайт доступен на нескольких языках, Вы можете использовать дополнительные плагины для перевода текста на другие языки. Плагин будет выводить Ваши переводы при переключении языка посетителем сайта."

#: includes/admin/menu-pages/language-menu-page.php:33
msgid "Language"
msgstr "Язык"

#: includes/admin/menu-pages/reports-menu-page.php:52
#: includes/admin/menu-pages/reports-menu-page.php:211
#: includes/admin/menu-pages/reports-menu-page.php:215
msgid "Reports"
msgstr "Отчёты"

#: includes/admin/menu-pages/reports-menu-page.php:55
#: includes/admin/room-list-table.php:94
msgid "Export"
msgstr "Экспорт"

#: includes/admin/menu-pages/reports-menu-page.php:132
#: includes/bookings-calendar.php:695
msgid "All Statuses"
msgstr "Все статусы"

#: includes/admin/menu-pages/reports-menu-page.php:138
msgid "Booking dates between"
msgstr "Даты бронирования между"

#: includes/admin/menu-pages/reports-menu-page.php:139
msgid "Check-in date between"
msgstr "Дата заезда между"

#: includes/admin/menu-pages/reports-menu-page.php:140
msgid "Check-out date between"
msgstr "Дата отъезда между"

#: includes/admin/menu-pages/reports-menu-page.php:141
msgid "In-house between"
msgstr "Время пребывания между"

#: includes/admin/menu-pages/reports-menu-page.php:142
msgid "Date of reservation between"
msgstr "Дата бронирования между"

#: includes/admin/menu-pages/reports-menu-page.php:152
msgid "Export Bookings"
msgstr "Экспортировать бронирования"

#: includes/admin/menu-pages/reports-menu-page.php:164
msgid "Choose start date"
msgstr "Выберите дату заезда"

#: includes/admin/menu-pages/reports-menu-page.php:165
msgid "Choose end date"
msgstr "Выберите дату отъезда"

#: includes/admin/menu-pages/reports-menu-page.php:171
msgid "Also export imported bookings"
msgstr ""

#: includes/admin/menu-pages/reports-menu-page.php:175
msgid "Select columns to export"
msgstr "Выберите столбцы для экспортирования"

#: includes/admin/menu-pages/reports-menu-page.php:185
msgid "Generate CSV"
msgstr "Создать CSV"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:25
msgid "Number of accommodations"
msgstr "Количество вариантов размещения (номеров)"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:57
#: includes/payments/gateways/gateway.php:494
#: includes/widgets/rooms-widget.php:185
#: templates/create-booking/results/reserve-rooms.php:35
#: assets/blocks/blocks.js:436
#: assets/blocks/blocks.js:686
#: assets/blocks/blocks.js:1215
msgid "Title"
msgstr "Название"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:61
msgid "Leave empty to use accommodation type title."
msgstr "Оставьте пустым, чтобы использовать название типа размещения."

#: includes/admin/menu-pages/rooms-generator-menu-page.php:66
msgid "Generate"
msgstr "Сгенерировать"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:75
msgid "Accommodation generated."
msgid_plural "%s accommodations generated."
msgstr[0] "Вариант размещения сгенерирован."
msgstr[1] "%s варианта размещения сгенерированы."
msgstr[2] "%s вариантов размещения сгенерировано."
msgstr[3] ""

#: includes/admin/menu-pages/settings-menu-page.php:113
msgid "General"
msgstr "Общие"

#: includes/admin/menu-pages/settings-menu-page.php:116
msgid "Pages"
msgstr "Страницы"

#: includes/admin/menu-pages/settings-menu-page.php:123
msgid "Search Results Page"
msgstr "Страница результатов поиска"

#: includes/admin/menu-pages/settings-menu-page.php:124
msgid "Select page to display search results. Use search results shortcode on this page."
msgstr "Выберите страницу для отображения результатов поиска. Используйте шорткод \"Результаты поиска\" на этой странице."

#: includes/admin/menu-pages/settings-menu-page.php:132
msgid "Checkout Page"
msgstr "Страница оформления заказа"

#: includes/admin/menu-pages/settings-menu-page.php:133
msgid "Select page user will be redirected to complete booking."
msgstr "Выберите страницу, на которую клиент будет перенаправлен для завершения бронирования."

#: includes/admin/menu-pages/settings-menu-page.php:141
msgid "Terms & Conditions"
msgstr "Правила и условия"

#: includes/admin/menu-pages/settings-menu-page.php:142
msgid "If you define a \"Terms\" page the customer will be asked if they accept them when checking out."
msgstr "Если вы укажете страницу «Правил и условий», то клиент должен будет принять их перед оплатой/завершением бронирования."

#: includes/admin/menu-pages/settings-menu-page.php:150
msgid "Open the Terms & Conditions page in a new window"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:151
msgid "By enabling this option you can avoid errors related to displaying your terms & conditions inline for website pages created in page builders."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:159
msgid "My Account Page"
msgstr "Страница Мой аккаунт"

#: includes/admin/menu-pages/settings-menu-page.php:160
msgid "Select a page to display user account. Use the customer account shortcode on this page."
msgstr "Выберите страницу для отображения аккаунта пользователя. Используйте шорткод аккаунта клиента на этой странице."

#: includes/admin/menu-pages/settings-menu-page.php:170
#: includes/admin/menu-pages/settings-menu-page.php:177
#: includes/post-types/payment-cpt.php:205
msgid "Currency"
msgstr "Валюта"

#: includes/admin/menu-pages/settings-menu-page.php:186
msgid "Currency Position"
msgstr "Расположение иконки валюты"

#: includes/admin/menu-pages/settings-menu-page.php:195
msgid "Decimal Separator"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:204
msgid "Thousand Separator"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:214
msgid "Number of Decimals"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:226
msgid "Misc"
msgstr "Разное"

#: includes/admin/menu-pages/settings-menu-page.php:233
msgid "Square Units"
msgstr "Единицы измерения площади"

#: includes/admin/menu-pages/settings-menu-page.php:242
msgid "Datepicker Date Format"
msgstr "Формат выбора даты"

#: includes/admin/menu-pages/settings-menu-page.php:251
#: includes/emails/templaters/email-templater.php:148
msgid "Check-out Time"
msgstr "Время отъезда"

#: includes/admin/menu-pages/settings-menu-page.php:259
#: includes/emails/templaters/email-templater.php:144
msgid "Check-in Time"
msgstr "Время заезда"

#: includes/admin/menu-pages/settings-menu-page.php:267
msgid "Bed Types"
msgstr "Типы кроватей"

#: includes/admin/menu-pages/settings-menu-page.php:274
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:155
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:251
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:338
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:409
#: includes/post-types/attributes-cpt.php:365
#: includes/post-types/coupon-cpt.php:79
#: includes/post-types/coupon-cpt.php:111
#: includes/post-types/coupon-cpt.php:156
msgid "Type"
msgstr "Тип"

#: includes/admin/menu-pages/settings-menu-page.php:279
msgid "Add Bed Type"
msgstr "Добавить тип кровати"

#: includes/admin/menu-pages/settings-menu-page.php:286
msgid "Show Lowest Price for"
msgstr "Показывать самую низкую цену за"

#: includes/admin/menu-pages/settings-menu-page.php:287
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:185
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:281
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:367
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:438
msgid "days"
msgstr "день/дней"

#: includes/admin/menu-pages/settings-menu-page.php:291
msgid "Lowest price of accommodation for selected number of days if check-in and check-out dates are not set. Example: set 0 to display today's lowest price, set 7 to display the lowest price for the next week."
msgstr "Этой цифрой вы задаете диапазон дней, из которого система автоматически выберет самую низкую стоимость проживания для отображения на сайте, если даты заезда и отъезда не выбраны. Например: выберите 0, чтобы показать самую низкую стоимость на момент поиска; выберите 7, чтобы показать самую низкую стоимость в пределах следующей недели (семи дней)."

#: includes/admin/menu-pages/settings-menu-page.php:298
#: includes/post-types/coupon-cpt.php:288
msgid "Coupons"
msgstr "Купоны"

#: includes/admin/menu-pages/settings-menu-page.php:308
msgid "Default calendar view"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:309
msgid "Initial display format of the administrator bookings calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:317
msgid "Text on Checkout"
msgstr "Текст на странице оформления заказа"

#: includes/admin/menu-pages/settings-menu-page.php:318
msgid "This text will appear on the checkout page."
msgstr "Этот текст появится на странице оформления заказа."

#: includes/admin/menu-pages/settings-menu-page.php:329
msgid "Disable Booking"
msgstr "Отключить возможность бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:336
msgid "Hide reservation forms and buttons"
msgstr "Скрыть формы бронирования и кнопки"

#: includes/admin/menu-pages/settings-menu-page.php:345
msgid "Text instead of reservation form while booking is disabled"
msgstr "Показать этот текст вместо формы бронирования пока бронирование недоступно"

#: includes/admin/menu-pages/settings-menu-page.php:356
#: includes/admin/menu-pages/shortcodes-menu-page.php:510
#: includes/wizard.php:115
#: assets/blocks/blocks.js:1562
#: assets/blocks/blocks.js:1592
msgid "Booking Confirmation"
msgstr "Подтверждение бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:363
msgid "Confirmation Mode"
msgstr "Режим подтверждения бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:365
msgid "By customer via email"
msgstr "Клиентом по почте"

#: includes/admin/menu-pages/settings-menu-page.php:366
msgid "By admin manually"
msgstr "Администратором вручную"

#: includes/admin/menu-pages/settings-menu-page.php:367
msgid "Confirmation upon payment"
msgstr "Подтверждение после оплаты"

#: includes/admin/menu-pages/settings-menu-page.php:376
msgid "Booking Confirmed Page"
msgstr "Страница \"Бронирование подтверждено\""

#: includes/admin/menu-pages/settings-menu-page.php:377
msgid "Page user will be redirected to once the booking is confirmed via email or by admin."
msgstr "Страница, на которую попадает пользователь после подтверждения бронирования по почте или администратором."

#: includes/admin/menu-pages/settings-menu-page.php:385
msgid "Approval Time for User"
msgstr "Время для подтверждения бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:386
msgid "Period of time in minutes the user is given to confirm booking via email. Unconfirmed bookings become Abandoned and accommodation status changes to Available."
msgstr "Период времени в минутах, за который пользователь должен подтвердить бронирование в письме. Неподтверждённое бронирование переходит в статус \"Заброшено\", а место размещения становится доступным для бронирования."

#: includes/admin/menu-pages/settings-menu-page.php:396
msgid "Country of residence field is required for reservation."
msgstr "Страна проживания обязательна для заполнения."

#: includes/admin/menu-pages/settings-menu-page.php:404
msgid "Full address fields are required for reservation."
msgstr "Все поля адреса обязательны для заполнения."

#: includes/admin/menu-pages/settings-menu-page.php:412
msgid "Customer information is required when placing admin bookings."
msgstr "Информация о клиенте необходима при добавлении бронирования администратором."

#: includes/admin/menu-pages/settings-menu-page.php:421
msgid "Default Country on Checkout"
msgstr "Страна по умолчанию на странице оформления бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:429
#: includes/emails/templaters/email-templater.php:199
#: includes/post-types/booking-cpt.php:194
#: includes/views/shortcodes/checkout-view.php:477
msgid "Price Breakdown"
msgstr "Разбивка цены"

#: includes/admin/menu-pages/settings-menu-page.php:430
msgid "Price breakdown unfolded by default."
msgstr "Разбивка цен по умолчанию развернута."

#: includes/admin/menu-pages/settings-menu-page.php:439
msgid "Accounts"
msgstr "Аккаунты"

#: includes/admin/menu-pages/settings-menu-page.php:446
msgid "Account creation"
msgstr "Создание аккаунта"

#: includes/admin/menu-pages/settings-menu-page.php:447
msgid "Automatically create an account for a user at checkout."
msgstr "Автоматически создавать аккаунт для пользователя при оформлении заказа."

#: includes/admin/menu-pages/settings-menu-page.php:455
msgid "Allow customers to create an account during checkout."
msgstr "Разрешить клиентам создавать аккаунт при оформлении заказа."

#: includes/admin/menu-pages/settings-menu-page.php:463
msgid "Allow customers to log into their existing account during checkout."
msgstr "Разрешить клиентам вход в существующий аккаунт при оформлении заказа."

#: includes/admin/menu-pages/settings-menu-page.php:472
#: includes/upgrader.php:751
#: includes/wizard.php:164
msgid "Booking Cancellation"
msgstr "Отмена бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:479
msgid "User can cancel booking via link provided inside email."
msgstr "Пользователь может отменить бронирование по ссылке в письме."

#: includes/admin/menu-pages/settings-menu-page.php:487
msgid "Booking Cancelation Page"
msgstr "Страница отмены бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:488
msgid "Page to confirm booking cancelation."
msgstr "Страница для подтверждения отмены бронирования."

#: includes/admin/menu-pages/settings-menu-page.php:496
msgid "Booking Canceled Page"
msgstr "Страница отмененного бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:497
msgid "Page to redirect to after a booking is canceled."
msgstr "Страница, на которую будет перенаправлен пользователь когда бронирование отменено."

#: includes/admin/menu-pages/settings-menu-page.php:506
msgid "Search Options"
msgstr "Настройки поиска"

#: includes/admin/menu-pages/settings-menu-page.php:515
msgid "Max Adults"
msgstr "Максимальное количество взрослых"

#: includes/admin/menu-pages/settings-menu-page.php:516
msgid "Maximum accommodation occupancy available in the Search Form."
msgstr "Максимально возможное количество гостей для заселения, отображающееся в форме поиска."

#: includes/admin/menu-pages/settings-menu-page.php:526
msgid "Max Children"
msgstr "Максимальное количество детей"

#: includes/admin/menu-pages/settings-menu-page.php:534
msgid "Age of Child"
msgstr "Возраст ребёнка"

#: includes/admin/menu-pages/settings-menu-page.php:535
msgid "Optional description of the \"Children\" field."
msgstr "Описание для поля \"Дети\" (необязательно)."

#: includes/admin/menu-pages/settings-menu-page.php:543
msgid "Limit search results based on the requested number of guests."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:551
msgid "Book button behavior on the search results page"
msgstr "Поведение кнопки \"Забронировать\" на странице результатов поиска"

#: includes/admin/menu-pages/settings-menu-page.php:552
msgid "Redirect to the checkout page immediately after successful addition to reservation."
msgstr "Перенаправить на страницу оформления бронирования сразу после успешного добавления в корзину."

#: includes/admin/menu-pages/settings-menu-page.php:560
msgid "Recommendation"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:561
msgid "Enable search form to recommend the best set of accommodations according to a number of guests."
msgstr "Разрешить плагину рекомендовать гостю варианты размещения на основе указанного количества человек в форме поиска."

#: includes/admin/menu-pages/settings-menu-page.php:570
msgid "Skip Search Results"
msgstr "Пропустить результаты поиска"

#: includes/admin/menu-pages/settings-menu-page.php:571
msgid "Skip search results page and enable direct booking from accommodation pages."
msgstr "Пропустить результаты поиска и включить прямое бронирование непосредственно со страниц жилья."

#: includes/admin/menu-pages/settings-menu-page.php:578
msgid "Direct Booking Form"
msgstr "Форма прямого бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:581
msgid "Show price for selected period"
msgstr "Показывать цену за выбранный период"

#: includes/admin/menu-pages/settings-menu-page.php:582
msgid "Show price together with adults and children fields"
msgstr "Показывать цену вместе с полями взрослы и дети"

#: includes/admin/menu-pages/settings-menu-page.php:592
msgid "Enable \"adults\" and \"children\" options for my website (default)."
msgstr "Включить параметры «Взрослые» и «Дети» для моего сайта (по умолчанию)."

#: includes/admin/menu-pages/settings-menu-page.php:593
msgid "Disable \"children\" option for my website (hide \"children\" field and use Guests label instead)."
msgstr "Отключить параметр «Дети» для моего сайта (скрыть поле «Дети» и вместо этого использовать параметр \"Гости\")."

#: includes/admin/menu-pages/settings-menu-page.php:594
msgid "Disable \"adults\" and \"children\" options for my website."
msgstr "Отключить параметры «Взрослые» и «Дети» для моего сайта."

#: includes/admin/menu-pages/settings-menu-page.php:597
msgid "Guest Management"
msgstr "Управление гостями"

#: includes/admin/menu-pages/settings-menu-page.php:598
msgid "Applies to frontend only."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:606
msgid "Hide \"adults\" and \"children\" fields within search availability forms."
msgstr "Скрыть поля \"Взрослые\" и \"Дети\" в формах поиска."

#: includes/admin/menu-pages/settings-menu-page.php:614
msgid "Remember the user's selected number of guests until the checkout page."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:623
msgid "Do not apply booking rules for admin bookings."
msgstr "Не применять правила бронирования для администратора."

#: includes/admin/menu-pages/settings-menu-page.php:631
msgid "Display Options"
msgstr "Параметры отображения"

#: includes/admin/menu-pages/settings-menu-page.php:638
msgid "Display gallery images of accommodation page in lightbox."
msgstr "Показывать галерею фотографий типа размещения в лайтбоксе."

#: includes/admin/menu-pages/settings-menu-page.php:645
#: includes/admin/menu-pages/shortcodes-menu-page.php:61
#: assets/blocks/blocks.js:250
#: assets/blocks/blocks.js:385
msgid "Availability Calendar"
msgstr "Календарь проверки наличия мест"

#: includes/admin/menu-pages/settings-menu-page.php:646
#: includes/admin/menu-pages/shortcodes-menu-page.php:76
#: assets/blocks/blocks.js:307
msgid "Display per-night prices in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:654
#: includes/admin/menu-pages/shortcodes-menu-page.php:82
#: assets/blocks/blocks.js:318
msgid "Truncate per-night prices in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:662
#: includes/admin/menu-pages/shortcodes-menu-page.php:88
#: assets/blocks/blocks.js:329
msgid "Display the currency sign in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:672
msgid "Calendar Theme"
msgstr "Тема календаря"

#: includes/admin/menu-pages/settings-menu-page.php:673
msgid "Select theme for an availability calendar."
msgstr "Выберите тему для календаря проверки наличия мест."

#: includes/admin/menu-pages/settings-menu-page.php:680
msgid "Template Mode"
msgstr "Режим шаблона"

#: includes/admin/menu-pages/settings-menu-page.php:682
msgid "Developer Mode"
msgstr "Режим разработчика"

#: includes/admin/menu-pages/settings-menu-page.php:683
msgid "Theme Mode"
msgstr "Режим темы"

#: includes/admin/menu-pages/settings-menu-page.php:685
msgid "Choose Theme Mode to display the content with the styles of your theme. Choose Developer Mode to control appearance of the content with custom page templates, actions and filters. This option can't be changed if your theme is initially integrated with the plugin."
msgstr "Выберите \"Режим темы\", чтобы отображать содержимое плагина в стилях Вашей WordPress темы. Выберите \"Режим разработчика\", чтобы менять внешний вид содержимого с помощью собственных шаблонов страниц, действий и фильтров. Эта опция не может быть изменена, если Ваша тема изначально интегрирована с Hotel Booking плагином."

#: includes/admin/menu-pages/settings-menu-page.php:698
msgid "More Styles"
msgstr "Больше стилей"

#: includes/admin/menu-pages/settings-menu-page.php:699
msgid "Extend the styling options of Hotel Booking plugin with the new free addon - Hotel Booking Styles."
msgstr "Расширьте параметры стилизации плагина Hotel Booking с помощью нового бесплатного приложения Hotel Booking Styles."

#: includes/admin/menu-pages/settings-menu-page.php:711
msgid "Calendars Synchronization"
msgstr "Синхронизация календарей"

#: includes/admin/menu-pages/settings-menu-page.php:718
msgid "Export admin blocks."
msgstr "Экспортировать админ-блоки."

#: includes/admin/menu-pages/settings-menu-page.php:726
msgid "Do not export imported bookings."
msgstr "Не экспортировать импортированные бронирования. "

#: includes/admin/menu-pages/settings-menu-page.php:734
msgid "Export and import bookings with buffer time included."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:742
msgid "Minimize Logs"
msgstr "Минимизировать логи"

#: includes/admin/menu-pages/settings-menu-page.php:743
msgid "Enable the plugin to record only important messages."
msgstr "Включить плагин для записи только важных сообщений."

#: includes/admin/menu-pages/settings-menu-page.php:751
msgid "Calendars Synchronization Scheduler"
msgstr "График синхронизации календарей"

#: includes/admin/menu-pages/settings-menu-page.php:762
msgid "Enable automatic external calendars synchronization"
msgstr "Включить автоматическую синхронизацию внешних календарей"

#: includes/admin/menu-pages/settings-menu-page.php:771
msgid "Clock"
msgstr "Часы"

#: includes/admin/menu-pages/settings-menu-page.php:772
msgid "Sync calendars at this time (UTC) or starting at this time every interval below."
msgstr "Синхронизировать календари в это время (UTC) или начиная с этого времени с периодичностью указанной ниже."

#: includes/admin/menu-pages/settings-menu-page.php:783
msgid "Interval"
msgstr "Интервал"

#: includes/admin/menu-pages/settings-menu-page.php:785
#: includes/crons/cron-manager.php:102
msgid "Quarter an Hour"
msgstr "15 минут"

#: includes/admin/menu-pages/settings-menu-page.php:786
#: includes/crons/cron-manager.php:107
msgid "Half an Hour"
msgstr "Полчаса"

#: includes/admin/menu-pages/settings-menu-page.php:787
msgid "Once Hourly"
msgstr "Один раз в час"

#: includes/admin/menu-pages/settings-menu-page.php:788
msgid "Twice Daily"
msgstr "Два раза в день"

#: includes/admin/menu-pages/settings-menu-page.php:789
msgid "Once Daily"
msgstr "Один раз в день"

#: includes/admin/menu-pages/settings-menu-page.php:800
msgid "Automatically delete sync logs older than"
msgstr "Автоматически удалять журнал изменений по сроку давности более, чем"

#: includes/admin/menu-pages/settings-menu-page.php:802
msgid "Day"
msgstr "День"

#: includes/admin/menu-pages/settings-menu-page.php:803
msgid "Week"
msgstr "Неделя"

#: includes/admin/menu-pages/settings-menu-page.php:804
#: includes/bookings-calendar.php:575
msgid "Month"
msgstr "Месяц"

#: includes/admin/menu-pages/settings-menu-page.php:805
#: includes/bookings-calendar.php:576
msgid "Quarter"
msgstr "Квартал"

#: includes/admin/menu-pages/settings-menu-page.php:806
msgid "Half a Year"
msgstr "Полгода"

#: includes/admin/menu-pages/settings-menu-page.php:807
msgid "Never Delete"
msgstr "Никогда не удалять"

#: includes/admin/menu-pages/settings-menu-page.php:817
msgid "Block Editor"
msgstr "Блоковый редактор"

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/admin/menu-pages/settings-menu-page.php:832
msgid "Enable block editor for \"%s\"."
msgstr "Включить блоковый редактор для \"%s\"."

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/post-types/coupon-cpt.php:59
#: includes/post-types/room-type-cpt.php:53
#: includes/post-types/room-type-cpt.php:64
#: includes/widgets/rooms-widget.php:21
msgid "Accommodation Types"
msgstr "Типы размещения"

#: includes/admin/menu-pages/settings-menu-page.php:832
#: includes/csv/bookings/bookings-exporter-helper.php:97
#: includes/emails/templaters/reserved-rooms-templater.php:183
#: includes/post-types/coupon-cpt.php:136
#: includes/post-types/service-cpt.php:91
#: includes/post-types/service-cpt.php:101
#: includes/views/booking-view.php:202
msgid "Services"
msgstr "Услуги"

#: includes/admin/menu-pages/settings-menu-page.php:863
msgid "Admin Emails"
msgstr "Письма администратору"

#: includes/admin/menu-pages/settings-menu-page.php:876
msgid "Customer Emails"
msgstr "Письма клиенту"

#: includes/admin/menu-pages/settings-menu-page.php:881
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the <a>Hotel Booking & Mailchimp Integration</a>."
msgstr "Сделайте гостей вашими постоянными посетителями с помощью приложения <a>Hotel Booking & Mailchimp Integration</a>, которое позволяет настроить автоматические кампании email-рассылок. "

#: includes/admin/menu-pages/settings-menu-page.php:884
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the Hotel Booking & Mailchimp Integration."
msgstr "Сделайте гостей вашими постоянными посетителями с помощью приложения Hotel Booking & Mailchimp Integration, которое позволяет настроить автоматические кампании email-рассылок. "

#: includes/admin/menu-pages/settings-menu-page.php:903
msgid "Cancellation Details Template"
msgstr "Шаблон письма с деталями отмены бронирования"

#: includes/admin/menu-pages/settings-menu-page.php:904
msgid "Used for %cancellation_details% tag."
msgstr "Используется для тега %cancellation_details%."

#: includes/admin/menu-pages/settings-menu-page.php:926
msgid "Email Settings"
msgstr "Настройки писем"

#: includes/admin/menu-pages/settings-menu-page.php:931
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with <a>Hotel Booking Notifier</a>."
msgstr "Используйте приложение <a>Hotel Booking Notifier</a> для автоматической отправки писем типа инструкций где взять ключ, правила внутреннего распорядка до или после заезда/отъезда."

#: includes/admin/menu-pages/settings-menu-page.php:934
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with Hotel Booking Notifier."
msgstr "Используйте приложение Hotel Booking Notifier для автоматической отправки писем типа инструкций где взять ключ, правила внутреннего распорядка до или после заезда/отъезда."

#: includes/admin/menu-pages/settings-menu-page.php:942
msgid "Email Sender"
msgstr "Отправитель письма"

#: includes/admin/menu-pages/settings-menu-page.php:949
msgid "Administrator Email"
msgstr "Email администратора"

#: includes/admin/menu-pages/settings-menu-page.php:958
msgid "From Email"
msgstr "Email отправителя"

#: includes/admin/menu-pages/settings-menu-page.php:967
msgid "From Name"
msgstr "Имя отправителя"

#: includes/admin/menu-pages/settings-menu-page.php:977
msgid "Logo URL"
msgstr "Ссылка на логотипе"

#: includes/admin/menu-pages/settings-menu-page.php:987
msgid "Footer Text"
msgstr "Текст футера (нижней части письма)"

#: includes/admin/menu-pages/settings-menu-page.php:997
msgid "Reserved Accommodation Details Template"
msgstr "Шаблон письма с деталями забронированного варианта размещения"

#: includes/admin/menu-pages/settings-menu-page.php:998
msgid "Used for %reserved_rooms_details% tag."
msgstr "Используется для тега %reserved_rooms_details%."

#: includes/admin/menu-pages/settings-menu-page.php:1011
msgid "Styles"
msgstr "Стили"

#: includes/admin/menu-pages/settings-menu-page.php:1018
msgid "Base Color"
msgstr "Основной цвет"

#: includes/admin/menu-pages/settings-menu-page.php:1027
msgid "Background Color"
msgstr "Цвет фона"

#: includes/admin/menu-pages/settings-menu-page.php:1036
msgid "Body Background Color"
msgstr "Фоновый цвет основного текста"

#: includes/admin/menu-pages/settings-menu-page.php:1045
msgid "Body Text Color"
msgstr "Цвет основного текста"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "Payment Gateways"
msgstr "Способы оплаты"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "General Settings"
msgstr "Общие настройки"

#: includes/admin/menu-pages/settings-menu-page.php:1071
msgid "Need more gateways? Use our Hotel Booking <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a> extension."
msgstr "Нужно больше денежных шлюзов? Используйте приложение Hotel Booking <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a>. "

#: includes/admin/menu-pages/settings-menu-page.php:1075
msgid "You may also email the <a href=\"%s\" target=\"_blank\">balance payment request</a> link to your guests."
msgstr "Вы также можете отправить своим гостям <a href=\"%s\" target=\"_blank\"> ссылку на запрос платежа по остатку суммы </a>."

#: includes/admin/menu-pages/settings-menu-page.php:1086
msgid "User Pays"
msgstr "Клиент платит"

#: includes/admin/menu-pages/settings-menu-page.php:1088
msgid "Full Amount"
msgstr "Всю стоимость"

#: includes/admin/menu-pages/settings-menu-page.php:1089
#: includes/views/booking-view.php:463
msgid "Deposit"
msgstr "Задаток"

#: includes/admin/menu-pages/settings-menu-page.php:1098
msgid "Deposit Type"
msgstr "Тип задатка"

#: includes/admin/menu-pages/settings-menu-page.php:1100
#: includes/post-types/coupon-cpt.php:115
#: includes/post-types/coupon-cpt.php:160
msgid "Fixed"
msgstr "Фиксированная сумма"

#: includes/admin/menu-pages/settings-menu-page.php:1101
msgid "Percent"
msgstr "Процент"

#: includes/admin/menu-pages/settings-menu-page.php:1110
msgid "Deposit Amount"
msgstr "Сумма залога"

#: includes/admin/menu-pages/settings-menu-page.php:1121
msgid "Deposit Time Frame (days)"
msgstr "Период для депозита (дней)"

#: includes/admin/menu-pages/settings-menu-page.php:1122
msgid "Apply deposit to bookings made in at least the selected number of days prior to the check-in date. Otherwise, the full amount is charged."
msgstr "Применять депозит для бронирований совершенных за выбранное количество дней до даты заселения. В противном случае взимается полная сумма."

#: includes/admin/menu-pages/settings-menu-page.php:1133
msgid "Force Secure Checkout"
msgstr "Включить безопасные платежи"

#: includes/admin/menu-pages/settings-menu-page.php:1135
msgid "Force SSL (HTTPS) on the checkout pages. You must have an SSL certificate installed to use this option."
msgstr "Включить SSL (HTTPS) на странице оформления бронирования. SSL сертификат должен быть установлен для использования этой опции."

#: includes/admin/menu-pages/settings-menu-page.php:1142
msgid "Reservation Received Page"
msgstr "Страница \"Бронирование получено\""

#: includes/admin/menu-pages/settings-menu-page.php:1150
msgid "Failed Transaction Page"
msgstr "Страница неудачной оплаты"

#: includes/admin/menu-pages/settings-menu-page.php:1158
msgid "Default Gateway"
msgstr "Способ оплаты по умолчанию"

#: includes/admin/menu-pages/settings-menu-page.php:1172
#: includes/payments/gateways/bank-gateway.php:127
msgid "Pending Payment Time"
msgstr "Время ожидания оплаты"

#: includes/admin/menu-pages/settings-menu-page.php:1173
msgid "Period of time in minutes the user is given to complete payment. Unpaid bookings become Abandoned and accommodation status changes to Available."
msgstr "Период времени в минутах, за который пользователь должен завершить бронирование. Неподтверждённое бронирование переходит в статус \"Заброшено\", а место размещения становится доступным для бронирования."

#: includes/admin/menu-pages/settings-menu-page.php:1195
msgid "Install <a href=\"%s\" target=\"_blank\">Hotel Booking addons</a> to manage their settings."
msgstr "Установите <a href=\"%s\" target=\"_blank\"> дополнения для плагина Hotel Booking</a>, чтобы настроить их."

#: includes/admin/menu-pages/settings-menu-page.php:1197
msgid "Install Hotel Booking addons to manage their settings."
msgstr "Установите дополнения для плагина Hotel Booking, чтобы настроить их."

#: includes/admin/menu-pages/settings-menu-page.php:1214
msgid "Advanced"
msgstr "Дополнительно"

#: includes/admin/menu-pages/settings-menu-page.php:1226
#: includes/admin/menu-pages/settings-menu-page.php:1228
msgid "License"
msgstr "Лицензия"

#: includes/admin/menu-pages/settings-menu-page.php:1305
msgid "Settings saved."
msgstr "Настройки сохранены."

#: includes/admin/menu-pages/settings-menu-page.php:1344
msgid "<strong>Note:</strong> Payment methods will appear on the checkout page only when Confirmation Upon Payment is enabled in Accommodation > Settings > General > Confirmation Mode."
msgstr "<strong> Примечание:</strong>. Способы оплаты будут отображаться на странице оформления заказа только в том случае, если в разделе «Размещение»> «Настройки»> «Общие»> «Режим подтверждения бронирования» включен параметр «Подтверждение после оплаты»."

#: includes/admin/menu-pages/settings-menu-page.php:1432
#: includes/admin/menu-pages/settings-menu-page.php:1436
#: assets/blocks/blocks.js:141
#: assets/blocks/blocks.js:276
#: assets/blocks/blocks.js:429
#: assets/blocks/blocks.js:679
#: assets/blocks/blocks.js:1196
#: assets/blocks/blocks.js:1419
#: assets/blocks/blocks.js:1501
msgid "Settings"
msgstr "Настройки"

#: includes/admin/menu-pages/shortcodes-menu-page.php:21
#: assets/blocks/blocks.js:117
msgid "Availability Search Form"
msgstr "Форма проверки наличия мест"

#: includes/admin/menu-pages/shortcodes-menu-page.php:22
msgid "Display search form."
msgstr "Показывает форму поиска."

#: includes/admin/menu-pages/shortcodes-menu-page.php:25
#: assets/blocks/blocks.js:148
msgid "The number of adults presetted in the search form."
msgstr "Количество взрослых, заранее установленное в форме поиска."

#: includes/admin/menu-pages/shortcodes-menu-page.php:30
#: assets/blocks/blocks.js:163
msgid "The number of children presetted in the search form."
msgstr "Количество детей, заранее установленное в форме поиска."

#: includes/admin/menu-pages/shortcodes-menu-page.php:35
msgid "Check-in date presetted in the search form."
msgstr "Дата заезда, заранее установленная в форме поиска."

#: includes/admin/menu-pages/shortcodes-menu-page.php:36
#: includes/admin/menu-pages/shortcodes-menu-page.php:41
msgid "date in format %s"
msgstr "дата в формате %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:40
msgid "Check-out date presetted in the search form."
msgstr "Дата отъезда, заранее установленная в форме поиска."

#: includes/admin/menu-pages/shortcodes-menu-page.php:45
#: assets/blocks/blocks.js:201
msgid "Custom attributes for advanced search."
msgstr "Настраиваемые атрибуты для расширенного поиска."

#: includes/admin/menu-pages/shortcodes-menu-page.php:46
#: assets/blocks/blocks.js:202
msgid "Comma-separated slugs of attributes."
msgstr "Ярлыки атрибутов, разделенные запятыми."

#: includes/admin/menu-pages/shortcodes-menu-page.php:50
#: includes/admin/menu-pages/shortcodes-menu-page.php:94
#: includes/admin/menu-pages/shortcodes-menu-page.php:180
#: includes/admin/menu-pages/shortcodes-menu-page.php:259
#: includes/admin/menu-pages/shortcodes-menu-page.php:361
#: includes/admin/menu-pages/shortcodes-menu-page.php:422
#: includes/admin/menu-pages/shortcodes-menu-page.php:450
#: includes/admin/menu-pages/shortcodes-menu-page.php:470
#: includes/admin/menu-pages/shortcodes-menu-page.php:494
#: includes/admin/menu-pages/shortcodes-menu-page.php:514
#: includes/admin/menu-pages/shortcodes-menu-page.php:530
#: includes/admin/menu-pages/shortcodes-menu-page.php:546
msgid "Custom CSS class for shortcode wrapper"
msgstr "Ваши CSS классы для обёртки шорткода. "

#: includes/admin/menu-pages/shortcodes-menu-page.php:51
#: includes/admin/menu-pages/shortcodes-menu-page.php:95
#: includes/admin/menu-pages/shortcodes-menu-page.php:181
#: includes/admin/menu-pages/shortcodes-menu-page.php:260
#: includes/admin/menu-pages/shortcodes-menu-page.php:362
#: includes/admin/menu-pages/shortcodes-menu-page.php:423
#: includes/admin/menu-pages/shortcodes-menu-page.php:451
#: includes/admin/menu-pages/shortcodes-menu-page.php:471
#: includes/admin/menu-pages/shortcodes-menu-page.php:495
#: includes/admin/menu-pages/shortcodes-menu-page.php:515
#: includes/admin/menu-pages/shortcodes-menu-page.php:531
#: includes/admin/menu-pages/shortcodes-menu-page.php:547
msgid "whitespace separated css classes"
msgstr "CSS классы, разделённые пробелами"

#: includes/admin/menu-pages/shortcodes-menu-page.php:65
#: includes/admin/menu-pages/shortcodes-menu-page.php:465
#: includes/admin/menu-pages/shortcodes-menu-page.php:489
#: includes/csv/bookings/bookings-exporter-helper.php:76
#: includes/emails/templaters/reserved-rooms-templater.php:187
msgid "Accommodation Type ID"
msgstr "ID типа размещения"

#: includes/admin/menu-pages/shortcodes-menu-page.php:66
#: includes/admin/menu-pages/shortcodes-menu-page.php:466
msgid "ID of Accommodation Type to check availability."
msgstr "ID типа размещения для проверки наличия мест."

#: includes/admin/menu-pages/shortcodes-menu-page.php:67
#: includes/admin/menu-pages/shortcodes-menu-page.php:378
#: includes/admin/menu-pages/shortcodes-menu-page.php:467
#: includes/admin/menu-pages/shortcodes-menu-page.php:491
msgid "integer number"
msgstr "целое число"

#: includes/admin/menu-pages/shortcodes-menu-page.php:70
#: assets/blocks/blocks.js:295
msgid "How many months to show."
msgstr "Количество месяцев для отображения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:72
#: assets/blocks/blocks.js:296
msgid "Set the number of columns or the number of rows and columns separated by comma. Example: \"3\" or \"2,3\""
msgstr "Задайте число столбцов или число строк и столбцов, разделенных запятыми. Пример: \"3\" или \"2,3\""

#: includes/admin/menu-pages/shortcodes-menu-page.php:106
#: assets/blocks/blocks.js:251
msgid "Display availability calendar of the current accommodation type or by ID."
msgstr "Показывает календарь доступности текущего типа размещения или по ID."

#: includes/admin/menu-pages/shortcodes-menu-page.php:111
#: assets/blocks/blocks.js:397
#: assets/blocks/blocks.js:630
msgid "Availability Search Results"
msgstr "Результаты поиска Проверки наличия мест"

#: includes/admin/menu-pages/shortcodes-menu-page.php:112
#: assets/blocks/blocks.js:398
msgid "Display listing of accommodation types that meet the search criteria."
msgstr "Показывает список типов размещения, удовлетворяющих критериям поиска."

#: includes/admin/menu-pages/shortcodes-menu-page.php:115
#: includes/admin/menu-pages/shortcodes-menu-page.php:212
#: includes/admin/menu-pages/shortcodes-menu-page.php:381
#: assets/blocks/blocks.js:437
#: assets/blocks/blocks.js:687
#: assets/blocks/blocks.js:1216
msgid "Whether to display title of the accommodation type."
msgstr "Показывать ли название типа размещения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:120
#: includes/admin/menu-pages/shortcodes-menu-page.php:217
#: includes/admin/menu-pages/shortcodes-menu-page.php:386
#: assets/blocks/blocks.js:449
#: assets/blocks/blocks.js:699
#: assets/blocks/blocks.js:1228
msgid "Whether to display featured image of the accommodation type."
msgstr "Показывать ли главное изображение типа размещения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:125
#: includes/admin/menu-pages/shortcodes-menu-page.php:222
#: includes/admin/menu-pages/shortcodes-menu-page.php:391
#: assets/blocks/blocks.js:461
#: assets/blocks/blocks.js:711
#: assets/blocks/blocks.js:1240
msgid "Whether to display gallery of the accommodation type."
msgstr "Показывать ли галерею типа размещения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:130
#: includes/admin/menu-pages/shortcodes-menu-page.php:227
#: includes/admin/menu-pages/shortcodes-menu-page.php:396
#: assets/blocks/blocks.js:473
#: assets/blocks/blocks.js:723
#: assets/blocks/blocks.js:1252
msgid "Whether to display excerpt (short description) of the accommodation type."
msgstr "Показывать ли отрывок из описания типа размещения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:135
#: includes/admin/menu-pages/shortcodes-menu-page.php:232
#: includes/admin/menu-pages/shortcodes-menu-page.php:401
#: assets/blocks/blocks.js:485
#: assets/blocks/blocks.js:735
#: assets/blocks/blocks.js:1264
msgid "Whether to display details of the accommodation type."
msgstr "Показывать ли детали типа размещения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:140
#: includes/admin/menu-pages/shortcodes-menu-page.php:237
#: includes/admin/menu-pages/shortcodes-menu-page.php:406
#: includes/admin/menu-pages/shortcodes-menu-page.php:427
#: assets/blocks/blocks.js:497
#: assets/blocks/blocks.js:747
#: assets/blocks/blocks.js:1276
msgid "Whether to display price of the accommodation type."
msgstr "Показывать ли цену типа размещения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:145
#: includes/admin/menu-pages/shortcodes-menu-page.php:242
#: includes/admin/menu-pages/shortcodes-menu-page.php:411
msgid "Show View Details button"
msgstr "Показывать кнопку Посмотреть детали"

#: includes/admin/menu-pages/shortcodes-menu-page.php:146
#: includes/admin/menu-pages/shortcodes-menu-page.php:243
#: includes/admin/menu-pages/shortcodes-menu-page.php:412
#: assets/blocks/blocks.js:509
#: assets/blocks/blocks.js:759
#: assets/blocks/blocks.js:1288
msgid "Whether to display \"View Details\" button with the link to accommodation type."
msgstr "Показывать ли кнопку \"Посмотреть детали\" со ссылкой на тип размещения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:151
#: includes/admin/menu-pages/shortcodes-menu-page.php:284
#: includes/admin/menu-pages/shortcodes-menu-page.php:332
msgid "Sort by."
msgstr "Сортировать по"

#: includes/admin/menu-pages/shortcodes-menu-page.php:153
#: includes/admin/menu-pages/shortcodes-menu-page.php:173
#: includes/admin/menu-pages/shortcodes-menu-page.php:286
#: includes/admin/menu-pages/shortcodes-menu-page.php:306
#: includes/admin/menu-pages/shortcodes-menu-page.php:334
#: includes/admin/menu-pages/shortcodes-menu-page.php:354
msgid "%1$s. See the <a href=\"%2$s\" target=\"_blank\">full list</a>."
msgstr "%1$s. Посмотреть <a href=\"%2$s\" target=\"_blank\">весь список</a>."

#: includes/admin/menu-pages/shortcodes-menu-page.php:160
#: includes/admin/menu-pages/shortcodes-menu-page.php:293
#: includes/admin/menu-pages/shortcodes-menu-page.php:341
msgid "Designates the ascending or descending order of sorting."
msgstr "Определяет порядок сортировки по возрастанию или убыванию."

#: includes/admin/menu-pages/shortcodes-menu-page.php:162
#: includes/admin/menu-pages/shortcodes-menu-page.php:295
#: includes/admin/menu-pages/shortcodes-menu-page.php:343
msgid "ASC - from lowest to highest values (1, 2, 3). DESC - from highest to lowest values (3, 2, 1)."
msgstr "ASC - от наименьшего к наибольшему значению (1, 2, 3). DESC - от наибольшего к наименьшему значению (3, 2, 1)."

#: includes/admin/menu-pages/shortcodes-menu-page.php:166
#: includes/admin/menu-pages/shortcodes-menu-page.php:299
#: includes/admin/menu-pages/shortcodes-menu-page.php:347
#: assets/blocks/blocks.js:574
#: assets/blocks/blocks.js:910
#: assets/blocks/blocks.js:1094
msgid "Custom field name. Required if \"orderby\" is one of the \"meta_value\", \"meta_value_num\" or \"meta_value_*\"."
msgstr "Имя настраиваемого поля. Требуется, если \"orderby\" является одним из \"meta_value\", \"meta_value_num\" или \"meta_value_*\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:167
#: includes/admin/menu-pages/shortcodes-menu-page.php:300
#: includes/admin/menu-pages/shortcodes-menu-page.php:348
msgid "custom field name"
msgstr "имя настраиваемого поля"

#: includes/admin/menu-pages/shortcodes-menu-page.php:168
#: includes/admin/menu-pages/shortcodes-menu-page.php:177
#: includes/admin/menu-pages/shortcodes-menu-page.php:301
#: includes/admin/menu-pages/shortcodes-menu-page.php:310
#: includes/admin/menu-pages/shortcodes-menu-page.php:349
#: includes/admin/menu-pages/shortcodes-menu-page.php:358
#: includes/admin/menu-pages/shortcodes-menu-page.php:645
msgid "empty string"
msgstr "Пустая строка"

#: includes/admin/menu-pages/shortcodes-menu-page.php:171
#: includes/admin/menu-pages/shortcodes-menu-page.php:304
#: includes/admin/menu-pages/shortcodes-menu-page.php:352
msgid "Specified type of the custom field. Can be used in conjunction with orderby=\"meta_value\"."
msgstr "Указанный тип настраиваемого поля. Может использоваться в сочетании с orderby= \"meta_value\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:185
msgid "Sort by. Use \"orderby\" insted."
msgstr "Сортировать по. Вместо этого используйте \"orderby\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:191
#: includes/admin/menu-pages/shortcodes-menu-page.php:248
msgid "Show Book button"
msgstr "Показывать кнопку Забронировать"

#: includes/admin/menu-pages/shortcodes-menu-page.php:192
#: includes/admin/menu-pages/shortcodes-menu-page.php:249
#: includes/admin/menu-pages/shortcodes-menu-page.php:417
#: assets/blocks/blocks.js:771
#: assets/blocks/blocks.js:1300
msgid "Whether to display Book button."
msgstr "Показывать ли кнопку \"Забронировать\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "NOTE:"
msgstr "Примечание:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
msgid "Use only on page that you set as Search Results Page in <a href=\"%s\">Settings</a>"
msgstr "Использовать только на странице, выбранной как \"Результаты поиска\" в <a href=\"%s\">Настройках</a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:209
#: assets/blocks/blocks.js:642
msgid "Accommodation Types Listing"
msgstr "Список типов размещения"

#: includes/admin/menu-pages/shortcodes-menu-page.php:254
#: includes/admin/menu-pages/shortcodes-menu-page.php:327
#: assets/blocks/blocks.js:804
#: assets/blocks/blocks.js:1028
msgid "Count per page"
msgstr "Количество на странице"

#: includes/admin/menu-pages/shortcodes-menu-page.php:255
#: assets/blocks/blocks.js:805
msgid "integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "число, -1 для отображения всех записей, по умолчанию: На страницах блога отображать не более установленного количества записей (Настройки\\Чтение)"

#: includes/admin/menu-pages/shortcodes-menu-page.php:264
#: assets/blocks/blocks.js:817
msgid "IDs of categories that will be shown."
msgstr "ID категорий, которые будут показаны."

#: includes/admin/menu-pages/shortcodes-menu-page.php:265
#: includes/admin/menu-pages/shortcodes-menu-page.php:270
#: includes/admin/menu-pages/shortcodes-menu-page.php:275
#: includes/admin/menu-pages/shortcodes-menu-page.php:323
#: assets/blocks/blocks.js:1017
msgid "Comma-separated IDs."
msgstr "ID значения, разделённые запятыми."

#: includes/admin/menu-pages/shortcodes-menu-page.php:269
#: assets/blocks/blocks.js:829
msgid "IDs of tags that will be shown."
msgstr "ID меток, которые будут показаны."

#: includes/admin/menu-pages/shortcodes-menu-page.php:274
#: assets/blocks/blocks.js:793
msgid "IDs of accommodations that will be shown."
msgstr "ID размещений, которые будут показаны."

#: includes/admin/menu-pages/shortcodes-menu-page.php:279
#: assets/blocks/blocks.js:841
msgid "Logical relationship between each taxonomy when there is more than one."
msgstr "Логические отношения между каждой таксономией, когда их несколько."

#: includes/admin/menu-pages/shortcodes-menu-page.php:319
#: assets/blocks/blocks.js:983
msgid "Services Listing"
msgstr "Список услуг"

#: includes/admin/menu-pages/shortcodes-menu-page.php:322
#: assets/blocks/blocks.js:792
msgid "IDs"
msgstr "ID значения"

#: includes/admin/menu-pages/shortcodes-menu-page.php:324
#: assets/blocks/blocks.js:1016
msgid "IDs of services that will be shown. "
msgstr "ID услуг, которые будут показаны. "

#: includes/admin/menu-pages/shortcodes-menu-page.php:368
msgid "Show All Services"
msgstr "Показывает все услуги"

#: includes/admin/menu-pages/shortcodes-menu-page.php:373
#: assets/blocks/blocks.js:1167
#: assets/blocks/blocks.js:1344
msgid "Single Accommodation Type"
msgstr "Один тип размещения"

#: includes/admin/menu-pages/shortcodes-menu-page.php:377
msgid "ID of accommodation type to display."
msgstr "ID типа размещения для отображения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:441
msgid "Display accommodation type with title and image."
msgstr "Показывает тип размещения с названием и фотографией."

#: includes/admin/menu-pages/shortcodes-menu-page.php:446
#: assets/blocks/blocks.js:1356
#: assets/blocks/blocks.js:1386
msgid "Checkout Form"
msgstr "Форма оплаты"

#: includes/admin/menu-pages/shortcodes-menu-page.php:447
#: assets/blocks/blocks.js:1357
msgid "Display checkout form."
msgstr "Показывает страницу оплаты."

#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "Use only on page that you set as Checkout Page in <a href=\"%s\">Settings</a>"
msgstr "Используйте только на странице, которую Вы выбрали как Страницу оформления бронирования в <a href=\"%s\">Настройках</a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:462
#: assets/blocks/blocks.js:1398
#: assets/blocks/blocks.js:1468
msgid "Booking Form"
msgstr "Форма бронирования"

#: includes/admin/menu-pages/shortcodes-menu-page.php:481
msgid "Show Booking Form for Accommodation Type with id 777"
msgstr "Показывает форму бронирования на странице типа размещения с ID 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:486
#: assets/blocks/blocks.js:1480
#: assets/blocks/blocks.js:1550
msgid "Accommodation Rates List"
msgstr "Список тарифов для типов размещения"

#: includes/admin/menu-pages/shortcodes-menu-page.php:490
#: assets/blocks/blocks.js:1204
msgid "ID of accommodation type."
msgstr "ID типа размещения."

#: includes/admin/menu-pages/shortcodes-menu-page.php:505
msgid "Show Accommodation Rates List for accommodation type with id 777"
msgstr "Показывает список тарифов для типа размещения с ID 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:511
#: assets/blocks/blocks.js:1563
msgid "Display booking and payment details."
msgstr "Показывает детали бронирования и оплаты."

#: includes/admin/menu-pages/shortcodes-menu-page.php:521
msgid "Use this shortcode on Booking Confirmed and Reservation Received pages"
msgstr "Используйте этот шорткод на страницах \"Бронирование подтверждено\" и \"Бронирование получено\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:526
msgid "Booking Cancelation"
msgstr "Отмена бронирования"

#: includes/admin/menu-pages/shortcodes-menu-page.php:527
msgid "Display booking cancelation details."
msgstr "Показывать информацию об отмене бронирования."

#: includes/admin/menu-pages/shortcodes-menu-page.php:537
msgid "Use this shortcode on the Booking Cancelation page"
msgstr "Используйте этот шорткод на странице отмены бронирования"

#: includes/admin/menu-pages/shortcodes-menu-page.php:542
msgid "Customer Account"
msgstr "Аккаунт клиента"

#: includes/admin/menu-pages/shortcodes-menu-page.php:543
msgid "Display log in form or customer account area."
msgstr "Показывает форму входа или аккаунт клиента."

#: includes/admin/menu-pages/shortcodes-menu-page.php:553
msgid "Use this shortcode to create the My Account page."
msgstr "Используйте этот шорткод для создания страницы Мой аккаунт."

#: includes/admin/menu-pages/shortcodes-menu-page.php:565
#: includes/admin/menu-pages/shortcodes-menu-page.php:699
#: includes/admin/menu-pages/shortcodes-menu-page.php:703
msgid "Shortcodes"
msgstr "Шорткоды"

#: includes/admin/menu-pages/shortcodes-menu-page.php:569
msgid "Shortcode"
msgstr "Шорткод"

#: includes/admin/menu-pages/shortcodes-menu-page.php:570
#: includes/post-types/attributes-cpt.php:307
msgid "Parameters"
msgstr "Параметры"

#: includes/admin/menu-pages/shortcodes-menu-page.php:571
msgid "Example"
msgstr "Пример"

#: includes/admin/menu-pages/shortcodes-menu-page.php:603
#: includes/admin/menu-pages/shortcodes-menu-page.php:625
msgid "Deprecated since %s"
msgstr "Устаревшее с версии %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:635
msgid "Values:"
msgstr "Значения:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:640
msgid "Default:"
msgstr "По умолчанию:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:687
msgid "Optional."
msgstr "Необязательно."

#: includes/admin/menu-pages/shortcodes-menu-page.php:695
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:30
#: includes/payments/gateways/gateway.php:365
#: includes/views/shortcodes/checkout-view.php:247
#: includes/views/shortcodes/checkout-view.php:270
#: includes/views/shortcodes/checkout-view.php:538
#: includes/views/shortcodes/checkout-view.php:572
#: templates/account/account-details.php:34
msgid "Required"
msgstr "Обязательно для заполнения"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:34
msgid "Taxes and fees saved."
msgstr "Налоги и сборы сохранены."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:41
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:459
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:463
msgid "Taxes & Fees"
msgstr "Налоги и сборы "

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:136
#: includes/csv/bookings/bookings-exporter-helper.php:102
#: includes/views/booking-view.php:296
msgid "Fees"
msgstr "Сборы"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:137
msgid "No fees have been created yet."
msgstr "Вы еще не добавили сборы."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:138
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:234
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:321
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:392
#: includes/post-types/booking-cpt.php:219
msgid "Add new"
msgstr "Добавить новый"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:146
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:242
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:329
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:400
msgid "Label"
msgstr "Название"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:147
msgid "New fee"
msgstr "Новый сбор"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:158
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:254
msgid "Per guest / per day"
msgstr "За гостя / за сутки"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:159
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:255
msgid "Per accommodation / per day"
msgstr "За вариант размещения / за сутки"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:160
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:256
msgid "Per accommodation (%)"
msgstr "За вариант размещения (%)"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:182
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:278
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:364
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:435
msgid "Limit"
msgstr "Лимит"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:183
msgid "How often this fee is charged. Set 0 to charge each day of the stay period. Set 1 to charge once."
msgstr "Как часто эта плата взимается. Установите 0, для оплаты каждый день периода пребывания. Установите 1 для оплаты один раз."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:197
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:200
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:293
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:296
msgid "Include"
msgstr "Включить"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:198
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:294
msgid "Show accommodation rate with this charge included"
msgstr "Показывать стоимость проживания с учетом этой суммы"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:232
#: includes/csv/bookings/bookings-exporter-helper.php:95
#: includes/views/booking-view.php:171
msgid "Accommodation Taxes"
msgstr "Налоги на вариант размещения"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:233
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:320
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:391
msgid "No taxes have been created yet."
msgstr "Вы еще не добавили налоги."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:243
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:330
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:401
msgid "New tax"
msgstr "Новый налог"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:279
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:365
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:436
msgid "Limit of days the fee is charged. Set 0 to charge each day of stay period. Set 1 to charge once."
msgstr "Лимит дней, за которые взимается плата. Установите 0, для оплаты каждый день пребывания. Установите 1 для оплаты один раз."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:319
#: includes/views/booking-view.php:265
msgid "Service Taxes"
msgstr "Налоги на услуги"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:341
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:412
#: includes/post-types/coupon-cpt.php:114
#: includes/post-types/coupon-cpt.php:159
msgid "Percentage"
msgstr "Процент"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:390
#: includes/views/booking-view.php:349
msgid "Fee Taxes"
msgstr "Налоги со сборов"

#: includes/admin/room-list-table.php:95
msgid "External Calendars"
msgstr "Внешние календари"

#: includes/admin/room-list-table.php:157
#: includes/admin/room-list-table.php:211
msgid "Sync External Calendars"
msgstr "Синхронизировать внешние календари"

#: includes/admin/room-list-table.php:163
#: includes/admin/sync-rooms-list-table.php:65
msgctxt "Placeholder for empty accommodation title"
msgid "(no title)"
msgstr "(без названия)"

#: includes/admin/room-list-table.php:185
msgid "Download Calendar"
msgstr "Загрузить календарь"

#: includes/admin/sync-logs-list-table.php:73
msgid "Message"
msgstr "Сообщение"

#: includes/admin/sync-logs-list-table.php:82
msgid "Success"
msgstr "Успешно"

#: includes/admin/sync-logs-list-table.php:85
msgid "Info"
msgstr "Инфо"

#: includes/admin/sync-logs-list-table.php:88
msgid "Warning"
msgstr "Предупреждение"

#: includes/admin/sync-rooms-list-table.php:71
msgctxt "This is date and time format 31/12/2017 - 23:59:59"
msgid "d/m/Y - H:i:s"
msgstr "d/m/Y - H:i:s"

#: includes/admin/sync-rooms-list-table.php:75
#: includes/ajax.php:945
msgid "Waiting"
msgstr "В ожидании"

#: includes/admin/sync-rooms-list-table.php:78
#: includes/ajax.php:948
msgid "Processing"
msgstr "Обработка"

#: includes/admin/sync-rooms-list-table.php:128
msgctxt "Total number of processed bookings"
msgid "Total"
msgstr "Всего"

#: includes/admin/sync-rooms-list-table.php:129
msgid "Succeed"
msgstr "Успешно"

#: includes/admin/sync-rooms-list-table.php:130
msgid "Skipped"
msgstr "Пропущено"

#: includes/admin/sync-rooms-list-table.php:131
msgid "Failed"
msgstr "Неудачно"

#: includes/admin/sync-rooms-list-table.php:132
msgid "Removed"
msgstr "Удалено"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:58
msgid "Copying to clipboard failed. Please press Ctrl/Cmd+C to copy."
msgstr "Не удалось скопировать в буфер обмена. Нажмите Ctrl/Cmd+C для копирования."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:80
msgid "Description is missing."
msgstr "Отсутствует описание."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:83
msgid "User is missing."
msgstr "Отсутствует пользователь."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:86
msgid "Permission is missing."
msgstr "Отсутствует разрешение."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:102
msgid "You do not have permission to assign API Keys to the selected user."
msgstr "У вас недостаточно прав для назначения API ключей выбранному пользователю."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:132
msgid "API Key updated successfully."
msgstr "API-ключ успешно обновлен."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:164
msgid "API Key generated successfully. Make sure to copy your new keys now as the secret key will be hidden once you leave this page."
msgstr "API Key сгенерирован успешно. Убедитесь, что вы скопировали новые ключи сейчас, так как секретный ключ будет скрыт, как только вы покинете эту страницу."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:176
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:116
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:119
msgid "Revoke key"
msgstr "Отозвать ключ"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:46
msgid "No keys found."
msgstr "Ключи не найдены."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:57
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:22
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:95
#: includes/payments/gateways/gateway.php:504
#: includes/post-types/coupon-cpt.php:37
#: includes/post-types/rate-cpt.php:81
msgid "Description"
msgstr "Описание"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:58
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:81
msgid "Consumer key ending in"
msgstr "Ключ клиента заканчивается на"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:59
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:36
msgid "User"
msgstr "Пользователь"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:60
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:55
msgid "Permissions"
msgstr "Права доступа"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:61
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:89
msgid "Last access"
msgstr "Последний доступ"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:99
msgid "API key"
msgstr "API ключ"

#. translators: %d: API key ID.
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:111
msgid "ID: %d"
msgstr "ID: %d"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:126
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:227
msgid "Revoke"
msgstr "Отозвать"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:182
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:65
msgid "Read"
msgstr "Чтение"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:183
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:66
msgid "Write"
msgstr "Запись"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:184
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:67
msgid "Read/Write"
msgstr "Чтение/Запись"

#. translators: 1: last access date 2: last access time
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:205
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:96
msgid "%1$s at %2$s"
msgstr "%1$s в %2$s"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:213
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:100
msgid "Unknown"
msgstr "Неизвестно"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:68
msgid "You do not have permission to edit this API Key"
msgstr "У вас недостаточно прав для редактирования этого ключа API"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:89
msgid "REST API"
msgstr "REST API"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:91
msgid "Add key"
msgstr "Добавить ключ"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:176
msgid "You do not have permission to revoke this API Key"
msgstr "У вас недостаточно прав чтобы отозвать этот API ключ"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:191
msgid "You do not have permission to edit API Keys"
msgstr "У вас недостаточно прав для редактирования API ключей"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:211
msgid "You do not have permission to revoke API Keys"
msgstr "У вас недостаточно прав чтобы отзывать API ключи"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:13
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:114
msgid "Generate API key"
msgstr "Сгенерировать ключ API"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:25
msgid "Friendly name for identifying this key."
msgstr "Удобное название для идентификации этого ключа."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:39
msgid "Owner of these keys."
msgstr "Владелец этих ключей."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:58
msgid "Access type of these keys."
msgstr "Тип доступа к этим ключам."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:148
msgid "Consumer key"
msgstr "Consumer key (ключ клиента)"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copied!"
msgstr "Скопировано!"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copy"
msgstr "Копировать"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:156
msgid "Consumer secret"
msgstr "Consumer secret (секретный ключ)"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:164
msgid "QR Code"
msgstr "QR код"

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:33
#: includes/ajax-api/ajax-actions/update-booking-notes.php:52
#: includes/ajax.php:344
#: includes/ajax.php:388
#: includes/csv/bookings/bookings-query.php:85
msgid "Please complete all required fields and try again."
msgstr "Пожалуйста, заполните все необходимые поля и попробуйте ещё раз."

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:55
msgid "Sorry, the minimum allowed payment amount is %s to use this payment method."
msgstr "Извините, минимально возможная сумма платежа %s при использовании этого способа оплаты."

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:60
#: includes/post-types/booking-cpt.php:35
msgid "Booking Information"
msgstr "Информация о бронировании"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:68
#: includes/emails/templaters/email-templater.php:136
#: includes/post-types/booking-cpt.php:51
#: template-functions.php:706
#: template-functions.php:710
#: templates/create-booking/search/search-form.php:53
#: templates/edit-booking/edit-dates.php:33
#: templates/shortcodes/search/search-form.php:43
#: templates/widgets/search-availability/search-form.php:43
#: assets/blocks/blocks.js:177
msgid "Check-in Date"
msgstr "Дата заезда"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:72
#: includes/emails/templaters/email-templater.php:140
#: includes/post-types/booking-cpt.php:60
#: template-functions.php:715
#: template-functions.php:719
#: templates/create-booking/search/search-form.php:73
#: templates/edit-booking/edit-dates.php:42
#: templates/shortcodes/search/search-form.php:63
#: templates/widgets/search-availability/search-form.php:62
#: assets/blocks/blocks.js:189
msgid "Check-out Date"
msgstr "Дата отъезда"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:91
msgid "Summary"
msgstr ""

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:99
msgid "Source"
msgstr ""

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:110
#: includes/post-types/booking-cpt.php:83
#: templates/emails/customer-approved-booking.php:31
#: templates/emails/customer-cancelled-booking.php:29
#: templates/emails/customer-confirmation-booking.php:35
#: templates/emails/customer-pending-booking.php:32
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:30
msgid "Customer Information"
msgstr "Информация о покупателе"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:150
#: includes/csv/bookings/bookings-exporter-helper.php:90
#: includes/emails/templaters/email-templater.php:189
#: includes/post-types/booking-cpt.php:164
msgid "Customer Note"
msgstr "Пожелание клиента"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:156
#: includes/post-types/booking-cpt.php:171
msgid "Additional Information"
msgstr "Дополнительная информация"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:160
#: includes/csv/bookings/bookings-exporter-helper.php:107
#: includes/post-types/booking-cpt.php:178
#: includes/post-types/coupon-cpt.php:289
msgid "Coupon"
msgstr "Купон"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:164
#: includes/post-types/booking-cpt.php:187
msgid "Total Booking Price"
msgstr "Общая стоимость бронирования"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:173
#: includes/bundles/customer-bundle.php:173
#: includes/post-types/booking-cpt.php:201
#: includes/views/shortcodes/checkout-view.php:735
msgid "Notes"
msgstr "Примечания"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:192
msgid "%1$s on %2$s"
msgstr "%1$s %2$s"

#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:185
#: template-functions.php:88
msgid "Based on your search parameters"
msgstr "Согласно Вашим параметрам поиска"

#: includes/ajax.php:245
msgid "No bookings found for your request."
msgstr "По вашему запросу бронирований не найдено."

#: includes/ajax.php:252
msgid "Uploads directory is not writable."
msgstr "Каталог загрузок недоступен для записи."

#: includes/ajax.php:314
msgid "No enough data"
msgstr "Недостаточно данных"

#: includes/ajax.php:332
#: includes/script-managers/admin-script-manager.php:94
msgid "An error has occurred"
msgstr "Произошла ошибка"

#: includes/ajax.php:372
#: includes/script-managers/public-script-manager.php:199
msgid "An error has occurred, please try again later."
msgstr "Произошла ошибка, пожалуйста, попробуйте позже."

#: includes/ajax.php:473
msgid "The number of adults is not valid."
msgstr "Количество взрослых неверно."

#: includes/ajax.php:477
msgid "The number of guests is not valid."
msgstr "Количество гостей неверно."

#: includes/ajax.php:519
#: includes/ajax.php:593
msgid "An error has occurred. Please try again later."
msgstr "Произошла ошибка. Пожалуйста, попробуйте позже."

#: includes/ajax.php:750
msgid "Chosen payment method is not available. Please refresh the page and try one more time."
msgstr "Выбранный способ оплаты недоступен. Пожалуйста, обновите страницу и попробуйте ещё раз."

#: includes/ajax.php:832
msgid "Coupon applied successfully."
msgstr "Купон успешно применён."

#: includes/ajax.php:838
#: includes/entities/coupon.php:366
msgid "Coupon is not valid."
msgstr "Купон недействительный."

#: includes/ajax.php:1046
msgid "You do not have permission to do this action."
msgstr "У вас нет разрешения на выполнение этого действия."

#: includes/attribute-functions.php:164
#: includes/post-types/attributes-cpt.php:137
#: includes/post-types/attributes-cpt.php:149
#: includes/post-types/attributes-cpt.php:345
msgctxt "Not selected value in the search form."
msgid "&mdash;"
msgstr "&mdash;"

#: includes/bookings-calendar.php:577
msgid "Year"
msgstr "Год"

#: includes/bookings-calendar.php:578
#: includes/post-types/attributes-cpt.php:298
#: includes/reports/report-filters.php:94
msgid "Custom"
msgstr "От/до"

#: includes/bookings-calendar.php:608
#: includes/post-types/booking-cpt/statuses.php:102
#: includes/reports/data/report-earnings-by-dates-data.php:28
msgctxt "Booking status"
msgid "Confirmed"
msgstr "Подтверждено"

#: includes/bookings-calendar.php:645
#: includes/reports/abstract-report.php:46
#: includes/reports/earnings-report.php:361
msgid "Show"
msgstr "Показать"

#: includes/bookings-calendar.php:649
#: templates/create-booking/search/search-form.php:131
#: templates/shortcodes/search/search-form.php:138
#: templates/widgets/search-availability/search-form.php:142
msgid "Search"
msgstr "Найти"

#: includes/bookings-calendar.php:652
#: includes/bookings-calendar.php:654
#: includes/bookings-calendar.php:697
#: includes/script-managers/public-script-manager.php:200
msgid "Booked"
msgstr "Забронировано"

#: includes/bookings-calendar.php:657
#: includes/bookings-calendar.php:659
#: includes/bookings-calendar.php:698
#: includes/script-managers/public-script-manager.php:202
msgid "Pending"
msgstr "В ожидании"

#: includes/bookings-calendar.php:662
#: includes/bookings-calendar.php:664
msgid "External"
msgstr "Внешний"

#: includes/bookings-calendar.php:667
#: includes/bookings-calendar.php:669
#: includes/bookings-calendar.php:1133
msgid "Blocked"
msgstr "Заблокировано "

#: includes/bookings-calendar.php:687
msgid "Search results for accommodations that have bookings with status \"%s\" from %s until %s"
msgstr "Результаты поиска для размещений со статусом бронирования  \"%s\" от %s до %s"

#: includes/bookings-calendar.php:696
msgid "Free"
msgstr "Свободно"

#: includes/bookings-calendar.php:699
msgid "Locked (Booked or Pending)"
msgstr "Заблокировано (забронировано или в ожидании подтверждения)"

#: includes/bookings-calendar.php:729
#: includes/bookings-calendar.php:819
msgid "Until"
msgstr "До"

#: includes/bookings-calendar.php:775
msgid "Period:"
msgstr "Период:"

#: includes/bookings-calendar.php:782
msgid "&lt; Prev"
msgstr "&lt; Предыдущий"

#: includes/bookings-calendar.php:799
msgid "Next &gt;"
msgstr "Следующий &gt;"

#: includes/bookings-calendar.php:874
msgid "No accommodations found."
msgstr "Не найдено мест."

#: includes/bookings-calendar.php:1123
msgid "Check-out #%d"
msgstr "Отъезд #%d"

#: includes/bookings-calendar.php:1127
msgid "Check-in #%d"
msgstr "Заезд #%d"

#: includes/bookings-calendar.php:1131
msgid "Booking #%d"
msgstr "Бронирование #%d"

#: includes/bookings-calendar.php:1136
#: includes/bookings-calendar.php:1140
#: includes/script-managers/public-script-manager.php:201
msgid "Buffer time."
msgstr "Время буфера."

#: includes/bookings-calendar.php:1143
msgctxt "Availability"
msgid "Free"
msgstr "Свободно"

#: includes/bookings-calendar.php:1172
#: templates/emails/reserved-room-details.php:15
msgid "Adults: %s"
msgstr "Взрослых: %s"

#: includes/bookings-calendar.php:1176
#: templates/emails/reserved-room-details.php:17
msgid "Children: %s"
msgstr "Детей: %s"

#: includes/bookings-calendar.php:1183
msgid "Booking imported with UID %s."
msgstr "Бронирование импортировано с UID %s."

#: includes/bookings-calendar.php:1185
msgid "Imported booking."
msgstr "Импортированное бронирование."

#: includes/bookings-calendar.php:1193
msgid "Description: %s."
msgstr "Описание: %s."

#: includes/bookings-calendar.php:1197
msgid "Source: %s."
msgstr "Источник: %s."

#: includes/bundles/countries-bundle.php:16
msgid "Afghanistan"
msgstr "Афганистан"

#: includes/bundles/countries-bundle.php:17
msgid "&#197;land Islands"
msgstr "Аландские острова"

#: includes/bundles/countries-bundle.php:18
msgid "Albania"
msgstr "Албания"

#: includes/bundles/countries-bundle.php:19
msgid "Algeria"
msgstr "Алжир"

#: includes/bundles/countries-bundle.php:20
msgid "American Samoa"
msgstr "Американское Самоа"

#: includes/bundles/countries-bundle.php:21
msgid "Andorra"
msgstr "Андорра"

#: includes/bundles/countries-bundle.php:22
msgid "Angola"
msgstr "Ангола"

#: includes/bundles/countries-bundle.php:23
msgid "Anguilla"
msgstr "Ангилья"

#: includes/bundles/countries-bundle.php:24
msgid "Antarctica"
msgstr "Антарктида"

#: includes/bundles/countries-bundle.php:25
msgid "Antigua and Barbuda"
msgstr "Антигуа и Барбуда"

#: includes/bundles/countries-bundle.php:26
msgid "Argentina"
msgstr "Аргентина"

#: includes/bundles/countries-bundle.php:27
msgid "Armenia"
msgstr "Армения"

#: includes/bundles/countries-bundle.php:28
msgid "Aruba"
msgstr "Аруба"

#: includes/bundles/countries-bundle.php:29
msgid "Australia"
msgstr "Австралия"

#: includes/bundles/countries-bundle.php:30
msgid "Austria"
msgstr "Австрия"

#: includes/bundles/countries-bundle.php:31
msgid "Azerbaijan"
msgstr "Азербайджан"

#: includes/bundles/countries-bundle.php:32
msgid "Bahamas"
msgstr "Багамские Острова"

#: includes/bundles/countries-bundle.php:33
msgid "Bahrain"
msgstr "Бахрейн"

#: includes/bundles/countries-bundle.php:34
msgid "Bangladesh"
msgstr "Бангладеш"

#: includes/bundles/countries-bundle.php:35
msgid "Barbados"
msgstr "Барбадос"

#: includes/bundles/countries-bundle.php:36
msgid "Belarus"
msgstr "Беларусь"

#: includes/bundles/countries-bundle.php:37
msgid "Belgium"
msgstr "Бельгия"

#: includes/bundles/countries-bundle.php:38
msgid "Belau"
msgstr "Палау"

#: includes/bundles/countries-bundle.php:39
msgid "Belize"
msgstr "Белиз"

#: includes/bundles/countries-bundle.php:40
msgid "Benin"
msgstr "Бенин"

#: includes/bundles/countries-bundle.php:41
msgid "Bermuda"
msgstr "Бермудские Острова"

#: includes/bundles/countries-bundle.php:42
msgid "Bhutan"
msgstr "Бутан"

#: includes/bundles/countries-bundle.php:43
msgid "Bolivia"
msgstr "Боливия"

#: includes/bundles/countries-bundle.php:44
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Бонэйр, Синт-Эстатиус и Саба"

#: includes/bundles/countries-bundle.php:45
msgid "Bosnia and Herzegovina"
msgstr "Босния и Герцеговина"

#: includes/bundles/countries-bundle.php:46
msgid "Botswana"
msgstr "Ботсвана"

#: includes/bundles/countries-bundle.php:47
msgid "Bouvet Island"
msgstr "Остров Буве"

#: includes/bundles/countries-bundle.php:48
msgid "Brazil"
msgstr "Бразилия"

#: includes/bundles/countries-bundle.php:49
msgid "British Indian Ocean Territory"
msgstr "Британская территория Индийского океана"

#: includes/bundles/countries-bundle.php:50
msgid "British Virgin Islands"
msgstr "Британские Виргинские Острова"

#: includes/bundles/countries-bundle.php:51
msgid "Brunei"
msgstr "Бруней"

#: includes/bundles/countries-bundle.php:52
msgid "Bulgaria"
msgstr "Болгария"

#: includes/bundles/countries-bundle.php:53
msgid "Burkina Faso"
msgstr "Буркина-Фасо"

#: includes/bundles/countries-bundle.php:54
msgid "Burundi"
msgstr "Бурунди"

#: includes/bundles/countries-bundle.php:55
msgid "Cambodia"
msgstr "Камбоджа"

#: includes/bundles/countries-bundle.php:56
msgid "Cameroon"
msgstr "Камерун"

#: includes/bundles/countries-bundle.php:57
msgid "Canada"
msgstr "Канада"

#: includes/bundles/countries-bundle.php:58
msgid "Cape Verde"
msgstr "Кабо-Верде"

#: includes/bundles/countries-bundle.php:59
msgid "Cayman Islands"
msgstr "Каймановы Острова"

#: includes/bundles/countries-bundle.php:60
msgid "Central African Republic"
msgstr "Центральноафриканская Республика"

#: includes/bundles/countries-bundle.php:61
msgid "Chad"
msgstr "Чад"

#: includes/bundles/countries-bundle.php:62
msgid "Chile"
msgstr "Чили"

#: includes/bundles/countries-bundle.php:63
msgid "China"
msgstr "Китай"

#: includes/bundles/countries-bundle.php:64
msgid "Christmas Island"
msgstr "Остров Рождества"

#: includes/bundles/countries-bundle.php:65
msgid "Cocos (Keeling) Islands"
msgstr "Кокосовые острова (Килинг)"

#: includes/bundles/countries-bundle.php:66
msgid "Colombia"
msgstr "Колумбия"

#: includes/bundles/countries-bundle.php:67
msgid "Comoros"
msgstr "Коморы"

#: includes/bundles/countries-bundle.php:68
msgid "Congo (Brazzaville)"
msgstr "Конго (Браззавиль)"

#: includes/bundles/countries-bundle.php:69
msgid "Congo (Kinshasa)"
msgstr "Конго (Киншаса)"

#: includes/bundles/countries-bundle.php:70
msgid "Cook Islands"
msgstr "Острова Кука"

#: includes/bundles/countries-bundle.php:71
msgid "Costa Rica"
msgstr "Коста-Рика"

#: includes/bundles/countries-bundle.php:72
msgid "Croatia"
msgstr "Хорватия"

#: includes/bundles/countries-bundle.php:73
msgid "Cuba"
msgstr "Куба"

#: includes/bundles/countries-bundle.php:74
msgid "Cura&ccedil;ao"
msgstr "Кюрасао"

#: includes/bundles/countries-bundle.php:75
msgid "Cyprus"
msgstr "Кипр"

#: includes/bundles/countries-bundle.php:76
msgid "Czech Republic"
msgstr "Чешская Республика"

#: includes/bundles/countries-bundle.php:77
msgid "Denmark"
msgstr "Дания"

#: includes/bundles/countries-bundle.php:78
msgid "Djibouti"
msgstr "Джибути"

#: includes/bundles/countries-bundle.php:79
msgid "Dominica"
msgstr "Доминика"

#: includes/bundles/countries-bundle.php:80
msgid "Dominican Republic"
msgstr "Доминиканская Республика"

#: includes/bundles/countries-bundle.php:81
msgid "Ecuador"
msgstr "Эквадор"

#: includes/bundles/countries-bundle.php:82
msgid "Egypt"
msgstr "Египет"

#: includes/bundles/countries-bundle.php:83
msgid "El Salvador"
msgstr "Сальвадор"

#: includes/bundles/countries-bundle.php:84
msgid "Equatorial Guinea"
msgstr "Экваториальная Гвинея"

#: includes/bundles/countries-bundle.php:85
msgid "Eritrea"
msgstr "Эритрея"

#: includes/bundles/countries-bundle.php:86
msgid "Estonia"
msgstr "Эстония"

#: includes/bundles/countries-bundle.php:87
msgid "Ethiopia"
msgstr "Эфиопия"

#: includes/bundles/countries-bundle.php:88
msgid "Falkland Islands"
msgstr "Фолклендские острова"

#: includes/bundles/countries-bundle.php:89
msgid "Faroe Islands"
msgstr "Фарерские острова"

#: includes/bundles/countries-bundle.php:90
msgid "Fiji"
msgstr "Фиджи"

#: includes/bundles/countries-bundle.php:91
msgid "Finland"
msgstr "Финляндия"

#: includes/bundles/countries-bundle.php:92
msgid "France"
msgstr "Франция"

#: includes/bundles/countries-bundle.php:93
msgid "French Guiana"
msgstr "Гвиана"

#: includes/bundles/countries-bundle.php:94
msgid "French Polynesia"
msgstr "Французская Полинезия"

#: includes/bundles/countries-bundle.php:95
msgid "French Southern Territories"
msgstr "Южные Французские Территории"

#: includes/bundles/countries-bundle.php:96
msgid "Gabon"
msgstr "Габон"

#: includes/bundles/countries-bundle.php:97
msgid "Gambia"
msgstr "Гамбия"

#: includes/bundles/countries-bundle.php:98
msgid "Georgia"
msgstr "Грузия"

#: includes/bundles/countries-bundle.php:99
msgid "Germany"
msgstr "Германия"

#: includes/bundles/countries-bundle.php:100
msgid "Ghana"
msgstr "Гана"

#: includes/bundles/countries-bundle.php:101
msgid "Gibraltar"
msgstr "Гибралтар"

#: includes/bundles/countries-bundle.php:102
msgid "Greece"
msgstr "Греция"

#: includes/bundles/countries-bundle.php:103
msgid "Greenland"
msgstr "Гренландия"

#: includes/bundles/countries-bundle.php:104
msgid "Grenada"
msgstr "Гренада"

#: includes/bundles/countries-bundle.php:105
msgid "Guadeloupe"
msgstr "Гваделупа"

#: includes/bundles/countries-bundle.php:106
msgid "Guam"
msgstr "Гуам"

#: includes/bundles/countries-bundle.php:107
msgid "Guatemala"
msgstr "Гватемала"

#: includes/bundles/countries-bundle.php:108
msgid "Guernsey"
msgstr "Гернси"

#: includes/bundles/countries-bundle.php:109
msgid "Guinea"
msgstr "Гвинея"

#: includes/bundles/countries-bundle.php:110
msgid "Guinea-Bissau"
msgstr "Гвинея-Бисау"

#: includes/bundles/countries-bundle.php:111
msgid "Guyana"
msgstr "Гайана"

#: includes/bundles/countries-bundle.php:112
msgid "Haiti"
msgstr "Гаити"

#: includes/bundles/countries-bundle.php:113
msgid "Heard Island and McDonald Islands"
msgstr "Остров Херд и острова Макдональд"

#: includes/bundles/countries-bundle.php:114
msgid "Honduras"
msgstr "Гондурас"

#: includes/bundles/countries-bundle.php:115
msgid "Hong Kong"
msgstr "Гонконг"

#: includes/bundles/countries-bundle.php:116
msgid "Hungary"
msgstr "Венгрия"

#: includes/bundles/countries-bundle.php:117
msgid "Iceland"
msgstr "Исландия"

#: includes/bundles/countries-bundle.php:118
msgid "India"
msgstr "Индия"

#: includes/bundles/countries-bundle.php:119
msgid "Indonesia"
msgstr "Индонезия"

#: includes/bundles/countries-bundle.php:120
msgid "Iran"
msgstr "Иран"

#: includes/bundles/countries-bundle.php:121
msgid "Iraq"
msgstr "Ирак"

#: includes/bundles/countries-bundle.php:122
msgid "Ireland"
msgstr "Ирландия"

#: includes/bundles/countries-bundle.php:123
msgid "Isle of Man"
msgstr "Остров Мэн"

#: includes/bundles/countries-bundle.php:124
msgid "Israel"
msgstr "Израиль"

#: includes/bundles/countries-bundle.php:125
msgid "Italy"
msgstr "Италия"

#: includes/bundles/countries-bundle.php:126
msgid "Ivory Coast"
msgstr "Кот-д'Ивуар"

#: includes/bundles/countries-bundle.php:127
msgid "Jamaica"
msgstr "Ямайка"

#: includes/bundles/countries-bundle.php:128
msgid "Japan"
msgstr "Япония"

#: includes/bundles/countries-bundle.php:129
msgid "Jersey"
msgstr "Джерси"

#: includes/bundles/countries-bundle.php:130
msgid "Jordan"
msgstr "Иордан"

#: includes/bundles/countries-bundle.php:131
msgid "Kazakhstan"
msgstr "Казахстан"

#: includes/bundles/countries-bundle.php:132
msgid "Kenya"
msgstr "Кения"

#: includes/bundles/countries-bundle.php:133
msgid "Kiribati"
msgstr "Кирибати"

#: includes/bundles/countries-bundle.php:134
msgid "Kuwait"
msgstr "Кувейт"

#: includes/bundles/countries-bundle.php:135
msgid "Kyrgyzstan"
msgstr "Кыргызстан"

#: includes/bundles/countries-bundle.php:136
msgid "Laos"
msgstr "Лаос"

#: includes/bundles/countries-bundle.php:137
msgid "Latvia"
msgstr "Латвия"

#: includes/bundles/countries-bundle.php:138
msgid "Lebanon"
msgstr "Ливан"

#: includes/bundles/countries-bundle.php:139
msgid "Lesotho"
msgstr "Лесото"

#: includes/bundles/countries-bundle.php:140
msgid "Liberia"
msgstr "Либерия"

#: includes/bundles/countries-bundle.php:141
msgid "Libya"
msgstr "Ливия"

#: includes/bundles/countries-bundle.php:142
msgid "Liechtenstein"
msgstr "Лихтенштейн"

#: includes/bundles/countries-bundle.php:143
msgid "Lithuania"
msgstr "Литва"

#: includes/bundles/countries-bundle.php:144
msgid "Luxembourg"
msgstr "Люксембург"

#: includes/bundles/countries-bundle.php:145
msgid "Macao S.A.R., China"
msgstr "Специальный административный район Макао"

#: includes/bundles/countries-bundle.php:146
msgid "Macedonia"
msgstr "Македония"

#: includes/bundles/countries-bundle.php:147
msgid "Madagascar"
msgstr "Мадагаскар"

#: includes/bundles/countries-bundle.php:148
msgid "Malawi"
msgstr "Малави"

#: includes/bundles/countries-bundle.php:149
msgid "Malaysia"
msgstr "Малайзия"

#: includes/bundles/countries-bundle.php:150
msgid "Maldives"
msgstr "Мальдивы"

#: includes/bundles/countries-bundle.php:151
msgid "Mali"
msgstr "Мали"

#: includes/bundles/countries-bundle.php:152
msgid "Malta"
msgstr "Мальта"

#: includes/bundles/countries-bundle.php:153
msgid "Marshall Islands"
msgstr "Маршалловы острова"

#: includes/bundles/countries-bundle.php:154
msgid "Martinique"
msgstr "Мартиника"

#: includes/bundles/countries-bundle.php:155
msgid "Mauritania"
msgstr "Мавритания"

#: includes/bundles/countries-bundle.php:156
msgid "Mauritius"
msgstr "Маврикий"

#: includes/bundles/countries-bundle.php:157
msgid "Mayotte"
msgstr "Майотта"

#: includes/bundles/countries-bundle.php:158
msgid "Mexico"
msgstr "Мексика"

#: includes/bundles/countries-bundle.php:159
msgid "Micronesia"
msgstr "Микронезия"

#: includes/bundles/countries-bundle.php:160
msgid "Moldova"
msgstr "Молдова"

#: includes/bundles/countries-bundle.php:161
msgid "Monaco"
msgstr "Монако"

#: includes/bundles/countries-bundle.php:162
msgid "Mongolia"
msgstr "Монголия"

#: includes/bundles/countries-bundle.php:163
msgid "Montenegro"
msgstr "Черногория"

#: includes/bundles/countries-bundle.php:164
msgid "Montserrat"
msgstr "Монтсеррат"

#: includes/bundles/countries-bundle.php:165
msgid "Morocco"
msgstr "Марокко"

#: includes/bundles/countries-bundle.php:166
msgid "Mozambique"
msgstr "Мозамбик"

#: includes/bundles/countries-bundle.php:167
msgid "Myanmar"
msgstr "Мьянма"

#: includes/bundles/countries-bundle.php:168
msgid "Namibia"
msgstr "Намибия"

#: includes/bundles/countries-bundle.php:169
msgid "Nauru"
msgstr "Науру"

#: includes/bundles/countries-bundle.php:170
msgid "Nepal"
msgstr "Непал"

#: includes/bundles/countries-bundle.php:171
msgid "Netherlands"
msgstr "Нидерланды"

#: includes/bundles/countries-bundle.php:172
msgid "New Caledonia"
msgstr "Новая Каледония"

#: includes/bundles/countries-bundle.php:173
msgid "New Zealand"
msgstr "Новая Зеландия"

#: includes/bundles/countries-bundle.php:174
msgid "Nicaragua"
msgstr "Никарагуа"

#: includes/bundles/countries-bundle.php:175
msgid "Niger"
msgstr "Нигер"

#: includes/bundles/countries-bundle.php:176
msgid "Nigeria"
msgstr "Нигерия"

#: includes/bundles/countries-bundle.php:177
msgid "Niue"
msgstr "Ниуэ"

#: includes/bundles/countries-bundle.php:178
msgid "Norfolk Island"
msgstr "Остров Норфолк"

#: includes/bundles/countries-bundle.php:179
msgid "Northern Mariana Islands"
msgstr "Северные Марианские Острова"

#: includes/bundles/countries-bundle.php:180
msgid "North Korea"
msgstr "Северная Корея"

#: includes/bundles/countries-bundle.php:181
msgid "Norway"
msgstr "Норвегия"

#: includes/bundles/countries-bundle.php:182
msgid "Oman"
msgstr "Оман"

#: includes/bundles/countries-bundle.php:183
msgid "Pakistan"
msgstr "Пакистан"

#: includes/bundles/countries-bundle.php:184
msgid "Palestinian Territory"
msgstr "Палестинские территории"

#: includes/bundles/countries-bundle.php:185
msgid "Panama"
msgstr "Панама"

#: includes/bundles/countries-bundle.php:186
msgid "Papua New Guinea"
msgstr "Папуа — Новая Гвинея"

#: includes/bundles/countries-bundle.php:187
msgid "Paraguay"
msgstr "Парагвай"

#: includes/bundles/countries-bundle.php:188
#: includes/settings/main-settings.php:37
msgid "Peru"
msgstr "Перу"

#: includes/bundles/countries-bundle.php:189
msgid "Philippines"
msgstr "Филиппины"

#: includes/bundles/countries-bundle.php:190
msgid "Pitcairn"
msgstr "Острова Питкэрн"

#: includes/bundles/countries-bundle.php:191
msgid "Poland"
msgstr "Польша"

#: includes/bundles/countries-bundle.php:192
msgid "Portugal"
msgstr "Португалия"

#: includes/bundles/countries-bundle.php:193
msgid "Puerto Rico"
msgstr "Пуэрто-Рико"

#: includes/bundles/countries-bundle.php:194
msgid "Qatar"
msgstr "Катар"

#: includes/bundles/countries-bundle.php:195
msgid "Reunion"
msgstr "Реюньон"

#: includes/bundles/countries-bundle.php:196
msgid "Romania"
msgstr "Румыния"

#: includes/bundles/countries-bundle.php:197
msgid "Russia"
msgstr "Россия"

#: includes/bundles/countries-bundle.php:198
msgid "Rwanda"
msgstr "Руанда"

#: includes/bundles/countries-bundle.php:199
msgid "Saint Barth&eacute;lemy"
msgstr "Сен-Бартелеми"

#: includes/bundles/countries-bundle.php:200
msgid "Saint Helena"
msgstr "Остров Святой Елены"

#: includes/bundles/countries-bundle.php:201
msgid "Saint Kitts and Nevis"
msgstr "Сент-Китс и Невис"

#: includes/bundles/countries-bundle.php:202
msgid "Saint Lucia"
msgstr "Сент-Люсия"

#: includes/bundles/countries-bundle.php:203
msgid "Saint Martin (French part)"
msgstr "Сен-Мартен (французская часть)"

#: includes/bundles/countries-bundle.php:204
msgid "Saint Martin (Dutch part)"
msgstr "Сен-Мартен (голландская часть)"

#: includes/bundles/countries-bundle.php:205
msgid "Saint Pierre and Miquelon"
msgstr "Сен-Пьер и Микелон"

#: includes/bundles/countries-bundle.php:206
msgid "Saint Vincent and the Grenadines"
msgstr "Сент-Винсент и Гренадины"

#: includes/bundles/countries-bundle.php:207
msgid "San Marino"
msgstr "Сан-Марино"

#: includes/bundles/countries-bundle.php:208
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "Сан-Томе и Принсипи"

#: includes/bundles/countries-bundle.php:209
msgid "Saudi Arabia"
msgstr "Саудовская Аравия"

#: includes/bundles/countries-bundle.php:210
msgid "Senegal"
msgstr "Сенегал"

#: includes/bundles/countries-bundle.php:211
msgid "Serbia"
msgstr "Сербия"

#: includes/bundles/countries-bundle.php:212
msgid "Seychelles"
msgstr "Сейшельские Острова"

#: includes/bundles/countries-bundle.php:213
msgid "Sierra Leone"
msgstr "Сьерра-Леоне"

#: includes/bundles/countries-bundle.php:214
msgid "Singapore"
msgstr "Сингапур"

#: includes/bundles/countries-bundle.php:215
msgid "Slovakia"
msgstr "Словакия"

#: includes/bundles/countries-bundle.php:216
msgid "Slovenia"
msgstr "Словения"

#: includes/bundles/countries-bundle.php:217
msgid "Solomon Islands"
msgstr "Соломоновы Острова"

#: includes/bundles/countries-bundle.php:218
msgid "Somalia"
msgstr "Сомали"

#: includes/bundles/countries-bundle.php:219
msgid "South Africa"
msgstr "Южно-Африканская Республика"

#: includes/bundles/countries-bundle.php:220
msgid "South Georgia/Sandwich Islands"
msgstr "Южная Георгия и Южные Сандвичевы Острова"

#: includes/bundles/countries-bundle.php:221
msgid "South Korea"
msgstr "Южная Корея"

#: includes/bundles/countries-bundle.php:222
msgid "South Sudan"
msgstr "Южный Судан"

#: includes/bundles/countries-bundle.php:223
msgid "Spain"
msgstr "Испания"

#: includes/bundles/countries-bundle.php:224
msgid "Sri Lanka"
msgstr "Шри-Ланка"

#: includes/bundles/countries-bundle.php:225
msgid "Sudan"
msgstr "Судан"

#: includes/bundles/countries-bundle.php:226
msgid "Suriname"
msgstr "Суринам"

#: includes/bundles/countries-bundle.php:227
msgid "Svalbard and Jan Mayen"
msgstr "Шпицберген и Ян-Майен"

#: includes/bundles/countries-bundle.php:228
msgid "Swaziland"
msgstr "Свазиленд"

#: includes/bundles/countries-bundle.php:229
msgid "Sweden"
msgstr "Швеция"

#: includes/bundles/countries-bundle.php:230
msgid "Switzerland"
msgstr "Швейцария"

#: includes/bundles/countries-bundle.php:231
msgid "Syria"
msgstr "Сирия"

#: includes/bundles/countries-bundle.php:232
msgid "Taiwan"
msgstr "Тайвань"

#: includes/bundles/countries-bundle.php:233
msgid "Tajikistan"
msgstr "Таджикистан"

#: includes/bundles/countries-bundle.php:234
msgid "Tanzania"
msgstr "Танзания"

#: includes/bundles/countries-bundle.php:235
msgid "Thailand"
msgstr "Таиланд"

#: includes/bundles/countries-bundle.php:236
msgid "Timor-Leste"
msgstr "Тимор-Лесте"

#: includes/bundles/countries-bundle.php:237
msgid "Togo"
msgstr "Того"

#: includes/bundles/countries-bundle.php:238
msgid "Tokelau"
msgstr "Токелау"

#: includes/bundles/countries-bundle.php:239
msgid "Tonga"
msgstr "Тонга"

#: includes/bundles/countries-bundle.php:240
msgid "Trinidad and Tobago"
msgstr "Тринидад и Тобаго"

#: includes/bundles/countries-bundle.php:241
msgid "Tunisia"
msgstr "Тунис"

#: includes/bundles/countries-bundle.php:242
msgid "Turkey"
msgstr "Турция"

#: includes/bundles/countries-bundle.php:243
msgid "Turkmenistan"
msgstr "Туркменистан"

#: includes/bundles/countries-bundle.php:244
msgid "Turks and Caicos Islands"
msgstr "Теркс и Кайкос"

#: includes/bundles/countries-bundle.php:245
msgid "Tuvalu"
msgstr "Тувалу"

#: includes/bundles/countries-bundle.php:246
msgid "Uganda"
msgstr "Уганда"

#: includes/bundles/countries-bundle.php:247
msgid "Ukraine"
msgstr "Украина"

#: includes/bundles/countries-bundle.php:248
msgid "United Arab Emirates"
msgstr "Объединенные Арабские Эмираты"

#: includes/bundles/countries-bundle.php:249
msgid "United Kingdom (UK)"
msgstr "Великобритания"

#: includes/bundles/countries-bundle.php:250
msgid "United States (US)"
msgstr "Соединенные Штаты Америки"

#: includes/bundles/countries-bundle.php:251
msgid "United States (US) Minor Outlying Islands"
msgstr "Внешние малые острова США"

#: includes/bundles/countries-bundle.php:252
msgid "United States (US) Virgin Islands"
msgstr "Виргинские Острова (США)"

#: includes/bundles/countries-bundle.php:253
msgid "Uruguay"
msgstr "Уругвай"

#: includes/bundles/countries-bundle.php:254
msgid "Uzbekistan"
msgstr "Узбекистан"

#: includes/bundles/countries-bundle.php:255
msgid "Vanuatu"
msgstr "Вануату"

#: includes/bundles/countries-bundle.php:256
msgid "Vatican"
msgstr "Ватикан"

#: includes/bundles/countries-bundle.php:257
msgid "Venezuela"
msgstr "Венесуэла"

#: includes/bundles/countries-bundle.php:258
msgid "Vietnam"
msgstr "Вьетнам"

#: includes/bundles/countries-bundle.php:259
msgid "Wallis and Futuna"
msgstr "Уоллис и Футуна"

#: includes/bundles/countries-bundle.php:260
msgid "Western Sahara"
msgstr "Западная Сахара"

#: includes/bundles/countries-bundle.php:261
msgid "Samoa"
msgstr "Самоа"

#: includes/bundles/countries-bundle.php:262
msgid "Yemen"
msgstr "Йемен"

#: includes/bundles/countries-bundle.php:263
msgid "Zambia"
msgstr "Замбия"

#: includes/bundles/countries-bundle.php:264
msgid "Zimbabwe"
msgstr "Зимбабве"

#: includes/bundles/currency-bundle.php:17
msgid "Euro"
msgstr "Евро"

#: includes/bundles/currency-bundle.php:18
msgid "United States (US) dollar"
msgstr "Доллар США"

#: includes/bundles/currency-bundle.php:19
msgid "Pound sterling"
msgstr "Фунт стерлингов"

#: includes/bundles/currency-bundle.php:20
msgid "United Arab Emirates dirham"
msgstr "Дирхам ОАЭ"

#: includes/bundles/currency-bundle.php:21
msgid "Afghan afghani"
msgstr "Афганский афгани"

#: includes/bundles/currency-bundle.php:22
msgid "Albanian lek"
msgstr "Албанский лек"

#: includes/bundles/currency-bundle.php:23
msgid "Armenian dram"
msgstr "Армянский драм"

#: includes/bundles/currency-bundle.php:24
msgid "Netherlands Antillean guilder"
msgstr "Нидерландский антильский гульден"

#: includes/bundles/currency-bundle.php:25
msgid "Angolan kwanza"
msgstr "Ангольская кванза"

#: includes/bundles/currency-bundle.php:26
msgid "Argentine peso"
msgstr "Аргентинский песо"

#: includes/bundles/currency-bundle.php:27
msgid "Australian dollar"
msgstr "Австралийский доллар"

#: includes/bundles/currency-bundle.php:28
msgid "Aruban florin"
msgstr "Арубанский флорин"

#: includes/bundles/currency-bundle.php:29
msgid "Azerbaijani manat"
msgstr "Азербайджанский манат"

#: includes/bundles/currency-bundle.php:30
msgid "Bosnia and Herzegovina convertible mark"
msgstr "Конвертируемая марка Боснии и Герцеговины"

#: includes/bundles/currency-bundle.php:31
msgid "Barbadian dollar"
msgstr "Барбадосский доллар"

#: includes/bundles/currency-bundle.php:32
msgid "Bangladeshi taka"
msgstr "Бангладешская така"

#: includes/bundles/currency-bundle.php:33
msgid "Bulgarian lev"
msgstr "Болгарский лев"

#: includes/bundles/currency-bundle.php:34
msgid "Bahraini dinar"
msgstr "Бахрейнский динар"

#: includes/bundles/currency-bundle.php:35
msgid "Burundian franc"
msgstr "Бурундийский франк"

#: includes/bundles/currency-bundle.php:36
msgid "Bermudian dollar"
msgstr "Бермудский доллар"

#: includes/bundles/currency-bundle.php:37
msgid "Brunei dollar"
msgstr "Брунейский доллар"

#: includes/bundles/currency-bundle.php:38
msgid "Bolivian boliviano"
msgstr "Боливийский боливиано"

#: includes/bundles/currency-bundle.php:39
msgid "Brazilian real"
msgstr "Бразильский реал"

#: includes/bundles/currency-bundle.php:40
msgid "Bahamian dollar"
msgstr "Багамский доллар"

#: includes/bundles/currency-bundle.php:41
msgid "Bitcoin"
msgstr "Биткойн"

#: includes/bundles/currency-bundle.php:42
msgid "Bhutanese ngultrum"
msgstr "Бутанский нгултрум"

#: includes/bundles/currency-bundle.php:43
msgid "Botswana pula"
msgstr "Ботсванская пула"

#: includes/bundles/currency-bundle.php:44
msgid "Belarusian ruble (old)"
msgstr "Белорусский рубль (старый)"

#: includes/bundles/currency-bundle.php:45
msgid "Belarusian ruble"
msgstr "Белорусский рубль"

#: includes/bundles/currency-bundle.php:46
msgid "Belize dollar"
msgstr "Белизский доллар"

#: includes/bundles/currency-bundle.php:47
msgid "Canadian dollar"
msgstr "Канадский доллар"

#: includes/bundles/currency-bundle.php:48
msgid "Congolese franc"
msgstr "Конголезский франк"

#: includes/bundles/currency-bundle.php:49
msgid "Swiss franc"
msgstr "Швейцарский франк"

#: includes/bundles/currency-bundle.php:50
msgid "Chilean peso"
msgstr "Чилийский песо"

#: includes/bundles/currency-bundle.php:51
msgid "Chinese yuan"
msgstr "Китайский юань"

#: includes/bundles/currency-bundle.php:52
msgid "Colombian peso"
msgstr "Колумбийский песо"

#: includes/bundles/currency-bundle.php:53
msgid "Costa Rican col&oacute;n"
msgstr "Коста-риканский колон"

#: includes/bundles/currency-bundle.php:54
msgid "Cuban convertible peso"
msgstr "Кубинское конвертируемое песо"

#: includes/bundles/currency-bundle.php:55
msgid "Cuban peso"
msgstr "Кубинский песо"

#: includes/bundles/currency-bundle.php:56
msgid "Cape Verdean escudo"
msgstr "Эскудо Кабо-Верде"

#: includes/bundles/currency-bundle.php:57
msgid "Czech koruna"
msgstr "Чешская крона"

#: includes/bundles/currency-bundle.php:58
msgid "Djiboutian franc"
msgstr "Франк Джибути"

#: includes/bundles/currency-bundle.php:59
msgid "Danish krone"
msgstr "Датская крона"

#: includes/bundles/currency-bundle.php:60
msgid "Dominican peso"
msgstr "Доминиканский песо"

#: includes/bundles/currency-bundle.php:61
msgid "Algerian dinar"
msgstr "Алжирский динар"

#: includes/bundles/currency-bundle.php:62
msgid "Egyptian pound"
msgstr "Египетский фунт"

#: includes/bundles/currency-bundle.php:63
msgid "Eritrean nakfa"
msgstr "Эритрейская накфа"

#: includes/bundles/currency-bundle.php:64
msgid "Ethiopian birr"
msgstr "Эфиопский быр"

#: includes/bundles/currency-bundle.php:65
msgid "Fijian dollar"
msgstr "Фиджийский доллар"

#: includes/bundles/currency-bundle.php:66
msgid "Falkland Islands pound"
msgstr "Фунт Фолклендских островов"

#: includes/bundles/currency-bundle.php:67
msgid "Georgian lari"
msgstr "Грузинский лари"

#: includes/bundles/currency-bundle.php:68
msgid "Guernsey pound"
msgstr "Гернсийский фунт"

#: includes/bundles/currency-bundle.php:69
msgid "Ghana cedi"
msgstr "Ганский седи"

#: includes/bundles/currency-bundle.php:70
msgid "Gibraltar pound"
msgstr "Гибралтарский фунт"

#: includes/bundles/currency-bundle.php:71
msgid "Gambian dalasi"
msgstr "Гамбийский даласи"

#: includes/bundles/currency-bundle.php:72
msgid "Guinean franc"
msgstr "Гвинейский франк"

#: includes/bundles/currency-bundle.php:73
msgid "Guatemalan quetzal"
msgstr "Гватемальский кетсаль"

#: includes/bundles/currency-bundle.php:74
msgid "Guyanese dollar"
msgstr "Гайанский доллар"

#: includes/bundles/currency-bundle.php:75
msgid "Hong Kong dollar"
msgstr "Гонконгский доллар"

#: includes/bundles/currency-bundle.php:76
msgid "Honduran lempira"
msgstr "Гондурасская лемпира"

#: includes/bundles/currency-bundle.php:77
msgid "Croatian kuna"
msgstr "Хорватская куна"

#: includes/bundles/currency-bundle.php:78
msgid "Haitian gourde"
msgstr "Гаитянский гурд"

#: includes/bundles/currency-bundle.php:79
msgid "Hungarian forint"
msgstr "Венгерский форинт"

#: includes/bundles/currency-bundle.php:80
msgid "Indonesian rupiah"
msgstr "Индонезийская рупия"

#: includes/bundles/currency-bundle.php:81
msgid "Israeli new shekel"
msgstr "Новый израильский шекель"

#: includes/bundles/currency-bundle.php:82
msgid "Manx pound"
msgstr "Фунт Острова Мэн"

#: includes/bundles/currency-bundle.php:83
msgid "Indian rupee"
msgstr "Индийская рупия"

#: includes/bundles/currency-bundle.php:84
msgid "Iraqi dinar"
msgstr "Иракский динар"

#: includes/bundles/currency-bundle.php:85
msgid "Iranian rial"
msgstr "Иранский риал"

#: includes/bundles/currency-bundle.php:86
msgid "Iranian toman"
msgstr "Персидский туман"

#: includes/bundles/currency-bundle.php:87
msgid "Icelandic kr&oacute;na"
msgstr "Исландская крона"

#: includes/bundles/currency-bundle.php:88
msgid "Jersey pound"
msgstr "Джерсийский фунт"

#: includes/bundles/currency-bundle.php:89
msgid "Jamaican dollar"
msgstr "Ямайский доллар"

#: includes/bundles/currency-bundle.php:90
msgid "Jordanian dinar"
msgstr "Иорданский динар"

#: includes/bundles/currency-bundle.php:91
msgid "Japanese yen"
msgstr "Японская иена"

#: includes/bundles/currency-bundle.php:92
msgid "Kenyan shilling"
msgstr "Кенийский шиллинг"

#: includes/bundles/currency-bundle.php:93
msgid "Kyrgyzstani som"
msgstr "Киргизский сом"

#: includes/bundles/currency-bundle.php:94
msgid "Cambodian riel"
msgstr "Камбоджийский риель"

#: includes/bundles/currency-bundle.php:95
msgid "Comorian franc"
msgstr "Коморский франк"

#: includes/bundles/currency-bundle.php:96
msgid "North Korean won"
msgstr "Северокорейская вона"

#: includes/bundles/currency-bundle.php:97
msgid "South Korean won"
msgstr "Южнокорейская вона"

#: includes/bundles/currency-bundle.php:98
msgid "Kuwaiti dinar"
msgstr "Кувейтский динар"

#: includes/bundles/currency-bundle.php:99
msgid "Cayman Islands dollar"
msgstr "Доллар Каймановых островов"

#: includes/bundles/currency-bundle.php:100
msgid "Kazakhstani tenge"
msgstr "Казахстанский тенге"

#: includes/bundles/currency-bundle.php:101
msgid "Lao kip"
msgstr "Лаосский кип"

#: includes/bundles/currency-bundle.php:102
msgid "Lebanese pound"
msgstr "Ливанский фунт"

#: includes/bundles/currency-bundle.php:103
msgid "Sri Lankan rupee"
msgstr "Шри-Ланкийская рупия"

#: includes/bundles/currency-bundle.php:104
msgid "Liberian dollar"
msgstr "Доллар Либерии"

#: includes/bundles/currency-bundle.php:105
msgid "Lesotho loti"
msgstr "Лоти Лесото"

#: includes/bundles/currency-bundle.php:106
msgid "Libyan dinar"
msgstr "Ливийский динар"

#: includes/bundles/currency-bundle.php:107
msgid "Moroccan dirham"
msgstr "Марокканский дирхам"

#: includes/bundles/currency-bundle.php:108
msgid "Moldovan leu"
msgstr "Молдавский лей"

#: includes/bundles/currency-bundle.php:109
msgid "Malagasy ariary"
msgstr "Малагасийский ариари"

#: includes/bundles/currency-bundle.php:110
msgid "Macedonian denar"
msgstr "Македонский денар"

#: includes/bundles/currency-bundle.php:111
msgid "Burmese kyat"
msgstr "Бирманский кять"

#: includes/bundles/currency-bundle.php:112
msgid "Mongolian t&ouml;gr&ouml;g"
msgstr "Монгольский тугрик"

#: includes/bundles/currency-bundle.php:113
msgid "Macanese pataca"
msgstr "Патака Макао"

#: includes/bundles/currency-bundle.php:114
msgid "Mauritanian ouguiya"
msgstr "Мавританская угия"

#: includes/bundles/currency-bundle.php:115
msgid "Mauritian rupee"
msgstr "Маврикийская рупия"

#: includes/bundles/currency-bundle.php:116
msgid "Maldivian rufiyaa"
msgstr "Мальдивская руфия"

#: includes/bundles/currency-bundle.php:117
msgid "Malawian kwacha"
msgstr "Малавийская квача"

#: includes/bundles/currency-bundle.php:118
msgid "Mexican peso"
msgstr "Мексиканское песо"

#: includes/bundles/currency-bundle.php:119
msgid "Malaysian ringgit"
msgstr "Малайзийский ринггит"

#: includes/bundles/currency-bundle.php:120
msgid "Mozambican metical"
msgstr "Мозамбикский метикал"

#: includes/bundles/currency-bundle.php:121
msgid "Namibian dollar"
msgstr "Намибийский доллар"

#: includes/bundles/currency-bundle.php:122
msgid "Nigerian naira"
msgstr "Нигерийская найра"

#: includes/bundles/currency-bundle.php:123
msgid "Nicaraguan c&oacute;rdoba"
msgstr "Никарагуанская кордоба"

#: includes/bundles/currency-bundle.php:124
msgid "Norwegian krone"
msgstr "Норвежская крона"

#: includes/bundles/currency-bundle.php:125
msgid "Nepalese rupee"
msgstr "Непальская рупия"

#: includes/bundles/currency-bundle.php:126
msgid "New Zealand dollar"
msgstr "Новозеландский доллар"

#: includes/bundles/currency-bundle.php:127
msgid "Omani rial"
msgstr "Оманский риал"

#: includes/bundles/currency-bundle.php:128
msgid "Panamanian balboa"
msgstr "Панамский бальбоа"

#: includes/bundles/currency-bundle.php:129
msgid "Sol"
msgstr "Соль"

#: includes/bundles/currency-bundle.php:130
msgid "Papua New Guinean kina"
msgstr "Кина Папуа - Новой Гвинеи"

#: includes/bundles/currency-bundle.php:131
msgid "Philippine peso"
msgstr "Филиппинское песо"

#: includes/bundles/currency-bundle.php:132
msgid "Pakistani rupee"
msgstr "Пакистанская рупия"

#: includes/bundles/currency-bundle.php:133
msgid "Polish z&#x142;oty"
msgstr "Польский злотый"

#: includes/bundles/currency-bundle.php:134
msgid "Transnistrian ruble"
msgstr "Приднестровский рубль"

#: includes/bundles/currency-bundle.php:135
msgid "Paraguayan guaran&iacute;"
msgstr "Парагвайский гуарани"

#: includes/bundles/currency-bundle.php:136
msgid "Qatari riyal"
msgstr "Катарский риал"

#: includes/bundles/currency-bundle.php:137
msgid "Romanian leu"
msgstr "Румынский лей"

#: includes/bundles/currency-bundle.php:138
msgid "Serbian dinar"
msgstr "Сербский динар"

#: includes/bundles/currency-bundle.php:139
msgid "Russian ruble"
msgstr "Российский рубль"

#: includes/bundles/currency-bundle.php:140
msgid "Rwandan franc"
msgstr "Руандийский франк"

#: includes/bundles/currency-bundle.php:141
msgid "Saudi riyal"
msgstr "Саудовский риял"

#: includes/bundles/currency-bundle.php:142
msgid "Solomon Islands dollar"
msgstr "Доллар Соломоновых Островов"

#: includes/bundles/currency-bundle.php:143
msgid "Seychellois rupee"
msgstr "Сейшельская рупия"

#: includes/bundles/currency-bundle.php:144
msgid "Sudanese pound"
msgstr "Суданский фунт"

#: includes/bundles/currency-bundle.php:145
msgid "Swedish krona"
msgstr "Шведская крона"

#: includes/bundles/currency-bundle.php:146
msgid "Singapore dollar"
msgstr "Сингапурский доллар"

#: includes/bundles/currency-bundle.php:147
msgid "Saint Helena pound"
msgstr "Фунт Святой Елены"

#: includes/bundles/currency-bundle.php:148
msgid "Sierra Leonean leone"
msgstr "Сьерра-Леонский леоне"

#: includes/bundles/currency-bundle.php:149
msgid "Somali shilling"
msgstr "Сомалийский шиллинг"

#: includes/bundles/currency-bundle.php:150
msgid "Surinamese dollar"
msgstr "Суринамский доллар"

#: includes/bundles/currency-bundle.php:151
msgid "South Sudanese pound"
msgstr "Южносуданский фунт"

#: includes/bundles/currency-bundle.php:152
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe dobra"
msgstr "Добра Сан-Томе и Принсипи"

#: includes/bundles/currency-bundle.php:153
msgid "Syrian pound"
msgstr "Сирийский фунт"

#: includes/bundles/currency-bundle.php:154
msgid "Swazi lilangeni"
msgstr "Свазилендский лилангени"

#: includes/bundles/currency-bundle.php:155
msgid "Thai baht"
msgstr "Тайский бат"

#: includes/bundles/currency-bundle.php:156
msgid "Tajikistani somoni"
msgstr "Таджикский сомони"

#: includes/bundles/currency-bundle.php:157
msgid "Turkmenistan manat"
msgstr "Туркменский манат"

#: includes/bundles/currency-bundle.php:158
msgid "Tunisian dinar"
msgstr "Тунисский динар"

#: includes/bundles/currency-bundle.php:159
msgid "Tongan pa&#x2bb;anga"
msgstr "Тонганская паанга"

#: includes/bundles/currency-bundle.php:160
msgid "Turkish lira"
msgstr "Турецкая лира"

#: includes/bundles/currency-bundle.php:161
msgid "Trinidad and Tobago dollar"
msgstr "Доллар Тринидада и Тобаго"

#: includes/bundles/currency-bundle.php:162
msgid "New Taiwan dollar"
msgstr "Новый тайваньский доллар"

#: includes/bundles/currency-bundle.php:163
msgid "Tanzanian shilling"
msgstr "Танзанийский шиллинг"

#: includes/bundles/currency-bundle.php:164
msgid "Ukrainian hryvnia"
msgstr "Украинская гривна"

#: includes/bundles/currency-bundle.php:165
msgid "Ugandan shilling"
msgstr "Угандийский шиллинг"

#: includes/bundles/currency-bundle.php:166
msgid "Uruguayan peso"
msgstr "Уругвайское песо"

#: includes/bundles/currency-bundle.php:167
msgid "Uzbekistani som"
msgstr "Узбекский сум"

#: includes/bundles/currency-bundle.php:168
msgid "Venezuelan bol&iacute;var"
msgstr "Венесуэльский боливар"

#: includes/bundles/currency-bundle.php:169
msgid "Bol&iacute;var soberano"
msgstr "Боливар Соберано"

#: includes/bundles/currency-bundle.php:170
msgid "Vietnamese &#x111;&#x1ed3;ng"
msgstr "Вьетнамский донг"

#: includes/bundles/currency-bundle.php:171
msgid "Vanuatu vatu"
msgstr "Вануатский вату"

#: includes/bundles/currency-bundle.php:172
msgid "Samoan t&#x101;l&#x101;"
msgstr "Самоанская тала"

#: includes/bundles/currency-bundle.php:173
msgid "Central African CFA franc"
msgstr "Центральноафриканский франк КФА"

#: includes/bundles/currency-bundle.php:174
msgid "East Caribbean dollar"
msgstr "Восточнокарибский доллар"

#: includes/bundles/currency-bundle.php:175
msgid "West African CFA franc"
msgstr "Франк КФА Западной Африки"

#: includes/bundles/currency-bundle.php:176
msgid "CFP franc"
msgstr "Французский тихоокеанский франк"

#: includes/bundles/currency-bundle.php:177
msgid "Yemeni rial"
msgstr "Йеменский риал"

#: includes/bundles/currency-bundle.php:178
msgid "South African rand"
msgstr "Южноафриканский ранд"

#: includes/bundles/currency-bundle.php:179
msgid "Zambian kwacha"
msgstr "Замбийская квача"

#: includes/bundles/currency-bundle.php:358
msgid "Before"
msgstr "Перед"

#: includes/bundles/currency-bundle.php:359
msgid "After"
msgstr "После"

#: includes/bundles/currency-bundle.php:360
msgid "Before with space"
msgstr "Слева с пробелом"

#: includes/bundles/currency-bundle.php:361
msgid "After with space"
msgstr "Справа с пробелом"

#: includes/bundles/customer-bundle.php:97
msgid "First name is required."
msgstr "Имя обязательно для заполнения."

#: includes/bundles/customer-bundle.php:106
msgid "Last name is required."
msgstr "Фамилия обязательна для заполнения."

#: includes/bundles/customer-bundle.php:115
msgid "Email is required."
msgstr "Адрес электронной почты обязателен для заполнения."

#: includes/bundles/customer-bundle.php:124
msgid "Phone is required."
msgstr "Телефон обязателен для заполнения."

#: includes/bundles/customer-bundle.php:128
#: includes/views/shortcodes/checkout-view.php:650
msgid "Country of residence"
msgstr "Страна проживания"

#: includes/bundles/customer-bundle.php:133
msgid "Country is required."
msgstr "Страна обязательна для заполнения."

#: includes/bundles/customer-bundle.php:142
msgid "Address is required."
msgstr "Адрес обязателен для заполнения."

#: includes/bundles/customer-bundle.php:151
msgid "City is required."
msgstr "Город обязателен для заполнения."

#: includes/bundles/customer-bundle.php:160
msgid "State is required."
msgstr "Штат обязателен для заполнения."

#: includes/bundles/customer-bundle.php:169
msgid "Postcode is required."
msgstr "Индекс обязателен для заполнения."

#: includes/bundles/customer-bundle.php:178
msgid "Note is required."
msgstr "Примечание необходимо."

#: includes/bundles/units-bundle.php:16
msgid "Square Meter"
msgstr "Квадратный метр"

#: includes/bundles/units-bundle.php:17
msgid "Square Foot"
msgstr "Квадратный фут"

#: includes/bundles/units-bundle.php:18
msgid "Square Yard"
msgstr "Квадратный ярд"

#: includes/bundles/units-bundle.php:21
msgid "m²"
msgstr "м²"

#: includes/bundles/units-bundle.php:22
msgid "ft²"
msgstr "фут²"

#: includes/bundles/units-bundle.php:23
msgid "yd²"
msgstr "ярд²"

#: includes/core/helpers/price-helper.php:57
msgctxt "Zero price"
msgid "Free"
msgstr "Бесплатно"

#. translators: Price per one night. Example: $99 per night
#: includes/core/helpers/price-helper.php:144
msgctxt "Price per one night. Example: $99 per night"
msgid "per night"
msgstr "за сутки"

#. translators: Price for X nights. Example: $99 for 2 nights, $99 for 21 nights
#: includes/core/helpers/price-helper.php:156
msgctxt "Price for X nights. Example: $99 for 2 nights, $99 for 21 nights"
msgid "for %d nights"
msgid_plural "for %d nights"
msgstr[0] "за %d сутки"
msgstr[1] "за %d суток"
msgstr[2] "за %d суток"
msgstr[3] "за %d суток"

#: includes/crons/cron-manager.php:112
msgid "User Approval Time setted in Hotel Booking Settings"
msgstr "Время на подтверждение бронирования клиентом, установленное в настройках плагина Hotel Booking"

#: includes/crons/cron-manager.php:117
msgid "Pending Payment Time set in Hotel Booking Settings"
msgstr "Время на подтверждение бронирования оплатой, установленное в настройках плагина Hotel Booking"

#: includes/crons/cron-manager.php:122
msgid "Interval for automatic cleaning of synchronization logs."
msgstr "Интервал для автоматической очистки журнала синхронизации."

#: includes/crons/cron-manager.php:127
msgid "Once a week"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:73
#: templates/account/bookings.php:19
#: templates/account/bookings.php:70
#: templates/create-booking/search/search-form.php:42
#: templates/edit-booking/edit-dates.php:29
#: templates/shortcodes/search/search-form.php:35
msgid "Check-in"
msgstr "Заезд"

#: includes/csv/bookings/bookings-exporter-helper.php:74
#: templates/account/bookings.php:20
#: templates/account/bookings.php:73
#: templates/create-booking/search/search-form.php:62
#: templates/edit-booking/edit-dates.php:38
#: templates/shortcodes/search/search-form.php:55
msgid "Check-out"
msgstr "Отъезд"

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/csv/bookings/bookings-exporter-helper.php:78
#: includes/post-types/rate-cpt.php:104
msgid "Rate"
msgstr "Тариф"

#: includes/csv/bookings/bookings-exporter-helper.php:79
msgid "Adults/Guests"
msgstr "Взрослые/Гости"

#: includes/csv/bookings/bookings-exporter-helper.php:91
#: includes/emails/templaters/reserved-rooms-templater.php:223
#: includes/views/edit-booking/checkout-view.php:164
#: includes/views/shortcodes/checkout-view.php:291
msgid "Full Guest Name"
msgstr "Полное имя гостя"

#: includes/csv/bookings/bookings-exporter-helper.php:92
#: includes/views/booking-view.php:141
msgid "Accommodation Subtotal"
msgstr "Промежуточный итог по варианту размещения"

#: includes/csv/bookings/bookings-exporter-helper.php:93
#: includes/post-types/coupon-cpt.php:72
#: includes/views/booking-view.php:150
msgid "Accommodation Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:94
#: includes/views/booking-view.php:160
msgid "Accommodation Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:96
#: includes/views/booking-view.php:186
msgid "Accommodation Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:98
#: includes/views/booking-view.php:225
msgid "Services Subtotal"
msgstr "Промежуточный итог по услугам"

#: includes/csv/bookings/bookings-exporter-helper.php:99
#: includes/views/booking-view.php:236
msgid "Services Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:100
#: includes/views/booking-view.php:248
msgid "Services Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:101
#: includes/views/booking-view.php:280
msgid "Service Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:103
#: includes/views/booking-view.php:312
msgid "Fees Subtotal"
msgstr "Промежуточный итог по сборам"

#: includes/csv/bookings/bookings-exporter-helper.php:104
#: includes/views/booking-view.php:321
msgid "Fees Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:105
#: includes/views/booking-view.php:331
msgid "Fees Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:106
#: includes/views/booking-view.php:364
msgid "Fee Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:108
msgid "Discount"
msgstr "Скидка"

#: includes/csv/bookings/bookings-exporter-helper.php:109
#: includes/views/booking-view.php:451
#: templates/account/bookings.php:21
#: templates/account/bookings.php:76
msgid "Total"
msgstr "Итого"

#: includes/csv/bookings/bookings-exporter-helper.php:110
msgid "Paid"
msgstr "Оплачено"

#: includes/csv/bookings/bookings-exporter-helper.php:111
#: includes/post-types/payment-cpt.php:129
#: includes/shortcodes/booking-confirmation-shortcode.php:284
msgid "Payment Details"
msgstr "Детали оплаты"

#: includes/csv/bookings/bookings-query.php:92
msgid "Please select columns to export."
msgstr "Пожалуйста, выберите столбцы для экспортирования."

#: includes/csv/csv-export-handler.php:32
#: includes/payments/gateways/stripe-gateway.php:559
msgid "Nonce verification failed."
msgstr "Не удалось выполнить проверку одноразового номера."

#: includes/csv/csv-export-handler.php:50
msgid "The file does not exist."
msgstr "Файл не существует."

#: includes/emails/abstract-email.php:441
msgid "Disable this email notification"
msgstr "Отключить этот тип уведомления"

#: includes/emails/abstract-email.php:449
msgid "Subject"
msgstr "Тема"

#: includes/emails/abstract-email.php:461
msgid "Header"
msgstr "Заголовок"

#: includes/emails/abstract-email.php:473
msgid "Email Template"
msgstr "Шаблон письма"

#: includes/emails/abstract-email.php:570
msgid "\"%s\" email will not be sent: there is no customer email in the booking."
msgstr "\"%s\" email не был отправлен: в бронировании не указан email адрес клиента."

#: includes/emails/abstract-email.php:594
msgid "Deprecated tags in header of %s"
msgstr "Устаревшие теги в заголовке письма %s"

#: includes/emails/abstract-email.php:597
msgid "Deprecated tags in subject of %s"
msgstr "Устаревшие теги в теме письма %s"

#: includes/emails/abstract-email.php:600
msgid "Deprecated tags in template of %s"
msgstr "Устаревшие теги в шаблоне %s"

#: includes/emails/booking/admin/base-email.php:37
msgid "Recipients"
msgstr "Получатели"

#: includes/emails/booking/admin/base-email.php:40
msgid "You can use multiple comma-separated emails"
msgstr "Вы можете использовать несколько email адресов, разделенных запятыми."

#: includes/emails/booking/admin/base-email.php:89
msgid "\"%s\" mail was sent to admin."
msgstr "\"%s\" письмо было отправлено администратору."

#: includes/emails/booking/admin/base-email.php:93
msgid "\"%s\" mail sending to admin is failed."
msgstr "\"%s\" отправка письма администратору не удалась."

#: includes/emails/booking/admin/cancelled-email.php:8
msgid "Booking Cancelled"
msgstr "Бронирование отменено"

#: includes/emails/booking/admin/cancelled-email.php:12
msgid "%site_title% - Booking #%booking_id% Cancelled"
msgstr "%site_title% - Бронирование #%booking_id% отменено"

#: includes/emails/booking/admin/cancelled-email.php:16
msgid "Email that will be sent to Admin when customer cancels booking."
msgstr "Письмо, которое будет отправляться администратору после отмены бронирования клиентом."

#: includes/emails/booking/admin/cancelled-email.php:20
#: includes/emails/booking/customer/cancelled-email.php:20
msgid "Cancelled Booking Email"
msgstr "Письмо об отмене бронирования"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:8
#: includes/emails/booking/admin/confirmed-email.php:8
#: includes/wizard.php:134
msgid "Booking Confirmed"
msgstr "Бронирование подтверждено"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:12
#: includes/emails/booking/admin/confirmed-email.php:12
msgid "%site_title% - Booking #%booking_id% Confirmed"
msgstr "%site_title% - Бронирование #%booking_id% Подтверждено"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:16
msgid "Email that will be sent to Admin when payment is completed."
msgstr "Письмо администратору об оплаченном бронировании."

#: includes/emails/booking/admin/confirmed-by-payment-email.php:20
msgid "Approved Booking Email (via payment)"
msgstr "Письмо о подтвержденном бронировании (оплаченном)"

#: includes/emails/booking/admin/confirmed-email.php:16
msgid "Email that will be sent to Admin when customer confirms booking."
msgstr "Письмо, которое будет отправлено администратору после подтверждения бронирования клиентом."

#: includes/emails/booking/admin/confirmed-email.php:20
#: includes/emails/booking/customer/approved-email.php:20
msgid "Approved Booking Email"
msgstr "Письмо о подтвержденном бронировании"

#: includes/emails/booking/admin/pending-email.php:8
msgid "Confirm new booking"
msgstr "Подтвердите новое бронирование"

#: includes/emails/booking/admin/pending-email.php:12
msgid "%site_title% - New booking #%booking_id%"
msgstr "%site_title% - Новое бронирование #%booking_id%"

#: includes/emails/booking/admin/pending-email.php:16
msgid "Email that will be sent to administrator after booking is placed."
msgstr "Письмо администратору о новом запросе на бронирование."

#: includes/emails/booking/admin/pending-email.php:20
msgid "Pending Booking Email"
msgstr "Письмо о бронировании, ожидающем подтверждения"

#: includes/emails/booking/customer/approved-email.php:8
msgid "Your booking is approved"
msgstr "Ваше бронирование подтверждено"

#: includes/emails/booking/customer/approved-email.php:12
msgid "%site_title% - Your booking #%booking_id% is approved"
msgstr "%site_title% - Ваше бронирование #%booking_id% подтверждено"

#: includes/emails/booking/customer/approved-email.php:16
msgid "Email that will be sent to customer when booking is approved."
msgstr "Письмо, которое отправляется клиенту после подтверждения бронирования."

#: includes/emails/booking/customer/base-email.php:55
msgid "\"%s\" mail was sent to customer."
msgstr "\"%s\" письмо отправлено клиенту."

#: includes/emails/booking/customer/base-email.php:59
msgid "\"%s\" mail sending is failed."
msgstr "\"%s\" не удалось отправить письмо."

#: includes/emails/booking/customer/cancelled-email.php:8
msgid "Your booking is cancelled"
msgstr "Ваше бронирование отменено"

#: includes/emails/booking/customer/cancelled-email.php:12
msgid "%site_title% - Your booking #%booking_id% is cancelled"
msgstr "%site_title% - Ваше бронирование #%booking_id% отменено"

#: includes/emails/booking/customer/cancelled-email.php:16
msgid "Email that will be sent to customer when booking is cancelled."
msgstr "Письмо, которое будет отправляться клиенту после отмены бронирования."

#: includes/emails/booking/customer/confirmation-email.php:8
msgid "Confirm your booking"
msgstr "Подтвердите Ваше бронирование"

#: includes/emails/booking/customer/confirmation-email.php:12
msgid "%site_title% - Confirm your booking #%booking_id%"
msgstr "%site_title% - Подтвердите Ваше бронирование #%booking_id%"

#: includes/emails/booking/customer/confirmation-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Customer confirmation via email."
msgstr "Это письмо отправляется, если в \"Режиме подтверждения бронирования\" выбрано \"Клиентом по почте\"."

#: includes/emails/booking/customer/confirmation-email.php:17
#: includes/emails/booking/customer/direct-bank-transfer-email.php:43
#: includes/emails/booking/customer/pending-email.php:17
msgid "Email that will be sent to customer after booking is placed."
msgstr "Письмо, которое отправляется клиенту после запроса на бронирование."

#: includes/emails/booking/customer/confirmation-email.php:21
msgid "New Booking Email (Confirmation by User)"
msgstr "Письмо о новом бронировании (подтверждается пользователем)"

#: includes/emails/booking/customer/direct-bank-transfer-email.php:35
msgid "Pay for your booking"
msgstr ""

#: includes/emails/booking/customer/direct-bank-transfer-email.php:39
msgid "%site_title% - Pay for your booking #%booking_id%"
msgstr ""

#: includes/emails/booking/customer/direct-bank-transfer-email.php:47
msgid "Payment Instructions Email"
msgstr ""

#: includes/emails/booking/customer/pending-email.php:8
msgid "Your booking is placed"
msgstr "Ваш запрос на бронирование отправлен"

#: includes/emails/booking/customer/pending-email.php:12
msgid "%site_title% - Booking #%booking_id% is placed"
msgstr "%site_title% - Запрос на бронирование #%booking_id% отправлен"

#: includes/emails/booking/customer/pending-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Admin confirmation."
msgstr "Это письмо отправляется, если в \"Режиме подтверждения бронирования\" выбрано \"Администратором вручную\""

#: includes/emails/booking/customer/pending-email.php:21
msgid "New Booking Email (Confirmation by Admin)"
msgstr "Письмо о новом бронировании (подтверждается администратором)"

#: includes/emails/booking/customer/registration-email.php:8
msgid "Welcome"
msgstr "Добро пожаловать"

#: includes/emails/booking/customer/registration-email.php:12
msgid "%site_title% - account details"
msgstr "%site_title% - данные аккаунта"

#: includes/emails/booking/customer/registration-email.php:16
msgid "Email that will be sent to a customer after they registered on your site."
msgstr "E-mail, который будет отправлен клиенту после регистрации на вашем сайте."

#: includes/emails/booking/customer/registration-email.php:20
msgid "Customer Registration Email"
msgstr "Email регистрации клиента"

#: includes/emails/templaters/abstract-templater.php:77
msgid "Email Tags"
msgstr ""

#: includes/emails/templaters/abstract-templater.php:87
#: includes/emails/templaters/abstract-templater.php:89
msgid "Deprecated."
msgstr "Устарело"

#: includes/emails/templaters/abstract-templater.php:101
msgid "none"
msgstr "Нет"

#: includes/emails/templaters/cancellation-booking-templater.php:50
msgid "User Cancellation Link"
msgstr "Ссылка для отмены бронирования для пользователя"

#: includes/emails/templaters/email-templater.php:109
msgid "Site title (set in Settings > General)"
msgstr "Название сайта (установлено в Настройки > Общие)"

#: includes/emails/templaters/email-templater.php:124
#: includes/post-types/payment-cpt.php:232
msgid "Booking ID"
msgstr "ID бронирования"

#: includes/emails/templaters/email-templater.php:128
msgid "Booking Edit Link"
msgstr "Ссылка на редактирование бронирования"

#: includes/emails/templaters/email-templater.php:132
msgid "Booking Total Price"
msgstr "Итоговая стоимость бронирования"

#: includes/emails/templaters/email-templater.php:153
#: includes/emails/templaters/email-templater.php:296
msgid "Customer First Name"
msgstr "Имя клиента"

#: includes/emails/templaters/email-templater.php:157
#: includes/emails/templaters/email-templater.php:300
msgid "Customer Last Name"
msgstr "Фамилия клиента"

#: includes/emails/templaters/email-templater.php:161
#: includes/emails/templaters/email-templater.php:304
msgid "Customer Email"
msgstr "Email клиента"

#: includes/emails/templaters/email-templater.php:165
#: includes/emails/templaters/email-templater.php:308
msgid "Customer Phone"
msgstr "Телефон клиента"

#: includes/emails/templaters/email-templater.php:169
#: includes/emails/templaters/email-templater.php:312
msgid "Customer Country"
msgstr "Страна клиента"

#: includes/emails/templaters/email-templater.php:173
#: includes/emails/templaters/email-templater.php:316
msgid "Customer Address"
msgstr "Адрес клиента"

#: includes/emails/templaters/email-templater.php:177
#: includes/emails/templaters/email-templater.php:320
msgid "Customer City"
msgstr "Город клиента"

#: includes/emails/templaters/email-templater.php:181
#: includes/emails/templaters/email-templater.php:324
msgid "Customer State/County"
msgstr "Штат/Административная единица клиента"

#: includes/emails/templaters/email-templater.php:185
#: includes/emails/templaters/email-templater.php:328
msgid "Customer Postcode"
msgstr "Индекс клиента"

#: includes/emails/templaters/email-templater.php:194
msgid "Reserved Accommodations Details"
msgstr "Детали забронированного варианта размещения"

#: includes/emails/templaters/email-templater.php:216
#: includes/views/create-booking/checkout-view.php:15
#: includes/views/shortcodes/checkout-view.php:164
#: templates/shortcodes/booking-details/booking-details.php:18
msgid "Booking Details"
msgstr "Детали бронирования"

#: includes/emails/templaters/email-templater.php:230
msgid "Confirmation Link"
msgstr "Ссылка для подтверждения бронирования"

#: includes/emails/templaters/email-templater.php:234
msgid "Confirmation Link Expiration Time ( UTC )"
msgstr "Время истечения срока ссылки для подтверждения бронирования (UTC)"

#: includes/emails/templaters/email-templater.php:248
msgid "Cancellation Details (if enabled)"
msgstr "Детали отмены бронирования (если возможность отмены включена)"

#: includes/emails/templaters/email-templater.php:262
msgid "The total amount of payment"
msgstr "Общая сумма платежа"

#: includes/emails/templaters/email-templater.php:266
msgid "The unique ID of payment"
msgstr "Уникальный ID платежа"

#: includes/emails/templaters/email-templater.php:270
msgid "The method of payment"
msgstr "Способ оплаты"

#: includes/emails/templaters/email-templater.php:274
msgid "Payment instructions"
msgstr "Инструкция к оплате"

#: includes/emails/templaters/email-templater.php:288
msgid "User login"
msgstr "Логин пользователя"

#: includes/emails/templaters/email-templater.php:292
msgid "User password"
msgstr "Пароль пользователя"

#: includes/emails/templaters/email-templater.php:332
msgid "Link to My Account page"
msgstr "Ссылка на страницу Мой аккаунт"

#: includes/emails/templaters/email-templater.php:562
#: includes/upgrader.php:868
#: includes/wizard.php:213
msgid "My Account"
msgstr "Мой аккаунт"

#: includes/emails/templaters/reserved-rooms-templater.php:191
msgid "Accommodation Type Link"
msgstr "Ссылка типа размещения"

#: includes/emails/templaters/reserved-rooms-templater.php:195
msgid "Accommodation Type Title"
msgstr "Название типа размещения"

#: includes/emails/templaters/reserved-rooms-templater.php:199
msgid "Accommodation Title"
msgstr ""

#: includes/emails/templaters/reserved-rooms-templater.php:203
msgid "Accommodation Type Categories"
msgstr "Категории типа размещения"

#: includes/emails/templaters/reserved-rooms-templater.php:207
msgid "Accommodation Type Bed"
msgstr "Кровать в типе размещения"

#: includes/emails/templaters/reserved-rooms-templater.php:211
msgid "Accommodation Rate Title"
msgstr "Название тарифа"

#: includes/emails/templaters/reserved-rooms-templater.php:215
msgid "Accommodation Rate Description"
msgstr "Описание тарифа"

#: includes/emails/templaters/reserved-rooms-templater.php:219
msgid "Sequential Number of Accommodation"
msgstr "Порядковый номер варианта размещения"

#: includes/entities/coupon.php:370
msgid "This coupon has expired."
msgstr "Истёк срок годности этого купона."

#: includes/entities/coupon.php:374
msgid "Sorry, this coupon is not applicable to your booking contents."
msgstr "Извините, этот купон не распространяется на условия вашего бронирования."

#: includes/entities/coupon.php:378
msgid "Coupon usage limit has been reached."
msgstr "Достигнут лимит использования купона."

#: includes/entities/reserved-service.php:98
msgid " &#215; %d night"
msgid_plural " &#215; %d nights"
msgstr[0] " &#215; %d день"
msgstr[1] " &#215; %d дня"
msgstr[2] " &#215; %d дней"
msgstr[3] ""

#: includes/entities/reserved-service.php:103
#: includes/shortcodes/search-results-shortcode.php:904
msgid "%d adult"
msgid_plural "%d adults"
msgstr[0] "%d взрослый"
msgstr[1] "%d взрослых"
msgstr[2] "%d взрослых"
msgstr[3] ""

#: includes/entities/reserved-service.php:105
#: includes/shortcodes/search-results-shortcode.php:896
#: includes/shortcodes/search-results-shortcode.php:900
msgid "%d guest"
msgid_plural "%d guests"
msgstr[0] "%d гость"
msgstr[1] "%d гостя"
msgstr[2] "%d гостей"
msgstr[3] ""

#: includes/entities/reserved-service.php:110
msgid " &#215; %d time"
msgid_plural " &#215; %d times"
msgstr[0] " &#215; %d раз"
msgstr[1] " &#215; %d раза"
msgstr[2] "&#215; %d раз"
msgstr[3] ""

#: includes/entities/service.php:195
msgid "Per Instance"
msgstr "За единицу"

#: includes/i-cal/background-processes/background-synchronizer.php:34
msgid "Maximum execution time is set to %d seconds."
msgstr "Максимальное время выполнения установлено на %d секунд."

#: includes/i-cal/background-processes/background-synchronizer.php:80
msgid "%d URL pulled for parsing."
msgid_plural "%d URLs pulled for parsing."
msgstr[0] "%d URL взят на обработку."
msgstr[1] "%d URL взято на обработку."
msgstr[2] "%d URL взято на обработку."
msgstr[3] ""

#: includes/i-cal/background-processes/background-synchronizer.php:82
msgid "Skipped. No URLs found for parsing."
msgstr "Пропущено. Нет URL для обработки."

#: includes/i-cal/background-processes/background-uploader.php:64
msgid "Cannot read uploaded file"
msgstr "Невозможно прочитать загруженный файл"

#: includes/i-cal/background-processes/background-worker.php:327
msgctxt "%s - calendar URI or calendar filename"
msgid "%1$d event found in calendar %2$s"
msgid_plural "%1$d events found in calendar %2$s"
msgstr[0] "%1$d событие найдено в календаре %2$s"
msgstr[1] "%1$d события найдены в %2$s"
msgstr[2] "%1$d событий найдено в календаре %2$s"
msgstr[3] ""

#: includes/i-cal/background-processes/background-worker.php:357
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar source is empty (%s)"
msgstr "Источник календаря пустой (%s)"

#: includes/i-cal/background-processes/background-worker.php:370
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar file is not empty, but there are no events in %s"
msgstr "Файл календаря не пустой, но нет событий в %s"

#: includes/i-cal/background-processes/background-worker.php:403
msgid "We will need to check %d previous booking after importing and remove it if the booking is outdated."
msgid_plural "We will need to check %d previous bookings after importing and remove the outdated ones."
msgstr[0] "Нам нужно будет проверить %d предыдущее бронирование после импорта и удалить их, если бронирования устарели."
msgstr[1] "Нам нужно будет проверить %d предыдущих бронирований после импорта и удалить их, если бронирования устарели."
msgstr[2] "Нам нужно будет проверить %d предыдущих бронирований после импорта и удалить их, если бронирования устарели."
msgstr[3] ""

#: includes/i-cal/background-processes/background-worker.php:425
msgid "Error while loading calendar (%1$s): %2$s"
msgstr "Ошибка во время загрузки календаря (%1$s): %2$s"

#: includes/i-cal/background-processes/background-worker.php:427
msgctxt "%s - error description"
msgid "Parse error. %s"
msgstr "Ошибка обработки. %s"

#: includes/i-cal/background-processes/background-worker.php:468
msgid "Skipped. Outdated booking #%d already removed."
msgstr "Пропущено. Устаревшее бронирование #%d уже удалено."

#: includes/i-cal/background-processes/background-worker.php:475
msgid "Skipped. Booking #%d updated with new data."
msgstr "Пропущено. Бронирование #%d обновлено новой информацией. "

#: includes/i-cal/background-processes/background-worker.php:497
msgid "The outdated booking #%d has been removed."
msgstr "Устаревшее бронирование #%d было удалено. "

#: includes/i-cal/importer.php:104
msgid "Skipped. Event from %1$s to %2$s has passed."
msgstr "Пропущено. Событие с %1$s по %2$s прошло."

#: includes/i-cal/importer.php:120
msgid "New booking #%1$d. The dates from %2$s to %3$s are now blocked."
msgstr "Новое бронирование #%1$d. Даты с %2$s по %3$s заблокированы."

#: includes/i-cal/importer.php:140
msgid "Success. Booking #%d updated with new data."
msgstr "Успешно. Бронирование #%d обновлено новой информацией."

#: includes/i-cal/importer.php:148
msgid "Skipped. The dates from %1$s to %2$s are already blocked."
msgstr "Пропущено. Даты с %1$s по %2$s уже заблокированы."

#: includes/i-cal/importer.php:164
msgid "Success. Booking #%1$d updated with new data. Removed %2$d outdated booking."
msgid_plural "Success. Booking #%1$d updated with new data. Removed %2$d outdated bookings."
msgstr[0] "Успешно. Бронирование #%1$d обновлено новыми данными. Удалено %2$d устаревшее бронирование.  "
msgstr[1] "Успешно. Бронирование #%1$d обновлено новыми данными. Удалено %2$d устаревших бронирований.  "
msgstr[2] "Успешно. Бронирование #%1$d обновлено новыми данными. Удалено %2$d устаревших бронирований.  "
msgstr[3] ""

#: includes/i-cal/importer.php:166
msgid "Success. Booking #%1$d updated with new data."
msgstr "Успешно. Бронирование #%1$d обновлено новыми данными. "

#: includes/i-cal/importer.php:177
msgid "Cannot import new event. Dates from %1$s to %2$s are partially blocked by booking %3$s."
msgid_plural "Cannot import new event. Dates from %1$s to %2$s are partially blocked by bookings %3$s."
msgstr[0] "Невозможно импортировать новое событие. Даты с %1$s по  %2$s частино заблокированы бронированием %3$s."
msgstr[1] "Невозможно импортировать новое событие. Даты с %1$s по  %2$s частино заблокированы бронированиями %3$s."
msgstr[2] "Невозможно импортировать новое событие. Даты с %1$s по  %2$s частино заблокированы бронированиями %3$s."
msgstr[3] ""

#: includes/i-cal/importer.php:233
msgid "Booking imported with UID %1$s.<br />Summary: %2$s.<br />Description: %3$s.<br />Source: %4$s."
msgstr "Бронирование импортировано с UID %1$s.<br />Сводка: %2$s.<br />Описание: %3$s.<br />Источник: %4$s."

#: includes/i-cal/logs-handler.php:25
msgid "Process Information"
msgstr "Обработать информацию"

#: includes/i-cal/logs-handler.php:35
msgid "Total bookings: %s"
msgstr "Всего бронирований: %s"

#: includes/i-cal/logs-handler.php:37
msgid "Success bookings: %s"
msgstr "Удачные бронирования: %s"

#: includes/i-cal/logs-handler.php:39
msgid "Skipped bookings: %s"
msgstr "Пропущенные бронирования: %s"

#: includes/i-cal/logs-handler.php:41
msgid "Failed bookings: %s"
msgstr "Неудавшиеся бронирования: %s"

#: includes/i-cal/logs-handler.php:43
msgid "Removed bookings: %s"
msgstr "Удаленные бронирования: %s"

#: includes/i-cal/logs-handler.php:87
msgid "Expand All"
msgstr "Развернуть все"

#: includes/i-cal/logs-handler.php:91
msgid "Collapse All"
msgstr "Свернуть все"

#: includes/i-cal/logs-handler.php:138
msgid "All done! %1$d booking was successfully added."
msgid_plural "All done! %1$d bookings were successfully added."
msgstr[0] "Готово! %1$d бронирование было добавлено."
msgstr[1] "Готово! %1$d бронирования было добавлено."
msgstr[2] "Готово! %1$d бронирований было добавлено."
msgstr[3] ""

#: includes/i-cal/logs-handler.php:139
msgid " There was %2$d failure."
msgid_plural " There were %2$d failures."
msgstr[0] " Обнаружена %2$d ошибка."
msgstr[1] " Обнаружено %2$d ошибки."
msgstr[2] " Обнаружено %2$d ошибок."
msgstr[3] ""

#: includes/license-notice.php:87
#: includes/license-notice.php:160
msgid "Your License Key is not active. Please, <a href=\"%s\">activate your License Key</a> to get plugin updates."
msgstr ""

#: includes/license-notice.php:152
msgid "Dismiss "
msgstr "Отклонить "

#: includes/linked-rooms.php:31
msgid "Blocked because the linked accommodation is booked"
msgstr ""

#: includes/notices.php:138
#: includes/notices.php:156
#: includes/wizard.php:33
msgid "Hotel Booking Plugin"
msgstr "Плагин Hotel Booking"

#: includes/notices.php:139
msgid "Your database is being updated in the background."
msgstr "Ваша база данных обновляется в фоновом режиме."

#: includes/notices.php:141
msgid "Taking a while? Click here to run it now."
msgstr "Слишком долго? Нажмите сюда, чтобы запустить сейчас."

#: includes/notices.php:157
msgid "Add \"Booking Confirmation\" shortcode to your \"Booking Confirmed\" and \"Reservation Received\" pages to show more details about booking or payment.<br/>Click \"Update Pages\" to apply all changes automatically or skip this notice and add \"Booking Confirmation\" shortcode manually.<br/><b><em>This action will replace the whole content of the pages.</em></b>"
msgstr "Добавьте шорткод \"Подтверждение бронирования\" к страницам \"Бронирование подтверждено\" и \"Бронирование получено\" для того, чтобы показать больше деталей бронирования и оплаты. <br/>Нажмите \"Обновить страницы\", чтобы применить эти изменения автоматически или пропустите это сообщение, добавив короткий код \"Подтверждение бронирования\" самостоятельно.<br/><b><em>Это действие полностью заменит содержимое страниц.</em></b>"

#: includes/notices.php:159
msgid "Update Pages"
msgstr "Обновить страницы"

#: includes/notices.php:161
#: includes/wizard.php:36
msgid "Skip"
msgstr "Пропустить"

#: includes/payments/gateways/bank-gateway.php:82
#: includes/payments/gateways/bank-gateway.php:91
msgid "Direct Bank Transfer"
msgstr "Прямой банковский перевод"

#: includes/payments/gateways/bank-gateway.php:92
msgid "Make your payment directly into our bank account. Please use your Booking ID as the payment reference."
msgstr "Прямой денежный перевод на наш банковский счет. Пожалуйста, указывайте ID бронирования в назначении платежа."

#: includes/payments/gateways/bank-gateway.php:118
msgid "Enable Auto-Abandonment"
msgstr ""

#: includes/payments/gateways/bank-gateway.php:119
msgid "Automatically abandon bookings and release reserved slots if payment is not received within a specified time period. You need to manually set the status of paid payments to Completed to avoid automatic abandonment."
msgstr ""

#: includes/payments/gateways/bank-gateway.php:128
msgid "Period of time in hours a user has to pay for a booking. Unpaid bookings become abandoned, and accommodations become available for others."
msgstr ""

#: includes/payments/gateways/beanstream-gateway.php:54
msgid "Beanstream/Bambora"
msgstr "Beanstream/Bambora"

#: includes/payments/gateways/beanstream-gateway.php:59
#: includes/payments/gateways/braintree-gateway.php:149
#: includes/payments/gateways/paypal-gateway.php:72
#: includes/payments/gateways/two-checkout-gateway.php:70
msgid "Use the card number %1$s with CVC %2$s and a valid expiration date to test a payment."
msgstr "Используйте номер карты %1$s с CVC %2$s и действительную дату окончания, чтобы протестировать платёж."

#: includes/payments/gateways/beanstream-gateway.php:66
msgid "Pay by Card (Beanstream)"
msgstr "Оплата картой (Beanstream)"

#: includes/payments/gateways/beanstream-gateway.php:67
msgid "Pay with your credit card via Beanstream."
msgstr "Оплатить картой (Beanstream)."

#: includes/payments/gateways/beanstream-gateway.php:85
#: includes/payments/gateways/braintree-gateway.php:194
#: includes/payments/gateways/stripe-gateway.php:226
msgid "%1$s is enabled, but the <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "%1$s включен, но <a href=\"%2$s\"> опция Включить безопасные платежи</a> отключена. Пожалуйста, включите SSL и убедитесь, что SSL сертификат на Вашем сервере действителен. В противном случае, %1$s будет работать только в тестовом режиме."

#: includes/payments/gateways/beanstream-gateway.php:87
#: includes/payments/gateways/braintree-gateway.php:196
#: includes/payments/gateways/stripe-gateway.php:228
msgid "The <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "Опция <a href=\"%2$s\">Включить безопасные платежи</a> отключена. Пожалуйста, включите SSL и убедитесь, что SSL сертификат на Вашем сервере действителен. В противном случае, %1$s будет работать только в тестовом режиме."

#: includes/payments/gateways/beanstream-gateway.php:90
msgid "Beanstream"
msgstr "Beanstream"

#: includes/payments/gateways/beanstream-gateway.php:103
#: includes/payments/gateways/braintree-gateway.php:212
msgid "Merchant ID"
msgstr "Торговый ID (Merchant ID)"

#: includes/payments/gateways/beanstream-gateway.php:105
msgid "Your Merchant ID can be found in the top-right corner of the screen after logging in to the Beanstream Back Office"
msgstr "Ваш идентификационный номер продавца (Merchant ID) находится в правом верхнем углу в аккаунте Beanstream"

#: includes/payments/gateways/beanstream-gateway.php:112
msgid "Payments Passcode"
msgstr "Код доступа платежей"

#: includes/payments/gateways/beanstream-gateway.php:114
msgid "To generate the passcode, navigate to Administration > Account Settings > Order Settings in the sidebar, then scroll to Payment Gateway > Security/Authentication"
msgstr "Чтобы сгенерировать код доступа, следуйте пути Administration > Account Settings > Order Settings в сайдбаре, и перейдите в Payment Gateway > Security/Authentication"

#: includes/payments/gateways/beanstream-gateway.php:163
msgid "Beanstream Payment Error: %s"
msgstr "Beanstream ошибка платежа: %s"

#: includes/payments/gateways/beanstream-gateway.php:201
msgid "Payment single use token is required."
msgstr "Одноразовый ключ безопасности платежа (payment single use token) обязателен."

#: includes/payments/gateways/braintree-gateway.php:142
#: includes/payments/gateways/braintree-gateway.php:199
msgid "Braintree"
msgstr "Braintree"

#: includes/payments/gateways/braintree-gateway.php:155
#: includes/payments/gateways/stripe-gateway.php:86
msgid "Webhooks Destination URL: %s"
msgstr "URL целевого веб-узла (Webhooks Destination): %s"

#: includes/payments/gateways/braintree-gateway.php:168
msgid "Pay by Card (Braintree)"
msgstr "Оплата картой (Braintree)"

#: includes/payments/gateways/braintree-gateway.php:169
msgid "Pay with your credit card via Braintree."
msgstr "Оплатить кредитной картой в системе Braintree."

#: includes/payments/gateways/braintree-gateway.php:189
msgid "Braintree gateway cannot be enabled due to some problems: %s"
msgstr "Braintree не может быть включен из-за некоторых проблем: %s"

#: includes/payments/gateways/braintree-gateway.php:214
msgid "In your Braintree account select Account > My User > View Authorizations."
msgstr "В Вашем аккаунте Braintree выберите Account > My User > View Authorizations."

#: includes/payments/gateways/braintree-gateway.php:221
#: includes/payments/gateways/stripe-gateway.php:284
msgid "Public Key"
msgstr "Открытый ключ"

#: includes/payments/gateways/braintree-gateway.php:229
msgid "Private Key"
msgstr "Приватный ключ (Private Key)"

#: includes/payments/gateways/braintree-gateway.php:237
msgid "Merchant Account ID"
msgstr "ID торгового счета (Merchant Account ID)"

#: includes/payments/gateways/braintree-gateway.php:238
msgid "In case the site currency differs from default currency in your Braintree account, you can set specific merchant account to avoid <a href=\"%s\">complications with currencty conversions</a>. Otherwise leave the field empty."
msgstr "В случае, если валюта сайта отличается от валюты, установленной по умолчанию в Вашем аккаунте Braintree, Вы можете установить специальный торговый счет, чтобы избежать <a href=\"%s\">сложностей с конвертацией валюты</a>. В противном случае, оставьте поле пустым."

#: includes/payments/gateways/braintree-gateway.php:293
msgid "Braintree submitted for settlement (Transaction ID: %s)"
msgstr "Braintree представлен для расчётов (ID транзакции: %s)"

#: includes/payments/gateways/braintree-gateway.php:303
msgid "Braintree Payment Error: %s"
msgstr "Braintree ошибка платежа: %s"

#: includes/payments/gateways/braintree-gateway.php:330
msgid "Payment method nonce is required."
msgstr "Одноразовый номер платёжного метода (Payment method nonce) обязателен."

#: includes/payments/gateways/braintree/webhook-listener.php:116
msgid "Payment dispute opened"
msgstr "Платёжный спор открыт"

#: includes/payments/gateways/braintree/webhook-listener.php:121
msgid "Payment dispute lost"
msgstr "Платёжный спор проигран"

#: includes/payments/gateways/braintree/webhook-listener.php:126
msgid "Payment dispute won"
msgstr "Платёжный спор выигран"

#: includes/payments/gateways/braintree/webhook-listener.php:143
msgid "Payment refunded in Braintree"
msgstr "Возврат платежа Braintree осуществлён"

#: includes/payments/gateways/braintree/webhook-listener.php:147
msgid "Braintree transaction voided"
msgstr "Транзакция Braintree аннулированная"

#: includes/payments/gateways/cash-gateway.php:45
#: includes/payments/gateways/cash-gateway.php:53
msgid "Pay on Arrival"
msgstr "Оплата по прибытии"

#: includes/payments/gateways/cash-gateway.php:54
msgid "Pay with cash on arrival."
msgstr "Оплата наличными по прибытии."

#: includes/payments/gateways/gateway.php:301
msgid "%s is a required field."
msgstr "%s это обязательное поле."

#: includes/payments/gateways/gateway.php:314
msgid "%s is not a valid email address."
msgstr "%s неверный email адрес."

#. translators: %s is the payment gateway title.
#: includes/payments/gateways/gateway.php:472
msgid "Enable \"%s\""
msgstr "Включить \"%s\""

#: includes/payments/gateways/gateway.php:482
msgid "Test Mode"
msgstr "Тестовый режим"

#: includes/payments/gateways/gateway.php:483
msgid "Enable Sandbox Mode"
msgstr "Включить режим песочницы (тестовый)"

#: includes/payments/gateways/gateway.php:485
msgid "Sandbox can be used to test payments."
msgstr "Используйте песочницу для тестирования платежей."

#: includes/payments/gateways/gateway.php:496
msgid "Payment method title that the customer will see on your website."
msgstr "Название способа оплаты, которое клиент будет видеть на Вашем сайте."

#: includes/payments/gateways/gateway.php:506
msgid "Payment method description that the customer will see on your website."
msgstr "Описание способа оплаты, которое клиент будет видеть на Вашем сайте."

#: includes/payments/gateways/gateway.php:516
msgid "Instructions"
msgstr "Инструкция"

#: includes/payments/gateways/gateway.php:518
msgid "Instructions for a customer on how to complete the payment."
msgstr "Инструкция для клиента касательно завершения оплаты."

#: includes/payments/gateways/gateway.php:543
msgid "Reservation #%d"
msgstr "Бронирование #%d"

#: includes/payments/gateways/gateway.php:545
msgid "Accommodation(s) reservation"
msgstr "Бронирование жилья"

#: includes/payments/gateways/manual-gateway.php:14
#: includes/payments/gateways/manual-gateway.php:19
msgid "Manual Payment"
msgstr "Ручной платеж"

#: includes/payments/gateways/paypal-gateway.php:67
#: includes/payments/gateways/paypal-gateway.php:80
msgid "PayPal"
msgstr "PayPal"

#: includes/payments/gateways/paypal-gateway.php:81
msgid "Pay via PayPal"
msgstr "Оплатить через PayPal"

#: includes/payments/gateways/paypal-gateway.php:117
msgid "Paypal Business Email"
msgstr "Адрес электронной почты для бизнеса PayPal"

#: includes/payments/gateways/paypal-gateway.php:125
msgid "Disable IPN Verification"
msgstr "Отключить мгновенные платёжные уведомления (IPN Verification)"

#: includes/payments/gateways/paypal-gateway.php:127
msgid "Specify an IPN listener for a specific payment instead of the listeners specified in your PayPal Profile."
msgstr "Устанавливать IPN обработчик для конкретного платежа вместо указанных в Вашей учетной записи PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:164
msgid "Payment %s via IPN."
msgstr "Платеж %s через IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:183
msgid "Payment failed due to invalid PayPal business email."
msgstr "Не удалось выполнить платеж из-за недействительной электронной почты PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:200
msgid "Payment failed due to invalid currency in PayPal IPN."
msgstr "Не удалось выполнить платеж из-за неправильной валюты в IPN PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:215
msgid "Payment failed due to invalid amount in PayPal IPN."
msgstr "Платеж не выполнен из-за неправильной суммы в IPN PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:230
msgid "Payment failed due to invalid purchase key in PayPal IPN."
msgstr "Не удалось выполнить платеж из-за недействительного ключа покупки в IPN PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:302
msgid "Payment made via eCheck and will clear automatically in 5-8 days."
msgstr "Оплата была произведена с помощью eCheck - деньги появятся на счету автоматически в течение 5-8 дней."

#: includes/payments/gateways/paypal/ipn-listener.php:307
msgid "Payment requires a confirmed customer address and must be accepted manually through PayPal."
msgstr "Для платежа необходим адрес подтверждённой электронной почты клиента. Платёж должен быть принят вручную через PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:312
msgid "Payment must be accepted manually through PayPal due to international account regulations."
msgstr "Платеж должен быть принят вручную через PayPal в соответствии с международными правилами пользования счетами."

#: includes/payments/gateways/paypal/ipn-listener.php:317
msgid "Payment received in non-shop currency and must be accepted manually through PayPal."
msgstr "Платёж получен не в установленной валюте магазина и должен быть принят вручную через систему PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:323
msgid "Payment is being reviewed by PayPal staff as high-risk or in possible violation of government regulations."
msgstr "Платёж рассматривается представителями PayPal по причине возможного риска или нарушения правительственных постановлений."

#: includes/payments/gateways/paypal/ipn-listener.php:328
msgid "Payment was sent to unconfirmed or non-registered email address."
msgstr "Платёж был отправлен на неподтвержденную или незарегистрированную электронную почту."

#: includes/payments/gateways/paypal/ipn-listener.php:333
msgid "PayPal account must be upgraded before this payment can be accepted."
msgstr "Учётная запись PayPal должна быть обновлена для принятия этого платежа."

#: includes/payments/gateways/paypal/ipn-listener.php:338
msgid "PayPal account is not verified. Verify account in order to accept this payment."
msgstr "Учётная запись PayPal не подтверждена. Подтвердите её, чтобы получить этот платёж."

#: includes/payments/gateways/paypal/ipn-listener.php:343
msgid "Payment is pending for unknown reasons. Contact PayPal support for assistance."
msgstr "Платёж в ожидании по неизвестной причине. Обратитесь в службу поддержки PayPal за помощью."

#: includes/payments/gateways/paypal/ipn-listener.php:363
msgid "Partial PayPal refund processed: %s"
msgstr "Частичный возврат денег через PayPal проведён: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:370
msgid "PayPal Payment #%s Refunded for reason: %s"
msgstr "PayPal платеж #%s Возвращен по причине: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:374
msgid "PayPal Refund Transaction ID: %s"
msgstr "ID транзакции возврата денег через PayPal: %s"

#: includes/payments/gateways/stripe-gateway.php:149
#: includes/payments/gateways/stripe-gateway.php:231
msgid "Stripe"
msgstr "Stripe"

#: includes/payments/gateways/stripe-gateway.php:186
msgid "Use the card number %1$s with CVC %2$s, a valid expiration date and random 5-digit ZIP-code to test a payment."
msgstr "Используйте номер карты %1$s и CVC %2$s, действующий срок годности и случайный пятизначный индекс (цифры), чтобы протестировать оплату."

#: includes/payments/gateways/stripe-gateway.php:202
msgid "Pay by Card (Stripe)"
msgstr "Оплата картой (Stripe)"

#: includes/payments/gateways/stripe-gateway.php:203
msgid "Pay with your credit card via Stripe."
msgstr "Оплатить картой (Stripe)."

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:240
#: includes/payments/gateways/stripe-gateway.php:259
#: includes/payments/gateways/stripe-gateway.php:688
msgid "Bancontact"
msgstr "Bancontact"

#: includes/payments/gateways/stripe-gateway.php:241
#: includes/payments/gateways/stripe-gateway.php:260
#: includes/payments/gateways/stripe-gateway.php:689
msgid "iDEAL"
msgstr "iDEAL"

#: includes/payments/gateways/stripe-gateway.php:242
#: includes/payments/gateways/stripe-gateway.php:261
#: includes/payments/gateways/stripe-gateway.php:690
msgid "Giropay"
msgstr "Giropay"

#: includes/payments/gateways/stripe-gateway.php:243
#: includes/payments/gateways/stripe-gateway.php:262
#: includes/payments/gateways/stripe-gateway.php:691
msgid "SEPA Direct Debit"
msgstr "SEPA Direct Debit"

#. translators: %1$s - name of payment method, %2$s - currency codes
#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:244
#: includes/payments/gateways/stripe-gateway.php:269
#: includes/payments/gateways/stripe-gateway.php:276
#: includes/payments/gateways/stripe-gateway.php:692
msgid "Klarna"
msgstr ""

#. translators: %s - currency codes
#: includes/payments/gateways/stripe-gateway.php:252
msgid "The %s currency is selected in the main settings."
msgstr ""

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:258
msgid "%1$s support the following currencies: %2$s."
msgstr ""

#. translators: %1$s - name of payment method, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:268
msgid "%1$s supports: %2$s."
msgstr ""

#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:275
msgid "%s special restrictions."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:293
msgid "Secret Key"
msgstr "Секретный ключ"

#: includes/payments/gateways/stripe-gateway.php:301
msgid "Webhook Secret"
msgstr "Секретный код вебхука"

#: includes/payments/gateways/stripe-gateway.php:310
msgid "Payment Methods"
msgstr "Способы оплаты"

#: includes/payments/gateways/stripe-gateway.php:311
msgid "Card Payments"
msgstr "Платежи картой"

#: includes/payments/gateways/stripe-gateway.php:322
msgid "Checkout Locale"
msgstr "Язык страницы оплаты"

#: includes/payments/gateways/stripe-gateway.php:325
msgid "Display Checkout in the user's preferred language, if available."
msgstr "Отображать страницу оплаты на языке пользователя (если доступно)."

#: includes/payments/gateways/stripe-gateway.php:395
msgid "The payment method is not selected."
msgstr "Способ оплаты не установлен."

#: includes/payments/gateways/stripe-gateway.php:401
msgid "PaymentIntent ID is not set."
msgstr ""

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:430
msgid "Payment for PaymentIntent %s succeeded."
msgstr "Платеж по PaymentIntent %s успешен."

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:436
msgid "Payment for PaymentIntent %s is processing."
msgstr "Платеж по PaymentIntent %s обрабатывается."

#. translators: %1$s - Stripe PaymentIntent ID, %2$s - Stripe error message text
#: includes/payments/gateways/stripe-gateway.php:457
msgid "Failed to process PaymentIntent %1$s. %2$s"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:474
msgid "Can't charge the payment again: payment's flow already completed."
msgstr "Невозможно сделать повторную оплату: оплата уже завершена."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:496
msgid "Charge %s succeeded."
msgstr "Оплата %s успешна."

#. translators: %1$s - Stripe Charge ID; %2$s - payment price
#: includes/payments/gateways/stripe-gateway.php:503
msgid "Charge %1$s for %2$s created."
msgstr "Оплата %1$s за %2$s создана."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:508
msgid "Charge %s failed."
msgstr "Оплата %s неуспешна."

#: includes/payments/gateways/stripe-gateway.php:515
msgid "Charge error. %s"
msgstr "Ошибка оплаты. %s"

#: includes/payments/gateways/stripe-gateway.php:529
msgid "Argentinean"
msgstr "Аргентинский"

#: includes/payments/gateways/stripe-gateway.php:530
msgid "Simplified Chinese"
msgstr "Китайский упрощенный"

#: includes/payments/gateways/stripe-gateway.php:531
msgid "Danish"
msgstr "Датский"

#: includes/payments/gateways/stripe-gateway.php:532
msgid "Dutch"
msgstr "Голландский"

#: includes/payments/gateways/stripe-gateway.php:533
msgid "English"
msgstr "Английский"

#: includes/payments/gateways/stripe-gateway.php:534
msgid "Finnish"
msgstr "Финский"

#: includes/payments/gateways/stripe-gateway.php:535
msgid "French"
msgstr "Французский"

#: includes/payments/gateways/stripe-gateway.php:536
msgid "German"
msgstr "Немецкий"

#: includes/payments/gateways/stripe-gateway.php:537
msgid "Italian"
msgstr "Итальянский"

#: includes/payments/gateways/stripe-gateway.php:538
msgid "Japanese"
msgstr "Японский"

#: includes/payments/gateways/stripe-gateway.php:539
msgid "Norwegian"
msgstr "Норвежский"

#: includes/payments/gateways/stripe-gateway.php:540
msgid "Polish"
msgstr "Польский"

#: includes/payments/gateways/stripe-gateway.php:541
msgid "Russian"
msgstr "Русский"

#: includes/payments/gateways/stripe-gateway.php:542
msgid "Spanish"
msgstr "Испанский"

#: includes/payments/gateways/stripe-gateway.php:543
msgid "Swedish"
msgstr "Шведский"

#: includes/payments/gateways/stripe-gateway.php:571
msgid "PaymentIntent ID is missing."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:687
msgid "Card"
msgstr "Карта"

#: includes/payments/gateways/stripe-gateway.php:694
msgid "Credit or debit card"
msgstr "Кредитная или дебетовая карта"

#: includes/payments/gateways/stripe-gateway.php:695
msgid "IBAN"
msgstr "IBAN"

#: includes/payments/gateways/stripe-gateway.php:696
msgid "Select iDEAL Bank"
msgstr "Выберите iDEAL Bank"

#: includes/payments/gateways/stripe-gateway.php:698
msgid "You will be redirected to a secure page to complete the payment."
msgstr "Вас будет перенаправлено на защищенную страницу для завершения платежа."

#: includes/payments/gateways/stripe-gateway.php:699
msgid "By providing your IBAN and confirming this payment, you are authorizing this merchant and Stripe, our payment service provider, to send instructions to your bank to debit your account and your bank to debit your account in accordance with those instructions. You are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited."
msgstr "Предоставляя свой IBAN и подтверждая этот платеж, Вы авторизуете продавца и Stripe (наш сервис для приема онлайн-платежей), чтобы отправлять инструкции в Ваш банк для снятия денег со счета и банка в соответствии с инструкциями. Вам разрешен возврат денег из Вашего банка в соответствии с банковским договором. Возврат денег нужно запрашивать на протяжении 8 недель с момента снятия денег с Вашего счета."

#. translators: %s - payment method type code like: card
#: includes/payments/gateways/stripe/stripe-api.php:172
msgid "Could not create PaymentIntent for a not allowed payment type: %s"
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:163
msgid "Webhook received. Payment %s was cancelled by the customer."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:174
msgid "Webhook received. Payment %s failed and couldn't be processed."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:200
msgid "Webhook received. Payment %s was successfully processed."
msgstr ""

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:215
msgid "Webhook received. Charge %s succeeded."
msgstr "Webhook получен. Оплата %s успешна."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:227
msgid "Webhook received. Charge %s failed."
msgstr "Webhook получен. Оплата %s неуспешна."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:238
msgid "Webhook received. The source %s is chargeable."
msgstr "Webhook получен. Источник %s подлежит оплате."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:252
msgid "Webhook received. Payment source %s was cancelled by customer."
msgstr "Webhook получен. Источник оплаты %s отменён клиентом."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:263
msgid "Webhook received. Payment source %s failed and couldn't be processed."
msgstr "Webhook получен. Источник оплаты %s неуспешный - его обработка невозможна."

#: includes/payments/gateways/test-gateway.php:46
#: includes/payments/gateways/test-gateway.php:52
msgid "Test Payment"
msgstr "Тестовая оплата"

#: includes/payments/gateways/two-checkout-gateway.php:65
#: includes/payments/gateways/two-checkout-gateway.php:101
msgid "2Checkout"
msgstr "2Checkout"

#: includes/payments/gateways/two-checkout-gateway.php:88
msgid "To setup the callback process for 2Checkout to automatically mark payments completed, you will need to"
msgstr "Для настройки процесса уведомления для автоматической отметки 2Checkout платежей как завершенных, сделайте следующее:"

#: includes/payments/gateways/two-checkout-gateway.php:90
msgid "Login to your 2Checkout account and click the Notifications tab"
msgstr "Войдите в Ваш аккаунт 2Checkout и откройте вкладку Уведомления (Notifications)"

#: includes/payments/gateways/two-checkout-gateway.php:91
msgid "Click Enable All Notifications"
msgstr "Нажмите Включить все уведомления (Enable All Notifications)"

#: includes/payments/gateways/two-checkout-gateway.php:92
msgid "In the Global URL field, enter the url %s"
msgstr "В поле Гглобальный URL (Global URL) введите %s"

#: includes/payments/gateways/two-checkout-gateway.php:93
msgid "Click Apply"
msgstr "Нажмите Применить (Apply)"

#: includes/payments/gateways/two-checkout-gateway.php:189
msgid "Account Number"
msgstr "Номер аккаунта"

#: includes/payments/gateways/two-checkout-gateway.php:197
msgid "Secret Word"
msgstr "Секретное слово"

#: includes/payments/gateways/two-checkout/ins-listener.php:68
msgid "2Checkout \"Order Created\" notification received."
msgstr "Уведомление \"заказ создан\" получено от 2Checkout."

#: includes/payments/gateways/two-checkout/ins-listener.php:73
msgid "Payment refunded in 2Checkout"
msgstr "Платеж возвращен в 2Checkout"

#: includes/payments/gateways/two-checkout/ins-listener.php:80
msgid "2Checkout fraud review passed"
msgstr "Проверка на 2checkout прошла успешно"

#: includes/payments/gateways/two-checkout/ins-listener.php:83
msgid "2Checkout fraud review failed"
msgstr "Проверка на 2checkout не прошла"

#: includes/payments/gateways/two-checkout/ins-listener.php:86
msgid "2Checkout fraud review in progress"
msgstr "Производится проверка на 2checkout"

#: includes/post-types/attributes-cpt.php:55
msgid "Attribute"
msgstr "Атрибут"

#: includes/post-types/attributes-cpt.php:56
msgctxt "Add New Attribute"
msgid "Add New"
msgstr "Добавить новый"

#: includes/post-types/attributes-cpt.php:57
msgid "Add New Attribute"
msgstr "Добавить новый атрибут"

#: includes/post-types/attributes-cpt.php:58
msgid "Edit Attribute"
msgstr "Изменить атрибут"

#: includes/post-types/attributes-cpt.php:59
msgid "New Attribute"
msgstr "Новый атрибут"

#: includes/post-types/attributes-cpt.php:60
msgid "View Attribute"
msgstr "Просмотр атрибута"

#: includes/post-types/attributes-cpt.php:62
msgid "Search Attribute"
msgstr "Искать атрибут"

#: includes/post-types/attributes-cpt.php:63
msgid "No Attributes found"
msgstr "Атрибуты не найдены"

#: includes/post-types/attributes-cpt.php:64
msgid "No Attributes found in Trash"
msgstr "Атрибуты в корзине не найдены "

#: includes/post-types/attributes-cpt.php:66
msgid "Insert into attribute description"
msgstr "Вставить в описание атрибута"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:157
msgid "Search %s"
msgstr "Поиск %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:159
msgid "All %s"
msgstr "Все %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:161
msgid "Edit %s"
msgstr "Редактировать %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:163
msgid "Update %s"
msgstr "Обновить %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:165
msgid "Add new %s"
msgstr "Добавить новый %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:167
msgid "New %s"
msgstr "Новый %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:169
msgid "No &quot;%s&quot; found"
msgstr "&quot;%s&quot;  - не найдено"

#: includes/post-types/attributes-cpt.php:302
msgid "Name (numeric)"
msgstr "Название (числовое)"

#: includes/post-types/attributes-cpt.php:303
msgid "Term ID"
msgstr "ID варианта"

#: includes/post-types/attributes-cpt.php:314
msgid "Enable Archives"
msgstr "Включить архивы"

#: includes/post-types/attributes-cpt.php:315
msgid "Link the attribute to an archive page with all accommodation types that have this attribute."
msgstr "Добавить ссылку с атрибута на страницу архива со всеми вариантами размещения, у которых тоже есть этот атрибут."

#: includes/post-types/attributes-cpt.php:324
msgid "Visible in Details"
msgstr "Показывать в разделе \"Детали\""

#: includes/post-types/attributes-cpt.php:325
msgid "Display the attribute in details section of an accommodation type."
msgstr "Отображать атрибут в разделе \"Детали\" на странице типа размещения."

#: includes/post-types/attributes-cpt.php:334
msgid "Default Sort Order"
msgstr "Порядок сортировки по умолчанию"

#: includes/post-types/attributes-cpt.php:344
msgid "Default Text"
msgstr "Текст по умолчанию"

#: includes/post-types/attributes-cpt.php:355
msgid "Select"
msgstr "Выбрать"

#: includes/post-types/booking-cpt.php:75
#: templates/edit-booking/edit-dates.php:24
msgid "Edit Dates"
msgstr "Изменить даты"

#: includes/post-types/booking-cpt.php:215
msgid "Note"
msgstr "Заметка"

#: includes/post-types/booking-cpt.php:243
msgctxt "Add New Booking"
msgid "Add New Booking"
msgstr "Добавить бронирование"

#: includes/post-types/booking-cpt.php:247
#: templates/emails/admin-customer-cancelled-booking.php:16
#: templates/emails/admin-customer-confirmed-booking.php:16
#: templates/emails/admin-payment-confirmed-booking.php:16
#: templates/emails/admin-pending-booking.php:16
#: templates/emails/customer-approved-booking.php:24
#: templates/emails/customer-pending-booking.php:26
msgid "View Booking"
msgstr "Посмотреть бронирование"

#: includes/post-types/booking-cpt.php:248
msgid "Search Booking"
msgstr "Найти бронирование"

#: includes/post-types/booking-cpt.php:249
msgid "No bookings found"
msgstr "Не найдено ни одного бронирования"

#: includes/post-types/booking-cpt.php:250
msgid "No bookings found in Trash"
msgstr "Ни одного бронирования не найдено в корзине"

#: includes/post-types/booking-cpt.php:251
msgid "All Bookings"
msgstr "Все бронирования"

#: includes/post-types/booking-cpt.php:252
msgid "Insert into booking description"
msgstr "Добавить в описание бронирования"

#: includes/post-types/booking-cpt.php:253
msgid "Uploaded to this booking"
msgstr "Загружено в это бронирование"

#: includes/post-types/booking-cpt/statuses.php:58
msgctxt "Booking status"
msgid "Pending User Confirmation"
msgstr "Ожидает подтверждения клиента"

#: includes/post-types/booking-cpt/statuses.php:63
msgid "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgid_plural "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgstr[0] "Ожидает подтверждения клиента <span class=\"count\">(%s)</span>"
msgstr[1] "Ожидает подтверждения клиента <span class=\"count\">(%s)</span>"
msgstr[2] "Ожидает подтверждения клиента  <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:69
msgctxt "Booking status"
msgid "Pending Payment"
msgstr "Ожидает оплаты"

#: includes/post-types/booking-cpt/statuses.php:74
msgid "Pending Payment <span class=\"count\">(%s)</span>"
msgid_plural "Pending Payment <span class=\"count\">(%s)</span>"
msgstr[0] "Ожидает оплаты <span class=\"count\">(%s)</span>"
msgstr[1] "Ожидают оплаты <span class=\"count\">(%s)</span>"
msgstr[2] "Ожидают оплаты <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:80
msgctxt "Booking status"
msgid "Pending Admin"
msgstr "Ожидает администратора"

#: includes/post-types/booking-cpt/statuses.php:85
msgid "Pending Admin <span class=\"count\">(%s)</span>"
msgid_plural "Pending Admin <span class=\"count\">(%s)</span>"
msgstr[0] "Ожидает администратора <span class=\"count\">(%s)</span>"
msgstr[1] "Ожидают администратора <span class=\"count\">(%s)</span>"
msgstr[2] "Ожидают администратора <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:91
#: includes/reports/data/report-earnings-by-dates-data.php:31
msgctxt "Booking status"
msgid "Abandoned"
msgstr "Заброшен"

#: includes/post-types/booking-cpt/statuses.php:96
#: includes/post-types/payment-cpt/statuses.php:83
msgid "Abandoned <span class=\"count\">(%s)</span>"
msgid_plural "Abandoned <span class=\"count\">(%s)</span>"
msgstr[0] "Заброшен <span class=\"count\">(%s)</span>"
msgstr[1] "Заброшено <span class=\"count\">(%s)</span>"
msgstr[2] "Заброшено <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:107
msgid "Confirmed <span class=\"count\">(%s)</span>"
msgid_plural "Confirmed <span class=\"count\">(%s)</span>"
msgstr[0] "Подтвержден <span class=\"count\">(%s)</span>"
msgstr[1] "Подтверждены <span class=\"count\">(%s)</span>"
msgstr[2] "Подтверждены <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:113
#: includes/reports/data/report-earnings-by-dates-data.php:30
msgctxt "Booking status"
msgid "Cancelled"
msgstr "Отменен"

#: includes/post-types/booking-cpt/statuses.php:118
#: includes/post-types/payment-cpt/statuses.php:116
msgid "Cancelled <span class=\"count\">(%s)</span>"
msgid_plural "Cancelled <span class=\"count\">(%s)</span>"
msgstr[0] "Отменен <span class=\"count\">(%s)</span>"
msgstr[1] "Отменено <span class=\"count\">(%s)</span>"
msgstr[2] "Отменено <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:180
#: includes/post-types/payment-cpt/statuses.php:213
msgid "Status changed from %s to %s."
msgstr "Статус изменен с %s на %s."

#: includes/post-types/coupon-cpt.php:45
msgid "A brief description to remind you what this code is for."
msgstr ""

#: includes/post-types/coupon-cpt.php:52
msgid "Conditions"
msgstr ""

#: includes/post-types/coupon-cpt.php:60
msgid "Apply a coupon code to selected accommodations in a booking. Leave blank to apply to all accommodations."
msgstr ""

#: includes/post-types/coupon-cpt.php:82
msgid "Percentage discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:83
msgid "Fixed discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:84
msgid "Fixed discount on daily/nightly price"
msgstr ""

#: includes/post-types/coupon-cpt.php:94
#: includes/post-types/coupon-cpt.php:125
#: includes/post-types/coupon-cpt.php:170
msgid "Enter percent or fixed amount according to selected type."
msgstr "Введите процент или фиксированную сумму в соответствии с выбранным типом."

#: includes/post-types/coupon-cpt.php:104
msgid "Service Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:137
msgid "Apply a coupon code to selected services in a booking. Leave blank to apply to all services."
msgstr ""

#: includes/post-types/coupon-cpt.php:149
msgid "Fee Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:180
msgid "Usage Restrictions"
msgstr ""

#: includes/post-types/coupon-cpt.php:195
msgid "Check-in After"
msgstr "Заезд после"

#: includes/post-types/coupon-cpt.php:203
msgid "Check-out Before"
msgstr "Отъезд до"

#: includes/post-types/coupon-cpt.php:211
msgid "Min days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:212
msgid "For early bird discount. The coupon code applies if a booking is made in a minimum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:222
msgid "Max days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:223
msgid "For last minute discount. The coupon code applies if a booking is made in a maximum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:233
msgid "Minimum Days"
msgstr "Минимальное количество дней пребывания"

#: includes/post-types/coupon-cpt.php:243
msgid "Maximum Days"
msgstr "Максимальное количество дней пребывания"

#: includes/post-types/coupon-cpt.php:253
msgid "Usage Limit"
msgstr "Лимит использования"

#: includes/post-types/coupon-cpt.php:263
msgid "Usage Count"
msgstr "Счётчик использования"

#: includes/post-types/coupon-cpt.php:290
msgctxt "Add New Coupon"
msgid "Add New"
msgstr "Добавить новый"

#: includes/post-types/coupon-cpt.php:291
msgid "Add New Coupon"
msgstr "Добавить купон"

#: includes/post-types/coupon-cpt.php:292
msgid "Edit Coupon"
msgstr "Изменить купон"

#: includes/post-types/coupon-cpt.php:293
msgid "New Coupon"
msgstr "Новый купон"

#: includes/post-types/coupon-cpt.php:294
msgid "View Coupon"
msgstr "Смотреть купон"

#: includes/post-types/coupon-cpt.php:295
msgid "Search Coupon"
msgstr "Искать купон"

#: includes/post-types/coupon-cpt.php:296
msgid "No coupons found"
msgstr "Купоны не найдены"

#: includes/post-types/coupon-cpt.php:297
msgid "No coupons found in Trash"
msgstr "Купонов в корзине не найдено"

#: includes/post-types/coupon-cpt.php:298
msgid "All Coupons"
msgstr "Все купоны"

#: includes/post-types/payment-cpt.php:37
msgid "Payment History"
msgstr "История платежей"

#: includes/post-types/payment-cpt.php:38
msgid "Payment"
msgstr "Платеж"

#: includes/post-types/payment-cpt.php:39
msgctxt "Add New Payment"
msgid "Add New"
msgstr "Добавить новый"

#: includes/post-types/payment-cpt.php:40
msgid "Add New Payment"
msgstr "Добавить платеж"

#: includes/post-types/payment-cpt.php:41
msgid "Edit Payment"
msgstr "Редактировать платеж"

#: includes/post-types/payment-cpt.php:42
msgid "New Payment"
msgstr "Новый платеж"

#: includes/post-types/payment-cpt.php:43
msgid "View Payment"
msgstr "Смотреть платеж"

#: includes/post-types/payment-cpt.php:44
msgid "Search Payment"
msgstr "Найти платеж"

#: includes/post-types/payment-cpt.php:45
msgid "No payments found"
msgstr "Не найдено ни одного платежа"

#: includes/post-types/payment-cpt.php:46
msgid "No payments found in Trash"
msgstr "Ни одного платежа не найдено в корзине"

#: includes/post-types/payment-cpt.php:47
msgid "Payments"
msgstr "Платежи"

#: includes/post-types/payment-cpt.php:48
msgid "Insert into payment description"
msgstr "Вставить в описание платежа"

#: includes/post-types/payment-cpt.php:49
msgid "Uploaded to this payment"
msgstr "Загружено в этот платеж"

#: includes/post-types/payment-cpt.php:54
msgid "Payments."
msgstr "Платежи."

#: includes/post-types/payment-cpt.php:169
msgid "Gateway Mode"
msgstr "Режим платежного шлюза"

#: includes/post-types/payment-cpt.php:171
msgid "Sandbox"
msgstr "Тестовый"

#: includes/post-types/payment-cpt.php:172
msgid "Live"
msgstr "Реальный"

#: includes/post-types/payment-cpt.php:194
msgid "Fee"
msgstr "Сбор"

#: includes/post-types/payment-cpt.php:215
msgid "Payment Type"
msgstr "Способ оплаты"

#: includes/post-types/payment-cpt.php:240
msgid "Billing Info"
msgstr "Платежная информация"

#: includes/post-types/payment-cpt.php:287
msgid "Address 1"
msgstr "Адрес 1"

#: includes/post-types/payment-cpt.php:295
msgid "Address 2"
msgstr "Адрес 2"

#: includes/post-types/payment-cpt.php:319
msgid "Postal Code (ZIP)"
msgstr "Индекс"

#: includes/post-types/payment-cpt/statuses.php:45
msgctxt "Payment status"
msgid "Pending"
msgstr "В ожидании"

#: includes/post-types/payment-cpt/statuses.php:50
msgid "Pending <span class=\"count\">(%s)</span>"
msgid_plural "Pending <span class=\"count\">(%s)</span>"
msgstr[0] "В ожидании <span class=\"count\">(%s)</span>"
msgstr[1] "В ожидании <span class=\"count\">(%s)</span>"
msgstr[2] "В ожидании <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/payment-cpt/statuses.php:56
msgctxt "Payment status"
msgid "Completed"
msgstr "Завершен"

#: includes/post-types/payment-cpt/statuses.php:61
msgid "Completed <span class=\"count\">(%s)</span>"
msgid_plural "Completed <span class=\"count\">(%s)</span>"
msgstr[0] "Завершен <span class=\"count\">(%s)</span>"
msgstr[1] "Завершено <span class=\"count\">(%s)</span>"
msgstr[2] "Завершено <span class=\"count\">(%s)</span>"
msgstr[3] "Завершено <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:67
msgctxt "Payment status"
msgid "Failed"
msgstr "Неудавшийся"

#: includes/post-types/payment-cpt/statuses.php:72
msgid "Failed <span class=\"count\">(%s)</span>"
msgid_plural "Failed <span class=\"count\">(%s)</span>"
msgstr[0] "Неудавшийся <span class=\"count\">(%s)</span>"
msgstr[1] "Неудавшихся <span class=\"count\">(%s)</span>"
msgstr[2] "Неудавшихся <span class=\"count\">(%s)</span>"
msgstr[3] "Неудавшихся <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:78
msgctxt "Payment status"
msgid "Abandoned"
msgstr "Заброшен"

#: includes/post-types/payment-cpt/statuses.php:89
msgctxt "Payment status"
msgid "On Hold"
msgstr "На удержании"

#: includes/post-types/payment-cpt/statuses.php:94
msgid "On Hold <span class=\"count\">(%s)</span>"
msgid_plural "On Hold <span class=\"count\">(%s)</span>"
msgstr[0] "На удержании <span class=\"count\">(%s)</span>"
msgstr[1] "На удержании <span class=\"count\">(%s)</span>"
msgstr[2] "На удержании <span class=\"count\">(%s)</span>"
msgstr[3] "На удержании <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:100
msgctxt "Payment status"
msgid "Refunded"
msgstr "Возвращен"

#: includes/post-types/payment-cpt/statuses.php:105
msgid "Refunded <span class=\"count\">(%s)</span>"
msgid_plural "Refunded <span class=\"count\">(%s)</span>"
msgstr[0] "Возвращен <span class=\"count\">(%s)</span>"
msgstr[1] "Возвращено <span class=\"count\">(%s)</span>"
msgstr[2] "Возвращено <span class=\"count\">(%s)</span>"
msgstr[3] "Возвращено <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:111
msgctxt "Payment status"
msgid "Cancelled"
msgstr "Отменен"

#: includes/post-types/payment-cpt/statuses.php:180
msgid "Payment (#%s) for this booking is on hold"
msgstr "Оплата (#%s) за это бронирование в состоянии ожидания"

#: includes/post-types/rate-cpt.php:23
msgid "Rate Info"
msgstr "Информация о тарифе"

#: includes/post-types/rate-cpt.php:49
#: includes/post-types/season-cpt.php:99
msgid "Season"
msgstr "Сезон"

#: includes/post-types/rate-cpt.php:72
msgid "Move price to top to set higher priority."
msgstr "Перетащите вверх цену с более высоким приоритетом."

#: includes/post-types/rate-cpt.php:84
msgid "Will be displayed on the checkout page."
msgstr "Будет показано на странице оформления заказа."

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/post-types/rate-cpt.php:103
#: includes/post-types/rate-cpt.php:113
msgid "Rates"
msgstr "Тарифы"

#: includes/post-types/rate-cpt.php:105
msgctxt "Add New Rate"
msgid "Add New"
msgstr "Добавить новый"

#: includes/post-types/rate-cpt.php:106
msgid "Add New Rate"
msgstr "Добавить тариф"

#: includes/post-types/rate-cpt.php:107
msgid "Edit Rate"
msgstr "Изменить тариф"

#: includes/post-types/rate-cpt.php:108
msgid "New Rate"
msgstr "Новый тариф"

#: includes/post-types/rate-cpt.php:109
msgid "View Rate"
msgstr "Cмотреть тариф"

#: includes/post-types/rate-cpt.php:110
msgid "Search Rate"
msgstr "Найти тариф"

#: includes/post-types/rate-cpt.php:111
msgid "No rates found"
msgstr "Тарифы не найдены"

#: includes/post-types/rate-cpt.php:112
msgid "No rates found in Trash"
msgstr "Тарифов в корзине не найдено"

#: includes/post-types/rate-cpt.php:114
msgid "Insert into rate description"
msgstr "Вставить в описание тарифа"

#: includes/post-types/rate-cpt.php:115
msgid "Uploaded to this rate"
msgstr "Загружено в этот тариф"

#: includes/post-types/rate-cpt.php:120
msgid "This is where you can add new rates."
msgstr "Здесь вы можете добавить новые тарифы."

#: includes/post-types/reserved-room-cpt.php:23
msgid "Reserved Accommodation"
msgstr "Забронированный вариант размещения"

#: includes/post-types/room-cpt.php:33
msgctxt "Add New Accommodation"
msgid "Add New"
msgstr "Добавить новый"

#: includes/post-types/room-cpt.php:34
msgid "Add New Accommodation"
msgstr "Добавить вариант размещения"

#: includes/post-types/room-cpt.php:35
msgid "Edit Accommodation"
msgstr "Изменить вариант размещения"

#: includes/post-types/room-cpt.php:36
msgid "New Accommodation"
msgstr "Новый вариант размещения"

#: includes/post-types/room-cpt.php:37
msgid "View Accommodation"
msgstr "Смотреть вариант размещения"

#: includes/post-types/room-cpt.php:38
msgid "Search Accommodation"
msgstr "Найти вариант размещения"

#: includes/post-types/room-cpt.php:39
#: templates/create-booking/results/rooms-found.php:21
#: templates/shortcodes/search-results/results-info.php:19
msgid "No accommodations found"
msgstr "Не найдено ни одного варианта размещения"

#: includes/post-types/room-cpt.php:40
msgid "No accommodations found in Trash"
msgstr "Ни одного варианта размещения не найдено в корзине"

#: includes/post-types/room-cpt.php:42
msgid "Insert into accommodation description"
msgstr "Вставить в описание варианта размещения"

#: includes/post-types/room-cpt.php:43
msgid "Uploaded to this accommodation"
msgstr "Загружено в этот вариант размещения"

#: includes/post-types/room-cpt.php:48
msgid "This is where you can add new accommodations to your hotel."
msgstr "Здесь вы можете добавлять новые варианты размещения в ваш отель."

#: includes/post-types/room-cpt.php:106
msgid "Automatically block current accommodation when the selected ones are booked"
msgstr ""

#: includes/post-types/room-type-cpt.php:55
msgctxt "Add New Accommodation Type"
msgid "Add Accommodation Type"
msgstr "Добавить тип размещения"

#: includes/post-types/room-type-cpt.php:56
msgid "Add New Accommodation Type"
msgstr "Добавить новый тип размещения"

#: includes/post-types/room-type-cpt.php:57
msgid "Edit Accommodation Type"
msgstr "Изменить тип размещения"

#: includes/post-types/room-type-cpt.php:58
msgid "New Accommodation Type"
msgstr "Новый тип размещения"

#: includes/post-types/room-type-cpt.php:59
msgid "View Accommodation Type"
msgstr "Просмотреть тип размещения"

#: includes/post-types/room-type-cpt.php:61
msgid "Search Accommodation Type"
msgstr "Найти тип размещения"

#: includes/post-types/room-type-cpt.php:62
msgid "No Accommodation types found"
msgstr "Ни одного типа размещения не найдено"

#: includes/post-types/room-type-cpt.php:63
msgid "No Accommodation types found in Trash"
msgstr "Ни одного типа размещения не найдено в корзине"

#: includes/post-types/room-type-cpt.php:65
msgid "Insert into accommodation type description"
msgstr "Вставить в описание типа размещения"

#: includes/post-types/room-type-cpt.php:66
msgid "Uploaded to this accommodation type"
msgstr "Загружено в этот тип размещения"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:83
msgctxt "slug"
msgid "accommodation"
msgstr "accommodation"

#: includes/post-types/room-type-cpt.php:108
msgid "Accommodation Categories"
msgstr "Категории размещения"

#: includes/post-types/room-type-cpt.php:109
msgid "Accommodation Category"
msgstr "Категория размещения"

#: includes/post-types/room-type-cpt.php:110
msgid "Search Accommodation Categories"
msgstr "Найти категорию размещения"

#: includes/post-types/room-type-cpt.php:111
msgid "Popular Accommodation Categories"
msgstr "Популярные категории размещения"

#: includes/post-types/room-type-cpt.php:112
msgid "All Accommodation Categories"
msgstr "Все категории размещений"

#: includes/post-types/room-type-cpt.php:113
msgid "Parent Accommodation Category"
msgstr "Родительская категория"

#: includes/post-types/room-type-cpt.php:114
msgid "Parent Accommodation Category:"
msgstr "Родительская категория:"

#: includes/post-types/room-type-cpt.php:115
msgid "Edit Accommodation Category"
msgstr "Изменить категорию размещения"

#: includes/post-types/room-type-cpt.php:116
msgid "Update Accommodation Category"
msgstr "Обновить категорию размещения"

#: includes/post-types/room-type-cpt.php:117
msgid "Add New Accommodation Category"
msgstr "Добавить категорию размещения"

#: includes/post-types/room-type-cpt.php:118
msgid "New Accommodation Category Name"
msgstr "Имя новой категории размещения"

#: includes/post-types/room-type-cpt.php:119
msgid "Separate categories with commas"
msgstr "Разделяйте категории запятыми"

#: includes/post-types/room-type-cpt.php:120
msgid "Add or remove categories"
msgstr "Добавить или удалить категории"

#: includes/post-types/room-type-cpt.php:121
msgid "Choose from the most used categories"
msgstr "Выбрать из наиболее используемых категорий"

#: includes/post-types/room-type-cpt.php:122
msgid "No categories found."
msgstr "Категории не найдены."

#: includes/post-types/room-type-cpt.php:123
#: assets/blocks/blocks.js:816
msgid "Categories"
msgstr "Категории"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:138
msgctxt "slug"
msgid "accommodation-category"
msgstr "accommodation-category"

#: includes/post-types/room-type-cpt.php:163
msgid "Accommodation Tags"
msgstr "Метки варианта размещения"

#: includes/post-types/room-type-cpt.php:164
msgid "Accommodation Tag"
msgstr "Метка варианта размещения"

#: includes/post-types/room-type-cpt.php:165
msgid "Search Accommodation Tags"
msgstr "Искать метки варианта размещения"

#: includes/post-types/room-type-cpt.php:166
msgid "Popular Accommodation Tags"
msgstr "Популярные метки варианта размещения"

#: includes/post-types/room-type-cpt.php:167
msgid "All Accommodation Tags"
msgstr "Все метки типа размещения"

#: includes/post-types/room-type-cpt.php:168
msgid "Parent Accommodation Tag"
msgstr "Родительская метка типа размещения"

#: includes/post-types/room-type-cpt.php:169
msgid "Parent Accommodation Tag:"
msgstr "Родительская метка типа размещения:"

#: includes/post-types/room-type-cpt.php:170
msgid "Edit Accommodation Tag"
msgstr "Редактировать метку типа размещения"

#: includes/post-types/room-type-cpt.php:171
msgid "Update Accommodation Tag"
msgstr "Обновить метку типа размещения"

#: includes/post-types/room-type-cpt.php:172
msgid "Add New Accommodation Tag"
msgstr "Добавить метку типа размещения"

#: includes/post-types/room-type-cpt.php:173
msgid "New Accommodation Tag Name"
msgstr "Название новой метки типа размещения"

#: includes/post-types/room-type-cpt.php:174
msgid "Separate tags with commas"
msgstr "Метки разделяются запятыми"

#: includes/post-types/room-type-cpt.php:175
msgid "Add or remove tags"
msgstr "Добавить или удалить метки"

#: includes/post-types/room-type-cpt.php:176
msgid "Choose from the most used tags"
msgstr "Выбрать из наиболее используемых меток"

#: includes/post-types/room-type-cpt.php:177
msgid "No tags found."
msgstr "Меток не найдено."

#: includes/post-types/room-type-cpt.php:178
#: assets/blocks/blocks.js:828
msgid "Tags"
msgstr "Метки"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:192
msgctxt "slug"
msgid "accommodation-tag"
msgstr "accommodation-tag"

#: includes/post-types/room-type-cpt.php:217
#: includes/post-types/room-type-cpt.php:232
msgid "Amenities"
msgstr "Удобства"

#: includes/post-types/room-type-cpt.php:218
msgid "Amenity"
msgstr "Удобство"

#: includes/post-types/room-type-cpt.php:219
msgid "Search Amenities"
msgstr "Искать удобства"

#: includes/post-types/room-type-cpt.php:220
msgid "Popular Amenities"
msgstr "Популярные удобства"

#: includes/post-types/room-type-cpt.php:221
msgid "All Amenities"
msgstr "Все удобства"

#: includes/post-types/room-type-cpt.php:222
msgid "Parent Amenity"
msgstr "Удобство родительского порядка"

#: includes/post-types/room-type-cpt.php:223
msgid "Parent Amenity:"
msgstr "Удобство родительского порядка:"

#: includes/post-types/room-type-cpt.php:224
msgid "Edit Amenity"
msgstr "Изменить удобство"

#: includes/post-types/room-type-cpt.php:225
msgid "Update Amenity"
msgstr "Обновить удобство"

#: includes/post-types/room-type-cpt.php:226
msgid "Add New Amenity"
msgstr "Добавить удобство"

#: includes/post-types/room-type-cpt.php:227
msgid "New Amenity Name"
msgstr "Название нового удобства"

#: includes/post-types/room-type-cpt.php:228
msgid "Separate amenities with commas"
msgstr "Разделите удобства запятыми"

#: includes/post-types/room-type-cpt.php:229
msgid "Add or remove amenities"
msgstr "Добавить или удалить удобства"

#: includes/post-types/room-type-cpt.php:230
msgid "Choose from the most used amenities"
msgstr "Выберите из наиболее часто используемых удобств"

#: includes/post-types/room-type-cpt.php:231
msgid "No amenities found."
msgstr "Не найдено ни одного удобства."

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:247
msgctxt "slug"
msgid "accommodation-facility"
msgstr "accommodation-facility"

#: includes/post-types/room-type-cpt.php:293
msgid "State the age or disable children in <a href=\"%s\">settings</a>."
msgstr "Укажите возраст детей или отключите параметр поиска \"Дети\" в <a href=\"%s\">Настройках</a>."

#: includes/post-types/room-type-cpt.php:303
msgid "Leave this option empty to calculate total capacity automatically to meet the exact number of adults AND children set above. This is the default behavior. Configure this option to allow any variations of adults OR children set above at checkout so that in total it meets the limit of manually set \"Capacity\". For example, configuration \"adults:5\", \"children:4\", \"capacity:5\" means the property can accommodate up to 5 adults, up to 4 children, but up to 5 guests in total (not 9)."
msgstr "Оставьте поле пустым для подсчета общего количества гостей автоматически, в пределах соответствия с количеством взрослых и детей, установленным выше. Это поведение по умолчанию. Заполните поле, чтобы позволить гостю выбрать любое количество взрослых ИЛИ детей, в пределах установленного выше количества гостей, но чтобы общее количество не превышало лимит, который вы установите в этом поле. Например, «Взрослых: 5\", \"Детей: 4\", \"Вместимость: 5\" означает, что номер может вместить не более 5-ти взрослых и 4-х детей, но не более 5-ти человек в сумме (не 9)."

#: includes/post-types/room-type-cpt.php:313
msgid "Base Adults Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:314
#: includes/post-types/room-type-cpt.php:325
msgid "An optional starting value used when creating seasonal prices in the Rates menu."
msgstr ""

#: includes/post-types/room-type-cpt.php:324
msgid "Base Children Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:334
msgid "Other"
msgstr "Другое"

#: includes/post-types/room-type-cpt.php:340
msgid "Size, %s"
msgstr "Размер, %s"

#: includes/post-types/room-type-cpt.php:341
msgid "Leave blank to hide."
msgstr "Не заполняйте, чтобы скрыть на сайте."

#: includes/post-types/room-type-cpt.php:355
msgid "City view, seaside, swimming pool etc."
msgstr "Вид на город, море, бассейн и т.д."

#: includes/post-types/room-type-cpt.php:366
msgid "Bed type"
msgstr "Тип кровати"

#: includes/post-types/room-type-cpt.php:369
msgid "Set bed types list in <a href=\"%link%\" target=\"_blank\">settings</a>."
msgstr "Добавьте типы кроватей в <a href=\"%link%\" target=\"_blank\">настройках</a>."

#: includes/post-types/room-type-cpt.php:379
msgid "Photo Gallery"
msgstr "Фотогалерея"

#: includes/post-types/room-type-cpt.php:390
#: includes/post-types/room-type-cpt.php:395
msgid "Available Services"
msgstr "Доступные услуги"

#: includes/post-types/season-cpt.php:25
msgid "Season Info"
msgstr "Информация о сезоне"

#: includes/post-types/season-cpt.php:32
#: includes/reports/earnings-report.php:332
msgid "Start date"
msgstr "Дата начала"

#: includes/post-types/season-cpt.php:45
#: includes/reports/earnings-report.php:344
msgid "End date"
msgstr "Дата окончания"

#: includes/post-types/season-cpt.php:55
msgid "Applied for days"
msgstr "Применяется к дням"

#: includes/post-types/season-cpt.php:59
msgid "Hold Ctrl / Cmd to select multiple."
msgstr "Удерживайте Ctrl / Cmd, чтобы выбрать несколько."

#: includes/post-types/season-cpt.php:68
msgid "Annual repeats begin on the Start date of the season, for one year from the current date."
msgstr ""

#: includes/post-types/season-cpt.php:70
msgid "Does not repeat"
msgstr "Не повторяется"

#: includes/post-types/season-cpt.php:81
msgid "Repeat until date"
msgstr ""

#: includes/post-types/season-cpt.php:100
msgctxt "Add New Season"
msgid "Add New"
msgstr "Добавить новый"

#: includes/post-types/season-cpt.php:101
msgid "Add New Season"
msgstr "Добавить сезон"

#: includes/post-types/season-cpt.php:102
msgid "Edit Season"
msgstr "Изменить сезон"

#: includes/post-types/season-cpt.php:103
msgid "New Season"
msgstr "Новый сезон"

#: includes/post-types/season-cpt.php:104
msgid "View Season"
msgstr "Смотреть сезон"

#: includes/post-types/season-cpt.php:105
msgid "Search Season"
msgstr "Найти сезон"

#: includes/post-types/season-cpt.php:106
msgid "No seasons found"
msgstr "Не найдено ни одного сезона"

#: includes/post-types/season-cpt.php:107
msgid "No seasons found in Trash"
msgstr "Ни одного сезона не найдено в корзине"

#: includes/post-types/season-cpt.php:109
msgid "Insert into season description"
msgstr "Вставить в описание сезона"

#: includes/post-types/season-cpt.php:110
msgid "Uploaded to this season"
msgstr "Загружено в этот сезон"

#: includes/post-types/season-cpt.php:115
msgid "This is where you can add new seasons."
msgstr "Здесь вы можете добавлять новые сезоны."

#: includes/post-types/service-cpt.php:92
#: includes/views/booking-view.php:206
msgid "Service"
msgstr "Услуга"

#: includes/post-types/service-cpt.php:93
msgctxt "Add New Service"
msgid "Add New"
msgstr "Добавить новую"

#: includes/post-types/service-cpt.php:94
msgid "Add New Service"
msgstr "Добавить услугу"

#: includes/post-types/service-cpt.php:95
msgid "Edit Service"
msgstr "Изменить услугу"

#: includes/post-types/service-cpt.php:96
msgid "New Service"
msgstr "Новая услуга"

#: includes/post-types/service-cpt.php:97
msgid "View Service"
msgstr "Смотреть услугу"

#: includes/post-types/service-cpt.php:98
msgid "Search Service"
msgstr "Найти услугу"

#: includes/post-types/service-cpt.php:99
msgid "No services found"
msgstr "Услуги не найдены"

#: includes/post-types/service-cpt.php:100
msgid "No services found in Trash"
msgstr "Услуги не найдены в корзине"

#: includes/post-types/service-cpt.php:102
msgid "Insert into service description"
msgstr "Вставить в описание услуги"

#: includes/post-types/service-cpt.php:103
msgid "Uploaded to this service"
msgstr "Загружено к этой услуге"

#. translators: do not translate
#: includes/post-types/service-cpt.php:120
msgctxt "slug"
msgid "services"
msgstr "services"

#: includes/post-types/service-cpt.php:156
msgid "How many times the customer will be charged."
msgstr "Сколько раз с клиента будет взиматься плата."

#: includes/post-types/service-cpt.php:167
msgid "Minimum"
msgstr "Минимум"

#: includes/post-types/service-cpt.php:181
msgid "Maximum"
msgstr "Максимум"

#: includes/post-types/service-cpt.php:193
msgid "Empty means unlimited"
msgstr "Оставьте пустым для неограниченного количества раз"

#: includes/reports/data/report-earnings-by-dates-data.php:29
msgctxt "Booking status"
msgid "Pending"
msgstr "В ожидании"

#: includes/reports/earnings-report.php:91
msgid "Total Sales"
msgstr "Всего продаж"

#: includes/reports/earnings-report.php:94
msgid "Total Without Taxes"
msgstr "Всего без налогов"

#: includes/reports/earnings-report.php:97
msgid "Total Fees"
msgstr "Всего сборов"

#: includes/reports/earnings-report.php:100
msgid "Total Services"
msgstr "Всего услуг"

#: includes/reports/earnings-report.php:103
msgid "Total Discounts"
msgstr "Всего скидки"

#: includes/reports/earnings-report.php:106
msgid "Total Bookings"
msgstr "Всего бронирований"

#: includes/reports/earnings-report.php:289
#: includes/reports/report-filters.php:38
msgid "Revenue"
msgstr ""

#: includes/reports/earnings-report.php:352
#: includes/views/create-booking/checkout-view.php:56
#: includes/views/shortcodes/checkout-view.php:90
msgid "Apply"
msgstr "Применить"

#: includes/reports/earnings-report.php:496
msgid "From %s to %s"
msgstr "С %s по %s"

#: includes/reports/report-filters.php:61
msgid "Today"
msgstr "Cегодня"

#: includes/reports/report-filters.php:64
msgid "Yesterday"
msgstr "Вчера"

#: includes/reports/report-filters.php:67
msgid "This week"
msgstr "Эта неделя"

#: includes/reports/report-filters.php:70
msgid "Last week"
msgstr "Прошлая неделя"

#: includes/reports/report-filters.php:73
msgid "Last 30 days"
msgstr "Последние 30 дней"

#: includes/reports/report-filters.php:76
msgid "This month"
msgstr "Этот месяц"

#: includes/reports/report-filters.php:79
msgid "Last month"
msgstr "Прошлый месяц"

#: includes/reports/report-filters.php:82
msgid "This quarter"
msgstr "Этот квартал"

#: includes/reports/report-filters.php:85
msgid "Last quarter"
msgstr "Прошлый квартал"

#: includes/reports/report-filters.php:88
msgid "This year"
msgstr "Этот год"

#: includes/reports/report-filters.php:91
msgid "Last year"
msgstr "Прошлый год"

#. translators: %s - original Rate title
#: includes/repositories/rate-repository.php:195
msgid "%s - copy"
msgstr "%s - копия"

#: includes/script-managers/admin-script-manager.php:92
msgid "Accommodation Type Gallery"
msgstr "Галерея типа размещения"

#: includes/script-managers/admin-script-manager.php:93
msgid "Add Gallery To Accommodation Type"
msgstr "Добавить галерею для типа размещения"

#: includes/script-managers/admin-script-manager.php:105
msgid "Display imported bookings."
msgstr "Показать импортированые бронирования."

#: includes/script-managers/admin-script-manager.php:106
msgid "Processing..."
msgstr "Обработка..."

#: includes/script-managers/admin-script-manager.php:107
msgid "Cancelling..."
msgstr "Отмена..."

#: includes/script-managers/admin-script-manager.php:108
msgid "Want to delete?"
msgstr "Хотите удалить?"

#: includes/script-managers/public-script-manager.php:204
msgid "Not available"
msgstr "Недоступно"

#: includes/script-managers/public-script-manager.php:205
msgid "This is earlier than allowed by our advance reservation rules."
msgstr "Это раннее чем допускается нашими правилами предварительного бронирования."

#: includes/script-managers/public-script-manager.php:206
msgid "This is later than allowed by our advance reservation rules."
msgstr "Это позже чем допускается нашими правилами предварительного бронирования."

#: includes/script-managers/public-script-manager.php:210
msgid "Day in the past"
msgstr "День в прошлом"

#: includes/script-managers/public-script-manager.php:211
msgid "Check-in date"
msgstr "Дата заезда"

#: includes/script-managers/public-script-manager.php:212
msgid "Less than min days stay"
msgstr "Меньше, чем минимальное количество дней пребывания"

#: includes/script-managers/public-script-manager.php:213
msgid "More than max days stay"
msgstr "Больше, чем максимальное количество дней пребывания"

#: includes/script-managers/public-script-manager.php:215
msgid "Later than max date for current check-in date"
msgstr "Позже, чем максимальная дата для текущей даты заезда"

#: includes/script-managers/public-script-manager.php:216
msgid "Rules:"
msgstr "Правила:"

#: includes/script-managers/public-script-manager.php:217
msgid "Tokenisation failed: %s"
msgstr "Токенизация не удалась: %s"

#: includes/script-managers/public-script-manager.php:218
#: includes/script-managers/public-script-manager.php:219
msgid "%1$d &times; &ldquo;%2$s&rdquo; has been added to your reservation."
msgid_plural "%1$d &times; &ldquo;%2$s&rdquo; have been added to your reservation."
msgstr[0] "%1$d &times; &ldquo;%2$s&rdquo; добавлен к вашему бронированию."
msgstr[1] "%1$d &times; &ldquo;%2$s&rdquo; добавлены к вашему бронированию."
msgstr[2] "%1$d &times; &ldquo;%2$s&rdquo; добавлены к вашему бронированию."
msgstr[3] ""

#: includes/script-managers/public-script-manager.php:220
#: includes/script-managers/public-script-manager.php:221
msgid "%s accommodation selected."
msgid_plural "%s accommodations selected."
msgstr[0] "%s вариант размещения выбран."
msgstr[1] "%s варианта размещения выбраны."
msgstr[2] "%s вариантов размещения выбрано."
msgstr[3] ""

#: includes/script-managers/public-script-manager.php:222
msgid "Coupon code is empty."
msgstr "Скидочный купон пустой."

#: includes/script-managers/public-script-manager.php:225
msgid "Select dates"
msgstr ""

#: includes/settings/main-settings.php:26
msgid "Dark Blue"
msgstr "Тёмно-синяя"

#: includes/settings/main-settings.php:27
msgid "Dark Green"
msgstr "Тёмно-зелёная"

#: includes/settings/main-settings.php:28
msgid "Dark Red"
msgstr "Тёмно-красная"

#: includes/settings/main-settings.php:29
msgid "Grayscale"
msgstr "Серая"

#: includes/settings/main-settings.php:30
msgid "Light Blue"
msgstr "Светло-голубая"

#: includes/settings/main-settings.php:31
msgid "Light Coral"
msgstr "Светло-коралловая"

#: includes/settings/main-settings.php:32
msgid "Light Green"
msgstr "Светло-зелёная"

#: includes/settings/main-settings.php:33
msgid "Light Yellow"
msgstr "Светло-жёлтая"

#: includes/settings/main-settings.php:34
msgid "Minimal Blue"
msgstr "Минимальная синяя"

#: includes/settings/main-settings.php:35
msgid "Minimal Orange"
msgstr "Минимальная оранжевая"

#: includes/settings/main-settings.php:36
msgid "Minimal"
msgstr "Минимальная"

#: includes/settings/main-settings.php:38
msgid "Sky Blue"
msgstr "Лазурная"

#: includes/settings/main-settings.php:39
msgid "Slate Blue"
msgstr "Cеро-голубая"

#: includes/settings/main-settings.php:40
msgid "Turquoise"
msgstr "Бирюзовая"

#: includes/shortcodes/account-shortcode.php:212
#: includes/views/shortcodes/checkout-view.php:22
msgid "Invalid login or password."
msgstr "Неверный логин или пароль."

#: includes/shortcodes/account-shortcode.php:221
msgid "Account data updated."
msgstr "Данные аккаунта обновлены."

#: includes/shortcodes/account-shortcode.php:227
msgid "Password changed."
msgstr "Пароль изменен."

#: includes/shortcodes/account-shortcode.php:238
msgid "Dashboard"
msgstr "Панель управления"

#: includes/shortcodes/account-shortcode.php:240
msgid "Account"
msgstr "Аккаунт"

#: includes/shortcodes/account-shortcode.php:241
msgid "Logout"
msgstr "Выйти"

#: includes/shortcodes/account-shortcode.php:279
msgid "Passwords do not match."
msgstr "Пароли не совпадают."

#: includes/shortcodes/account-shortcode.php:282
msgid "Please, provide a valid current password."
msgstr "Пожалуйста, укажите правильный текущий пароль."

#: includes/shortcodes/account-shortcode.php:301
#: includes/views/shortcodes/checkout-view.php:54
msgid "Lost your password?"
msgstr "Забыли пароль?"

#: includes/shortcodes/booking-confirmation-shortcode.php:294
msgid "Payment:"
msgstr "Оплата:"

#: includes/shortcodes/booking-confirmation-shortcode.php:302
msgid "Payment Method:"
msgstr "Способ оплаты:"

#: includes/shortcodes/booking-confirmation-shortcode.php:315
#: templates/shortcodes/booking-details/booking-details.php:42
msgid "Status:"
msgstr "Статус:"

#: includes/shortcodes/checkout-shortcode.php:196
msgid "Bookings are disabled in the settings."
msgstr ""

#: includes/shortcodes/checkout-shortcode/step-booking.php:151
msgid "Checkout data is not valid."
msgstr "Данные для заказа недействительны."

#: includes/shortcodes/checkout-shortcode/step-booking.php:449
msgid "Payment method is not valid."
msgstr "Способ оплаты неверный."

#: includes/shortcodes/checkout-shortcode/step-checkout.php:193
msgid "Accommodation count is not valid."
msgstr "Количество вариантов размещения неверное."

#: includes/shortcodes/checkout-shortcode/step.php:110
msgid "Accommodation is already booked."
msgstr "Данный вариант размещения уже забронирован."

#: includes/shortcodes/checkout-shortcode/step.php:120
#: includes/shortcodes/checkout-shortcode/step.php:129
#: includes/shortcodes/checkout-shortcode/step.php:138
msgid "Reservation submitted"
msgstr "Бронирование отправлено"

#: includes/shortcodes/checkout-shortcode/step.php:121
msgid "Details of your reservation have just been sent to you in a confirmation email. Please check your inbox to complete booking."
msgstr "Детали подтверждения бронирования отправлены вам на почту. Проверьте письмо для завершения бронирования."

#: includes/shortcodes/checkout-shortcode/step.php:130
#: includes/shortcodes/checkout-shortcode/step.php:139
msgid "We received your booking request. Once it is confirmed we will notify you via email."
msgstr "Мы получил ваш запрос на бронирование. Вы будете оповещены по электронной почте после его подтверждения."

#: includes/shortcodes/room-rates-shortcode.php:104
#: template-functions.php:31
msgid "Choose dates to see relevant prices"
msgstr "Выберите даты, чтобы увидеть актуальные цены"

#: includes/shortcodes/search-results-shortcode.php:766
msgid "Select from available accommodations."
msgstr "Выберите из доступных вариантов размещения."

#: includes/shortcodes/search-results-shortcode.php:775
#: includes/shortcodes/search-results-shortcode.php:1013
#: template-functions.php:843
msgid "Confirm Reservation"
msgstr "Подтвердить бронирование"

#: includes/shortcodes/search-results-shortcode.php:804
msgid "Recommended for %d adult"
msgid_plural "Recommended for %d adults"
msgstr[0] "Рекомендовано для %d взрослого"
msgstr[1] "Рекомендовано для %d взрослых"
msgstr[2] "Рекомендовано для %d взрослых"
msgstr[3] ""

#: includes/shortcodes/search-results-shortcode.php:806
msgid " and %d child"
msgid_plural " and %d children"
msgstr[0] " и %d ребёнка"
msgstr[1] " и %d детей"
msgstr[2] " и %d детей"
msgstr[3] ""

#: includes/shortcodes/search-results-shortcode.php:809
msgid "Recommended for %d guest"
msgid_plural "Recommended for %d guests"
msgstr[0] "Рекомендовано для %d гостя"
msgstr[1] "Рекомендовано для %d гостей"
msgstr[2] "Рекомендовано для %d гостей"
msgstr[3] ""

#: includes/shortcodes/search-results-shortcode.php:891
msgid "Max occupancy:"
msgstr "Вмещает:"

#: includes/shortcodes/search-results-shortcode.php:910
msgid "%d child"
msgid_plural "%d children"
msgstr[0] "%d ребёнка"
msgstr[1] "%d детей"
msgstr[2] "%d детей"
msgstr[3] ""

#: includes/shortcodes/search-results-shortcode.php:945
#: templates/create-booking/results/reserve-rooms.php:76
msgid "Reserve"
msgstr "Забронировать"

#: includes/shortcodes/search-results-shortcode.php:1002
msgid "of %d accommodation available."
msgid_plural "of %d accommodations available."
msgstr[0] "из %d варианта размещения доступно."
msgstr[1] "из %d вариантов размещения доступно."
msgstr[2] "из %d вариантов размещения доступно."
msgstr[3] ""

#. translators: Verb. To book an accommodation.
#: includes/shortcodes/search-results-shortcode.php:1012
#: template-functions.php:531
#: template-functions.php:544
msgid "Book"
msgstr "Забронировать"

#: includes/users-and-roles/customers.php:290
#: includes/users-and-roles/customers.php:383
msgid "Please, provide a valid email."
msgstr "Пожалуйста, укажите действующий адрес электронной почты."

#: includes/users-and-roles/customers.php:318
msgid "Could not create a customer."
msgstr "Не удалось создать клиента."

#: includes/users-and-roles/customers.php:379
msgid "Could not retrieve a customer."
msgstr "Не удалось получить клиента."

#: includes/users-and-roles/customers.php:531
#: includes/users-and-roles/customers.php:577
msgid "Please, provide a valid Customer ID."
msgstr "Пожалуйста, укажите правильный ID клиента."

#: includes/users-and-roles/customers.php:563
msgid "A database error."
msgstr "Ошибка базы данных."

#: includes/users-and-roles/customers.php:583
msgid "No customer was deleted."
msgstr "Клиент не был удален."

#: includes/users-and-roles/customers.php:694
#: includes/views/shortcodes/checkout-view.php:31
msgid "An account with this email already exists. Please, log in."
msgstr "Аккаунт с таким адресом электронной почты уже существует. Пожалуйста, войдите."

#: includes/users-and-roles/roles.php:34
msgid "Hotel Manager"
msgstr "Гостиничный менеджер"

#: includes/users-and-roles/roles.php:41
msgid "Hotel Worker"
msgstr "Гостиничный работник"

#: includes/users-and-roles/roles.php:48
msgid "Hotel Customer"
msgstr "Гостиничный клиент"

#: includes/users-and-roles/user.php:54
msgid "Please provide a valid email address."
msgstr "Пожалуйста, укажите правильный адрес электронной почты."

#: includes/users-and-roles/user.php:69
msgid "Please enter a valid account username."
msgstr "Пожалуйста, введите правильное имя пользователя."

#: includes/users-and-roles/user.php:73
msgid "An account is already registered with that username. Please choose another."
msgstr "Аккаунт с таким именем пользователя уже зарегистрирован. Пожалуйста, выберите другое имя."

#: includes/users-and-roles/user.php:81
msgid "Please enter an account password."
msgstr "Пожалуйста, введите пароль вашего аккаунта."

#: includes/utils/date-utils.php:145
msgid "Sunday"
msgstr "Воскресенье"

#: includes/utils/date-utils.php:146
msgid "Monday"
msgstr "Понедельник"

#: includes/utils/date-utils.php:147
msgid "Tuesday"
msgstr "Вторник"

#: includes/utils/date-utils.php:148
msgid "Wednesday"
msgstr "Среда"

#: includes/utils/date-utils.php:149
msgid "Thursday"
msgstr "Четверг"

#: includes/utils/date-utils.php:150
msgid "Friday"
msgstr "Пятница"

#: includes/utils/date-utils.php:151
msgid "Saturday"
msgstr "Суббота"

#: includes/utils/parse-utils.php:135
msgid "Check-out date cannot be earlier than check-in date."
msgstr "Дата отъезда не может быть ранее даты заезда."

#: includes/utils/parse-utils.php:159
msgid "Adults number is not valid"
msgstr "Количество взрослых неверно"

#: includes/utils/parse-utils.php:183
msgid "Children number is not valid"
msgstr "Количество детей неверно"

#: includes/utils/taxes-and-fees-utils.php:27
msgctxt "Text about taxes and fees below the price."
msgid " (+taxes and fees)"
msgstr " (+налоги и сборы)"

#. translators: %s is a tax value
#: includes/utils/taxes-and-fees-utils.php:56
msgctxt "Text about taxes and fees below the price."
msgid " (+%s taxes and fees)"
msgstr " (+%s налогов и сборов)"

#: includes/utils/taxes-and-fees-utils.php:84
msgctxt "Text about taxes and fees below the price."
msgid " (includes taxes and fees)"
msgstr " (включая налоги и сборы)"

#: includes/views/booking-view.php:79
msgctxt "Accommodation type in price breakdown table. Example: #1 Double Room"
msgid "#%d %s"
msgstr "#%d %s"

#: includes/views/booking-view.php:82
msgid "Expand"
msgstr "Развернуть"

#: includes/views/booking-view.php:91
#: includes/views/edit-booking/checkout-view.php:209
msgid "Rate: %s"
msgstr "Тариф: %s"

#: includes/views/booking-view.php:125
msgid "Dates"
msgstr "Даты"

#: includes/views/booking-view.php:207
#: includes/views/loop-room-type-view.php:39
#: includes/views/single-room-type-view.php:131
#: includes/widgets/rooms-widget.php:197
#: assets/blocks/blocks.js:484
#: assets/blocks/blocks.js:734
#: assets/blocks/blocks.js:1263
msgid "Details"
msgstr "Детали"

#: includes/views/booking-view.php:380
msgid "Subtotal"
msgstr "Промежуточный итог"

#: includes/views/booking-view.php:393
msgid "Coupon: %s"
msgstr "Купон на скидку: %s"

#: includes/views/booking-view.php:412
msgid "Subtotal (excl. taxes)"
msgstr "Итого (без налогов)"

#: includes/views/booking-view.php:422
msgid "Taxes"
msgstr "Налоги"

#: includes/views/create-booking/checkout-view.php:55
#: includes/views/shortcodes/checkout-view.php:89
msgid "Coupon Code:"
msgstr "Код купона:"

#: includes/views/edit-booking/checkout-view.php:25
msgid "New Booking Details"
msgstr "Детали нового бронирования"

#: includes/views/edit-booking/checkout-view.php:43
msgid "Original Booking Details"
msgstr "Детали первоначального бронирования"

#: includes/views/edit-booking/checkout-view.php:154
#: includes/views/shortcodes/checkout-view.php:269
#: template-functions.php:794
#: templates/create-booking/search/search-form.php:111
#: templates/shortcodes/search/search-form.php:105
msgid "Children %s"
msgstr "Дети %s"

#: includes/views/edit-booking/checkout-view.php:232
#: templates/emails/reserved-room-details.php:30
msgid "Additional Services"
msgstr "Дополнительные услуги"

#: includes/views/edit-booking/checkout-view.php:249
#: includes/views/reserved-room-view.php:26
#: template-functions.php:937
msgid "x %d guest"
msgid_plural "x %d guests"
msgstr[0] "x %d гость"
msgstr[1] "x %d гостя"
msgstr[2] "x %d гостей"
msgstr[3] ""

#: includes/views/edit-booking/checkout-view.php:253
#: includes/views/reserved-room-view.php:31
#: template-functions.php:940
msgid "x %d time"
msgid_plural "x %d times"
msgstr[0] "x %d раз"
msgstr[1] "x %d раза"
msgstr[2] "x %d раз"
msgstr[3] ""

#: includes/views/global-view.php:53
msgid "Accommodation pagination"
msgstr "Количество вариантов размещения на странице"

#: includes/views/global-view.php:56
msgid "Services pagination"
msgstr "Количество услуг на странице"

#: includes/views/loop-room-type-view.php:55
#: includes/views/single-room-type-view.php:147
#: templates/widgets/rooms/room-content.php:99
msgid "Categories:"
msgstr "Категории:"

#: includes/views/loop-room-type-view.php:67
#: includes/views/single-room-type-view.php:159
#: templates/widgets/rooms/room-content.php:129
msgid "Amenities:"
msgstr "Удобства:"

#: includes/views/loop-room-type-view.php:97
#: includes/views/loop-room-type-view.php:115
#: includes/views/single-room-type-view.php:189
#: includes/views/single-room-type-view.php:207
#: templates/widgets/rooms/room-content.php:67
#: templates/widgets/rooms/room-content.php:79
#: templates/widgets/search-availability/search-form.php:80
msgid "Guests:"
msgstr "Гостей:"

#: includes/views/loop-room-type-view.php:140
#: includes/views/single-room-type-view.php:232
#: templates/widgets/rooms/room-content.php:175
msgid "Bed Type:"
msgstr "Тип кровати:"

#: includes/views/loop-room-type-view.php:164
#: includes/views/single-room-type-view.php:256
#: templates/widgets/rooms/room-content.php:159
msgid "View:"
msgstr "Вид из окна:"

#: includes/views/loop-room-type-view.php:184
#: includes/views/single-room-type-view.php:276
#: template-functions.php:839
#: templates/widgets/rooms/room-content.php:227
msgid "Prices start at:"
msgstr "Цены от:"

#: includes/views/loop-service-view.php:46
msgid "Price:"
msgstr "Цена:"

#: includes/views/shortcodes/checkout-view.php:49
msgid "Returning customer?"
msgstr "Зарегистрированный клиент?"

#: includes/views/shortcodes/checkout-view.php:50
msgid "Click here to log in"
msgstr "Нажмите здесь, чтобы войти"

#. translators: 1 - username;
#: includes/views/shortcodes/checkout-view.php:70
#: templates/account/dashboard.php:29
msgid "Hello %1$s (not %1$s? <a href=\"%2$s\">Log out</a>)."
msgstr "Здравствуйте, %1$s (не %1$s? <a href=\"%2$s\">Выйти</a>)."

#: includes/views/shortcodes/checkout-view.php:182
msgid "Accommodation #%d"
msgstr "Вариант размещения #%d"

#: includes/views/shortcodes/checkout-view.php:186
msgid "Accommodation Type:"
msgstr "Тип размещения:"

#: includes/views/shortcodes/checkout-view.php:324
msgid "Choose Rate"
msgstr "Выберите тариф"

#: includes/views/shortcodes/checkout-view.php:397
msgid "Choose Additional Services"
msgstr "Выберите дополнительные услуги"

#: includes/views/shortcodes/checkout-view.php:429
msgid "for "
msgstr "для "

#: includes/views/shortcodes/checkout-view.php:442
msgctxt "Example: Breakfast for X guest(s)"
msgid " guest(s)"
msgstr " гостя(ей)"

#: includes/views/shortcodes/checkout-view.php:464
msgid "time(s)"
msgstr "раз(а)"

#: includes/views/shortcodes/checkout-view.php:533
msgctxt "I've read and accept the terms & conditions"
msgid "terms & conditions"
msgstr "правила и условия"

#: includes/views/shortcodes/checkout-view.php:536
msgctxt "I've read and accept the <tag>terms & conditions</tag>"
msgid "I've read and accept the %s"
msgstr "Я прочитал и принимаю %s"

#: includes/views/shortcodes/checkout-view.php:579
msgid "Your Information"
msgstr "Ваша информация"

#: includes/views/shortcodes/checkout-view.php:582
#: template-functions.php:696
#: templates/widgets/search-availability/search-form.php:24
msgid "Required fields are followed by %s"
msgstr "Отмеченные %s поля обязательны для заполнения."

#: includes/views/shortcodes/checkout-view.php:758
msgid "Create an account"
msgstr "Создать аккаунт"

#: includes/views/shortcodes/checkout-view.php:775
msgid "Payment Method"
msgstr "Способ оплаты"

#: includes/views/shortcodes/checkout-view.php:780
msgid "Sorry, it seems that there are no available payment methods."
msgstr "Извините, но доступных способов оплаты нет."

#: includes/views/shortcodes/checkout-view.php:874
#: templates/emails/admin-customer-cancelled-booking.php:32
#: templates/emails/admin-customer-confirmed-booking.php:32
#: templates/emails/admin-payment-confirmed-booking.php:39
#: templates/emails/admin-pending-booking.php:32
#: templates/emails/customer-approved-booking.php:28
#: templates/emails/customer-cancelled-booking.php:26
#: templates/emails/customer-confirmation-booking.php:32
#: templates/emails/customer-pending-booking.php:30
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:27
msgid "Total Price:"
msgstr "Итоговая стоимость:"

#: includes/views/shortcodes/checkout-view.php:886
msgid "Deposit:"
msgstr "Залог:"

#: includes/views/shortcodes/checkout-view.php:906
#: templates/shortcodes/booking-details/booking-details.php:25
#: templates/widgets/search-availability/search-form.php:35
msgid "Check-in:"
msgstr "Дата заезда:"

#: includes/views/shortcodes/checkout-view.php:913
msgctxt "from 10:00 am"
msgid "from"
msgstr "с"

#: includes/views/shortcodes/checkout-view.php:929
#: templates/shortcodes/booking-details/booking-details.php:29
#: templates/widgets/search-availability/search-form.php:54
msgid "Check-out:"
msgstr "Дата отъезда:"

#: includes/views/shortcodes/checkout-view.php:936
msgctxt "until 10:00 am"
msgid "until"
msgstr "до"

#: includes/views/shortcodes/checkout-view.php:1012
#: templates/create-booking/checkout/checkout-form.php:42
msgid "Book Now"
msgstr "Забронировать"

#: includes/views/single-room-type-view.php:127
msgid "Availability"
msgstr "Доступность"

#: includes/views/single-room-type-view.php:292
msgid "Reservation Form"
msgstr "Форма бронирования"

#: includes/widgets/rooms-widget.php:24
msgid "Display Accommodation Types"
msgstr "Показывать типы размещения"

#: includes/widgets/rooms-widget.php:169
#: includes/widgets/search-availability-widget.php:236
msgid "Title:"
msgstr "Название:"

#: includes/widgets/rooms-widget.php:189
#: assets/blocks/blocks.js:448
#: assets/blocks/blocks.js:698
#: assets/blocks/blocks.js:1227
msgid "Featured Image"
msgstr "Главное изображение"

#: includes/widgets/rooms-widget.php:193
#: assets/blocks/blocks.js:472
#: assets/blocks/blocks.js:722
#: assets/blocks/blocks.js:1251
msgid "Excerpt (short description)"
msgstr "Отрывок (короткое описание)"

#: includes/widgets/rooms-widget.php:205
#: assets/blocks/blocks.js:770
#: assets/blocks/blocks.js:1299
msgid "Book Button"
msgstr "Кнопка \"Забронировать\""

#: includes/widgets/search-availability-widget.php:50
#: includes/wizard.php:84
msgid "Search Availability"
msgstr "Проверить наличие мест"

#: includes/widgets/search-availability-widget.php:53
msgid "Search Availability Form"
msgstr "Форма для проверки наличия мест"

#: includes/widgets/search-availability-widget.php:240
msgid "Check-in Date:"
msgstr "Дата заезда:"

#: includes/widgets/search-availability-widget.php:241
#: includes/widgets/search-availability-widget.php:246
msgctxt "Date format tip"
msgid "Preset date. Formatted as %s"
msgstr "Предустановленные даты. В формате %s"

#: includes/widgets/search-availability-widget.php:244
msgid "Check-out Date:"
msgstr "Дата отъезда:"

#: includes/widgets/search-availability-widget.php:249
msgid "Preset Adults:"
msgstr "Предустановленное значение поля Взрослые:"

#: includes/widgets/search-availability-widget.php:257
msgid "Preset Children:"
msgstr "Предустановленное значение поля Дети:"

#: includes/widgets/search-availability-widget.php:265
msgid "Attributes:"
msgstr "Атрибуты:"

#: includes/wizard.php:34
msgid "Booking Confirmation and Search Results pages are required to handle bookings. Press \"Install Pages\" button to create and set up these pages. Dismiss this notice if you already installed them."
msgstr "Страницы оплаты и результатов поиска необходимы для правильной работы системы бронирования. Нажмите \"Установить страницы\", чтобы создать и установить эти страницы. Пропустите это уведомление, если эти страницы уже установлены."

#: includes/wizard.php:35
msgid "Install Pages"
msgstr "Установить страницы"

#: includes/wizard.php:147
msgid "Booking Canceled"
msgstr "Бронирование отменено"

#: includes/wizard.php:148
msgid "Your reservation is canceled."
msgstr "Ваше бронирование отменено."

#: includes/wizard.php:183
msgid "Reservation Received"
msgstr "Бронирование получено"

#: includes/wizard.php:196
msgid "Transaction Failed"
msgstr "Сбой транзакции"

#: includes/wizard.php:197
msgid "Unfortunately, your transaction cannot be completed at this time. Please try again or contact us."
msgstr "К сожалению, сейчас ваш платеж не может быть завершен. Пожалуйста, повторите попытку или свяжитесь с нами."

#: plugin.php:1100
msgid "Prices start at: %s"
msgstr "Цены от: %s"

#: template-functions.php:563
msgid "View Details"
msgstr "Посмотреть детали"

#: template-functions.php:593
#: template-functions.php:652
msgid "Accommodation %s not found."
msgstr ""

#: template-functions.php:707
#: template-functions.php:716
#: templates/create-booking/search/search-form.php:43
#: templates/create-booking/search/search-form.php:63
#: templates/edit-booking/edit-dates.php:30
#: templates/edit-booking/edit-dates.php:39
#: templates/shortcodes/search/search-form.php:36
#: templates/shortcodes/search/search-form.php:56
#: templates/widgets/search-availability/search-form.php:36
#: templates/widgets/search-availability/search-form.php:55
msgctxt "Date format tip"
msgid "Formatted as %s"
msgstr "В формате %s"

#: template-functions.php:831
msgid "Reserve %1$s of %2$s available accommodations."
msgstr "Забронировать %1$s из %2$s доступных вариантов."

#: template-functions.php:835
msgid "%s is available for selected dates."
msgstr "%s доступно для выбранных дат."

#: template-functions.php:849
#: templates/edit-booking/edit-dates.php:46
msgid "Check Availability"
msgstr "Проверить наличие мест"

#: template-functions.php:910
msgid "Rate:"
msgstr "Тариф:"

#: template-functions.php:930
msgid "Services:"
msgstr "Услуги:"

#: template-functions.php:953
msgid "Guest:"
msgstr "Гости:"

#: template-functions.php:976
msgid "Payment ID"
msgstr "ID платежа"

#: template-functions.php:1008
msgid "Total Paid"
msgstr "Всего оплачено"

#: template-functions.php:1017
msgid "To Pay"
msgstr "Осталось оплатить"

#: template-functions.php:1042
msgid "Add Payment Manually"
msgstr "Добавить платёж вручную"

#: templates/account/account-details.php:78
msgid "Change Password"
msgstr "Изменить пароль"

#: templates/account/account-details.php:81
msgid "Old Password"
msgstr "Старый пароль"

#: templates/account/account-details.php:85
msgid "New Password"
msgstr "Новый пароль"

#: templates/account/account-details.php:89
msgid "Confirm New Password"
msgstr "Подтвердите новый пароль"

#: templates/account/account-details.php:99
msgid "You are not allowed to access this page."
msgstr "Вы не имеете доступа к этой странице."

#: templates/account/bookings.php:116
#: templates/account/bookings.php:121
msgid "No bookings found."
msgstr "Не найдено ни одного бронирования."

#: templates/account/dashboard.php:41
msgid "From your account dashboard you can view <a href=\"%1$s\">your recent bookings</a> or edit your <a href=\"%2$s\">password and account details</a>."
msgstr "На главной странице аккаунта вы можете видеть ваши <a href=\"%1$s\">недавние бронирования</a> или изменить ваш <a href=\"%2$s\">пароль и данные учетной записи</a>."

#: templates/create-booking/results/reserve-rooms.php:37
msgid "Base price"
msgstr "Начальная цена"

#: templates/create-booking/results/rooms-found.php:19
#: templates/shortcodes/search-results/results-info.php:17
msgid "%s accommodation found"
msgid_plural "%s accommodations found"
msgstr[0] "%s вариант найден"
msgstr[1] "%s варианта найдено"
msgstr[2] "%s вариантов найдено"
msgstr[3] ""

#: templates/create-booking/results/rooms-found.php:24
#: templates/shortcodes/search-results/results-info.php:21
msgid " from %s - till %s"
msgstr " с %s - до %s"

#: templates/edit-booking/add-room-popup.php:24
#: templates/edit-booking/edit-reserved-rooms.php:36
msgid "Add Accommodation"
msgstr "Добавить вариант размещения"

#: templates/edit-booking/checkout-form.php:28
msgid "Save"
msgstr "Сохранить"

#: templates/edit-booking/edit-dates.php:25
msgid "Choose new dates to check availability of reserved accommodations in the original booking."
msgstr "Выберите даты, чтобы проверить доступность вариантов размещения в первоначальном бронировании."

#: templates/edit-booking/edit-reserved-rooms.php:39
msgid "Add, remove or replace accommodations in the original booking."
msgstr "Добавляйте, удаляйте или заменяйте варианты размещения в первоначальном бронировании."

#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Not Available"
msgstr "Недоступно"

#: templates/edit-booking/edit-reserved-rooms.php:79
#: templates/edit-booking/summary-table.php:65
msgid "Continue"
msgstr "Продолжить"

#: templates/edit-booking/summary-table.php:26
msgid "Choose how to associate data"
msgstr "Выберите способ использования деталей бронирования"

#: templates/edit-booking/summary-table.php:27
msgid "Use Source Accommodation to assign pre-filled booking information available in the original booking, e.g., full guest name, selected rate, services, etc."
msgstr "Используйте оригинальный вариант размещения для копирования уже сохраненных деталей бронирования, например, полное имя гостя, тарифы, услуги, и т.д."

#: templates/edit-booking/summary-table.php:32
msgid "Source accommodation"
msgstr "Оригинальный вариант размещения"

#: templates/edit-booking/summary-table.php:34
msgid "Target accommodation"
msgstr "Целевой вариант размещения"

#: templates/emails/admin-customer-cancelled-booking.php:15
msgid "Booking #%s is cancelled by customer."
msgstr "Бронирование #%s было отменено клиентом."

#: templates/emails/admin-customer-cancelled-booking.php:17
#: templates/emails/admin-customer-confirmed-booking.php:17
#: templates/emails/admin-payment-confirmed-booking.php:24
#: templates/emails/admin-pending-booking.php:17
#: templates/emails/customer-approved-booking.php:17
#: templates/emails/customer-cancelled-booking.php:18
#: templates/emails/customer-confirmation-booking.php:24
#: templates/emails/customer-pending-booking.php:19
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:19
msgid "Details of booking"
msgstr "Детали бронирования"

#: templates/emails/admin-customer-cancelled-booking.php:18
#: templates/emails/admin-customer-confirmed-booking.php:18
#: templates/emails/admin-payment-confirmed-booking.php:25
#: templates/emails/admin-pending-booking.php:18
#: templates/emails/customer-approved-booking.php:20
#: templates/emails/customer-cancelled-booking.php:21
#: templates/emails/customer-confirmation-booking.php:27
#: templates/emails/customer-pending-booking.php:22
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:22
msgid "Check-in: %1$s, from %2$s"
msgstr "Дата заезда: %1$s, до %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:20
#: templates/emails/admin-customer-confirmed-booking.php:20
#: templates/emails/admin-payment-confirmed-booking.php:27
#: templates/emails/admin-pending-booking.php:20
#: templates/emails/customer-approved-booking.php:22
#: templates/emails/customer-cancelled-booking.php:23
#: templates/emails/customer-confirmation-booking.php:29
#: templates/emails/customer-pending-booking.php:24
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:24
msgid "Check-out: %1$s, until %2$s"
msgstr "Дата отъезда: %1$s, до %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:24
#: templates/emails/admin-customer-confirmed-booking.php:24
#: templates/emails/admin-payment-confirmed-booking.php:31
#: templates/emails/admin-pending-booking.php:24
#: templates/emails/customer-approved-booking.php:32
#: templates/emails/customer-cancelled-booking.php:30
#: templates/emails/customer-confirmation-booking.php:36
#: templates/emails/customer-pending-booking.php:33
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:31
msgid "Name: %1$s %2$s"
msgstr "Имя: %1$s %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:26
#: templates/emails/admin-customer-confirmed-booking.php:26
#: templates/emails/admin-payment-confirmed-booking.php:33
#: templates/emails/admin-pending-booking.php:26
#: templates/emails/customer-approved-booking.php:34
#: templates/emails/customer-cancelled-booking.php:32
#: templates/emails/customer-confirmation-booking.php:38
#: templates/emails/customer-pending-booking.php:35
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:33
msgid "Email: %s"
msgstr "Email адрес: %s"

#: templates/emails/admin-customer-cancelled-booking.php:28
#: templates/emails/admin-customer-confirmed-booking.php:28
#: templates/emails/admin-payment-confirmed-booking.php:35
#: templates/emails/admin-pending-booking.php:28
#: templates/emails/customer-approved-booking.php:36
#: templates/emails/customer-cancelled-booking.php:34
#: templates/emails/customer-confirmation-booking.php:40
#: templates/emails/customer-pending-booking.php:37
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:35
msgid "Phone: %s"
msgstr "Телефон: %s"

#: templates/emails/admin-customer-cancelled-booking.php:30
#: templates/emails/admin-customer-confirmed-booking.php:30
#: templates/emails/admin-payment-confirmed-booking.php:37
#: templates/emails/admin-pending-booking.php:30
#: templates/emails/customer-approved-booking.php:38
#: templates/emails/customer-cancelled-booking.php:36
#: templates/emails/customer-confirmation-booking.php:42
#: templates/emails/customer-pending-booking.php:39
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:37
msgid "Note: %s"
msgstr "Примечание: %s"

#: templates/emails/admin-customer-confirmed-booking.php:15
msgid "Booking #%s is confirmed by customer."
msgstr "Бронирование #%s было подтверждено клиентом. "

#: templates/emails/admin-payment-confirmed-booking.php:15
msgid "Booking #%s is confirmed by payment."
msgstr "Бронирование #%s подтверждено оплатой."

#: templates/emails/admin-payment-confirmed-booking.php:17
msgid "Details of payment"
msgstr "Детали оплаты"

#: templates/emails/admin-payment-confirmed-booking.php:18
msgid "Payment ID: #%s"
msgstr "ID платежа: #%s"

#: templates/emails/admin-payment-confirmed-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:16
msgid "Amount: %s"
msgstr "Сумма: %s"

#: templates/emails/admin-payment-confirmed-booking.php:22
msgid "Method: %s"
msgstr "Способ оплаты: %s"

#: templates/emails/admin-pending-booking.php:15
msgid "Booking #%s is pending for Administrator approval."
msgstr "Бронирование #%s в ожидании подтверждения администратором."

#: templates/emails/cancellation-details.php:14
msgid "Click the link below to cancel your booking."
msgstr "Нажмите на ссылку ниже для отмены бронирования."

#: templates/emails/cancellation-details.php:16
msgid "Cancel your booking"
msgstr "Отмените бронирование"

#: templates/emails/customer-approved-booking.php:15
msgid "Dear %1$s %2$s, your reservation is approved!"
msgstr "%1$s %2$s, Ваше бронирование подтверждено!"

#: templates/emails/customer-approved-booking.php:18
#: templates/emails/customer-cancelled-booking.php:19
#: templates/emails/customer-confirmation-booking.php:25
#: templates/emails/customer-pending-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:20
msgid "ID: #%s"
msgstr "ID: #%s"

#: templates/emails/customer-approved-booking.php:41
#: templates/emails/customer-cancelled-booking.php:38
#: templates/emails/customer-confirmation-booking.php:44
#: templates/emails/customer-pending-booking.php:41
#: templates/emails/customer-registration.php:26
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:39
msgid "Thank you!"
msgstr "Спасибо!"

#: templates/emails/customer-cancelled-booking.php:15
msgid "Dear %1$s %2$s, your reservation is cancelled!"
msgstr "%1$s %2$s, Ваше бронирование отменено!"

#: templates/emails/customer-confirmation-booking.php:14
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:12
msgid "Dear %1$s %2$s, we received your request for reservation."
msgstr "%1$s %2$s, мы получили Ваш запрос на бронирование."

#: templates/emails/customer-confirmation-booking.php:16
msgid "Click the link below to confirm your booking."
msgstr "Нажмите на ссылку ниже для подтверждения бронирования."

#: templates/emails/customer-confirmation-booking.php:18
msgid "Confirm"
msgstr "Подтвердить"

#: templates/emails/customer-confirmation-booking.php:20
msgid "Note: link expires on"
msgstr "Внимание: ссылка станет нерабочей"

#: templates/emails/customer-confirmation-booking.php:20
msgid "UTC"
msgstr "UTC"

#: templates/emails/customer-confirmation-booking.php:22
msgid "If you did not place this booking, please ignore this email."
msgstr "Если Вы не оставляли запрос на бронирование, пожалуйста, проигнорируйте это письмо."

#: templates/emails/customer-pending-booking.php:15
msgid "Dear %1$s %2$s, your reservation is pending."
msgstr "%1$s %2$s, Ваше бронирование ожидает подтверждения."

#: templates/emails/customer-pending-booking.php:17
msgid "We will notify you by email once it is confirmed by our staff."
msgstr "Как только Ваш запрос будет подтвержден, мы пришлём уведомление на Ваш email адрес."

#: templates/emails/customer-registration.php:15
msgid "Hi %1$s %2$s,"
msgstr "Здравствуйте %1$s %2$s,"

#: templates/emails/customer-registration.php:17
msgid "Thanks for creating an account on %1$s."
msgstr "Спасибо за создание аккаунта на %1$s."

#: templates/emails/customer-registration.php:19
msgid "You Account Details"
msgstr "Данные вашего аккаунта"

#: templates/emails/customer-registration.php:20
msgid "Login: %s"
msgstr "Логин: %s"

#: templates/emails/customer-registration.php:21
msgid "Password: %s"
msgstr "Пароль: %s"

#: templates/emails/customer-registration.php:22
msgid "Log in here: %s"
msgstr "Войти в аккаунт: %s"

#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:14
msgid "To confirm your booking, please follow the instructions below for payment."
msgstr ""

#: templates/emails/reserved-room-details.php:14
msgid "Accommodation #%s"
msgstr "Вариант размещения #%s"

#: templates/emails/reserved-room-details.php:21
msgid "Accommodation: <a href=\"%1$s\">%2$s</a>"
msgstr "Номер/комната: <a href=\"%1$s\">%2$s</a>"

#: templates/emails/reserved-room-details.php:24
msgid "Accommodation Rate: %s"
msgstr "Тариф для размещения: %s"

#: templates/emails/reserved-room-details.php:28
msgid "Bed Type: %s"
msgstr "Тип кровати: %s"

#: templates/required-fields-tip.php:8
msgid "Required fields are followed by"
msgstr "Обязательные для заполнения поля обозначены"

#: templates/shortcodes/booking-cancellation/already-cancelled.php:7
msgid "Booking is already canceled."
msgstr "Бронирование уже отменено."

#: templates/shortcodes/booking-cancellation/booking-cancellation-button.php:15
msgid "Cancel Booking"
msgstr "Отменить бронирование"

#: templates/shortcodes/booking-cancellation/invalid-request.php:7
#: templates/shortcodes/booking-confirmation/invalid-request.php:7
msgid "Invalid request."
msgstr "Недопустимый запрос."

#: templates/shortcodes/booking-cancellation/not-possible.php:7
msgid "Cancelation of your booking is not possible for some reason. Please contact the website administrator."
msgstr "Отмена бронирования невозможна по какой-либо причине. Пожалуйста, свяжитесь с администратором сайта."

#: templates/shortcodes/booking-confirmation/already-confirmed.php:7
msgid "Booking is already confirmed."
msgstr "Бронирование уже подтверждено."

#: templates/shortcodes/booking-confirmation/confirmed.php:7
msgid "Your booking is confirmed. Thank You!"
msgstr "Ваше бронирование подтверждено. Спасибо!"

#: templates/shortcodes/booking-confirmation/expired.php:7
msgid "Your booking request is expired. Please start a new booking request."
msgstr "Истёк срок запроса на бронирование. Пожалуйста, забронируйте заново."

#: templates/shortcodes/booking-confirmation/not-possible.php:7
msgid "Confirmation of your booking request is not possible for some reason. Please start a new booking request."
msgstr "Подтверждение Вашего запроса на бронирование невозможно по какой-то причине. Пожалуйста, забронируйте повторно."

#: templates/shortcodes/booking-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received and confirmed."
msgstr "Мы рады сообщить, что ваш запрос на бронирование получен и подтвержден."

#: templates/shortcodes/booking-details/booking-details.php:21
msgid "Booking:"
msgstr "Бронирование:"

#: templates/shortcodes/booking-details/booking-details.php:47
msgid "Details:"
msgstr "Детали:"

#: templates/shortcodes/payment-confirmation/completed.php:11
msgid "Thank you for your payment. Your transaction has been completed."
msgstr "Оплата завершена. Спасибо!"

#: templates/shortcodes/payment-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received."
msgstr "Мы рады сообщить, что ваш запрос на бронирование был получен."

#: templates/shortcodes/room-rates/rate-content.php:17
msgid "from %s"
msgstr "с %s"

#: templates/shortcodes/rooms/not-found.php:7
msgid "No accommodations matching criteria."
msgstr "Нет вариантов размещения соответствующих запросу."

#: templates/shortcodes/services/not-found.php:7
msgid "No services matched criteria."
msgstr "Нет услуги по вашему запросу."

#: templates/widgets/rooms/not-found.php:6
msgid "Nothing found."
msgstr "Ничего не найдено."

#: templates/widgets/search-availability/search-form.php:105
msgid "Children %s:"
msgstr "Дети %s:"

#: assets/blocks/blocks.js:178
#: assets/blocks/blocks.js:190
msgid "Preset date. Formatted as %s"
msgstr "Заданная дата. В формате %s"

#: assets/blocks/blocks.js:283
#: assets/blocks/blocks.js:1425
#: assets/blocks/blocks.js:1507
msgid "Select an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:284
#: assets/blocks/blocks.js:1426
#: assets/blocks/blocks.js:1508
msgid "ID of an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:460
#: assets/blocks/blocks.js:710
#: assets/blocks/blocks.js:1239
msgid "Gallery"
msgstr "Галерея"

#: assets/blocks/blocks.js:508
#: assets/blocks/blocks.js:758
#: assets/blocks/blocks.js:1287
msgid "View Button"
msgstr "Кнопка \"Просмотр\""

#: assets/blocks/blocks.js:522
#: assets/blocks/blocks.js:558
#: assets/blocks/blocks.js:858
#: assets/blocks/blocks.js:894
#: assets/blocks/blocks.js:1042
#: assets/blocks/blocks.js:1078
msgid "Order"
msgstr "Порядок"

#: assets/blocks/blocks.js:530
#: assets/blocks/blocks.js:866
#: assets/blocks/blocks.js:1050
msgid "Order By"
msgstr "Упорядочить по"

#: assets/blocks/blocks.js:533
#: assets/blocks/blocks.js:869
#: assets/blocks/blocks.js:1053
msgid "No order"
msgstr "Без сортировки"

#: assets/blocks/blocks.js:534
#: assets/blocks/blocks.js:870
#: assets/blocks/blocks.js:1054
msgid "Post ID"
msgstr "ID записи"

#: assets/blocks/blocks.js:535
#: assets/blocks/blocks.js:871
#: assets/blocks/blocks.js:1055
msgid "Post author"
msgstr "Автор записи"

#: assets/blocks/blocks.js:536
#: assets/blocks/blocks.js:872
#: assets/blocks/blocks.js:1056
msgid "Post title"
msgstr "Заголовок записи"

#: assets/blocks/blocks.js:537
#: assets/blocks/blocks.js:873
#: assets/blocks/blocks.js:1057
msgid "Post name (post slug)"
msgstr "Имя записи (слаг записи)"

#: assets/blocks/blocks.js:538
#: assets/blocks/blocks.js:874
#: assets/blocks/blocks.js:1058
msgid "Post date"
msgstr "Дата записи"

#: assets/blocks/blocks.js:539
#: assets/blocks/blocks.js:875
#: assets/blocks/blocks.js:1059
msgid "Last modified date"
msgstr "Дата последнего изменения"

#: assets/blocks/blocks.js:540
#: assets/blocks/blocks.js:876
#: assets/blocks/blocks.js:1060
msgid "Parent ID"
msgstr "Родительский ID"

#: assets/blocks/blocks.js:541
#: assets/blocks/blocks.js:877
#: assets/blocks/blocks.js:1061
msgid "Random order"
msgstr "Случайный порядок"

#: assets/blocks/blocks.js:542
#: assets/blocks/blocks.js:878
#: assets/blocks/blocks.js:1062
msgid "Number of comments"
msgstr "Количество комментариев"

#: assets/blocks/blocks.js:543
#: assets/blocks/blocks.js:879
#: assets/blocks/blocks.js:1063
msgid "Relevance"
msgstr "Релевантность"

#: assets/blocks/blocks.js:544
#: assets/blocks/blocks.js:880
#: assets/blocks/blocks.js:1064
msgid "Page order"
msgstr "Порядок страниц"

#: assets/blocks/blocks.js:545
#: assets/blocks/blocks.js:881
#: assets/blocks/blocks.js:1065
msgid "Meta value"
msgstr "Значение метаданных"

#: assets/blocks/blocks.js:546
#: assets/blocks/blocks.js:882
#: assets/blocks/blocks.js:1066
msgid "Numeric meta value"
msgstr "Числовое мета-значение"

#: assets/blocks/blocks.js:561
#: assets/blocks/blocks.js:897
#: assets/blocks/blocks.js:1081
msgid "Ascending (1, 2, 3)"
msgstr "По возрастанию (1, 2, 3)"

#: assets/blocks/blocks.js:562
#: assets/blocks/blocks.js:898
#: assets/blocks/blocks.js:1082
msgid "Descending (3, 2, 1)"
msgstr "По убыванию (3, 2, 1)"

#: assets/blocks/blocks.js:573
#: assets/blocks/blocks.js:909
#: assets/blocks/blocks.js:1093
msgid "Meta Name"
msgstr "Имя мета-данных"

#: assets/blocks/blocks.js:585
#: assets/blocks/blocks.js:921
#: assets/blocks/blocks.js:1105
msgid "Meta Type"
msgstr "Тип метаданных"

#: assets/blocks/blocks.js:586
#: assets/blocks/blocks.js:922
#: assets/blocks/blocks.js:1106
msgid "Specified type of the custom field. Can be used in conjunction with \"orderby\" = \"meta_value\"."
msgstr "Указанный тип настраиваемого поля. Может использоваться в сочетании с \"orderby\" = \"meta_value\"."

#: assets/blocks/blocks.js:589
#: assets/blocks/blocks.js:925
#: assets/blocks/blocks.js:1109
msgid "Any"
msgstr "Любой"

#: assets/blocks/blocks.js:590
#: assets/blocks/blocks.js:926
#: assets/blocks/blocks.js:1110
msgid "Numeric"
msgstr "Числовой"

#: assets/blocks/blocks.js:591
#: assets/blocks/blocks.js:927
#: assets/blocks/blocks.js:1111
msgid "Binary"
msgstr "Бинарный"

#: assets/blocks/blocks.js:592
#: assets/blocks/blocks.js:928
#: assets/blocks/blocks.js:1112
msgid "String"
msgstr "Строковой"

#: assets/blocks/blocks.js:594
#: assets/blocks/blocks.js:930
#: assets/blocks/blocks.js:1114
msgid "Time"
msgstr "Время"

#: assets/blocks/blocks.js:595
#: assets/blocks/blocks.js:931
#: assets/blocks/blocks.js:1115
msgid "Date and time"
msgstr "Дата и время"

#: assets/blocks/blocks.js:596
#: assets/blocks/blocks.js:932
#: assets/blocks/blocks.js:1116
msgid "Decimal number"
msgstr "Десятичное число"

#: assets/blocks/blocks.js:597
#: assets/blocks/blocks.js:933
#: assets/blocks/blocks.js:1117
msgid "Signed number"
msgstr "Знаковое число"

#: assets/blocks/blocks.js:598
#: assets/blocks/blocks.js:934
#: assets/blocks/blocks.js:1118
msgid "Unsigned number"
msgstr "Беззнаковое число"

#: assets/blocks/blocks.js:784
#: assets/blocks/blocks.js:1009
msgid "Query Settings"
msgstr "Настройки запроса"

#: assets/blocks/blocks.js:840
msgid "Relation"
msgstr "Связь"

#: assets/blocks/blocks.js:1029
msgid "Values: integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "Значения: число, -1 для отображения всех записей, по умолчанию: На страницах блога отображать не более установленного количества записей (Настройки\\Чтение)"

#: assets/blocks/blocks.js:1203
msgid "Select an accommodation type."
msgstr ""

