# Copyright (C) 2025 MotoPress
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: hotel-booking-plugin\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/motopress-hotel-booking\n"
"Last-Translator: \n"
"Language-Team: Polish\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-19T19:58:50+00:00\n"
"PO-Revision-Date: 2025-03-05 20:50\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: motopress-hotel-booking\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"
"X-Crowdin-Project: hotel-booking-plugin\n"
"X-Crowdin-Project-ID: 463550\n"
"X-Crowdin-Language: pl\n"
"X-Crowdin-File: motopress-hotel-booking.pot\n"
"X-Crowdin-File-ID: 44\n"
"Language: pl_PL\n"

#. Plugin Name of the plugin
#. translators: Name of the plugin, do not translate
#: motopress-hotel-booking.php
#: includes/script-managers/block-script-manager.php:27
msgid "Hotel Booking"
msgstr "Hotel Booking"

#. Plugin URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/products/hotel-booking/"
msgstr ""

#. Description of the plugin
#: motopress-hotel-booking.php
msgid "Manage your hotel booking services. Perfect for hotels, villas, guest houses, hostels, and apartments of all sizes."
msgstr "Zarządzaj usługami rezerwacji hoteli. Idealne rozwiązanie dla hoteli, pensjonatów, hostelów i apartamentów itd."

#. Author of the plugin
#: motopress-hotel-booking.php
msgid "MotoPress"
msgstr ""

#. Author URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/"
msgstr ""

#: functions.php:71
msgctxt "Post Status"
msgid "New"
msgstr "Nowy"

#: functions.php:74
msgctxt "Post Status"
msgid "Auto Draft"
msgstr "Automatyczny szkic"

#. translators: %s: URL to plugins.php page
#: functions.php:518
msgid "You are using two instances of Hotel Booking plugin at the same time, please <a href=\"%s\">deactivate one of them</a>."
msgstr "Używasz jednocześnie dwóch wystąpień wtyczki do rezerwacji hoteli, <a href=\"%s\">dezaktywuj jedną z nich</a>."

#: functions.php:535
msgid "<a href=\"%s\">Upgrade to Premium</a> to enable this feature."
msgstr "<a href=\"%s\">Wykup wersję Premium</a> by aktywować tę opcję."

#: includes/actions-handler.php:100
#: includes/admin/sync-logs-list-table.php:91
#: includes/csv/csv-export-handler.php:33
#: includes/csv/csv-export-handler.php:51
#: includes/payments/gateways/stripe-gateway.php:560
#: includes/payments/gateways/stripe-gateway.php:572
#: includes/payments/gateways/stripe-gateway.php:631
msgid "Error"
msgstr "Błąd"

#: includes/admin/customers-list-table.php:143
#: includes/admin/menu-pages/rooms-generator-menu-page.php:84
#: includes/admin/sync-rooms-list-table.php:146
#: includes/post-types/room-type-cpt.php:354
#: templates/account/bookings.php:80
msgid "View"
msgstr "Widok"

#: includes/admin/customers-list-table.php:147
#: includes/admin/fields/abstract-complex-field.php:25
#: includes/admin/fields/rules-list-field.php:61
#: includes/admin/sync-rooms-list-table.php:147
msgid "Delete"
msgstr "Usuń"

#: includes/admin/customers-list-table.php:212
#: includes/post-types/attributes-cpt.php:301
msgid "Name"
msgstr "Nazwa"

#: includes/admin/customers-list-table.php:213
#: includes/admin/menu-pages/customers-menu-page.php:207
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:122
#: includes/bundles/customer-bundle.php:110
#: includes/csv/bookings/bookings-exporter-helper.php:83
#: includes/post-types/booking-cpt.php:106
#: includes/post-types/payment-cpt.php:263
#: includes/views/shortcodes/checkout-view.php:618
#: templates/account/account-details.php:34
msgid "Email"
msgstr "E-mail"

#: includes/admin/customers-list-table.php:214
#: includes/admin/menus.php:72
#: includes/admin/menus.php:73
#: includes/post-types/booking-cpt.php:241
#: includes/shortcodes/account-shortcode.php:239
msgid "Bookings"
msgstr "Rezerwacje"

#: includes/admin/customers-list-table.php:215
msgid "Date Registered"
msgstr ""

#: includes/admin/customers-list-table.php:216
msgid "Last Active"
msgstr ""

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:16
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:33
msgid "Terms"
msgstr "Warunki"

#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:27
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:46
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:41
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:115
msgid "Created on:"
msgstr "Dodane:"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:88
msgid "Please add attribute in default language to configure terms."
msgstr "Dodaj atrybut w domyślnym języku, aby skonfigurować warunki."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:98
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:116
msgid "Configure terms"
msgstr "Konfiguruj terminy"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:20
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:107
#: includes/post-types/reserved-room-cpt.php:22
msgid "Reserved Accommodations"
msgstr "Zarezerwowane pokoje"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:21
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:66
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Update Booking"
msgstr "Aktualizuj rezerwację"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:22
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:12
msgid "Logs"
msgstr "Logi"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:54
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:125
msgid "Delete Permanently"
msgstr "Usuń bezpowrotnie"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:58
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:127
msgid "Move to Trash"
msgstr "Przenieś do kosza"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Create Booking"
msgstr "Utwórz rezerwację"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:85
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:98
msgid "Resend Email"
msgstr ""

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:100
msgid "Send a copy of the Approved Booking email to the customer`s email address."
msgstr ""

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:116
#: templates/edit-booking/edit-reserved-rooms.php:35
msgid "Edit Accommodations"
msgstr "Edytuj zakwaterowanie"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:125
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:85
#: includes/shortcodes/booking-confirmation-shortcode.php:298
msgid "Date:"
msgstr "Data:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:130
msgid "Author:"
msgstr "Autor:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:145
#: includes/payments/gateways/stripe-gateway.php:528
msgid "Auto"
msgstr "Automatyczny"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:155
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:89
msgid "Message:"
msgstr "Wiadomość:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:227
msgid "Confirmation email has been sent to customer."
msgstr ""

#: includes/admin/edit-cpt-pages/coupon-edit-cpt-page.php:14
msgid "Coupon code"
msgstr "Kod kuponu rabtowego"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:11
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:64
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Update Payment"
msgstr "Aktualizuj płatność"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:44
msgid "Modified on:"
msgstr "Modyfikowane:"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Create Payment"
msgstr "Utwórz płatność"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:47
msgid "Season Prices"
msgstr "Ceny sezonowe"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:48
msgid "<code>Please select Accommodation Type and click Create Rate button to continue.</code>"
msgstr "<code>Wybierz typ zakwaterowania i kliknij przycisk Utwórz cenę, aby kontynuować.</code>"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:66
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:65
#: includes/views/loop-room-type-view.php:113
#: includes/views/single-room-type-view.php:205
#: template-functions.php:920
#: templates/create-booking/results/reserve-rooms.php:51
#: templates/widgets/rooms/room-content.php:77
#: templates/widgets/search-availability/search-form.php:78
msgid "Adults:"
msgstr "Dorośli:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:66
#: includes/views/loop-room-type-view.php:128
#: includes/views/single-room-type-view.php:220
#: template-functions.php:925
#: templates/create-booking/results/reserve-rooms.php:52
#: templates/widgets/rooms/room-content.php:91
#: templates/widgets/search-availability/search-form.php:103
msgid "Children:"
msgstr "Dzieci:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:70
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:63
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:110
#: includes/shortcodes/booking-confirmation-shortcode.php:306
#: includes/shortcodes/search-results-shortcode.php:770
#: includes/shortcodes/search-results-shortcode.php:921
#: templates/shortcodes/booking-details/booking-details.php:33
msgid "Total:"
msgstr "Razem:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:80
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Update Rate"
msgstr "Zaktualizuj stawkę"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:97
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
msgid "Active"
msgstr "Aktywny"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:98
#: includes/admin/groups/license-settings-group.php:61
msgid "Disabled"
msgstr "Wyłączony"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Create Rate"
msgstr "Dodaj stawkę"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:190
msgid "Duplicate Rate"
msgstr "Duplikuj stawkę"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:12
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:36
#: includes/admin/menu-pages/booking-rules-menu-page.php:219
#: includes/admin/menu-pages/booking-rules-menu-page.php:264
#: includes/admin/menu-pages/booking-rules-menu-page.php:310
#: includes/admin/menu-pages/booking-rules-menu-page.php:356
#: includes/admin/menu-pages/booking-rules-menu-page.php:487
#: includes/admin/menu-pages/booking-rules-menu-page.php:533
#: includes/admin/menu-pages/booking-rules-menu-page.php:579
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:208
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:304
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:377
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:448
#: includes/post-types/room-cpt.php:31
#: includes/post-types/room-cpt.php:41
#: includes/wizard.php:103
msgid "Accommodations"
msgstr "Pokoje"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:15
#: includes/post-types/attributes-cpt.php:54
#: includes/post-types/attributes-cpt.php:61
#: includes/post-types/attributes-cpt.php:65
msgid "Attributes"
msgstr "Atrybuty"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:18
msgid "Accommodation Reviews"
msgstr "Recenzje zakwaterowania"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:27
msgid "Allow guests to <a href=\"%s\" target=\"_blank\">submit star ratings and reviews</a> evaluating your accommodations."
msgstr "Pozwól gościom na <a href=\"%s\" target=\"_blank\">dodanie ocen z gwiazdką oraz opinii</a> o twoich pokojach."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:69
msgid "Number of Accommodations:"
msgstr "Ilość noclegów:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:74
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:29
msgid "Count of real accommodations of this type in your hotel."
msgstr "Rzeczywista ilość pokojów tego typu w hotelu."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:122
msgid "Total Accommodations:"
msgstr "Suma pokoi:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:159
msgid "Show Accommodations"
msgstr "Pokaż pokoje"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:139
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:18
#: includes/admin/menu-pages/rooms-generator-menu-page.php:146
#: includes/admin/menu-pages/rooms-generator-menu-page.php:150
msgid "Generate Accommodations"
msgstr "Generowanie noclegów"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:147
msgid "Active Accommodations:"
msgstr "Aktywne pokoje:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:168
#: includes/post-types/room-cpt.php:93
msgid "Linked Accommodations"
msgstr ""

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:173
msgid "Link accommodations on the Edit Accommodation page to ensure bookings for one make any linked properties unavailable for the same dates."
msgstr ""

#: includes/admin/fields/abstract-complex-field.php:24
#: includes/admin/fields/rules-list-field.php:57
#: templates/edit-booking/add-room-popup.php:45
msgid "Add"
msgstr "Dodaj"

#: includes/admin/fields/amount-field.php:74
msgid "Per adult:"
msgstr "Na osobę dorosłą:"

#: includes/admin/fields/amount-field.php:77
msgid "Per child:"
msgstr "Na dziecko:"

#: includes/admin/fields/amount-field.php:198
msgid "Per adult: "
msgstr "Na osobę dorosłą: "

#: includes/admin/fields/amount-field.php:200
msgid "Per child: "
msgstr "Na dziecko: "

#: includes/admin/fields/complex-horizontal-field.php:71
#: includes/admin/fields/rules-list-field.php:62
#: templates/account/bookings.php:22
#: templates/account/bookings.php:79
#: templates/edit-booking/edit-reserved-rooms.php:47
msgid "Actions"
msgstr "Akcje"

#: includes/admin/fields/complex-horizontal-field.php:111
msgid "Move up"
msgstr ""

#: includes/admin/fields/complex-horizontal-field.php:112
msgid "Move down"
msgstr ""

#: includes/admin/fields/complex-horizontal-field.php:113
msgid "Move to top"
msgstr ""

#: includes/admin/fields/complex-horizontal-field.php:114
msgid "Move to bottom"
msgstr ""

#: includes/admin/fields/complex-vertical-field.php:17
#: includes/admin/menu-pages/settings-menu-page.php:580
#: includes/settings/main-settings.php:25
#: includes/settings/main-settings.php:43
msgid "Default"
msgstr "Domyślny"

#: includes/admin/fields/dynamic-select-field.php:61
#: includes/admin/fields/page-select-field.php:16
#: includes/admin/menu-pages/customers-menu-page.php:260
#: includes/admin/menu-pages/rooms-generator-menu-page.php:38
#: includes/admin/menu-pages/settings-menu-page.php:420
#: includes/post-types/booking-cpt.php:122
#: includes/post-types/booking-cpt.php:179
#: includes/post-types/payment-cpt.php:231
#: includes/post-types/rate-cpt.php:31
#: includes/post-types/room-cpt.php:79
#: includes/views/shortcodes/checkout-view.php:250
#: includes/views/shortcodes/checkout-view.php:273
#: templates/account/account-details.php:51
#: templates/edit-booking/add-room-popup.php:30
#: templates/edit-booking/add-room-popup.php:38
msgid "— Select —"
msgstr "— Wybierz —"

#: includes/admin/fields/install-plugin-field.php:33
msgid "Install & Activate"
msgstr "Zainstaluj & Aktywuj"

#: includes/admin/fields/media-field.php:76
msgid "Add image"
msgstr ""

#: includes/admin/fields/media-field.php:76
msgid "Add gallery"
msgstr "Dodaj galerię"

#: includes/admin/fields/media-field.php:77
msgid "Remove image"
msgstr ""

#: includes/admin/fields/media-field.php:77
msgid "Remove gallery"
msgstr "Usuń galerię"

#: includes/admin/fields/multiple-checkbox-field.php:88
#: template-functions.php:1088
msgid "Select all"
msgstr "Zaznacz wszystko"

#: includes/admin/fields/multiple-checkbox-field.php:92
#: template-functions.php:1090
msgid "Unselect all"
msgstr "Odznacz wszystko"

#: includes/admin/fields/notes-list-field.php:23
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:68
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:33
#: includes/csv/bookings/bookings-exporter-helper.php:112
#: assets/blocks/blocks.js:593
#: assets/blocks/blocks.js:929
#: assets/blocks/blocks.js:1113
msgid "Date"
msgstr "Data"

#: includes/admin/fields/notes-list-field.php:33
msgid "Author"
msgstr "Autor"

#: includes/admin/fields/rules-list-field.php:59
#: includes/admin/room-list-table.php:154
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:115
#: includes/bookings-calendar.php:613
#: includes/script-managers/admin-script-manager.php:97
msgid "Edit"
msgstr "Edytuj"

#: includes/admin/fields/rules-list-field.php:60
#: includes/admin/sync-rooms-list-table.php:81
#: includes/ajax.php:951
#: includes/script-managers/admin-script-manager.php:98
msgid "Done"
msgstr "Gotowe"

#: includes/admin/fields/rules-list-field.php:64
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:85
#: includes/admin/menu-pages/booking-rules-menu-page.php:180
#: includes/admin/menu-pages/booking-rules-menu-page.php:183
#: includes/admin/menu-pages/booking-rules-menu-page.php:407
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:211
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:307
#: includes/script-managers/admin-script-manager.php:95
msgid "All"
msgstr "Wszystkie"

#: includes/admin/fields/rules-list-field.php:65
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:83
#: includes/post-types/coupon-cpt.php:81
#: includes/post-types/coupon-cpt.php:113
#: includes/post-types/coupon-cpt.php:158
#: includes/script-managers/admin-script-manager.php:96
msgid "None"
msgstr "Żadne"

#: includes/admin/fields/time-picker-field.php:13
msgid "HH:MM"
msgstr "HH:MM"

#: includes/admin/fields/total-price-field.php:18
msgid "Recalculate Total Price"
msgstr "Ponowne obliczanie ceny łącznej"

#: includes/admin/fields/variable-pricing-field.php:89
#: includes/views/booking-view.php:121
msgid "Nights"
msgstr "Noce"

#: includes/admin/fields/variable-pricing-field.php:97
#: includes/script-managers/admin-script-manager.php:102
msgid "and more"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:98
#: includes/admin/menu-pages/edit-booking/edit-control.php:95
#: includes/script-managers/admin-script-manager.php:101
#: includes/shortcodes/search-results-shortcode.php:1009
#: includes/views/booking-view.php:400
#: templates/edit-booking/edit-reserved-rooms.php:70
msgid "Remove"
msgstr "Usuń"

#: includes/admin/fields/variable-pricing-field.php:104
msgid "Add length of stay"
msgstr "Dodaj długość pobytu"

#: includes/admin/fields/variable-pricing-field.php:109
msgid "Base Occupancy"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:110
#: includes/admin/fields/variable-pricing-field.php:170
msgid "Price per night"
msgstr "Cena za noc"

#: includes/admin/fields/variable-pricing-field.php:118
#: includes/admin/fields/variable-pricing-field.php:167
#: includes/emails/templaters/reserved-rooms-templater.php:175
#: includes/post-types/room-type-cpt.php:283
#: includes/views/booking-view.php:105
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:242
#: template-functions.php:765
#: templates/create-booking/search/search-form.php:94
#: templates/shortcodes/search/search-form.php:80
#: assets/blocks/blocks.js:147
msgid "Adults"
msgstr "Dorośli"

#: includes/admin/fields/variable-pricing-field.php:122
#: includes/csv/bookings/bookings-exporter-helper.php:80
#: includes/emails/templaters/reserved-rooms-templater.php:179
#: includes/post-types/room-type-cpt.php:292
#: includes/views/booking-view.php:116
#: templates/create-booking/search/search-form.php:109
#: templates/shortcodes/search/search-form.php:103
#: assets/blocks/blocks.js:162
msgid "Children"
msgstr "Dzieci"

#: includes/admin/fields/variable-pricing-field.php:130
msgid "Price per extra adult"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:137
msgid "Price per extra child"
msgstr ""

#: includes/admin/fields/variable-pricing-field.php:154
msgid "Enable variable pricing"
msgstr "Włącz zmienne ceny"

#: includes/admin/fields/variable-pricing-field.php:188
msgid "Add Variation"
msgstr "Dodaj odmianę"

#: includes/admin/fields/variable-pricing-field.php:215
#: includes/admin/fields/variable-pricing-field.php:231
msgid "Remove variation"
msgstr "Usuń odmianę"

#: includes/admin/groups/license-settings-group.php:21
msgid "The License Key is required in order to get automatic plugin updates and support. You can manage your License Key in your personal account. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Learn more</a>."
msgstr "Klucz licencyjny jest wymagany do automatycznego otrzymania aktualizacji wtyczek i wsparcia. Można zarządzać kluczem licencyjnym w koncie osobistym. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Więcej info</a>."

#: includes/admin/groups/license-settings-group.php:28
msgid "License Key"
msgstr "Klucz licencyjny"

#: includes/admin/groups/license-settings-group.php:42
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:62
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:22
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:28
#: includes/admin/menu-pages/create-booking/checkout-step.php:63
#: includes/admin/sync-logs-list-table.php:72
#: includes/admin/sync-rooms-list-table.php:127
#: includes/csv/bookings/bookings-exporter-helper.php:72
#: template-functions.php:977
#: templates/edit-booking/edit-reserved-rooms.php:46
msgid "Status"
msgstr "Status"

#: includes/admin/groups/license-settings-group.php:49
msgid "Inactive"
msgstr "Nieaktywny"

#: includes/admin/groups/license-settings-group.php:55
msgid "Valid until"
msgstr "Ważny do"

#: includes/admin/groups/license-settings-group.php:57
msgid "Valid (Lifetime)"
msgstr "Ważny (Dożywotni)"

#: includes/admin/groups/license-settings-group.php:64
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:123
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:85
msgid "Expired"
msgstr "Wygasł"

#: includes/admin/groups/license-settings-group.php:67
msgid "Invalid"
msgstr "Nieprawidłowy"

#: includes/admin/groups/license-settings-group.php:71
msgid "Your License Key does not match the installed plugin. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>How to fix this.</a>"
msgstr "Twój klucz licencyjny nie odpowiada zainstalowanej wtyczcę. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>Jak usunąć ten problem.</a>"

#: includes/admin/groups/license-settings-group.php:74
msgid "Product ID is not valid"
msgstr "Nieprawidłowy identyfikator produktu"

#: includes/admin/groups/license-settings-group.php:83
msgid "Action"
msgstr "Akcja"

#: includes/admin/groups/license-settings-group.php:90
msgid "Activate License"
msgstr "Aktywuj licencję"

#: includes/admin/groups/license-settings-group.php:96
msgid "Deactivate License"
msgstr "Zdeaktywuj licencję"

#: includes/admin/groups/license-settings-group.php:103
msgid "Renew License"
msgstr "Odnowić licencję"

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:9
msgid "Attributes let you define extra accommodation data, such as location or type. You can use these attributes in the search availability form as advanced search filters."
msgstr "Atrybuty pozwalają zdefiniować dodatkowe dane dotyczące zakwaterowania, takie jak lokalizacja lub typ. Możesz użyć tych atrybutów w formularzu wyszukiwania dostępności jako filtrów wyszukiwania zaawansowanego."

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:72
msgid "This attribute refers to non-unique taxonomy - %1$s - which was already registered with attribute %2$s."
msgstr "Ten atrybut odnosi się do nieunikalnej taksonomii - %1$s - która została już zarejestrowana z atrybutem %2$s."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:92
msgid "You cannot manage terms of trashed attributes."
msgstr "Nie możesz zarządzać terminami atrybutów w koszu."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:17
#: includes/admin/menu-pages/calendar-menu-page.php:31
#: includes/post-types/booking-cpt.php:246
msgid "New Booking"
msgstr "Nowa rezerwacja"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:61
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:26
#: includes/admin/menu-pages/shortcodes-menu-page.php:376
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:64
#: includes/csv/bookings/bookings-exporter-helper.php:71
#: includes/post-types/booking-cpt.php:42
#: includes/post-types/payment-cpt.php:150
msgid "ID"
msgstr "IDENTYFIKATOR"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:63
msgid "Check-in / Check-out"
msgstr ""

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:64
#: includes/views/booking-view.php:107
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:244
#: template-functions.php:767
#: templates/shortcodes/search/search-form.php:82
msgid "Guests"
msgstr "Goście"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:65
#: templates/emails/admin-customer-cancelled-booking.php:23
#: templates/emails/admin-customer-confirmed-booking.php:23
#: templates/emails/admin-payment-confirmed-booking.php:30
#: templates/emails/admin-pending-booking.php:23
msgid "Customer Info"
msgstr "Dane klienta"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:66
#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:16
#: includes/post-types/rate-cpt.php:64
#: includes/post-types/service-cpt.php:132
#: includes/post-types/service-cpt.php:137
#: includes/views/single-service-view.php:18
#: includes/widgets/rooms-widget.php:201
#: assets/blocks/blocks.js:496
#: assets/blocks/blocks.js:547
#: assets/blocks/blocks.js:746
#: assets/blocks/blocks.js:883
#: assets/blocks/blocks.js:1067
#: assets/blocks/blocks.js:1275
msgid "Price"
msgstr "Cena"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:67
#: includes/admin/menu-pages/booking-rules-menu-page.php:402
#: includes/admin/room-list-table.php:93
#: includes/admin/sync-rooms-list-table.php:126
#: includes/bookings-calendar.php:829
#: includes/bookings-calendar.php:847
#: includes/csv/bookings/bookings-exporter-helper.php:77
#: includes/post-types/room-cpt.php:32
#: includes/post-types/room-cpt.php:74
#: includes/post-types/room-type-cpt.php:60
#: templates/edit-booking/add-room-popup.php:36
#: templates/edit-booking/edit-reserved-rooms.php:45
msgid "Accommodation"
msgstr "Obiekt"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:121
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:83
msgid "Expire %s"
msgstr "Wygasa %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:138
#: includes/script-managers/admin-script-manager.php:99
msgid "Adults: "
msgstr "Dorośli: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:142
#: includes/script-managers/admin-script-manager.php:100
msgid "Children: "
msgstr "Dzieci: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:184
msgid "%s night"
msgid_plural "%s nights"
msgstr[0] "%s noc"
msgstr[1] "%s nocy"
msgstr[2] "%s nocy"
msgstr[3] ""

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:197
#: includes/bookings-calendar.php:1189
msgid "Summary: %s."
msgstr "Podsumowanie: %s."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:280
msgid "Paid: %s"
msgstr "Opłacono: %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:345
msgid "Set to %s"
msgstr "Ustaw na %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:460
msgid "Booking status changed."
msgid_plural "%s booking statuses changed."
msgstr[0] "Rezerwacja zmieniła swój status."
msgstr[1] "%s rezerwacji zmieniły swój status."
msgstr[2] "%s rezerwacji zmieniły swój status."
msgstr[3] ""

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:506
msgid "All accommodation types"
msgstr "Wszystkie typy zakwaterowania"

#. translators: The number of imported bookings: "Imported <span>(11)</span>"
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:526
msgid "Imported %s"
msgstr "Zaimportowano %s"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:19
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:29
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:168
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:264
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:349
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:420
#: includes/post-types/coupon-cpt.php:93
#: includes/post-types/coupon-cpt.php:124
#: includes/post-types/coupon-cpt.php:169
#: includes/post-types/payment-cpt.php:182
#: includes/views/booking-view.php:126
#: includes/views/booking-view.php:172
#: includes/views/booking-view.php:208
#: includes/views/booking-view.php:266
#: includes/views/booking-view.php:297
#: includes/views/booking-view.php:350
#: template-functions.php:978
msgid "Amount"
msgstr "Kwota"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:20
msgid "Uses"
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:21
#: includes/post-types/coupon-cpt.php:187
msgid "Expiration Date"
msgstr "Termin ważności"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:41
#: includes/views/edit-booking/checkout-view.php:115
#: template-functions.php:900
msgid "Accommodation:"
msgstr "Pokoje:"

#. translators: %s is a coupon amount per day
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:60
msgid "%s per day"
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:67
msgid "Service:"
msgstr "Usługa:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:88
msgid "Fee:"
msgstr "Opłata:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:153
msgid "Note: the use of coupons is disabled in settings."
msgstr ""

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:165
#: includes/admin/menu-pages/settings-menu-page.php:299
msgid "Enable the use of coupons."
msgstr "Włącz używanie kuponów rabatowych."

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:27
#: includes/admin/menu-pages/customers-menu-page.php:299
msgid "Customer"
msgstr "Klient"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:30
#: includes/post-types/booking-cpt.php:242
#: templates/account/bookings.php:18
#: templates/account/bookings.php:66
msgid "Booking"
msgstr "Rezerwacja"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:31
#: includes/post-types/payment-cpt.php:159
msgid "Gateway"
msgstr "Bramka płatnicza"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:32
#: includes/post-types/payment-cpt.php:223
msgid "Transaction ID"
msgstr "ID/numer transakcji"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:105
#: includes/admin/menu-pages/create-booking/booking-step.php:66
#: includes/bookings-calendar.php:606
#: includes/script-managers/admin-script-manager.php:103
msgid "Booking #%s"
msgstr "Rezerwacja #%s"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:13
msgid "Rates are used to offer different prices of the same accommodation type depending on extra conditions, e.g. With Breakfast, With No Breakfast, Refundable etc. Guests will choose the preferable rate when submitting a booking request. Create one default rate if you have no price tiers. To add price variations for different periods - open a rate, add a season, and set the price."
msgstr ""

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:23
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:32
#: includes/admin/menu-pages/booking-rules-menu-page.php:393
#: includes/admin/menu-pages/rooms-generator-menu-page.php:34
#: includes/csv/bookings/bookings-exporter-helper.php:75
#: includes/post-types/rate-cpt.php:30
#: includes/post-types/room-cpt.php:84
#: includes/post-types/room-type-cpt.php:54
#: templates/create-booking/search/search-form.php:82
#: templates/edit-booking/add-room-popup.php:28
#: templates/edit-booking/edit-reserved-rooms.php:44
#: assets/blocks/blocks.js:282
#: assets/blocks/blocks.js:1202
#: assets/blocks/blocks.js:1424
#: assets/blocks/blocks.js:1506
msgid "Accommodation Type"
msgstr "Typ pokoju"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:24
msgid "Season &#8212; Price"
msgstr "Cena &#8212; sezonowa"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:67
#: includes/post-types/rate-cpt.php:73
msgid "Add New Season Price"
msgstr "Dodaj nową cenę sezonową"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:104
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:94
#: includes/post-types/season-cpt.php:71
msgid "Annually"
msgstr ""

#. translators: %s: A date string such as "December 31, 2025".
#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:108
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:98
msgid "Annually until %s"
msgstr ""

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:201
msgid "Duplicate"
msgstr "Duplikuj"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:281
msgid "Rate was duplicated."
msgstr "Ocena została zduplikowana."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:13
msgid "These are real accommodations like rooms, apartments, houses, villas, beds (for hostels) etc."
msgstr "Są to prawdziwe obiekty noclegowe, takie jak pokoje, apartamenty, domy, wille, łóżka (dla hostelów) itp."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:77
#: includes/admin/menu-pages/reports-menu-page.php:129
#: includes/bookings-calendar.php:746
msgid "All Accommodation Types"
msgstr "Wszystkie typy pokojów"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:11
msgid "These are not physical accommodations, but their types. E.g. standard double room. To specify the real number of existing accommodations, you'll need to use Generate Accommodations menu."
msgstr "To nie są noclegi fizyczne, ale ich typy. Na przykład. Standardowy pokój dwuosobowy. Aby określić rzeczywistą liczbę istniejących pomieszczeń, musisz użyć menu Generowanie zakwaterowania."

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:34
#: includes/post-types/room-type-cpt.php:275
#: includes/post-types/room-type-cpt.php:302
#: templates/create-booking/results/reserve-rooms.php:36
msgid "Capacity"
msgstr "Pojemność"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:35
msgid "Bed Type"
msgstr "Typ łóżka"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:68
#: includes/views/loop-room-type-view.php:152
#: includes/views/single-room-type-view.php:244
#: templates/widgets/rooms/room-content.php:167
msgid "Size:"
msgstr "Rozmiar:"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:114
msgid "Active:"
msgstr "Aktywny:"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:13
msgid "Seasons are real periods of time, dates or days that come with different prices for accommodations. E.g. Winter 2018 ($120 per night), Christmas ($150 per night)."
msgstr "Pory roku to okresy czasu, daty i dni, które mają różne ceny zakwaterowania. Na przykład. Zima 2018 (120 USD za noc), Boże Narodzenie (150 USD za noc)."

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:20
msgid "Start"
msgstr "Początek"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:21
msgid "End"
msgstr "Koniec"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:22
#: includes/admin/menu-pages/booking-rules-menu-page.php:210
#: includes/admin/menu-pages/booking-rules-menu-page.php:255
msgid "Days"
msgstr "Dni"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:23
#: includes/post-types/season-cpt.php:67
msgid "Repeat"
msgstr "Powtórz"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:11
msgid "Services are extra offers that you can sell or give for free. E.g. Thai massage, transfer, babysitting. Guests can pre-order them when placing a booking."
msgstr "Usługi to dodatkowe oferty, które można sprzedawać lub oferować za darmo. Na przykład. masaż tajlandzki, transfer, opieka nad dziećmi. Goście mogą wcześniej zapoznać się z nimi podczas dokonywania rezerwacji."

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:17
#: includes/post-types/service-cpt.php:150
msgid "Periodicity"
msgstr "Periodyczność"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:18
#: includes/post-types/service-cpt.php:206
msgid "Charge"
msgstr "Opłata"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:41
#: includes/entities/service.php:193
#: includes/post-types/service-cpt.php:153
msgid "Per Day"
msgstr "Za dzień"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:43
#: includes/post-types/service-cpt.php:154
msgid "Guest Choice"
msgstr "Wybór gości"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:45
#: includes/entities/service.php:197
#: includes/post-types/service-cpt.php:152
msgid "Once"
msgstr "Jednorazowo"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:203
#: includes/post-types/service-cpt.php:209
msgid "Per Guest"
msgstr "Na gościa"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:205
#: includes/post-types/service-cpt.php:208
msgid "Per Accommodation"
msgstr "Za pokój"

#: includes/admin/manage-tax-pages/facility-manage-tax-page.php:11
msgid "These are accommodation amenities, generally free ones. E.g. air-conditioning, wifi."
msgstr "Są to udogodnienia, zazwyczaj darmowe. Na przykład. klimatyzacja, wifi."

#: includes/admin/menu-pages/booking-rules-menu-page.php:34
msgid "Booking rules saved."
msgstr "Reguły rezerwacji zostały zapisane."

#: includes/admin/menu-pages/booking-rules-menu-page.php:41
#: includes/admin/menu-pages/booking-rules-menu-page.php:602
#: includes/admin/menu-pages/booking-rules-menu-page.php:606
#: includes/admin/menu-pages/settings-menu-page.php:622
msgid "Booking Rules"
msgstr "Zasady rezerwacji"

#: includes/admin/menu-pages/booking-rules-menu-page.php:90
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:70
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:118
#: templates/account/account-details.php:94
msgid "Save Changes"
msgstr "Zapisz zmiany"

#: includes/admin/menu-pages/booking-rules-menu-page.php:199
msgid "Check-in days"
msgstr "Dni zameldowania"

#: includes/admin/menu-pages/booking-rules-menu-page.php:200
msgid "Guests can check in any day."
msgstr "Goście mogą zameldować się każdego dnia."

#: includes/admin/menu-pages/booking-rules-menu-page.php:201
#: includes/admin/menu-pages/booking-rules-menu-page.php:246
#: includes/admin/menu-pages/booking-rules-menu-page.php:291
#: includes/admin/menu-pages/booking-rules-menu-page.php:337
#: includes/admin/menu-pages/booking-rules-menu-page.php:383
#: includes/admin/menu-pages/booking-rules-menu-page.php:468
#: includes/admin/menu-pages/booking-rules-menu-page.php:514
#: includes/admin/menu-pages/booking-rules-menu-page.php:560
msgid "Add rule"
msgstr "Dodaj regułę"

#: includes/admin/menu-pages/booking-rules-menu-page.php:229
#: includes/admin/menu-pages/booking-rules-menu-page.php:274
#: includes/admin/menu-pages/booking-rules-menu-page.php:320
#: includes/admin/menu-pages/booking-rules-menu-page.php:366
#: includes/admin/menu-pages/booking-rules-menu-page.php:497
#: includes/admin/menu-pages/booking-rules-menu-page.php:543
#: includes/admin/menu-pages/booking-rules-menu-page.php:589
#: includes/post-types/season-cpt.php:98
#: includes/post-types/season-cpt.php:108
msgid "Seasons"
msgstr "Sezony"

#: includes/admin/menu-pages/booking-rules-menu-page.php:244
msgid "Check-out days"
msgstr "Dni wymeldowania"

#: includes/admin/menu-pages/booking-rules-menu-page.php:245
msgid "Guests can check out any day."
msgstr "Goście mogą wymeldować się każdego dnia."

#: includes/admin/menu-pages/booking-rules-menu-page.php:289
#: includes/admin/menu-pages/booking-rules-menu-page.php:300
msgid "Minimum stay"
msgstr "Minimalny pobyt"

#: includes/admin/menu-pages/booking-rules-menu-page.php:290
msgid "There are no minimum stay rules."
msgstr "Nie ma zasad minimalnego pobytu."

#: includes/admin/menu-pages/booking-rules-menu-page.php:301
#: includes/admin/menu-pages/booking-rules-menu-page.php:347
#: includes/admin/menu-pages/booking-rules-menu-page.php:478
#: includes/admin/menu-pages/booking-rules-menu-page.php:524
#: includes/admin/menu-pages/booking-rules-menu-page.php:570
msgid "nights"
msgstr "noce"

#: includes/admin/menu-pages/booking-rules-menu-page.php:335
#: includes/admin/menu-pages/booking-rules-menu-page.php:346
msgid "Maximum stay"
msgstr "Maksymalny pobyt"

#: includes/admin/menu-pages/booking-rules-menu-page.php:336
msgid "There are no maximum stay rules."
msgstr "Nie ma maksymalnych reguł pobytu."

#: includes/admin/menu-pages/booking-rules-menu-page.php:381
msgid "Block accommodation"
msgstr "Zablokuj zakwaterowanie"

#: includes/admin/menu-pages/booking-rules-menu-page.php:382
msgid "There are no blocking accommodation rules."
msgstr "Nie ma blokujących reguł zakwaterowania."

#: includes/admin/menu-pages/booking-rules-menu-page.php:414
#: includes/bookings-calendar.php:723
#: includes/bookings-calendar.php:817
msgid "From"
msgstr "Od"

#: includes/admin/menu-pages/booking-rules-menu-page.php:424
msgid "Till"
msgstr "Do"

#: includes/admin/menu-pages/booking-rules-menu-page.php:434
msgid "Restriction"
msgstr "Ograniczenie"

#: includes/admin/menu-pages/booking-rules-menu-page.php:436
msgid "Not check-in rule marks the date as unavailable for check-in."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:437
msgid "Not check-out rule marks the date as unavailable for check-out."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:438
msgid "Not stay-in rule displays the date as blocked. This date is unavailable for check-in and check-out on the next date."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:439
msgid "Not stay-in with Not check-out rules completely block the selected date, additionally displaying the previous date as unavailable for check-in."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:444
#: includes/script-managers/public-script-manager.php:208
msgid "Not check-in"
msgstr "Nie zameldowane"

#: includes/admin/menu-pages/booking-rules-menu-page.php:445
#: includes/script-managers/public-script-manager.php:209
msgid "Not check-out"
msgstr "Nie wymeldowane"

#: includes/admin/menu-pages/booking-rules-menu-page.php:446
#: includes/script-managers/public-script-manager.php:207
msgid "Not stay-in"
msgstr "Nie zatrzymywać się"

#: includes/admin/menu-pages/booking-rules-menu-page.php:454
msgid "Comment"
msgstr "Komentarz"

#: includes/admin/menu-pages/booking-rules-menu-page.php:466
#: includes/admin/menu-pages/booking-rules-menu-page.php:477
msgid "Minimum advance reservation"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:467
msgid "There are no minimum advance reservation rules."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:512
#: includes/admin/menu-pages/booking-rules-menu-page.php:523
msgid "Maximum advance reservation"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:513
msgid "There are no maximum advance reservation rules."
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:558
#: includes/admin/menu-pages/booking-rules-menu-page.php:569
msgid "Booking buffer"
msgstr ""

#: includes/admin/menu-pages/booking-rules-menu-page.php:559
msgid "There are no booking buffer rules."
msgstr ""

#: includes/admin/menu-pages/calendar-menu-page.php:41
#: includes/admin/menu-pages/calendar-menu-page.php:69
msgid "Booking Calendar"
msgstr "Kalendarz rezerwacji"

#: includes/admin/menu-pages/calendar-menu-page.php:65
msgid "Calendar"
msgstr "Kalendarz"

#: includes/admin/menu-pages/create-booking-menu-page.php:135
#: includes/admin/menu-pages/create-booking-menu-page.php:169
#: includes/post-types/booking-cpt.php:244
msgid "Add New Booking"
msgstr "Dodaj nową rezerwację"

#: includes/admin/menu-pages/create-booking-menu-page.php:136
msgid "Clear Search Results"
msgstr "Wyczyść wyniki wyszukiwania"

#: includes/admin/menu-pages/create-booking-menu-page.php:184
#: includes/admin/menu-pages/edit-booking-menu-page.php:69
msgid "Note: booking rules are disabled in the plugin settings and are not taken into account."
msgstr ""

#: includes/admin/menu-pages/create-booking/booking-step.php:50
#: includes/shortcodes/checkout-shortcode/step-booking.php:478
msgid "Unable to create booking. Please try again."
msgstr "Nie udało się utworzyć rezerwacji. Spróbuj ponownie."

#: includes/admin/menu-pages/create-booking/booking-step.php:74
#: includes/shortcodes/checkout-shortcode/step-booking.php:107
msgid "Booking is blocked due to maintenance reason. Please try again later."
msgstr "Rezerwacja jest blokowana z powodów technicznych. Spróbuj ponownie później."

#: includes/admin/menu-pages/create-booking/booking-step.php:121
#: includes/admin/menu-pages/create-booking/booking-step.php:275
#: includes/admin/menu-pages/create-booking/checkout-step.php:130
#: includes/admin/menu-pages/edit-booking/booking-control.php:34
#: includes/admin/menu-pages/edit-booking/checkout-control.php:61
#: includes/admin/menu-pages/edit-booking/summary-control.php:56
#: includes/shortcodes/checkout-shortcode/step-booking.php:183
#: includes/shortcodes/checkout-shortcode/step-booking.php:363
#: includes/shortcodes/checkout-shortcode/step-checkout.php:170
#: includes/utils/parse-utils.php:250
msgid "There are no accommodations selected for reservation."
msgstr "Nie ma pokojów wybranych do rezerwacji."

#: includes/admin/menu-pages/create-booking/booking-step.php:123
#: includes/admin/menu-pages/create-booking/booking-step.php:155
#: includes/admin/menu-pages/create-booking/checkout-step.php:132
#: includes/admin/menu-pages/create-booking/checkout-step.php:165
#: includes/admin/menu-pages/create-booking/checkout-step.php:196
#: includes/utils/parse-utils.php:210
#: includes/utils/parse-utils.php:285
#: includes/utils/parse-utils.php:305
msgid "Selected accommodations are not valid."
msgstr "Wybrane zakwaterowanie są nieprawidłowe."

#: includes/admin/menu-pages/create-booking/booking-step.php:150
#: includes/admin/menu-pages/create-booking/checkout-step.php:160
#: includes/admin/menu-pages/create-booking/step.php:191
#: includes/ajax.php:612
#: includes/shortcodes/checkout-shortcode/step-booking.php:200
#: includes/shortcodes/checkout-shortcode/step-booking.php:207
#: includes/shortcodes/checkout-shortcode/step-checkout.php:187
#: includes/shortcodes/checkout-shortcode/step-checkout.php:199
#: includes/utils/parse-utils.php:301
msgid "Accommodation Type is not valid."
msgstr "Nieprawidłowy typ pokoju."

#: includes/admin/menu-pages/create-booking/booking-step.php:160
#: includes/admin/menu-pages/create-booking/booking-step.php:184
#: includes/ajax.php:623
#: includes/shortcodes/checkout-shortcode/step-booking.php:213
#: includes/shortcodes/checkout-shortcode/step-booking.php:231
#: includes/utils/parse-utils.php:322
msgid "Rate is not valid."
msgstr "Nieprawidłowa ocena."

#: includes/admin/menu-pages/create-booking/booking-step.php:189
#: includes/admin/menu-pages/create-booking/step.php:211
#: includes/admin/menu-pages/create-booking/step.php:215
#: includes/shortcodes/checkout-shortcode/step-booking.php:237
#: includes/shortcodes/search-results-shortcode.php:634
#: includes/utils/parse-utils.php:163
#: includes/utils/parse-utils.php:326
msgid "Adults number is not valid."
msgstr "Nieprawidłowa ilość dorosłych."

#: includes/admin/menu-pages/create-booking/booking-step.php:194
#: includes/admin/menu-pages/create-booking/step.php:235
#: includes/admin/menu-pages/create-booking/step.php:239
#: includes/ajax.php:500
#: includes/shortcodes/checkout-shortcode/step-booking.php:243
#: includes/shortcodes/search-results-shortcode.php:650
#: includes/utils/parse-utils.php:187
#: includes/utils/parse-utils.php:330
msgid "Children number is not valid."
msgstr "Nieprawidłowa ilość dzieci."

#: includes/admin/menu-pages/create-booking/booking-step.php:199
#: includes/ajax.php:634
#: includes/shortcodes/checkout-shortcode/step-booking.php:248
#: includes/utils/parse-utils.php:334
msgid "The total number of guests is not valid."
msgstr "Całkowita liczba gości jest nieprawidłowa."

#: includes/admin/menu-pages/create-booking/booking-step.php:210
#: includes/admin/menu-pages/create-booking/checkout-step.php:181
#: includes/shortcodes/checkout-shortcode/step-booking.php:259
#: includes/shortcodes/checkout-shortcode/step-checkout.php:245
#: includes/utils/parse-utils.php:345
msgid "Selected dates do not meet booking rules for type %s"
msgstr "Wybrane daty nie spełniają reguł rezerwacji dla typu %s"

#: includes/admin/menu-pages/create-booking/booking-step.php:263
#: includes/admin/menu-pages/create-booking/checkout-step.php:186
#: includes/utils/parse-utils.php:264
msgid "Accommodations are not available."
msgstr "Udogodnienia nie są dostępne."

#: includes/admin/menu-pages/create-booking/checkout-step.php:170
#: includes/shortcodes/checkout-shortcode/step-checkout.php:234
msgid "There are no rates for requested dates."
msgstr "Nie ma ocen za żądane daty."

#: includes/admin/menu-pages/create-booking/results-step.php:211
#: includes/admin/menu-pages/settings-menu-page.php:542
#: includes/wizard.php:93
msgid "Search Results"
msgstr "Rezułtaty wyszukiwania"

#: includes/admin/menu-pages/create-booking/search-step.php:47
#: includes/admin/menu-pages/create-booking/search-step.php:50
msgid "— Any —"
msgstr "— Jakikolwiek —"

#: includes/admin/menu-pages/create-booking/step.php:34
msgid "Search parameters are not set."
msgstr "Parametry wyszukiwania nie są ustawione."

#: includes/admin/menu-pages/create-booking/step.php:129
#: includes/ajax.php:438
#: includes/script-managers/public-script-manager.php:223
#: includes/shortcodes/checkout-shortcode/step.php:53
#: includes/shortcodes/search-results-shortcode.php:665
#: includes/utils/parse-utils.php:87
msgid "Check-in date is not valid."
msgstr "Nieprawidłowa data zameldowania."

#: includes/admin/menu-pages/create-booking/step.php:131
#: includes/shortcodes/checkout-shortcode/step.php:56
#: includes/shortcodes/search-results-shortcode.php:668
#: includes/utils/parse-utils.php:89
msgid "Check-in date cannot be earlier than today."
msgstr "Data zameldowania nie może być wcześniejsza niż data dzisiejsza."

#: includes/admin/menu-pages/create-booking/step.php:157
#: includes/ajax.php:457
#: includes/script-managers/public-script-manager.php:224
#: includes/shortcodes/checkout-shortcode/step.php:90
#: includes/shortcodes/search-results-shortcode.php:686
#: includes/utils/parse-utils.php:120
msgid "Check-out date is not valid."
msgstr "Nieprawidłowa data wymeldowania."

#: includes/admin/menu-pages/create-booking/step.php:168
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:106
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:210
#: includes/shortcodes/checkout-shortcode/step.php:101
#: includes/shortcodes/search-results-shortcode.php:698
#: includes/utils/parse-utils.php:131
msgid "Nothing found. Please try again with different search parameters."
msgstr "Nic nie znaleziono. Spróbuj ponownie z innymi parametrami wyszukiwania."

#: includes/admin/menu-pages/customers-menu-page.php:54
msgid "Sorry, you are not allowed to access this page."
msgstr ""

#: includes/admin/menu-pages/customers-menu-page.php:160
msgid "User ID"
msgstr ""

#: includes/admin/menu-pages/customers-menu-page.php:171
#: templates/account/account-details.php:30
msgid "Username"
msgstr ""

#: includes/admin/menu-pages/customers-menu-page.php:183
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:114
#: includes/bundles/customer-bundle.php:92
#: includes/csv/bookings/bookings-exporter-helper.php:81
#: includes/post-types/booking-cpt.php:90
#: includes/post-types/payment-cpt.php:247
#: includes/views/shortcodes/checkout-view.php:588
#: templates/account/account-details.php:22
msgid "First Name"
msgstr "Imię"

#: includes/admin/menu-pages/customers-menu-page.php:195
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:118
#: includes/bundles/customer-bundle.php:101
#: includes/csv/bookings/bookings-exporter-helper.php:82
#: includes/post-types/booking-cpt.php:98
#: includes/post-types/payment-cpt.php:255
#: includes/views/shortcodes/checkout-view.php:603
#: templates/account/account-details.php:26
msgid "Last Name"
msgstr "Nazwisko"

#: includes/admin/menu-pages/customers-menu-page.php:219
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:126
#: includes/bundles/customer-bundle.php:119
#: includes/csv/bookings/bookings-exporter-helper.php:84
#: includes/post-types/booking-cpt.php:114
#: includes/post-types/payment-cpt.php:271
#: includes/views/shortcodes/checkout-view.php:633
#: templates/account/account-details.php:38
msgid "Phone"
msgstr "Numer telefonu"

#: includes/admin/menu-pages/customers-menu-page.php:231
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:134
#: includes/bundles/customer-bundle.php:137
#: includes/csv/bookings/bookings-exporter-helper.php:86
#: includes/post-types/booking-cpt.php:131
#: includes/views/shortcodes/checkout-view.php:675
#: templates/account/account-details.php:42
msgid "Address"
msgstr "Adres"

#: includes/admin/menu-pages/customers-menu-page.php:243
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:142
#: includes/bundles/customer-bundle.php:155
#: includes/csv/bookings/bookings-exporter-helper.php:88
#: includes/post-types/booking-cpt.php:147
#: includes/post-types/payment-cpt.php:311
#: includes/views/shortcodes/checkout-view.php:705
#: templates/account/account-details.php:64
msgid "State / County"
msgstr "Stan / hrabstwo"

#: includes/admin/menu-pages/customers-menu-page.php:255
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:130
#: includes/csv/bookings/bookings-exporter-helper.php:85
#: includes/post-types/booking-cpt.php:123
#: includes/post-types/payment-cpt.php:279
#: templates/account/account-details.php:46
msgid "Country"
msgstr "Kraj"

#: includes/admin/menu-pages/customers-menu-page.php:268
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:138
#: includes/bundles/customer-bundle.php:146
#: includes/csv/bookings/bookings-exporter-helper.php:87
#: includes/post-types/booking-cpt.php:139
#: includes/post-types/payment-cpt.php:303
#: includes/views/shortcodes/checkout-view.php:690
#: templates/account/account-details.php:68
msgid "City"
msgstr "Miasto"

#: includes/admin/menu-pages/customers-menu-page.php:280
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:146
#: includes/bundles/customer-bundle.php:164
#: includes/csv/bookings/bookings-exporter-helper.php:89
#: includes/post-types/booking-cpt.php:155
#: includes/views/shortcodes/checkout-view.php:720
#: templates/account/account-details.php:72
msgid "Postcode"
msgstr "Kod pocztowy"

#: includes/admin/menu-pages/customers-menu-page.php:301
#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/i-cal-import-menu-page.php:162
#: includes/admin/menu-pages/i-cal-import-menu-page.php:209
#: includes/admin/menu-pages/i-cal-menu-page.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:40
#: includes/post-types/editable-cpt.php:95
msgid "Back"
msgstr "Powrót"

#: includes/admin/menu-pages/customers-menu-page.php:305
msgid "Edit User Profile"
msgstr ""

#: includes/admin/menu-pages/customers-menu-page.php:322
msgid "Customer data updated."
msgstr ""

#: includes/admin/menu-pages/customers-menu-page.php:328
msgid "User account updated."
msgstr ""

#: includes/admin/menu-pages/customers-menu-page.php:363
#: includes/admin/menu-pages/i-cal-menu-page.php:150
msgid "Update"
msgstr "Aktualizuj"

#: includes/admin/menu-pages/customers-menu-page.php:369
#: includes/admin/menu-pages/customers-menu-page.php:382
#: includes/admin/menu-pages/customers-menu-page.php:386
msgid "Customers"
msgstr ""

#: includes/admin/menu-pages/edit-booking-menu-page.php:80
msgid "The booking is not set."
msgstr "Rezerwacja nie została ustawiona."

#: includes/admin/menu-pages/edit-booking-menu-page.php:88
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:49
msgid "The booking not found."
msgstr "Nie znaleziono rezerwacji."

#: includes/admin/menu-pages/edit-booking-menu-page.php:140
msgid "Edit Booking #%d"
msgstr "Edytuj rezerwację nr %d"

#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/reports-menu-page.php:201
msgid "Cancel"
msgstr "Anuluj"

#: includes/admin/menu-pages/edit-booking-menu-page.php:227
#: includes/admin/menu-pages/edit-booking-menu-page.php:234
#: includes/post-types/booking-cpt.php:245
msgid "Edit Booking"
msgstr "Edytuj rezerwację"

#: includes/admin/menu-pages/edit-booking/booking-control.php:18
#: includes/admin/menu-pages/edit-booking/checkout-control.php:35
#: includes/admin/menu-pages/edit-booking/edit-control.php:56
#: includes/admin/menu-pages/edit-booking/summary-control.php:31
msgid "You cannot edit the imported booking. Please update the source booking and resync your calendars."
msgstr "Nie możesz edytować zaimportowanej rezerwacji. Zaktualizuj rezerwację źródłową i ponownie zsynchronizuj swoje kalendarze."

#: includes/admin/menu-pages/edit-booking/booking-control.php:22
#: includes/ajax-api/ajax-actions/abstract-ajax-api-action.php:142
#: includes/ajax.php:184
msgid "Request does not pass security verification. Please refresh the page and try one more time."
msgstr "Żądanie nie przeszło pomyślnej weryfikacji zabezpieczeń. Odśwież stronę i spróbuj jeszcze raz."

#: includes/admin/menu-pages/edit-booking/booking-control.php:26
#: includes/admin/menu-pages/edit-booking/checkout-control.php:40
#: includes/admin/menu-pages/edit-booking/summary-control.php:33
#: includes/utils/parse-utils.php:233
msgid "Check-in date is not set."
msgstr "Data zameldowania nie jest ustawiona."

#: includes/admin/menu-pages/edit-booking/booking-control.php:30
#: includes/admin/menu-pages/edit-booking/checkout-control.php:42
#: includes/admin/menu-pages/edit-booking/summary-control.php:35
#: includes/utils/parse-utils.php:235
msgid "Check-out date is not set."
msgstr "Data wyjazdu nie jest ustawiona."

#: includes/admin/menu-pages/edit-booking/booking-control.php:72
msgid "Unable to update booking. Please try again."
msgstr "Nie można zaktualizować rezerwacji. Spróbuj ponownie."

#: includes/admin/menu-pages/edit-booking/booking-control.php:75
msgid "Booking was edited."
msgstr "Rezerwacja została edytowana."

#: includes/admin/menu-pages/edit-booking/edit-control.php:94
#: includes/script-managers/public-script-manager.php:203
#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Available"
msgstr "Dostępny"

#: includes/admin/menu-pages/edit-booking/edit-control.php:96
#: templates/edit-booking/edit-reserved-rooms.php:71
msgid "Replace"
msgstr "Zamień"

#: includes/admin/menu-pages/edit-booking/summary-control.php:148
msgid "— Add new —"
msgstr "— Dodaj nowy —"

#: includes/admin/menu-pages/extensions-menu-page.php:137
#: includes/admin/menu-pages/extensions-menu-page.php:185
#: includes/admin/menu-pages/extensions-menu-page.php:190
#: includes/admin/menu-pages/settings-menu-page.php:1192
msgid "Extensions"
msgstr "Rozszerzenia"

#: includes/admin/menu-pages/extensions-menu-page.php:140
msgid "Extend the functionality of Hotel Booking plugin with the number of helpful addons for your custom purposes."
msgstr "Rozszerz funkcjonalność wtyczki Hotel Booking o liczbę przydatnych dodatków do własnych celów."

#: includes/admin/menu-pages/extensions-menu-page.php:170
msgid "Get this Extension"
msgstr "Pobierz to rozszerzenie"

#: includes/admin/menu-pages/extensions-menu-page.php:178
msgid "No extensions found."
msgstr "Nie znaleziono rozszerzeń."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:80
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:60
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:102
#: includes/i-cal/logs-handler.php:73
msgid "Abort Process"
msgstr "Przerwij proces"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:81
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:61
msgid "Aborting..."
msgstr "Przerwanie..."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:161
#: includes/admin/menu-pages/i-cal-import-menu-page.php:210
#: includes/admin/menu-pages/i-cal-import-menu-page.php:224
#: includes/admin/room-list-table.php:156
msgid "Import Calendar"
msgstr "Importuj kalendarz"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:166
msgid "Accommodation: %s"
msgstr "Nocleg: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:169
msgid "Accommodation Type: %s"
msgstr "Typ noclegu: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:176
msgid "Please be patient while the calendars are imported. You will be notified via this page when the process is completed."
msgstr "Prosimy o cierpliwość podczas importowania kalendarzy. Zostaniesz powiadomiony za pośrednictwem tej strony po zakończeniu procesu."

#: includes/admin/menu-pages/i-cal-menu-page.php:67
msgid "Accommodation updated."
msgstr "Nocleg został zaktualizowany."

#: includes/admin/menu-pages/i-cal-menu-page.php:73
msgid "This calendar has already been imported for another accommodation."
msgstr "Ten kalendarz został już zaimportowany do innego zakwaterowania."

#: includes/admin/menu-pages/i-cal-menu-page.php:103
msgid "Sync, Import and Export Calendars"
msgstr "Synchronizuj, importuj i eksportuj kalendarze"

#. translators: %s - room name. Example: "Comfort Triple 1"
#: includes/admin/menu-pages/i-cal-menu-page.php:113
msgid "Edit External Calendars of \"%s\""
msgstr "Edytuj zewnętrzne kalendarze z \"%s\""

#: includes/admin/menu-pages/i-cal-menu-page.php:122
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:101
msgid "Sync All External Calendars"
msgstr "Synchronizuj wszystkie zewnętrzne kalendarze"

#: includes/admin/menu-pages/i-cal-menu-page.php:123
msgid "Sync your bookings across all online channels like Booking.com, TripAdvisor, Airbnb etc. via iCalendar file format."
msgstr "Synchronizuj swoje rezerwacje we wszystkich serwisach online, takich jak Booking.com, TripAdvisor, Airbnb itd. poprzez plik formatu iCalendar."

#: includes/admin/menu-pages/i-cal-menu-page.php:219
msgid "Calendar URL"
msgstr "Adres URL kalendarza"

#: includes/admin/menu-pages/i-cal-menu-page.php:225
msgid "Add New Calendar"
msgstr "Dodaj nowy kalendarz"

#: includes/admin/menu-pages/i-cal-menu-page.php:233
#: includes/admin/menu-pages/i-cal-menu-page.php:237
msgid "Sync Calendars"
msgstr "Synchronizuj kalendarze"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:62
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:103
#: includes/i-cal/logs-handler.php:83
msgid "Delete All Logs"
msgstr "Usuń wszystkie dzienniki"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:63
msgid "Deleting..."
msgstr "Usuwanie..."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:64
msgid "%d item"
msgstr "%d pozycja"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:65
msgid "%d items"
msgstr "%d pozycji"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:91
msgid "Calendars Synchronization Status"
msgstr "Status synchronizacji kalendarzy"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:92
msgid "Here you can see synchronization status of your external calendars."
msgstr "Tutaj można obejrzeć status synchronizacji zewnętrznych kalendarzy."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:134
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:148
msgid "Calendars Sync Status"
msgstr "Status synchronizacji kalendarzy"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:151
msgid "Display calendars synchronization status."
msgstr "Wyświetl status synchronizacji kalendarzy."

#: includes/admin/menu-pages/language-menu-page.php:10
#: includes/admin/menu-pages/language-menu-page.php:37
msgid "Language Guide"
msgstr "Przewodnik po języku"

#: includes/admin/menu-pages/language-menu-page.php:11
msgid "Default language"
msgstr "Domyślny język"

#: includes/admin/menu-pages/language-menu-page.php:13
msgid "This plugin will display all system messages, labels, buttons in the language set in <em>General > Settings > Site Language</em>. If the plugin is not available in your language, you may <a href=\"%s\">contribute your translation</a>."
msgstr "Ta wtyczka wyświetli wszystkie komunikaty systemowe, etykiety, przyciski w języku ustawionym w <em>Ogólne> Ustawienia> Język serwisu </em>. W przypadku gdy wtyczka nie jest dostępna w Twoim języku, możesz <a href=\"%s\">dodać swoje tłumaczenie</a>."

#: includes/admin/menu-pages/language-menu-page.php:14
msgid "Custom translations and edits"
msgstr "Niestandardowe tłumaczenia i edycje"

#: includes/admin/menu-pages/language-menu-page.php:15
msgid "You may customize plugin translation by editing the needed texts or adding your translation following these steps:"
msgstr "Możesz dostosować tłumaczenie wtyczki, edytując potrzebne teksty lub dodając swoje tłumaczenia w następujący sposób:"

#: includes/admin/menu-pages/language-menu-page.php:17
msgid "Take the source file for your translations %s or needed translated locale."
msgstr "Pobierz plik źródłowy do tłumaczeń %s lub potrzebne tłumaczenie."

#: includes/admin/menu-pages/language-menu-page.php:18
msgid "Translate texts with any translation program like Poedit, Loco, Pootle etc."
msgstr "Przetłumacz teksty z pomocą dowolnego programu tłumaczeniowego, takiego jak Poedit, Loco, Pootle itp."

#: includes/admin/menu-pages/language-menu-page.php:19
msgid "Put created .mo file with your translations into the folder %s. Where {lang} is ISO-639 language code and {country} is ISO-3166 country code. Example: Brazilian Portuguese file would be called motopress-hotel-booking-pt_BR.mo."
msgstr "Dodaj utworzony plik .mo z tłumaczeniami do foldera %s. Element nazwy {lang} jest kodem języka według ISO-639, a {country} - kod kraju według ISO-3166. Przykład: nazwa brazylijskiego i portugalskiego pliku będzie motopress-hotel-booking-pt_BR.mo."

#: includes/admin/menu-pages/language-menu-page.php:22
msgid "Multilingual content"
msgstr "Wielojęzyczna treść"

#: includes/admin/menu-pages/language-menu-page.php:23
msgid "If your site is multilingual, you may use additional plugins to translate your added content into multiple languages allowing the site visitors to switch them."
msgstr "Jeśli Twoja strona jest wielojęzyczna, możesz użyć dodatkowych wtyczek, aby przetłumaczyć jej zawartość na wiele języków, aby użytkownicy mogli przełączać się między nimi."

#: includes/admin/menu-pages/language-menu-page.php:33
msgid "Language"
msgstr "Język"

#: includes/admin/menu-pages/reports-menu-page.php:52
#: includes/admin/menu-pages/reports-menu-page.php:211
#: includes/admin/menu-pages/reports-menu-page.php:215
msgid "Reports"
msgstr "Raporty"

#: includes/admin/menu-pages/reports-menu-page.php:55
#: includes/admin/room-list-table.php:94
msgid "Export"
msgstr "Eksportuj"

#: includes/admin/menu-pages/reports-menu-page.php:132
#: includes/bookings-calendar.php:695
msgid "All Statuses"
msgstr "Wszystkie statusy"

#: includes/admin/menu-pages/reports-menu-page.php:138
msgid "Booking dates between"
msgstr "Daty rezerwacji między"

#: includes/admin/menu-pages/reports-menu-page.php:139
msgid "Check-in date between"
msgstr "Data zameldowania między"

#: includes/admin/menu-pages/reports-menu-page.php:140
msgid "Check-out date between"
msgstr "Data wymeldowania między"

#: includes/admin/menu-pages/reports-menu-page.php:141
msgid "In-house between"
msgstr "Na miejscu między"

#: includes/admin/menu-pages/reports-menu-page.php:142
msgid "Date of reservation between"
msgstr "Data rezerwacji między"

#: includes/admin/menu-pages/reports-menu-page.php:152
msgid "Export Bookings"
msgstr "Eksportuj rezerwacje"

#: includes/admin/menu-pages/reports-menu-page.php:164
msgid "Choose start date"
msgstr "Wybierz datę rozpoczęcia"

#: includes/admin/menu-pages/reports-menu-page.php:165
msgid "Choose end date"
msgstr "Wybierz datę zakończenia"

#: includes/admin/menu-pages/reports-menu-page.php:171
msgid "Also export imported bookings"
msgstr ""

#: includes/admin/menu-pages/reports-menu-page.php:175
msgid "Select columns to export"
msgstr "Wybierz kolumny do wyeksportowania"

#: includes/admin/menu-pages/reports-menu-page.php:185
msgid "Generate CSV"
msgstr "Generuj CSV"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:25
msgid "Number of accommodations"
msgstr "Ilość pokoi"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:57
#: includes/payments/gateways/gateway.php:494
#: includes/widgets/rooms-widget.php:185
#: templates/create-booking/results/reserve-rooms.php:35
#: assets/blocks/blocks.js:436
#: assets/blocks/blocks.js:686
#: assets/blocks/blocks.js:1215
msgid "Title"
msgstr "Tytuł"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:61
msgid "Leave empty to use accommodation type title."
msgstr "Zostaw puste, aby użyć Tytuł rodzaju pokoju."

#: includes/admin/menu-pages/rooms-generator-menu-page.php:66
msgid "Generate"
msgstr "Generuj"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:75
msgid "Accommodation generated."
msgid_plural "%s accommodations generated."
msgstr[0] "Pokój utworzono."
msgstr[1] "Utworzono %s pokojów."
msgstr[2] "Utworzono %s pokojów."
msgstr[3] ""

#: includes/admin/menu-pages/settings-menu-page.php:113
msgid "General"
msgstr "Ogólne"

#: includes/admin/menu-pages/settings-menu-page.php:116
msgid "Pages"
msgstr "Strony"

#: includes/admin/menu-pages/settings-menu-page.php:123
msgid "Search Results Page"
msgstr "Strona wyników wyszukiwania"

#: includes/admin/menu-pages/settings-menu-page.php:124
msgid "Select page to display search results. Use search results shortcode on this page."
msgstr "Wybierz stronę do wyświetlana wyników wyszukiwania. Stosuj shortcode dla wyników wyszukiwania na tej stronie."

#: includes/admin/menu-pages/settings-menu-page.php:132
msgid "Checkout Page"
msgstr "Strona realizacji transakcji"

#: includes/admin/menu-pages/settings-menu-page.php:133
msgid "Select page user will be redirected to complete booking."
msgstr "Wybierz stronę do której zostanie przekierowany użytkownik do by sfinalizować rezerwację."

#: includes/admin/menu-pages/settings-menu-page.php:141
msgid "Terms & Conditions"
msgstr "Zasady i Warunki"

#: includes/admin/menu-pages/settings-menu-page.php:142
msgid "If you define a \"Terms\" page the customer will be asked if they accept them when checking out."
msgstr "Jeśli zdefiniujesz stronę \"Warunki\", klient zostanie zapytany, czy akceptuje je przy kasie."

#: includes/admin/menu-pages/settings-menu-page.php:150
msgid "Open the Terms & Conditions page in a new window"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:151
msgid "By enabling this option you can avoid errors related to displaying your terms & conditions inline for website pages created in page builders."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:159
msgid "My Account Page"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:160
msgid "Select a page to display user account. Use the customer account shortcode on this page."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:170
#: includes/admin/menu-pages/settings-menu-page.php:177
#: includes/post-types/payment-cpt.php:205
msgid "Currency"
msgstr "Waluta"

#: includes/admin/menu-pages/settings-menu-page.php:186
msgid "Currency Position"
msgstr "Pozycja walutowa"

#: includes/admin/menu-pages/settings-menu-page.php:195
msgid "Decimal Separator"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:204
msgid "Thousand Separator"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:214
msgid "Number of Decimals"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:226
msgid "Misc"
msgstr "Inne"

#: includes/admin/menu-pages/settings-menu-page.php:233
msgid "Square Units"
msgstr "Jednostki powierzchni"

#: includes/admin/menu-pages/settings-menu-page.php:242
msgid "Datepicker Date Format"
msgstr "Format daty w Przełączniku dat"

#: includes/admin/menu-pages/settings-menu-page.php:251
#: includes/emails/templaters/email-templater.php:148
msgid "Check-out Time"
msgstr "Czas wymeldowania"

#: includes/admin/menu-pages/settings-menu-page.php:259
#: includes/emails/templaters/email-templater.php:144
msgid "Check-in Time"
msgstr "Czas zameldowania"

#: includes/admin/menu-pages/settings-menu-page.php:267
msgid "Bed Types"
msgstr "Typy łóżek"

#: includes/admin/menu-pages/settings-menu-page.php:274
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:155
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:251
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:338
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:409
#: includes/post-types/attributes-cpt.php:365
#: includes/post-types/coupon-cpt.php:79
#: includes/post-types/coupon-cpt.php:111
#: includes/post-types/coupon-cpt.php:156
msgid "Type"
msgstr "Typ"

#: includes/admin/menu-pages/settings-menu-page.php:279
msgid "Add Bed Type"
msgstr "Dodaj typ łóżka"

#: includes/admin/menu-pages/settings-menu-page.php:286
msgid "Show Lowest Price for"
msgstr "Pokaż najniższą cenę za"

#: includes/admin/menu-pages/settings-menu-page.php:287
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:185
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:281
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:367
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:438
msgid "days"
msgstr "dni"

#: includes/admin/menu-pages/settings-menu-page.php:291
msgid "Lowest price of accommodation for selected number of days if check-in and check-out dates are not set. Example: set 0 to display today's lowest price, set 7 to display the lowest price for the next week."
msgstr "Najniższa cena zakwaterowania dla wybranej liczby dni, jeśli nie są ustawione daty zameldowania i wymeldowania. Przykład: ustaw 0, aby wyświetlić dzisiejszą najniższą cenę, ustaw 7, aby wyświetlić najniższą cenę za następny tydzień."

#: includes/admin/menu-pages/settings-menu-page.php:298
#: includes/post-types/coupon-cpt.php:288
msgid "Coupons"
msgstr "Kupony rabatowe"

#: includes/admin/menu-pages/settings-menu-page.php:308
msgid "Default calendar view"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:309
msgid "Initial display format of the administrator bookings calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:317
msgid "Text on Checkout"
msgstr "Tekst przy kasie"

#: includes/admin/menu-pages/settings-menu-page.php:318
msgid "This text will appear on the checkout page."
msgstr "Ten tekst pojawi się na stronie kasy."

#: includes/admin/menu-pages/settings-menu-page.php:329
msgid "Disable Booking"
msgstr "Wyłącz opcję Rezerwacja"

#: includes/admin/menu-pages/settings-menu-page.php:336
msgid "Hide reservation forms and buttons"
msgstr "Schować przyciski i formularze rezerwacji"

#: includes/admin/menu-pages/settings-menu-page.php:345
msgid "Text instead of reservation form while booking is disabled"
msgstr "Tekst zamiast formularza rezerwacji podczas gdy opcja Rezerwacji jest wyłączona"

#: includes/admin/menu-pages/settings-menu-page.php:356
#: includes/admin/menu-pages/shortcodes-menu-page.php:510
#: includes/wizard.php:115
#: assets/blocks/blocks.js:1562
#: assets/blocks/blocks.js:1592
msgid "Booking Confirmation"
msgstr "Potwierdzenie rejestracji"

#: includes/admin/menu-pages/settings-menu-page.php:363
msgid "Confirmation Mode"
msgstr "Tryb potwierdzenia"

#: includes/admin/menu-pages/settings-menu-page.php:365
msgid "By customer via email"
msgstr "Przez klienta za pośrednictwem poczty elektronicznej"

#: includes/admin/menu-pages/settings-menu-page.php:366
msgid "By admin manually"
msgstr "Ręcznie przez admina"

#: includes/admin/menu-pages/settings-menu-page.php:367
msgid "Confirmation upon payment"
msgstr "Potwierdzenie po opłaceniu"

#: includes/admin/menu-pages/settings-menu-page.php:376
msgid "Booking Confirmed Page"
msgstr "Strona potwierdzona rezerwacji"

#: includes/admin/menu-pages/settings-menu-page.php:377
msgid "Page user will be redirected to once the booking is confirmed via email or by admin."
msgstr "Użytkownik strony zostanie przekierowany, gdy rezerwacja zostanie potwierdzona e-mailem lub przez administratora."

#: includes/admin/menu-pages/settings-menu-page.php:385
msgid "Approval Time for User"
msgstr "Czas zatwierdzenia przez Użytkownika"

#: includes/admin/menu-pages/settings-menu-page.php:386
msgid "Period of time in minutes the user is given to confirm booking via email. Unconfirmed bookings become Abandoned and accommodation status changes to Available."
msgstr "Okres czasu w minutach, przez który Użytkownik otrzymuje potwierdzenie rezerwacji za pośrednictwem e-mail. Niepotwierdzone rezerwacje zmieniają swój status na Porzucone, a status pokoju zmienia się na Dostępne."

#: includes/admin/menu-pages/settings-menu-page.php:396
msgid "Country of residence field is required for reservation."
msgstr "Pole Kraj zamieszkania jest wymagane do dokonania rezerwacji."

#: includes/admin/menu-pages/settings-menu-page.php:404
msgid "Full address fields are required for reservation."
msgstr "Do rezerwacji potrzebny jest pełny adres."

#: includes/admin/menu-pages/settings-menu-page.php:412
msgid "Customer information is required when placing admin bookings."
msgstr "Informacje o kliencie są wymagane podczas dokonywania rezerwacji przez administratora."

#: includes/admin/menu-pages/settings-menu-page.php:421
msgid "Default Country on Checkout"
msgstr "Domyślny kraj przy kasie"

#: includes/admin/menu-pages/settings-menu-page.php:429
#: includes/emails/templaters/email-templater.php:199
#: includes/post-types/booking-cpt.php:194
#: includes/views/shortcodes/checkout-view.php:477
msgid "Price Breakdown"
msgstr "Podział ceny"

#: includes/admin/menu-pages/settings-menu-page.php:430
msgid "Price breakdown unfolded by default."
msgstr "Zestawienie cen rozwinięte domyślnie."

#: includes/admin/menu-pages/settings-menu-page.php:439
msgid "Accounts"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:446
msgid "Account creation"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:447
msgid "Automatically create an account for a user at checkout."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:455
msgid "Allow customers to create an account during checkout."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:463
msgid "Allow customers to log into their existing account during checkout."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:472
#: includes/upgrader.php:751
#: includes/wizard.php:164
msgid "Booking Cancellation"
msgstr "Rezygnacja z rezerwacji"

#: includes/admin/menu-pages/settings-menu-page.php:479
msgid "User can cancel booking via link provided inside email."
msgstr "Użytkownik może odwołać rezerwację poprzez link, dostarczony w mailu."

#: includes/admin/menu-pages/settings-menu-page.php:487
msgid "Booking Cancelation Page"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:488
msgid "Page to confirm booking cancelation."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:496
msgid "Booking Canceled Page"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:497
msgid "Page to redirect to after a booking is canceled."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:506
msgid "Search Options"
msgstr "Opcje wyszukiwania"

#: includes/admin/menu-pages/settings-menu-page.php:515
msgid "Max Adults"
msgstr "Maksymalna ilość dorosłych"

#: includes/admin/menu-pages/settings-menu-page.php:516
msgid "Maximum accommodation occupancy available in the Search Form."
msgstr "Liczba gości w Formularzu wyszukiwania, którą apartament może pomieścić."

#: includes/admin/menu-pages/settings-menu-page.php:526
msgid "Max Children"
msgstr "Maksymalna ilość dzieci"

#: includes/admin/menu-pages/settings-menu-page.php:534
msgid "Age of Child"
msgstr "Wiek dziecka"

#: includes/admin/menu-pages/settings-menu-page.php:535
msgid "Optional description of the \"Children\" field."
msgstr "Opcjonalny opis pola \"Dzieci\"."

#: includes/admin/menu-pages/settings-menu-page.php:543
msgid "Limit search results based on the requested number of guests."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:551
msgid "Book button behavior on the search results page"
msgstr "Zachowanie przycisku książki na stronie wyników wyszukiwania"

#: includes/admin/menu-pages/settings-menu-page.php:552
msgid "Redirect to the checkout page immediately after successful addition to reservation."
msgstr "Przekieruj do strony kasy natychmiast po pomyślnym dodaniu do rezerwacji."

#: includes/admin/menu-pages/settings-menu-page.php:560
msgid "Recommendation"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:561
msgid "Enable search form to recommend the best set of accommodations according to a number of guests."
msgstr "Włącz formularz wyszukiwania, aby polecić najlepszy zestaw apartamentów według liczby gości."

#: includes/admin/menu-pages/settings-menu-page.php:570
msgid "Skip Search Results"
msgstr "Pomiń wyniki wyszukiwania"

#: includes/admin/menu-pages/settings-menu-page.php:571
msgid "Skip search results page and enable direct booking from accommodation pages."
msgstr "Pomiń stronę wyników wyszukiwania i włącz bezpośrednią rezerwację ze stron zakwaterowania."

#: includes/admin/menu-pages/settings-menu-page.php:578
msgid "Direct Booking Form"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:581
msgid "Show price for selected period"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:582
msgid "Show price together with adults and children fields"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:592
msgid "Enable \"adults\" and \"children\" options for my website (default)."
msgstr "Włącz opcje \"dorośli\" i \"dzieci\" dla mojej witryny (domyślne)."

#: includes/admin/menu-pages/settings-menu-page.php:593
msgid "Disable \"children\" option for my website (hide \"children\" field and use Guests label instead)."
msgstr "Wyłącz opcję \"dzieci\" dla mojej strony (ukryj pole \"dzieci\" i użyj etykiety Goście)."

#: includes/admin/menu-pages/settings-menu-page.php:594
msgid "Disable \"adults\" and \"children\" options for my website."
msgstr "Wyłącz opcje \"dorosłych\" i \"dzieci\" dla mojej witryny."

#: includes/admin/menu-pages/settings-menu-page.php:597
msgid "Guest Management"
msgstr "Zarządzanie gośćmi"

#: includes/admin/menu-pages/settings-menu-page.php:598
msgid "Applies to frontend only."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:606
msgid "Hide \"adults\" and \"children\" fields within search availability forms."
msgstr "Ukryj pola \"dorośli\" i \"dzieci\" w formularzach wyszukiwania dostępności."

#: includes/admin/menu-pages/settings-menu-page.php:614
msgid "Remember the user's selected number of guests until the checkout page."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:623
msgid "Do not apply booking rules for admin bookings."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:631
msgid "Display Options"
msgstr "Opcje wyświetlania"

#: includes/admin/menu-pages/settings-menu-page.php:638
msgid "Display gallery images of accommodation page in lightbox."
msgstr "Wyświetlić galerię strony zakwaterowania w Lightbox."

#: includes/admin/menu-pages/settings-menu-page.php:645
#: includes/admin/menu-pages/shortcodes-menu-page.php:61
#: assets/blocks/blocks.js:250
#: assets/blocks/blocks.js:385
msgid "Availability Calendar"
msgstr "Kalendarz dostępności"

#: includes/admin/menu-pages/settings-menu-page.php:646
#: includes/admin/menu-pages/shortcodes-menu-page.php:76
#: assets/blocks/blocks.js:307
msgid "Display per-night prices in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:654
#: includes/admin/menu-pages/shortcodes-menu-page.php:82
#: assets/blocks/blocks.js:318
msgid "Truncate per-night prices in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:662
#: includes/admin/menu-pages/shortcodes-menu-page.php:88
#: assets/blocks/blocks.js:329
msgid "Display the currency sign in the availability calendar."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:672
msgid "Calendar Theme"
msgstr "Temat kalendarza"

#: includes/admin/menu-pages/settings-menu-page.php:673
msgid "Select theme for an availability calendar."
msgstr "Wybierz temat dla kalendarza dostępności."

#: includes/admin/menu-pages/settings-menu-page.php:680
msgid "Template Mode"
msgstr "Tryb Szablon"

#: includes/admin/menu-pages/settings-menu-page.php:682
msgid "Developer Mode"
msgstr "Tryb Developer"

#: includes/admin/menu-pages/settings-menu-page.php:683
msgid "Theme Mode"
msgstr "Tryb nakładki"

#: includes/admin/menu-pages/settings-menu-page.php:685
msgid "Choose Theme Mode to display the content with the styles of your theme. Choose Developer Mode to control appearance of the content with custom page templates, actions and filters. This option can't be changed if your theme is initially integrated with the plugin."
msgstr "Wybierz tryb Temat do wyświetlania treści w stylach tematu. Wybierz tryb Developer aby zarządzać wyglądem zawartości z niestandardowymi szablonami stron, akcjami i filtrami. Ta opcja nie może być zmieniona, jeśli temat był wstępnie zintegrowany ze wtyczką."

#: includes/admin/menu-pages/settings-menu-page.php:698
msgid "More Styles"
msgstr "Więcej stylów"

#: includes/admin/menu-pages/settings-menu-page.php:699
msgid "Extend the styling options of Hotel Booking plugin with the new free addon - Hotel Booking Styles."
msgstr "Rozszerz opcje stylizacji wtyczki Hotel Booking o nowy darmowy dodatek - Style rezerwacji hoteli."

#: includes/admin/menu-pages/settings-menu-page.php:711
msgid "Calendars Synchronization"
msgstr "Synchronizacja kalendarzy"

#: includes/admin/menu-pages/settings-menu-page.php:718
msgid "Export admin blocks."
msgstr "Eksportuj bloki administratora."

#: includes/admin/menu-pages/settings-menu-page.php:726
msgid "Do not export imported bookings."
msgstr "Nie eksportuj zaimportowanych rezerwacji."

#: includes/admin/menu-pages/settings-menu-page.php:734
msgid "Export and import bookings with buffer time included."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:742
msgid "Minimize Logs"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:743
msgid "Enable the plugin to record only important messages."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:751
msgid "Calendars Synchronization Scheduler"
msgstr "Harmonogram synchronizacji kalendarzy"

#: includes/admin/menu-pages/settings-menu-page.php:762
msgid "Enable automatic external calendars synchronization"
msgstr "Włącz automatyczną synchronizację kalendarzy zewnętrznych"

#: includes/admin/menu-pages/settings-menu-page.php:771
msgid "Clock"
msgstr "Zegar"

#: includes/admin/menu-pages/settings-menu-page.php:772
msgid "Sync calendars at this time (UTC) or starting at this time every interval below."
msgstr "Synchronizuj kalendarze o tej godzinie (UTC) lub zaczynając o tej godzinie w każdym okresie poniżej."

#: includes/admin/menu-pages/settings-menu-page.php:783
msgid "Interval"
msgstr "Interwał"

#: includes/admin/menu-pages/settings-menu-page.php:785
#: includes/crons/cron-manager.php:102
msgid "Quarter an Hour"
msgstr "Kwadrans na godzinę"

#: includes/admin/menu-pages/settings-menu-page.php:786
#: includes/crons/cron-manager.php:107
msgid "Half an Hour"
msgstr "Pół godziny"

#: includes/admin/menu-pages/settings-menu-page.php:787
msgid "Once Hourly"
msgstr "Raz na godzinę"

#: includes/admin/menu-pages/settings-menu-page.php:788
msgid "Twice Daily"
msgstr "Dwa razy dziennie"

#: includes/admin/menu-pages/settings-menu-page.php:789
msgid "Once Daily"
msgstr "Raz dziennie"

#: includes/admin/menu-pages/settings-menu-page.php:800
msgid "Automatically delete sync logs older than"
msgstr "Automatycznie usuwaj dzienniki synchronizacji starsze niż"

#: includes/admin/menu-pages/settings-menu-page.php:802
msgid "Day"
msgstr "Dzień"

#: includes/admin/menu-pages/settings-menu-page.php:803
msgid "Week"
msgstr "Tydzień"

#: includes/admin/menu-pages/settings-menu-page.php:804
#: includes/bookings-calendar.php:575
msgid "Month"
msgstr "Miesiąc"

#: includes/admin/menu-pages/settings-menu-page.php:805
#: includes/bookings-calendar.php:576
msgid "Quarter"
msgstr "Kwartał"

#: includes/admin/menu-pages/settings-menu-page.php:806
msgid "Half a Year"
msgstr "Pół roku"

#: includes/admin/menu-pages/settings-menu-page.php:807
msgid "Never Delete"
msgstr "Nigdy nie usuwaj"

#: includes/admin/menu-pages/settings-menu-page.php:817
msgid "Block Editor"
msgstr "Edytor bloków"

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/admin/menu-pages/settings-menu-page.php:832
msgid "Enable block editor for \"%s\"."
msgstr "Włącz edytor bloków dla \"%s\"."

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/post-types/coupon-cpt.php:59
#: includes/post-types/room-type-cpt.php:53
#: includes/post-types/room-type-cpt.php:64
#: includes/widgets/rooms-widget.php:21
msgid "Accommodation Types"
msgstr "Typy pokojów"

#: includes/admin/menu-pages/settings-menu-page.php:832
#: includes/csv/bookings/bookings-exporter-helper.php:97
#: includes/emails/templaters/reserved-rooms-templater.php:183
#: includes/post-types/coupon-cpt.php:136
#: includes/post-types/service-cpt.php:91
#: includes/post-types/service-cpt.php:101
#: includes/views/booking-view.php:202
msgid "Services"
msgstr "Usługi"

#: includes/admin/menu-pages/settings-menu-page.php:863
msgid "Admin Emails"
msgstr "Emaile admina"

#: includes/admin/menu-pages/settings-menu-page.php:876
msgid "Customer Emails"
msgstr "Emaile klienta"

#: includes/admin/menu-pages/settings-menu-page.php:881
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the <a>Hotel Booking & Mailchimp Integration</a>."
msgstr "Zamień jednorazowych gości w lojalnych klientów, wysyłając automatyczne kampanie marketingowe z <a>rezerwacją hoteli i integracją Mailchimp</a>."

#: includes/admin/menu-pages/settings-menu-page.php:884
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the Hotel Booking & Mailchimp Integration."
msgstr "Zamień jednorazowych gości w lojalnych klientów, wysyłając automatyczne kampanie marketingowe dzięki systemowi rezerwacji hoteli i integracji Mailchimp."

#: includes/admin/menu-pages/settings-menu-page.php:903
msgid "Cancellation Details Template"
msgstr "Szablon Szczegóły anulowania"

#: includes/admin/menu-pages/settings-menu-page.php:904
msgid "Used for %cancellation_details% tag."
msgstr "Używane do znacznika %cancellation_details%."

#: includes/admin/menu-pages/settings-menu-page.php:926
msgid "Email Settings"
msgstr "Ustawienia Email"

#: includes/admin/menu-pages/settings-menu-page.php:931
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with <a>Hotel Booking Notifier</a>."
msgstr "Wysyłaj automatyczne powiadomienia e-mail, takie jak instrukcje dotyczące odbioru kluczy, regulamin hotelu, przed i po przyjeździe / wyjeździe za pomocą <a>Powiadomienia o rezerwacji hotelu</a>."

#: includes/admin/menu-pages/settings-menu-page.php:934
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with Hotel Booking Notifier."
msgstr "Wysyłaj automatyczne powiadomienia e-mail, takie jak instrukcje dotyczące odbioru kluczy, regulamin domu, przed i po przyjeździe / wyjeździe za pomocą Powiadomienia o rezerwacji hotelu."

#: includes/admin/menu-pages/settings-menu-page.php:942
msgid "Email Sender"
msgstr "Wysyłający e-maila"

#: includes/admin/menu-pages/settings-menu-page.php:949
msgid "Administrator Email"
msgstr "E-mail administratora"

#: includes/admin/menu-pages/settings-menu-page.php:958
msgid "From Email"
msgstr "Z emaila"

#: includes/admin/menu-pages/settings-menu-page.php:967
msgid "From Name"
msgstr "Od"

#: includes/admin/menu-pages/settings-menu-page.php:977
msgid "Logo URL"
msgstr "Adres Url loga"

#: includes/admin/menu-pages/settings-menu-page.php:987
msgid "Footer Text"
msgstr "Tekst stopki"

#: includes/admin/menu-pages/settings-menu-page.php:997
msgid "Reserved Accommodation Details Template"
msgstr "Szablon Szczegóły zarezerwowanych apartamentów"

#: includes/admin/menu-pages/settings-menu-page.php:998
msgid "Used for %reserved_rooms_details% tag."
msgstr "Używane do znacznika %reserved_rooms_details%."

#: includes/admin/menu-pages/settings-menu-page.php:1011
msgid "Styles"
msgstr "Style"

#: includes/admin/menu-pages/settings-menu-page.php:1018
msgid "Base Color"
msgstr "Kolor podstawowy"

#: includes/admin/menu-pages/settings-menu-page.php:1027
msgid "Background Color"
msgstr "Kolor tła"

#: includes/admin/menu-pages/settings-menu-page.php:1036
msgid "Body Background Color"
msgstr "Kolor tła ciała strony"

#: includes/admin/menu-pages/settings-menu-page.php:1045
msgid "Body Text Color"
msgstr "Kolor tekstu ciała strony"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "Payment Gateways"
msgstr "Bramki płatnicze"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "General Settings"
msgstr "Ogólne ustawienia"

#: includes/admin/menu-pages/settings-menu-page.php:1071
msgid "Need more gateways? Use our Hotel Booking <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a> extension."
msgstr "Potrzebujesz więcej bramek? Skorzystaj z naszego rozszerzenia rezerwacji hoteli <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a>."

#: includes/admin/menu-pages/settings-menu-page.php:1075
msgid "You may also email the <a href=\"%s\" target=\"_blank\">balance payment request</a> link to your guests."
msgstr "Możesz również wysłać gościom e-mail z <a href=\"%s\" target=\"_blank\">prośbą o wypłatę salda</a>."

#: includes/admin/menu-pages/settings-menu-page.php:1086
msgid "User Pays"
msgstr "Użytkownik płaci"

#: includes/admin/menu-pages/settings-menu-page.php:1088
msgid "Full Amount"
msgstr "Pełna suma"

#: includes/admin/menu-pages/settings-menu-page.php:1089
#: includes/views/booking-view.php:463
msgid "Deposit"
msgstr "Depozyt"

#: includes/admin/menu-pages/settings-menu-page.php:1098
msgid "Deposit Type"
msgstr "Typ depozytu"

#: includes/admin/menu-pages/settings-menu-page.php:1100
#: includes/post-types/coupon-cpt.php:115
#: includes/post-types/coupon-cpt.php:160
msgid "Fixed"
msgstr "Stały"

#: includes/admin/menu-pages/settings-menu-page.php:1101
msgid "Percent"
msgstr "Procent"

#: includes/admin/menu-pages/settings-menu-page.php:1110
msgid "Deposit Amount"
msgstr "Kwota depozytu"

#: includes/admin/menu-pages/settings-menu-page.php:1121
msgid "Deposit Time Frame (days)"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:1122
msgid "Apply deposit to bookings made in at least the selected number of days prior to the check-in date. Otherwise, the full amount is charged."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:1133
msgid "Force Secure Checkout"
msgstr "Wymuś bezpieczny Checkout"

#: includes/admin/menu-pages/settings-menu-page.php:1135
msgid "Force SSL (HTTPS) on the checkout pages. You must have an SSL certificate installed to use this option."
msgstr "Wymuś protokół SSL (HTTPS) na stronie płatności. Do korzystania z tej opcji jest wymagany zainstalowany certyfikat SSL."

#: includes/admin/menu-pages/settings-menu-page.php:1142
msgid "Reservation Received Page"
msgstr "Strona otrzymana rezerwacji"

#: includes/admin/menu-pages/settings-menu-page.php:1150
msgid "Failed Transaction Page"
msgstr "Strona Nieudana transakcja"

#: includes/admin/menu-pages/settings-menu-page.php:1158
msgid "Default Gateway"
msgstr "Domyślna bramka płatnicza"

#: includes/admin/menu-pages/settings-menu-page.php:1172
#: includes/payments/gateways/bank-gateway.php:127
msgid "Pending Payment Time"
msgstr "Czas oczekiwania na płatności"

#: includes/admin/menu-pages/settings-menu-page.php:1173
msgid "Period of time in minutes the user is given to complete payment. Unpaid bookings become Abandoned and accommodation status changes to Available."
msgstr "Okres czasu w minutach, który użytkownik otrzymuje do dokonania płatności. Niezapłacone rezerwacje zmieniają status na Porzucone, a status obiektu zmienia się na Dostępne."

#: includes/admin/menu-pages/settings-menu-page.php:1195
msgid "Install <a href=\"%s\" target=\"_blank\">Hotel Booking addons</a> to manage their settings."
msgstr "Zainstaluj <a href=\"%s\" target=\"_blank\">Hotel Booking addons</a> by zarządzać ustawieniami."

#: includes/admin/menu-pages/settings-menu-page.php:1197
msgid "Install Hotel Booking addons to manage their settings."
msgstr "Zainstaluj dodatki do rezerwacji hoteli, aby zarządzać ich ustawieniami."

#: includes/admin/menu-pages/settings-menu-page.php:1214
msgid "Advanced"
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:1226
#: includes/admin/menu-pages/settings-menu-page.php:1228
msgid "License"
msgstr "Licencja"

#: includes/admin/menu-pages/settings-menu-page.php:1305
msgid "Settings saved."
msgstr "Ustawienia zostały zapisane."

#: includes/admin/menu-pages/settings-menu-page.php:1344
msgid "<strong>Note:</strong> Payment methods will appear on the checkout page only when Confirmation Upon Payment is enabled in Accommodation > Settings > General > Confirmation Mode."
msgstr "<strong>Uwaga:</strong> Metody płatności pojawią się na stronie kasy tylko wtedy, gdy opcja Potwierdzenie przy płatności jest włączona w Zakwaterowanie > Ustawienia > Ogólne> Tryb potwierdzenia."

#: includes/admin/menu-pages/settings-menu-page.php:1432
#: includes/admin/menu-pages/settings-menu-page.php:1436
#: assets/blocks/blocks.js:141
#: assets/blocks/blocks.js:276
#: assets/blocks/blocks.js:429
#: assets/blocks/blocks.js:679
#: assets/blocks/blocks.js:1196
#: assets/blocks/blocks.js:1419
#: assets/blocks/blocks.js:1501
msgid "Settings"
msgstr "Ustawienia"

#: includes/admin/menu-pages/shortcodes-menu-page.php:21
#: assets/blocks/blocks.js:117
msgid "Availability Search Form"
msgstr "Formularz wyszukiwania dostępnych"

#: includes/admin/menu-pages/shortcodes-menu-page.php:22
msgid "Display search form."
msgstr "Pokaż formularz wyszukiwania."

#: includes/admin/menu-pages/shortcodes-menu-page.php:25
#: assets/blocks/blocks.js:148
msgid "The number of adults presetted in the search form."
msgstr "Ilość dorosłych została zapisana do formularzu wyszukiwania."

#: includes/admin/menu-pages/shortcodes-menu-page.php:30
#: assets/blocks/blocks.js:163
msgid "The number of children presetted in the search form."
msgstr "Ilość dzieci została zapisana do formularzu wyszukiwania."

#: includes/admin/menu-pages/shortcodes-menu-page.php:35
msgid "Check-in date presetted in the search form."
msgstr "Data zameldowania została zapisana do formularzu wyszukiwania."

#: includes/admin/menu-pages/shortcodes-menu-page.php:36
#: includes/admin/menu-pages/shortcodes-menu-page.php:41
msgid "date in format %s"
msgstr "data w formacie %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:40
msgid "Check-out date presetted in the search form."
msgstr "Data wymeldowania została zapisana do formularzu wyszukiwania."

#: includes/admin/menu-pages/shortcodes-menu-page.php:45
#: assets/blocks/blocks.js:201
msgid "Custom attributes for advanced search."
msgstr "Niestandardowe atrybuty wyszukiwania zaawansowanego."

#: includes/admin/menu-pages/shortcodes-menu-page.php:46
#: assets/blocks/blocks.js:202
msgid "Comma-separated slugs of attributes."
msgstr "Oddzielone przecinkami fragmenty atrybutów."

#: includes/admin/menu-pages/shortcodes-menu-page.php:50
#: includes/admin/menu-pages/shortcodes-menu-page.php:94
#: includes/admin/menu-pages/shortcodes-menu-page.php:180
#: includes/admin/menu-pages/shortcodes-menu-page.php:259
#: includes/admin/menu-pages/shortcodes-menu-page.php:361
#: includes/admin/menu-pages/shortcodes-menu-page.php:422
#: includes/admin/menu-pages/shortcodes-menu-page.php:450
#: includes/admin/menu-pages/shortcodes-menu-page.php:470
#: includes/admin/menu-pages/shortcodes-menu-page.php:494
#: includes/admin/menu-pages/shortcodes-menu-page.php:514
#: includes/admin/menu-pages/shortcodes-menu-page.php:530
#: includes/admin/menu-pages/shortcodes-menu-page.php:546
msgid "Custom CSS class for shortcode wrapper"
msgstr "Dedykowane CSS klasy dla shortcode wrapper"

#: includes/admin/menu-pages/shortcodes-menu-page.php:51
#: includes/admin/menu-pages/shortcodes-menu-page.php:95
#: includes/admin/menu-pages/shortcodes-menu-page.php:181
#: includes/admin/menu-pages/shortcodes-menu-page.php:260
#: includes/admin/menu-pages/shortcodes-menu-page.php:362
#: includes/admin/menu-pages/shortcodes-menu-page.php:423
#: includes/admin/menu-pages/shortcodes-menu-page.php:451
#: includes/admin/menu-pages/shortcodes-menu-page.php:471
#: includes/admin/menu-pages/shortcodes-menu-page.php:495
#: includes/admin/menu-pages/shortcodes-menu-page.php:515
#: includes/admin/menu-pages/shortcodes-menu-page.php:531
#: includes/admin/menu-pages/shortcodes-menu-page.php:547
msgid "whitespace separated css classes"
msgstr "klasy css rozdzielone whitespace"

#: includes/admin/menu-pages/shortcodes-menu-page.php:65
#: includes/admin/menu-pages/shortcodes-menu-page.php:465
#: includes/admin/menu-pages/shortcodes-menu-page.php:489
#: includes/csv/bookings/bookings-exporter-helper.php:76
#: includes/emails/templaters/reserved-rooms-templater.php:187
msgid "Accommodation Type ID"
msgstr "ID typu pokoju"

#: includes/admin/menu-pages/shortcodes-menu-page.php:66
#: includes/admin/menu-pages/shortcodes-menu-page.php:466
msgid "ID of Accommodation Type to check availability."
msgstr "ID typu pokoju aby sprawdzić dostępność."

#: includes/admin/menu-pages/shortcodes-menu-page.php:67
#: includes/admin/menu-pages/shortcodes-menu-page.php:378
#: includes/admin/menu-pages/shortcodes-menu-page.php:467
#: includes/admin/menu-pages/shortcodes-menu-page.php:491
msgid "integer number"
msgstr "liczba całkowita"

#: includes/admin/menu-pages/shortcodes-menu-page.php:70
#: assets/blocks/blocks.js:295
msgid "How many months to show."
msgstr "Ile miesięcy pokazać."

#: includes/admin/menu-pages/shortcodes-menu-page.php:72
#: assets/blocks/blocks.js:296
msgid "Set the number of columns or the number of rows and columns separated by comma. Example: \"3\" or \"2,3\""
msgstr "Ustaw liczbę kolumn lub liczbę wierszy i kolumn oddzielonych przecinkami. Przykład: \" 3\" lub \"2,3\""

#: includes/admin/menu-pages/shortcodes-menu-page.php:106
#: assets/blocks/blocks.js:251
msgid "Display availability calendar of the current accommodation type or by ID."
msgstr "Wyświetl kalendarz dostępności dla aktualnego typu zakwaterowania lub według ID."

#: includes/admin/menu-pages/shortcodes-menu-page.php:111
#: assets/blocks/blocks.js:397
#: assets/blocks/blocks.js:630
msgid "Availability Search Results"
msgstr "Dostępne wyniki wyszukiwania"

#: includes/admin/menu-pages/shortcodes-menu-page.php:112
#: assets/blocks/blocks.js:398
msgid "Display listing of accommodation types that meet the search criteria."
msgstr "Wyświetlać typy pokojów, które spełniają kryteria wyszukiwania."

#: includes/admin/menu-pages/shortcodes-menu-page.php:115
#: includes/admin/menu-pages/shortcodes-menu-page.php:212
#: includes/admin/menu-pages/shortcodes-menu-page.php:381
#: assets/blocks/blocks.js:437
#: assets/blocks/blocks.js:687
#: assets/blocks/blocks.js:1216
msgid "Whether to display title of the accommodation type."
msgstr "Wyświetlać tytuł typu pokoju."

#: includes/admin/menu-pages/shortcodes-menu-page.php:120
#: includes/admin/menu-pages/shortcodes-menu-page.php:217
#: includes/admin/menu-pages/shortcodes-menu-page.php:386
#: assets/blocks/blocks.js:449
#: assets/blocks/blocks.js:699
#: assets/blocks/blocks.js:1228
msgid "Whether to display featured image of the accommodation type."
msgstr "Wyświetlać polecane zdjęcia dla typu pokoju."

#: includes/admin/menu-pages/shortcodes-menu-page.php:125
#: includes/admin/menu-pages/shortcodes-menu-page.php:222
#: includes/admin/menu-pages/shortcodes-menu-page.php:391
#: assets/blocks/blocks.js:461
#: assets/blocks/blocks.js:711
#: assets/blocks/blocks.js:1240
msgid "Whether to display gallery of the accommodation type."
msgstr "Wyświetlać galerię dla typu pokoju."

#: includes/admin/menu-pages/shortcodes-menu-page.php:130
#: includes/admin/menu-pages/shortcodes-menu-page.php:227
#: includes/admin/menu-pages/shortcodes-menu-page.php:396
#: assets/blocks/blocks.js:473
#: assets/blocks/blocks.js:723
#: assets/blocks/blocks.js:1252
msgid "Whether to display excerpt (short description) of the accommodation type."
msgstr "Wyświetlać krótki opis typu pokoju."

#: includes/admin/menu-pages/shortcodes-menu-page.php:135
#: includes/admin/menu-pages/shortcodes-menu-page.php:232
#: includes/admin/menu-pages/shortcodes-menu-page.php:401
#: assets/blocks/blocks.js:485
#: assets/blocks/blocks.js:735
#: assets/blocks/blocks.js:1264
msgid "Whether to display details of the accommodation type."
msgstr "Czy wyświetlić szczegóły dotyczące typu pokoju."

#: includes/admin/menu-pages/shortcodes-menu-page.php:140
#: includes/admin/menu-pages/shortcodes-menu-page.php:237
#: includes/admin/menu-pages/shortcodes-menu-page.php:406
#: includes/admin/menu-pages/shortcodes-menu-page.php:427
#: assets/blocks/blocks.js:497
#: assets/blocks/blocks.js:747
#: assets/blocks/blocks.js:1276
msgid "Whether to display price of the accommodation type."
msgstr "Czy wyświetlać cenę dla typu pokoju."

#: includes/admin/menu-pages/shortcodes-menu-page.php:145
#: includes/admin/menu-pages/shortcodes-menu-page.php:242
#: includes/admin/menu-pages/shortcodes-menu-page.php:411
msgid "Show View Details button"
msgstr "Pokazać przycisk Więcej info"

#: includes/admin/menu-pages/shortcodes-menu-page.php:146
#: includes/admin/menu-pages/shortcodes-menu-page.php:243
#: includes/admin/menu-pages/shortcodes-menu-page.php:412
#: assets/blocks/blocks.js:509
#: assets/blocks/blocks.js:759
#: assets/blocks/blocks.js:1288
msgid "Whether to display \"View Details\" button with the link to accommodation type."
msgstr "Wyświetlać przycisk \"Więcej info\" z linkiem do typu pokoju."

#: includes/admin/menu-pages/shortcodes-menu-page.php:151
#: includes/admin/menu-pages/shortcodes-menu-page.php:284
#: includes/admin/menu-pages/shortcodes-menu-page.php:332
msgid "Sort by."
msgstr "Sortuj według."

#: includes/admin/menu-pages/shortcodes-menu-page.php:153
#: includes/admin/menu-pages/shortcodes-menu-page.php:173
#: includes/admin/menu-pages/shortcodes-menu-page.php:286
#: includes/admin/menu-pages/shortcodes-menu-page.php:306
#: includes/admin/menu-pages/shortcodes-menu-page.php:334
#: includes/admin/menu-pages/shortcodes-menu-page.php:354
msgid "%1$s. See the <a href=\"%2$s\" target=\"_blank\">full list</a>."
msgstr "%1$s. Zobacz <a href=\"%2$s\" target=\"_blank\">pełną listę</a>."

#: includes/admin/menu-pages/shortcodes-menu-page.php:160
#: includes/admin/menu-pages/shortcodes-menu-page.php:293
#: includes/admin/menu-pages/shortcodes-menu-page.php:341
msgid "Designates the ascending or descending order of sorting."
msgstr "Wyznacza rosnącą lub malejącą kolejność sortowania."

#: includes/admin/menu-pages/shortcodes-menu-page.php:162
#: includes/admin/menu-pages/shortcodes-menu-page.php:295
#: includes/admin/menu-pages/shortcodes-menu-page.php:343
msgid "ASC - from lowest to highest values (1, 2, 3). DESC - from highest to lowest values (3, 2, 1)."
msgstr "ASC - od najniższych do najwyższych wartości (1, 2, 3). DESC - od najwyższych do najniższych wartości (3, 2, 1)."

#: includes/admin/menu-pages/shortcodes-menu-page.php:166
#: includes/admin/menu-pages/shortcodes-menu-page.php:299
#: includes/admin/menu-pages/shortcodes-menu-page.php:347
#: assets/blocks/blocks.js:574
#: assets/blocks/blocks.js:910
#: assets/blocks/blocks.js:1094
msgid "Custom field name. Required if \"orderby\" is one of the \"meta_value\", \"meta_value_num\" or \"meta_value_*\"."
msgstr "Nazwa pola niestandardowego. Wymagana, jeśli \" orderby \\ \"jest jedną z wartości \" meta_value \\ \", \" meta_value_num \\ \"lub \" meta_value _ * \\ \"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:167
#: includes/admin/menu-pages/shortcodes-menu-page.php:300
#: includes/admin/menu-pages/shortcodes-menu-page.php:348
msgid "custom field name"
msgstr "nazwa pola niestandardowego"

#: includes/admin/menu-pages/shortcodes-menu-page.php:168
#: includes/admin/menu-pages/shortcodes-menu-page.php:177
#: includes/admin/menu-pages/shortcodes-menu-page.php:301
#: includes/admin/menu-pages/shortcodes-menu-page.php:310
#: includes/admin/menu-pages/shortcodes-menu-page.php:349
#: includes/admin/menu-pages/shortcodes-menu-page.php:358
#: includes/admin/menu-pages/shortcodes-menu-page.php:645
msgid "empty string"
msgstr "puste pole"

#: includes/admin/menu-pages/shortcodes-menu-page.php:171
#: includes/admin/menu-pages/shortcodes-menu-page.php:304
#: includes/admin/menu-pages/shortcodes-menu-page.php:352
msgid "Specified type of the custom field. Can be used in conjunction with orderby=\"meta_value\"."
msgstr "Określony typ pola niestandardowego. Może być używane w połączeniu z orderby = \" meta_value \\ \"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:185
msgid "Sort by. Use \"orderby\" insted."
msgstr "Sortuj według. Użyj \" orderby \\ \"insted."

#: includes/admin/menu-pages/shortcodes-menu-page.php:191
#: includes/admin/menu-pages/shortcodes-menu-page.php:248
msgid "Show Book button"
msgstr "Pokaż przycisk rezerwacji"

#: includes/admin/menu-pages/shortcodes-menu-page.php:192
#: includes/admin/menu-pages/shortcodes-menu-page.php:249
#: includes/admin/menu-pages/shortcodes-menu-page.php:417
#: assets/blocks/blocks.js:771
#: assets/blocks/blocks.js:1300
msgid "Whether to display Book button."
msgstr "Czy wyświetlać przycisk Rezerwuj."

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "NOTE:"
msgstr "UWAGA:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
msgid "Use only on page that you set as Search Results Page in <a href=\"%s\">Settings</a>"
msgstr "Stosować tylko na stronie, która jest ustawiona jako Strona wyników wyszukiwania w <a href=\"%s\">Ustawieniach</a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:209
#: assets/blocks/blocks.js:642
msgid "Accommodation Types Listing"
msgstr "Lista typów pokojów"

#: includes/admin/menu-pages/shortcodes-menu-page.php:254
#: includes/admin/menu-pages/shortcodes-menu-page.php:327
#: assets/blocks/blocks.js:804
#: assets/blocks/blocks.js:1028
msgid "Count per page"
msgstr "Ilość na stronie"

#: includes/admin/menu-pages/shortcodes-menu-page.php:255
#: assets/blocks/blocks.js:805
msgid "integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "integer, -1 aby wyświetlić wszystko, domyślnie: \"Strony blogów pokazują co najwyżej\""

#: includes/admin/menu-pages/shortcodes-menu-page.php:264
#: assets/blocks/blocks.js:817
msgid "IDs of categories that will be shown."
msgstr "Identyfikatory kategorii, które zostaną wyświetlone."

#: includes/admin/menu-pages/shortcodes-menu-page.php:265
#: includes/admin/menu-pages/shortcodes-menu-page.php:270
#: includes/admin/menu-pages/shortcodes-menu-page.php:275
#: includes/admin/menu-pages/shortcodes-menu-page.php:323
#: assets/blocks/blocks.js:1017
msgid "Comma-separated IDs."
msgstr "Rozdzielone przecinkami numery ID."

#: includes/admin/menu-pages/shortcodes-menu-page.php:269
#: assets/blocks/blocks.js:829
msgid "IDs of tags that will be shown."
msgstr "Identyfikatory tagów, które zostaną wyświetlone."

#: includes/admin/menu-pages/shortcodes-menu-page.php:274
#: assets/blocks/blocks.js:793
msgid "IDs of accommodations that will be shown."
msgstr "Identyfikatory kwater, które zostaną wyświetlone."

#: includes/admin/menu-pages/shortcodes-menu-page.php:279
#: assets/blocks/blocks.js:841
msgid "Logical relationship between each taxonomy when there is more than one."
msgstr "Logiczna zależność między każdą taksonomią, gdy jest więcej niż jedna."

#: includes/admin/menu-pages/shortcodes-menu-page.php:319
#: assets/blocks/blocks.js:983
msgid "Services Listing"
msgstr "Lista usług"

#: includes/admin/menu-pages/shortcodes-menu-page.php:322
#: assets/blocks/blocks.js:792
msgid "IDs"
msgstr "Numery ID"

#: includes/admin/menu-pages/shortcodes-menu-page.php:324
#: assets/blocks/blocks.js:1016
msgid "IDs of services that will be shown. "
msgstr "ID usług, które zostaną wyświetlane. "

#: includes/admin/menu-pages/shortcodes-menu-page.php:368
msgid "Show All Services"
msgstr "Pokaż wszystkie usługi"

#: includes/admin/menu-pages/shortcodes-menu-page.php:373
#: assets/blocks/blocks.js:1167
#: assets/blocks/blocks.js:1344
msgid "Single Accommodation Type"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:377
msgid "ID of accommodation type to display."
msgstr "ID typów pokoi do wyświetlania."

#: includes/admin/menu-pages/shortcodes-menu-page.php:441
msgid "Display accommodation type with title and image."
msgstr "Wyświetlić typ pokoju z tytułem i zdjęciami."

#: includes/admin/menu-pages/shortcodes-menu-page.php:446
#: assets/blocks/blocks.js:1356
#: assets/blocks/blocks.js:1386
msgid "Checkout Form"
msgstr "Formularz kasy"

#: includes/admin/menu-pages/shortcodes-menu-page.php:447
#: assets/blocks/blocks.js:1357
msgid "Display checkout form."
msgstr "Pokaż formularz kasy."

#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "Use only on page that you set as Checkout Page in <a href=\"%s\">Settings</a>"
msgstr "Stosować tylko na stronie, które jest ustawiona jako Strona kasy w <a href=\"%s\">Ustawieniach</a>"

#: includes/admin/menu-pages/shortcodes-menu-page.php:462
#: assets/blocks/blocks.js:1398
#: assets/blocks/blocks.js:1468
msgid "Booking Form"
msgstr "Formularz rezerwacji"

#: includes/admin/menu-pages/shortcodes-menu-page.php:481
msgid "Show Booking Form for Accommodation Type with id 777"
msgstr "Pokaż Formularz rezerwacji dla typu pokoju z numerem id 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:486
#: assets/blocks/blocks.js:1480
#: assets/blocks/blocks.js:1550
msgid "Accommodation Rates List"
msgstr "Lista ocen obiektów"

#: includes/admin/menu-pages/shortcodes-menu-page.php:490
#: assets/blocks/blocks.js:1204
msgid "ID of accommodation type."
msgstr "ID typu pokoju."

#: includes/admin/menu-pages/shortcodes-menu-page.php:505
msgid "Show Accommodation Rates List for accommodation type with id 777"
msgstr "Pokaż Listę ocen dla typu pokoju z numerem id 777"

#: includes/admin/menu-pages/shortcodes-menu-page.php:511
#: assets/blocks/blocks.js:1563
msgid "Display booking and payment details."
msgstr "Wyświetl szczegóły rezerwacji i płatności."

#: includes/admin/menu-pages/shortcodes-menu-page.php:521
msgid "Use this shortcode on Booking Confirmed and Reservation Received pages"
msgstr "Użyj tego skrótu na stronach potwierdzonych rezerwacji i otrzymanych rezerwacji"

#: includes/admin/menu-pages/shortcodes-menu-page.php:526
msgid "Booking Cancelation"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:527
msgid "Display booking cancelation details."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:537
msgid "Use this shortcode on the Booking Cancelation page"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:542
msgid "Customer Account"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:543
msgid "Display log in form or customer account area."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:553
msgid "Use this shortcode to create the My Account page."
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:565
#: includes/admin/menu-pages/shortcodes-menu-page.php:699
#: includes/admin/menu-pages/shortcodes-menu-page.php:703
msgid "Shortcodes"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:569
msgid "Shortcode"
msgstr ""

#: includes/admin/menu-pages/shortcodes-menu-page.php:570
#: includes/post-types/attributes-cpt.php:307
msgid "Parameters"
msgstr "Parametry"

#: includes/admin/menu-pages/shortcodes-menu-page.php:571
msgid "Example"
msgstr "Przykład"

#: includes/admin/menu-pages/shortcodes-menu-page.php:603
#: includes/admin/menu-pages/shortcodes-menu-page.php:625
msgid "Deprecated since %s"
msgstr "Nieaktualne od %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:635
msgid "Values:"
msgstr "Wartości:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:640
msgid "Default:"
msgstr "Domyślnie:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:687
msgid "Optional."
msgstr "Opcjonalne."

#: includes/admin/menu-pages/shortcodes-menu-page.php:695
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:30
#: includes/payments/gateways/gateway.php:365
#: includes/views/shortcodes/checkout-view.php:247
#: includes/views/shortcodes/checkout-view.php:270
#: includes/views/shortcodes/checkout-view.php:538
#: includes/views/shortcodes/checkout-view.php:572
#: templates/account/account-details.php:34
msgid "Required"
msgstr "Wymagane"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:34
msgid "Taxes and fees saved."
msgstr "Zapisano podatki i opłaty."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:41
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:459
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:463
msgid "Taxes & Fees"
msgstr "Podatki i opłaty"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:136
#: includes/csv/bookings/bookings-exporter-helper.php:102
#: includes/views/booking-view.php:296
msgid "Fees"
msgstr "Opłaty"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:137
msgid "No fees have been created yet."
msgstr "Nie utworzono jeszcze żadnych opłat."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:138
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:234
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:321
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:392
#: includes/post-types/booking-cpt.php:219
msgid "Add new"
msgstr "Dodaj nowy"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:146
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:242
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:329
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:400
msgid "Label"
msgstr "Etykieta"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:147
msgid "New fee"
msgstr "Nowa opłata"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:158
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:254
msgid "Per guest / per day"
msgstr "Na gościa / dziennie"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:159
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:255
msgid "Per accommodation / per day"
msgstr "Za zakwaterowanie / dzień"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:160
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:256
msgid "Per accommodation (%)"
msgstr "Na zakwaterowanie (%)"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:182
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:278
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:364
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:435
msgid "Limit"
msgstr ""

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:183
msgid "How often this fee is charged. Set 0 to charge each day of the stay period. Set 1 to charge once."
msgstr ""

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:197
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:200
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:293
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:296
msgid "Include"
msgstr ""

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:198
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:294
msgid "Show accommodation rate with this charge included"
msgstr ""

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:232
#: includes/csv/bookings/bookings-exporter-helper.php:95
#: includes/views/booking-view.php:171
msgid "Accommodation Taxes"
msgstr "Podatki od zakwaterowania"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:233
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:320
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:391
msgid "No taxes have been created yet."
msgstr "Nie utworzono jeszcze żadnych podatków."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:243
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:330
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:401
msgid "New tax"
msgstr "Nowy podatek"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:279
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:365
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:436
msgid "Limit of days the fee is charged. Set 0 to charge each day of stay period. Set 1 to charge once."
msgstr ""

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:319
#: includes/views/booking-view.php:265
msgid "Service Taxes"
msgstr "Podatki od usług"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:341
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:412
#: includes/post-types/coupon-cpt.php:114
#: includes/post-types/coupon-cpt.php:159
msgid "Percentage"
msgstr "Procent"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:390
#: includes/views/booking-view.php:349
msgid "Fee Taxes"
msgstr "Podatki od opłat"

#: includes/admin/room-list-table.php:95
msgid "External Calendars"
msgstr "Zewnętrzne kalendarze"

#: includes/admin/room-list-table.php:157
#: includes/admin/room-list-table.php:211
msgid "Sync External Calendars"
msgstr "Synchronizuj zewnętrzne kalendarze"

#: includes/admin/room-list-table.php:163
#: includes/admin/sync-rooms-list-table.php:65
msgctxt "Placeholder for empty accommodation title"
msgid "(no title)"
msgstr "(bez tytułu)"

#: includes/admin/room-list-table.php:185
msgid "Download Calendar"
msgstr "Pobierz kalendarz"

#: includes/admin/sync-logs-list-table.php:73
msgid "Message"
msgstr "Wiadomość"

#: includes/admin/sync-logs-list-table.php:82
msgid "Success"
msgstr "Sukces"

#: includes/admin/sync-logs-list-table.php:85
msgid "Info"
msgstr "Informacje"

#: includes/admin/sync-logs-list-table.php:88
msgid "Warning"
msgstr "Ostrzeżenie"

#: includes/admin/sync-rooms-list-table.php:71
msgctxt "This is date and time format 31/12/2017 - 23:59:59"
msgid "d/m/Y - H:i:s"
msgstr ""

#: includes/admin/sync-rooms-list-table.php:75
#: includes/ajax.php:945
msgid "Waiting"
msgstr "W trakcie oczekiwania"

#: includes/admin/sync-rooms-list-table.php:78
#: includes/ajax.php:948
msgid "Processing"
msgstr "W trakcie przetwarzania"

#: includes/admin/sync-rooms-list-table.php:128
msgctxt "Total number of processed bookings"
msgid "Total"
msgstr "Razem"

#: includes/admin/sync-rooms-list-table.php:129
msgid "Succeed"
msgstr "Udane"

#: includes/admin/sync-rooms-list-table.php:130
msgid "Skipped"
msgstr "Pominięte"

#: includes/admin/sync-rooms-list-table.php:131
msgid "Failed"
msgstr "Nieudane"

#: includes/admin/sync-rooms-list-table.php:132
msgid "Removed"
msgstr "Usunięty"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:58
msgid "Copying to clipboard failed. Please press Ctrl/Cmd+C to copy."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:80
msgid "Description is missing."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:83
msgid "User is missing."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:86
msgid "Permission is missing."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:102
msgid "You do not have permission to assign API Keys to the selected user."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:132
msgid "API Key updated successfully."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:164
msgid "API Key generated successfully. Make sure to copy your new keys now as the secret key will be hidden once you leave this page."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:176
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:116
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:119
msgid "Revoke key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:46
msgid "No keys found."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:57
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:22
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:95
#: includes/payments/gateways/gateway.php:504
#: includes/post-types/coupon-cpt.php:37
#: includes/post-types/rate-cpt.php:81
msgid "Description"
msgstr "Opisanie"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:58
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:81
msgid "Consumer key ending in"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:59
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:36
msgid "User"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:60
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:55
msgid "Permissions"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:61
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:89
msgid "Last access"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:99
msgid "API key"
msgstr ""

#. translators: %d: API key ID.
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:111
msgid "ID: %d"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:126
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:227
msgid "Revoke"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:182
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:65
msgid "Read"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:183
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:66
msgid "Write"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:184
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:67
msgid "Read/Write"
msgstr ""

#. translators: 1: last access date 2: last access time
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:205
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:96
msgid "%1$s at %2$s"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:213
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:100
msgid "Unknown"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:68
msgid "You do not have permission to edit this API Key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:89
msgid "REST API"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:91
msgid "Add key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:176
msgid "You do not have permission to revoke this API Key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:191
msgid "You do not have permission to edit API Keys"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:211
msgid "You do not have permission to revoke API Keys"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:13
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:114
msgid "Generate API key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:25
msgid "Friendly name for identifying this key."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:39
msgid "Owner of these keys."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:58
msgid "Access type of these keys."
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:148
msgid "Consumer key"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copied!"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copy"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:156
msgid "Consumer secret"
msgstr ""

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:164
msgid "QR Code"
msgstr ""

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:33
#: includes/ajax-api/ajax-actions/update-booking-notes.php:52
#: includes/ajax.php:344
#: includes/ajax.php:388
#: includes/csv/bookings/bookings-query.php:85
msgid "Please complete all required fields and try again."
msgstr "Wypełnij wszystkie pola wymagane i spróbuj ponownie."

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:55
msgid "Sorry, the minimum allowed payment amount is %s to use this payment method."
msgstr "Przepraszamy, minimalna dozwolona kwota płatności to %s, aby użyć tej metody płatności."

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:60
#: includes/post-types/booking-cpt.php:35
msgid "Booking Information"
msgstr "Dane rezerwacji"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:68
#: includes/emails/templaters/email-templater.php:136
#: includes/post-types/booking-cpt.php:51
#: template-functions.php:706
#: template-functions.php:710
#: templates/create-booking/search/search-form.php:53
#: templates/edit-booking/edit-dates.php:33
#: templates/shortcodes/search/search-form.php:43
#: templates/widgets/search-availability/search-form.php:43
#: assets/blocks/blocks.js:177
msgid "Check-in Date"
msgstr "Data zameldowania"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:72
#: includes/emails/templaters/email-templater.php:140
#: includes/post-types/booking-cpt.php:60
#: template-functions.php:715
#: template-functions.php:719
#: templates/create-booking/search/search-form.php:73
#: templates/edit-booking/edit-dates.php:42
#: templates/shortcodes/search/search-form.php:63
#: templates/widgets/search-availability/search-form.php:62
#: assets/blocks/blocks.js:189
msgid "Check-out Date"
msgstr "Data wymeldowania"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:91
msgid "Summary"
msgstr ""

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:99
msgid "Source"
msgstr ""

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:110
#: includes/post-types/booking-cpt.php:83
#: templates/emails/customer-approved-booking.php:31
#: templates/emails/customer-cancelled-booking.php:29
#: templates/emails/customer-confirmation-booking.php:35
#: templates/emails/customer-pending-booking.php:32
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:30
msgid "Customer Information"
msgstr "Dane klienta"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:150
#: includes/csv/bookings/bookings-exporter-helper.php:90
#: includes/emails/templaters/email-templater.php:189
#: includes/post-types/booking-cpt.php:164
msgid "Customer Note"
msgstr "Komentarz klienta"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:156
#: includes/post-types/booking-cpt.php:171
msgid "Additional Information"
msgstr "Dodatkowe informacje"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:160
#: includes/csv/bookings/bookings-exporter-helper.php:107
#: includes/post-types/booking-cpt.php:178
#: includes/post-types/coupon-cpt.php:289
msgid "Coupon"
msgstr "Kupon rabatowy"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:164
#: includes/post-types/booking-cpt.php:187
msgid "Total Booking Price"
msgstr "Łączna cena rezerwacji"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:173
#: includes/bundles/customer-bundle.php:173
#: includes/post-types/booking-cpt.php:201
#: includes/views/shortcodes/checkout-view.php:735
msgid "Notes"
msgstr "Uwagi"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:192
msgid "%1$s on %2$s"
msgstr ""

#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:185
#: template-functions.php:88
msgid "Based on your search parameters"
msgstr "Na podstawie Twoich parametrów wyszukiwania"

#: includes/ajax.php:245
msgid "No bookings found for your request."
msgstr "Nie znaleziono rezerwacji dla twojego zapytania."

#: includes/ajax.php:252
msgid "Uploads directory is not writable."
msgstr "Katalog uploads nie jest zapisywalny."

#: includes/ajax.php:314
msgid "No enough data"
msgstr "Brak wystarczających danych"

#: includes/ajax.php:332
#: includes/script-managers/admin-script-manager.php:94
msgid "An error has occurred"
msgstr "Wystąpił błąd"

#: includes/ajax.php:372
#: includes/script-managers/public-script-manager.php:199
msgid "An error has occurred, please try again later."
msgstr "Wystąpił błąd, spróbuj ponownie później."

#: includes/ajax.php:473
msgid "The number of adults is not valid."
msgstr "Nieprawidłowa liczba dorosłych."

#: includes/ajax.php:477
msgid "The number of guests is not valid."
msgstr ""

#: includes/ajax.php:519
#: includes/ajax.php:593
msgid "An error has occurred. Please try again later."
msgstr "Wystąpił błąd. Spróbuj ponownie później."

#: includes/ajax.php:750
msgid "Chosen payment method is not available. Please refresh the page and try one more time."
msgstr "Wybrana metoda płatności nie jest dostępna. Przeładuj stronę i spróbuj jeszcze raz."

#: includes/ajax.php:832
msgid "Coupon applied successfully."
msgstr "Kupon rabatowy został pomyślnie zastosowany."

#: includes/ajax.php:838
#: includes/entities/coupon.php:366
msgid "Coupon is not valid."
msgstr "Kupon rabatowy jest nieaktywny."

#: includes/ajax.php:1046
msgid "You do not have permission to do this action."
msgstr ""

#: includes/attribute-functions.php:164
#: includes/post-types/attributes-cpt.php:137
#: includes/post-types/attributes-cpt.php:149
#: includes/post-types/attributes-cpt.php:345
msgctxt "Not selected value in the search form."
msgid "&mdash;"
msgstr ""

#: includes/bookings-calendar.php:577
msgid "Year"
msgstr "Rok"

#: includes/bookings-calendar.php:578
#: includes/post-types/attributes-cpt.php:298
#: includes/reports/report-filters.php:94
msgid "Custom"
msgstr "Zaawansowane"

#: includes/bookings-calendar.php:608
#: includes/post-types/booking-cpt/statuses.php:102
#: includes/reports/data/report-earnings-by-dates-data.php:28
msgctxt "Booking status"
msgid "Confirmed"
msgstr "Potwierdzone"

#: includes/bookings-calendar.php:645
#: includes/reports/abstract-report.php:46
#: includes/reports/earnings-report.php:361
msgid "Show"
msgstr "Pokaż"

#: includes/bookings-calendar.php:649
#: templates/create-booking/search/search-form.php:131
#: templates/shortcodes/search/search-form.php:138
#: templates/widgets/search-availability/search-form.php:142
msgid "Search"
msgstr "Szukaj"

#: includes/bookings-calendar.php:652
#: includes/bookings-calendar.php:654
#: includes/bookings-calendar.php:697
#: includes/script-managers/public-script-manager.php:200
msgid "Booked"
msgstr "Zarezerwowane"

#: includes/bookings-calendar.php:657
#: includes/bookings-calendar.php:659
#: includes/bookings-calendar.php:698
#: includes/script-managers/public-script-manager.php:202
msgid "Pending"
msgstr "W oczekiwaniu"

#: includes/bookings-calendar.php:662
#: includes/bookings-calendar.php:664
msgid "External"
msgstr ""

#: includes/bookings-calendar.php:667
#: includes/bookings-calendar.php:669
#: includes/bookings-calendar.php:1133
msgid "Blocked"
msgstr ""

#: includes/bookings-calendar.php:687
msgid "Search results for accommodations that have bookings with status \"%s\" from %s until %s"
msgstr "Wyniki dla obiektów z statusem \"%s\" od %s do %s"

#: includes/bookings-calendar.php:696
msgid "Free"
msgstr "Darmowe"

#: includes/bookings-calendar.php:699
msgid "Locked (Booked or Pending)"
msgstr "Zablokowane (zarezerwowane lub oczekujące)"

#: includes/bookings-calendar.php:729
#: includes/bookings-calendar.php:819
msgid "Until"
msgstr "Do"

#: includes/bookings-calendar.php:775
msgid "Period:"
msgstr ""

#: includes/bookings-calendar.php:782
msgid "&lt; Prev"
msgstr "&lt; Poprzedni"

#: includes/bookings-calendar.php:799
msgid "Next &gt;"
msgstr "Następny &gt;"

#: includes/bookings-calendar.php:874
msgid "No accommodations found."
msgstr "Nie znaleziono żadnych obiektów."

#: includes/bookings-calendar.php:1123
msgid "Check-out #%d"
msgstr "Data wymeldowania #%d"

#: includes/bookings-calendar.php:1127
msgid "Check-in #%d"
msgstr "Data zameldowania #%d"

#: includes/bookings-calendar.php:1131
msgid "Booking #%d"
msgstr "Rezerwacja #%d"

#: includes/bookings-calendar.php:1136
#: includes/bookings-calendar.php:1140
#: includes/script-managers/public-script-manager.php:201
msgid "Buffer time."
msgstr ""

#: includes/bookings-calendar.php:1143
msgctxt "Availability"
msgid "Free"
msgstr "Dostępne"

#: includes/bookings-calendar.php:1172
#: templates/emails/reserved-room-details.php:15
msgid "Adults: %s"
msgstr "Dorośli: %s"

#: includes/bookings-calendar.php:1176
#: templates/emails/reserved-room-details.php:17
msgid "Children: %s"
msgstr "Dzieci: %s"

#: includes/bookings-calendar.php:1183
msgid "Booking imported with UID %s."
msgstr "Rezerwacja zaimportowana z UID %s."

#: includes/bookings-calendar.php:1185
msgid "Imported booking."
msgstr "Zaimportowana rezerwacja."

#: includes/bookings-calendar.php:1193
msgid "Description: %s."
msgstr "Opis: %s."

#: includes/bookings-calendar.php:1197
msgid "Source: %s."
msgstr "Źródło: %s."

#: includes/bundles/countries-bundle.php:16
msgid "Afghanistan"
msgstr "Afganistan"

#: includes/bundles/countries-bundle.php:17
msgid "&#197;land Islands"
msgstr "Wyspy Alandzkie"

#: includes/bundles/countries-bundle.php:18
msgid "Albania"
msgstr "Albania"

#: includes/bundles/countries-bundle.php:19
msgid "Algeria"
msgstr "Algieria"

#: includes/bundles/countries-bundle.php:20
msgid "American Samoa"
msgstr "Samoa Amerykańskie"

#: includes/bundles/countries-bundle.php:21
msgid "Andorra"
msgstr "Andora"

#: includes/bundles/countries-bundle.php:22
msgid "Angola"
msgstr "Angola"

#: includes/bundles/countries-bundle.php:23
msgid "Anguilla"
msgstr "Anguilla"

#: includes/bundles/countries-bundle.php:24
msgid "Antarctica"
msgstr "Bangladesz"

#: includes/bundles/countries-bundle.php:25
msgid "Antigua and Barbuda"
msgstr "Antigua i Barbuda"

#: includes/bundles/countries-bundle.php:26
msgid "Argentina"
msgstr "Argentyna"

#: includes/bundles/countries-bundle.php:27
msgid "Armenia"
msgstr "Armenia"

#: includes/bundles/countries-bundle.php:28
msgid "Aruba"
msgstr "Aruba"

#: includes/bundles/countries-bundle.php:29
msgid "Australia"
msgstr "Australia"

#: includes/bundles/countries-bundle.php:30
msgid "Austria"
msgstr "Austria"

#: includes/bundles/countries-bundle.php:31
msgid "Azerbaijan"
msgstr "Azerbejdżan"

#: includes/bundles/countries-bundle.php:32
msgid "Bahamas"
msgstr "Bahamy"

#: includes/bundles/countries-bundle.php:33
msgid "Bahrain"
msgstr "Bahrajn"

#: includes/bundles/countries-bundle.php:34
msgid "Bangladesh"
msgstr "Bangladesz"

#: includes/bundles/countries-bundle.php:35
msgid "Barbados"
msgstr "Barbados"

#: includes/bundles/countries-bundle.php:36
msgid "Belarus"
msgstr "Białoruś"

#: includes/bundles/countries-bundle.php:37
msgid "Belgium"
msgstr "Belgia"

#: includes/bundles/countries-bundle.php:38
msgid "Belau"
msgstr "Belau"

#: includes/bundles/countries-bundle.php:39
msgid "Belize"
msgstr "Belize"

#: includes/bundles/countries-bundle.php:40
msgid "Benin"
msgstr "Benin"

#: includes/bundles/countries-bundle.php:41
msgid "Bermuda"
msgstr "Bermudy"

#: includes/bundles/countries-bundle.php:42
msgid "Bhutan"
msgstr "Bhutan"

#: includes/bundles/countries-bundle.php:43
msgid "Bolivia"
msgstr "Boliwia"

#: includes/bundles/countries-bundle.php:44
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius i Saba"

#: includes/bundles/countries-bundle.php:45
msgid "Bosnia and Herzegovina"
msgstr "Bośnia i Hercegowina"

#: includes/bundles/countries-bundle.php:46
msgid "Botswana"
msgstr "Botswana"

#: includes/bundles/countries-bundle.php:47
msgid "Bouvet Island"
msgstr "Wyspa Bouvet"

#: includes/bundles/countries-bundle.php:48
msgid "Brazil"
msgstr "Brazylia"

#: includes/bundles/countries-bundle.php:49
msgid "British Indian Ocean Territory"
msgstr "Brytyjskie Terytorium Oceanu Indyjskiego"

#: includes/bundles/countries-bundle.php:50
msgid "British Virgin Islands"
msgstr "Brytyjskie Wyspy Dziewicze"

#: includes/bundles/countries-bundle.php:51
msgid "Brunei"
msgstr "Brunei"

#: includes/bundles/countries-bundle.php:52
msgid "Bulgaria"
msgstr "Bułgaria"

#: includes/bundles/countries-bundle.php:53
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: includes/bundles/countries-bundle.php:54
msgid "Burundi"
msgstr "Burundi"

#: includes/bundles/countries-bundle.php:55
msgid "Cambodia"
msgstr "Kambodża"

#: includes/bundles/countries-bundle.php:56
msgid "Cameroon"
msgstr "Kamerun"

#: includes/bundles/countries-bundle.php:57
msgid "Canada"
msgstr "Kanada"

#: includes/bundles/countries-bundle.php:58
msgid "Cape Verde"
msgstr "Wyspy Zielonego Przylądka"

#: includes/bundles/countries-bundle.php:59
msgid "Cayman Islands"
msgstr "Kajmany"

#: includes/bundles/countries-bundle.php:60
msgid "Central African Republic"
msgstr "Republika Środkowoafrykańska"

#: includes/bundles/countries-bundle.php:61
msgid "Chad"
msgstr "Czad"

#: includes/bundles/countries-bundle.php:62
msgid "Chile"
msgstr "Chile"

#: includes/bundles/countries-bundle.php:63
msgid "China"
msgstr "Chiny"

#: includes/bundles/countries-bundle.php:64
msgid "Christmas Island"
msgstr "Wyspa Bożego Narodzenia"

#: includes/bundles/countries-bundle.php:65
msgid "Cocos (Keeling) Islands"
msgstr "Wyspy Kokosowe (Keeling)"

#: includes/bundles/countries-bundle.php:66
msgid "Colombia"
msgstr "Kolumbia"

#: includes/bundles/countries-bundle.php:67
msgid "Comoros"
msgstr "Komory"

#: includes/bundles/countries-bundle.php:68
msgid "Congo (Brazzaville)"
msgstr "Kongo - Brazzaville"

#: includes/bundles/countries-bundle.php:69
msgid "Congo (Kinshasa)"
msgstr "Kongo - Kinszasa"

#: includes/bundles/countries-bundle.php:70
msgid "Cook Islands"
msgstr "Wyspy Cooka"

#: includes/bundles/countries-bundle.php:71
msgid "Costa Rica"
msgstr "Kostaryka"

#: includes/bundles/countries-bundle.php:72
msgid "Croatia"
msgstr "Chorwacja"

#: includes/bundles/countries-bundle.php:73
msgid "Cuba"
msgstr "Kuba"

#: includes/bundles/countries-bundle.php:74
msgid "Cura&ccedil;ao"
msgstr "Curacao"

#: includes/bundles/countries-bundle.php:75
msgid "Cyprus"
msgstr "Cypr"

#: includes/bundles/countries-bundle.php:76
msgid "Czech Republic"
msgstr "Republika Czeska"

#: includes/bundles/countries-bundle.php:77
msgid "Denmark"
msgstr "Dania"

#: includes/bundles/countries-bundle.php:78
msgid "Djibouti"
msgstr "Dżibuti"

#: includes/bundles/countries-bundle.php:79
msgid "Dominica"
msgstr "Dominika"

#: includes/bundles/countries-bundle.php:80
msgid "Dominican Republic"
msgstr "Republika Dominikany"

#: includes/bundles/countries-bundle.php:81
msgid "Ecuador"
msgstr "Ekwador"

#: includes/bundles/countries-bundle.php:82
msgid "Egypt"
msgstr "Egipt"

#: includes/bundles/countries-bundle.php:83
msgid "El Salvador"
msgstr "Salwador"

#: includes/bundles/countries-bundle.php:84
msgid "Equatorial Guinea"
msgstr "Gwinea Równikowa"

#: includes/bundles/countries-bundle.php:85
msgid "Eritrea"
msgstr "Erytrea"

#: includes/bundles/countries-bundle.php:86
msgid "Estonia"
msgstr "Estonia"

#: includes/bundles/countries-bundle.php:87
msgid "Ethiopia"
msgstr "Etiopia"

#: includes/bundles/countries-bundle.php:88
msgid "Falkland Islands"
msgstr "Falklandy"

#: includes/bundles/countries-bundle.php:89
msgid "Faroe Islands"
msgstr "Wyspy Owcze"

#: includes/bundles/countries-bundle.php:90
msgid "Fiji"
msgstr "Fidżi"

#: includes/bundles/countries-bundle.php:91
msgid "Finland"
msgstr "Finlandia"

#: includes/bundles/countries-bundle.php:92
msgid "France"
msgstr "Francja"

#: includes/bundles/countries-bundle.php:93
msgid "French Guiana"
msgstr "Gujana Francuska"

#: includes/bundles/countries-bundle.php:94
msgid "French Polynesia"
msgstr "Polinezja Francuska"

#: includes/bundles/countries-bundle.php:95
msgid "French Southern Territories"
msgstr "Francuskie Terytoria Południowe"

#: includes/bundles/countries-bundle.php:96
msgid "Gabon"
msgstr "Gabon"

#: includes/bundles/countries-bundle.php:97
msgid "Gambia"
msgstr "Gambia"

#: includes/bundles/countries-bundle.php:98
msgid "Georgia"
msgstr "Gruzja"

#: includes/bundles/countries-bundle.php:99
msgid "Germany"
msgstr "Niemcy"

#: includes/bundles/countries-bundle.php:100
msgid "Ghana"
msgstr "Ghana"

#: includes/bundles/countries-bundle.php:101
msgid "Gibraltar"
msgstr "Gibraltar"

#: includes/bundles/countries-bundle.php:102
msgid "Greece"
msgstr "Grecja"

#: includes/bundles/countries-bundle.php:103
msgid "Greenland"
msgstr "Grenlandia"

#: includes/bundles/countries-bundle.php:104
msgid "Grenada"
msgstr "Grenada"

#: includes/bundles/countries-bundle.php:105
msgid "Guadeloupe"
msgstr "Gwadelupa"

#: includes/bundles/countries-bundle.php:106
msgid "Guam"
msgstr "Guam"

#: includes/bundles/countries-bundle.php:107
msgid "Guatemala"
msgstr "Gwatemala"

#: includes/bundles/countries-bundle.php:108
msgid "Guernsey"
msgstr "Guernsey"

#: includes/bundles/countries-bundle.php:109
msgid "Guinea"
msgstr "Gwinea"

#: includes/bundles/countries-bundle.php:110
msgid "Guinea-Bissau"
msgstr "Gwinea Bissau"

#: includes/bundles/countries-bundle.php:111
msgid "Guyana"
msgstr "Gujana"

#: includes/bundles/countries-bundle.php:112
msgid "Haiti"
msgstr "Haiti"

#: includes/bundles/countries-bundle.php:113
msgid "Heard Island and McDonald Islands"
msgstr "Wyspy Heard i McDonalda"

#: includes/bundles/countries-bundle.php:114
msgid "Honduras"
msgstr "Honduras"

#: includes/bundles/countries-bundle.php:115
msgid "Hong Kong"
msgstr "Hongkong"

#: includes/bundles/countries-bundle.php:116
msgid "Hungary"
msgstr "Węgry"

#: includes/bundles/countries-bundle.php:117
msgid "Iceland"
msgstr "Islandia"

#: includes/bundles/countries-bundle.php:118
msgid "India"
msgstr "Indie"

#: includes/bundles/countries-bundle.php:119
msgid "Indonesia"
msgstr "Indonezja"

#: includes/bundles/countries-bundle.php:120
msgid "Iran"
msgstr "Iran"

#: includes/bundles/countries-bundle.php:121
msgid "Iraq"
msgstr "Irak"

#: includes/bundles/countries-bundle.php:122
msgid "Ireland"
msgstr "Irlandia"

#: includes/bundles/countries-bundle.php:123
msgid "Isle of Man"
msgstr "Wyspa Man"

#: includes/bundles/countries-bundle.php:124
msgid "Israel"
msgstr "Izrael"

#: includes/bundles/countries-bundle.php:125
msgid "Italy"
msgstr "Włochy"

#: includes/bundles/countries-bundle.php:126
msgid "Ivory Coast"
msgstr "Wybrzeże Kości Słoniowej"

#: includes/bundles/countries-bundle.php:127
msgid "Jamaica"
msgstr "Jamajka"

#: includes/bundles/countries-bundle.php:128
msgid "Japan"
msgstr "Japonia"

#: includes/bundles/countries-bundle.php:129
msgid "Jersey"
msgstr "Jersey"

#: includes/bundles/countries-bundle.php:130
msgid "Jordan"
msgstr "Jordania"

#: includes/bundles/countries-bundle.php:131
msgid "Kazakhstan"
msgstr "Kazachstan"

#: includes/bundles/countries-bundle.php:132
msgid "Kenya"
msgstr "Kenia"

#: includes/bundles/countries-bundle.php:133
msgid "Kiribati"
msgstr "Kiribati"

#: includes/bundles/countries-bundle.php:134
msgid "Kuwait"
msgstr "Kuwejt"

#: includes/bundles/countries-bundle.php:135
msgid "Kyrgyzstan"
msgstr "Kirgistan"

#: includes/bundles/countries-bundle.php:136
msgid "Laos"
msgstr "Laos"

#: includes/bundles/countries-bundle.php:137
msgid "Latvia"
msgstr "Łotwa"

#: includes/bundles/countries-bundle.php:138
msgid "Lebanon"
msgstr "Liban"

#: includes/bundles/countries-bundle.php:139
msgid "Lesotho"
msgstr "Lesoto"

#: includes/bundles/countries-bundle.php:140
msgid "Liberia"
msgstr "Liberia"

#: includes/bundles/countries-bundle.php:141
msgid "Libya"
msgstr "Libia"

#: includes/bundles/countries-bundle.php:142
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: includes/bundles/countries-bundle.php:143
msgid "Lithuania"
msgstr "Litwa"

#: includes/bundles/countries-bundle.php:144
msgid "Luxembourg"
msgstr "Luksemburg"

#: includes/bundles/countries-bundle.php:145
msgid "Macao S.A.R., China"
msgstr "Makao S.A.R., Chiny"

#: includes/bundles/countries-bundle.php:146
msgid "Macedonia"
msgstr "Macedonia"

#: includes/bundles/countries-bundle.php:147
msgid "Madagascar"
msgstr "Madagaskar"

#: includes/bundles/countries-bundle.php:148
msgid "Malawi"
msgstr "Malawi"

#: includes/bundles/countries-bundle.php:149
msgid "Malaysia"
msgstr "Malezja"

#: includes/bundles/countries-bundle.php:150
msgid "Maldives"
msgstr "Malediwy"

#: includes/bundles/countries-bundle.php:151
msgid "Mali"
msgstr "Mali"

#: includes/bundles/countries-bundle.php:152
msgid "Malta"
msgstr "Malta"

#: includes/bundles/countries-bundle.php:153
msgid "Marshall Islands"
msgstr "Wyspy Marshalla"

#: includes/bundles/countries-bundle.php:154
msgid "Martinique"
msgstr "Martynika"

#: includes/bundles/countries-bundle.php:155
msgid "Mauritania"
msgstr "Mauretania"

#: includes/bundles/countries-bundle.php:156
msgid "Mauritius"
msgstr "Mauritius"

#: includes/bundles/countries-bundle.php:157
msgid "Mayotte"
msgstr "Majotta"

#: includes/bundles/countries-bundle.php:158
msgid "Mexico"
msgstr "Meksyk"

#: includes/bundles/countries-bundle.php:159
msgid "Micronesia"
msgstr "Mikronezja"

#: includes/bundles/countries-bundle.php:160
msgid "Moldova"
msgstr "Mołdawia"

#: includes/bundles/countries-bundle.php:161
msgid "Monaco"
msgstr "Monako"

#: includes/bundles/countries-bundle.php:162
msgid "Mongolia"
msgstr "Mongolia"

#: includes/bundles/countries-bundle.php:163
msgid "Montenegro"
msgstr "Czarnogóra"

#: includes/bundles/countries-bundle.php:164
msgid "Montserrat"
msgstr "Montserrat"

#: includes/bundles/countries-bundle.php:165
msgid "Morocco"
msgstr "Maroko"

#: includes/bundles/countries-bundle.php:166
msgid "Mozambique"
msgstr "Mozambik"

#: includes/bundles/countries-bundle.php:167
msgid "Myanmar"
msgstr "Mjanma"

#: includes/bundles/countries-bundle.php:168
msgid "Namibia"
msgstr "Namibia"

#: includes/bundles/countries-bundle.php:169
msgid "Nauru"
msgstr "Nauru"

#: includes/bundles/countries-bundle.php:170
msgid "Nepal"
msgstr "Nepal"

#: includes/bundles/countries-bundle.php:171
msgid "Netherlands"
msgstr "Holandia"

#: includes/bundles/countries-bundle.php:172
msgid "New Caledonia"
msgstr "Nowa Kaledonia"

#: includes/bundles/countries-bundle.php:173
msgid "New Zealand"
msgstr "Nowa Zelandia"

#: includes/bundles/countries-bundle.php:174
msgid "Nicaragua"
msgstr "Nikaragua"

#: includes/bundles/countries-bundle.php:175
msgid "Niger"
msgstr "Niger"

#: includes/bundles/countries-bundle.php:176
msgid "Nigeria"
msgstr "Nigeria"

#: includes/bundles/countries-bundle.php:177
msgid "Niue"
msgstr "Niue"

#: includes/bundles/countries-bundle.php:178
msgid "Norfolk Island"
msgstr "Wyspa Norfolk"

#: includes/bundles/countries-bundle.php:179
msgid "Northern Mariana Islands"
msgstr "Północne wyspy Mariany"

#: includes/bundles/countries-bundle.php:180
msgid "North Korea"
msgstr "Korea Północna"

#: includes/bundles/countries-bundle.php:181
msgid "Norway"
msgstr "Norwegia"

#: includes/bundles/countries-bundle.php:182
msgid "Oman"
msgstr "Oman"

#: includes/bundles/countries-bundle.php:183
msgid "Pakistan"
msgstr "Pakistan"

#: includes/bundles/countries-bundle.php:184
msgid "Palestinian Territory"
msgstr "Terytorium palestyńskie"

#: includes/bundles/countries-bundle.php:185
msgid "Panama"
msgstr "Panama"

#: includes/bundles/countries-bundle.php:186
msgid "Papua New Guinea"
msgstr "Papua Nowa Gwinea"

#: includes/bundles/countries-bundle.php:187
msgid "Paraguay"
msgstr "Paragwaj"

#: includes/bundles/countries-bundle.php:188
#: includes/settings/main-settings.php:37
msgid "Peru"
msgstr "Peru"

#: includes/bundles/countries-bundle.php:189
msgid "Philippines"
msgstr "Filipiny"

#: includes/bundles/countries-bundle.php:190
msgid "Pitcairn"
msgstr "Pitcairn"

#: includes/bundles/countries-bundle.php:191
msgid "Poland"
msgstr "Polska"

#: includes/bundles/countries-bundle.php:192
msgid "Portugal"
msgstr "Portugalia"

#: includes/bundles/countries-bundle.php:193
msgid "Puerto Rico"
msgstr "Portoryko"

#: includes/bundles/countries-bundle.php:194
msgid "Qatar"
msgstr "Katar"

#: includes/bundles/countries-bundle.php:195
msgid "Reunion"
msgstr "Reunion"

#: includes/bundles/countries-bundle.php:196
msgid "Romania"
msgstr "Rumunia"

#: includes/bundles/countries-bundle.php:197
msgid "Russia"
msgstr "Rosja"

#: includes/bundles/countries-bundle.php:198
msgid "Rwanda"
msgstr "Rwandy"

#: includes/bundles/countries-bundle.php:199
msgid "Saint Barth&eacute;lemy"
msgstr "Wyspa Świętego Bartłomieja"

#: includes/bundles/countries-bundle.php:200
msgid "Saint Helena"
msgstr "Święta Helena"

#: includes/bundles/countries-bundle.php:201
msgid "Saint Kitts and Nevis"
msgstr "Saint Kitts i Nevis"

#: includes/bundles/countries-bundle.php:202
msgid "Saint Lucia"
msgstr "Święta Lucia"

#: includes/bundles/countries-bundle.php:203
msgid "Saint Martin (French part)"
msgstr "Saint Martin (część francuska)"

#: includes/bundles/countries-bundle.php:204
msgid "Saint Martin (Dutch part)"
msgstr "Saint Martin (część holenderska)"

#: includes/bundles/countries-bundle.php:205
msgid "Saint Pierre and Miquelon"
msgstr "Saint Pierre i Miquelon"

#: includes/bundles/countries-bundle.php:206
msgid "Saint Vincent and the Grenadines"
msgstr "Saint Vincent i Grenadyny"

#: includes/bundles/countries-bundle.php:207
msgid "San Marino"
msgstr "San Marino"

#: includes/bundles/countries-bundle.php:208
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "Wyspy Świętego Tomasza i Książęca"

#: includes/bundles/countries-bundle.php:209
msgid "Saudi Arabia"
msgstr "Arabia Saudyjska"

#: includes/bundles/countries-bundle.php:210
msgid "Senegal"
msgstr "Senegal"

#: includes/bundles/countries-bundle.php:211
msgid "Serbia"
msgstr "Serbia"

#: includes/bundles/countries-bundle.php:212
msgid "Seychelles"
msgstr "Seszele"

#: includes/bundles/countries-bundle.php:213
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: includes/bundles/countries-bundle.php:214
msgid "Singapore"
msgstr "Singapur"

#: includes/bundles/countries-bundle.php:215
msgid "Slovakia"
msgstr "Słowacja"

#: includes/bundles/countries-bundle.php:216
msgid "Slovenia"
msgstr "Słowenia"

#: includes/bundles/countries-bundle.php:217
msgid "Solomon Islands"
msgstr "Wyspy Salomona"

#: includes/bundles/countries-bundle.php:218
msgid "Somalia"
msgstr "Somali"

#: includes/bundles/countries-bundle.php:219
msgid "South Africa"
msgstr "Afryka Południowa"

#: includes/bundles/countries-bundle.php:220
msgid "South Georgia/Sandwich Islands"
msgstr "Wyspy Georgia Południowa / Sandwich"

#: includes/bundles/countries-bundle.php:221
msgid "South Korea"
msgstr "Korea Południowa"

#: includes/bundles/countries-bundle.php:222
msgid "South Sudan"
msgstr "Południowy Sudan"

#: includes/bundles/countries-bundle.php:223
msgid "Spain"
msgstr "Hiszpania"

#: includes/bundles/countries-bundle.php:224
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: includes/bundles/countries-bundle.php:225
msgid "Sudan"
msgstr "Sudan"

#: includes/bundles/countries-bundle.php:226
msgid "Suriname"
msgstr "Surinam"

#: includes/bundles/countries-bundle.php:227
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard i Jan Mayen"

#: includes/bundles/countries-bundle.php:228
msgid "Swaziland"
msgstr "Suazi"

#: includes/bundles/countries-bundle.php:229
msgid "Sweden"
msgstr "Szwecja"

#: includes/bundles/countries-bundle.php:230
msgid "Switzerland"
msgstr "Szwajcaria"

#: includes/bundles/countries-bundle.php:231
msgid "Syria"
msgstr "Syria"

#: includes/bundles/countries-bundle.php:232
msgid "Taiwan"
msgstr "Tajwan"

#: includes/bundles/countries-bundle.php:233
msgid "Tajikistan"
msgstr "Tadżykistan"

#: includes/bundles/countries-bundle.php:234
msgid "Tanzania"
msgstr "Tanzania"

#: includes/bundles/countries-bundle.php:235
msgid "Thailand"
msgstr "Tajlandia"

#: includes/bundles/countries-bundle.php:236
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: includes/bundles/countries-bundle.php:237
msgid "Togo"
msgstr "Togo"

#: includes/bundles/countries-bundle.php:238
msgid "Tokelau"
msgstr "Tokelau"

#: includes/bundles/countries-bundle.php:239
msgid "Tonga"
msgstr "Tonga"

#: includes/bundles/countries-bundle.php:240
msgid "Trinidad and Tobago"
msgstr "Trynidad i Tobago"

#: includes/bundles/countries-bundle.php:241
msgid "Tunisia"
msgstr "Tunezja"

#: includes/bundles/countries-bundle.php:242
msgid "Turkey"
msgstr "Turcja"

#: includes/bundles/countries-bundle.php:243
msgid "Turkmenistan"
msgstr "Turkmenia"

#: includes/bundles/countries-bundle.php:244
msgid "Turks and Caicos Islands"
msgstr "Wyspy Turks i Caicos"

#: includes/bundles/countries-bundle.php:245
msgid "Tuvalu"
msgstr "Tuvalu"

#: includes/bundles/countries-bundle.php:246
msgid "Uganda"
msgstr "Uganda"

#: includes/bundles/countries-bundle.php:247
msgid "Ukraine"
msgstr "Ukraina"

#: includes/bundles/countries-bundle.php:248
msgid "United Arab Emirates"
msgstr "Zjednoczone Emiraty Arabskie"

#: includes/bundles/countries-bundle.php:249
msgid "United Kingdom (UK)"
msgstr "Wielka Brytania (UK)"

#: includes/bundles/countries-bundle.php:250
msgid "United States (US)"
msgstr "Stany Zjednoczone (US)"

#: includes/bundles/countries-bundle.php:251
msgid "United States (US) Minor Outlying Islands"
msgstr "Stany Zjednoczone (US) Dalekie Wyspy Mniejsze"

#: includes/bundles/countries-bundle.php:252
msgid "United States (US) Virgin Islands"
msgstr "Stany Zjednoczone (US) Wyspy Dziewicze"

#: includes/bundles/countries-bundle.php:253
msgid "Uruguay"
msgstr "Urugwaj"

#: includes/bundles/countries-bundle.php:254
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: includes/bundles/countries-bundle.php:255
msgid "Vanuatu"
msgstr "Vanuatu"

#: includes/bundles/countries-bundle.php:256
msgid "Vatican"
msgstr "Watykan"

#: includes/bundles/countries-bundle.php:257
msgid "Venezuela"
msgstr "Wenezuela"

#: includes/bundles/countries-bundle.php:258
msgid "Vietnam"
msgstr "Wietnam"

#: includes/bundles/countries-bundle.php:259
msgid "Wallis and Futuna"
msgstr "Wallis i Futuna"

#: includes/bundles/countries-bundle.php:260
msgid "Western Sahara"
msgstr "Sahara Zachodnia"

#: includes/bundles/countries-bundle.php:261
msgid "Samoa"
msgstr "Samoa"

#: includes/bundles/countries-bundle.php:262
msgid "Yemen"
msgstr "Jemen"

#: includes/bundles/countries-bundle.php:263
msgid "Zambia"
msgstr "Zambia"

#: includes/bundles/countries-bundle.php:264
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: includes/bundles/currency-bundle.php:17
msgid "Euro"
msgstr "Euro"

#: includes/bundles/currency-bundle.php:18
msgid "United States (US) dollar"
msgstr "Dolar amerykański (US)"

#: includes/bundles/currency-bundle.php:19
msgid "Pound sterling"
msgstr "Funt szterling"

#: includes/bundles/currency-bundle.php:20
msgid "United Arab Emirates dirham"
msgstr "Zjednoczone Emiraty Arabskie Dirham"

#: includes/bundles/currency-bundle.php:21
msgid "Afghan afghani"
msgstr "Afgańska afghani"

#: includes/bundles/currency-bundle.php:22
msgid "Albanian lek"
msgstr "Leku albańskie"

#: includes/bundles/currency-bundle.php:23
msgid "Armenian dram"
msgstr "Armeńskie dram"

#: includes/bundles/currency-bundle.php:24
msgid "Netherlands Antillean guilder"
msgstr "Gulden Antyli Niderlandzkich"

#: includes/bundles/currency-bundle.php:25
msgid "Angolan kwanza"
msgstr "Kwanza angolski"

#: includes/bundles/currency-bundle.php:26
msgid "Argentine peso"
msgstr "Peso argentyńskie"

#: includes/bundles/currency-bundle.php:27
msgid "Australian dollar"
msgstr "Dolary australijskie"

#: includes/bundles/currency-bundle.php:28
msgid "Aruban florin"
msgstr "Florin arubski"

#: includes/bundles/currency-bundle.php:29
msgid "Azerbaijani manat"
msgstr "Manat azerski"

#: includes/bundles/currency-bundle.php:30
msgid "Bosnia and Herzegovina convertible mark"
msgstr "Konwertybilna marka Bośni i Hercegowiny"

#: includes/bundles/currency-bundle.php:31
msgid "Barbadian dollar"
msgstr "Dolar barbadoski"

#: includes/bundles/currency-bundle.php:32
msgid "Bangladeshi taka"
msgstr "Taka Bangladeszu"

#: includes/bundles/currency-bundle.php:33
msgid "Bulgarian lev"
msgstr "Bułgarski lew"

#: includes/bundles/currency-bundle.php:34
msgid "Bahraini dinar"
msgstr "Dinar bahrański"

#: includes/bundles/currency-bundle.php:35
msgid "Burundian franc"
msgstr "Frank burundyjski"

#: includes/bundles/currency-bundle.php:36
msgid "Bermudian dollar"
msgstr "Dolar bermudzki"

#: includes/bundles/currency-bundle.php:37
msgid "Brunei dollar"
msgstr "Dolar brunejski"

#: includes/bundles/currency-bundle.php:38
msgid "Bolivian boliviano"
msgstr "Boliwijski boliwiano"

#: includes/bundles/currency-bundle.php:39
msgid "Brazilian real"
msgstr "Real brazylijski"

#: includes/bundles/currency-bundle.php:40
msgid "Bahamian dollar"
msgstr "Dolar bahamski"

#: includes/bundles/currency-bundle.php:41
msgid "Bitcoin"
msgstr "Bitcoin"

#: includes/bundles/currency-bundle.php:42
msgid "Bhutanese ngultrum"
msgstr "Ngultrum bhutański"

#: includes/bundles/currency-bundle.php:43
msgid "Botswana pula"
msgstr "Pula botswańska"

#: includes/bundles/currency-bundle.php:44
msgid "Belarusian ruble (old)"
msgstr "Rubel białoruski (stary)"

#: includes/bundles/currency-bundle.php:45
msgid "Belarusian ruble"
msgstr "Rubel białoruski"

#: includes/bundles/currency-bundle.php:46
msgid "Belize dollar"
msgstr "Dolar belizeński"

#: includes/bundles/currency-bundle.php:47
msgid "Canadian dollar"
msgstr "Dolary kanadyjskie"

#: includes/bundles/currency-bundle.php:48
msgid "Congolese franc"
msgstr "Frank kongijski"

#: includes/bundles/currency-bundle.php:49
msgid "Swiss franc"
msgstr "Frank szwajcarski"

#: includes/bundles/currency-bundle.php:50
msgid "Chilean peso"
msgstr "Peso chilijskie"

#: includes/bundles/currency-bundle.php:51
msgid "Chinese yuan"
msgstr "Chiński Juan"

#: includes/bundles/currency-bundle.php:52
msgid "Colombian peso"
msgstr "Peso kolumbijskie"

#: includes/bundles/currency-bundle.php:53
msgid "Costa Rican col&oacute;n"
msgstr "Kolon kostarykański"

#: includes/bundles/currency-bundle.php:54
msgid "Cuban convertible peso"
msgstr "Wenezuelski peso konwertowalne"

#: includes/bundles/currency-bundle.php:55
msgid "Cuban peso"
msgstr "Peso kubańskie"

#: includes/bundles/currency-bundle.php:56
msgid "Cape Verdean escudo"
msgstr "Escudo kapweńskie"

#: includes/bundles/currency-bundle.php:57
msgid "Czech koruna"
msgstr "Czeska korona"

#: includes/bundles/currency-bundle.php:58
msgid "Djiboutian franc"
msgstr "Frank dżiboutijski"

#: includes/bundles/currency-bundle.php:59
msgid "Danish krone"
msgstr "Korona duńska"

#: includes/bundles/currency-bundle.php:60
msgid "Dominican peso"
msgstr "Peso dominikańskie"

#: includes/bundles/currency-bundle.php:61
msgid "Algerian dinar"
msgstr "Dinar algierski"

#: includes/bundles/currency-bundle.php:62
msgid "Egyptian pound"
msgstr "Funt egipski"

#: includes/bundles/currency-bundle.php:63
msgid "Eritrean nakfa"
msgstr "Nakfa erytrejska"

#: includes/bundles/currency-bundle.php:64
msgid "Ethiopian birr"
msgstr "Birr etiopski"

#: includes/bundles/currency-bundle.php:65
msgid "Fijian dollar"
msgstr "Dolar fidżijski"

#: includes/bundles/currency-bundle.php:66
msgid "Falkland Islands pound"
msgstr "Funt Falklandów"

#: includes/bundles/currency-bundle.php:67
msgid "Georgian lari"
msgstr "Lari gruzińskie"

#: includes/bundles/currency-bundle.php:68
msgid "Guernsey pound"
msgstr "Funt guernseyjski"

#: includes/bundles/currency-bundle.php:69
msgid "Ghana cedi"
msgstr "Cedi ghańskie"

#: includes/bundles/currency-bundle.php:70
msgid "Gibraltar pound"
msgstr "Funt gibraltarski"

#: includes/bundles/currency-bundle.php:71
msgid "Gambian dalasi"
msgstr "Dalasi gambijskie"

#: includes/bundles/currency-bundle.php:72
msgid "Guinean franc"
msgstr "Frank gwinejski"

#: includes/bundles/currency-bundle.php:73
msgid "Guatemalan quetzal"
msgstr "Quetzal gwatemalski"

#: includes/bundles/currency-bundle.php:74
msgid "Guyanese dollar"
msgstr "Dolar gujański"

#: includes/bundles/currency-bundle.php:75
msgid "Hong Kong dollar"
msgstr "Dolar hongkoński"

#: includes/bundles/currency-bundle.php:76
msgid "Honduran lempira"
msgstr "Lempira honduraska"

#: includes/bundles/currency-bundle.php:77
msgid "Croatian kuna"
msgstr "Kuna chorwacka"

#: includes/bundles/currency-bundle.php:78
msgid "Haitian gourde"
msgstr "Gourde haitański"

#: includes/bundles/currency-bundle.php:79
msgid "Hungarian forint"
msgstr "Forint węgierski"

#: includes/bundles/currency-bundle.php:80
msgid "Indonesian rupiah"
msgstr "Rupia indonezyjska"

#: includes/bundles/currency-bundle.php:81
msgid "Israeli new shekel"
msgstr "Nowy szekel izraelski"

#: includes/bundles/currency-bundle.php:82
msgid "Manx pound"
msgstr "Funt manx"

#: includes/bundles/currency-bundle.php:83
msgid "Indian rupee"
msgstr "Rupia indyjska"

#: includes/bundles/currency-bundle.php:84
msgid "Iraqi dinar"
msgstr "Dinar iracki"

#: includes/bundles/currency-bundle.php:85
msgid "Iranian rial"
msgstr "Rial irański"

#: includes/bundles/currency-bundle.php:86
msgid "Iranian toman"
msgstr "Toman irański"

#: includes/bundles/currency-bundle.php:87
msgid "Icelandic kr&oacute;na"
msgstr "Korona islandzka"

#: includes/bundles/currency-bundle.php:88
msgid "Jersey pound"
msgstr "Funt jerseyjski"

#: includes/bundles/currency-bundle.php:89
msgid "Jamaican dollar"
msgstr "Dolar jamajski"

#: includes/bundles/currency-bundle.php:90
msgid "Jordanian dinar"
msgstr "Dinar jordański"

#: includes/bundles/currency-bundle.php:91
msgid "Japanese yen"
msgstr "Japoński jen"

#: includes/bundles/currency-bundle.php:92
msgid "Kenyan shilling"
msgstr "Szyling kenijski"

#: includes/bundles/currency-bundle.php:93
msgid "Kyrgyzstani som"
msgstr "Som kirgizański"

#: includes/bundles/currency-bundle.php:94
msgid "Cambodian riel"
msgstr "Riel kambodżański"

#: includes/bundles/currency-bundle.php:95
msgid "Comorian franc"
msgstr "Frank komoryjski"

#: includes/bundles/currency-bundle.php:96
msgid "North Korean won"
msgstr "Won północnokoreański"

#: includes/bundles/currency-bundle.php:97
msgid "South Korean won"
msgstr "Won południowokoreański"

#: includes/bundles/currency-bundle.php:98
msgid "Kuwaiti dinar"
msgstr "Dinar kuwejcki"

#: includes/bundles/currency-bundle.php:99
msgid "Cayman Islands dollar"
msgstr "Dolar wysp Kajmanów"

#: includes/bundles/currency-bundle.php:100
msgid "Kazakhstani tenge"
msgstr "Tenge kazachstański"

#: includes/bundles/currency-bundle.php:101
msgid "Lao kip"
msgstr "Kip laotański"

#: includes/bundles/currency-bundle.php:102
msgid "Lebanese pound"
msgstr "Funt libański"

#: includes/bundles/currency-bundle.php:103
msgid "Sri Lankan rupee"
msgstr "Rupia lankijska"

#: includes/bundles/currency-bundle.php:104
msgid "Liberian dollar"
msgstr "Dolar liberyjski"

#: includes/bundles/currency-bundle.php:105
msgid "Lesotho loti"
msgstr "Loti lesotyjski"

#: includes/bundles/currency-bundle.php:106
msgid "Libyan dinar"
msgstr "Dinar libijski"

#: includes/bundles/currency-bundle.php:107
msgid "Moroccan dirham"
msgstr "Dirham marokański"

#: includes/bundles/currency-bundle.php:108
msgid "Moldovan leu"
msgstr "Leu mołdawski"

#: includes/bundles/currency-bundle.php:109
msgid "Malagasy ariary"
msgstr "Ariary madagaskarskie"

#: includes/bundles/currency-bundle.php:110
msgid "Macedonian denar"
msgstr "Denar macedoński"

#: includes/bundles/currency-bundle.php:111
msgid "Burmese kyat"
msgstr "Kyat birmański"

#: includes/bundles/currency-bundle.php:112
msgid "Mongolian t&ouml;gr&ouml;g"
msgstr "Tögrög mongolski"

#: includes/bundles/currency-bundle.php:113
msgid "Macanese pataca"
msgstr "Pataca makauska"

#: includes/bundles/currency-bundle.php:114
msgid "Mauritanian ouguiya"
msgstr "Ouguiya mauretańska"

#: includes/bundles/currency-bundle.php:115
msgid "Mauritian rupee"
msgstr "Rupia maurytyjska"

#: includes/bundles/currency-bundle.php:116
msgid "Maldivian rufiyaa"
msgstr "Rufiyaa malediwska"

#: includes/bundles/currency-bundle.php:117
msgid "Malawian kwacha"
msgstr "Kwacha malawijska"

#: includes/bundles/currency-bundle.php:118
msgid "Mexican peso"
msgstr "Peso meksykańskie"

#: includes/bundles/currency-bundle.php:119
msgid "Malaysian ringgit"
msgstr "Ringity malezyjskie"

#: includes/bundles/currency-bundle.php:120
msgid "Mozambican metical"
msgstr "Metical mozambicki"

#: includes/bundles/currency-bundle.php:121
msgid "Namibian dollar"
msgstr "Dolar namibijski"

#: includes/bundles/currency-bundle.php:122
msgid "Nigerian naira"
msgstr "Naira nigeryjska"

#: includes/bundles/currency-bundle.php:123
msgid "Nicaraguan c&oacute;rdoba"
msgstr "Kordoba nikaraguańska"

#: includes/bundles/currency-bundle.php:124
msgid "Norwegian krone"
msgstr "Korona norweska"

#: includes/bundles/currency-bundle.php:125
msgid "Nepalese rupee"
msgstr "Rupia nepalska"

#: includes/bundles/currency-bundle.php:126
msgid "New Zealand dollar"
msgstr "Dolar nowozelandzki"

#: includes/bundles/currency-bundle.php:127
msgid "Omani rial"
msgstr "Rial omański"

#: includes/bundles/currency-bundle.php:128
msgid "Panamanian balboa"
msgstr "Balboa panamska"

#: includes/bundles/currency-bundle.php:129
msgid "Sol"
msgstr "Sol"

#: includes/bundles/currency-bundle.php:130
msgid "Papua New Guinean kina"
msgstr "Kina papuaska"

#: includes/bundles/currency-bundle.php:131
msgid "Philippine peso"
msgstr "Peso filipińskie"

#: includes/bundles/currency-bundle.php:132
msgid "Pakistani rupee"
msgstr "Rupia pakistańska"

#: includes/bundles/currency-bundle.php:133
msgid "Polish z&#x142;oty"
msgstr "Polish złoty"

#: includes/bundles/currency-bundle.php:134
msgid "Transnistrian ruble"
msgstr "Rubel naddniestrzański"

#: includes/bundles/currency-bundle.php:135
msgid "Paraguayan guaran&iacute;"
msgstr "Guaraní paragwajski;"

#: includes/bundles/currency-bundle.php:136
msgid "Qatari riyal"
msgstr "Riyal katarski"

#: includes/bundles/currency-bundle.php:137
msgid "Romanian leu"
msgstr "Lej rumuński"

#: includes/bundles/currency-bundle.php:138
msgid "Serbian dinar"
msgstr "Dinar serbski"

#: includes/bundles/currency-bundle.php:139
msgid "Russian ruble"
msgstr "Rubel rosyjski"

#: includes/bundles/currency-bundle.php:140
msgid "Rwandan franc"
msgstr "Frank rwandyjski"

#: includes/bundles/currency-bundle.php:141
msgid "Saudi riyal"
msgstr "Rial saudyjski"

#: includes/bundles/currency-bundle.php:142
msgid "Solomon Islands dollar"
msgstr "Dolar Wysp Salomona"

#: includes/bundles/currency-bundle.php:143
msgid "Seychellois rupee"
msgstr "Rupia seszelska"

#: includes/bundles/currency-bundle.php:144
msgid "Sudanese pound"
msgstr "Funt sudański"

#: includes/bundles/currency-bundle.php:145
msgid "Swedish krona"
msgstr "Korona szwedzka"

#: includes/bundles/currency-bundle.php:146
msgid "Singapore dollar"
msgstr "Dolar singapurski"

#: includes/bundles/currency-bundle.php:147
msgid "Saint Helena pound"
msgstr "Funt Wyspy Świętej Heleny"

#: includes/bundles/currency-bundle.php:148
msgid "Sierra Leonean leone"
msgstr "Leone sierra leonejski"

#: includes/bundles/currency-bundle.php:149
msgid "Somali shilling"
msgstr "Szyling somalijski"

#: includes/bundles/currency-bundle.php:150
msgid "Surinamese dollar"
msgstr "Dolar surinamski"

#: includes/bundles/currency-bundle.php:151
msgid "South Sudanese pound"
msgstr "Funt południowosudański"

#: includes/bundles/currency-bundle.php:152
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe dobra"
msgstr "Dobra Wysp Świętego Tomasza i Książęca"

#: includes/bundles/currency-bundle.php:153
msgid "Syrian pound"
msgstr "Funt syryjski"

#: includes/bundles/currency-bundle.php:154
msgid "Swazi lilangeni"
msgstr "Lilangeni szwajcarski"

#: includes/bundles/currency-bundle.php:155
msgid "Thai baht"
msgstr "Baht tajski"

#: includes/bundles/currency-bundle.php:156
msgid "Tajikistani somoni"
msgstr "Somoni tadżyckie"

#: includes/bundles/currency-bundle.php:157
msgid "Turkmenistan manat"
msgstr "Manat turkmeński"

#: includes/bundles/currency-bundle.php:158
msgid "Tunisian dinar"
msgstr "Dinar tunesiański"

#: includes/bundles/currency-bundle.php:159
msgid "Tongan pa&#x2bb;anga"
msgstr "Pa&#x2bb;anga tongijska"

#: includes/bundles/currency-bundle.php:160
msgid "Turkish lira"
msgstr "Lira turecka"

#: includes/bundles/currency-bundle.php:161
msgid "Trinidad and Tobago dollar"
msgstr "Dolar Trynidadu i Tobago"

#: includes/bundles/currency-bundle.php:162
msgid "New Taiwan dollar"
msgstr "Nowy dolar tajwański"

#: includes/bundles/currency-bundle.php:163
msgid "Tanzanian shilling"
msgstr "Szyling tanzański"

#: includes/bundles/currency-bundle.php:164
msgid "Ukrainian hryvnia"
msgstr "Hrywna ukraińska"

#: includes/bundles/currency-bundle.php:165
msgid "Ugandan shilling"
msgstr "Szyling ugandyjski"

#: includes/bundles/currency-bundle.php:166
msgid "Uruguayan peso"
msgstr "Peso urugwajskie"

#: includes/bundles/currency-bundle.php:167
msgid "Uzbekistani som"
msgstr "Som uzbecki"

#: includes/bundles/currency-bundle.php:168
msgid "Venezuelan bol&iacute;var"
msgstr "Venezuelan bolivar"

#: includes/bundles/currency-bundle.php:169
msgid "Bol&iacute;var soberano"
msgstr "Wenezuelski bolivar"

#: includes/bundles/currency-bundle.php:170
msgid "Vietnamese &#x111;&#x1ed3;ng"
msgstr "Wietnamski dong"

#: includes/bundles/currency-bundle.php:171
msgid "Vanuatu vatu"
msgstr "Watu vanuacki"

#: includes/bundles/currency-bundle.php:172
msgid "Samoan t&#x101;l&#x101;"
msgstr "Talā samoański;"

#: includes/bundles/currency-bundle.php:173
msgid "Central African CFA franc"
msgstr "Frank CFA Afryki Centralnej"

#: includes/bundles/currency-bundle.php:174
msgid "East Caribbean dollar"
msgstr "Dolar wschodniokaraibski"

#: includes/bundles/currency-bundle.php:175
msgid "West African CFA franc"
msgstr "Frank CFA Afryki Zachodniej"

#: includes/bundles/currency-bundle.php:176
msgid "CFP franc"
msgstr "Frank CFP"

#: includes/bundles/currency-bundle.php:177
msgid "Yemeni rial"
msgstr "Rial jemeński"

#: includes/bundles/currency-bundle.php:178
msgid "South African rand"
msgstr "Rand południowoafrykański"

#: includes/bundles/currency-bundle.php:179
msgid "Zambian kwacha"
msgstr "Kwacha zambijska"

#: includes/bundles/currency-bundle.php:358
msgid "Before"
msgstr "Przed"

#: includes/bundles/currency-bundle.php:359
msgid "After"
msgstr "Po"

#: includes/bundles/currency-bundle.php:360
msgid "Before with space"
msgstr "Przed ze spacją"

#: includes/bundles/currency-bundle.php:361
msgid "After with space"
msgstr "Po ze spacją"

#: includes/bundles/customer-bundle.php:97
msgid "First name is required."
msgstr "Pole Imię jest wymagane."

#: includes/bundles/customer-bundle.php:106
msgid "Last name is required."
msgstr "Pole Nazwisko jest wymagane."

#: includes/bundles/customer-bundle.php:115
msgid "Email is required."
msgstr "Pole Email jest wymagane."

#: includes/bundles/customer-bundle.php:124
msgid "Phone is required."
msgstr "Pole Telefon jest wymagane."

#: includes/bundles/customer-bundle.php:128
#: includes/views/shortcodes/checkout-view.php:650
msgid "Country of residence"
msgstr "Kraj zamieszkania"

#: includes/bundles/customer-bundle.php:133
msgid "Country is required."
msgstr "Pole Kraj jest wymagane."

#: includes/bundles/customer-bundle.php:142
msgid "Address is required."
msgstr "Pole adres jest wymagane."

#: includes/bundles/customer-bundle.php:151
msgid "City is required."
msgstr "Pole Miasto jest wymagane."

#: includes/bundles/customer-bundle.php:160
msgid "State is required."
msgstr "Pole Województwo jest wymagane."

#: includes/bundles/customer-bundle.php:169
msgid "Postcode is required."
msgstr "Pole Kod pocztowy jest wymagane."

#: includes/bundles/customer-bundle.php:178
msgid "Note is required."
msgstr "Uwaga jest wymagana."

#: includes/bundles/units-bundle.php:16
msgid "Square Meter"
msgstr "Metr kwadratowy"

#: includes/bundles/units-bundle.php:17
msgid "Square Foot"
msgstr "Stopa kwadratowa"

#: includes/bundles/units-bundle.php:18
msgid "Square Yard"
msgstr "Jard kwadratowy"

#: includes/bundles/units-bundle.php:21
msgid "m²"
msgstr ""

#: includes/bundles/units-bundle.php:22
msgid "ft²"
msgstr ""

#: includes/bundles/units-bundle.php:23
msgid "yd²"
msgstr ""

#: includes/core/helpers/price-helper.php:57
msgctxt "Zero price"
msgid "Free"
msgstr "Darmowe"

#. translators: Price per one night. Example: $99 per night
#: includes/core/helpers/price-helper.php:144
msgctxt "Price per one night. Example: $99 per night"
msgid "per night"
msgstr "za noc"

#. translators: Price for X nights. Example: $99 for 2 nights, $99 for 21 nights
#: includes/core/helpers/price-helper.php:156
msgctxt "Price for X nights. Example: $99 for 2 nights, $99 for 21 nights"
msgid "for %d nights"
msgid_plural "for %d nights"
msgstr[0] "za noc"
msgstr[1] "za %d nocy"
msgstr[2] "za %d nocy"
msgstr[3] "za %d nocy"

#: includes/crons/cron-manager.php:112
msgid "User Approval Time setted in Hotel Booking Settings"
msgstr "Czas zatwierdzenia przez użytkownika można ustawić w ustawieniach Hotel Booking"

#: includes/crons/cron-manager.php:117
msgid "Pending Payment Time set in Hotel Booking Settings"
msgstr "Czas oczekiwania płatności można ustawić w ustawieniach Hotel Booking"

#: includes/crons/cron-manager.php:122
msgid "Interval for automatic cleaning of synchronization logs."
msgstr "Odstęp czasu między automatycznym czyszczeniem logów synchronizacji."

#: includes/crons/cron-manager.php:127
msgid "Once a week"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:73
#: templates/account/bookings.php:19
#: templates/account/bookings.php:70
#: templates/create-booking/search/search-form.php:42
#: templates/edit-booking/edit-dates.php:29
#: templates/shortcodes/search/search-form.php:35
msgid "Check-in"
msgstr "Zameldować się"

#: includes/csv/bookings/bookings-exporter-helper.php:74
#: templates/account/bookings.php:20
#: templates/account/bookings.php:73
#: templates/create-booking/search/search-form.php:62
#: templates/edit-booking/edit-dates.php:38
#: templates/shortcodes/search/search-form.php:55
msgid "Check-out"
msgstr "Wymeldować się"

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/csv/bookings/bookings-exporter-helper.php:78
#: includes/post-types/rate-cpt.php:104
msgid "Rate"
msgstr "Stawka"

#: includes/csv/bookings/bookings-exporter-helper.php:79
msgid "Adults/Guests"
msgstr "Dorośli/goście"

#: includes/csv/bookings/bookings-exporter-helper.php:91
#: includes/emails/templaters/reserved-rooms-templater.php:223
#: includes/views/edit-booking/checkout-view.php:164
#: includes/views/shortcodes/checkout-view.php:291
msgid "Full Guest Name"
msgstr "Pełne imię gościa"

#: includes/csv/bookings/bookings-exporter-helper.php:92
#: includes/views/booking-view.php:141
msgid "Accommodation Subtotal"
msgstr "Suma częściowa zakwaterowania"

#: includes/csv/bookings/bookings-exporter-helper.php:93
#: includes/post-types/coupon-cpt.php:72
#: includes/views/booking-view.php:150
msgid "Accommodation Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:94
#: includes/views/booking-view.php:160
msgid "Accommodation Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:96
#: includes/views/booking-view.php:186
msgid "Accommodation Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:98
#: includes/views/booking-view.php:225
msgid "Services Subtotal"
msgstr "Podsuma usług"

#: includes/csv/bookings/bookings-exporter-helper.php:99
#: includes/views/booking-view.php:236
msgid "Services Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:100
#: includes/views/booking-view.php:248
msgid "Services Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:101
#: includes/views/booking-view.php:280
msgid "Service Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:103
#: includes/views/booking-view.php:312
msgid "Fees Subtotal"
msgstr "Suma częściowa opłat"

#: includes/csv/bookings/bookings-exporter-helper.php:104
#: includes/views/booking-view.php:321
msgid "Fees Discount"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:105
#: includes/views/booking-view.php:331
msgid "Fees Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:106
#: includes/views/booking-view.php:364
msgid "Fee Taxes Total"
msgstr ""

#: includes/csv/bookings/bookings-exporter-helper.php:108
msgid "Discount"
msgstr "Rabat"

#: includes/csv/bookings/bookings-exporter-helper.php:109
#: includes/views/booking-view.php:451
#: templates/account/bookings.php:21
#: templates/account/bookings.php:76
msgid "Total"
msgstr "Razem"

#: includes/csv/bookings/bookings-exporter-helper.php:110
msgid "Paid"
msgstr "Opłacone"

#: includes/csv/bookings/bookings-exporter-helper.php:111
#: includes/post-types/payment-cpt.php:129
#: includes/shortcodes/booking-confirmation-shortcode.php:284
msgid "Payment Details"
msgstr "Szczegóły płatności"

#: includes/csv/bookings/bookings-query.php:92
msgid "Please select columns to export."
msgstr "Proszę wybrać kolumny do wyeksportowania."

#: includes/csv/csv-export-handler.php:32
#: includes/payments/gateways/stripe-gateway.php:559
msgid "Nonce verification failed."
msgstr "Weryfikacja jednorazowa nie powiodła się."

#: includes/csv/csv-export-handler.php:50
msgid "The file does not exist."
msgstr "Plik nie istnieje."

#: includes/emails/abstract-email.php:441
msgid "Disable this email notification"
msgstr "Wyłącz to powiadomienie e-mail"

#: includes/emails/abstract-email.php:449
msgid "Subject"
msgstr "Temat"

#: includes/emails/abstract-email.php:461
msgid "Header"
msgstr "Nagłówek"

#: includes/emails/abstract-email.php:473
msgid "Email Template"
msgstr "Szablon email"

#: includes/emails/abstract-email.php:570
msgid "\"%s\" email will not be sent: there is no customer email in the booking."
msgstr "\"%s\" mail nie zostanie wysłany: w rezerwacji nie ma adresu e-mail klienta."

#: includes/emails/abstract-email.php:594
msgid "Deprecated tags in header of %s"
msgstr "Nieaktualne znaczniki w nagłówku %s"

#: includes/emails/abstract-email.php:597
msgid "Deprecated tags in subject of %s"
msgstr "Nieaktualne znaczniki w temacie %s"

#: includes/emails/abstract-email.php:600
msgid "Deprecated tags in template of %s"
msgstr "Nieaktualne znaczniki w szablonie %s"

#: includes/emails/booking/admin/base-email.php:37
msgid "Recipients"
msgstr ""

#: includes/emails/booking/admin/base-email.php:40
msgid "You can use multiple comma-separated emails"
msgstr "Możesz użyć wielu e-maili oddzielonych przecinkami"

#: includes/emails/booking/admin/base-email.php:89
msgid "\"%s\" mail was sent to admin."
msgstr "\"%s\" mail został wysłany do admina."

#: includes/emails/booking/admin/base-email.php:93
msgid "\"%s\" mail sending to admin is failed."
msgstr "\"%s\" przesyłanie do admina nie powiodło się."

#: includes/emails/booking/admin/cancelled-email.php:8
msgid "Booking Cancelled"
msgstr "Rezerwacja odwołana"

#: includes/emails/booking/admin/cancelled-email.php:12
msgid "%site_title% - Booking #%booking_id% Cancelled"
msgstr "%site_title% - rezerwacja #%booking_id% odwołana"

#: includes/emails/booking/admin/cancelled-email.php:16
msgid "Email that will be sent to Admin when customer cancels booking."
msgstr "Ten mail zostanie wysłany do admina w razie gdy klient odwoła rezerwację."

#: includes/emails/booking/admin/cancelled-email.php:20
#: includes/emails/booking/customer/cancelled-email.php:20
msgid "Cancelled Booking Email"
msgstr "Email z odwołaniem rezerwacji"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:8
#: includes/emails/booking/admin/confirmed-email.php:8
#: includes/wizard.php:134
msgid "Booking Confirmed"
msgstr "Rezerwacja potwierdzona"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:12
#: includes/emails/booking/admin/confirmed-email.php:12
msgid "%site_title% - Booking #%booking_id% Confirmed"
msgstr "%site_title% - Rezerwacja #%booking_id% potwierdzona"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:16
msgid "Email that will be sent to Admin when payment is completed."
msgstr "Mail, który zostanie wysłany do administratora po zakończeniu płatności."

#: includes/emails/booking/admin/confirmed-by-payment-email.php:20
msgid "Approved Booking Email (via payment)"
msgstr "Zatwierdzony adres e-maila dla rezerwacji (za pośrednictwem płatności)"

#: includes/emails/booking/admin/confirmed-email.php:16
msgid "Email that will be sent to Admin when customer confirms booking."
msgstr "Email, który zostanie wysłany do admina po potwierdzeniu rezerwacji przez klienta."

#: includes/emails/booking/admin/confirmed-email.php:20
#: includes/emails/booking/customer/approved-email.php:20
msgid "Approved Booking Email"
msgstr "Email z potwierdzoną rezerwacją"

#: includes/emails/booking/admin/pending-email.php:8
msgid "Confirm new booking"
msgstr "Potwierdź nową rezerwację"

#: includes/emails/booking/admin/pending-email.php:12
msgid "%site_title% - New booking #%booking_id%"
msgstr "%site_title% - Nowa rezerwacja #%booking_id%"

#: includes/emails/booking/admin/pending-email.php:16
msgid "Email that will be sent to administrator after booking is placed."
msgstr "Email, który zostanie wysłany do administratora po dokonaniu rezerwacji."

#: includes/emails/booking/admin/pending-email.php:20
msgid "Pending Booking Email"
msgstr "Email Rezerwacja w oczekiwaniu"

#: includes/emails/booking/customer/approved-email.php:8
msgid "Your booking is approved"
msgstr "Twoja rezerwacja została potwierdzona"

#: includes/emails/booking/customer/approved-email.php:12
msgid "%site_title% - Your booking #%booking_id% is approved"
msgstr "%site_title% - Twoja rezerwacja #%booking_id% została potwierdzona"

#: includes/emails/booking/customer/approved-email.php:16
msgid "Email that will be sent to customer when booking is approved."
msgstr "Email, który zostanie wysłany do klienta po potwierdzeniu rezerwacji."

#: includes/emails/booking/customer/base-email.php:55
msgid "\"%s\" mail was sent to customer."
msgstr "\"%s\" mail został wysłany do klienta."

#: includes/emails/booking/customer/base-email.php:59
msgid "\"%s\" mail sending is failed."
msgstr "\"%s\" przesyłanie nie powiodło się."

#: includes/emails/booking/customer/cancelled-email.php:8
msgid "Your booking is cancelled"
msgstr "Twoja rezerwacja została odwołana"

#: includes/emails/booking/customer/cancelled-email.php:12
msgid "%site_title% - Your booking #%booking_id% is cancelled"
msgstr "%site_title% - Twoja rezerwacja #%booking_id% została odwołana"

#: includes/emails/booking/customer/cancelled-email.php:16
msgid "Email that will be sent to customer when booking is cancelled."
msgstr "Email, który zostanie wysłany do klienta po odowołaniu rezerwacji."

#: includes/emails/booking/customer/confirmation-email.php:8
msgid "Confirm your booking"
msgstr "Potwierdź rezerwację"

#: includes/emails/booking/customer/confirmation-email.php:12
msgid "%site_title% - Confirm your booking #%booking_id%"
msgstr "%site_title% - Potwierdź rezerwację #%booking_id%"

#: includes/emails/booking/customer/confirmation-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Customer confirmation via email."
msgstr "Ta wiadomość e-mail jest wysyłana, gdy \"Tryb potwierdzenia rezerwacji\" jest ustawiony na potwierdzenie klienta za pośrednictwem poczty elektronicznej."

#: includes/emails/booking/customer/confirmation-email.php:17
#: includes/emails/booking/customer/direct-bank-transfer-email.php:43
#: includes/emails/booking/customer/pending-email.php:17
msgid "Email that will be sent to customer after booking is placed."
msgstr "E-mail, który zostanie wysłany do klienta po dokonaniu rezerwacji."

#: includes/emails/booking/customer/confirmation-email.php:21
msgid "New Booking Email (Confirmation by User)"
msgstr "Nowy email dla rezerwacji (Potwierdzenie przez klienta)"

#: includes/emails/booking/customer/direct-bank-transfer-email.php:35
msgid "Pay for your booking"
msgstr ""

#: includes/emails/booking/customer/direct-bank-transfer-email.php:39
msgid "%site_title% - Pay for your booking #%booking_id%"
msgstr ""

#: includes/emails/booking/customer/direct-bank-transfer-email.php:47
msgid "Payment Instructions Email"
msgstr ""

#: includes/emails/booking/customer/pending-email.php:8
msgid "Your booking is placed"
msgstr "Twoja rezerwacja umieszczona"

#: includes/emails/booking/customer/pending-email.php:12
msgid "%site_title% - Booking #%booking_id% is placed"
msgstr "%site_title% - Rezerwacja #%booking_id% dodana"

#: includes/emails/booking/customer/pending-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Admin confirmation."
msgstr "Ten e-mail zostanie wysłany, gdy jest ustawiony tryb \"Potwierdzenie rezerwacji\" w ustawieniach Potwierdzenie przez admina."

#: includes/emails/booking/customer/pending-email.php:21
msgid "New Booking Email (Confirmation by Admin)"
msgstr "Nowy email dla rezerwacji (Potwierdzenie przez admina)"

#: includes/emails/booking/customer/registration-email.php:8
msgid "Welcome"
msgstr ""

#: includes/emails/booking/customer/registration-email.php:12
msgid "%site_title% - account details"
msgstr ""

#: includes/emails/booking/customer/registration-email.php:16
msgid "Email that will be sent to a customer after they registered on your site."
msgstr ""

#: includes/emails/booking/customer/registration-email.php:20
msgid "Customer Registration Email"
msgstr ""

#: includes/emails/templaters/abstract-templater.php:77
msgid "Email Tags"
msgstr ""

#: includes/emails/templaters/abstract-templater.php:87
#: includes/emails/templaters/abstract-templater.php:89
msgid "Deprecated."
msgstr "Nieaktualne."

#: includes/emails/templaters/abstract-templater.php:101
msgid "none"
msgstr "żadne"

#: includes/emails/templaters/cancellation-booking-templater.php:50
msgid "User Cancellation Link"
msgstr "Link Odwołanie przez użytkownika"

#: includes/emails/templaters/email-templater.php:109
msgid "Site title (set in Settings > General)"
msgstr "Tytuł strony (dodawany w Ustawienia > Ogólne)"

#: includes/emails/templaters/email-templater.php:124
#: includes/post-types/payment-cpt.php:232
msgid "Booking ID"
msgstr "ID numer rezerwacji"

#: includes/emails/templaters/email-templater.php:128
msgid "Booking Edit Link"
msgstr "Link do edycji rezerwacji"

#: includes/emails/templaters/email-templater.php:132
msgid "Booking Total Price"
msgstr "Łączna cena rezerwacji"

#: includes/emails/templaters/email-templater.php:153
#: includes/emails/templaters/email-templater.php:296
msgid "Customer First Name"
msgstr "Imię klienta"

#: includes/emails/templaters/email-templater.php:157
#: includes/emails/templaters/email-templater.php:300
msgid "Customer Last Name"
msgstr "Nazwisko klienta"

#: includes/emails/templaters/email-templater.php:161
#: includes/emails/templaters/email-templater.php:304
msgid "Customer Email"
msgstr "E-mail klienta"

#: includes/emails/templaters/email-templater.php:165
#: includes/emails/templaters/email-templater.php:308
msgid "Customer Phone"
msgstr "Numer telefonu klienta"

#: includes/emails/templaters/email-templater.php:169
#: includes/emails/templaters/email-templater.php:312
msgid "Customer Country"
msgstr "Kraj klienta"

#: includes/emails/templaters/email-templater.php:173
#: includes/emails/templaters/email-templater.php:316
msgid "Customer Address"
msgstr "Adres klienta"

#: includes/emails/templaters/email-templater.php:177
#: includes/emails/templaters/email-templater.php:320
msgid "Customer City"
msgstr "Miasto klienta"

#: includes/emails/templaters/email-templater.php:181
#: includes/emails/templaters/email-templater.php:324
msgid "Customer State/County"
msgstr "Stan klienta / hrabstwo"

#: includes/emails/templaters/email-templater.php:185
#: includes/emails/templaters/email-templater.php:328
msgid "Customer Postcode"
msgstr "Kod pocztowy klienta"

#: includes/emails/templaters/email-templater.php:194
msgid "Reserved Accommodations Details"
msgstr "Zarezerwowane pokoje Szczegóły"

#: includes/emails/templaters/email-templater.php:216
#: includes/views/create-booking/checkout-view.php:15
#: includes/views/shortcodes/checkout-view.php:164
#: templates/shortcodes/booking-details/booking-details.php:18
msgid "Booking Details"
msgstr "Szczegół rezerwacji"

#: includes/emails/templaters/email-templater.php:230
msgid "Confirmation Link"
msgstr "Link potwierdzenia"

#: includes/emails/templaters/email-templater.php:234
msgid "Confirmation Link Expiration Time ( UTC )"
msgstr "Czas wygaśnięcia linka do potwierdzenia ( UTC )"

#: includes/emails/templaters/email-templater.php:248
msgid "Cancellation Details (if enabled)"
msgstr "Szczegóły anulowania (jeśli jest włączona)"

#: includes/emails/templaters/email-templater.php:262
msgid "The total amount of payment"
msgstr ""

#: includes/emails/templaters/email-templater.php:266
msgid "The unique ID of payment"
msgstr "Unikatowy numer Id płatności"

#: includes/emails/templaters/email-templater.php:270
msgid "The method of payment"
msgstr "Metoda płatności"

#: includes/emails/templaters/email-templater.php:274
msgid "Payment instructions"
msgstr "Instrukcje dotyczące płatności"

#: includes/emails/templaters/email-templater.php:288
msgid "User login"
msgstr ""

#: includes/emails/templaters/email-templater.php:292
msgid "User password"
msgstr ""

#: includes/emails/templaters/email-templater.php:332
msgid "Link to My Account page"
msgstr ""

#: includes/emails/templaters/email-templater.php:562
#: includes/upgrader.php:868
#: includes/wizard.php:213
msgid "My Account"
msgstr ""

#: includes/emails/templaters/reserved-rooms-templater.php:191
msgid "Accommodation Type Link"
msgstr "Link do typu pokoju"

#: includes/emails/templaters/reserved-rooms-templater.php:195
msgid "Accommodation Type Title"
msgstr "Tytuł typu pokoju"

#: includes/emails/templaters/reserved-rooms-templater.php:199
msgid "Accommodation Title"
msgstr ""

#: includes/emails/templaters/reserved-rooms-templater.php:203
msgid "Accommodation Type Categories"
msgstr "Kategoria typów obiektów"

#: includes/emails/templaters/reserved-rooms-templater.php:207
msgid "Accommodation Type Bed"
msgstr "Typ łóżka w pokoju"

#: includes/emails/templaters/reserved-rooms-templater.php:211
msgid "Accommodation Rate Title"
msgstr "Tytuł oceny pokoju"

#: includes/emails/templaters/reserved-rooms-templater.php:215
msgid "Accommodation Rate Description"
msgstr "Opisanie oceny pokoju"

#: includes/emails/templaters/reserved-rooms-templater.php:219
msgid "Sequential Number of Accommodation"
msgstr "Sekwencyjna liczba miejsc noclegowych"

#: includes/entities/coupon.php:370
msgid "This coupon has expired."
msgstr "Ten kupon rabatowy wygasł."

#: includes/entities/coupon.php:374
msgid "Sorry, this coupon is not applicable to your booking contents."
msgstr "Niestety ten kupon rabatowy nie dotyczy Twojej rezerwacji."

#: includes/entities/coupon.php:378
msgid "Coupon usage limit has been reached."
msgstr "Został osiągnięty limit wykorzystania kuponu rabatowego."

#: includes/entities/reserved-service.php:98
msgid " &#215; %d night"
msgid_plural " &#215; %d nights"
msgstr[0] " &#215; %d noc"
msgstr[1] " &#215; %d nocy"
msgstr[2] " &#215; %d nocy"
msgstr[3] ""

#: includes/entities/reserved-service.php:103
#: includes/shortcodes/search-results-shortcode.php:904
msgid "%d adult"
msgid_plural "%d adults"
msgstr[0] "%d dorosły"
msgstr[1] "%d dorosłych"
msgstr[2] "%d dorosli"
msgstr[3] ""

#: includes/entities/reserved-service.php:105
#: includes/shortcodes/search-results-shortcode.php:896
#: includes/shortcodes/search-results-shortcode.php:900
msgid "%d guest"
msgid_plural "%d guests"
msgstr[0] "%d gość"
msgstr[1] "%d gości"
msgstr[2] "%d gości"
msgstr[3] ""

#: includes/entities/reserved-service.php:110
msgid " &#215; %d time"
msgid_plural " &#215; %d times"
msgstr[0] " &#215; %d raz"
msgstr[1] " &#215; %d razy"
msgstr[2] " &#215; %d razy"
msgstr[3] ""

#: includes/entities/service.php:195
msgid "Per Instance"
msgstr "Na instancję"

#: includes/i-cal/background-processes/background-synchronizer.php:34
msgid "Maximum execution time is set to %d seconds."
msgstr "Maksymalny czas wykonania jest ustawiony na %d sekund."

#: includes/i-cal/background-processes/background-synchronizer.php:80
msgid "%d URL pulled for parsing."
msgid_plural "%d URLs pulled for parsing."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: includes/i-cal/background-processes/background-synchronizer.php:82
msgid "Skipped. No URLs found for parsing."
msgstr "Pominięte. Brak znalezionych adresów URL dla parsowania."

#: includes/i-cal/background-processes/background-uploader.php:64
msgid "Cannot read uploaded file"
msgstr "Nie udało się odczytać przesłanego pliku"

#: includes/i-cal/background-processes/background-worker.php:327
msgctxt "%s - calendar URI or calendar filename"
msgid "%1$d event found in calendar %2$s"
msgid_plural "%1$d events found in calendar %2$s"
msgstr[0] "%1$d wydarzenie znaleziono w kalendarzu %2$s"
msgstr[1] "%1$d wdarzenia znaleziono w kalendarzu %2$s"
msgstr[2] "%1$d wdarzeń znaleziono w kalendarzu %2$s"
msgstr[3] ""

#: includes/i-cal/background-processes/background-worker.php:357
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar source is empty (%s)"
msgstr "Źródło kalendarza jest puste (%s)"

#: includes/i-cal/background-processes/background-worker.php:370
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar file is not empty, but there are no events in %s"
msgstr "Plik kalendarza nie jest pusty, ale nie ma żadnych wydarzeń w %s"

#: includes/i-cal/background-processes/background-worker.php:403
msgid "We will need to check %d previous booking after importing and remove it if the booking is outdated."
msgid_plural "We will need to check %d previous bookings after importing and remove the outdated ones."
msgstr[0] "Po zaimportowaniu będziemy musieli sprawdzić %d poprzednią rezerwację i usunąć ją, jeśli jest nieaktualna."
msgstr[1] "Po zaimportowaniu będziemy musieli sprawdzić %d poprzednie rezerwacje i usunąć je, jeśli są nieaktualne."
msgstr[2] "Po zaimportowaniu będziemy musieli sprawdzić %d poprzednie rezerwacje i usunąć je, jeśli są nieaktualne."
msgstr[3] ""

#: includes/i-cal/background-processes/background-worker.php:425
msgid "Error while loading calendar (%1$s): %2$s"
msgstr "Podczas ładowania kalendarza wystąpił błąd (%1$s): %2$s"

#: includes/i-cal/background-processes/background-worker.php:427
msgctxt "%s - error description"
msgid "Parse error. %s"
msgstr "Błąd przetwarzania. %s"

#: includes/i-cal/background-processes/background-worker.php:468
msgid "Skipped. Outdated booking #%d already removed."
msgstr "Pominięto. Nieaktualna rezerwacja #%d została już usunięta."

#: includes/i-cal/background-processes/background-worker.php:475
msgid "Skipped. Booking #%d updated with new data."
msgstr "Pominięto. Rezerwacja #%d zaktualizowana o nowe dane."

#: includes/i-cal/background-processes/background-worker.php:497
msgid "The outdated booking #%d has been removed."
msgstr "Nieaktualna rezerwacja #%d została usunięta."

#: includes/i-cal/importer.php:104
msgid "Skipped. Event from %1$s to %2$s has passed."
msgstr ""

#: includes/i-cal/importer.php:120
msgid "New booking #%1$d. The dates from %2$s to %3$s are now blocked."
msgstr ""

#: includes/i-cal/importer.php:140
msgid "Success. Booking #%d updated with new data."
msgstr "Sukces. Rezerwacja nr %d została zaktualizowana o nowe dane."

#: includes/i-cal/importer.php:148
msgid "Skipped. The dates from %1$s to %2$s are already blocked."
msgstr "Pominięto. Daty od %1$s do %2$s są już zablokowane."

#: includes/i-cal/importer.php:164
msgid "Success. Booking #%1$d updated with new data. Removed %2$d outdated booking."
msgid_plural "Success. Booking #%1$d updated with new data. Removed %2$d outdated bookings."
msgstr[0] "Sukces. Rezerwacja nr %1$d została zaktualizowana o nowe dane. Usunięto %2$d nieaktualną rezerwację."
msgstr[1] "Sukces. Rezerwacja nr %1$d została zaktualizowana o nowe dane. Usunięto %2$d nieaktualne rezerwacje."
msgstr[2] "Sukces. Rezerwacja nr %1$d została zaktualizowana o nowe dane. Usunięto %2$d nieaktualne rezerwacje."
msgstr[3] ""

#: includes/i-cal/importer.php:166
msgid "Success. Booking #%1$d updated with new data."
msgstr "Sukces. Rezerwacja nr %1$d została zaktualizowana o nowe dane."

#: includes/i-cal/importer.php:177
msgid "Cannot import new event. Dates from %1$s to %2$s are partially blocked by booking %3$s."
msgid_plural "Cannot import new event. Dates from %1$s to %2$s are partially blocked by bookings %3$s."
msgstr[0] "Nie można importować nowego wydarzenia. Daty od %1$s do %2$s są częściowo blokowane przez rezerwacje %3$s."
msgstr[1] "Nie można importować nowego wydarzenia. Daty od %1$s do %2$s są częściowo blokowane przez rezerwacje %3$s."
msgstr[2] "Nie można importować nowego wydarzenia. Daty od %1$s do %2$s są częściowo blokowane przez rezerwacje %3$s."
msgstr[3] ""

#: includes/i-cal/importer.php:233
msgid "Booking imported with UID %1$s.<br />Summary: %2$s.<br />Description: %3$s.<br />Source: %4$s."
msgstr "Rezerwacja zaimportowana z UID %1$s.<br />Podsumowanie: %2$s.<br />Opis: %3$s.<br />Źródło: %4$s."

#: includes/i-cal/logs-handler.php:25
msgid "Process Information"
msgstr "Informacje o procesie"

#: includes/i-cal/logs-handler.php:35
msgid "Total bookings: %s"
msgstr "Ogólna ilość rezerwacji: %s"

#: includes/i-cal/logs-handler.php:37
msgid "Success bookings: %s"
msgstr "Udane rezerwacje: %s"

#: includes/i-cal/logs-handler.php:39
msgid "Skipped bookings: %s"
msgstr "Pomijane rezerwacje: %s"

#: includes/i-cal/logs-handler.php:41
msgid "Failed bookings: %s"
msgstr "Nieudane rezerwacje: %s"

#: includes/i-cal/logs-handler.php:43
msgid "Removed bookings: %s"
msgstr "Usunięte rezerwacje: %s"

#: includes/i-cal/logs-handler.php:87
msgid "Expand All"
msgstr "Rozwiń wszystko"

#: includes/i-cal/logs-handler.php:91
msgid "Collapse All"
msgstr "Zwiń wszystko"

#: includes/i-cal/logs-handler.php:138
msgid "All done! %1$d booking was successfully added."
msgid_plural "All done! %1$d bookings were successfully added."
msgstr[0] "Wszystko gotowe! %1$d rezerwacja została pomyślnie dodana."
msgstr[1] "Wszystko gotowe! %1$d rezerwacje zostały pomyślnie dodane."
msgstr[2] "Wszystko gotowe! %1$d rezerwacji zostały pomyślnie dodane."
msgstr[3] ""

#: includes/i-cal/logs-handler.php:139
msgid " There was %2$d failure."
msgid_plural " There were %2$d failures."
msgstr[0] " Wystąpił %2$d błąd."
msgstr[1] " Wystąpiło %2$d błędy/ów."
msgstr[2] " Wystąpiło %2$d błędy/ów."
msgstr[3] ""

#: includes/license-notice.php:87
#: includes/license-notice.php:160
msgid "Your License Key is not active. Please, <a href=\"%s\">activate your License Key</a> to get plugin updates."
msgstr ""

#: includes/license-notice.php:152
msgid "Dismiss "
msgstr "Odrzuć "

#: includes/linked-rooms.php:31
msgid "Blocked because the linked accommodation is booked"
msgstr ""

#: includes/notices.php:138
#: includes/notices.php:156
#: includes/wizard.php:33
msgid "Hotel Booking Plugin"
msgstr "Wtyczka Hotel Booking"

#: includes/notices.php:139
msgid "Your database is being updated in the background."
msgstr "Twoja baza danych jest aktualizowana w tle."

#: includes/notices.php:141
msgid "Taking a while? Click here to run it now."
msgstr "Zajmuje dużo czasu? Kliknij tutaj, aby uruchomić teraz."

#: includes/notices.php:157
msgid "Add \"Booking Confirmation\" shortcode to your \"Booking Confirmed\" and \"Reservation Received\" pages to show more details about booking or payment.<br/>Click \"Update Pages\" to apply all changes automatically or skip this notice and add \"Booking Confirmation\" shortcode manually.<br/><b><em>This action will replace the whole content of the pages.</em></b>"
msgstr "Dodaj \"Potwierdzenie rezerwacji\" do stron \"Potwierdzona rezerwacja\" i \"Otrzymano rezerwację\", aby wyświetlić więcej szczegółów dotyczących rezerwacji lub płatności.<br/>Kliknij \"Aktualizuj strony\", aby zastosować wszystkie zmiany automatycznie lub pomiń to powiadomienie i dodaj ręcznie krótki kod \"Potwierdzenie rezerwacji\".<br/><b><em>Ta akcja spowoduje zastąpienie całej zawartości stron.</em></b>"

#: includes/notices.php:159
msgid "Update Pages"
msgstr "Aktualizuj strony"

#: includes/notices.php:161
#: includes/wizard.php:36
msgid "Skip"
msgstr "Pominąć"

#: includes/payments/gateways/bank-gateway.php:82
#: includes/payments/gateways/bank-gateway.php:91
msgid "Direct Bank Transfer"
msgstr "Bezpośredni przelew bankowy"

#: includes/payments/gateways/bank-gateway.php:92
msgid "Make your payment directly into our bank account. Please use your Booking ID as the payment reference."
msgstr "Dokonaj płatności bezpośrednio na nasze konto bankowe. Użyj swojego identyfikatora rezerwacji jako odniesienia do płatności."

#: includes/payments/gateways/bank-gateway.php:118
msgid "Enable Auto-Abandonment"
msgstr ""

#: includes/payments/gateways/bank-gateway.php:119
msgid "Automatically abandon bookings and release reserved slots if payment is not received within a specified time period. You need to manually set the status of paid payments to Completed to avoid automatic abandonment."
msgstr ""

#: includes/payments/gateways/bank-gateway.php:128
msgid "Period of time in hours a user has to pay for a booking. Unpaid bookings become abandoned, and accommodations become available for others."
msgstr ""

#: includes/payments/gateways/beanstream-gateway.php:54
msgid "Beanstream/Bambora"
msgstr ""

#: includes/payments/gateways/beanstream-gateway.php:59
#: includes/payments/gateways/braintree-gateway.php:149
#: includes/payments/gateways/paypal-gateway.php:72
#: includes/payments/gateways/two-checkout-gateway.php:70
msgid "Use the card number %1$s with CVC %2$s and a valid expiration date to test a payment."
msgstr "Użyj karty %1$s z CVC %2$s i ważną datą wygaśnięcia aby przetestować płatność."

#: includes/payments/gateways/beanstream-gateway.php:66
msgid "Pay by Card (Beanstream)"
msgstr "Opłata kartą kredytową (Beanstream)"

#: includes/payments/gateways/beanstream-gateway.php:67
msgid "Pay with your credit card via Beanstream."
msgstr "Opłata kartą kredytową przez Beanstream."

#: includes/payments/gateways/beanstream-gateway.php:85
#: includes/payments/gateways/braintree-gateway.php:194
#: includes/payments/gateways/stripe-gateway.php:226
msgid "%1$s is enabled, but the <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "%1$s jest włączony, ale opcja <a href=\"%2$s\">Force Secure Checkout</a> jest wyłączona. Włącz SSL i upewnij się, że serwer ma prawidłowy certyfikat SSL. W przeciwnym razie, %1$s będzie działać tylko w trybie testowym."

#: includes/payments/gateways/beanstream-gateway.php:87
#: includes/payments/gateways/braintree-gateway.php:196
#: includes/payments/gateways/stripe-gateway.php:228
msgid "The <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "Stripe jest włączony, ale opcja <a href=\"%2$s\">Force Secure Checkout</a> jest wyłączona. Trzeba włączyć SSL i sprawdzić czy serwer posiada ważny certyfikat SSL. W przeciwnym razie, %1$s będzie działał tylko w trybie testowym."

#: includes/payments/gateways/beanstream-gateway.php:90
msgid "Beanstream"
msgstr ""

#: includes/payments/gateways/beanstream-gateway.php:103
#: includes/payments/gateways/braintree-gateway.php:212
msgid "Merchant ID"
msgstr "Identyfikator sprzedawcy"

#: includes/payments/gateways/beanstream-gateway.php:105
msgid "Your Merchant ID can be found in the top-right corner of the screen after logging in to the Beanstream Back Office"
msgstr "Identyfikator sprzedawcy można znaleźć w prawej górnej części ekranu po zalogowaniu się do Beanstream Back Office"

#: includes/payments/gateways/beanstream-gateway.php:112
msgid "Payments Passcode"
msgstr "Kod dostępu do płatności"

#: includes/payments/gateways/beanstream-gateway.php:114
msgid "To generate the passcode, navigate to Administration > Account Settings > Order Settings in the sidebar, then scroll to Payment Gateway > Security/Authentication"
msgstr "Aby wygenerować kod dostępu, przejdź do sekcji Administracja > Ustawienia konta > stawienia zlecenia na pasku bocznym, potem przejdź do Bramki płatności > Bezpieczeństwo / Uwierzytelnianie"

#: includes/payments/gateways/beanstream-gateway.php:163
msgid "Beanstream Payment Error: %s"
msgstr "Błąd płatności przez Beanstreem: %s"

#: includes/payments/gateways/beanstream-gateway.php:201
msgid "Payment single use token is required."
msgstr "Do dokonania płatności jest wzmagany token do jednorazowego użytku."

#: includes/payments/gateways/braintree-gateway.php:142
#: includes/payments/gateways/braintree-gateway.php:199
msgid "Braintree"
msgstr ""

#: includes/payments/gateways/braintree-gateway.php:155
#: includes/payments/gateways/stripe-gateway.php:86
msgid "Webhooks Destination URL: %s"
msgstr "Webhooks docelowy URL: %s"

#: includes/payments/gateways/braintree-gateway.php:168
msgid "Pay by Card (Braintree)"
msgstr "Opłata kartą kredytową (Braintree)"

#: includes/payments/gateways/braintree-gateway.php:169
msgid "Pay with your credit card via Braintree."
msgstr "Zapłać kartą kredytową przez Braintree."

#: includes/payments/gateways/braintree-gateway.php:189
msgid "Braintree gateway cannot be enabled due to some problems: %s"
msgstr "Brama Braintree nie może być włączona z powodu problemów: %s"

#: includes/payments/gateways/braintree-gateway.php:214
msgid "In your Braintree account select Account > My User > View Authorizations."
msgstr "Na koncie Braintree wybierz opcję Konto > Użytkownik > Wyświetl autoryzacje."

#: includes/payments/gateways/braintree-gateway.php:221
#: includes/payments/gateways/stripe-gateway.php:284
msgid "Public Key"
msgstr "Publiczny klucz"

#: includes/payments/gateways/braintree-gateway.php:229
msgid "Private Key"
msgstr "Klucz prywatny"

#: includes/payments/gateways/braintree-gateway.php:237
msgid "Merchant Account ID"
msgstr "Identyfikator konta sprzedawcy"

#: includes/payments/gateways/braintree-gateway.php:238
msgid "In case the site currency differs from default currency in your Braintree account, you can set specific merchant account to avoid <a href=\"%s\">complications with currencty conversions</a>. Otherwise leave the field empty."
msgstr "W przypadku, gdy waluta serwisu różni się od waluty domyślnej na koncie Braintree, można ustawić określone konto handlowe, aby uniknąć powikłań związanych z <a href=\"%s\">konwersjami waluty</a>. W przeciwnym razie zostaw pole puste."

#: includes/payments/gateways/braintree-gateway.php:293
msgid "Braintree submitted for settlement (Transaction ID: %s)"
msgstr "Braintree złożył wniosek o rozliczenie (Identyfikator transakcji: %s)"

#: includes/payments/gateways/braintree-gateway.php:303
msgid "Braintree Payment Error: %s"
msgstr "Błąd płatności Braintree: %s"

#: includes/payments/gateways/braintree-gateway.php:330
msgid "Payment method nonce is required."
msgstr "Nie wymagane metody płatności."

#: includes/payments/gateways/braintree/webhook-listener.php:116
msgid "Payment dispute opened"
msgstr "Spór o płatność został otwarty"

#: includes/payments/gateways/braintree/webhook-listener.php:121
msgid "Payment dispute lost"
msgstr "Przegrany spor płatniczy"

#: includes/payments/gateways/braintree/webhook-listener.php:126
msgid "Payment dispute won"
msgstr "Wygrano spór płatniczy"

#: includes/payments/gateways/braintree/webhook-listener.php:143
msgid "Payment refunded in Braintree"
msgstr "Płatność została zwrócona w Braintree"

#: includes/payments/gateways/braintree/webhook-listener.php:147
msgid "Braintree transaction voided"
msgstr "Transakcja Braintree została anulowana"

#: includes/payments/gateways/cash-gateway.php:45
#: includes/payments/gateways/cash-gateway.php:53
msgid "Pay on Arrival"
msgstr "Zapłać przy przyjeździe"

#: includes/payments/gateways/cash-gateway.php:54
msgid "Pay with cash on arrival."
msgstr "Zapłać gotówką w dniu przyjazdu."

#: includes/payments/gateways/gateway.php:301
msgid "%s is a required field."
msgstr "%s jest polem wymaganym."

#: includes/payments/gateways/gateway.php:314
msgid "%s is not a valid email address."
msgstr "%s nie jest prawidłowym adresem e-mail."

#. translators: %s is the payment gateway title.
#: includes/payments/gateways/gateway.php:472
msgid "Enable \"%s\""
msgstr "Włącz \"%s\""

#: includes/payments/gateways/gateway.php:482
msgid "Test Mode"
msgstr "Tryb testowy"

#: includes/payments/gateways/gateway.php:483
msgid "Enable Sandbox Mode"
msgstr "Włącz tryb Sandbox"

#: includes/payments/gateways/gateway.php:485
msgid "Sandbox can be used to test payments."
msgstr "Sandbox może być stosowany do testowania płatności."

#: includes/payments/gateways/gateway.php:496
msgid "Payment method title that the customer will see on your website."
msgstr "Tytuł metody płatności, jaki klient zobaczy na Twojej stronie."

#: includes/payments/gateways/gateway.php:506
msgid "Payment method description that the customer will see on your website."
msgstr "Opisanie metody płatności, jakie klient zobaczy na Twojej stronie."

#: includes/payments/gateways/gateway.php:516
msgid "Instructions"
msgstr "Instrukcje"

#: includes/payments/gateways/gateway.php:518
msgid "Instructions for a customer on how to complete the payment."
msgstr "Instrukcje dla klienta dotyczące sposobu realizacji płatności."

#: includes/payments/gateways/gateway.php:543
msgid "Reservation #%d"
msgstr "Rezerwacja nr %d"

#: includes/payments/gateways/gateway.php:545
msgid "Accommodation(s) reservation"
msgstr "Rezerwacja miejsc noclegowych"

#: includes/payments/gateways/manual-gateway.php:14
#: includes/payments/gateways/manual-gateway.php:19
msgid "Manual Payment"
msgstr "Płatność ręczną"

#: includes/payments/gateways/paypal-gateway.php:67
#: includes/payments/gateways/paypal-gateway.php:80
msgid "PayPal"
msgstr ""

#: includes/payments/gateways/paypal-gateway.php:81
msgid "Pay via PayPal"
msgstr "Zapłać przez PayPal"

#: includes/payments/gateways/paypal-gateway.php:117
msgid "Paypal Business Email"
msgstr "Biznesowy Paypal Email"

#: includes/payments/gateways/paypal-gateway.php:125
msgid "Disable IPN Verification"
msgstr "Wyłącz weryfikację IPN"

#: includes/payments/gateways/paypal-gateway.php:127
msgid "Specify an IPN listener for a specific payment instead of the listeners specified in your PayPal Profile."
msgstr "Określ detektor IPN dla określonej płatności zamiast detektorów, określonych w profilu PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:164
msgid "Payment %s via IPN."
msgstr "Płatności %s przez IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:183
msgid "Payment failed due to invalid PayPal business email."
msgstr "Płatność nie powiodła się z powodu nieprawidłowego biznesowego PayPal e-mail."

#: includes/payments/gateways/paypal/ipn-listener.php:200
msgid "Payment failed due to invalid currency in PayPal IPN."
msgstr "Płatność nie powiodła się z powodu nieprawidłowej waluty w PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:215
msgid "Payment failed due to invalid amount in PayPal IPN."
msgstr "Płatność nie powiodła się z powodu nieprawidłowej kwoty w PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:230
msgid "Payment failed due to invalid purchase key in PayPal IPN."
msgstr "Płatność nie powiodła się z powodu nieprawidłowego klucza zakupu w PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:302
msgid "Payment made via eCheck and will clear automatically in 5-8 days."
msgstr "Opłata dokonana za pośrednictwem systemu eCheck i zostanie automatycznie skasowana przez 5-8 dni."

#: includes/payments/gateways/paypal/ipn-listener.php:307
msgid "Payment requires a confirmed customer address and must be accepted manually through PayPal."
msgstr "Płatność wymaga potwierdzonego adresu klienta i musi być zaakceptowana ręcznie za pośrednictwem systemu PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:312
msgid "Payment must be accepted manually through PayPal due to international account regulations."
msgstr "Płatność musi być zaakceptowana ręcznie za pośrednictwem systemu PayPal ze względu na przepisy rachunków międzynarodowych."

#: includes/payments/gateways/paypal/ipn-listener.php:317
msgid "Payment received in non-shop currency and must be accepted manually through PayPal."
msgstr "Płatność otrzymana w nie ustawionej dla sklepu walucie i musi być zaakceptowane ręcznie za pośrednictwem systemu PayPal."

#: includes/payments/gateways/paypal/ipn-listener.php:323
msgid "Payment is being reviewed by PayPal staff as high-risk or in possible violation of government regulations."
msgstr "Płatność jest sprawdzany przez pracowników PayPal jako operacja wysokiego ryzyka lub ewentualne naruszenie przepisów państwowych."

#: includes/payments/gateways/paypal/ipn-listener.php:328
msgid "Payment was sent to unconfirmed or non-registered email address."
msgstr "Płatność została wysłana na niepotwierdzony lub nie zarejestrowany adres e-mail."

#: includes/payments/gateways/paypal/ipn-listener.php:333
msgid "PayPal account must be upgraded before this payment can be accepted."
msgstr "Konto PayPal musi być uaktualnione aby ta opłata była przyjęta."

#: includes/payments/gateways/paypal/ipn-listener.php:338
msgid "PayPal account is not verified. Verify account in order to accept this payment."
msgstr "Konto PayPal nie jest zweryfikowane. Zweryfikuj konto aby zaakceptować płatność."

#: includes/payments/gateways/paypal/ipn-listener.php:343
msgid "Payment is pending for unknown reasons. Contact PayPal support for assistance."
msgstr "Płatność jest w trakcie oczekiwania z niewiadomych powodów. Skontaktuj się z działem PayPal wsparcia technicznego, aby uzyskać pomoc."

#: includes/payments/gateways/paypal/ipn-listener.php:363
msgid "Partial PayPal refund processed: %s"
msgstr "Częściowy zwrot PayPal przetwarzany: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:370
msgid "PayPal Payment #%s Refunded for reason: %s"
msgstr "PayPal Płatność #%s zwrócono z powodu: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:374
msgid "PayPal Refund Transaction ID: %s"
msgstr "Number transakcji zwrotu Paypal: %s"

#: includes/payments/gateways/stripe-gateway.php:149
#: includes/payments/gateways/stripe-gateway.php:231
msgid "Stripe"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:186
msgid "Use the card number %1$s with CVC %2$s, a valid expiration date and random 5-digit ZIP-code to test a payment."
msgstr "Użyj numeru karty %1$s z kodem CVC %2$s, prawidłową datą ważności i losowym 5-cyfrowym kodem pocztowym, aby przetestować płatność."

#: includes/payments/gateways/stripe-gateway.php:202
msgid "Pay by Card (Stripe)"
msgstr "Zapłać kartą kredytową (Stripe)"

#: includes/payments/gateways/stripe-gateway.php:203
msgid "Pay with your credit card via Stripe."
msgstr "Zapłać kartą kredytową poprzez Stripre."

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:240
#: includes/payments/gateways/stripe-gateway.php:259
#: includes/payments/gateways/stripe-gateway.php:688
msgid "Bancontact"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:241
#: includes/payments/gateways/stripe-gateway.php:260
#: includes/payments/gateways/stripe-gateway.php:689
msgid "iDEAL"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:242
#: includes/payments/gateways/stripe-gateway.php:261
#: includes/payments/gateways/stripe-gateway.php:690
msgid "Giropay"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:243
#: includes/payments/gateways/stripe-gateway.php:262
#: includes/payments/gateways/stripe-gateway.php:691
msgid "SEPA Direct Debit"
msgstr "Polecenie zapłaty SEPA"

#. translators: %1$s - name of payment method, %2$s - currency codes
#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:244
#: includes/payments/gateways/stripe-gateway.php:269
#: includes/payments/gateways/stripe-gateway.php:276
#: includes/payments/gateways/stripe-gateway.php:692
msgid "Klarna"
msgstr ""

#. translators: %s - currency codes
#: includes/payments/gateways/stripe-gateway.php:252
msgid "The %s currency is selected in the main settings."
msgstr ""

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:258
msgid "%1$s support the following currencies: %2$s."
msgstr ""

#. translators: %1$s - name of payment method, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:268
msgid "%1$s supports: %2$s."
msgstr ""

#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:275
msgid "%s special restrictions."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:293
msgid "Secret Key"
msgstr "Sekretny klucz"

#: includes/payments/gateways/stripe-gateway.php:301
msgid "Webhook Secret"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:310
msgid "Payment Methods"
msgstr "Metody płatności"

#: includes/payments/gateways/stripe-gateway.php:311
msgid "Card Payments"
msgstr "Płatności kartą"

#: includes/payments/gateways/stripe-gateway.php:322
msgid "Checkout Locale"
msgstr "Ustawienia regionalne Kasy"

#: includes/payments/gateways/stripe-gateway.php:325
msgid "Display Checkout in the user's preferred language, if available."
msgstr "Wyświetlanie strony kasy w preferowanym języku użytkownika, jeśli jest dostępny."

#: includes/payments/gateways/stripe-gateway.php:395
msgid "The payment method is not selected."
msgstr "Nie wybrano metody płatności."

#: includes/payments/gateways/stripe-gateway.php:401
msgid "PaymentIntent ID is not set."
msgstr ""

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:430
msgid "Payment for PaymentIntent %s succeeded."
msgstr "Płatność za PaymentIntent %s powiodła się."

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:436
msgid "Payment for PaymentIntent %s is processing."
msgstr "Płatność za PaymentIntent %s jest przetwarzana."

#. translators: %1$s - Stripe PaymentIntent ID, %2$s - Stripe error message text
#: includes/payments/gateways/stripe-gateway.php:457
msgid "Failed to process PaymentIntent %1$s. %2$s"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:474
msgid "Can't charge the payment again: payment's flow already completed."
msgstr "Nie można ponownie obciążyć płatności: przepływ płatności już zakończony."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:496
msgid "Charge %s succeeded."
msgstr "Ładowanie %s powiodło się."

#. translators: %1$s - Stripe Charge ID; %2$s - payment price
#: includes/payments/gateways/stripe-gateway.php:503
msgid "Charge %1$s for %2$s created."
msgstr "Obciąż %1$s za %2$s utworzone."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:508
msgid "Charge %s failed."
msgstr "Obciążenie %s nie powiodło się."

#: includes/payments/gateways/stripe-gateway.php:515
msgid "Charge error. %s"
msgstr "Błąd ładowania. %s"

#: includes/payments/gateways/stripe-gateway.php:529
msgid "Argentinean"
msgstr "Argentyński"

#: includes/payments/gateways/stripe-gateway.php:530
msgid "Simplified Chinese"
msgstr "Uproszczony chiński"

#: includes/payments/gateways/stripe-gateway.php:531
msgid "Danish"
msgstr "Duński"

#: includes/payments/gateways/stripe-gateway.php:532
msgid "Dutch"
msgstr "Holenderski"

#: includes/payments/gateways/stripe-gateway.php:533
msgid "English"
msgstr "Angielski"

#: includes/payments/gateways/stripe-gateway.php:534
msgid "Finnish"
msgstr "Fiński"

#: includes/payments/gateways/stripe-gateway.php:535
msgid "French"
msgstr "Francuski"

#: includes/payments/gateways/stripe-gateway.php:536
msgid "German"
msgstr "Niemiecki"

#: includes/payments/gateways/stripe-gateway.php:537
msgid "Italian"
msgstr "Włoski"

#: includes/payments/gateways/stripe-gateway.php:538
msgid "Japanese"
msgstr "Japoński"

#: includes/payments/gateways/stripe-gateway.php:539
msgid "Norwegian"
msgstr "Norweski"

#: includes/payments/gateways/stripe-gateway.php:540
msgid "Polish"
msgstr "Polski"

#: includes/payments/gateways/stripe-gateway.php:541
msgid "Russian"
msgstr "Rosyjski"

#: includes/payments/gateways/stripe-gateway.php:542
msgid "Spanish"
msgstr "Hiszpański"

#: includes/payments/gateways/stripe-gateway.php:543
msgid "Swedish"
msgstr "Szwedzki"

#: includes/payments/gateways/stripe-gateway.php:571
msgid "PaymentIntent ID is missing."
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:687
msgid "Card"
msgstr "Karta"

#: includes/payments/gateways/stripe-gateway.php:694
msgid "Credit or debit card"
msgstr "Karta kredytowa lub debetowa"

#: includes/payments/gateways/stripe-gateway.php:695
msgid "IBAN"
msgstr ""

#: includes/payments/gateways/stripe-gateway.php:696
msgid "Select iDEAL Bank"
msgstr "Wybierz bank iDEAL"

#: includes/payments/gateways/stripe-gateway.php:698
msgid "You will be redirected to a secure page to complete the payment."
msgstr "Zostaniesz przekierowany na bezpieczną stronę, aby dokończyć płatność."

#: includes/payments/gateways/stripe-gateway.php:699
msgid "By providing your IBAN and confirming this payment, you are authorizing this merchant and Stripe, our payment service provider, to send instructions to your bank to debit your account and your bank to debit your account in accordance with those instructions. You are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited."
msgstr "Podając swój numer IBAN i potwierdzając tę ​​płatność, upoważniasz tego handlowca i firmę Stripe, naszego dostawcę usług płatniczych, do wysyłania instrukcji do Twojego banku w celu obciążenia Twojego konta, a do banku w celu obciążenia Twojego rachunku zgodnie z tymi instrukcjami. Masz prawo do zwrotu środków z banku zgodnie z warunkami umowy z bankiem. O zwrot kosztów należy wystąpić w ciągu 8 tygodni od daty obciążenia konta."

#. translators: %s - payment method type code like: card
#: includes/payments/gateways/stripe/stripe-api.php:172
msgid "Could not create PaymentIntent for a not allowed payment type: %s"
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:163
msgid "Webhook received. Payment %s was cancelled by the customer."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:174
msgid "Webhook received. Payment %s failed and couldn't be processed."
msgstr ""

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:200
msgid "Webhook received. Payment %s was successfully processed."
msgstr ""

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:215
msgid "Webhook received. Charge %s succeeded."
msgstr "Otrzymano webhook. Obciążenie %s powiodło się."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:227
msgid "Webhook received. Charge %s failed."
msgstr "Otrzymano webhook. Obciążenie %s nie powiodło się."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:238
msgid "Webhook received. The source %s is chargeable."
msgstr "Otrzymano webhook. Źródło %s podlega opłacie."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:252
msgid "Webhook received. Payment source %s was cancelled by customer."
msgstr "Otrzymano webhook. Źródło płatności %s zostało anulowane przez klienta."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:263
msgid "Webhook received. Payment source %s failed and couldn't be processed."
msgstr "Otrzymano webhook. Źródło płatności %s nie powiodło się i nie można go przetworzyć."

#: includes/payments/gateways/test-gateway.php:46
#: includes/payments/gateways/test-gateway.php:52
msgid "Test Payment"
msgstr "Testuj płatności"

#: includes/payments/gateways/two-checkout-gateway.php:65
#: includes/payments/gateways/two-checkout-gateway.php:101
msgid "2Checkout"
msgstr ""

#: includes/payments/gateways/two-checkout-gateway.php:88
msgid "To setup the callback process for 2Checkout to automatically mark payments completed, you will need to"
msgstr "Aby skonfigurować w 2Checkout opcję do automatycznego oznaczania wykonanych płatności, trzeba"

#: includes/payments/gateways/two-checkout-gateway.php:90
msgid "Login to your 2Checkout account and click the Notifications tab"
msgstr "Zaloguj się do swojego konta 2Checkout i przejdź do zakładki Powiadomienia"

#: includes/payments/gateways/two-checkout-gateway.php:91
msgid "Click Enable All Notifications"
msgstr "Kliknąć Włącz wszystkie powiadomienia"

#: includes/payments/gateways/two-checkout-gateway.php:92
msgid "In the Global URL field, enter the url %s"
msgstr "W polu Globalny URL wpisz URL %s"

#: includes/payments/gateways/two-checkout-gateway.php:93
msgid "Click Apply"
msgstr "Kliknij przycisk Zastosuj"

#: includes/payments/gateways/two-checkout-gateway.php:189
msgid "Account Number"
msgstr "Numer konta"

#: includes/payments/gateways/two-checkout-gateway.php:197
msgid "Secret Word"
msgstr "Sekretne słowo"

#: includes/payments/gateways/two-checkout/ins-listener.php:68
msgid "2Checkout \"Order Created\" notification received."
msgstr "Otrzymano powiadomienie 2Checkout \"Utworzono zamówienie\"."

#: includes/payments/gateways/two-checkout/ins-listener.php:73
msgid "Payment refunded in 2Checkout"
msgstr "Płatność zwrócona w 2Checkout"

#: includes/payments/gateways/two-checkout/ins-listener.php:80
msgid "2Checkout fraud review passed"
msgstr "2Checkout ocena oszustwa przeszedła"

#: includes/payments/gateways/two-checkout/ins-listener.php:83
msgid "2Checkout fraud review failed"
msgstr "2Checkout ocena oszustwa nie powiodła się"

#: includes/payments/gateways/two-checkout/ins-listener.php:86
msgid "2Checkout fraud review in progress"
msgstr "2Checkout ocena oszustwa w trakcie przetwarzania"

#: includes/post-types/attributes-cpt.php:55
msgid "Attribute"
msgstr "Atrybut"

#: includes/post-types/attributes-cpt.php:56
msgctxt "Add New Attribute"
msgid "Add New"
msgstr "Dodaj nowy"

#: includes/post-types/attributes-cpt.php:57
msgid "Add New Attribute"
msgstr "Dodaj nowy atrybut"

#: includes/post-types/attributes-cpt.php:58
msgid "Edit Attribute"
msgstr "Edytuj atrybut"

#: includes/post-types/attributes-cpt.php:59
msgid "New Attribute"
msgstr "Nowy atrybut"

#: includes/post-types/attributes-cpt.php:60
msgid "View Attribute"
msgstr "Wyświetl atrybut"

#: includes/post-types/attributes-cpt.php:62
msgid "Search Attribute"
msgstr "Atrybut wyszukiwania"

#: includes/post-types/attributes-cpt.php:63
msgid "No Attributes found"
msgstr "Nie znaleziono atrybutów"

#: includes/post-types/attributes-cpt.php:64
msgid "No Attributes found in Trash"
msgstr "Nie znaleziono atrybutów w Koszu"

#: includes/post-types/attributes-cpt.php:66
msgid "Insert into attribute description"
msgstr "Wstaw do opisu atrybutu"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:157
msgid "Search %s"
msgstr "Wyszukaj %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:159
msgid "All %s"
msgstr "Wszystkie %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:161
msgid "Edit %s"
msgstr "Edytuj %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:163
msgid "Update %s"
msgstr "Aktualizuj %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:165
msgid "Add new %s"
msgstr "Dodaj nowy %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:167
msgid "New %s"
msgstr "Nowy %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:169
msgid "No &quot;%s&quot; found"
msgstr ""

#: includes/post-types/attributes-cpt.php:302
msgid "Name (numeric)"
msgstr "Nazwa (numeryczna)"

#: includes/post-types/attributes-cpt.php:303
msgid "Term ID"
msgstr "Identyfikator terminu"

#: includes/post-types/attributes-cpt.php:314
msgid "Enable Archives"
msgstr "Włącz archiwa"

#: includes/post-types/attributes-cpt.php:315
msgid "Link the attribute to an archive page with all accommodation types that have this attribute."
msgstr "Połącz atrybut ze stroną archiwum ze wszystkimi typami zakwaterowania, które mają ten atrybut."

#: includes/post-types/attributes-cpt.php:324
msgid "Visible in Details"
msgstr "Widoczne w szczegółach"

#: includes/post-types/attributes-cpt.php:325
msgid "Display the attribute in details section of an accommodation type."
msgstr "Wyświetl atrybut w sekcji szczegółów typu zakwaterowania."

#: includes/post-types/attributes-cpt.php:334
msgid "Default Sort Order"
msgstr "Domyślna kolejność sortowania"

#: includes/post-types/attributes-cpt.php:344
msgid "Default Text"
msgstr "Tekst domyślny"

#: includes/post-types/attributes-cpt.php:355
msgid "Select"
msgstr "Wybierz"

#: includes/post-types/booking-cpt.php:75
#: templates/edit-booking/edit-dates.php:24
msgid "Edit Dates"
msgstr "Edytuj daty"

#: includes/post-types/booking-cpt.php:215
msgid "Note"
msgstr ""

#: includes/post-types/booking-cpt.php:243
msgctxt "Add New Booking"
msgid "Add New Booking"
msgstr "Dodaj nową rezerwację"

#: includes/post-types/booking-cpt.php:247
#: templates/emails/admin-customer-cancelled-booking.php:16
#: templates/emails/admin-customer-confirmed-booking.php:16
#: templates/emails/admin-payment-confirmed-booking.php:16
#: templates/emails/admin-pending-booking.php:16
#: templates/emails/customer-approved-booking.php:24
#: templates/emails/customer-pending-booking.php:26
msgid "View Booking"
msgstr "Sprawdź rezerwację"

#: includes/post-types/booking-cpt.php:248
msgid "Search Booking"
msgstr "Szukaj rezerwacji"

#: includes/post-types/booking-cpt.php:249
msgid "No bookings found"
msgstr "Nie znaleziono żadnych rezerwacji"

#: includes/post-types/booking-cpt.php:250
msgid "No bookings found in Trash"
msgstr "Nie znaleziono żadnych rezerwacji w Koszu"

#: includes/post-types/booking-cpt.php:251
msgid "All Bookings"
msgstr "Wszystkie rezerwacje"

#: includes/post-types/booking-cpt.php:252
msgid "Insert into booking description"
msgstr "Dodaj do pisania rezerwacji"

#: includes/post-types/booking-cpt.php:253
msgid "Uploaded to this booking"
msgstr "Dodane do tej rezerwacji"

#: includes/post-types/booking-cpt/statuses.php:58
msgctxt "Booking status"
msgid "Pending User Confirmation"
msgstr "W oczekiwaniu potwierdzenia przez użytkownika"

#: includes/post-types/booking-cpt/statuses.php:63
msgid "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgid_plural "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgstr[0] "W oczekiwaniu potwierdzenia przez użytkownika <span class=\"count\">(%s)</span>"
msgstr[1] "W oczekiwaniu potwierdzenia przez użytkownika <span class=\"count\">(%s)</span>"
msgstr[2] "W oczekiwaniu potwierdzenia przez użytkownika <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:69
msgctxt "Booking status"
msgid "Pending Payment"
msgstr "W oczekiwaniu płatności"

#: includes/post-types/booking-cpt/statuses.php:74
msgid "Pending Payment <span class=\"count\">(%s)</span>"
msgid_plural "Pending Payment <span class=\"count\">(%s)</span>"
msgstr[0] "W oczekiwaniu płatności <span class=\"count\">(%s)</span>"
msgstr[1] "W oczekiwaniu płatności <span class=\"count\">(%s)</span>"
msgstr[2] "W oczekiwaniu płatności <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:80
msgctxt "Booking status"
msgid "Pending Admin"
msgstr "Oczekuje administratora"

#: includes/post-types/booking-cpt/statuses.php:85
msgid "Pending Admin <span class=\"count\">(%s)</span>"
msgid_plural "Pending Admin <span class=\"count\">(%s)</span>"
msgstr[0] "Oczekuje administratora <span class=\"count\">(%s)</span>"
msgstr[1] "Oczekują administratora <span class=\"count\">(%s)</span>"
msgstr[2] "Oczekują administratora <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:91
#: includes/reports/data/report-earnings-by-dates-data.php:31
msgctxt "Booking status"
msgid "Abandoned"
msgstr "Porzucone"

#: includes/post-types/booking-cpt/statuses.php:96
#: includes/post-types/payment-cpt/statuses.php:83
msgid "Abandoned <span class=\"count\">(%s)</span>"
msgid_plural "Abandoned <span class=\"count\">(%s)</span>"
msgstr[0] "Porzucono <span class=\"count\">(%s)</span>"
msgstr[1] "Porzucono <span class=\"count\">(%s)</span>"
msgstr[2] "Porzucono <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:107
msgid "Confirmed <span class=\"count\">(%s)</span>"
msgid_plural "Confirmed <span class=\"count\">(%s)</span>"
msgstr[0] "Potwierdzony <span class=\"count\">(%s)</span>"
msgstr[1] "Potwierdzone <span class=\"count\">(%s)</span>"
msgstr[2] "Potwierdzonych <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:113
#: includes/reports/data/report-earnings-by-dates-data.php:30
msgctxt "Booking status"
msgid "Cancelled"
msgstr "Anulowane"

#: includes/post-types/booking-cpt/statuses.php:118
#: includes/post-types/payment-cpt/statuses.php:116
msgid "Cancelled <span class=\"count\">(%s)</span>"
msgid_plural "Cancelled <span class=\"count\">(%s)</span>"
msgstr[0] "Anulowany <span class=\"count\">(%s)</span>"
msgstr[1] "Anulowane <span class=\"count\">(%s)</span>"
msgstr[2] "Anulowanych <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/booking-cpt/statuses.php:180
#: includes/post-types/payment-cpt/statuses.php:213
msgid "Status changed from %s to %s."
msgstr "Status zmienił się z %s do %s."

#: includes/post-types/coupon-cpt.php:45
msgid "A brief description to remind you what this code is for."
msgstr ""

#: includes/post-types/coupon-cpt.php:52
msgid "Conditions"
msgstr ""

#: includes/post-types/coupon-cpt.php:60
msgid "Apply a coupon code to selected accommodations in a booking. Leave blank to apply to all accommodations."
msgstr ""

#: includes/post-types/coupon-cpt.php:82
msgid "Percentage discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:83
msgid "Fixed discount on accommodation price"
msgstr ""

#: includes/post-types/coupon-cpt.php:84
msgid "Fixed discount on daily/nightly price"
msgstr ""

#: includes/post-types/coupon-cpt.php:94
#: includes/post-types/coupon-cpt.php:125
#: includes/post-types/coupon-cpt.php:170
msgid "Enter percent or fixed amount according to selected type."
msgstr ""

#: includes/post-types/coupon-cpt.php:104
msgid "Service Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:137
msgid "Apply a coupon code to selected services in a booking. Leave blank to apply to all services."
msgstr ""

#: includes/post-types/coupon-cpt.php:149
msgid "Fee Discount"
msgstr ""

#: includes/post-types/coupon-cpt.php:180
msgid "Usage Restrictions"
msgstr ""

#: includes/post-types/coupon-cpt.php:195
msgid "Check-in After"
msgstr "Zameldowanie po"

#: includes/post-types/coupon-cpt.php:203
msgid "Check-out Before"
msgstr "Wymeldowanie przed"

#: includes/post-types/coupon-cpt.php:211
msgid "Min days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:212
msgid "For early bird discount. The coupon code applies if a booking is made in a minimum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:222
msgid "Max days before check-in"
msgstr ""

#: includes/post-types/coupon-cpt.php:223
msgid "For last minute discount. The coupon code applies if a booking is made in a maximum set number of days before the check-in date."
msgstr ""

#: includes/post-types/coupon-cpt.php:233
msgid "Minimum Days"
msgstr "Minimalna liczba dni"

#: includes/post-types/coupon-cpt.php:243
msgid "Maximum Days"
msgstr "Maksymalna liczba dni"

#: includes/post-types/coupon-cpt.php:253
msgid "Usage Limit"
msgstr "Limit użycia"

#: includes/post-types/coupon-cpt.php:263
msgid "Usage Count"
msgstr "Ilość zastosowań"

#: includes/post-types/coupon-cpt.php:290
msgctxt "Add New Coupon"
msgid "Add New"
msgstr "Dodaj nowy"

#: includes/post-types/coupon-cpt.php:291
msgid "Add New Coupon"
msgstr "Dodaj nowy kupon"

#: includes/post-types/coupon-cpt.php:292
msgid "Edit Coupon"
msgstr "Edytuj kupon rabatowy"

#: includes/post-types/coupon-cpt.php:293
msgid "New Coupon"
msgstr "Nowy kupon rabatowy"

#: includes/post-types/coupon-cpt.php:294
msgid "View Coupon"
msgstr "Obejrzeć kupony rabatowe"

#: includes/post-types/coupon-cpt.php:295
msgid "Search Coupon"
msgstr "Szukać kuponu rabatowego"

#: includes/post-types/coupon-cpt.php:296
msgid "No coupons found"
msgstr "Nie znaleziono żadnych kuponów rabatowych"

#: includes/post-types/coupon-cpt.php:297
msgid "No coupons found in Trash"
msgstr "Nie znaleziono w Koszu żadnych kuponów rabatowych"

#: includes/post-types/coupon-cpt.php:298
msgid "All Coupons"
msgstr "Wszystkie kupony rabatowe"

#: includes/post-types/payment-cpt.php:37
msgid "Payment History"
msgstr "Historia płatności"

#: includes/post-types/payment-cpt.php:38
msgid "Payment"
msgstr "Płatności"

#: includes/post-types/payment-cpt.php:39
msgctxt "Add New Payment"
msgid "Add New"
msgstr "Dodaj nową"

#: includes/post-types/payment-cpt.php:40
msgid "Add New Payment"
msgstr "Dodaj nowe płatności"

#: includes/post-types/payment-cpt.php:41
msgid "Edit Payment"
msgstr "Edytuj płatności"

#: includes/post-types/payment-cpt.php:42
msgid "New Payment"
msgstr "Nowe płatności"

#: includes/post-types/payment-cpt.php:43
msgid "View Payment"
msgstr "Sprawdź płatności"

#: includes/post-types/payment-cpt.php:44
msgid "Search Payment"
msgstr "Szukaj płatności"

#: includes/post-types/payment-cpt.php:45
msgid "No payments found"
msgstr "Nie znaleziono żadnych płatności"

#: includes/post-types/payment-cpt.php:46
msgid "No payments found in Trash"
msgstr "Nie znaleziono żadnych płatności w Koszu"

#: includes/post-types/payment-cpt.php:47
msgid "Payments"
msgstr "Płatności"

#: includes/post-types/payment-cpt.php:48
msgid "Insert into payment description"
msgstr "Dodaj do opisania płatności"

#: includes/post-types/payment-cpt.php:49
msgid "Uploaded to this payment"
msgstr "Dodane do tej płatności"

#: includes/post-types/payment-cpt.php:54
msgid "Payments."
msgstr "Płatności."

#: includes/post-types/payment-cpt.php:169
msgid "Gateway Mode"
msgstr "Tryb bramy płatniczej"

#: includes/post-types/payment-cpt.php:171
msgid "Sandbox"
msgstr ""

#: includes/post-types/payment-cpt.php:172
msgid "Live"
msgstr ""

#: includes/post-types/payment-cpt.php:194
msgid "Fee"
msgstr "Opłata"

#: includes/post-types/payment-cpt.php:215
msgid "Payment Type"
msgstr "Typ płatności"

#: includes/post-types/payment-cpt.php:240
msgid "Billing Info"
msgstr "Informacje rozliczeniowe"

#: includes/post-types/payment-cpt.php:287
msgid "Address 1"
msgstr "Adres 1"

#: includes/post-types/payment-cpt.php:295
msgid "Address 2"
msgstr "Adres 2"

#: includes/post-types/payment-cpt.php:319
msgid "Postal Code (ZIP)"
msgstr "Kod pocztowy (ZIP)"

#: includes/post-types/payment-cpt/statuses.php:45
msgctxt "Payment status"
msgid "Pending"
msgstr "W oczekiwaniu"

#: includes/post-types/payment-cpt/statuses.php:50
msgid "Pending <span class=\"count\">(%s)</span>"
msgid_plural "Pending <span class=\"count\">(%s)</span>"
msgstr[0] "W oczekiwaniu <span class=\"count\">(%s)</span>"
msgstr[1] "W oczekiwaniu <span class=\"count\">(%s)</span>"
msgstr[2] "W oczekiwaniu <span class=\"count\">(%s)</span>"
msgstr[3] ""

#: includes/post-types/payment-cpt/statuses.php:56
msgctxt "Payment status"
msgid "Completed"
msgstr "Wykonane"

#: includes/post-types/payment-cpt/statuses.php:61
msgid "Completed <span class=\"count\">(%s)</span>"
msgid_plural "Completed <span class=\"count\">(%s)</span>"
msgstr[0] "Zakończono <span class=\"count\">(%s)</span>"
msgstr[1] "Zakończono <span class=\"count\">(%s)</span>"
msgstr[2] "Zakończono <span class=\"count\">(%s)</span>"
msgstr[3] "Zakończono <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:67
msgctxt "Payment status"
msgid "Failed"
msgstr "Nieudane"

#: includes/post-types/payment-cpt/statuses.php:72
msgid "Failed <span class=\"count\">(%s)</span>"
msgid_plural "Failed <span class=\"count\">(%s)</span>"
msgstr[0] "Nieudany <span class=\"count\">(%s)</span>"
msgstr[1] "Nieudane <span class=\"count\">(%s)</span>"
msgstr[2] "Nieudanych <span class=\"count\">(%s)</span>"
msgstr[3] "Nieudanych <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:78
msgctxt "Payment status"
msgid "Abandoned"
msgstr "Porzucone"

#: includes/post-types/payment-cpt/statuses.php:89
msgctxt "Payment status"
msgid "On Hold"
msgstr "W oczekiwaniu"

#: includes/post-types/payment-cpt/statuses.php:94
msgid "On Hold <span class=\"count\">(%s)</span>"
msgid_plural "On Hold <span class=\"count\">(%s)</span>"
msgstr[0] "W oczekiwaniu <span class=\"count\">(%s)</span>"
msgstr[1] "W oczekiwaniu <span class=\"count\">(%s)</span>"
msgstr[2] "W oczekiwaniu <span class=\"count\">(%s)</span>"
msgstr[3] "W oczekiwaniu <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:100
msgctxt "Payment status"
msgid "Refunded"
msgstr "Refundowano"

#: includes/post-types/payment-cpt/statuses.php:105
msgid "Refunded <span class=\"count\">(%s)</span>"
msgid_plural "Refunded <span class=\"count\">(%s)</span>"
msgstr[0] "Refundowano <span class=\"count\">(%s)</span>"
msgstr[1] "Refundowano <span class=\"count\">(%s)</span>"
msgstr[2] "Refundowano <span class=\"count\">(%s)</span>"
msgstr[3] "Refundowano <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:111
msgctxt "Payment status"
msgid "Cancelled"
msgstr "Anulowane"

#: includes/post-types/payment-cpt/statuses.php:180
msgid "Payment (#%s) for this booking is on hold"
msgstr "Opłata (#%s) za tą rezerwację jest w oczekiwaniu"

#: includes/post-types/rate-cpt.php:23
msgid "Rate Info"
msgstr "Oceny"

#: includes/post-types/rate-cpt.php:49
#: includes/post-types/season-cpt.php:99
msgid "Season"
msgstr "Sezon"

#: includes/post-types/rate-cpt.php:72
msgid "Move price to top to set higher priority."
msgstr "Przenieś cenę do góry, aby ustawić wyższy priorytet."

#: includes/post-types/rate-cpt.php:84
msgid "Will be displayed on the checkout page."
msgstr "Zostanie wyświetlane na stronie transakcji."

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/post-types/rate-cpt.php:103
#: includes/post-types/rate-cpt.php:113
msgid "Rates"
msgstr "Oceny"

#: includes/post-types/rate-cpt.php:105
msgctxt "Add New Rate"
msgid "Add New"
msgstr "Dodaj nową"

#: includes/post-types/rate-cpt.php:106
msgid "Add New Rate"
msgstr "Dodaj nową ocenę"

#: includes/post-types/rate-cpt.php:107
msgid "Edit Rate"
msgstr "Edytuj ocenę"

#: includes/post-types/rate-cpt.php:108
msgid "New Rate"
msgstr "Nowa ocena"

#: includes/post-types/rate-cpt.php:109
msgid "View Rate"
msgstr "Sprawdź ocenę"

#: includes/post-types/rate-cpt.php:110
msgid "Search Rate"
msgstr "Szukaj oceny"

#: includes/post-types/rate-cpt.php:111
msgid "No rates found"
msgstr "Nie znaleziono stawek"

#: includes/post-types/rate-cpt.php:112
msgid "No rates found in Trash"
msgstr "Nie znaleziono stawek w Koszu"

#: includes/post-types/rate-cpt.php:114
msgid "Insert into rate description"
msgstr "Wstaw do opisu stawki"

#: includes/post-types/rate-cpt.php:115
msgid "Uploaded to this rate"
msgstr "Przesłano do tej stawki"

#: includes/post-types/rate-cpt.php:120
msgid "This is where you can add new rates."
msgstr "Tutaj możesz dodać nowe stawki."

#: includes/post-types/reserved-room-cpt.php:23
msgid "Reserved Accommodation"
msgstr "Zarezerwowany pokój"

#: includes/post-types/room-cpt.php:33
msgctxt "Add New Accommodation"
msgid "Add New"
msgstr "Dodaj nowy"

#: includes/post-types/room-cpt.php:34
msgid "Add New Accommodation"
msgstr "Dodaj nowy pokój"

#: includes/post-types/room-cpt.php:35
msgid "Edit Accommodation"
msgstr "Edytuj pokój"

#: includes/post-types/room-cpt.php:36
msgid "New Accommodation"
msgstr "Nowy pokój"

#: includes/post-types/room-cpt.php:37
msgid "View Accommodation"
msgstr "Sprawdź pokój"

#: includes/post-types/room-cpt.php:38
msgid "Search Accommodation"
msgstr "Szukaj pokój"

#: includes/post-types/room-cpt.php:39
#: templates/create-booking/results/rooms-found.php:21
#: templates/shortcodes/search-results/results-info.php:19
msgid "No accommodations found"
msgstr "Nie znaleziono żadnych pokojów"

#: includes/post-types/room-cpt.php:40
msgid "No accommodations found in Trash"
msgstr "Nie znaleziono żadnych pokojów w Koszu"

#: includes/post-types/room-cpt.php:42
msgid "Insert into accommodation description"
msgstr "Dodaj to opisania tego pokoju"

#: includes/post-types/room-cpt.php:43
msgid "Uploaded to this accommodation"
msgstr "Dodane do tego pokoju"

#: includes/post-types/room-cpt.php:48
msgid "This is where you can add new accommodations to your hotel."
msgstr "Tutaj można dodać nowe pokoje do hotelu."

#: includes/post-types/room-cpt.php:106
msgid "Automatically block current accommodation when the selected ones are booked"
msgstr ""

#: includes/post-types/room-type-cpt.php:55
msgctxt "Add New Accommodation Type"
msgid "Add Accommodation Type"
msgstr "Dodaj typ pokoju"

#: includes/post-types/room-type-cpt.php:56
msgid "Add New Accommodation Type"
msgstr "Dodaj nowy typ pokoju"

#: includes/post-types/room-type-cpt.php:57
msgid "Edit Accommodation Type"
msgstr "Edytuj pyt pokoju"

#: includes/post-types/room-type-cpt.php:58
msgid "New Accommodation Type"
msgstr "Nowy typ pokoju"

#: includes/post-types/room-type-cpt.php:59
msgid "View Accommodation Type"
msgstr "Zobacz typ pokoju"

#: includes/post-types/room-type-cpt.php:61
msgid "Search Accommodation Type"
msgstr "Szukaj typ pokoju"

#: includes/post-types/room-type-cpt.php:62
msgid "No Accommodation types found"
msgstr "Nie znaleziono żadnych typów pokojów"

#: includes/post-types/room-type-cpt.php:63
msgid "No Accommodation types found in Trash"
msgstr "Nie znaleziono żadnych typów pokojów w Koszu"

#: includes/post-types/room-type-cpt.php:65
msgid "Insert into accommodation type description"
msgstr "Dodać do opisania tego typu pokoju"

#: includes/post-types/room-type-cpt.php:66
msgid "Uploaded to this accommodation type"
msgstr "Dodane do tego typu hotelu"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:83
msgctxt "slug"
msgid "accommodation"
msgstr "accommodation"

#: includes/post-types/room-type-cpt.php:108
msgid "Accommodation Categories"
msgstr "Kategorii obiektów"

#: includes/post-types/room-type-cpt.php:109
msgid "Accommodation Category"
msgstr "Kategoria obiektów"

#: includes/post-types/room-type-cpt.php:110
msgid "Search Accommodation Categories"
msgstr "Szukaj kategorii obiektów"

#: includes/post-types/room-type-cpt.php:111
msgid "Popular Accommodation Categories"
msgstr "Popularne kategorie obiektów"

#: includes/post-types/room-type-cpt.php:112
msgid "All Accommodation Categories"
msgstr "Wszystkie kategorie obiektów"

#: includes/post-types/room-type-cpt.php:113
msgid "Parent Accommodation Category"
msgstr "Kategoria nadrzędna obiektów"

#: includes/post-types/room-type-cpt.php:114
msgid "Parent Accommodation Category:"
msgstr "Kategoria nadrzędna obiektów:"

#: includes/post-types/room-type-cpt.php:115
msgid "Edit Accommodation Category"
msgstr "Edytuj kategorię obiektów"

#: includes/post-types/room-type-cpt.php:116
msgid "Update Accommodation Category"
msgstr "Zaktualizuj nową kategorię obiektów"

#: includes/post-types/room-type-cpt.php:117
msgid "Add New Accommodation Category"
msgstr "Dodaj nową kategorię obiektów"

#: includes/post-types/room-type-cpt.php:118
msgid "New Accommodation Category Name"
msgstr "Nazwa nowej kategorii obiektów"

#: includes/post-types/room-type-cpt.php:119
msgid "Separate categories with commas"
msgstr "Rozdziel kategorii przecinkami"

#: includes/post-types/room-type-cpt.php:120
msgid "Add or remove categories"
msgstr "Dodaj lub usuń kategorii"

#: includes/post-types/room-type-cpt.php:121
msgid "Choose from the most used categories"
msgstr "Wybierz z najczęściej stosowanych kategorii"

#: includes/post-types/room-type-cpt.php:122
msgid "No categories found."
msgstr "Nie znaleziono żadnych kategorii."

#: includes/post-types/room-type-cpt.php:123
#: assets/blocks/blocks.js:816
msgid "Categories"
msgstr "Kategorie"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:138
msgctxt "slug"
msgid "accommodation-category"
msgstr "accommodation-category"

#: includes/post-types/room-type-cpt.php:163
msgid "Accommodation Tags"
msgstr "Tagi zakwaterowania"

#: includes/post-types/room-type-cpt.php:164
msgid "Accommodation Tag"
msgstr "Tag zakwaterowania"

#: includes/post-types/room-type-cpt.php:165
msgid "Search Accommodation Tags"
msgstr "Szukaj tagów zakwaterowania"

#: includes/post-types/room-type-cpt.php:166
msgid "Popular Accommodation Tags"
msgstr "Tagi popularnych kwater"

#: includes/post-types/room-type-cpt.php:167
msgid "All Accommodation Tags"
msgstr "Wszystkie tagi zakwaterowania"

#: includes/post-types/room-type-cpt.php:168
msgid "Parent Accommodation Tag"
msgstr "Tag zakwaterowania rodzica"

#: includes/post-types/room-type-cpt.php:169
msgid "Parent Accommodation Tag:"
msgstr "Tag zakwaterowania rodzica:"

#: includes/post-types/room-type-cpt.php:170
msgid "Edit Accommodation Tag"
msgstr "Edytuj tag zakwaterowania"

#: includes/post-types/room-type-cpt.php:171
msgid "Update Accommodation Tag"
msgstr "Aktualizuj tag zakwaterowania"

#: includes/post-types/room-type-cpt.php:172
msgid "Add New Accommodation Tag"
msgstr "Dodaj nowy tag zakwaterowania"

#: includes/post-types/room-type-cpt.php:173
msgid "New Accommodation Tag Name"
msgstr "Nazwa tagu nowego zakwaterowania"

#: includes/post-types/room-type-cpt.php:174
msgid "Separate tags with commas"
msgstr "Oddziel tagi przecinkami"

#: includes/post-types/room-type-cpt.php:175
msgid "Add or remove tags"
msgstr "Dodaj lub usuń tagi"

#: includes/post-types/room-type-cpt.php:176
msgid "Choose from the most used tags"
msgstr "Wybierz spośród najczęściej używanych tagów"

#: includes/post-types/room-type-cpt.php:177
msgid "No tags found."
msgstr "Nie znaleziono tagów."

#: includes/post-types/room-type-cpt.php:178
#: assets/blocks/blocks.js:828
msgid "Tags"
msgstr "Tagi"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:192
msgctxt "slug"
msgid "accommodation-tag"
msgstr "accommodation-tag"

#: includes/post-types/room-type-cpt.php:217
#: includes/post-types/room-type-cpt.php:232
msgid "Amenities"
msgstr "Udogodnienia"

#: includes/post-types/room-type-cpt.php:218
msgid "Amenity"
msgstr "Udogodnienie"

#: includes/post-types/room-type-cpt.php:219
msgid "Search Amenities"
msgstr "Szukaj udogodnień"

#: includes/post-types/room-type-cpt.php:220
msgid "Popular Amenities"
msgstr "Popularne udogodnienia"

#: includes/post-types/room-type-cpt.php:221
msgid "All Amenities"
msgstr "Wszystkie uudogodnienia"

#: includes/post-types/room-type-cpt.php:222
msgid "Parent Amenity"
msgstr "Nadrzędne udogodnienie"

#: includes/post-types/room-type-cpt.php:223
msgid "Parent Amenity:"
msgstr "Nadrzędne udogodnienie:"

#: includes/post-types/room-type-cpt.php:224
msgid "Edit Amenity"
msgstr "Edytuj udogodnienie"

#: includes/post-types/room-type-cpt.php:225
msgid "Update Amenity"
msgstr "Aktualizuj udogodnienie"

#: includes/post-types/room-type-cpt.php:226
msgid "Add New Amenity"
msgstr "Dodaj nowe udogodnienie"

#: includes/post-types/room-type-cpt.php:227
msgid "New Amenity Name"
msgstr "Nazwa nowego udogodnienia"

#: includes/post-types/room-type-cpt.php:228
msgid "Separate amenities with commas"
msgstr "Rozdziel udogodnienia przecinkami"

#: includes/post-types/room-type-cpt.php:229
msgid "Add or remove amenities"
msgstr "Dodaj lub usuń udogodnienia"

#: includes/post-types/room-type-cpt.php:230
msgid "Choose from the most used amenities"
msgstr "Wybierz z najczęściej wybieranych udogodnień"

#: includes/post-types/room-type-cpt.php:231
msgid "No amenities found."
msgstr "Nie znaleziono żadnych udogodnień."

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:247
msgctxt "slug"
msgid "accommodation-facility"
msgstr "accommodation-facility"

#: includes/post-types/room-type-cpt.php:293
msgid "State the age or disable children in <a href=\"%s\">settings</a>."
msgstr "Podaj wiek lub wyłącz dzieci w <a href=\"%s\">ustawieniach</a>."

#: includes/post-types/room-type-cpt.php:303
msgid "Leave this option empty to calculate total capacity automatically to meet the exact number of adults AND children set above. This is the default behavior. Configure this option to allow any variations of adults OR children set above at checkout so that in total it meets the limit of manually set \"Capacity\". For example, configuration \"adults:5\", \"children:4\", \"capacity:5\" means the property can accommodate up to 5 adults, up to 4 children, but up to 5 guests in total (not 9)."
msgstr "Pozostaw tę opcję pustą, aby automatycznie obliczyć całkowitą pojemność w celu uzyskania dokładnej liczby dorosłych ORAZ dzieci ustawionej powyżej. Jest to zachowanie domyślne. Skonfiguruj tę opcję, aby zezwalać na dowolne odmiany dorosłych LUB dzieci ustawione powyżej przy kasie, tak aby w sumie spełniała limit ręcznie ustawionych \\ \"Pojemność \". Na przykład konfiguracja \\ \"dorośli: 5 \", \\ \"dzieci: 4 \", \\ \"pojemność: 5 \" oznacza, że ​​nieruchomość może pomieścić do 5 osób dorosłych, do 4 dzieci, ale łącznie do 5 osób (nie 9)."

#: includes/post-types/room-type-cpt.php:313
msgid "Base Adults Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:314
#: includes/post-types/room-type-cpt.php:325
msgid "An optional starting value used when creating seasonal prices in the Rates menu."
msgstr ""

#: includes/post-types/room-type-cpt.php:324
msgid "Base Children Occupancy"
msgstr ""

#: includes/post-types/room-type-cpt.php:334
msgid "Other"
msgstr "Inne"

#: includes/post-types/room-type-cpt.php:340
msgid "Size, %s"
msgstr "Rozmiar, %s"

#: includes/post-types/room-type-cpt.php:341
msgid "Leave blank to hide."
msgstr "Zostaw puste, aby ukryć."

#: includes/post-types/room-type-cpt.php:355
msgid "City view, seaside, swimming pool etc."
msgstr "Widok na miasto, brzeg morza, basen itd."

#: includes/post-types/room-type-cpt.php:366
msgid "Bed type"
msgstr "Typ łóżka"

#: includes/post-types/room-type-cpt.php:369
msgid "Set bed types list in <a href=\"%link%\" target=\"_blank\">settings</a>."
msgstr "Ustaw listę typów łóżek w <a href=\"%link%\" target=\"_blank\">Ustawieniach</a>."

#: includes/post-types/room-type-cpt.php:379
msgid "Photo Gallery"
msgstr "Galeria zdjęć"

#: includes/post-types/room-type-cpt.php:390
#: includes/post-types/room-type-cpt.php:395
msgid "Available Services"
msgstr "Dostępne usługi"

#: includes/post-types/season-cpt.php:25
msgid "Season Info"
msgstr "Informacje o sezonie"

#: includes/post-types/season-cpt.php:32
#: includes/reports/earnings-report.php:332
msgid "Start date"
msgstr "Data rozpoczęcia"

#: includes/post-types/season-cpt.php:45
#: includes/reports/earnings-report.php:344
msgid "End date"
msgstr "Data końcowa"

#: includes/post-types/season-cpt.php:55
msgid "Applied for days"
msgstr "Stosowane przez kilka dni"

#: includes/post-types/season-cpt.php:59
msgid "Hold Ctrl / Cmd to select multiple."
msgstr "Przytrzymaj Ctrl / Cmd aby zaznaczyć kilka."

#: includes/post-types/season-cpt.php:68
msgid "Annual repeats begin on the Start date of the season, for one year from the current date."
msgstr ""

#: includes/post-types/season-cpt.php:70
msgid "Does not repeat"
msgstr ""

#: includes/post-types/season-cpt.php:81
msgid "Repeat until date"
msgstr ""

#: includes/post-types/season-cpt.php:100
msgctxt "Add New Season"
msgid "Add New"
msgstr "Dodaj nowy"

#: includes/post-types/season-cpt.php:101
msgid "Add New Season"
msgstr "Dodaj nowy sezon"

#: includes/post-types/season-cpt.php:102
msgid "Edit Season"
msgstr "Edytuj sezon"

#: includes/post-types/season-cpt.php:103
msgid "New Season"
msgstr "Nowy sezon"

#: includes/post-types/season-cpt.php:104
msgid "View Season"
msgstr "Sprawdź sezon"

#: includes/post-types/season-cpt.php:105
msgid "Search Season"
msgstr "Szukaj sezonu"

#: includes/post-types/season-cpt.php:106
msgid "No seasons found"
msgstr "Nie znaleziono żadnych sezonów"

#: includes/post-types/season-cpt.php:107
msgid "No seasons found in Trash"
msgstr "Nie znaleziono żadnych sezonów w Koszu"

#: includes/post-types/season-cpt.php:109
msgid "Insert into season description"
msgstr "Dodaj do opisania sezonu"

#: includes/post-types/season-cpt.php:110
msgid "Uploaded to this season"
msgstr "Dodane do tego sezonu"

#: includes/post-types/season-cpt.php:115
msgid "This is where you can add new seasons."
msgstr "Tutaj można dodać nowe sezony."

#: includes/post-types/service-cpt.php:92
#: includes/views/booking-view.php:206
msgid "Service"
msgstr "Usługa"

#: includes/post-types/service-cpt.php:93
msgctxt "Add New Service"
msgid "Add New"
msgstr "Dodaj nową"

#: includes/post-types/service-cpt.php:94
msgid "Add New Service"
msgstr "Dodaj nową usługę"

#: includes/post-types/service-cpt.php:95
msgid "Edit Service"
msgstr "Edytuj usługę"

#: includes/post-types/service-cpt.php:96
msgid "New Service"
msgstr "Nowa usługa"

#: includes/post-types/service-cpt.php:97
msgid "View Service"
msgstr "Sprawdź usługę"

#: includes/post-types/service-cpt.php:98
msgid "Search Service"
msgstr "Szukaj usługi"

#: includes/post-types/service-cpt.php:99
msgid "No services found"
msgstr "Nie znaleziono żadnych usług"

#: includes/post-types/service-cpt.php:100
msgid "No services found in Trash"
msgstr "Nie znaleziono żadnych usług w Koszu"

#: includes/post-types/service-cpt.php:102
msgid "Insert into service description"
msgstr "Dodaj do opisania usługi"

#: includes/post-types/service-cpt.php:103
msgid "Uploaded to this service"
msgstr "Dodane do tej usługi"

#. translators: do not translate
#: includes/post-types/service-cpt.php:120
msgctxt "slug"
msgid "services"
msgstr "services"

#: includes/post-types/service-cpt.php:156
msgid "How many times the customer will be charged."
msgstr "Ile razy hotel obciąży Gościa opłatą."

#: includes/post-types/service-cpt.php:167
msgid "Minimum"
msgstr ""

#: includes/post-types/service-cpt.php:181
msgid "Maximum"
msgstr "Maksimum"

#: includes/post-types/service-cpt.php:193
msgid "Empty means unlimited"
msgstr "Puste oznacza nieograniczone"

#: includes/reports/data/report-earnings-by-dates-data.php:29
msgctxt "Booking status"
msgid "Pending"
msgstr "W oczekiwaniu"

#: includes/reports/earnings-report.php:91
msgid "Total Sales"
msgstr ""

#: includes/reports/earnings-report.php:94
msgid "Total Without Taxes"
msgstr ""

#: includes/reports/earnings-report.php:97
msgid "Total Fees"
msgstr ""

#: includes/reports/earnings-report.php:100
msgid "Total Services"
msgstr ""

#: includes/reports/earnings-report.php:103
msgid "Total Discounts"
msgstr ""

#: includes/reports/earnings-report.php:106
msgid "Total Bookings"
msgstr ""

#: includes/reports/earnings-report.php:289
#: includes/reports/report-filters.php:38
msgid "Revenue"
msgstr ""

#: includes/reports/earnings-report.php:352
#: includes/views/create-booking/checkout-view.php:56
#: includes/views/shortcodes/checkout-view.php:90
msgid "Apply"
msgstr "Zastosować"

#: includes/reports/earnings-report.php:496
msgid "From %s to %s"
msgstr ""

#: includes/reports/report-filters.php:61
msgid "Today"
msgstr ""

#: includes/reports/report-filters.php:64
msgid "Yesterday"
msgstr ""

#: includes/reports/report-filters.php:67
msgid "This week"
msgstr ""

#: includes/reports/report-filters.php:70
msgid "Last week"
msgstr ""

#: includes/reports/report-filters.php:73
msgid "Last 30 days"
msgstr ""

#: includes/reports/report-filters.php:76
msgid "This month"
msgstr ""

#: includes/reports/report-filters.php:79
msgid "Last month"
msgstr ""

#: includes/reports/report-filters.php:82
msgid "This quarter"
msgstr ""

#: includes/reports/report-filters.php:85
msgid "Last quarter"
msgstr ""

#: includes/reports/report-filters.php:88
msgid "This year"
msgstr ""

#: includes/reports/report-filters.php:91
msgid "Last year"
msgstr ""

#. translators: %s - original Rate title
#: includes/repositories/rate-repository.php:195
msgid "%s - copy"
msgstr "%s - kopiuj"

#: includes/script-managers/admin-script-manager.php:92
msgid "Accommodation Type Gallery"
msgstr "Galeria typów pokojów"

#: includes/script-managers/admin-script-manager.php:93
msgid "Add Gallery To Accommodation Type"
msgstr "Dodaj galerią do typu pokoju"

#: includes/script-managers/admin-script-manager.php:105
msgid "Display imported bookings."
msgstr "Wyświetl zaimportowane rezerwacje."

#: includes/script-managers/admin-script-manager.php:106
msgid "Processing..."
msgstr "Przetwarzanie..."

#: includes/script-managers/admin-script-manager.php:107
msgid "Cancelling..."
msgstr "Anulowanie..."

#: includes/script-managers/admin-script-manager.php:108
msgid "Want to delete?"
msgstr ""

#: includes/script-managers/public-script-manager.php:204
msgid "Not available"
msgstr "Niedostępne"

#: includes/script-managers/public-script-manager.php:205
msgid "This is earlier than allowed by our advance reservation rules."
msgstr ""

#: includes/script-managers/public-script-manager.php:206
msgid "This is later than allowed by our advance reservation rules."
msgstr ""

#: includes/script-managers/public-script-manager.php:210
msgid "Day in the past"
msgstr "Dzień w przeszłości"

#: includes/script-managers/public-script-manager.php:211
msgid "Check-in date"
msgstr "Data zameldowania"

#: includes/script-managers/public-script-manager.php:212
msgid "Less than min days stay"
msgstr "Pobyt mniej niż maksymalna liczba dni"

#: includes/script-managers/public-script-manager.php:213
msgid "More than max days stay"
msgstr "Pobyt więcej niż maksymalna liczba dni"

#: includes/script-managers/public-script-manager.php:215
msgid "Later than max date for current check-in date"
msgstr "Później niż maksymalna bieżąca data zameldowania"

#: includes/script-managers/public-script-manager.php:216
msgid "Rules:"
msgstr "Zasady:"

#: includes/script-managers/public-script-manager.php:217
msgid "Tokenisation failed: %s"
msgstr "Tokenizacja nie powiodła się: %s"

#: includes/script-managers/public-script-manager.php:218
#: includes/script-managers/public-script-manager.php:219
msgid "%1$d &times; &ldquo;%2$s&rdquo; has been added to your reservation."
msgid_plural "%1$d &times; &ldquo;%2$s&rdquo; have been added to your reservation."
msgstr[0] "%1$d &times; &ldquo;%2$s&rdquo; został dodane do rezerwacji."
msgstr[1] "%1$d &times; &ldquo;%2$s&rdquo; zostały dodane do rezerwacji."
msgstr[2] "%1$d &times; &ldquo;%2$s&rdquo; zostały dodane do rezerwacji."
msgstr[3] ""

#: includes/script-managers/public-script-manager.php:220
#: includes/script-managers/public-script-manager.php:221
msgid "%s accommodation selected."
msgid_plural "%s accommodations selected."
msgstr[0] "wybrano %s pokój."
msgstr[1] "wybrano %s pokoi."
msgstr[2] "wybrano %s pokojów."
msgstr[3] ""

#: includes/script-managers/public-script-manager.php:222
msgid "Coupon code is empty."
msgstr "Pole Kod kuponu rabatowego jest puste."

#: includes/script-managers/public-script-manager.php:225
msgid "Select dates"
msgstr ""

#: includes/settings/main-settings.php:26
msgid "Dark Blue"
msgstr "Ciemny niebieski"

#: includes/settings/main-settings.php:27
msgid "Dark Green"
msgstr "Ciemnozielony"

#: includes/settings/main-settings.php:28
msgid "Dark Red"
msgstr "Ciemno czerwony"

#: includes/settings/main-settings.php:29
msgid "Grayscale"
msgstr "Odcienie szarości"

#: includes/settings/main-settings.php:30
msgid "Light Blue"
msgstr "Jasny niebieski"

#: includes/settings/main-settings.php:31
msgid "Light Coral"
msgstr "Jasny koral"

#: includes/settings/main-settings.php:32
msgid "Light Green"
msgstr "Jasnozielony"

#: includes/settings/main-settings.php:33
msgid "Light Yellow"
msgstr "Jasny zółty"

#: includes/settings/main-settings.php:34
msgid "Minimal Blue"
msgstr "Minimalistyczny Niebieski"

#: includes/settings/main-settings.php:35
msgid "Minimal Orange"
msgstr "Minimalistyczny Pomarańczowy"

#: includes/settings/main-settings.php:36
msgid "Minimal"
msgstr "Minimalistyczny"

#: includes/settings/main-settings.php:38
msgid "Sky Blue"
msgstr "Błękitny"

#: includes/settings/main-settings.php:39
msgid "Slate Blue"
msgstr ""

#: includes/settings/main-settings.php:40
msgid "Turquoise"
msgstr "Turkusowy"

#: includes/shortcodes/account-shortcode.php:212
#: includes/views/shortcodes/checkout-view.php:22
msgid "Invalid login or password."
msgstr ""

#: includes/shortcodes/account-shortcode.php:221
msgid "Account data updated."
msgstr ""

#: includes/shortcodes/account-shortcode.php:227
msgid "Password changed."
msgstr ""

#: includes/shortcodes/account-shortcode.php:238
msgid "Dashboard"
msgstr ""

#: includes/shortcodes/account-shortcode.php:240
msgid "Account"
msgstr ""

#: includes/shortcodes/account-shortcode.php:241
msgid "Logout"
msgstr ""

#: includes/shortcodes/account-shortcode.php:279
msgid "Passwords do not match."
msgstr ""

#: includes/shortcodes/account-shortcode.php:282
msgid "Please, provide a valid current password."
msgstr ""

#: includes/shortcodes/account-shortcode.php:301
#: includes/views/shortcodes/checkout-view.php:54
msgid "Lost your password?"
msgstr ""

#: includes/shortcodes/booking-confirmation-shortcode.php:294
msgid "Payment:"
msgstr "Płatność:"

#: includes/shortcodes/booking-confirmation-shortcode.php:302
msgid "Payment Method:"
msgstr "Metoda płatności:"

#: includes/shortcodes/booking-confirmation-shortcode.php:315
#: templates/shortcodes/booking-details/booking-details.php:42
msgid "Status:"
msgstr "Status:"

#: includes/shortcodes/checkout-shortcode.php:196
msgid "Bookings are disabled in the settings."
msgstr ""

#: includes/shortcodes/checkout-shortcode/step-booking.php:151
msgid "Checkout data is not valid."
msgstr "Dane do kasy są nieprawidłowe."

#: includes/shortcodes/checkout-shortcode/step-booking.php:449
msgid "Payment method is not valid."
msgstr "Nieprawidłowa metoda płatności."

#: includes/shortcodes/checkout-shortcode/step-checkout.php:193
msgid "Accommodation count is not valid."
msgstr "Nieprawidłowa ilość pokojów."

#: includes/shortcodes/checkout-shortcode/step.php:110
msgid "Accommodation is already booked."
msgstr "Pokój jest już zarezerwowany."

#: includes/shortcodes/checkout-shortcode/step.php:120
#: includes/shortcodes/checkout-shortcode/step.php:129
#: includes/shortcodes/checkout-shortcode/step.php:138
msgid "Reservation submitted"
msgstr "Rezerwacja złożona"

#: includes/shortcodes/checkout-shortcode/step.php:121
msgid "Details of your reservation have just been sent to you in a confirmation email. Please check your inbox to complete booking."
msgstr "Szczegóły rezerwacji zostały wysłane w wiadomości e-mail z potwierdzeniem. Sprawdź skrzynkę odbiorczą, aby zakończyć rezerwację."

#: includes/shortcodes/checkout-shortcode/step.php:130
#: includes/shortcodes/checkout-shortcode/step.php:139
msgid "We received your booking request. Once it is confirmed we will notify you via email."
msgstr "Otrzymaliśmy prośbę o rezerwację. Poinformujemy Cię na e-mail gdy zostanie potwierdzona."

#: includes/shortcodes/room-rates-shortcode.php:104
#: template-functions.php:31
msgid "Choose dates to see relevant prices"
msgstr "Wybierz daty, aby zobaczyć odpowiednie ceny"

#: includes/shortcodes/search-results-shortcode.php:766
msgid "Select from available accommodations."
msgstr "Wybierz pokój z dostępnych."

#: includes/shortcodes/search-results-shortcode.php:775
#: includes/shortcodes/search-results-shortcode.php:1013
#: template-functions.php:843
msgid "Confirm Reservation"
msgstr "Potwierdź rezerwację"

#: includes/shortcodes/search-results-shortcode.php:804
msgid "Recommended for %d adult"
msgid_plural "Recommended for %d adults"
msgstr[0] "Rekomerndowane dla %d doroslego"
msgstr[1] "Rekomerndowane dla %d doroslych"
msgstr[2] "Rekomerndowane dla %d doroslych"
msgstr[3] ""

#: includes/shortcodes/search-results-shortcode.php:806
msgid " and %d child"
msgid_plural " and %d children"
msgstr[0] " i %d dziecko"
msgstr[1] " i %d dziecka"
msgstr[2] " i %d dzieci"
msgstr[3] ""

#: includes/shortcodes/search-results-shortcode.php:809
msgid "Recommended for %d guest"
msgid_plural "Recommended for %d guests"
msgstr[0] "Polecany dla %d gościa"
msgstr[1] "Polecany dla %d gości"
msgstr[2] "Polecany dla %d gości"
msgstr[3] ""

#: includes/shortcodes/search-results-shortcode.php:891
msgid "Max occupancy:"
msgstr "Maksymalne obłożenie:"

#: includes/shortcodes/search-results-shortcode.php:910
msgid "%d child"
msgid_plural "%d children"
msgstr[0] "%d dziecko"
msgstr[1] "%d dziecka"
msgstr[2] "%d dzieci"
msgstr[3] ""

#: includes/shortcodes/search-results-shortcode.php:945
#: templates/create-booking/results/reserve-rooms.php:76
msgid "Reserve"
msgstr "Zarezerwuj"

#: includes/shortcodes/search-results-shortcode.php:1002
msgid "of %d accommodation available."
msgid_plural "of %d accommodations available."
msgstr[0] "%d dostępnego pomieszczenia."
msgstr[1] "%d dostępnych pomieszczeń."
msgstr[2] "%d dostępnych pomieszczeń."
msgstr[3] ""

#. translators: Verb. To book an accommodation.
#: includes/shortcodes/search-results-shortcode.php:1012
#: template-functions.php:531
#: template-functions.php:544
msgid "Book"
msgstr "Zarezerwuj"

#: includes/users-and-roles/customers.php:290
#: includes/users-and-roles/customers.php:383
msgid "Please, provide a valid email."
msgstr ""

#: includes/users-and-roles/customers.php:318
msgid "Could not create a customer."
msgstr ""

#: includes/users-and-roles/customers.php:379
msgid "Could not retrieve a customer."
msgstr ""

#: includes/users-and-roles/customers.php:531
#: includes/users-and-roles/customers.php:577
msgid "Please, provide a valid Customer ID."
msgstr ""

#: includes/users-and-roles/customers.php:563
msgid "A database error."
msgstr ""

#: includes/users-and-roles/customers.php:583
msgid "No customer was deleted."
msgstr ""

#: includes/users-and-roles/customers.php:694
#: includes/views/shortcodes/checkout-view.php:31
msgid "An account with this email already exists. Please, log in."
msgstr ""

#: includes/users-and-roles/roles.php:34
msgid "Hotel Manager"
msgstr ""

#: includes/users-and-roles/roles.php:41
msgid "Hotel Worker"
msgstr ""

#: includes/users-and-roles/roles.php:48
msgid "Hotel Customer"
msgstr ""

#: includes/users-and-roles/user.php:54
msgid "Please provide a valid email address."
msgstr ""

#: includes/users-and-roles/user.php:69
msgid "Please enter a valid account username."
msgstr ""

#: includes/users-and-roles/user.php:73
msgid "An account is already registered with that username. Please choose another."
msgstr ""

#: includes/users-and-roles/user.php:81
msgid "Please enter an account password."
msgstr ""

#: includes/utils/date-utils.php:145
msgid "Sunday"
msgstr "Niedziela"

#: includes/utils/date-utils.php:146
msgid "Monday"
msgstr "Poniedzialek"

#: includes/utils/date-utils.php:147
msgid "Tuesday"
msgstr "Czwartek"

#: includes/utils/date-utils.php:148
msgid "Wednesday"
msgstr "Środa"

#: includes/utils/date-utils.php:149
msgid "Thursday"
msgstr "Wtorek"

#: includes/utils/date-utils.php:150
msgid "Friday"
msgstr "Piątek"

#: includes/utils/date-utils.php:151
msgid "Saturday"
msgstr "Sobota"

#: includes/utils/parse-utils.php:135
msgid "Check-out date cannot be earlier than check-in date."
msgstr "Data wymeldowania nie może być wcześniejsza niż data zameldowania."

#: includes/utils/parse-utils.php:159
msgid "Adults number is not valid"
msgstr "Liczba dorosłych jest nieprawidłowa"

#: includes/utils/parse-utils.php:183
msgid "Children number is not valid"
msgstr "Numer podrzędny jest nieprawidłowy"

#: includes/utils/taxes-and-fees-utils.php:27
msgctxt "Text about taxes and fees below the price."
msgid " (+taxes and fees)"
msgstr ""

#. translators: %s is a tax value
#: includes/utils/taxes-and-fees-utils.php:56
msgctxt "Text about taxes and fees below the price."
msgid " (+%s taxes and fees)"
msgstr ""

#: includes/utils/taxes-and-fees-utils.php:84
msgctxt "Text about taxes and fees below the price."
msgid " (includes taxes and fees)"
msgstr ""

#: includes/views/booking-view.php:79
msgctxt "Accommodation type in price breakdown table. Example: #1 Double Room"
msgid "#%d %s"
msgstr ""

#: includes/views/booking-view.php:82
msgid "Expand"
msgstr "Rozwiń"

#: includes/views/booking-view.php:91
#: includes/views/edit-booking/checkout-view.php:209
msgid "Rate: %s"
msgstr "Stawka: %s"

#: includes/views/booking-view.php:125
msgid "Dates"
msgstr "Daty"

#: includes/views/booking-view.php:207
#: includes/views/loop-room-type-view.php:39
#: includes/views/single-room-type-view.php:131
#: includes/widgets/rooms-widget.php:197
#: assets/blocks/blocks.js:484
#: assets/blocks/blocks.js:734
#: assets/blocks/blocks.js:1263
msgid "Details"
msgstr "Szczegóły"

#: includes/views/booking-view.php:380
msgid "Subtotal"
msgstr "Podsuma"

#: includes/views/booking-view.php:393
msgid "Coupon: %s"
msgstr "Kupon: %s"

#: includes/views/booking-view.php:412
msgid "Subtotal (excl. taxes)"
msgstr ""

#: includes/views/booking-view.php:422
msgid "Taxes"
msgstr ""

#: includes/views/create-booking/checkout-view.php:55
#: includes/views/shortcodes/checkout-view.php:89
msgid "Coupon Code:"
msgstr "Kod kuponu rabatowego:"

#: includes/views/edit-booking/checkout-view.php:25
msgid "New Booking Details"
msgstr "Nowe szczegóły rezerwacji"

#: includes/views/edit-booking/checkout-view.php:43
msgid "Original Booking Details"
msgstr "Szczegóły pierwotnej rezerwacji"

#: includes/views/edit-booking/checkout-view.php:154
#: includes/views/shortcodes/checkout-view.php:269
#: template-functions.php:794
#: templates/create-booking/search/search-form.php:111
#: templates/shortcodes/search/search-form.php:105
msgid "Children %s"
msgstr "Dzieci %s"

#: includes/views/edit-booking/checkout-view.php:232
#: templates/emails/reserved-room-details.php:30
msgid "Additional Services"
msgstr "Dodatkowe usługi"

#: includes/views/edit-booking/checkout-view.php:249
#: includes/views/reserved-room-view.php:26
#: template-functions.php:937
msgid "x %d guest"
msgid_plural "x %d guests"
msgstr[0] "x %d gość"
msgstr[1] "x %d gości"
msgstr[2] "x %d gości"
msgstr[3] ""

#: includes/views/edit-booking/checkout-view.php:253
#: includes/views/reserved-room-view.php:31
#: template-functions.php:940
msgid "x %d time"
msgid_plural "x %d times"
msgstr[0] "x %d raz"
msgstr[1] "x %d raza"
msgstr[2] "x %d raza"
msgstr[3] ""

#: includes/views/global-view.php:53
msgid "Accommodation pagination"
msgstr "Zakwaterowanie pagination"

#: includes/views/global-view.php:56
msgid "Services pagination"
msgstr "Usługi stronicowania"

#: includes/views/loop-room-type-view.php:55
#: includes/views/single-room-type-view.php:147
#: templates/widgets/rooms/room-content.php:99
msgid "Categories:"
msgstr "Kategorie:"

#: includes/views/loop-room-type-view.php:67
#: includes/views/single-room-type-view.php:159
#: templates/widgets/rooms/room-content.php:129
msgid "Amenities:"
msgstr "Udogodnienia:"

#: includes/views/loop-room-type-view.php:97
#: includes/views/loop-room-type-view.php:115
#: includes/views/single-room-type-view.php:189
#: includes/views/single-room-type-view.php:207
#: templates/widgets/rooms/room-content.php:67
#: templates/widgets/rooms/room-content.php:79
#: templates/widgets/search-availability/search-form.php:80
msgid "Guests:"
msgstr "Goście:"

#: includes/views/loop-room-type-view.php:140
#: includes/views/single-room-type-view.php:232
#: templates/widgets/rooms/room-content.php:175
msgid "Bed Type:"
msgstr "Typ łóżka:"

#: includes/views/loop-room-type-view.php:164
#: includes/views/single-room-type-view.php:256
#: templates/widgets/rooms/room-content.php:159
msgid "View:"
msgstr "Widok:"

#: includes/views/loop-room-type-view.php:184
#: includes/views/single-room-type-view.php:276
#: template-functions.php:839
#: templates/widgets/rooms/room-content.php:227
msgid "Prices start at:"
msgstr "Cena od:"

#: includes/views/loop-service-view.php:46
msgid "Price:"
msgstr "Cena:"

#: includes/views/shortcodes/checkout-view.php:49
msgid "Returning customer?"
msgstr ""

#: includes/views/shortcodes/checkout-view.php:50
msgid "Click here to log in"
msgstr ""

#. translators: 1 - username;
#: includes/views/shortcodes/checkout-view.php:70
#: templates/account/dashboard.php:29
msgid "Hello %1$s (not %1$s? <a href=\"%2$s\">Log out</a>)."
msgstr ""

#: includes/views/shortcodes/checkout-view.php:182
msgid "Accommodation #%d"
msgstr "Pokojów #%d"

#: includes/views/shortcodes/checkout-view.php:186
msgid "Accommodation Type:"
msgstr "Typ pokoju:"

#: includes/views/shortcodes/checkout-view.php:324
msgid "Choose Rate"
msgstr "Wybierz ocenę"

#: includes/views/shortcodes/checkout-view.php:397
msgid "Choose Additional Services"
msgstr "Wybierz dodatkowe usługi"

#: includes/views/shortcodes/checkout-view.php:429
msgid "for "
msgstr "dla "

#: includes/views/shortcodes/checkout-view.php:442
msgctxt "Example: Breakfast for X guest(s)"
msgid " guest(s)"
msgstr " gość (s)"

#: includes/views/shortcodes/checkout-view.php:464
msgid "time(s)"
msgstr "czas(y)"

#: includes/views/shortcodes/checkout-view.php:533
msgctxt "I've read and accept the terms & conditions"
msgid "terms & conditions"
msgstr "regulamin"

#: includes/views/shortcodes/checkout-view.php:536
msgctxt "I've read and accept the <tag>terms & conditions</tag>"
msgid "I've read and accept the %s"
msgstr "Przeczytałem i akceptuję %s"

#: includes/views/shortcodes/checkout-view.php:579
msgid "Your Information"
msgstr "Twoje informacje"

#: includes/views/shortcodes/checkout-view.php:582
#: template-functions.php:696
#: templates/widgets/search-availability/search-form.php:24
msgid "Required fields are followed by %s"
msgstr "Wymagane pola %s"

#: includes/views/shortcodes/checkout-view.php:758
msgid "Create an account"
msgstr ""

#: includes/views/shortcodes/checkout-view.php:775
msgid "Payment Method"
msgstr "Metoda płatności"

#: includes/views/shortcodes/checkout-view.php:780
msgid "Sorry, it seems that there are no available payment methods."
msgstr "Niestety, ale niema żadnych dostępnych metod płatności."

#: includes/views/shortcodes/checkout-view.php:874
#: templates/emails/admin-customer-cancelled-booking.php:32
#: templates/emails/admin-customer-confirmed-booking.php:32
#: templates/emails/admin-payment-confirmed-booking.php:39
#: templates/emails/admin-pending-booking.php:32
#: templates/emails/customer-approved-booking.php:28
#: templates/emails/customer-cancelled-booking.php:26
#: templates/emails/customer-confirmation-booking.php:32
#: templates/emails/customer-pending-booking.php:30
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:27
msgid "Total Price:"
msgstr "Razem:"

#: includes/views/shortcodes/checkout-view.php:886
msgid "Deposit:"
msgstr "Depozyt:"

#: includes/views/shortcodes/checkout-view.php:906
#: templates/shortcodes/booking-details/booking-details.php:25
#: templates/widgets/search-availability/search-form.php:35
msgid "Check-in:"
msgstr "Zameldować się:"

#: includes/views/shortcodes/checkout-view.php:913
msgctxt "from 10:00 am"
msgid "from"
msgstr "z"

#: includes/views/shortcodes/checkout-view.php:929
#: templates/shortcodes/booking-details/booking-details.php:29
#: templates/widgets/search-availability/search-form.php:54
msgid "Check-out:"
msgstr "Wymeldować się:"

#: includes/views/shortcodes/checkout-view.php:936
msgctxt "until 10:00 am"
msgid "until"
msgstr "do"

#: includes/views/shortcodes/checkout-view.php:1012
#: templates/create-booking/checkout/checkout-form.php:42
msgid "Book Now"
msgstr "Zarezerwuj"

#: includes/views/single-room-type-view.php:127
msgid "Availability"
msgstr "Dostępne"

#: includes/views/single-room-type-view.php:292
msgid "Reservation Form"
msgstr "Formularz rezerwacji"

#: includes/widgets/rooms-widget.php:24
msgid "Display Accommodation Types"
msgstr "Pokazać typy łóżek"

#: includes/widgets/rooms-widget.php:169
#: includes/widgets/search-availability-widget.php:236
msgid "Title:"
msgstr "Tytuł:"

#: includes/widgets/rooms-widget.php:189
#: assets/blocks/blocks.js:448
#: assets/blocks/blocks.js:698
#: assets/blocks/blocks.js:1227
msgid "Featured Image"
msgstr "Wyróżniony obraz"

#: includes/widgets/rooms-widget.php:193
#: assets/blocks/blocks.js:472
#: assets/blocks/blocks.js:722
#: assets/blocks/blocks.js:1251
msgid "Excerpt (short description)"
msgstr "Pokaż Fragment (krótkie opisanie)"

#: includes/widgets/rooms-widget.php:205
#: assets/blocks/blocks.js:770
#: assets/blocks/blocks.js:1299
msgid "Book Button"
msgstr "Przycisk książki"

#: includes/widgets/search-availability-widget.php:50
#: includes/wizard.php:84
msgid "Search Availability"
msgstr "Szukać dostępne"

#: includes/widgets/search-availability-widget.php:53
msgid "Search Availability Form"
msgstr "Formularz wyszukiwania dostępnych"

#: includes/widgets/search-availability-widget.php:240
msgid "Check-in Date:"
msgstr "Data zameldowania:"

#: includes/widgets/search-availability-widget.php:241
#: includes/widgets/search-availability-widget.php:246
msgctxt "Date format tip"
msgid "Preset date. Formatted as %s"
msgstr "Dane preseta. Sformatowane jako %s"

#: includes/widgets/search-availability-widget.php:244
msgid "Check-out Date:"
msgstr "Data wymeldowania:"

#: includes/widgets/search-availability-widget.php:249
msgid "Preset Adults:"
msgstr "Preset Dorosli:"

#: includes/widgets/search-availability-widget.php:257
msgid "Preset Children:"
msgstr "Preset Dzieci:"

#: includes/widgets/search-availability-widget.php:265
msgid "Attributes:"
msgstr "Atrybuty:"

#: includes/wizard.php:34
msgid "Booking Confirmation and Search Results pages are required to handle bookings. Press \"Install Pages\" button to create and set up these pages. Dismiss this notice if you already installed them."
msgstr "Strony Kasa i Wyniki wyszukiwania są wymagane do zarządzania rezerwacjami. Kliknij w przycisk \"Instaluj strony\" aby stworzyć i ustawić te strony. Wyłącz to powiadomienie, jeśli już zainstalowałeś te strony."

#: includes/wizard.php:35
msgid "Install Pages"
msgstr "Zainstaluj strony"

#: includes/wizard.php:147
msgid "Booking Canceled"
msgstr "Rezerwacja anulowana"

#: includes/wizard.php:148
msgid "Your reservation is canceled."
msgstr "Twoja rezerwacja została anulowana."

#: includes/wizard.php:183
msgid "Reservation Received"
msgstr "Rezerwacja otrzymana"

#: includes/wizard.php:196
msgid "Transaction Failed"
msgstr "Transakcja nie powiodła się"

#: includes/wizard.php:197
msgid "Unfortunately, your transaction cannot be completed at this time. Please try again or contact us."
msgstr "Niestety, w tej chwili nie można zakończyć transakcji. Spróbuj ponownie lub skontaktuj się z nami."

#: plugin.php:1100
msgid "Prices start at: %s"
msgstr "Ceny zaczynają się od: %s"

#: template-functions.php:563
msgid "View Details"
msgstr "Zobacz szczegóły"

#: template-functions.php:593
#: template-functions.php:652
msgid "Accommodation %s not found."
msgstr ""

#: template-functions.php:707
#: template-functions.php:716
#: templates/create-booking/search/search-form.php:43
#: templates/create-booking/search/search-form.php:63
#: templates/edit-booking/edit-dates.php:30
#: templates/edit-booking/edit-dates.php:39
#: templates/shortcodes/search/search-form.php:36
#: templates/shortcodes/search/search-form.php:56
#: templates/widgets/search-availability/search-form.php:36
#: templates/widgets/search-availability/search-form.php:55
msgctxt "Date format tip"
msgid "Formatted as %s"
msgstr "Sformatowane jako %s"

#: template-functions.php:831
msgid "Reserve %1$s of %2$s available accommodations."
msgstr "Zarezerwuj %1$s z %2$s dostępnych kwater."

#: template-functions.php:835
msgid "%s is available for selected dates."
msgstr "%s jest dostępny dla wybranych dat."

#: template-functions.php:849
#: templates/edit-booking/edit-dates.php:46
msgid "Check Availability"
msgstr "Sprawdź dostępność"

#: template-functions.php:910
msgid "Rate:"
msgstr "Ranking:"

#: template-functions.php:930
msgid "Services:"
msgstr "Usługi:"

#: template-functions.php:953
msgid "Guest:"
msgstr "Gość:"

#: template-functions.php:976
msgid "Payment ID"
msgstr "Numer ID płatności"

#: template-functions.php:1008
msgid "Total Paid"
msgstr "Łącznie zapłacono"

#: template-functions.php:1017
msgid "To Pay"
msgstr "Do zapłaty"

#: template-functions.php:1042
msgid "Add Payment Manually"
msgstr "Dodaj ręcznie płatności"

#: templates/account/account-details.php:78
msgid "Change Password"
msgstr ""

#: templates/account/account-details.php:81
msgid "Old Password"
msgstr ""

#: templates/account/account-details.php:85
msgid "New Password"
msgstr ""

#: templates/account/account-details.php:89
msgid "Confirm New Password"
msgstr ""

#: templates/account/account-details.php:99
msgid "You are not allowed to access this page."
msgstr ""

#: templates/account/bookings.php:116
#: templates/account/bookings.php:121
msgid "No bookings found."
msgstr "Nie znaleziono żadnych rezerwacji."

#: templates/account/dashboard.php:41
msgid "From your account dashboard you can view <a href=\"%1$s\">your recent bookings</a> or edit your <a href=\"%2$s\">password and account details</a>."
msgstr ""

#: templates/create-booking/results/reserve-rooms.php:37
msgid "Base price"
msgstr "Cena podstawowa"

#: templates/create-booking/results/rooms-found.php:19
#: templates/shortcodes/search-results/results-info.php:17
msgid "%s accommodation found"
msgid_plural "%s accommodations found"
msgstr[0] "%s obiekt znaleziono"
msgstr[1] "%s obiekty znaleziono"
msgstr[2] "%s obiektów znaleziono"
msgstr[3] ""

#: templates/create-booking/results/rooms-found.php:24
#: templates/shortcodes/search-results/results-info.php:21
msgid " from %s - till %s"
msgstr " od %s - do %s"

#: templates/edit-booking/add-room-popup.php:24
#: templates/edit-booking/edit-reserved-rooms.php:36
msgid "Add Accommodation"
msgstr "Dodaj zakwaterowanie"

#: templates/edit-booking/checkout-form.php:28
msgid "Save"
msgstr "Zapisz"

#: templates/edit-booking/edit-dates.php:25
msgid "Choose new dates to check availability of reserved accommodations in the original booking."
msgstr "Wybierz nowe daty, aby sprawdzić dostępność miejsc noclegowych w pierwotnej rezerwacji."

#: templates/edit-booking/edit-reserved-rooms.php:39
msgid "Add, remove or replace accommodations in the original booking."
msgstr "Dodaj, usuń lub zamień noclegi w pierwotnej rezerwacji."

#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Not Available"
msgstr "Niedostępne"

#: templates/edit-booking/edit-reserved-rooms.php:79
#: templates/edit-booking/summary-table.php:65
msgid "Continue"
msgstr "Kontynuuj"

#: templates/edit-booking/summary-table.php:26
msgid "Choose how to associate data"
msgstr "Wybierz sposób powiązania danych"

#: templates/edit-booking/summary-table.php:27
msgid "Use Source Accommodation to assign pre-filled booking information available in the original booking, e.g., full guest name, selected rate, services, etc."
msgstr "Użyj zakwaterowania źródłowego, aby przypisać wstępnie wypełnione informacje o rezerwacji dostępne w pierwotnej rezerwacji, np. pełne imię i nazwisko gościa, wybrana cena, usługi itp."

#: templates/edit-booking/summary-table.php:32
msgid "Source accommodation"
msgstr "Zakwaterowanie źródłowe"

#: templates/edit-booking/summary-table.php:34
msgid "Target accommodation"
msgstr "Zakwaterowanie docelowe"

#: templates/emails/admin-customer-cancelled-booking.php:15
msgid "Booking #%s is cancelled by customer."
msgstr "Rezerwacja #%s została odwołana przez klienta."

#: templates/emails/admin-customer-cancelled-booking.php:17
#: templates/emails/admin-customer-confirmed-booking.php:17
#: templates/emails/admin-payment-confirmed-booking.php:24
#: templates/emails/admin-pending-booking.php:17
#: templates/emails/customer-approved-booking.php:17
#: templates/emails/customer-cancelled-booking.php:18
#: templates/emails/customer-confirmation-booking.php:24
#: templates/emails/customer-pending-booking.php:19
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:19
msgid "Details of booking"
msgstr "Szczegóły rezerwacji"

#: templates/emails/admin-customer-cancelled-booking.php:18
#: templates/emails/admin-customer-confirmed-booking.php:18
#: templates/emails/admin-payment-confirmed-booking.php:25
#: templates/emails/admin-pending-booking.php:18
#: templates/emails/customer-approved-booking.php:20
#: templates/emails/customer-cancelled-booking.php:21
#: templates/emails/customer-confirmation-booking.php:27
#: templates/emails/customer-pending-booking.php:22
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:22
msgid "Check-in: %1$s, from %2$s"
msgstr "Zameldowanie: %1$s, od %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:20
#: templates/emails/admin-customer-confirmed-booking.php:20
#: templates/emails/admin-payment-confirmed-booking.php:27
#: templates/emails/admin-pending-booking.php:20
#: templates/emails/customer-approved-booking.php:22
#: templates/emails/customer-cancelled-booking.php:23
#: templates/emails/customer-confirmation-booking.php:29
#: templates/emails/customer-pending-booking.php:24
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:24
msgid "Check-out: %1$s, until %2$s"
msgstr "Wymeldowanie: %1$s, do %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:24
#: templates/emails/admin-customer-confirmed-booking.php:24
#: templates/emails/admin-payment-confirmed-booking.php:31
#: templates/emails/admin-pending-booking.php:24
#: templates/emails/customer-approved-booking.php:32
#: templates/emails/customer-cancelled-booking.php:30
#: templates/emails/customer-confirmation-booking.php:36
#: templates/emails/customer-pending-booking.php:33
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:31
msgid "Name: %1$s %2$s"
msgstr "Imię: %1$s %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:26
#: templates/emails/admin-customer-confirmed-booking.php:26
#: templates/emails/admin-payment-confirmed-booking.php:33
#: templates/emails/admin-pending-booking.php:26
#: templates/emails/customer-approved-booking.php:34
#: templates/emails/customer-cancelled-booking.php:32
#: templates/emails/customer-confirmation-booking.php:38
#: templates/emails/customer-pending-booking.php:35
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:33
msgid "Email: %s"
msgstr ""

#: templates/emails/admin-customer-cancelled-booking.php:28
#: templates/emails/admin-customer-confirmed-booking.php:28
#: templates/emails/admin-payment-confirmed-booking.php:35
#: templates/emails/admin-pending-booking.php:28
#: templates/emails/customer-approved-booking.php:36
#: templates/emails/customer-cancelled-booking.php:34
#: templates/emails/customer-confirmation-booking.php:40
#: templates/emails/customer-pending-booking.php:37
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:35
msgid "Phone: %s"
msgstr "Telefon: %s"

#: templates/emails/admin-customer-cancelled-booking.php:30
#: templates/emails/admin-customer-confirmed-booking.php:30
#: templates/emails/admin-payment-confirmed-booking.php:37
#: templates/emails/admin-pending-booking.php:30
#: templates/emails/customer-approved-booking.php:38
#: templates/emails/customer-cancelled-booking.php:36
#: templates/emails/customer-confirmation-booking.php:42
#: templates/emails/customer-pending-booking.php:39
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:37
msgid "Note: %s"
msgstr "Ważne: %s"

#: templates/emails/admin-customer-confirmed-booking.php:15
msgid "Booking #%s is confirmed by customer."
msgstr "Rezerwacja #%s została potwierdzona przez klienta."

#: templates/emails/admin-payment-confirmed-booking.php:15
msgid "Booking #%s is confirmed by payment."
msgstr "Rezerwacja #%s jest potwierdzona płatnością."

#: templates/emails/admin-payment-confirmed-booking.php:17
msgid "Details of payment"
msgstr "Szczegóły płatności"

#: templates/emails/admin-payment-confirmed-booking.php:18
msgid "Payment ID: #%s"
msgstr "Numer ID płatności: #%s"

#: templates/emails/admin-payment-confirmed-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:16
msgid "Amount: %s"
msgstr "Razem: %s"

#: templates/emails/admin-payment-confirmed-booking.php:22
msgid "Method: %s"
msgstr "Metoda: %s"

#: templates/emails/admin-pending-booking.php:15
msgid "Booking #%s is pending for Administrator approval."
msgstr "Rezerwacja #%s oczekuje na potwierdzenie przez administratora."

#: templates/emails/cancellation-details.php:14
msgid "Click the link below to cancel your booking."
msgstr "Kliknij w poniższy link aby anulować rezerwację."

#: templates/emails/cancellation-details.php:16
msgid "Cancel your booking"
msgstr "Anuluj rezerwację"

#: templates/emails/customer-approved-booking.php:15
msgid "Dear %1$s %2$s, your reservation is approved!"
msgstr "%1$s %2$s, Twoja rezerwacja została potwierdzona!"

#: templates/emails/customer-approved-booking.php:18
#: templates/emails/customer-cancelled-booking.php:19
#: templates/emails/customer-confirmation-booking.php:25
#: templates/emails/customer-pending-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:20
msgid "ID: #%s"
msgstr ""

#: templates/emails/customer-approved-booking.php:41
#: templates/emails/customer-cancelled-booking.php:38
#: templates/emails/customer-confirmation-booking.php:44
#: templates/emails/customer-pending-booking.php:41
#: templates/emails/customer-registration.php:26
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:39
msgid "Thank you!"
msgstr "Dziękujemy!"

#: templates/emails/customer-cancelled-booking.php:15
msgid "Dear %1$s %2$s, your reservation is cancelled!"
msgstr "%1$s %2$s, Twoja rezerwacja została odwołana!"

#: templates/emails/customer-confirmation-booking.php:14
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:12
msgid "Dear %1$s %2$s, we received your request for reservation."
msgstr "%1$s %2$s, otrzymaliśmy prośbę o rezerwację."

#: templates/emails/customer-confirmation-booking.php:16
msgid "Click the link below to confirm your booking."
msgstr "Kliknij w poniższy link aby potwierdzić rezerwację."

#: templates/emails/customer-confirmation-booking.php:18
msgid "Confirm"
msgstr "Potwierdź"

#: templates/emails/customer-confirmation-booking.php:20
msgid "Note: link expires on"
msgstr "Uwaga: link wygasa"

#: templates/emails/customer-confirmation-booking.php:20
msgid "UTC"
msgstr ""

#: templates/emails/customer-confirmation-booking.php:22
msgid "If you did not place this booking, please ignore this email."
msgstr "Jeśli niczego nie rezerwowałeś, zignoruj tę wiadomość."

#: templates/emails/customer-pending-booking.php:15
msgid "Dear %1$s %2$s, your reservation is pending."
msgstr "%1$s %2$s, Twoja rezerwacja jest w oczekiwaniu."

#: templates/emails/customer-pending-booking.php:17
msgid "We will notify you by email once it is confirmed by our staff."
msgstr "Poinformujemy Cię e-mailem gdy zostanie potwierdzone przez naszych pracowników."

#: templates/emails/customer-registration.php:15
msgid "Hi %1$s %2$s,"
msgstr ""

#: templates/emails/customer-registration.php:17
msgid "Thanks for creating an account on %1$s."
msgstr ""

#: templates/emails/customer-registration.php:19
msgid "You Account Details"
msgstr ""

#: templates/emails/customer-registration.php:20
msgid "Login: %s"
msgstr ""

#: templates/emails/customer-registration.php:21
msgid "Password: %s"
msgstr ""

#: templates/emails/customer-registration.php:22
msgid "Log in here: %s"
msgstr ""

#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:14
msgid "To confirm your booking, please follow the instructions below for payment."
msgstr ""

#: templates/emails/reserved-room-details.php:14
msgid "Accommodation #%s"
msgstr "Nocleg #%s"

#: templates/emails/reserved-room-details.php:21
msgid "Accommodation: <a href=\"%1$s\">%2$s</a>"
msgstr "Obiekt: <a href=\"%1$s\">%2$s</a>"

#: templates/emails/reserved-room-details.php:24
msgid "Accommodation Rate: %s"
msgstr "Ranking obiektu: %s"

#: templates/emails/reserved-room-details.php:28
msgid "Bed Type: %s"
msgstr "Typ łóżka: %s"

#: templates/required-fields-tip.php:8
msgid "Required fields are followed by"
msgstr "Wymagane pola"

#: templates/shortcodes/booking-cancellation/already-cancelled.php:7
msgid "Booking is already canceled."
msgstr ""

#: templates/shortcodes/booking-cancellation/booking-cancellation-button.php:15
msgid "Cancel Booking"
msgstr ""

#: templates/shortcodes/booking-cancellation/invalid-request.php:7
#: templates/shortcodes/booking-confirmation/invalid-request.php:7
msgid "Invalid request."
msgstr "Nieprawidłowe żądanie."

#: templates/shortcodes/booking-cancellation/not-possible.php:7
msgid "Cancelation of your booking is not possible for some reason. Please contact the website administrator."
msgstr ""

#: templates/shortcodes/booking-confirmation/already-confirmed.php:7
msgid "Booking is already confirmed."
msgstr "Rezerwacja już została potwierdzona."

#: templates/shortcodes/booking-confirmation/confirmed.php:7
msgid "Your booking is confirmed. Thank You!"
msgstr "Rezerwacja jest potwierdzona. Dziękujemy!"

#: templates/shortcodes/booking-confirmation/expired.php:7
msgid "Your booking request is expired. Please start a new booking request."
msgstr "Czas składania żądania rezerwacji wygasł. Zacznij nowe żądanie."

#: templates/shortcodes/booking-confirmation/not-possible.php:7
msgid "Confirmation of your booking request is not possible for some reason. Please start a new booking request."
msgstr "Potwierdzenie żądania o rezerwację nie jest możliwe. Zacznij nowe żądanie o rezerwację."

#: templates/shortcodes/booking-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received and confirmed."
msgstr "Miło nam poinformować, że Państwa prośba o rezerwację została przyjęta i potwierdzona."

#: templates/shortcodes/booking-details/booking-details.php:21
msgid "Booking:"
msgstr "Rezerwacja:"

#: templates/shortcodes/booking-details/booking-details.php:47
msgid "Details:"
msgstr "Szczegóły:"

#: templates/shortcodes/payment-confirmation/completed.php:11
msgid "Thank you for your payment. Your transaction has been completed."
msgstr "Dziękujemy za płatność. Transakcja została zakończona."

#: templates/shortcodes/payment-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received."
msgstr "Miło nam poinformować, że otrzymaliśmy Twoją prośbę o rezerwację."

#: templates/shortcodes/room-rates/rate-content.php:17
msgid "from %s"
msgstr "od %s"

#: templates/shortcodes/rooms/not-found.php:7
msgid "No accommodations matching criteria."
msgstr "Brak obiektów, spełniających kryteria."

#: templates/shortcodes/services/not-found.php:7
msgid "No services matched criteria."
msgstr "Brak usług, spełniających kryteria."

#: templates/widgets/rooms/not-found.php:6
msgid "Nothing found."
msgstr "Niczego nie znaleziono."

#: templates/widgets/search-availability/search-form.php:105
msgid "Children %s:"
msgstr "Dzieci %s:"

#: assets/blocks/blocks.js:178
#: assets/blocks/blocks.js:190
msgid "Preset date. Formatted as %s"
msgstr "Ustawiona data. Sformatowano jako %s"

#: assets/blocks/blocks.js:283
#: assets/blocks/blocks.js:1425
#: assets/blocks/blocks.js:1507
msgid "Select an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:284
#: assets/blocks/blocks.js:1426
#: assets/blocks/blocks.js:1508
msgid "ID of an accommodation type. Leave blank to use current."
msgstr ""

#: assets/blocks/blocks.js:460
#: assets/blocks/blocks.js:710
#: assets/blocks/blocks.js:1239
msgid "Gallery"
msgstr "Galeria"

#: assets/blocks/blocks.js:508
#: assets/blocks/blocks.js:758
#: assets/blocks/blocks.js:1287
msgid "View Button"
msgstr "Przycisk Wyświetl"

#: assets/blocks/blocks.js:522
#: assets/blocks/blocks.js:558
#: assets/blocks/blocks.js:858
#: assets/blocks/blocks.js:894
#: assets/blocks/blocks.js:1042
#: assets/blocks/blocks.js:1078
msgid "Order"
msgstr "Zamówienie"

#: assets/blocks/blocks.js:530
#: assets/blocks/blocks.js:866
#: assets/blocks/blocks.js:1050
msgid "Order By"
msgstr "Zamów według"

#: assets/blocks/blocks.js:533
#: assets/blocks/blocks.js:869
#: assets/blocks/blocks.js:1053
msgid "No order"
msgstr "Brak zamówienia"

#: assets/blocks/blocks.js:534
#: assets/blocks/blocks.js:870
#: assets/blocks/blocks.js:1054
msgid "Post ID"
msgstr "ID posta"

#: assets/blocks/blocks.js:535
#: assets/blocks/blocks.js:871
#: assets/blocks/blocks.js:1055
msgid "Post author"
msgstr "Autor posta"

#: assets/blocks/blocks.js:536
#: assets/blocks/blocks.js:872
#: assets/blocks/blocks.js:1056
msgid "Post title"
msgstr "Tytuł posta"

#: assets/blocks/blocks.js:537
#: assets/blocks/blocks.js:873
#: assets/blocks/blocks.js:1057
msgid "Post name (post slug)"
msgstr "Nazwa posta (post slug)"

#: assets/blocks/blocks.js:538
#: assets/blocks/blocks.js:874
#: assets/blocks/blocks.js:1058
msgid "Post date"
msgstr "Data wysłania"

#: assets/blocks/blocks.js:539
#: assets/blocks/blocks.js:875
#: assets/blocks/blocks.js:1059
msgid "Last modified date"
msgstr "Data ostatniej modyfikacji"

#: assets/blocks/blocks.js:540
#: assets/blocks/blocks.js:876
#: assets/blocks/blocks.js:1060
msgid "Parent ID"
msgstr ""

#: assets/blocks/blocks.js:541
#: assets/blocks/blocks.js:877
#: assets/blocks/blocks.js:1061
msgid "Random order"
msgstr "Losowe zamówienie"

#: assets/blocks/blocks.js:542
#: assets/blocks/blocks.js:878
#: assets/blocks/blocks.js:1062
msgid "Number of comments"
msgstr "Liczba komentarzy"

#: assets/blocks/blocks.js:543
#: assets/blocks/blocks.js:879
#: assets/blocks/blocks.js:1063
msgid "Relevance"
msgstr "Trafność"

#: assets/blocks/blocks.js:544
#: assets/blocks/blocks.js:880
#: assets/blocks/blocks.js:1064
msgid "Page order"
msgstr "Kolejność stron"

#: assets/blocks/blocks.js:545
#: assets/blocks/blocks.js:881
#: assets/blocks/blocks.js:1065
msgid "Meta value"
msgstr "Wartość meta"

#: assets/blocks/blocks.js:546
#: assets/blocks/blocks.js:882
#: assets/blocks/blocks.js:1066
msgid "Numeric meta value"
msgstr "Numeryczna wartość meta"

#: assets/blocks/blocks.js:561
#: assets/blocks/blocks.js:897
#: assets/blocks/blocks.js:1081
msgid "Ascending (1, 2, 3)"
msgstr "Rosnąco (1, 2, 3)"

#: assets/blocks/blocks.js:562
#: assets/blocks/blocks.js:898
#: assets/blocks/blocks.js:1082
msgid "Descending (3, 2, 1)"
msgstr "Malejąco (3, 2, 1)"

#: assets/blocks/blocks.js:573
#: assets/blocks/blocks.js:909
#: assets/blocks/blocks.js:1093
msgid "Meta Name"
msgstr ""

#: assets/blocks/blocks.js:585
#: assets/blocks/blocks.js:921
#: assets/blocks/blocks.js:1105
msgid "Meta Type"
msgstr ""

#: assets/blocks/blocks.js:586
#: assets/blocks/blocks.js:922
#: assets/blocks/blocks.js:1106
msgid "Specified type of the custom field. Can be used in conjunction with \"orderby\" = \"meta_value\"."
msgstr "Określony typ pola niestandardowego. Może być używane w połączeniu z \" orderby \\ \"= \" meta_value \\ \"."

#: assets/blocks/blocks.js:589
#: assets/blocks/blocks.js:925
#: assets/blocks/blocks.js:1109
msgid "Any"
msgstr "Każdy"

#: assets/blocks/blocks.js:590
#: assets/blocks/blocks.js:926
#: assets/blocks/blocks.js:1110
msgid "Numeric"
msgstr "Numeryczny"

#: assets/blocks/blocks.js:591
#: assets/blocks/blocks.js:927
#: assets/blocks/blocks.js:1111
msgid "Binary"
msgstr "Binarny"

#: assets/blocks/blocks.js:592
#: assets/blocks/blocks.js:928
#: assets/blocks/blocks.js:1112
msgid "String"
msgstr ""

#: assets/blocks/blocks.js:594
#: assets/blocks/blocks.js:930
#: assets/blocks/blocks.js:1114
msgid "Time"
msgstr "Czas"

#: assets/blocks/blocks.js:595
#: assets/blocks/blocks.js:931
#: assets/blocks/blocks.js:1115
msgid "Date and time"
msgstr "Data i godzina"

#: assets/blocks/blocks.js:596
#: assets/blocks/blocks.js:932
#: assets/blocks/blocks.js:1116
msgid "Decimal number"
msgstr "Liczba dziesiętna"

#: assets/blocks/blocks.js:597
#: assets/blocks/blocks.js:933
#: assets/blocks/blocks.js:1117
msgid "Signed number"
msgstr "Podpisany numer"

#: assets/blocks/blocks.js:598
#: assets/blocks/blocks.js:934
#: assets/blocks/blocks.js:1118
msgid "Unsigned number"
msgstr "Numer bez znaku"

#: assets/blocks/blocks.js:784
#: assets/blocks/blocks.js:1009
msgid "Query Settings"
msgstr "Ustawienia zapytania"

#: assets/blocks/blocks.js:840
msgid "Relation"
msgstr "Relacja"

#: assets/blocks/blocks.js:1029
msgid "Values: integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "Wartości: integer, -1 aby wyświetlić wszystko, domyślnie: \"Strony blogów pokazują co najwyżej\""

#: assets/blocks/blocks.js:1203
msgid "Select an accommodation type."
msgstr ""

