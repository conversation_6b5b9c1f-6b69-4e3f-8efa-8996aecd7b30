# Copyright (C) 2025 MotoPress
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: hotel-booking-plugin\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/motopress-hotel-booking\n"
"Last-Translator: \n"
"Language-Team: German\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-19T19:58:50+00:00\n"
"PO-Revision-Date: 2025-03-05 20:50\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: motopress-hotel-booking\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: hotel-booking-plugin\n"
"X-Crowdin-Project-ID: 463550\n"
"X-Crowdin-Language: de\n"
"X-Crowdin-File: motopress-hotel-booking.pot\n"
"X-Crowdin-File-ID: 44\n"
"Language: de_DE\n"

#. Plugin Name of the plugin
#. translators: Name of the plugin, do not translate
#: motopress-hotel-booking.php
#: includes/script-managers/block-script-manager.php:27
msgid "Hotel Booking"
msgstr "Hotel Booking"

#. Plugin URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/products/hotel-booking/"
msgstr "https://motopress.com/products/hotel-booking/"

#. Description of the plugin
#: motopress-hotel-booking.php
msgid "Manage your hotel booking services. Perfect for hotels, villas, guest houses, hostels, and apartments of all sizes."
msgstr "Verwalten Sie Ihre Hotelbuchung-Services. Ideal für Hotels, Villen, Pensionen, Hostels und Appartements aller Größen."

#. Author of the plugin
#: motopress-hotel-booking.php
msgid "MotoPress"
msgstr "MotoPress"

#. Author URI of the plugin
#: motopress-hotel-booking.php
msgid "https://motopress.com/"
msgstr "https://motopress.com/"

#: functions.php:71
msgctxt "Post Status"
msgid "New"
msgstr "Neu"

#: functions.php:74
msgctxt "Post Status"
msgid "Auto Draft"
msgstr "Auto-Entwurf"

#. translators: %s: URL to plugins.php page
#: functions.php:518
msgid "You are using two instances of Hotel Booking plugin at the same time, please <a href=\"%s\">deactivate one of them</a>."
msgstr "Sie verwenden zwei Instanzen von Hotel Booking plugin gleichzeitig, bitte <a href=\"%s\">deaktivieren Sie eine von ihnen</a>."

#: functions.php:535
msgid "<a href=\"%s\">Upgrade to Premium</a> to enable this feature."
msgstr "<a href=\"%s\">Upgrade auf Premium</a> um diese Funktion zu aktivieren."

#: includes/actions-handler.php:100
#: includes/admin/sync-logs-list-table.php:91
#: includes/csv/csv-export-handler.php:33
#: includes/csv/csv-export-handler.php:51
#: includes/payments/gateways/stripe-gateway.php:560
#: includes/payments/gateways/stripe-gateway.php:572
#: includes/payments/gateways/stripe-gateway.php:631
msgid "Error"
msgstr "Fehler"

#: includes/admin/customers-list-table.php:143
#: includes/admin/menu-pages/rooms-generator-menu-page.php:84
#: includes/admin/sync-rooms-list-table.php:146
#: includes/post-types/room-type-cpt.php:354
#: templates/account/bookings.php:80
msgid "View"
msgstr "Ansicht"

#: includes/admin/customers-list-table.php:147
#: includes/admin/fields/abstract-complex-field.php:25
#: includes/admin/fields/rules-list-field.php:61
#: includes/admin/sync-rooms-list-table.php:147
msgid "Delete"
msgstr "Löschen"

#: includes/admin/customers-list-table.php:212
#: includes/post-types/attributes-cpt.php:301
msgid "Name"
msgstr "Name"

#: includes/admin/customers-list-table.php:213
#: includes/admin/menu-pages/customers-menu-page.php:207
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:122
#: includes/bundles/customer-bundle.php:110
#: includes/csv/bookings/bookings-exporter-helper.php:83
#: includes/post-types/booking-cpt.php:106
#: includes/post-types/payment-cpt.php:263
#: includes/views/shortcodes/checkout-view.php:618
#: templates/account/account-details.php:34
msgid "Email"
msgstr "E-Mail"

#: includes/admin/customers-list-table.php:214
#: includes/admin/menus.php:72
#: includes/admin/menus.php:73
#: includes/post-types/booking-cpt.php:241
#: includes/shortcodes/account-shortcode.php:239
msgid "Bookings"
msgstr "Buchungen"

#: includes/admin/customers-list-table.php:215
msgid "Date Registered"
msgstr "Registrierungsdatum"

#: includes/admin/customers-list-table.php:216
msgid "Last Active"
msgstr "Zuletzt aktiv"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:16
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:33
msgid "Terms"
msgstr "Begriffe"

#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:27
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:46
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:41
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:115
msgid "Created on:"
msgstr "Erstellt am:"

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:88
msgid "Please add attribute in default language to configure terms."
msgstr "Bitte fügen Sie ein Attribut in der Standardsprache hinzu, um Begriffe zu konfigurieren."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/edit-cpt-pages/attributes-edit-cpt-page.php:98
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:116
msgid "Configure terms"
msgstr "Begriffe konfigurieren"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:20
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:107
#: includes/post-types/reserved-room-cpt.php:22
msgid "Reserved Accommodations"
msgstr "Reservierte Unterkünfte"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:21
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:66
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Update Booking"
msgstr "Buchung aktualisieren"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:22
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:12
msgid "Logs"
msgstr "Logs"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:54
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:125
msgid "Delete Permanently"
msgstr "Dauerhaft löschen"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:58
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:56
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:127
msgid "Move to Trash"
msgstr "Ab in den Müll"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:69
msgid "Create Booking"
msgstr "Buchung anlegen"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:85
#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:98
msgid "Resend Email"
msgstr "E-Mail erneut senden"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:100
msgid "Send a copy of the Approved Booking email to the customer`s email address."
msgstr "Kopie der genehmigten Buchung per E-Mail an die E-Mail-Adresse des Kunden senden."

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:116
#: templates/edit-booking/edit-reserved-rooms.php:35
msgid "Edit Accommodations"
msgstr "Unterkünfte bearbeiten"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:125
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:85
#: includes/shortcodes/booking-confirmation-shortcode.php:298
msgid "Date:"
msgstr "Datum:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:130
msgid "Author:"
msgstr "Autor:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:145
#: includes/payments/gateways/stripe-gateway.php:528
msgid "Auto"
msgstr "Automatisch"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:155
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:89
msgid "Message:"
msgstr "Nachricht:"

#: includes/admin/edit-cpt-pages/booking-edit-cpt-page.php:227
msgid "Confirmation email has been sent to customer."
msgstr "Bestätigungsmail ist an den Kunden gesendet worden."

#: includes/admin/edit-cpt-pages/coupon-edit-cpt-page.php:14
msgid "Coupon code"
msgstr "Gutscheincode"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:11
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:64
#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Update Payment"
msgstr "Zahlung aktualisieren"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:44
msgid "Modified on:"
msgstr "Geändert am:"

#: includes/admin/edit-cpt-pages/payment-edit-cpt-page.php:67
msgid "Create Payment"
msgstr "Zahlung anlegen"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:47
msgid "Season Prices"
msgstr "Saison-Preise"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:48
msgid "<code>Please select Accommodation Type and click Create Rate button to continue.</code>"
msgstr "<code>Bitte wählen Sie Unterkunftsart and Klicken Sie auf den Button „Rate erstellen“, um fortzufahren.</code>"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:66
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:65
#: includes/views/loop-room-type-view.php:113
#: includes/views/single-room-type-view.php:205
#: template-functions.php:920
#: templates/create-booking/results/reserve-rooms.php:51
#: templates/widgets/rooms/room-content.php:77
#: templates/widgets/search-availability/search-form.php:78
msgid "Adults:"
msgstr "Erwachsene:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:68
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:66
#: includes/views/loop-room-type-view.php:128
#: includes/views/single-room-type-view.php:220
#: template-functions.php:925
#: templates/create-booking/results/reserve-rooms.php:52
#: templates/widgets/rooms/room-content.php:91
#: templates/widgets/search-availability/search-form.php:103
msgid "Children:"
msgstr "Kinder:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:70
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:63
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:110
#: includes/shortcodes/booking-confirmation-shortcode.php:306
#: includes/shortcodes/search-results-shortcode.php:770
#: includes/shortcodes/search-results-shortcode.php:921
#: templates/shortcodes/booking-details/booking-details.php:33
msgid "Total:"
msgstr "Gesamt:"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:80
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Update Rate"
msgstr "Rate aktualisieren"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:97
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
msgid "Active"
msgstr "Aktiv"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:98
#: includes/admin/groups/license-settings-group.php:61
msgid "Disabled"
msgstr "Deaktiviert"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:138
msgid "Create Rate"
msgstr "Rate erstellen"

#: includes/admin/edit-cpt-pages/rate-edit-cpt-page.php:190
msgid "Duplicate Rate"
msgstr "Rate duplizieren"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:12
#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:36
#: includes/admin/menu-pages/booking-rules-menu-page.php:219
#: includes/admin/menu-pages/booking-rules-menu-page.php:264
#: includes/admin/menu-pages/booking-rules-menu-page.php:310
#: includes/admin/menu-pages/booking-rules-menu-page.php:356
#: includes/admin/menu-pages/booking-rules-menu-page.php:487
#: includes/admin/menu-pages/booking-rules-menu-page.php:533
#: includes/admin/menu-pages/booking-rules-menu-page.php:579
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:208
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:304
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:377
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:448
#: includes/post-types/room-cpt.php:31
#: includes/post-types/room-cpt.php:41
#: includes/wizard.php:103
msgid "Accommodations"
msgstr "Unterkünfte"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:15
#: includes/post-types/attributes-cpt.php:54
#: includes/post-types/attributes-cpt.php:61
#: includes/post-types/attributes-cpt.php:65
msgid "Attributes"
msgstr "Attribute"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:18
msgid "Accommodation Reviews"
msgstr "Bewertungen der Unterkünfte"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:27
msgid "Allow guests to <a href=\"%s\" target=\"_blank\">submit star ratings and reviews</a> evaluating your accommodations."
msgstr "Erlauben Sie Ihren Gästen, <a href=\"%s\" target=\"_blank\">Sternbewertungen und Rezensionen</a> , die  Ihrer Unterkunft bewerten, einzureichen."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:69
msgid "Number of Accommodations:"
msgstr "Anzahl der Unterkünfte:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:74
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:29
msgid "Count of real accommodations of this type in your hotel."
msgstr "Anzahl von reallen Unterkünften dieser Art in Ihrem Hotel."

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:122
msgid "Total Accommodations:"
msgstr "Gesamtanzahl der Unterkünfte:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:135
#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:159
msgid "Show Accommodations"
msgstr "Unterkünfte anzeigen"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:139
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:131
#: includes/admin/menu-pages/rooms-generator-menu-page.php:18
#: includes/admin/menu-pages/rooms-generator-menu-page.php:146
#: includes/admin/menu-pages/rooms-generator-menu-page.php:150
msgid "Generate Accommodations"
msgstr "Unterkünfte generieren"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:147
msgid "Active Accommodations:"
msgstr "Aktive Unterkünfte:"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:168
#: includes/post-types/room-cpt.php:93
msgid "Linked Accommodations"
msgstr "Verlinkte Unterkünfte"

#: includes/admin/edit-cpt-pages/room-type-edit-cpt-page.php:173
msgid "Link accommodations on the Edit Accommodation page to ensure bookings for one make any linked properties unavailable for the same dates."
msgstr "Link accommodations on the Edit Accommodation page to ensure bookings for one make any linked properties unavailable for the same dates."

#: includes/admin/fields/abstract-complex-field.php:24
#: includes/admin/fields/rules-list-field.php:57
#: templates/edit-booking/add-room-popup.php:45
msgid "Add"
msgstr "Hinzufügen"

#: includes/admin/fields/amount-field.php:74
msgid "Per adult:"
msgstr "Pro Erwachsener:"

#: includes/admin/fields/amount-field.php:77
msgid "Per child:"
msgstr "Pro Kind:"

#: includes/admin/fields/amount-field.php:198
msgid "Per adult: "
msgstr "Pro Erwachsener: "

#: includes/admin/fields/amount-field.php:200
msgid "Per child: "
msgstr "Pro Kind: "

#: includes/admin/fields/complex-horizontal-field.php:71
#: includes/admin/fields/rules-list-field.php:62
#: templates/account/bookings.php:22
#: templates/account/bookings.php:79
#: templates/edit-booking/edit-reserved-rooms.php:47
msgid "Actions"
msgstr "Aktionen"

#: includes/admin/fields/complex-horizontal-field.php:111
msgid "Move up"
msgstr "Nach oben"

#: includes/admin/fields/complex-horizontal-field.php:112
msgid "Move down"
msgstr "Nach unten"

#: includes/admin/fields/complex-horizontal-field.php:113
msgid "Move to top"
msgstr "Nach ganz oben verschieben"

#: includes/admin/fields/complex-horizontal-field.php:114
msgid "Move to bottom"
msgstr "Nach unten verschieben"

#: includes/admin/fields/complex-vertical-field.php:17
#: includes/admin/menu-pages/settings-menu-page.php:580
#: includes/settings/main-settings.php:25
#: includes/settings/main-settings.php:43
msgid "Default"
msgstr "Standard"

#: includes/admin/fields/dynamic-select-field.php:61
#: includes/admin/fields/page-select-field.php:16
#: includes/admin/menu-pages/customers-menu-page.php:260
#: includes/admin/menu-pages/rooms-generator-menu-page.php:38
#: includes/admin/menu-pages/settings-menu-page.php:420
#: includes/post-types/booking-cpt.php:122
#: includes/post-types/booking-cpt.php:179
#: includes/post-types/payment-cpt.php:231
#: includes/post-types/rate-cpt.php:31
#: includes/post-types/room-cpt.php:79
#: includes/views/shortcodes/checkout-view.php:250
#: includes/views/shortcodes/checkout-view.php:273
#: templates/account/account-details.php:51
#: templates/edit-booking/add-room-popup.php:30
#: templates/edit-booking/add-room-popup.php:38
msgid "— Select —"
msgstr "— Wählen —"

#: includes/admin/fields/install-plugin-field.php:33
msgid "Install & Activate"
msgstr "Installieren und aktivieren"

#: includes/admin/fields/media-field.php:76
msgid "Add image"
msgstr "Bild hinzufügen"

#: includes/admin/fields/media-field.php:76
msgid "Add gallery"
msgstr "Galerie hinzufügen"

#: includes/admin/fields/media-field.php:77
msgid "Remove image"
msgstr "Bild entfernen"

#: includes/admin/fields/media-field.php:77
msgid "Remove gallery"
msgstr "Galerie entfernen"

#: includes/admin/fields/multiple-checkbox-field.php:88
#: template-functions.php:1088
msgid "Select all"
msgstr "Alles auswählen"

#: includes/admin/fields/multiple-checkbox-field.php:92
#: template-functions.php:1090
msgid "Unselect all"
msgstr "Alles wiederufen"

#: includes/admin/fields/notes-list-field.php:23
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:68
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:33
#: includes/csv/bookings/bookings-exporter-helper.php:112
#: assets/blocks/blocks.js:593
#: assets/blocks/blocks.js:929
#: assets/blocks/blocks.js:1113
msgid "Date"
msgstr "Datum"

#: includes/admin/fields/notes-list-field.php:33
msgid "Author"
msgstr "Autor"

#: includes/admin/fields/rules-list-field.php:59
#: includes/admin/room-list-table.php:154
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:115
#: includes/bookings-calendar.php:613
#: includes/script-managers/admin-script-manager.php:97
msgid "Edit"
msgstr "Bearbeiten"

#: includes/admin/fields/rules-list-field.php:60
#: includes/admin/sync-rooms-list-table.php:81
#: includes/ajax.php:951
#: includes/script-managers/admin-script-manager.php:98
msgid "Done"
msgstr "Fertig"

#: includes/admin/fields/rules-list-field.php:64
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:85
#: includes/admin/menu-pages/booking-rules-menu-page.php:180
#: includes/admin/menu-pages/booking-rules-menu-page.php:183
#: includes/admin/menu-pages/booking-rules-menu-page.php:407
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:211
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:307
#: includes/script-managers/admin-script-manager.php:95
msgid "All"
msgstr "Alle"

#: includes/admin/fields/rules-list-field.php:65
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:83
#: includes/post-types/coupon-cpt.php:81
#: includes/post-types/coupon-cpt.php:113
#: includes/post-types/coupon-cpt.php:158
#: includes/script-managers/admin-script-manager.php:96
msgid "None"
msgstr "Kein"

#: includes/admin/fields/time-picker-field.php:13
msgid "HH:MM"
msgstr "HH:MM"

#: includes/admin/fields/total-price-field.php:18
msgid "Recalculate Total Price"
msgstr "Gesamtpreis berechnen"

#: includes/admin/fields/variable-pricing-field.php:89
#: includes/views/booking-view.php:121
msgid "Nights"
msgstr "Nächte"

#: includes/admin/fields/variable-pricing-field.php:97
#: includes/script-managers/admin-script-manager.php:102
msgid "and more"
msgstr "und mehr"

#: includes/admin/fields/variable-pricing-field.php:98
#: includes/admin/menu-pages/edit-booking/edit-control.php:95
#: includes/script-managers/admin-script-manager.php:101
#: includes/shortcodes/search-results-shortcode.php:1009
#: includes/views/booking-view.php:400
#: templates/edit-booking/edit-reserved-rooms.php:70
msgid "Remove"
msgstr "Entfernen"

#: includes/admin/fields/variable-pricing-field.php:104
msgid "Add length of stay"
msgstr "Aufenthaltsdauer hinzufügen"

#: includes/admin/fields/variable-pricing-field.php:109
msgid "Base Occupancy"
msgstr "Basis-Besetzung"

#: includes/admin/fields/variable-pricing-field.php:110
#: includes/admin/fields/variable-pricing-field.php:170
msgid "Price per night"
msgstr "Preis pro Nacht"

#: includes/admin/fields/variable-pricing-field.php:118
#: includes/admin/fields/variable-pricing-field.php:167
#: includes/emails/templaters/reserved-rooms-templater.php:175
#: includes/post-types/room-type-cpt.php:283
#: includes/views/booking-view.php:105
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:242
#: template-functions.php:765
#: templates/create-booking/search/search-form.php:94
#: templates/shortcodes/search/search-form.php:80
#: assets/blocks/blocks.js:147
msgid "Adults"
msgstr "Erwachsene"

#: includes/admin/fields/variable-pricing-field.php:122
#: includes/csv/bookings/bookings-exporter-helper.php:80
#: includes/emails/templaters/reserved-rooms-templater.php:179
#: includes/post-types/room-type-cpt.php:292
#: includes/views/booking-view.php:116
#: templates/create-booking/search/search-form.php:109
#: templates/shortcodes/search/search-form.php:103
#: assets/blocks/blocks.js:162
msgid "Children"
msgstr "Kinder"

#: includes/admin/fields/variable-pricing-field.php:130
msgid "Price per extra adult"
msgstr "Preis pro zusätzlichen Erwachsenen"

#: includes/admin/fields/variable-pricing-field.php:137
msgid "Price per extra child"
msgstr "Preis pro zusätzliches Kind"

#: includes/admin/fields/variable-pricing-field.php:154
msgid "Enable variable pricing"
msgstr "Variable Preisgestaltung aktivieren"

#: includes/admin/fields/variable-pricing-field.php:188
msgid "Add Variation"
msgstr "Variation hinzufügen"

#: includes/admin/fields/variable-pricing-field.php:215
#: includes/admin/fields/variable-pricing-field.php:231
msgid "Remove variation"
msgstr "Attribute"

#: includes/admin/groups/license-settings-group.php:21
msgid "The License Key is required in order to get automatic plugin updates and support. You can manage your License Key in your personal account. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Learn more</a>."
msgstr "Der Lizenzschlüssel ist erforderlich, um automatische Plugin-Updates und Support zu erhalten. Sie können Ihren Lizenzschlüssel in Ihrem persönlichen Account verwalten. <a href='https://motopress.zendesk.com/hc/en-us/articles/*********-How-to-use-your-personal-MotoPress-account' target='_blank'>Mehr erfahren</a>."

#: includes/admin/groups/license-settings-group.php:28
msgid "License Key"
msgstr "Lizenzschlüssel"

#: includes/admin/groups/license-settings-group.php:42
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:62
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:22
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:28
#: includes/admin/menu-pages/create-booking/checkout-step.php:63
#: includes/admin/sync-logs-list-table.php:72
#: includes/admin/sync-rooms-list-table.php:127
#: includes/csv/bookings/bookings-exporter-helper.php:72
#: template-functions.php:977
#: templates/edit-booking/edit-reserved-rooms.php:46
msgid "Status"
msgstr "Status"

#: includes/admin/groups/license-settings-group.php:49
msgid "Inactive"
msgstr "Inaktiv"

#: includes/admin/groups/license-settings-group.php:55
msgid "Valid until"
msgstr "Gültig bis"

#: includes/admin/groups/license-settings-group.php:57
msgid "Valid (Lifetime)"
msgstr "Gültig (Lifetime)"

#: includes/admin/groups/license-settings-group.php:64
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:123
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:136
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:85
msgid "Expired"
msgstr "Abgelaufen"

#: includes/admin/groups/license-settings-group.php:67
msgid "Invalid"
msgstr "Ungültig"

#: includes/admin/groups/license-settings-group.php:71
msgid "Your License Key does not match the installed plugin. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'>How to fix this.</a>"
msgstr "Ihr Lizenzschlüssel stimmt nicht mit dem installierten Plugin überein. <a href='https://motopress.zendesk.com/hc/en-us/articles/202957243-What-to-do-if-the-license-key-doesn-t-correspond-with-the-plugin-license' target='_blank'> So beheben Sie das. </a>"

#: includes/admin/groups/license-settings-group.php:74
msgid "Product ID is not valid"
msgstr "Produkt-ID ist nicht gültig"

#: includes/admin/groups/license-settings-group.php:83
msgid "Action"
msgstr "Aktion"

#: includes/admin/groups/license-settings-group.php:90
msgid "Activate License"
msgstr "Lizenz aktivieren"

#: includes/admin/groups/license-settings-group.php:96
msgid "Deactivate License"
msgstr "Lizenz deaktivieren"

#: includes/admin/groups/license-settings-group.php:103
msgid "Renew License"
msgstr "Lizenz verlängern"

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:9
msgid "Attributes let you define extra accommodation data, such as location or type. You can use these attributes in the search availability form as advanced search filters."
msgstr "Mithilfe von Attributen können Sie zusätzliche Unterkunftsdaten definieren, z. B. Standort oder Unterkunftsart. Sie können die Attribute im Verfügbarkeitssuche-Formular als erweiterte Suchfilter verwenden."

#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:72
msgid "This attribute refers to non-unique taxonomy - %1$s - which was already registered with attribute %2$s."
msgstr "Dieses Attribut bezieht sich auf nicht eindeutige Taxonomie, - %1$s - die bereits mit dem Attribut %2$s registriert wurde."

#. translators: Terms are variations for Attributes and bear no relation to the Terms and Conditions Page.
#: includes/admin/manage-cpt-pages/attributes-manage-cpt-page.php:92
msgid "You cannot manage terms of trashed attributes."
msgstr "Sie können keine Begriffe für gelöschte Attribute verwalten."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:17
#: includes/admin/menu-pages/calendar-menu-page.php:31
#: includes/post-types/booking-cpt.php:246
msgid "New Booking"
msgstr "Neue Buchung"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:61
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:26
#: includes/admin/menu-pages/shortcodes-menu-page.php:376
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:64
#: includes/csv/bookings/bookings-exporter-helper.php:71
#: includes/post-types/booking-cpt.php:42
#: includes/post-types/payment-cpt.php:150
msgid "ID"
msgstr "ID"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:63
msgid "Check-in / Check-out"
msgstr "Anreise/Abreise"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:64
#: includes/views/booking-view.php:107
#: includes/views/edit-booking/checkout-view.php:143
#: includes/views/shortcodes/checkout-view.php:244
#: template-functions.php:767
#: templates/shortcodes/search/search-form.php:82
msgid "Guests"
msgstr "Gäste"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:65
#: templates/emails/admin-customer-cancelled-booking.php:23
#: templates/emails/admin-customer-confirmed-booking.php:23
#: templates/emails/admin-payment-confirmed-booking.php:30
#: templates/emails/admin-pending-booking.php:23
msgid "Customer Info"
msgstr "Kundeninformationen"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:66
#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:16
#: includes/post-types/rate-cpt.php:64
#: includes/post-types/service-cpt.php:132
#: includes/post-types/service-cpt.php:137
#: includes/views/single-service-view.php:18
#: includes/widgets/rooms-widget.php:201
#: assets/blocks/blocks.js:496
#: assets/blocks/blocks.js:547
#: assets/blocks/blocks.js:746
#: assets/blocks/blocks.js:883
#: assets/blocks/blocks.js:1067
#: assets/blocks/blocks.js:1275
msgid "Price"
msgstr "Preis"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:67
#: includes/admin/menu-pages/booking-rules-menu-page.php:402
#: includes/admin/room-list-table.php:93
#: includes/admin/sync-rooms-list-table.php:126
#: includes/bookings-calendar.php:829
#: includes/bookings-calendar.php:847
#: includes/csv/bookings/bookings-exporter-helper.php:77
#: includes/post-types/room-cpt.php:32
#: includes/post-types/room-cpt.php:74
#: includes/post-types/room-type-cpt.php:60
#: templates/edit-booking/add-room-popup.php:36
#: templates/edit-booking/edit-reserved-rooms.php:45
msgid "Accommodation"
msgstr "Unterkunft"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:121
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:83
msgid "Expire %s"
msgstr "Endet %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:138
#: includes/script-managers/admin-script-manager.php:99
msgid "Adults: "
msgstr "Erwachsene: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:142
#: includes/script-managers/admin-script-manager.php:100
msgid "Children: "
msgstr "Kinder: "

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:184
msgid "%s night"
msgid_plural "%s nights"
msgstr[0] "%s Nacht"
msgstr[1] "%s Nächte"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:197
#: includes/bookings-calendar.php:1189
msgid "Summary: %s."
msgstr "Überblick: %s."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:280
msgid "Paid: %s"
msgstr "Bezahlt: %s"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:345
msgid "Set to %s"
msgstr "Auf %s festlegen"

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:460
msgid "Booking status changed."
msgid_plural "%s booking statuses changed."
msgstr[0] "Buchungsstatus geändert."
msgstr[1] "%s Buchungsstatus geändert."

#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:506
msgid "All accommodation types"
msgstr "Alle Unterkunftsarten"

#. translators: The number of imported bookings: "Imported <span>(11)</span>"
#: includes/admin/manage-cpt-pages/booking-manage-cpt-page.php:526
msgid "Imported %s"
msgstr "Importierte %s"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:19
#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:29
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:168
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:264
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:349
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:420
#: includes/post-types/coupon-cpt.php:93
#: includes/post-types/coupon-cpt.php:124
#: includes/post-types/coupon-cpt.php:169
#: includes/post-types/payment-cpt.php:182
#: includes/views/booking-view.php:126
#: includes/views/booking-view.php:172
#: includes/views/booking-view.php:208
#: includes/views/booking-view.php:266
#: includes/views/booking-view.php:297
#: includes/views/booking-view.php:350
#: template-functions.php:978
msgid "Amount"
msgstr "Anzahl"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:20
msgid "Uses"
msgstr "Verwendet"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:21
#: includes/post-types/coupon-cpt.php:187
msgid "Expiration Date"
msgstr "Ablaufdatum"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:41
#: includes/views/edit-booking/checkout-view.php:115
#: template-functions.php:900
msgid "Accommodation:"
msgstr "Unterkunft:"

#. translators: %s is a coupon amount per day
#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:60
msgid "%s per day"
msgstr "%s pro Tag"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:67
msgid "Service:"
msgstr "Dienstleistung:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:88
msgid "Fee:"
msgstr "Gebühr:"

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:153
msgid "Note: the use of coupons is disabled in settings."
msgstr "Hinweis: Die Verwendung von Gutscheinen ist in den Einstellungen deaktiviert."

#: includes/admin/manage-cpt-pages/coupon-manage-cpt-page.php:165
#: includes/admin/menu-pages/settings-menu-page.php:299
msgid "Enable the use of coupons."
msgstr "Verwendung von Gutscheinen aktivieren."

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:27
#: includes/admin/menu-pages/customers-menu-page.php:299
msgid "Customer"
msgstr "Kunde"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:30
#: includes/post-types/booking-cpt.php:242
#: templates/account/bookings.php:18
#: templates/account/bookings.php:66
msgid "Booking"
msgstr "Buchung"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:31
#: includes/post-types/payment-cpt.php:159
msgid "Gateway"
msgstr "Gateway"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:32
#: includes/post-types/payment-cpt.php:223
msgid "Transaction ID"
msgstr "Transaktions-ID"

#: includes/admin/manage-cpt-pages/payment-manage-cpt-page.php:105
#: includes/admin/menu-pages/create-booking/booking-step.php:66
#: includes/bookings-calendar.php:606
#: includes/script-managers/admin-script-manager.php:103
msgid "Booking #%s"
msgstr "Buchung #%s"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:13
msgid "Rates are used to offer different prices of the same accommodation type depending on extra conditions, e.g. With Breakfast, With No Breakfast, Refundable etc. Guests will choose the preferable rate when submitting a booking request. Create one default rate if you have no price tiers. To add price variations for different periods - open a rate, add a season, and set the price."
msgstr "Die Preise werden verwendet, um verschiedene Preise der gleichen Unterkunftsart je nach Zusatzbedingungen, z.B. bei Frühstück, ohne Frühstück, erstattungsfähige etc. anzubieten. Wenn Sie eine Buchungsanfrage abschicken, wählen Sie den bevorzugten Tarif. Erstellen Sie einen Standardpreis, wenn Sie keine Preisangaben haben. Um Preisschwankungen für verschiedene Zeiträume hinzuzufügen - öffnen Sie einen Kurs, fügen Sie eine Jahreszeit und legen Sie den Preis."

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:23
#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:32
#: includes/admin/menu-pages/booking-rules-menu-page.php:393
#: includes/admin/menu-pages/rooms-generator-menu-page.php:34
#: includes/csv/bookings/bookings-exporter-helper.php:75
#: includes/post-types/rate-cpt.php:30
#: includes/post-types/room-cpt.php:84
#: includes/post-types/room-type-cpt.php:54
#: templates/create-booking/search/search-form.php:82
#: templates/edit-booking/add-room-popup.php:28
#: templates/edit-booking/edit-reserved-rooms.php:44
#: assets/blocks/blocks.js:282
#: assets/blocks/blocks.js:1202
#: assets/blocks/blocks.js:1424
#: assets/blocks/blocks.js:1506
msgid "Accommodation Type"
msgstr "Unterkunftsart"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:24
msgid "Season &#8212; Price"
msgstr "Saison &#8212; Preis"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:67
#: includes/post-types/rate-cpt.php:73
msgid "Add New Season Price"
msgstr "Neuen Saison-Preis hinzufügen"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:104
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:94
#: includes/post-types/season-cpt.php:71
msgid "Annually"
msgstr "Jährlich"

#. translators: %s: A date string such as "December 31, 2025".
#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:108
#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:98
msgid "Annually until %s"
msgstr "Jährlich bis %s"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:201
msgid "Duplicate"
msgstr "Duplizieren"

#: includes/admin/manage-cpt-pages/rate-manage-cpt-page.php:281
msgid "Rate was duplicated."
msgstr "Rate wurde dupliziert."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:13
msgid "These are real accommodations like rooms, apartments, houses, villas, beds (for hostels) etc."
msgstr "Das sind echte Unterkünfte wie Zimmer, Appartements, Häuser, Villen, Betten (für Jugendherbergen) etc."

#: includes/admin/manage-cpt-pages/room-manage-cpt-page.php:77
#: includes/admin/menu-pages/reports-menu-page.php:129
#: includes/bookings-calendar.php:746
msgid "All Accommodation Types"
msgstr "Alle Unterkunftsarten"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:11
msgid "These are not physical accommodations, but their types. E.g. standard double room. To specify the real number of existing accommodations, you'll need to use Generate Accommodations menu."
msgstr "Das sind keine physischen Unterkünfte, sondern ihre Typen, z.B. Standard-Doppelzimmer. Um die reale Anzahl der vorhandenen Unterkünfte anzugeben, müssen Sie das Menü \"Unterkünfte verwalten\" verwenden."

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:34
#: includes/post-types/room-type-cpt.php:275
#: includes/post-types/room-type-cpt.php:302
#: templates/create-booking/results/reserve-rooms.php:36
msgid "Capacity"
msgstr "Kapazität"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:35
msgid "Bed Type"
msgstr "Bettentyp"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:68
#: includes/views/loop-room-type-view.php:152
#: includes/views/single-room-type-view.php:244
#: templates/widgets/rooms/room-content.php:167
msgid "Size:"
msgstr "Größe:"

#: includes/admin/manage-cpt-pages/room-type-manage-cpt-page.php:114
msgid "Active:"
msgstr "Aktiv:"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:13
msgid "Seasons are real periods of time, dates or days that come with different prices for accommodations. E.g. Winter 2018 ($120 per night), Christmas ($150 per night)."
msgstr "Saisons sind echte Zeiträume, Daten oder Tage, die abhängig von Unterkunft mit unterschiedlichen Preisen kommen, z.B. Winter 2018 ($120 pro Nacht), Weihnachten ($150 pro Nacht)."

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:20
msgid "Start"
msgstr "Beginn"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:21
msgid "End"
msgstr "Ende"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:22
#: includes/admin/menu-pages/booking-rules-menu-page.php:210
#: includes/admin/menu-pages/booking-rules-menu-page.php:255
msgid "Days"
msgstr "Tage"

#: includes/admin/manage-cpt-pages/season-manage-cpt-page.php:23
#: includes/post-types/season-cpt.php:67
msgid "Repeat"
msgstr "Wiederholen"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:11
msgid "Services are extra offers that you can sell or give for free. E.g. Thai massage, transfer, babysitting. Guests can pre-order them when placing a booking."
msgstr "Services sind zusätzliche Angebote, die Sie verkaufen oder kostenlos zur Verfügung stellen können, z.B. Thai-Massage, Transfer, Babysitting. Die services können bei der Buchung vorbestellt werden."

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:17
#: includes/post-types/service-cpt.php:150
msgid "Periodicity"
msgstr "Periodizität"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:18
#: includes/post-types/service-cpt.php:206
msgid "Charge"
msgstr "Gebühr"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:41
#: includes/entities/service.php:193
#: includes/post-types/service-cpt.php:153
msgid "Per Day"
msgstr "Pro Tag"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:43
#: includes/post-types/service-cpt.php:154
msgid "Guest Choice"
msgstr "Wahl des Gastes"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:45
#: includes/entities/service.php:197
#: includes/post-types/service-cpt.php:152
msgid "Once"
msgstr "Einmal"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:203
#: includes/post-types/service-cpt.php:209
msgid "Per Guest"
msgstr "Pro Gast"

#: includes/admin/manage-cpt-pages/service-manage-cpt-page.php:49
#: includes/entities/service.php:205
#: includes/post-types/service-cpt.php:208
msgid "Per Accommodation"
msgstr "Pro Unterkunft"

#: includes/admin/manage-tax-pages/facility-manage-tax-page.php:11
msgid "These are accommodation amenities, generally free ones. E.g. air-conditioning, wifi."
msgstr "Diese Unterkunftsmöglichkeiten sind in der Regel kostenlos, z.B. Klimaanlage, WiFi."

#: includes/admin/menu-pages/booking-rules-menu-page.php:34
msgid "Booking rules saved."
msgstr "Buchungsregeln gespeichert."

#: includes/admin/menu-pages/booking-rules-menu-page.php:41
#: includes/admin/menu-pages/booking-rules-menu-page.php:602
#: includes/admin/menu-pages/booking-rules-menu-page.php:606
#: includes/admin/menu-pages/settings-menu-page.php:622
msgid "Booking Rules"
msgstr "Buchungsregeln"

#: includes/admin/menu-pages/booking-rules-menu-page.php:90
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:70
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:118
#: templates/account/account-details.php:94
msgid "Save Changes"
msgstr "Änderungen speichern"

#: includes/admin/menu-pages/booking-rules-menu-page.php:199
msgid "Check-in days"
msgstr "Anreisetage"

#: includes/admin/menu-pages/booking-rules-menu-page.php:200
msgid "Guests can check in any day."
msgstr "Die Gäste können jederzeit einchecken."

#: includes/admin/menu-pages/booking-rules-menu-page.php:201
#: includes/admin/menu-pages/booking-rules-menu-page.php:246
#: includes/admin/menu-pages/booking-rules-menu-page.php:291
#: includes/admin/menu-pages/booking-rules-menu-page.php:337
#: includes/admin/menu-pages/booking-rules-menu-page.php:383
#: includes/admin/menu-pages/booking-rules-menu-page.php:468
#: includes/admin/menu-pages/booking-rules-menu-page.php:514
#: includes/admin/menu-pages/booking-rules-menu-page.php:560
msgid "Add rule"
msgstr "Regel hinzufügen"

#: includes/admin/menu-pages/booking-rules-menu-page.php:229
#: includes/admin/menu-pages/booking-rules-menu-page.php:274
#: includes/admin/menu-pages/booking-rules-menu-page.php:320
#: includes/admin/menu-pages/booking-rules-menu-page.php:366
#: includes/admin/menu-pages/booking-rules-menu-page.php:497
#: includes/admin/menu-pages/booking-rules-menu-page.php:543
#: includes/admin/menu-pages/booking-rules-menu-page.php:589
#: includes/post-types/season-cpt.php:98
#: includes/post-types/season-cpt.php:108
msgid "Seasons"
msgstr "Saisons"

#: includes/admin/menu-pages/booking-rules-menu-page.php:244
msgid "Check-out days"
msgstr "Abreisetage"

#: includes/admin/menu-pages/booking-rules-menu-page.php:245
msgid "Guests can check out any day."
msgstr "Die Gäste können jederzeit auschecken."

#: includes/admin/menu-pages/booking-rules-menu-page.php:289
#: includes/admin/menu-pages/booking-rules-menu-page.php:300
msgid "Minimum stay"
msgstr "Mindestaufenthalt"

#: includes/admin/menu-pages/booking-rules-menu-page.php:290
msgid "There are no minimum stay rules."
msgstr "Mindestaufenthalt: keine Beschränkungen."

#: includes/admin/menu-pages/booking-rules-menu-page.php:301
#: includes/admin/menu-pages/booking-rules-menu-page.php:347
#: includes/admin/menu-pages/booking-rules-menu-page.php:478
#: includes/admin/menu-pages/booking-rules-menu-page.php:524
#: includes/admin/menu-pages/booking-rules-menu-page.php:570
msgid "nights"
msgstr "Nächte"

#: includes/admin/menu-pages/booking-rules-menu-page.php:335
#: includes/admin/menu-pages/booking-rules-menu-page.php:346
msgid "Maximum stay"
msgstr "Maximalaufenthalt"

#: includes/admin/menu-pages/booking-rules-menu-page.php:336
msgid "There are no maximum stay rules."
msgstr "Maximalaufenthalt: keine Beschränkungen."

#: includes/admin/menu-pages/booking-rules-menu-page.php:381
msgid "Block accommodation"
msgstr "Unterkunft blockieren"

#: includes/admin/menu-pages/booking-rules-menu-page.php:382
msgid "There are no blocking accommodation rules."
msgstr "Unterkunftstrakt: keine Beschränkungen."

#: includes/admin/menu-pages/booking-rules-menu-page.php:414
#: includes/bookings-calendar.php:723
#: includes/bookings-calendar.php:817
msgid "From"
msgstr "Von"

#: includes/admin/menu-pages/booking-rules-menu-page.php:424
msgid "Till"
msgstr "Bis"

#: includes/admin/menu-pages/booking-rules-menu-page.php:434
msgid "Restriction"
msgstr "Beschränkung"

#: includes/admin/menu-pages/booking-rules-menu-page.php:436
msgid "Not check-in rule marks the date as unavailable for check-in."
msgstr "Nicht Check-in Regel markiert das Datum als nicht verfügbar für den Check-in."

#: includes/admin/menu-pages/booking-rules-menu-page.php:437
msgid "Not check-out rule marks the date as unavailable for check-out."
msgstr "Keine Checkout-Regel markiert das Datum als nicht verfügbar für den Check-out."

#: includes/admin/menu-pages/booking-rules-menu-page.php:438
msgid "Not stay-in rule displays the date as blocked. This date is unavailable for check-in and check-out on the next date."
msgstr "Nicht aufgebrachte Regel zeigt das Datum als gesperrt an. Dieses Datum ist für den Check-in und den Check-out am nächsten Tag nicht verfügbar."

#: includes/admin/menu-pages/booking-rules-menu-page.php:439
msgid "Not stay-in with Not check-out rules completely block the selected date, additionally displaying the previous date as unavailable for check-in."
msgstr "Nicht übernachten mit Nicht Checkout-Regeln blockieren das gewählte Datum, zusätzlich wird das vorherige Datum als nicht verfügbar angezeigt."

#: includes/admin/menu-pages/booking-rules-menu-page.php:444
#: includes/script-managers/public-script-manager.php:208
msgid "Not check-in"
msgstr "Nicht einreisen"

#: includes/admin/menu-pages/booking-rules-menu-page.php:445
#: includes/script-managers/public-script-manager.php:209
msgid "Not check-out"
msgstr "Nicht auschecken"

#: includes/admin/menu-pages/booking-rules-menu-page.php:446
#: includes/script-managers/public-script-manager.php:207
msgid "Not stay-in"
msgstr "Nicht bleiben"

#: includes/admin/menu-pages/booking-rules-menu-page.php:454
msgid "Comment"
msgstr "Kommentar"

#: includes/admin/menu-pages/booking-rules-menu-page.php:466
#: includes/admin/menu-pages/booking-rules-menu-page.php:477
msgid "Minimum advance reservation"
msgstr "Mindestreservierung im Voraus"

#: includes/admin/menu-pages/booking-rules-menu-page.php:467
msgid "There are no minimum advance reservation rules."
msgstr "Es gibt keine Regeln für die Mindestreservierung im Voraus."

#: includes/admin/menu-pages/booking-rules-menu-page.php:512
#: includes/admin/menu-pages/booking-rules-menu-page.php:523
msgid "Maximum advance reservation"
msgstr "Maximale Reservierung im Voraus"

#: includes/admin/menu-pages/booking-rules-menu-page.php:513
msgid "There are no maximum advance reservation rules."
msgstr "Es gibt keine Regeln für die maximale Reservierung im Voraus."

#: includes/admin/menu-pages/booking-rules-menu-page.php:558
#: includes/admin/menu-pages/booking-rules-menu-page.php:569
msgid "Booking buffer"
msgstr "Buchungspuffer"

#: includes/admin/menu-pages/booking-rules-menu-page.php:559
msgid "There are no booking buffer rules."
msgstr "Es gibt keine Regeln für Buchungspuffer."

#: includes/admin/menu-pages/calendar-menu-page.php:41
#: includes/admin/menu-pages/calendar-menu-page.php:69
msgid "Booking Calendar"
msgstr "Buchungskalender"

#: includes/admin/menu-pages/calendar-menu-page.php:65
msgid "Calendar"
msgstr "Kalender"

#: includes/admin/menu-pages/create-booking-menu-page.php:135
#: includes/admin/menu-pages/create-booking-menu-page.php:169
#: includes/post-types/booking-cpt.php:244
msgid "Add New Booking"
msgstr "Neue Buchung hinzufügen"

#: includes/admin/menu-pages/create-booking-menu-page.php:136
msgid "Clear Search Results"
msgstr "Suchergebnisse löschen"

#: includes/admin/menu-pages/create-booking-menu-page.php:184
#: includes/admin/menu-pages/edit-booking-menu-page.php:69
msgid "Note: booking rules are disabled in the plugin settings and are not taken into account."
msgstr "Hinweis: Die Buchungsregeln sind in den Buchungseinstellungen deaktiviert und werden nicht berücksichtigt."

#: includes/admin/menu-pages/create-booking/booking-step.php:50
#: includes/shortcodes/checkout-shortcode/step-booking.php:478
msgid "Unable to create booking. Please try again."
msgstr "Die Buchung kann nicht erstellt werden. Bitte versuchen Sie es erneut."

#: includes/admin/menu-pages/create-booking/booking-step.php:74
#: includes/shortcodes/checkout-shortcode/step-booking.php:107
msgid "Booking is blocked due to maintenance reason. Please try again later."
msgstr "Die Buchung ist aus Wartungsgründen gesperrt. Bitte versuchen Sie es später noch einmal."

#: includes/admin/menu-pages/create-booking/booking-step.php:121
#: includes/admin/menu-pages/create-booking/booking-step.php:275
#: includes/admin/menu-pages/create-booking/checkout-step.php:130
#: includes/admin/menu-pages/edit-booking/booking-control.php:34
#: includes/admin/menu-pages/edit-booking/checkout-control.php:61
#: includes/admin/menu-pages/edit-booking/summary-control.php:56
#: includes/shortcodes/checkout-shortcode/step-booking.php:183
#: includes/shortcodes/checkout-shortcode/step-booking.php:363
#: includes/shortcodes/checkout-shortcode/step-checkout.php:170
#: includes/utils/parse-utils.php:250
msgid "There are no accommodations selected for reservation."
msgstr "Es sind keine Unterkünfte für die Reservierung ausgewählt."

#: includes/admin/menu-pages/create-booking/booking-step.php:123
#: includes/admin/menu-pages/create-booking/booking-step.php:155
#: includes/admin/menu-pages/create-booking/checkout-step.php:132
#: includes/admin/menu-pages/create-booking/checkout-step.php:165
#: includes/admin/menu-pages/create-booking/checkout-step.php:196
#: includes/utils/parse-utils.php:210
#: includes/utils/parse-utils.php:285
#: includes/utils/parse-utils.php:305
msgid "Selected accommodations are not valid."
msgstr "Ausgewählte Unterkünfte sind nicht gültig."

#: includes/admin/menu-pages/create-booking/booking-step.php:150
#: includes/admin/menu-pages/create-booking/checkout-step.php:160
#: includes/admin/menu-pages/create-booking/step.php:191
#: includes/ajax.php:612
#: includes/shortcodes/checkout-shortcode/step-booking.php:200
#: includes/shortcodes/checkout-shortcode/step-booking.php:207
#: includes/shortcodes/checkout-shortcode/step-checkout.php:187
#: includes/shortcodes/checkout-shortcode/step-checkout.php:199
#: includes/utils/parse-utils.php:301
msgid "Accommodation Type is not valid."
msgstr "Unterkunftsart ist nicht gültig."

#: includes/admin/menu-pages/create-booking/booking-step.php:160
#: includes/admin/menu-pages/create-booking/booking-step.php:184
#: includes/ajax.php:623
#: includes/shortcodes/checkout-shortcode/step-booking.php:213
#: includes/shortcodes/checkout-shortcode/step-booking.php:231
#: includes/utils/parse-utils.php:322
msgid "Rate is not valid."
msgstr "Die Rate ist nicht gültig."

#: includes/admin/menu-pages/create-booking/booking-step.php:189
#: includes/admin/menu-pages/create-booking/step.php:211
#: includes/admin/menu-pages/create-booking/step.php:215
#: includes/shortcodes/checkout-shortcode/step-booking.php:237
#: includes/shortcodes/search-results-shortcode.php:634
#: includes/utils/parse-utils.php:163
#: includes/utils/parse-utils.php:326
msgid "Adults number is not valid."
msgstr "Anzahl der Erwachsenen ist nicht gültig."

#: includes/admin/menu-pages/create-booking/booking-step.php:194
#: includes/admin/menu-pages/create-booking/step.php:235
#: includes/admin/menu-pages/create-booking/step.php:239
#: includes/ajax.php:500
#: includes/shortcodes/checkout-shortcode/step-booking.php:243
#: includes/shortcodes/search-results-shortcode.php:650
#: includes/utils/parse-utils.php:187
#: includes/utils/parse-utils.php:330
msgid "Children number is not valid."
msgstr "Kinderzahl ist nicht gültig."

#: includes/admin/menu-pages/create-booking/booking-step.php:199
#: includes/ajax.php:634
#: includes/shortcodes/checkout-shortcode/step-booking.php:248
#: includes/utils/parse-utils.php:334
msgid "The total number of guests is not valid."
msgstr "Die Gesamtanzahl an Gästen ist ungültig."

#: includes/admin/menu-pages/create-booking/booking-step.php:210
#: includes/admin/menu-pages/create-booking/checkout-step.php:181
#: includes/shortcodes/checkout-shortcode/step-booking.php:259
#: includes/shortcodes/checkout-shortcode/step-checkout.php:245
#: includes/utils/parse-utils.php:345
msgid "Selected dates do not meet booking rules for type %s"
msgstr "Die ausgewählten Daten entsprechen nicht den Buchungsregeln für den Typ%s"

#: includes/admin/menu-pages/create-booking/booking-step.php:263
#: includes/admin/menu-pages/create-booking/checkout-step.php:186
#: includes/utils/parse-utils.php:264
msgid "Accommodations are not available."
msgstr "Unterkünfte sind nicht verfügbar."

#: includes/admin/menu-pages/create-booking/checkout-step.php:170
#: includes/shortcodes/checkout-shortcode/step-checkout.php:234
msgid "There are no rates for requested dates."
msgstr "Es gibt keine Angebote für die angeforderten Daten."

#: includes/admin/menu-pages/create-booking/results-step.php:211
#: includes/admin/menu-pages/settings-menu-page.php:542
#: includes/wizard.php:93
msgid "Search Results"
msgstr "Suchergebnisse"

#: includes/admin/menu-pages/create-booking/search-step.php:47
#: includes/admin/menu-pages/create-booking/search-step.php:50
msgid "— Any —"
msgstr "— Beliebig —"

#: includes/admin/menu-pages/create-booking/step.php:34
msgid "Search parameters are not set."
msgstr "Suchparameter sind nicht festgelegt."

#: includes/admin/menu-pages/create-booking/step.php:129
#: includes/ajax.php:438
#: includes/script-managers/public-script-manager.php:223
#: includes/shortcodes/checkout-shortcode/step.php:53
#: includes/shortcodes/search-results-shortcode.php:665
#: includes/utils/parse-utils.php:87
msgid "Check-in date is not valid."
msgstr "Anreisedatum ist falsch."

#: includes/admin/menu-pages/create-booking/step.php:131
#: includes/shortcodes/checkout-shortcode/step.php:56
#: includes/shortcodes/search-results-shortcode.php:668
#: includes/utils/parse-utils.php:89
msgid "Check-in date cannot be earlier than today."
msgstr "Anreisedatum kann nicht früher als heute sein."

#: includes/admin/menu-pages/create-booking/step.php:157
#: includes/ajax.php:457
#: includes/script-managers/public-script-manager.php:224
#: includes/shortcodes/checkout-shortcode/step.php:90
#: includes/shortcodes/search-results-shortcode.php:686
#: includes/utils/parse-utils.php:120
msgid "Check-out date is not valid."
msgstr "Abreisedatum ist falsch."

#: includes/admin/menu-pages/create-booking/step.php:168
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:106
#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:210
#: includes/shortcodes/checkout-shortcode/step.php:101
#: includes/shortcodes/search-results-shortcode.php:698
#: includes/utils/parse-utils.php:131
msgid "Nothing found. Please try again with different search parameters."
msgstr "Nichts gefunden. Bitte versuchen Sie es erneut mit anderen Suchparametern."

#: includes/admin/menu-pages/customers-menu-page.php:54
msgid "Sorry, you are not allowed to access this page."
msgstr "Leider dürfen Sie auf diese Seite nicht zugreifen."

#: includes/admin/menu-pages/customers-menu-page.php:160
msgid "User ID"
msgstr "Benutzer-ID"

#: includes/admin/menu-pages/customers-menu-page.php:171
#: templates/account/account-details.php:30
msgid "Username"
msgstr "Benutzername"

#: includes/admin/menu-pages/customers-menu-page.php:183
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:114
#: includes/bundles/customer-bundle.php:92
#: includes/csv/bookings/bookings-exporter-helper.php:81
#: includes/post-types/booking-cpt.php:90
#: includes/post-types/payment-cpt.php:247
#: includes/views/shortcodes/checkout-view.php:588
#: templates/account/account-details.php:22
msgid "First Name"
msgstr "Vorname"

#: includes/admin/menu-pages/customers-menu-page.php:195
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:118
#: includes/bundles/customer-bundle.php:101
#: includes/csv/bookings/bookings-exporter-helper.php:82
#: includes/post-types/booking-cpt.php:98
#: includes/post-types/payment-cpt.php:255
#: includes/views/shortcodes/checkout-view.php:603
#: templates/account/account-details.php:26
msgid "Last Name"
msgstr "Nachname"

#: includes/admin/menu-pages/customers-menu-page.php:219
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:126
#: includes/bundles/customer-bundle.php:119
#: includes/csv/bookings/bookings-exporter-helper.php:84
#: includes/post-types/booking-cpt.php:114
#: includes/post-types/payment-cpt.php:271
#: includes/views/shortcodes/checkout-view.php:633
#: templates/account/account-details.php:38
msgid "Phone"
msgstr "Telefon"

#: includes/admin/menu-pages/customers-menu-page.php:231
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:134
#: includes/bundles/customer-bundle.php:137
#: includes/csv/bookings/bookings-exporter-helper.php:86
#: includes/post-types/booking-cpt.php:131
#: includes/views/shortcodes/checkout-view.php:675
#: templates/account/account-details.php:42
msgid "Address"
msgstr "Adresse"

#: includes/admin/menu-pages/customers-menu-page.php:243
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:142
#: includes/bundles/customer-bundle.php:155
#: includes/csv/bookings/bookings-exporter-helper.php:88
#: includes/post-types/booking-cpt.php:147
#: includes/post-types/payment-cpt.php:311
#: includes/views/shortcodes/checkout-view.php:705
#: templates/account/account-details.php:64
msgid "State / County"
msgstr "Bundesland"

#: includes/admin/menu-pages/customers-menu-page.php:255
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:130
#: includes/csv/bookings/bookings-exporter-helper.php:85
#: includes/post-types/booking-cpt.php:123
#: includes/post-types/payment-cpt.php:279
#: templates/account/account-details.php:46
msgid "Country"
msgstr "Land"

#: includes/admin/menu-pages/customers-menu-page.php:268
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:138
#: includes/bundles/customer-bundle.php:146
#: includes/csv/bookings/bookings-exporter-helper.php:87
#: includes/post-types/booking-cpt.php:139
#: includes/post-types/payment-cpt.php:303
#: includes/views/shortcodes/checkout-view.php:690
#: templates/account/account-details.php:68
msgid "City"
msgstr "Stadt"

#: includes/admin/menu-pages/customers-menu-page.php:280
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:146
#: includes/bundles/customer-bundle.php:164
#: includes/csv/bookings/bookings-exporter-helper.php:89
#: includes/post-types/booking-cpt.php:155
#: includes/views/shortcodes/checkout-view.php:720
#: templates/account/account-details.php:72
msgid "Postcode"
msgstr "Postleitzahl"

#: includes/admin/menu-pages/customers-menu-page.php:301
#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/i-cal-import-menu-page.php:162
#: includes/admin/menu-pages/i-cal-import-menu-page.php:209
#: includes/admin/menu-pages/i-cal-menu-page.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:40
#: includes/post-types/editable-cpt.php:95
msgid "Back"
msgstr "Zurück"

#: includes/admin/menu-pages/customers-menu-page.php:305
msgid "Edit User Profile"
msgstr "Benutzerprofil bearbeiten"

#: includes/admin/menu-pages/customers-menu-page.php:322
msgid "Customer data updated."
msgstr "Die Kundendaten wurden aktualisiert."

#: includes/admin/menu-pages/customers-menu-page.php:328
msgid "User account updated."
msgstr "Benutzerkonto wurde geändert."

#: includes/admin/menu-pages/customers-menu-page.php:363
#: includes/admin/menu-pages/i-cal-menu-page.php:150
msgid "Update"
msgstr "Aktualisieren"

#: includes/admin/menu-pages/customers-menu-page.php:369
#: includes/admin/menu-pages/customers-menu-page.php:382
#: includes/admin/menu-pages/customers-menu-page.php:386
msgid "Customers"
msgstr "Kunden"

#: includes/admin/menu-pages/edit-booking-menu-page.php:80
msgid "The booking is not set."
msgstr "Die Buchung ist nicht festgelegt."

#: includes/admin/menu-pages/edit-booking-menu-page.php:88
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:49
msgid "The booking not found."
msgstr "Buchung nicht gefunden."

#: includes/admin/menu-pages/edit-booking-menu-page.php:140
msgid "Edit Booking #%d"
msgstr "Buchung #%d bearbeiten"

#: includes/admin/menu-pages/edit-booking-menu-page.php:143
#: includes/admin/menu-pages/reports-menu-page.php:201
msgid "Cancel"
msgstr "Stornieren"

#: includes/admin/menu-pages/edit-booking-menu-page.php:227
#: includes/admin/menu-pages/edit-booking-menu-page.php:234
#: includes/post-types/booking-cpt.php:245
msgid "Edit Booking"
msgstr "Buchung bearbeiten"

#: includes/admin/menu-pages/edit-booking/booking-control.php:18
#: includes/admin/menu-pages/edit-booking/checkout-control.php:35
#: includes/admin/menu-pages/edit-booking/edit-control.php:56
#: includes/admin/menu-pages/edit-booking/summary-control.php:31
msgid "You cannot edit the imported booking. Please update the source booking and resync your calendars."
msgstr "Sie können die importierte Buchung nicht bearbeiten. Bitte aktualisieren Sie die Quellbuchung und synchronisieren Sie Ihre Kalender neu."

#: includes/admin/menu-pages/edit-booking/booking-control.php:22
#: includes/ajax-api/ajax-actions/abstract-ajax-api-action.php:142
#: includes/ajax.php:184
msgid "Request does not pass security verification. Please refresh the page and try one more time."
msgstr "Die Anforderung hat die Sicherheitsüberprüfung nicht bestanden. Bitte aktualisieren Sie die Seite und versuchen Sie es noch einmal."

#: includes/admin/menu-pages/edit-booking/booking-control.php:26
#: includes/admin/menu-pages/edit-booking/checkout-control.php:40
#: includes/admin/menu-pages/edit-booking/summary-control.php:33
#: includes/utils/parse-utils.php:233
msgid "Check-in date is not set."
msgstr "Das Anreisedatum wurde nicht festgelegt."

#: includes/admin/menu-pages/edit-booking/booking-control.php:30
#: includes/admin/menu-pages/edit-booking/checkout-control.php:42
#: includes/admin/menu-pages/edit-booking/summary-control.php:35
#: includes/utils/parse-utils.php:235
msgid "Check-out date is not set."
msgstr "Das Abreisedatum wurde nicht festgelegt."

#: includes/admin/menu-pages/edit-booking/booking-control.php:72
msgid "Unable to update booking. Please try again."
msgstr "Die Buchung kann nicht geändert werden. Bitte versuchen Sie es erneut."

#: includes/admin/menu-pages/edit-booking/booking-control.php:75
msgid "Booking was edited."
msgstr "Die Buchung wurde bearbeitet."

#: includes/admin/menu-pages/edit-booking/edit-control.php:94
#: includes/script-managers/public-script-manager.php:203
#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Available"
msgstr "Verfügbar"

#: includes/admin/menu-pages/edit-booking/edit-control.php:96
#: templates/edit-booking/edit-reserved-rooms.php:71
msgid "Replace"
msgstr "Ersetzen"

#: includes/admin/menu-pages/edit-booking/summary-control.php:148
msgid "— Add new —"
msgstr "— Neu hinzufügen —"

#: includes/admin/menu-pages/extensions-menu-page.php:137
#: includes/admin/menu-pages/extensions-menu-page.php:185
#: includes/admin/menu-pages/extensions-menu-page.php:190
#: includes/admin/menu-pages/settings-menu-page.php:1192
msgid "Extensions"
msgstr "Erweiterungen"

#: includes/admin/menu-pages/extensions-menu-page.php:140
msgid "Extend the functionality of Hotel Booking plugin with the number of helpful addons for your custom purposes."
msgstr "Erweitern Sie die Funktionalität des Hotel Booking Plugins mit der Anzahl der hilfreichen Addons für Ihre individuellen Zwecke."

#: includes/admin/menu-pages/extensions-menu-page.php:170
msgid "Get this Extension"
msgstr "Erhalten Sie diese Erweiterung"

#: includes/admin/menu-pages/extensions-menu-page.php:178
msgid "No extensions found."
msgstr "Es wurde keine Erweiterungen gefunden."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:80
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:60
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:102
#: includes/i-cal/logs-handler.php:73
msgid "Abort Process"
msgstr "Prozess abbrechen"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:81
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:61
msgid "Aborting..."
msgstr "Wird abgebrochen..."

#: includes/admin/menu-pages/i-cal-import-menu-page.php:161
#: includes/admin/menu-pages/i-cal-import-menu-page.php:210
#: includes/admin/menu-pages/i-cal-import-menu-page.php:224
#: includes/admin/room-list-table.php:156
msgid "Import Calendar"
msgstr "Kalender importieren"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:166
msgid "Accommodation: %s"
msgstr "Unterkunft: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:169
msgid "Accommodation Type: %s"
msgstr "Unterkunftsart: %s"

#: includes/admin/menu-pages/i-cal-import-menu-page.php:176
msgid "Please be patient while the calendars are imported. You will be notified via this page when the process is completed."
msgstr "Bitte haben Sie etwas Geduld, während die Kalender importiert werden. Sie werden über diese Seite benachrichtigt, wenn der Prozess abgeschlossen ist."

#: includes/admin/menu-pages/i-cal-menu-page.php:67
msgid "Accommodation updated."
msgstr "Unterkunft aktualisiert."

#: includes/admin/menu-pages/i-cal-menu-page.php:73
msgid "This calendar has already been imported for another accommodation."
msgstr "Dieser Kalender wurde bereits für eine andere Unterkunft importiert."

#: includes/admin/menu-pages/i-cal-menu-page.php:103
msgid "Sync, Import and Export Calendars"
msgstr "Kalender synchronisieren, importieren und exportieren"

#. translators: %s - room name. Example: "Comfort Triple 1"
#: includes/admin/menu-pages/i-cal-menu-page.php:113
msgid "Edit External Calendars of \"%s\""
msgstr "Bearbeiten Sie externe Kalender von \"%s\""

#: includes/admin/menu-pages/i-cal-menu-page.php:122
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:101
msgid "Sync All External Calendars"
msgstr "Alle externen Kalender synchronisieren"

#: includes/admin/menu-pages/i-cal-menu-page.php:123
msgid "Sync your bookings across all online channels like Booking.com, TripAdvisor, Airbnb etc. via iCalendar file format."
msgstr "Synchronisieren Sie Ihre Buchungen über alle Online-Kanäle wie Booking.com, TripAdvisor, Airbnb usw. über das iCalendar-Dateiformat."

#: includes/admin/menu-pages/i-cal-menu-page.php:219
msgid "Calendar URL"
msgstr "Kalender-URL"

#: includes/admin/menu-pages/i-cal-menu-page.php:225
msgid "Add New Calendar"
msgstr "Neuen Kalender hinzufügen"

#: includes/admin/menu-pages/i-cal-menu-page.php:233
#: includes/admin/menu-pages/i-cal-menu-page.php:237
msgid "Sync Calendars"
msgstr "Kalender synchronisieren"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:62
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:103
#: includes/i-cal/logs-handler.php:83
msgid "Delete All Logs"
msgstr "Alle Protokolle löschen"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:63
msgid "Deleting..."
msgstr "Wird gelöscht..."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:64
msgid "%d item"
msgstr "%d Artikel"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:65
msgid "%d items"
msgstr "%d Artikel"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:91
msgid "Calendars Synchronization Status"
msgstr "Kalendersynchronisierungsstatus"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:92
msgid "Here you can see synchronization status of your external calendars."
msgstr "Hier können Sie sich den Synchronisierungsstatus Ihrer externen Kalender ansehen."

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:134
#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:148
msgid "Calendars Sync Status"
msgstr "Kalendersynchronisierungsstatus"

#: includes/admin/menu-pages/i-cal-sync-logs-menu-page.php:151
msgid "Display calendars synchronization status."
msgstr "Kalendersynchronisierungsstatus anzeigen."

#: includes/admin/menu-pages/language-menu-page.php:10
#: includes/admin/menu-pages/language-menu-page.php:37
msgid "Language Guide"
msgstr "Sprachführer"

#: includes/admin/menu-pages/language-menu-page.php:11
msgid "Default language"
msgstr "Standardsprache"

#: includes/admin/menu-pages/language-menu-page.php:13
msgid "This plugin will display all system messages, labels, buttons in the language set in <em>General > Settings > Site Language</em>. If the plugin is not available in your language, you may <a href=\"%s\">contribute your translation</a>."
msgstr "Dieses Plugin zeigt alle Systemmeldungen, Etiketten, Schaltflächen in der Sprache an, die in <em>Allgemein> Einstellungen> Standortsprache </em> eingestellt ist. Wenn das Plugin nicht in Ihrer Sprache verfügbar ist, können Sie <a href=\"%s\">Ihre Übersetzung</a> hinzufügen."

#: includes/admin/menu-pages/language-menu-page.php:14
msgid "Custom translations and edits"
msgstr "Benutzerdefinierte Übersetzungen und Bearbeitungen"

#: includes/admin/menu-pages/language-menu-page.php:15
msgid "You may customize plugin translation by editing the needed texts or adding your translation following these steps:"
msgstr "Sie können die Plugin-Übersetzung anpassen, indem Sie die benötigten Texte bearbeiten oder Ihre Übersetzung nach folgenden Schritten hinzufügen:"

#: includes/admin/menu-pages/language-menu-page.php:17
msgid "Take the source file for your translations %s or needed translated locale."
msgstr "Benutzen Sie die Quelldatei für Ihre Übersetzungen %s oder benötigte übersetzte Sprache/Locale."

#: includes/admin/menu-pages/language-menu-page.php:18
msgid "Translate texts with any translation program like Poedit, Loco, Pootle etc."
msgstr "Übersetzen Sie Texten mit jedem Übersetzungsprogramm wie Poedit, Loco, Pootle etc."

#: includes/admin/menu-pages/language-menu-page.php:19
msgid "Put created .mo file with your translations into the folder %s. Where {lang} is ISO-639 language code and {country} is ISO-3166 country code. Example: Brazilian Portuguese file would be called motopress-hotel-booking-pt_BR.mo."
msgstr "Kopieren Sie die .mo Datei mit Ihren Übersetzungen in den Ordner %s, in der {lang} ISO-639 Sprachcode und {Land} ISO-3166 Ländercode ist. Beispiel: Brasilianische portugiesische Datei würde dann motopress-hotel-buchung-pt_BR.mo heißen."

#: includes/admin/menu-pages/language-menu-page.php:22
msgid "Multilingual content"
msgstr "Mehrsprachiger Inhalt"

#: includes/admin/menu-pages/language-menu-page.php:23
msgid "If your site is multilingual, you may use additional plugins to translate your added content into multiple languages allowing the site visitors to switch them."
msgstr "Wenn Ihre Website mehrsprachig ist, können Sie zusätzliche Plugins verwenden, um Ihre hinzugefügten Inhalte in mehrere Sprachen zu übersetzen, damit die Website-Besucher zwischen ihnen wechseln können."

#: includes/admin/menu-pages/language-menu-page.php:33
msgid "Language"
msgstr "Sprache"

#: includes/admin/menu-pages/reports-menu-page.php:52
#: includes/admin/menu-pages/reports-menu-page.php:211
#: includes/admin/menu-pages/reports-menu-page.php:215
msgid "Reports"
msgstr "Berichte"

#: includes/admin/menu-pages/reports-menu-page.php:55
#: includes/admin/room-list-table.php:94
msgid "Export"
msgstr "Exportieren"

#: includes/admin/menu-pages/reports-menu-page.php:132
#: includes/bookings-calendar.php:695
msgid "All Statuses"
msgstr "Alle Status"

#: includes/admin/menu-pages/reports-menu-page.php:138
msgid "Booking dates between"
msgstr "Buchungsdaten zwischen"

#: includes/admin/menu-pages/reports-menu-page.php:139
msgid "Check-in date between"
msgstr "Check-in-Datum zwischen"

#: includes/admin/menu-pages/reports-menu-page.php:140
msgid "Check-out date between"
msgstr "Check-out-Datum zwischen"

#: includes/admin/menu-pages/reports-menu-page.php:141
msgid "In-house between"
msgstr "In-house zwischen"

#: includes/admin/menu-pages/reports-menu-page.php:142
msgid "Date of reservation between"
msgstr "Datum der Reservierung zwischen"

#: includes/admin/menu-pages/reports-menu-page.php:152
msgid "Export Bookings"
msgstr "Buchungen exportieren"

#: includes/admin/menu-pages/reports-menu-page.php:164
msgid "Choose start date"
msgstr "Wählen Anfangsdatum aus"

#: includes/admin/menu-pages/reports-menu-page.php:165
msgid "Choose end date"
msgstr "Wählen Enddatum aus"

#: includes/admin/menu-pages/reports-menu-page.php:171
msgid "Also export imported bookings"
msgstr "Auch importierte Buchungen exportieren"

#: includes/admin/menu-pages/reports-menu-page.php:175
msgid "Select columns to export"
msgstr "Wählen Spalten zum Exportieren aus"

#: includes/admin/menu-pages/reports-menu-page.php:185
msgid "Generate CSV"
msgstr "CSV erstellen"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:25
msgid "Number of accommodations"
msgstr "Anzahl der Unterkünfte"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:57
#: includes/payments/gateways/gateway.php:494
#: includes/widgets/rooms-widget.php:185
#: templates/create-booking/results/reserve-rooms.php:35
#: assets/blocks/blocks.js:436
#: assets/blocks/blocks.js:686
#: assets/blocks/blocks.js:1215
msgid "Title"
msgstr "Titel"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:61
msgid "Leave empty to use accommodation type title."
msgstr "Leer lassen, um Unterkunftsart Titel zu verwenden."

#: includes/admin/menu-pages/rooms-generator-menu-page.php:66
msgid "Generate"
msgstr "Generieren"

#: includes/admin/menu-pages/rooms-generator-menu-page.php:75
msgid "Accommodation generated."
msgid_plural "%s accommodations generated."
msgstr[0] "Unterkunft generiert."
msgstr[1] "%s Unterkünfte generiert."

#: includes/admin/menu-pages/settings-menu-page.php:113
msgid "General"
msgstr "Allgemein"

#: includes/admin/menu-pages/settings-menu-page.php:116
msgid "Pages"
msgstr "Seiten"

#: includes/admin/menu-pages/settings-menu-page.php:123
msgid "Search Results Page"
msgstr "Suchergebnisse-Seite"

#: includes/admin/menu-pages/settings-menu-page.php:124
msgid "Select page to display search results. Use search results shortcode on this page."
msgstr "Wählen Sie die Seite aus, um die Suchergebnisse anzuzeigen. Verwenden Sie die Suchergebnisse-Shortcode auf dieser Seite."

#: includes/admin/menu-pages/settings-menu-page.php:132
msgid "Checkout Page"
msgstr "Checkout-Seite"

#: includes/admin/menu-pages/settings-menu-page.php:133
msgid "Select page user will be redirected to complete booking."
msgstr "Wählen Sie die Seite Benutzer wird umgeleitet, um die Buchung zu vervollständigen."

#: includes/admin/menu-pages/settings-menu-page.php:141
msgid "Terms & Conditions"
msgstr "Allgemeine Geschäftsbedingungen"

#: includes/admin/menu-pages/settings-menu-page.php:142
msgid "If you define a \"Terms\" page the customer will be asked if they accept them when checking out."
msgstr "Wenn Sie eine AGB-Seite definieren, werden die Kunden beim Check-out gefragt, ob sie die Bedingungen akzeptieren."

#: includes/admin/menu-pages/settings-menu-page.php:150
msgid "Open the Terms & Conditions page in a new window"
msgstr "Öffne die Seite Allgemeine Geschäftsbedingungen in einem neuen Fenster"

#: includes/admin/menu-pages/settings-menu-page.php:151
msgid "By enabling this option you can avoid errors related to displaying your terms & conditions inline for website pages created in page builders."
msgstr "Wenn Sie diese Option aktivieren, können Sie Fehler im Zusammenhang mit der Anzeige Ihrer Agb eingebunden für Webseiten, die in Seitenbauern erstellt wurden, vermeiden."

#: includes/admin/menu-pages/settings-menu-page.php:159
msgid "My Account Page"
msgstr "Meine Kontoseite"

#: includes/admin/menu-pages/settings-menu-page.php:160
msgid "Select a page to display user account. Use the customer account shortcode on this page."
msgstr "Wählen Sie eine Seite aus, die im Benutzerkonto angezeigt werden soll. Verwenden Sie den Kundenkonto-Kurzcode auf dieser Seite."

#: includes/admin/menu-pages/settings-menu-page.php:170
#: includes/admin/menu-pages/settings-menu-page.php:177
#: includes/post-types/payment-cpt.php:205
msgid "Currency"
msgstr "Währung"

#: includes/admin/menu-pages/settings-menu-page.php:186
msgid "Currency Position"
msgstr "Währungsposition"

#: includes/admin/menu-pages/settings-menu-page.php:195
msgid "Decimal Separator"
msgstr "Dezimaltrennzeichen"

#: includes/admin/menu-pages/settings-menu-page.php:204
msgid "Thousand Separator"
msgstr "Tausender Trennzeichen"

#: includes/admin/menu-pages/settings-menu-page.php:214
msgid "Number of Decimals"
msgstr "Anzahl der Dezimalstellen"

#: includes/admin/menu-pages/settings-menu-page.php:226
msgid "Misc"
msgstr "Sonstiges"

#: includes/admin/menu-pages/settings-menu-page.php:233
msgid "Square Units"
msgstr "Quadrateinheiten"

#: includes/admin/menu-pages/settings-menu-page.php:242
msgid "Datepicker Date Format"
msgstr "Datumsauswahl Datumsformat"

#: includes/admin/menu-pages/settings-menu-page.php:251
#: includes/emails/templaters/email-templater.php:148
msgid "Check-out Time"
msgstr "Abreise-Zeit"

#: includes/admin/menu-pages/settings-menu-page.php:259
#: includes/emails/templaters/email-templater.php:144
msgid "Check-in Time"
msgstr "Anreise-Zeit"

#: includes/admin/menu-pages/settings-menu-page.php:267
msgid "Bed Types"
msgstr "Bettentypen"

#: includes/admin/menu-pages/settings-menu-page.php:274
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:155
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:251
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:338
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:409
#: includes/post-types/attributes-cpt.php:365
#: includes/post-types/coupon-cpt.php:79
#: includes/post-types/coupon-cpt.php:111
#: includes/post-types/coupon-cpt.php:156
msgid "Type"
msgstr "Typ"

#: includes/admin/menu-pages/settings-menu-page.php:279
msgid "Add Bed Type"
msgstr "Bettentyp hinzufügen"

#: includes/admin/menu-pages/settings-menu-page.php:286
msgid "Show Lowest Price for"
msgstr "Niedrigster Preis anzeigen für"

#: includes/admin/menu-pages/settings-menu-page.php:287
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:185
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:281
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:367
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:438
msgid "days"
msgstr "Tage"

#: includes/admin/menu-pages/settings-menu-page.php:291
msgid "Lowest price of accommodation for selected number of days if check-in and check-out dates are not set. Example: set 0 to display today's lowest price, set 7 to display the lowest price for the next week."
msgstr "Niedrigster Preis für die Anzahl der Tage, wenn Anreise- und Abreise-Daten nicht gesetzt sind. Beispiel: setzen Sie 0, um den aktuellsten Preis anzuzeigen, setzen Sie 7, um den niedrigsten Preis für die nächste Woche anzuzeigen."

#: includes/admin/menu-pages/settings-menu-page.php:298
#: includes/post-types/coupon-cpt.php:288
msgid "Coupons"
msgstr "Gutscheine"

#: includes/admin/menu-pages/settings-menu-page.php:308
msgid "Default calendar view"
msgstr "Standard Kalenderansicht"

#: includes/admin/menu-pages/settings-menu-page.php:309
msgid "Initial display format of the administrator bookings calendar."
msgstr "Ursprüngliches Format der Darstellung des Administrator-Buchungskalenders."

#: includes/admin/menu-pages/settings-menu-page.php:317
msgid "Text on Checkout"
msgstr "Text beim Check-out"

#: includes/admin/menu-pages/settings-menu-page.php:318
msgid "This text will appear on the checkout page."
msgstr "Dieser Text erscheint auf der Checkout-Seite."

#: includes/admin/menu-pages/settings-menu-page.php:329
msgid "Disable Booking"
msgstr "Buchung deaktivieren"

#: includes/admin/menu-pages/settings-menu-page.php:336
msgid "Hide reservation forms and buttons"
msgstr "Reservierungsformularen und Schaltflächen ausblenden"

#: includes/admin/menu-pages/settings-menu-page.php:345
msgid "Text instead of reservation form while booking is disabled"
msgstr "Text anstelle von Reservierungsformular bei Buchung ist deaktiviert"

#: includes/admin/menu-pages/settings-menu-page.php:356
#: includes/admin/menu-pages/shortcodes-menu-page.php:510
#: includes/wizard.php:115
#: assets/blocks/blocks.js:1562
#: assets/blocks/blocks.js:1592
msgid "Booking Confirmation"
msgstr "Buchungsbestätigung"

#: includes/admin/menu-pages/settings-menu-page.php:363
msgid "Confirmation Mode"
msgstr "Bestätigungsmodus"

#: includes/admin/menu-pages/settings-menu-page.php:365
msgid "By customer via email"
msgstr "Vom Kunden per E-Mail"

#: includes/admin/menu-pages/settings-menu-page.php:366
msgid "By admin manually"
msgstr "Von admin manuell"

#: includes/admin/menu-pages/settings-menu-page.php:367
msgid "Confirmation upon payment"
msgstr "Bestätigung bei Zahlung"

#: includes/admin/menu-pages/settings-menu-page.php:376
msgid "Booking Confirmed Page"
msgstr "Buchungsbestätigung Seite"

#: includes/admin/menu-pages/settings-menu-page.php:377
msgid "Page user will be redirected to once the booking is confirmed via email or by admin."
msgstr "Der Benutzer der Seite wird weitergeleitet, sobald die Buchung per E-Mail oder vom Administrator bestätigt wurde."

#: includes/admin/menu-pages/settings-menu-page.php:385
msgid "Approval Time for User"
msgstr "Genehmigungszeit für Benutzer"

#: includes/admin/menu-pages/settings-menu-page.php:386
msgid "Period of time in minutes the user is given to confirm booking via email. Unconfirmed bookings become Abandoned and accommodation status changes to Available."
msgstr "Zeitraum in Minuten wird dem Benutzer gegeben, um die Buchung per E-Mail zu bestätigen. Unbestätigte Buchungen werden aufgegeben und der Unterkunftsstatus ändert sich auf Verfügbar."

#: includes/admin/menu-pages/settings-menu-page.php:396
msgid "Country of residence field is required for reservation."
msgstr "Land des Wohnsitzes ist für die Reservierung erforderlich."

#: includes/admin/menu-pages/settings-menu-page.php:404
msgid "Full address fields are required for reservation."
msgstr "Für die Reservierung sind vollständige Adressfelder erforderlich."

#: includes/admin/menu-pages/settings-menu-page.php:412
msgid "Customer information is required when placing admin bookings."
msgstr "Kundeninformationen werden bei Admin-Buchung benötigt."

#: includes/admin/menu-pages/settings-menu-page.php:421
msgid "Default Country on Checkout"
msgstr "Standard-Land auf Checkout-Seite"

#: includes/admin/menu-pages/settings-menu-page.php:429
#: includes/emails/templaters/email-templater.php:199
#: includes/post-types/booking-cpt.php:194
#: includes/views/shortcodes/checkout-view.php:477
msgid "Price Breakdown"
msgstr "Preisaufteilung"

#: includes/admin/menu-pages/settings-menu-page.php:430
msgid "Price breakdown unfolded by default."
msgstr "Preisaufteilung standardmäßig angezeigt."

#: includes/admin/menu-pages/settings-menu-page.php:439
msgid "Accounts"
msgstr "Konten"

#: includes/admin/menu-pages/settings-menu-page.php:446
msgid "Account creation"
msgstr "Konto-Erstellung"

#: includes/admin/menu-pages/settings-menu-page.php:447
msgid "Automatically create an account for a user at checkout."
msgstr "Nach dem Check-out automatisch ein Konto für diesen Benutzer erstellen."

#: includes/admin/menu-pages/settings-menu-page.php:455
msgid "Allow customers to create an account during checkout."
msgstr "Kunden erlauben, während des Check-outs ein Konto zu erstellen."

#: includes/admin/menu-pages/settings-menu-page.php:463
msgid "Allow customers to log into their existing account during checkout."
msgstr "Kunden erlauben, sich während des Check-outs bei ihrem vorhandenen Konto anzumelden."

#: includes/admin/menu-pages/settings-menu-page.php:472
#: includes/upgrader.php:751
#: includes/wizard.php:164
msgid "Booking Cancellation"
msgstr "Stornierung der Buchung"

#: includes/admin/menu-pages/settings-menu-page.php:479
msgid "User can cancel booking via link provided inside email."
msgstr "Der Benutzer kann die Buchung über den Link innerhalb der E-Mail stornieren."

#: includes/admin/menu-pages/settings-menu-page.php:487
msgid "Booking Cancelation Page"
msgstr "Seite Buchungsstornierung"

#: includes/admin/menu-pages/settings-menu-page.php:488
msgid "Page to confirm booking cancelation."
msgstr "Seite für die Bestätigung der Stornierung der Buchung."

#: includes/admin/menu-pages/settings-menu-page.php:496
msgid "Booking Canceled Page"
msgstr "Seite Buchung storniert"

#: includes/admin/menu-pages/settings-menu-page.php:497
msgid "Page to redirect to after a booking is canceled."
msgstr "Zu dieser Seite werden Sie weitergeleitet, nachdem die Seite storniert wurde."

#: includes/admin/menu-pages/settings-menu-page.php:506
msgid "Search Options"
msgstr "Suchoptionen"

#: includes/admin/menu-pages/settings-menu-page.php:515
msgid "Max Adults"
msgstr "Max Erwachsene"

#: includes/admin/menu-pages/settings-menu-page.php:516
msgid "Maximum accommodation occupancy available in the Search Form."
msgstr "Maximale Belegung der Unterkunft im Suchformular."

#: includes/admin/menu-pages/settings-menu-page.php:526
msgid "Max Children"
msgstr "Max Kinder"

#: includes/admin/menu-pages/settings-menu-page.php:534
msgid "Age of Child"
msgstr "Alter des Kindes"

#: includes/admin/menu-pages/settings-menu-page.php:535
msgid "Optional description of the \"Children\" field."
msgstr "Optionale Beschreibung des Felds „Kinder“."

#: includes/admin/menu-pages/settings-menu-page.php:543
msgid "Limit search results based on the requested number of guests."
msgstr ""

#: includes/admin/menu-pages/settings-menu-page.php:551
msgid "Book button behavior on the search results page"
msgstr "Verhalten der Buchschaltfläche auf der Suchergebnisseite"

#: includes/admin/menu-pages/settings-menu-page.php:552
msgid "Redirect to the checkout page immediately after successful addition to reservation."
msgstr "Weiterleiten an die Checkout-Seite sofort nach erfolgreicher Hinzufügung zur Reservierung."

#: includes/admin/menu-pages/settings-menu-page.php:560
msgid "Recommendation"
msgstr "Empfehlung"

#: includes/admin/menu-pages/settings-menu-page.php:561
msgid "Enable search form to recommend the best set of accommodations according to a number of guests."
msgstr "Aktivieren Sie das Suchformular, um die besten Unterkünfte je nach Anzahl der Gäste zu empfehlen."

#: includes/admin/menu-pages/settings-menu-page.php:570
msgid "Skip Search Results"
msgstr "Suchergebnisse überspringen"

#: includes/admin/menu-pages/settings-menu-page.php:571
msgid "Skip search results page and enable direct booking from accommodation pages."
msgstr "Suchergebnisseite überspringen und direkte Buchung auf Unterkunftsseiten aktivieren."

#: includes/admin/menu-pages/settings-menu-page.php:578
msgid "Direct Booking Form"
msgstr "Formular Direktbuchung"

#: includes/admin/menu-pages/settings-menu-page.php:581
msgid "Show price for selected period"
msgstr "Preis für den ausgewählten Zeitraum anzeigen"

#: includes/admin/menu-pages/settings-menu-page.php:582
msgid "Show price together with adults and children fields"
msgstr "Preis mit Feldern für Erwachsene und Kinder anzeigen"

#: includes/admin/menu-pages/settings-menu-page.php:592
msgid "Enable \"adults\" and \"children\" options for my website (default)."
msgstr "Optionen „Erwachsene“ und „Kinder“ für meine Website aktivieren (Standard)."

#: includes/admin/menu-pages/settings-menu-page.php:593
msgid "Disable \"children\" option for my website (hide \"children\" field and use Guests label instead)."
msgstr "Option „Kinder“ für meine Website deaktivieren („Kinder“ Feld ausblenden und Gast Bezeichnung stattdessen verwenden)."

#: includes/admin/menu-pages/settings-menu-page.php:594
msgid "Disable \"adults\" and \"children\" options for my website."
msgstr "Optionen „Erwachsene“ und „Kinder“ für meine Website deaktivieren ."

#: includes/admin/menu-pages/settings-menu-page.php:597
msgid "Guest Management"
msgstr "Gästemanagement"

#: includes/admin/menu-pages/settings-menu-page.php:598
msgid "Applies to frontend only."
msgstr "Gilt nur für Frontend."

#: includes/admin/menu-pages/settings-menu-page.php:606
msgid "Hide \"adults\" and \"children\" fields within search availability forms."
msgstr "„Erwachsene“ und  „Kinder“ Felder für Verfügbarkeitssuche-Formulare ausblenden."

#: includes/admin/menu-pages/settings-menu-page.php:614
msgid "Remember the user's selected number of guests until the checkout page."
msgstr "Erinnern Sie sich an die ausgewählte Anzahl der Gäste bis zur Checkout-Seite."

#: includes/admin/menu-pages/settings-menu-page.php:623
msgid "Do not apply booking rules for admin bookings."
msgstr "Buchungsregeln auf Administrator-Buchungen nicht anwenden."

#: includes/admin/menu-pages/settings-menu-page.php:631
msgid "Display Options"
msgstr "Anzeigeoptionen"

#: includes/admin/menu-pages/settings-menu-page.php:638
msgid "Display gallery images of accommodation page in lightbox."
msgstr "Bildergalerie der Unterkunftsseite im Leuchtkasten anzeigen."

#: includes/admin/menu-pages/settings-menu-page.php:645
#: includes/admin/menu-pages/shortcodes-menu-page.php:61
#: assets/blocks/blocks.js:250
#: assets/blocks/blocks.js:385
msgid "Availability Calendar"
msgstr "Verfügbarkeitskalender"

#: includes/admin/menu-pages/settings-menu-page.php:646
#: includes/admin/menu-pages/shortcodes-menu-page.php:76
#: assets/blocks/blocks.js:307
msgid "Display per-night prices in the availability calendar."
msgstr "Preise pro Nacht im Kalender der Verfügbarkeit anzeigen."

#: includes/admin/menu-pages/settings-menu-page.php:654
#: includes/admin/menu-pages/shortcodes-menu-page.php:82
#: assets/blocks/blocks.js:318
msgid "Truncate per-night prices in the availability calendar."
msgstr "Preise pro Nacht im Verfügbarkeitskalender abschneiden."

#: includes/admin/menu-pages/settings-menu-page.php:662
#: includes/admin/menu-pages/shortcodes-menu-page.php:88
#: assets/blocks/blocks.js:329
msgid "Display the currency sign in the availability calendar."
msgstr "Zeigt das Währungszeichen im Verfügbarkeitskalender an."

#: includes/admin/menu-pages/settings-menu-page.php:672
msgid "Calendar Theme"
msgstr "Kalender-Thema"

#: includes/admin/menu-pages/settings-menu-page.php:673
msgid "Select theme for an availability calendar."
msgstr "Wählen Sie das Thema für einen Verfügbarkeitskalender aus."

#: includes/admin/menu-pages/settings-menu-page.php:680
msgid "Template Mode"
msgstr "Template-Modus"

#: includes/admin/menu-pages/settings-menu-page.php:682
msgid "Developer Mode"
msgstr "Entwicklermodus"

#: includes/admin/menu-pages/settings-menu-page.php:683
msgid "Theme Mode"
msgstr "Theme-Modus"

#: includes/admin/menu-pages/settings-menu-page.php:685
msgid "Choose Theme Mode to display the content with the styles of your theme. Choose Developer Mode to control appearance of the content with custom page templates, actions and filters. This option can't be changed if your theme is initially integrated with the plugin."
msgstr "Wählen Sie den Themenmodus, um den Inhalt mit den Stilen Ihres Themes anzuzeigen. Wählen Sie den Entwicklermodus, um das Aussehen des Inhalts mit benutzerdefinierten Seitenvorlagen, Aktionen und Filtern zu steuern. Diese Option kann nicht geändert werden, wenn Ihr Theme zunächst mit dem Plugin integriert ist."

#: includes/admin/menu-pages/settings-menu-page.php:698
msgid "More Styles"
msgstr "Mehr Stile"

#: includes/admin/menu-pages/settings-menu-page.php:699
msgid "Extend the styling options of Hotel Booking plugin with the new free addon - Hotel Booking Styles."
msgstr "Erweitern Sie die Stiloptionen des Hotel Booking Plugins mit der neuen kostenlosen Erweiterung Hotel Booking Styles."

#: includes/admin/menu-pages/settings-menu-page.php:711
msgid "Calendars Synchronization"
msgstr "Synchronisation von Kalendern"

#: includes/admin/menu-pages/settings-menu-page.php:718
msgid "Export admin blocks."
msgstr "Exportieren Admin-Blöcke ."

#: includes/admin/menu-pages/settings-menu-page.php:726
msgid "Do not export imported bookings."
msgstr "Exportieren importierte Buchungen nicht ."

#: includes/admin/menu-pages/settings-menu-page.php:734
msgid "Export and import bookings with buffer time included."
msgstr "Buchungen mit eingeschlossener Pufferzeit exportieren und importieren."

#: includes/admin/menu-pages/settings-menu-page.php:742
msgid "Minimize Logs"
msgstr "Protokolle minimieren"

#: includes/admin/menu-pages/settings-menu-page.php:743
msgid "Enable the plugin to record only important messages."
msgstr "Aktivieren Sie das Plugin, um nur wichtige Nachrichten aufzunehmen."

#: includes/admin/menu-pages/settings-menu-page.php:751
msgid "Calendars Synchronization Scheduler"
msgstr "Kalender Synchronisierungseinstellungen"

#: includes/admin/menu-pages/settings-menu-page.php:762
msgid "Enable automatic external calendars synchronization"
msgstr "Automatische Synchronisierung von externen Kalendern aktivieren"

#: includes/admin/menu-pages/settings-menu-page.php:771
msgid "Clock"
msgstr "Uhr"

#: includes/admin/menu-pages/settings-menu-page.php:772
msgid "Sync calendars at this time (UTC) or starting at this time every interval below."
msgstr "Kalender zu diesem Zeitpunkt (UTC) oder ab diesem Zeitpunkt bei jedem untenstehenden Intervall synchronisieren."

#: includes/admin/menu-pages/settings-menu-page.php:783
msgid "Interval"
msgstr "Intervall"

#: includes/admin/menu-pages/settings-menu-page.php:785
#: includes/crons/cron-manager.php:102
msgid "Quarter an Hour"
msgstr "Viertelstunde"

#: includes/admin/menu-pages/settings-menu-page.php:786
#: includes/crons/cron-manager.php:107
msgid "Half an Hour"
msgstr "Halbe Stunde"

#: includes/admin/menu-pages/settings-menu-page.php:787
msgid "Once Hourly"
msgstr "Einmal stündlich"

#: includes/admin/menu-pages/settings-menu-page.php:788
msgid "Twice Daily"
msgstr "Zweimal täglich"

#: includes/admin/menu-pages/settings-menu-page.php:789
msgid "Once Daily"
msgstr "Einmal täglich"

#: includes/admin/menu-pages/settings-menu-page.php:800
msgid "Automatically delete sync logs older than"
msgstr "Synch logs löschen, wenn älter als"

#: includes/admin/menu-pages/settings-menu-page.php:802
msgid "Day"
msgstr "Tag"

#: includes/admin/menu-pages/settings-menu-page.php:803
msgid "Week"
msgstr "Woche"

#: includes/admin/menu-pages/settings-menu-page.php:804
#: includes/bookings-calendar.php:575
msgid "Month"
msgstr "Monat"

#: includes/admin/menu-pages/settings-menu-page.php:805
#: includes/bookings-calendar.php:576
msgid "Quarter"
msgstr "Quartal"

#: includes/admin/menu-pages/settings-menu-page.php:806
msgid "Half a Year"
msgstr "Halbjährlich"

#: includes/admin/menu-pages/settings-menu-page.php:807
msgid "Never Delete"
msgstr "Niemals löschen"

#: includes/admin/menu-pages/settings-menu-page.php:817
msgid "Block Editor"
msgstr "Block-Editor"

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/admin/menu-pages/settings-menu-page.php:832
msgid "Enable block editor for \"%s\"."
msgstr "Block-Editor für \"%s\" aktivieren."

#: includes/admin/menu-pages/settings-menu-page.php:824
#: includes/post-types/coupon-cpt.php:59
#: includes/post-types/room-type-cpt.php:53
#: includes/post-types/room-type-cpt.php:64
#: includes/widgets/rooms-widget.php:21
msgid "Accommodation Types"
msgstr "Unterkunftsarten"

#: includes/admin/menu-pages/settings-menu-page.php:832
#: includes/csv/bookings/bookings-exporter-helper.php:97
#: includes/emails/templaters/reserved-rooms-templater.php:183
#: includes/post-types/coupon-cpt.php:136
#: includes/post-types/service-cpt.php:91
#: includes/post-types/service-cpt.php:101
#: includes/views/booking-view.php:202
msgid "Services"
msgstr "Dienstleistungen"

#: includes/admin/menu-pages/settings-menu-page.php:863
msgid "Admin Emails"
msgstr "Admin-E-Mails"

#: includes/admin/menu-pages/settings-menu-page.php:876
msgid "Customer Emails"
msgstr "Kunden-E-Mails"

#: includes/admin/menu-pages/settings-menu-page.php:881
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the <a>Hotel Booking & Mailchimp Integration</a>."
msgstr "Machen Sie aus einmaligen Gästen treue Kunden, indem Sie mit der <a>Hotel Booking & Mailchimp Integration</a> automatische Marketingkampagnen versenden."

#: includes/admin/menu-pages/settings-menu-page.php:884
msgid "Turn one-time guests into loyal customers by sending out automatic marketing campaigns with the Hotel Booking & Mailchimp Integration."
msgstr "Machen Sie aus einmaligen Gästen treue Kunden, indem Sie mit der Integration von Hotel Booking & Mailchimp automatische Marketingkampagnen versenden."

#: includes/admin/menu-pages/settings-menu-page.php:903
msgid "Cancellation Details Template"
msgstr "Stornierungsdetails-Template"

#: includes/admin/menu-pages/settings-menu-page.php:904
msgid "Used for %cancellation_details% tag."
msgstr "Verwendet für %cancellation_details% Tag."

#: includes/admin/menu-pages/settings-menu-page.php:926
msgid "Email Settings"
msgstr "E-Mail-Einstellungen"

#: includes/admin/menu-pages/settings-menu-page.php:931
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with <a>Hotel Booking Notifier</a>."
msgstr "Senden Sie mit <a>Hotel Booking Notifier</a> automatisierte E-Mail-Benachrichtigungen, z. B. Anweisungen zur Schlüsselübergabe, Hausregeln, vor und nach der Ankunft / Abreise."

#: includes/admin/menu-pages/settings-menu-page.php:934
msgid "Send automated email notifications, such as key pick-up instructions, house rules, before and after arrival/departure with Hotel Booking Notifier."
msgstr "Senden Sie mit dem Hotel Booking Notifier automatisierte E-Mail-Benachrichtigungen, wie Anweisungen zur Schlüsselübergabe, Hausregeln, vor und nach der Ankunft / Abreise."

#: includes/admin/menu-pages/settings-menu-page.php:942
msgid "Email Sender"
msgstr "E-Mail-Sender"

#: includes/admin/menu-pages/settings-menu-page.php:949
msgid "Administrator Email"
msgstr "Administrator E-Mail-Adresse"

#: includes/admin/menu-pages/settings-menu-page.php:958
msgid "From Email"
msgstr "Von E-Mail"

#: includes/admin/menu-pages/settings-menu-page.php:967
msgid "From Name"
msgstr "Von Namen"

#: includes/admin/menu-pages/settings-menu-page.php:977
msgid "Logo URL"
msgstr "Logo-URL"

#: includes/admin/menu-pages/settings-menu-page.php:987
msgid "Footer Text"
msgstr "Footer-Text"

#: includes/admin/menu-pages/settings-menu-page.php:997
msgid "Reserved Accommodation Details Template"
msgstr "Template für Details von reservierter Unterkunft"

#: includes/admin/menu-pages/settings-menu-page.php:998
msgid "Used for %reserved_rooms_details% tag."
msgstr "Verwendet für %reserved_rooms_details% Tag."

#: includes/admin/menu-pages/settings-menu-page.php:1011
msgid "Styles"
msgstr "Stile"

#: includes/admin/menu-pages/settings-menu-page.php:1018
msgid "Base Color"
msgstr "Grundfarbe"

#: includes/admin/menu-pages/settings-menu-page.php:1027
msgid "Background Color"
msgstr "Hintergrundfarbe"

#: includes/admin/menu-pages/settings-menu-page.php:1036
msgid "Body Background Color"
msgstr "Body-Hintergrundfarbe"

#: includes/admin/menu-pages/settings-menu-page.php:1045
msgid "Body Text Color"
msgstr "Body-Textfarbe"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "Payment Gateways"
msgstr "Zahlung-Gateways"

#: includes/admin/menu-pages/settings-menu-page.php:1066
msgid "General Settings"
msgstr "Allgemeine Einstellungen"

#: includes/admin/menu-pages/settings-menu-page.php:1071
msgid "Need more gateways? Use our Hotel Booking <a href=\"%s\" target=\"_blank\">WooCommerce Payments</a> extension."
msgstr "Benötigen Sie mehr Gateways? Benutzen unsere Hotel Booking <a href=\"%s\" target=\"_blank\">WooCommerce Zahlungen</a> Erweiterung."

#: includes/admin/menu-pages/settings-menu-page.php:1075
msgid "You may also email the <a href=\"%s\" target=\"_blank\">balance payment request</a> link to your guests."
msgstr "Sie können auch den <a href=\"%s\" target=\"_blank\">Restzahlungsanforderung</a> Link an Ihre Gäste senden."

#: includes/admin/menu-pages/settings-menu-page.php:1086
msgid "User Pays"
msgstr "Benutzer zahlt"

#: includes/admin/menu-pages/settings-menu-page.php:1088
msgid "Full Amount"
msgstr "Gesamtbetrag"

#: includes/admin/menu-pages/settings-menu-page.php:1089
#: includes/views/booking-view.php:463
msgid "Deposit"
msgstr "Anzahlung"

#: includes/admin/menu-pages/settings-menu-page.php:1098
msgid "Deposit Type"
msgstr "Einzahlungsart"

#: includes/admin/menu-pages/settings-menu-page.php:1100
#: includes/post-types/coupon-cpt.php:115
#: includes/post-types/coupon-cpt.php:160
msgid "Fixed"
msgstr "Fest"

#: includes/admin/menu-pages/settings-menu-page.php:1101
msgid "Percent"
msgstr "Prozent"

#: includes/admin/menu-pages/settings-menu-page.php:1110
msgid "Deposit Amount"
msgstr "Einzahlungsbetrag"

#: includes/admin/menu-pages/settings-menu-page.php:1121
msgid "Deposit Time Frame (days)"
msgstr "Zeitfenster der Einzahlung (Tage)"

#: includes/admin/menu-pages/settings-menu-page.php:1122
msgid "Apply deposit to bookings made in at least the selected number of days prior to the check-in date. Otherwise, the full amount is charged."
msgstr "Wenden Sie die Einzahlungen für die Buchungen mindestens für die ausgewählte Anzahl Tage vor dem Anreisedatum an. Andernfalls wird der volle Betrag in Rechnung gestellt."

#: includes/admin/menu-pages/settings-menu-page.php:1133
msgid "Force Secure Checkout"
msgstr "Sicherer Buchungsvorgang"

#: includes/admin/menu-pages/settings-menu-page.php:1135
msgid "Force SSL (HTTPS) on the checkout pages. You must have an SSL certificate installed to use this option."
msgstr "Obligatorische SSL (HTTPS) auf den Checkout-Seiten. Sie müssen ein SSL-Zertifikat installiert haben, um diese Option zu verwenden."

#: includes/admin/menu-pages/settings-menu-page.php:1142
msgid "Reservation Received Page"
msgstr "\"Reservierung erhalten\"-Seite"

#: includes/admin/menu-pages/settings-menu-page.php:1150
msgid "Failed Transaction Page"
msgstr "Fehlgeschlagene Transaktionsseite"

#: includes/admin/menu-pages/settings-menu-page.php:1158
msgid "Default Gateway"
msgstr "Standard-Gateway"

#: includes/admin/menu-pages/settings-menu-page.php:1172
#: includes/payments/gateways/bank-gateway.php:127
msgid "Pending Payment Time"
msgstr "Ausstehende Zahlungszeit"

#: includes/admin/menu-pages/settings-menu-page.php:1173
msgid "Period of time in minutes the user is given to complete payment. Unpaid bookings become Abandoned and accommodation status changes to Available."
msgstr "Zeitraum in Minuten, in dem der Benutzer die Zahlung bezahlt hat. Unbezahlte Buchungen werden aufgegeben und der Unterkunftsstatus ändert sich auf Verfügbar."

#: includes/admin/menu-pages/settings-menu-page.php:1195
msgid "Install <a href=\"%s\" target=\"_blank\">Hotel Booking addons</a> to manage their settings."
msgstr "Installieren <a href=\"%s\" target=\"_blank\">Hotel Booking Addons</a>, um ihre Einstellungen zu verwalten."

#: includes/admin/menu-pages/settings-menu-page.php:1197
msgid "Install Hotel Booking addons to manage their settings."
msgstr "Installieren Sie Hotel Booking Addons, um ihre Einstellungen zu verwalten."

#: includes/admin/menu-pages/settings-menu-page.php:1214
msgid "Advanced"
msgstr "Erweitert"

#: includes/admin/menu-pages/settings-menu-page.php:1226
#: includes/admin/menu-pages/settings-menu-page.php:1228
msgid "License"
msgstr "Lizenz"

#: includes/admin/menu-pages/settings-menu-page.php:1305
msgid "Settings saved."
msgstr "Einstellungen gespeichert."

#: includes/admin/menu-pages/settings-menu-page.php:1344
msgid "<strong>Note:</strong> Payment methods will appear on the checkout page only when Confirmation Upon Payment is enabled in Accommodation > Settings > General > Confirmation Mode."
msgstr "<strong>Hinweis:</strong> Zahlungsmethoden werden auf der Checkout-Seite nur dann angezeigt, wenn Bestätigung bei Zahlung  in Unterkunft > Einstellungen > Allgemein > Bestätigungsmodus aktiviert ist."

#: includes/admin/menu-pages/settings-menu-page.php:1432
#: includes/admin/menu-pages/settings-menu-page.php:1436
#: assets/blocks/blocks.js:141
#: assets/blocks/blocks.js:276
#: assets/blocks/blocks.js:429
#: assets/blocks/blocks.js:679
#: assets/blocks/blocks.js:1196
#: assets/blocks/blocks.js:1419
#: assets/blocks/blocks.js:1501
msgid "Settings"
msgstr "Einstellungen"

#: includes/admin/menu-pages/shortcodes-menu-page.php:21
#: assets/blocks/blocks.js:117
msgid "Availability Search Form"
msgstr "Verfügbarkeitsformularsuche"

#: includes/admin/menu-pages/shortcodes-menu-page.php:22
msgid "Display search form."
msgstr "Suchformular anzeigen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:25
#: assets/blocks/blocks.js:148
msgid "The number of adults presetted in the search form."
msgstr "Die Anzahl der Erwachsenen, die im Suchformular vorgegeben sind."

#: includes/admin/menu-pages/shortcodes-menu-page.php:30
#: assets/blocks/blocks.js:163
msgid "The number of children presetted in the search form."
msgstr "Die Anzahl der Kinder, die im Suchformular vorgegeben sind."

#: includes/admin/menu-pages/shortcodes-menu-page.php:35
msgid "Check-in date presetted in the search form."
msgstr "Vorgegebenes Eincheckdatum im Suchformular."

#: includes/admin/menu-pages/shortcodes-menu-page.php:36
#: includes/admin/menu-pages/shortcodes-menu-page.php:41
msgid "date in format %s"
msgstr "Datum im Format %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:40
msgid "Check-out date presetted in the search form."
msgstr "Vorgegebenes Auscheckdatum im Suchformular."

#: includes/admin/menu-pages/shortcodes-menu-page.php:45
#: assets/blocks/blocks.js:201
msgid "Custom attributes for advanced search."
msgstr "Benutzerdefinierte Attribute für die erweiterte Suche."

#: includes/admin/menu-pages/shortcodes-menu-page.php:46
#: assets/blocks/blocks.js:202
msgid "Comma-separated slugs of attributes."
msgstr "Durch Komma getrennte Slugs von Attributen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:50
#: includes/admin/menu-pages/shortcodes-menu-page.php:94
#: includes/admin/menu-pages/shortcodes-menu-page.php:180
#: includes/admin/menu-pages/shortcodes-menu-page.php:259
#: includes/admin/menu-pages/shortcodes-menu-page.php:361
#: includes/admin/menu-pages/shortcodes-menu-page.php:422
#: includes/admin/menu-pages/shortcodes-menu-page.php:450
#: includes/admin/menu-pages/shortcodes-menu-page.php:470
#: includes/admin/menu-pages/shortcodes-menu-page.php:494
#: includes/admin/menu-pages/shortcodes-menu-page.php:514
#: includes/admin/menu-pages/shortcodes-menu-page.php:530
#: includes/admin/menu-pages/shortcodes-menu-page.php:546
msgid "Custom CSS class for shortcode wrapper"
msgstr "Benutzerdefinierte CSS-Klasse für Shortcode-Wrapper"

#: includes/admin/menu-pages/shortcodes-menu-page.php:51
#: includes/admin/menu-pages/shortcodes-menu-page.php:95
#: includes/admin/menu-pages/shortcodes-menu-page.php:181
#: includes/admin/menu-pages/shortcodes-menu-page.php:260
#: includes/admin/menu-pages/shortcodes-menu-page.php:362
#: includes/admin/menu-pages/shortcodes-menu-page.php:423
#: includes/admin/menu-pages/shortcodes-menu-page.php:451
#: includes/admin/menu-pages/shortcodes-menu-page.php:471
#: includes/admin/menu-pages/shortcodes-menu-page.php:495
#: includes/admin/menu-pages/shortcodes-menu-page.php:515
#: includes/admin/menu-pages/shortcodes-menu-page.php:531
#: includes/admin/menu-pages/shortcodes-menu-page.php:547
msgid "whitespace separated css classes"
msgstr "Mit Leerzeichen getrennte CSS-Klassen"

#: includes/admin/menu-pages/shortcodes-menu-page.php:65
#: includes/admin/menu-pages/shortcodes-menu-page.php:465
#: includes/admin/menu-pages/shortcodes-menu-page.php:489
#: includes/csv/bookings/bookings-exporter-helper.php:76
#: includes/emails/templaters/reserved-rooms-templater.php:187
msgid "Accommodation Type ID"
msgstr "Unterkunftsart ID"

#: includes/admin/menu-pages/shortcodes-menu-page.php:66
#: includes/admin/menu-pages/shortcodes-menu-page.php:466
msgid "ID of Accommodation Type to check availability."
msgstr "Geben Sie ID der Unterkunftsart, um die Verfügbarkeit zu überprüfen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:67
#: includes/admin/menu-pages/shortcodes-menu-page.php:378
#: includes/admin/menu-pages/shortcodes-menu-page.php:467
#: includes/admin/menu-pages/shortcodes-menu-page.php:491
msgid "integer number"
msgstr "Integer-Nummer"

#: includes/admin/menu-pages/shortcodes-menu-page.php:70
#: assets/blocks/blocks.js:295
msgid "How many months to show."
msgstr "Wie viel Monate angezeigt werden sollen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:72
#: assets/blocks/blocks.js:296
msgid "Set the number of columns or the number of rows and columns separated by comma. Example: \"3\" or \"2,3\""
msgstr "Stellen Sie die Spaltenanzahl oder die Komma-getrennte Spalten- und Zielenanzahl ein. Zum Beispiel „3“ oder „2,3“"

#: includes/admin/menu-pages/shortcodes-menu-page.php:106
#: assets/blocks/blocks.js:251
msgid "Display availability calendar of the current accommodation type or by ID."
msgstr "Den Verfügbarkeitskalender der aktuellen Unterkunftsart oder nach ID anzeigen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:111
#: assets/blocks/blocks.js:397
#: assets/blocks/blocks.js:630
msgid "Availability Search Results"
msgstr "Verfügbarkeit Suchergebnisse"

#: includes/admin/menu-pages/shortcodes-menu-page.php:112
#: assets/blocks/blocks.js:398
msgid "Display listing of accommodation types that meet the search criteria."
msgstr "Liste der Unterkunftsarten anzeigen, die den Suchkriterien entsprechen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:115
#: includes/admin/menu-pages/shortcodes-menu-page.php:212
#: includes/admin/menu-pages/shortcodes-menu-page.php:381
#: assets/blocks/blocks.js:437
#: assets/blocks/blocks.js:687
#: assets/blocks/blocks.js:1216
msgid "Whether to display title of the accommodation type."
msgstr "Ob der Titel der Unterkunftsart angezeigt werden soll."

#: includes/admin/menu-pages/shortcodes-menu-page.php:120
#: includes/admin/menu-pages/shortcodes-menu-page.php:217
#: includes/admin/menu-pages/shortcodes-menu-page.php:386
#: assets/blocks/blocks.js:449
#: assets/blocks/blocks.js:699
#: assets/blocks/blocks.js:1228
msgid "Whether to display featured image of the accommodation type."
msgstr "Ob das Vorschaubild der Unterkunftsart angezeigt werden soll."

#: includes/admin/menu-pages/shortcodes-menu-page.php:125
#: includes/admin/menu-pages/shortcodes-menu-page.php:222
#: includes/admin/menu-pages/shortcodes-menu-page.php:391
#: assets/blocks/blocks.js:461
#: assets/blocks/blocks.js:711
#: assets/blocks/blocks.js:1240
msgid "Whether to display gallery of the accommodation type."
msgstr "Ob Galerie der Unterkunftsart angezeigt werden soll."

#: includes/admin/menu-pages/shortcodes-menu-page.php:130
#: includes/admin/menu-pages/shortcodes-menu-page.php:227
#: includes/admin/menu-pages/shortcodes-menu-page.php:396
#: assets/blocks/blocks.js:473
#: assets/blocks/blocks.js:723
#: assets/blocks/blocks.js:1252
msgid "Whether to display excerpt (short description) of the accommodation type."
msgstr "Ob Auszug (Kurzbeschreibung) der Unterkunftsart angezeigt werden soll."

#: includes/admin/menu-pages/shortcodes-menu-page.php:135
#: includes/admin/menu-pages/shortcodes-menu-page.php:232
#: includes/admin/menu-pages/shortcodes-menu-page.php:401
#: assets/blocks/blocks.js:485
#: assets/blocks/blocks.js:735
#: assets/blocks/blocks.js:1264
msgid "Whether to display details of the accommodation type."
msgstr "Ob Einzelheiten der Unterkunftsart angezeigt werden sollen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:140
#: includes/admin/menu-pages/shortcodes-menu-page.php:237
#: includes/admin/menu-pages/shortcodes-menu-page.php:406
#: includes/admin/menu-pages/shortcodes-menu-page.php:427
#: assets/blocks/blocks.js:497
#: assets/blocks/blocks.js:747
#: assets/blocks/blocks.js:1276
msgid "Whether to display price of the accommodation type."
msgstr "Ob der Preis der Unterkunftsart angezeigt werden soll."

#: includes/admin/menu-pages/shortcodes-menu-page.php:145
#: includes/admin/menu-pages/shortcodes-menu-page.php:242
#: includes/admin/menu-pages/shortcodes-menu-page.php:411
msgid "Show View Details button"
msgstr "\"Details ansehen\"-Button zeigen"

#: includes/admin/menu-pages/shortcodes-menu-page.php:146
#: includes/admin/menu-pages/shortcodes-menu-page.php:243
#: includes/admin/menu-pages/shortcodes-menu-page.php:412
#: assets/blocks/blocks.js:509
#: assets/blocks/blocks.js:759
#: assets/blocks/blocks.js:1288
msgid "Whether to display \"View Details\" button with the link to accommodation type."
msgstr "Ob \"Details ansehen\" -Taste mit dem Link zur Unterkunftsart angezeigt werden soll."

#: includes/admin/menu-pages/shortcodes-menu-page.php:151
#: includes/admin/menu-pages/shortcodes-menu-page.php:284
#: includes/admin/menu-pages/shortcodes-menu-page.php:332
msgid "Sort by."
msgstr "Sortieren nach."

#: includes/admin/menu-pages/shortcodes-menu-page.php:153
#: includes/admin/menu-pages/shortcodes-menu-page.php:173
#: includes/admin/menu-pages/shortcodes-menu-page.php:286
#: includes/admin/menu-pages/shortcodes-menu-page.php:306
#: includes/admin/menu-pages/shortcodes-menu-page.php:334
#: includes/admin/menu-pages/shortcodes-menu-page.php:354
msgid "%1$s. See the <a href=\"%2$s\" target=\"_blank\">full list</a>."
msgstr "%1$s. Sehen Sie <a href=\"%2$s\" target=\"_blank\">die vollständige Liste</a>."

#: includes/admin/menu-pages/shortcodes-menu-page.php:160
#: includes/admin/menu-pages/shortcodes-menu-page.php:293
#: includes/admin/menu-pages/shortcodes-menu-page.php:341
msgid "Designates the ascending or descending order of sorting."
msgstr "Bezeichnet die aufsteigende oder absteigende Sortierreihenfolge."

#: includes/admin/menu-pages/shortcodes-menu-page.php:162
#: includes/admin/menu-pages/shortcodes-menu-page.php:295
#: includes/admin/menu-pages/shortcodes-menu-page.php:343
msgid "ASC - from lowest to highest values (1, 2, 3). DESC - from highest to lowest values (3, 2, 1)."
msgstr "ASC - vom niedrigsten zum höchsten Wert (1, 2, 3). DESC -  vom höchsten zum niedrigsten Wert (3, 2, 1)."

#: includes/admin/menu-pages/shortcodes-menu-page.php:166
#: includes/admin/menu-pages/shortcodes-menu-page.php:299
#: includes/admin/menu-pages/shortcodes-menu-page.php:347
#: assets/blocks/blocks.js:574
#: assets/blocks/blocks.js:910
#: assets/blocks/blocks.js:1094
msgid "Custom field name. Required if \"orderby\" is one of the \"meta_value\", \"meta_value_num\" or \"meta_value_*\"."
msgstr "Benutzerdefinierter Feldname. Erforderlich, wenn \"orderby\" ist einer der \"meta_value\", \"meta_value_num\" oder \"meta_value_*\"."

#: includes/admin/menu-pages/shortcodes-menu-page.php:167
#: includes/admin/menu-pages/shortcodes-menu-page.php:300
#: includes/admin/menu-pages/shortcodes-menu-page.php:348
msgid "custom field name"
msgstr "benutzerdefinierter Feldname"

#: includes/admin/menu-pages/shortcodes-menu-page.php:168
#: includes/admin/menu-pages/shortcodes-menu-page.php:177
#: includes/admin/menu-pages/shortcodes-menu-page.php:301
#: includes/admin/menu-pages/shortcodes-menu-page.php:310
#: includes/admin/menu-pages/shortcodes-menu-page.php:349
#: includes/admin/menu-pages/shortcodes-menu-page.php:358
#: includes/admin/menu-pages/shortcodes-menu-page.php:645
msgid "empty string"
msgstr "Leere Reihe"

#: includes/admin/menu-pages/shortcodes-menu-page.php:171
#: includes/admin/menu-pages/shortcodes-menu-page.php:304
#: includes/admin/menu-pages/shortcodes-menu-page.php:352
msgid "Specified type of the custom field. Can be used in conjunction with orderby=\"meta_value\"."
msgstr "Angegebener Typ des benutzerdefinierten Felds. Kann in Verbindung mit orderby=\"meta_value\" verwendet werden."

#: includes/admin/menu-pages/shortcodes-menu-page.php:185
msgid "Sort by. Use \"orderby\" insted."
msgstr "Sortieren nach. Verwenden Sie \"orderby\" stattdessen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:191
#: includes/admin/menu-pages/shortcodes-menu-page.php:248
msgid "Show Book button"
msgstr "Buchungsbutton anzeigen"

#: includes/admin/menu-pages/shortcodes-menu-page.php:192
#: includes/admin/menu-pages/shortcodes-menu-page.php:249
#: includes/admin/menu-pages/shortcodes-menu-page.php:417
#: assets/blocks/blocks.js:771
#: assets/blocks/blocks.js:1300
msgid "Whether to display Book button."
msgstr "Ob die Buchungsbutton angezeigt werden soll."

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "NOTE:"
msgstr "HINWEIS:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:204
msgid "Use only on page that you set as Search Results Page in <a href=\"%s\">Settings</a>"
msgstr "Nur auf der Seite verwenden, die Sie als Suchergebnisse-Seite in <a href=\"%s\"> Einstellungen </a> festgelegt haben"

#: includes/admin/menu-pages/shortcodes-menu-page.php:209
#: assets/blocks/blocks.js:642
msgid "Accommodation Types Listing"
msgstr "Unterkunftsarten-Liste"

#: includes/admin/menu-pages/shortcodes-menu-page.php:254
#: includes/admin/menu-pages/shortcodes-menu-page.php:327
#: assets/blocks/blocks.js:804
#: assets/blocks/blocks.js:1028
msgid "Count per page"
msgstr "Anzahl pro Seite"

#: includes/admin/menu-pages/shortcodes-menu-page.php:255
#: assets/blocks/blocks.js:805
msgid "integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "Ganzzahl; -1 um alle anzuzeigen; Standard: 'Blogseiten zeigen maximal' (Einstellungen\\Lesen)"

#: includes/admin/menu-pages/shortcodes-menu-page.php:264
#: assets/blocks/blocks.js:817
msgid "IDs of categories that will be shown."
msgstr "IDs von Kategorien, die angezeigt werden."

#: includes/admin/menu-pages/shortcodes-menu-page.php:265
#: includes/admin/menu-pages/shortcodes-menu-page.php:270
#: includes/admin/menu-pages/shortcodes-menu-page.php:275
#: includes/admin/menu-pages/shortcodes-menu-page.php:323
#: assets/blocks/blocks.js:1017
msgid "Comma-separated IDs."
msgstr "Mit Komma getrennte IDs."

#: includes/admin/menu-pages/shortcodes-menu-page.php:269
#: assets/blocks/blocks.js:829
msgid "IDs of tags that will be shown."
msgstr "IDs von Tags, die angezeigt werden."

#: includes/admin/menu-pages/shortcodes-menu-page.php:274
#: assets/blocks/blocks.js:793
msgid "IDs of accommodations that will be shown."
msgstr "IDs von Unterkünften, die angezeigt werden."

#: includes/admin/menu-pages/shortcodes-menu-page.php:279
#: assets/blocks/blocks.js:841
msgid "Logical relationship between each taxonomy when there is more than one."
msgstr "Logischer Zusammenhang zwischen jeder Taxonomie, wenn es mehrere Taxonomien gibt."

#: includes/admin/menu-pages/shortcodes-menu-page.php:319
#: assets/blocks/blocks.js:983
msgid "Services Listing"
msgstr "Auflistung der Dienstleistungen"

#: includes/admin/menu-pages/shortcodes-menu-page.php:322
#: assets/blocks/blocks.js:792
msgid "IDs"
msgstr "ID´s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:324
#: assets/blocks/blocks.js:1016
msgid "IDs of services that will be shown. "
msgstr "IDs von Dienstleistungen, die angezeigt werden. "

#: includes/admin/menu-pages/shortcodes-menu-page.php:368
msgid "Show All Services"
msgstr "Alle Dienstleistungen anzeigen"

#: includes/admin/menu-pages/shortcodes-menu-page.php:373
#: assets/blocks/blocks.js:1167
#: assets/blocks/blocks.js:1344
msgid "Single Accommodation Type"
msgstr "Eine Unterkunftsart"

#: includes/admin/menu-pages/shortcodes-menu-page.php:377
msgid "ID of accommodation type to display."
msgstr "ID der Unterkunftsart anzuzeigen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:441
msgid "Display accommodation type with title and image."
msgstr "Unterkunftsart mit Titel und Bild anzeigen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:446
#: assets/blocks/blocks.js:1356
#: assets/blocks/blocks.js:1386
msgid "Checkout Form"
msgstr "Checkout-Formular"

#: includes/admin/menu-pages/shortcodes-menu-page.php:447
#: assets/blocks/blocks.js:1357
msgid "Display checkout form."
msgstr "Checkout-Formular anzeigen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:457
msgid "Use only on page that you set as Checkout Page in <a href=\"%s\">Settings</a>"
msgstr "Nur auf der Seite verwenden, die Sie als Checkout-Seite in <a href=\"%s\"> Einstellungen </a> festgelegt haben"

#: includes/admin/menu-pages/shortcodes-menu-page.php:462
#: assets/blocks/blocks.js:1398
#: assets/blocks/blocks.js:1468
msgid "Booking Form"
msgstr "Buchungsformular"

#: includes/admin/menu-pages/shortcodes-menu-page.php:481
msgid "Show Booking Form for Accommodation Type with id 777"
msgstr "Buchungsformular für Unterkunftsart mit der ID 777 anzeigen"

#: includes/admin/menu-pages/shortcodes-menu-page.php:486
#: assets/blocks/blocks.js:1480
#: assets/blocks/blocks.js:1550
msgid "Accommodation Rates List"
msgstr "Unterkunft-Ratenliste"

#: includes/admin/menu-pages/shortcodes-menu-page.php:490
#: assets/blocks/blocks.js:1204
msgid "ID of accommodation type."
msgstr "ID der Unterkunftsart."

#: includes/admin/menu-pages/shortcodes-menu-page.php:505
msgid "Show Accommodation Rates List for accommodation type with id 777"
msgstr "Unterkunft-Ratenliste für Unterkunftsart mit der ID 777 anzeigen"

#: includes/admin/menu-pages/shortcodes-menu-page.php:511
#: assets/blocks/blocks.js:1563
msgid "Display booking and payment details."
msgstr "Buchungs- und Zahlungsdetails anzeigen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:521
msgid "Use this shortcode on Booking Confirmed and Reservation Received pages"
msgstr "Nutzten Sie diesen Shortcode auf der \"Buchung bestätigt\"- oder \"Reservierung erhalten\"-Seiten"

#: includes/admin/menu-pages/shortcodes-menu-page.php:526
msgid "Booking Cancelation"
msgstr "Stornierung der Buchung"

#: includes/admin/menu-pages/shortcodes-menu-page.php:527
msgid "Display booking cancelation details."
msgstr "Detail der Stornierung der Buchung anzeigen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:537
msgid "Use this shortcode on the Booking Cancelation page"
msgstr "Diesen Kurzcode auf der Seite der Stornierung der Bestellung verwenden"

#: includes/admin/menu-pages/shortcodes-menu-page.php:542
msgid "Customer Account"
msgstr "Kundenkonto"

#: includes/admin/menu-pages/shortcodes-menu-page.php:543
msgid "Display log in form or customer account area."
msgstr "Anmeldeformular oder Kundenkontobereich anzeigen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:553
msgid "Use this shortcode to create the My Account page."
msgstr "Verwenden Sie diesen Kurzcode, um die Seite Mein Konto zu erstellen."

#: includes/admin/menu-pages/shortcodes-menu-page.php:565
#: includes/admin/menu-pages/shortcodes-menu-page.php:699
#: includes/admin/menu-pages/shortcodes-menu-page.php:703
msgid "Shortcodes"
msgstr "Kurzcodes"

#: includes/admin/menu-pages/shortcodes-menu-page.php:569
msgid "Shortcode"
msgstr "Tastenkombination"

#: includes/admin/menu-pages/shortcodes-menu-page.php:570
#: includes/post-types/attributes-cpt.php:307
msgid "Parameters"
msgstr "Parameter"

#: includes/admin/menu-pages/shortcodes-menu-page.php:571
msgid "Example"
msgstr "Beispiel"

#: includes/admin/menu-pages/shortcodes-menu-page.php:603
#: includes/admin/menu-pages/shortcodes-menu-page.php:625
msgid "Deprecated since %s"
msgstr "Veraltet seit %s"

#: includes/admin/menu-pages/shortcodes-menu-page.php:635
msgid "Values:"
msgstr "Werte:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:640
msgid "Default:"
msgstr "Standard:"

#: includes/admin/menu-pages/shortcodes-menu-page.php:687
msgid "Optional."
msgstr "Optional."

#: includes/admin/menu-pages/shortcodes-menu-page.php:695
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:30
#: includes/payments/gateways/gateway.php:365
#: includes/views/shortcodes/checkout-view.php:247
#: includes/views/shortcodes/checkout-view.php:270
#: includes/views/shortcodes/checkout-view.php:538
#: includes/views/shortcodes/checkout-view.php:572
#: templates/account/account-details.php:34
msgid "Required"
msgstr "Erforderlich"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:34
msgid "Taxes and fees saved."
msgstr "Steuern und Gebühren gespeichert."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:41
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:459
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:463
msgid "Taxes & Fees"
msgstr "Steuern und Gebühren"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:136
#: includes/csv/bookings/bookings-exporter-helper.php:102
#: includes/views/booking-view.php:296
msgid "Fees"
msgstr "Gebühren"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:137
msgid "No fees have been created yet."
msgstr "Keine Gebühren wurden noch erstellt."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:138
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:234
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:321
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:392
#: includes/post-types/booking-cpt.php:219
msgid "Add new"
msgstr "Neu hinzufügen"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:146
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:242
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:329
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:400
msgid "Label"
msgstr "Bezeichnung"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:147
msgid "New fee"
msgstr "Neue Gebühr"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:158
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:254
msgid "Per guest / per day"
msgstr "Pro Gast / pro Tag"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:159
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:255
msgid "Per accommodation / per day"
msgstr "Pro Unterkunft / pro Tag"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:160
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:256
msgid "Per accommodation (%)"
msgstr "Pro Unterkunft (%)"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:182
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:278
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:364
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:435
msgid "Limit"
msgstr "Grenzwert"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:183
msgid "How often this fee is charged. Set 0 to charge each day of the stay period. Set 1 to charge once."
msgstr "Häufigkeit der Berechnung dieser Gebühr. Legen Sie 0 fest, um sie für jeden Tag des Aufenthaltszeitraums zu berechnen. Bei einmaliger Berechnung 1 festlegen."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:197
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:200
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:293
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:296
msgid "Include"
msgstr "Einschließen"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:198
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:294
msgid "Show accommodation rate with this charge included"
msgstr "Übernachtungstarif einschließlich dieser Gebühr anzeigen"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:232
#: includes/csv/bookings/bookings-exporter-helper.php:95
#: includes/views/booking-view.php:171
msgid "Accommodation Taxes"
msgstr "Unterkunftssteuern"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:233
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:320
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:391
msgid "No taxes have been created yet."
msgstr "Keine Steuern wurden noch erstellt."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:243
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:330
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:401
msgid "New tax"
msgstr "Neue Steuer"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:279
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:365
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:436
msgid "Limit of days the fee is charged. Set 0 to charge each day of stay period. Set 1 to charge once."
msgstr "Die Tagesgrenze der Gebühr wird begrenzt. Setzen Sie 0 um jeden Tag des Aufenthalts zu berechnen. Setzen Sie 1 um einmalig zu berechnen."

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:319
#: includes/views/booking-view.php:265
msgid "Service Taxes"
msgstr "Dienstleistungssteuern"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:341
#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:412
#: includes/post-types/coupon-cpt.php:114
#: includes/post-types/coupon-cpt.php:159
msgid "Percentage"
msgstr "Prozentsatz"

#: includes/admin/menu-pages/taxes-and-fees-menu-page.php:390
#: includes/views/booking-view.php:349
msgid "Fee Taxes"
msgstr "Taxen und Gebühren"

#: includes/admin/room-list-table.php:95
msgid "External Calendars"
msgstr "Externe Kalender"

#: includes/admin/room-list-table.php:157
#: includes/admin/room-list-table.php:211
msgid "Sync External Calendars"
msgstr "Externe Kalender synchronisieren"

#: includes/admin/room-list-table.php:163
#: includes/admin/sync-rooms-list-table.php:65
msgctxt "Placeholder for empty accommodation title"
msgid "(no title)"
msgstr "(kein Titel)"

#: includes/admin/room-list-table.php:185
msgid "Download Calendar"
msgstr "Kalender herunterladen"

#: includes/admin/sync-logs-list-table.php:73
msgid "Message"
msgstr "Nachricht"

#: includes/admin/sync-logs-list-table.php:82
msgid "Success"
msgstr "Erfolgreich"

#: includes/admin/sync-logs-list-table.php:85
msgid "Info"
msgstr "Information"

#: includes/admin/sync-logs-list-table.php:88
msgid "Warning"
msgstr "Warnung"

#: includes/admin/sync-rooms-list-table.php:71
msgctxt "This is date and time format 31/12/2017 - 23:59:59"
msgid "d/m/Y - H:i:s"
msgstr "d/m/Y - H:i:s"

#: includes/admin/sync-rooms-list-table.php:75
#: includes/ajax.php:945
msgid "Waiting"
msgstr "Warten"

#: includes/admin/sync-rooms-list-table.php:78
#: includes/ajax.php:948
msgid "Processing"
msgstr "In Bearbeitung"

#: includes/admin/sync-rooms-list-table.php:128
msgctxt "Total number of processed bookings"
msgid "Total"
msgstr "Gesamt"

#: includes/admin/sync-rooms-list-table.php:129
msgid "Succeed"
msgstr "Erfolgreich"

#: includes/admin/sync-rooms-list-table.php:130
msgid "Skipped"
msgstr "Übersprungen"

#: includes/admin/sync-rooms-list-table.php:131
msgid "Failed"
msgstr "Fehlgeschlagen"

#: includes/admin/sync-rooms-list-table.php:132
msgid "Removed"
msgstr "Gelöscht"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:58
msgid "Copying to clipboard failed. Please press Ctrl/Cmd+C to copy."
msgstr "Fehler beim Kopieren in die Zwischenablage. Zum Kopieren Strg/Cmd+C drücken."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:80
msgid "Description is missing."
msgstr "Beschreibung fehlt."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:83
msgid "User is missing."
msgstr "Benutzer fehlt."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:86
msgid "Permission is missing."
msgstr "Berechtigung fehlt."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:102
msgid "You do not have permission to assign API Keys to the selected user."
msgstr "Sie haben keine Berechtigung, dem ausgewählten Benutzer API-Schlüssel zuzuweisen."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:132
msgid "API Key updated successfully."
msgstr "API-Schlüssel erfolgreich aktualisiert."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:164
msgid "API Key generated successfully. Make sure to copy your new keys now as the secret key will be hidden once you leave this page."
msgstr "Der API-Schlüssel wurde erfolgreich generiert. Kopieren Sie Ihre neuen Schlüssel jetzt, denn der geheime Schlüssel wird nach Verlassen dieser Seite ausgeblendet."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-ajax.php:176
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:116
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:119
msgid "Revoke key"
msgstr "Schlüssel widerrufen"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:46
msgid "No keys found."
msgstr "Keine Schlüssel gefunden."

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:57
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:22
#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:95
#: includes/payments/gateways/gateway.php:504
#: includes/post-types/coupon-cpt.php:37
#: includes/post-types/rate-cpt.php:81
msgid "Description"
msgstr "Beschreibung"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:58
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:81
msgid "Consumer key ending in"
msgstr "Verbraucherschlüssel läuft ab in"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:59
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:36
msgid "User"
msgstr "Benutzer"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:60
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:55
msgid "Permissions"
msgstr "Berechtigungen"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:61
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:89
msgid "Last access"
msgstr "Letzter Zugriff"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:99
msgid "API key"
msgstr "API-Schlüssel"

#. translators: %d: API key ID.
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:111
msgid "ID: %d"
msgstr "ID: %d"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:126
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:227
msgid "Revoke"
msgstr "Widerrufen"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:182
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:65
msgid "Read"
msgstr "Lesen"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:183
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:66
msgid "Write"
msgstr "Schreiben"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:184
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:67
msgid "Read/Write"
msgstr "Lesen/Schreiben"

#. translators: 1: last access date 2: last access time
#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:205
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:96
msgid "%1$s at %2$s"
msgstr "%1$s um %2$s"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys-table-list.php:213
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:100
msgid "Unknown"
msgstr "Unbekannt"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:68
msgid "You do not have permission to edit this API Key"
msgstr "Sie sind nicht berechtigt, diesen API-Schlüssel zu bearbeiten"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:89
msgid "REST API"
msgstr "REST API"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:91
msgid "Add key"
msgstr "Schlüssel hinzufügen"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:176
msgid "You do not have permission to revoke this API Key"
msgstr "Sie sind nicht berechtigt, diesen API-Schlüssel zu widerrufen"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:191
msgid "You do not have permission to edit API Keys"
msgstr "Sie sind nicht berechtigt, die API-Schlüssel zu bearbeiten"

#: includes/advanced/admin/tab/subtabs/api-keys/api-keys.php:211
msgid "You do not have permission to revoke API Keys"
msgstr "Sie sind nicht berechtigt, die API-Schlüssel zu widerrufen"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:13
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:114
msgid "Generate API key"
msgstr "API-Schlüssel generieren"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:25
msgid "Friendly name for identifying this key."
msgstr "Ein eingängiger Name, um diesen Schlüssel zu identifizieren."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:39
msgid "Owner of these keys."
msgstr "Besitzer dieser Schlüssel."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:58
msgid "Access type of these keys."
msgstr "Zugriffstyp auf diese Schlüssel."

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:148
msgid "Consumer key"
msgstr "Verbraucherschlüssel"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copied!"
msgstr "Kopiert!"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:151
#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:159
msgid "Copy"
msgstr "Kopieren"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:156
msgid "Consumer secret"
msgstr "Verbrauchergeheimnis"

#: includes/advanced/admin/tab/subtabs/api-keys/html-keys-edit.php:164
msgid "QR Code"
msgstr "QR-Code"

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:33
#: includes/ajax-api/ajax-actions/update-booking-notes.php:52
#: includes/ajax.php:344
#: includes/ajax.php:388
#: includes/csv/bookings/bookings-query.php:85
msgid "Please complete all required fields and try again."
msgstr "Bitte füllen Sie alle Pflichtfelder aus und versuchen Sie es erneut."

#: includes/ajax-api/ajax-actions/create-stripe-payment-intent.php:55
msgid "Sorry, the minimum allowed payment amount is %s to use this payment method."
msgstr "Entschuldigung, der minimal zulässige Zahlungsbetrag ist %s, um diese Zahlungsmethode zu verwenden."

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:60
#: includes/post-types/booking-cpt.php:35
msgid "Booking Information"
msgstr "Buchungsinformationen"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:68
#: includes/emails/templaters/email-templater.php:136
#: includes/post-types/booking-cpt.php:51
#: template-functions.php:706
#: template-functions.php:710
#: templates/create-booking/search/search-form.php:53
#: templates/edit-booking/edit-dates.php:33
#: templates/shortcodes/search/search-form.php:43
#: templates/widgets/search-availability/search-form.php:43
#: assets/blocks/blocks.js:177
msgid "Check-in Date"
msgstr "Anreisedatum"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:72
#: includes/emails/templaters/email-templater.php:140
#: includes/post-types/booking-cpt.php:60
#: template-functions.php:715
#: template-functions.php:719
#: templates/create-booking/search/search-form.php:73
#: templates/edit-booking/edit-dates.php:42
#: templates/shortcodes/search/search-form.php:63
#: templates/widgets/search-availability/search-form.php:62
#: assets/blocks/blocks.js:189
msgid "Check-out Date"
msgstr "Abreisedatum"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:91
msgid "Summary"
msgstr "Zusammenfassung"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:99
msgid "Source"
msgstr "Quelle"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:110
#: includes/post-types/booking-cpt.php:83
#: templates/emails/customer-approved-booking.php:31
#: templates/emails/customer-cancelled-booking.php:29
#: templates/emails/customer-confirmation-booking.php:35
#: templates/emails/customer-pending-booking.php:32
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:30
msgid "Customer Information"
msgstr "Kundeninformationen"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:150
#: includes/csv/bookings/bookings-exporter-helper.php:90
#: includes/emails/templaters/email-templater.php:189
#: includes/post-types/booking-cpt.php:164
msgid "Customer Note"
msgstr "Notizen des Kunden"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:156
#: includes/post-types/booking-cpt.php:171
msgid "Additional Information"
msgstr "Zusätzliche Informationen"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:160
#: includes/csv/bookings/bookings-exporter-helper.php:107
#: includes/post-types/booking-cpt.php:178
#: includes/post-types/coupon-cpt.php:289
msgid "Coupon"
msgstr "Gutschein"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:164
#: includes/post-types/booking-cpt.php:187
msgid "Total Booking Price"
msgstr "Gesamtpreis der Buchung"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:173
#: includes/bundles/customer-bundle.php:173
#: includes/post-types/booking-cpt.php:201
#: includes/views/shortcodes/checkout-view.php:735
msgid "Notes"
msgstr "Notizen"

#: includes/ajax-api/ajax-actions/get-admin-calendar-booking-info.php:192
msgid "%1$s on %2$s"
msgstr "%1$s am %2$s"

#: includes/ajax-api/ajax-actions/get-room-type-availability-data.php:185
#: template-functions.php:88
msgid "Based on your search parameters"
msgstr "Basierend auf Ihren Suchparametern"

#: includes/ajax.php:245
msgid "No bookings found for your request."
msgstr "Keine Buchungen wurden für Ihre Anfrage gefunden."

#: includes/ajax.php:252
msgid "Uploads directory is not writable."
msgstr "Das Upload-Verzeichnis ist nicht beschreibbar."

#: includes/ajax.php:314
msgid "No enough data"
msgstr "Zu wenig Daten"

#: includes/ajax.php:332
#: includes/script-managers/admin-script-manager.php:94
msgid "An error has occurred"
msgstr "Ein Fehler ist aufgetreten"

#: includes/ajax.php:372
#: includes/script-managers/public-script-manager.php:199
msgid "An error has occurred, please try again later."
msgstr "Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut."

#: includes/ajax.php:473
msgid "The number of adults is not valid."
msgstr "Die Anzahl der Erwachsenen ist nicht gültig."

#: includes/ajax.php:477
msgid "The number of guests is not valid."
msgstr "Die Anzahl der Gäste ist nicht gültig."

#: includes/ajax.php:519
#: includes/ajax.php:593
msgid "An error has occurred. Please try again later."
msgstr "Ein Fehler ist aufgetreten. Bitte versuchen Sie es später noch einmal."

#: includes/ajax.php:750
msgid "Chosen payment method is not available. Please refresh the page and try one more time."
msgstr "Ausgewählte Zahlungsmethode ist nicht verfügbar. Bitte aktualisieren Sie die Seite und versuchen Sie es noch einmal."

#: includes/ajax.php:832
msgid "Coupon applied successfully."
msgstr "Der Gutschein wurde erfolgreich angewendet."

#: includes/ajax.php:838
#: includes/entities/coupon.php:366
msgid "Coupon is not valid."
msgstr "Der Gutschein ist nicht gültig."

#: includes/ajax.php:1046
msgid "You do not have permission to do this action."
msgstr "Sie sind nicht berechtigt, diese Aktion auszuführen."

#: includes/attribute-functions.php:164
#: includes/post-types/attributes-cpt.php:137
#: includes/post-types/attributes-cpt.php:149
#: includes/post-types/attributes-cpt.php:345
msgctxt "Not selected value in the search form."
msgid "&mdash;"
msgstr "&mdash;"

#: includes/bookings-calendar.php:577
msgid "Year"
msgstr "Jahr"

#: includes/bookings-calendar.php:578
#: includes/post-types/attributes-cpt.php:298
#: includes/reports/report-filters.php:94
msgid "Custom"
msgstr "Benutzerdefiniert"

#: includes/bookings-calendar.php:608
#: includes/post-types/booking-cpt/statuses.php:102
#: includes/reports/data/report-earnings-by-dates-data.php:28
msgctxt "Booking status"
msgid "Confirmed"
msgstr "Bestätigt"

#: includes/bookings-calendar.php:645
#: includes/reports/abstract-report.php:46
#: includes/reports/earnings-report.php:361
msgid "Show"
msgstr "Vorschau"

#: includes/bookings-calendar.php:649
#: templates/create-booking/search/search-form.php:131
#: templates/shortcodes/search/search-form.php:138
#: templates/widgets/search-availability/search-form.php:142
msgid "Search"
msgstr "Suche"

#: includes/bookings-calendar.php:652
#: includes/bookings-calendar.php:654
#: includes/bookings-calendar.php:697
#: includes/script-managers/public-script-manager.php:200
msgid "Booked"
msgstr "Gebucht"

#: includes/bookings-calendar.php:657
#: includes/bookings-calendar.php:659
#: includes/bookings-calendar.php:698
#: includes/script-managers/public-script-manager.php:202
msgid "Pending"
msgstr "Ausstehend"

#: includes/bookings-calendar.php:662
#: includes/bookings-calendar.php:664
msgid "External"
msgstr "Extern"

#: includes/bookings-calendar.php:667
#: includes/bookings-calendar.php:669
#: includes/bookings-calendar.php:1133
msgid "Blocked"
msgstr "Blockiert"

#: includes/bookings-calendar.php:687
msgid "Search results for accommodations that have bookings with status \"%s\" from %s until %s"
msgstr "Suchergebnisse für Unterkünfte, die Buchungen mit Status \"%s\" von %s bis %s haben"

#: includes/bookings-calendar.php:696
msgid "Free"
msgstr "Kostenlos"

#: includes/bookings-calendar.php:699
msgid "Locked (Booked or Pending)"
msgstr "Gesperrt (Gebucht oder Ausstehend)"

#: includes/bookings-calendar.php:729
#: includes/bookings-calendar.php:819
msgid "Until"
msgstr "Bis"

#: includes/bookings-calendar.php:775
msgid "Period:"
msgstr "Zeitraum:"

#: includes/bookings-calendar.php:782
msgid "&lt; Prev"
msgstr "&lt; Zurück"

#: includes/bookings-calendar.php:799
msgid "Next &gt;"
msgstr "Weiter &gt;"

#: includes/bookings-calendar.php:874
msgid "No accommodations found."
msgstr "Keine Unterkünfte gefunden."

#: includes/bookings-calendar.php:1123
msgid "Check-out #%d"
msgstr "Abreise #%d"

#: includes/bookings-calendar.php:1127
msgid "Check-in #%d"
msgstr "Anreise #%d"

#: includes/bookings-calendar.php:1131
msgid "Booking #%d"
msgstr "Buchung #%d"

#: includes/bookings-calendar.php:1136
#: includes/bookings-calendar.php:1140
#: includes/script-managers/public-script-manager.php:201
msgid "Buffer time."
msgstr "Pufferzeit."

#: includes/bookings-calendar.php:1143
msgctxt "Availability"
msgid "Free"
msgstr "Frei"

#: includes/bookings-calendar.php:1172
#: templates/emails/reserved-room-details.php:15
msgid "Adults: %s"
msgstr "Erwachsene:  %s"

#: includes/bookings-calendar.php:1176
#: templates/emails/reserved-room-details.php:17
msgid "Children: %s"
msgstr "Kinder: %s"

#: includes/bookings-calendar.php:1183
msgid "Booking imported with UID %s."
msgstr "Die Buchung wurde mit UID %s importiert."

#: includes/bookings-calendar.php:1185
msgid "Imported booking."
msgstr "Importierte Buchung."

#: includes/bookings-calendar.php:1193
msgid "Description: %s."
msgstr "Beschreibung: %s."

#: includes/bookings-calendar.php:1197
msgid "Source: %s."
msgstr "Quelle: %s."

#: includes/bundles/countries-bundle.php:16
msgid "Afghanistan"
msgstr "Afghanistan"

#: includes/bundles/countries-bundle.php:17
msgid "&#197;land Islands"
msgstr "Åland"

#: includes/bundles/countries-bundle.php:18
msgid "Albania"
msgstr "Albanien"

#: includes/bundles/countries-bundle.php:19
msgid "Algeria"
msgstr "Algerien"

#: includes/bundles/countries-bundle.php:20
msgid "American Samoa"
msgstr "Amerikanisch-Samoa"

#: includes/bundles/countries-bundle.php:21
msgid "Andorra"
msgstr "Andorra"

#: includes/bundles/countries-bundle.php:22
msgid "Angola"
msgstr "Angola"

#: includes/bundles/countries-bundle.php:23
msgid "Anguilla"
msgstr "Anguilla"

#: includes/bundles/countries-bundle.php:24
msgid "Antarctica"
msgstr "Antarktika"

#: includes/bundles/countries-bundle.php:25
msgid "Antigua and Barbuda"
msgstr "Antigua und Barbuda"

#: includes/bundles/countries-bundle.php:26
msgid "Argentina"
msgstr "Argentinien"

#: includes/bundles/countries-bundle.php:27
msgid "Armenia"
msgstr "Armenien"

#: includes/bundles/countries-bundle.php:28
msgid "Aruba"
msgstr "Aruba"

#: includes/bundles/countries-bundle.php:29
msgid "Australia"
msgstr "Australien"

#: includes/bundles/countries-bundle.php:30
msgid "Austria"
msgstr "Österreich"

#: includes/bundles/countries-bundle.php:31
msgid "Azerbaijan"
msgstr "Aserbaidschan"

#: includes/bundles/countries-bundle.php:32
msgid "Bahamas"
msgstr "Bahamas"

#: includes/bundles/countries-bundle.php:33
msgid "Bahrain"
msgstr "Bahrain"

#: includes/bundles/countries-bundle.php:34
msgid "Bangladesh"
msgstr "Bangladesch"

#: includes/bundles/countries-bundle.php:35
msgid "Barbados"
msgstr "Barbados"

#: includes/bundles/countries-bundle.php:36
msgid "Belarus"
msgstr "Weißrussland"

#: includes/bundles/countries-bundle.php:37
msgid "Belgium"
msgstr "Belgien"

#: includes/bundles/countries-bundle.php:38
msgid "Belau"
msgstr "Belau"

#: includes/bundles/countries-bundle.php:39
msgid "Belize"
msgstr "Belize"

#: includes/bundles/countries-bundle.php:40
msgid "Benin"
msgstr "Benin"

#: includes/bundles/countries-bundle.php:41
msgid "Bermuda"
msgstr "Bermuda"

#: includes/bundles/countries-bundle.php:42
msgid "Bhutan"
msgstr "Bhutan"

#: includes/bundles/countries-bundle.php:43
msgid "Bolivia"
msgstr "Bolivien"

#: includes/bundles/countries-bundle.php:44
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Karibische Niederlande"

#: includes/bundles/countries-bundle.php:45
msgid "Bosnia and Herzegovina"
msgstr "Bosnien-Herzegowina"

#: includes/bundles/countries-bundle.php:46
msgid "Botswana"
msgstr "Botswana"

#: includes/bundles/countries-bundle.php:47
msgid "Bouvet Island"
msgstr "Bouvetinsel"

#: includes/bundles/countries-bundle.php:48
msgid "Brazil"
msgstr "Brasilien"

#: includes/bundles/countries-bundle.php:49
msgid "British Indian Ocean Territory"
msgstr "Britisches Territorium im Indischen Ozean"

#: includes/bundles/countries-bundle.php:50
msgid "British Virgin Islands"
msgstr "Britische Jungferninseln"

#: includes/bundles/countries-bundle.php:51
msgid "Brunei"
msgstr "Brunei"

#: includes/bundles/countries-bundle.php:52
msgid "Bulgaria"
msgstr "Bulgarien"

#: includes/bundles/countries-bundle.php:53
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: includes/bundles/countries-bundle.php:54
msgid "Burundi"
msgstr "Burundi"

#: includes/bundles/countries-bundle.php:55
msgid "Cambodia"
msgstr "Kambodscha"

#: includes/bundles/countries-bundle.php:56
msgid "Cameroon"
msgstr "Kamerun"

#: includes/bundles/countries-bundle.php:57
msgid "Canada"
msgstr "Kanada"

#: includes/bundles/countries-bundle.php:58
msgid "Cape Verde"
msgstr "Kap Verde"

#: includes/bundles/countries-bundle.php:59
msgid "Cayman Islands"
msgstr "Kaimaninseln"

#: includes/bundles/countries-bundle.php:60
msgid "Central African Republic"
msgstr "Zentralafrikanische Republik"

#: includes/bundles/countries-bundle.php:61
msgid "Chad"
msgstr "Tschad"

#: includes/bundles/countries-bundle.php:62
msgid "Chile"
msgstr "Chile"

#: includes/bundles/countries-bundle.php:63
msgid "China"
msgstr "China"

#: includes/bundles/countries-bundle.php:64
msgid "Christmas Island"
msgstr "Weihnachtsinsel"

#: includes/bundles/countries-bundle.php:65
msgid "Cocos (Keeling) Islands"
msgstr "Kokosinseln (Keelinginseln)"

#: includes/bundles/countries-bundle.php:66
msgid "Colombia"
msgstr "Kolumbien"

#: includes/bundles/countries-bundle.php:67
msgid "Comoros"
msgstr "Komoren"

#: includes/bundles/countries-bundle.php:68
msgid "Congo (Brazzaville)"
msgstr "Kongo - Brazzaville"

#: includes/bundles/countries-bundle.php:69
msgid "Congo (Kinshasa)"
msgstr "Kongo - Kinshasa"

#: includes/bundles/countries-bundle.php:70
msgid "Cook Islands"
msgstr "Cookinseln"

#: includes/bundles/countries-bundle.php:71
msgid "Costa Rica"
msgstr "Costa Rica"

#: includes/bundles/countries-bundle.php:72
msgid "Croatia"
msgstr "Kroatien"

#: includes/bundles/countries-bundle.php:73
msgid "Cuba"
msgstr "Kuba"

#: includes/bundles/countries-bundle.php:74
msgid "Cura&ccedil;ao"
msgstr "Curaçao"

#: includes/bundles/countries-bundle.php:75
msgid "Cyprus"
msgstr "Zypern"

#: includes/bundles/countries-bundle.php:76
msgid "Czech Republic"
msgstr "Tschechien"

#: includes/bundles/countries-bundle.php:77
msgid "Denmark"
msgstr "Dänemark"

#: includes/bundles/countries-bundle.php:78
msgid "Djibouti"
msgstr "Dschibuti"

#: includes/bundles/countries-bundle.php:79
msgid "Dominica"
msgstr "Dominikanische Republik"

#: includes/bundles/countries-bundle.php:80
msgid "Dominican Republic"
msgstr "Dominikanische Republik"

#: includes/bundles/countries-bundle.php:81
msgid "Ecuador"
msgstr "Ecuador"

#: includes/bundles/countries-bundle.php:82
msgid "Egypt"
msgstr "Ägypten"

#: includes/bundles/countries-bundle.php:83
msgid "El Salvador"
msgstr "El Salvador"

#: includes/bundles/countries-bundle.php:84
msgid "Equatorial Guinea"
msgstr "Äquatorialguinea"

#: includes/bundles/countries-bundle.php:85
msgid "Eritrea"
msgstr "Eritrea"

#: includes/bundles/countries-bundle.php:86
msgid "Estonia"
msgstr "Estonien"

#: includes/bundles/countries-bundle.php:87
msgid "Ethiopia"
msgstr "Äthiopien"

#: includes/bundles/countries-bundle.php:88
msgid "Falkland Islands"
msgstr "Falkland-Inseln"

#: includes/bundles/countries-bundle.php:89
msgid "Faroe Islands"
msgstr "Faroer-Inseln"

#: includes/bundles/countries-bundle.php:90
msgid "Fiji"
msgstr "Fidschi"

#: includes/bundles/countries-bundle.php:91
msgid "Finland"
msgstr "Finnland"

#: includes/bundles/countries-bundle.php:92
msgid "France"
msgstr "Frankreich"

#: includes/bundles/countries-bundle.php:93
msgid "French Guiana"
msgstr "Französisch-Guayana"

#: includes/bundles/countries-bundle.php:94
msgid "French Polynesia"
msgstr "Französisch-Polynesien"

#: includes/bundles/countries-bundle.php:95
msgid "French Southern Territories"
msgstr "Französische Südgebiete"

#: includes/bundles/countries-bundle.php:96
msgid "Gabon"
msgstr "Gabun"

#: includes/bundles/countries-bundle.php:97
msgid "Gambia"
msgstr "Gambien"

#: includes/bundles/countries-bundle.php:98
msgid "Georgia"
msgstr "Georgien"

#: includes/bundles/countries-bundle.php:99
msgid "Germany"
msgstr "Deutschland"

#: includes/bundles/countries-bundle.php:100
msgid "Ghana"
msgstr "Ghana"

#: includes/bundles/countries-bundle.php:101
msgid "Gibraltar"
msgstr "Gibraltar"

#: includes/bundles/countries-bundle.php:102
msgid "Greece"
msgstr "Griechenland"

#: includes/bundles/countries-bundle.php:103
msgid "Greenland"
msgstr "Grönland"

#: includes/bundles/countries-bundle.php:104
msgid "Grenada"
msgstr "Grenada"

#: includes/bundles/countries-bundle.php:105
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: includes/bundles/countries-bundle.php:106
msgid "Guam"
msgstr "Guam"

#: includes/bundles/countries-bundle.php:107
msgid "Guatemala"
msgstr "Guatemala"

#: includes/bundles/countries-bundle.php:108
msgid "Guernsey"
msgstr "Guernsey"

#: includes/bundles/countries-bundle.php:109
msgid "Guinea"
msgstr "Guinea"

#: includes/bundles/countries-bundle.php:110
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: includes/bundles/countries-bundle.php:111
msgid "Guyana"
msgstr "Guyana"

#: includes/bundles/countries-bundle.php:112
msgid "Haiti"
msgstr "Haiti"

#: includes/bundles/countries-bundle.php:113
msgid "Heard Island and McDonald Islands"
msgstr "Heard und McDonaldinseln"

#: includes/bundles/countries-bundle.php:114
msgid "Honduras"
msgstr "Honduras"

#: includes/bundles/countries-bundle.php:115
msgid "Hong Kong"
msgstr "Hong Kong"

#: includes/bundles/countries-bundle.php:116
msgid "Hungary"
msgstr "Ungarn"

#: includes/bundles/countries-bundle.php:117
msgid "Iceland"
msgstr "Island"

#: includes/bundles/countries-bundle.php:118
msgid "India"
msgstr "Indien"

#: includes/bundles/countries-bundle.php:119
msgid "Indonesia"
msgstr "Indonesien"

#: includes/bundles/countries-bundle.php:120
msgid "Iran"
msgstr "Iran"

#: includes/bundles/countries-bundle.php:121
msgid "Iraq"
msgstr "Irak"

#: includes/bundles/countries-bundle.php:122
msgid "Ireland"
msgstr "Irland"

#: includes/bundles/countries-bundle.php:123
msgid "Isle of Man"
msgstr "Isle of Man"

#: includes/bundles/countries-bundle.php:124
msgid "Israel"
msgstr "Israel"

#: includes/bundles/countries-bundle.php:125
msgid "Italy"
msgstr "Italien"

#: includes/bundles/countries-bundle.php:126
msgid "Ivory Coast"
msgstr "Elfenbeinküste"

#: includes/bundles/countries-bundle.php:127
msgid "Jamaica"
msgstr "Jamaika"

#: includes/bundles/countries-bundle.php:128
msgid "Japan"
msgstr "Japan"

#: includes/bundles/countries-bundle.php:129
msgid "Jersey"
msgstr "Jersey"

#: includes/bundles/countries-bundle.php:130
msgid "Jordan"
msgstr "Jordanien"

#: includes/bundles/countries-bundle.php:131
msgid "Kazakhstan"
msgstr "Kasachstan"

#: includes/bundles/countries-bundle.php:132
msgid "Kenya"
msgstr "Kenia"

#: includes/bundles/countries-bundle.php:133
msgid "Kiribati"
msgstr "Kiribati"

#: includes/bundles/countries-bundle.php:134
msgid "Kuwait"
msgstr "Kuwait"

#: includes/bundles/countries-bundle.php:135
msgid "Kyrgyzstan"
msgstr "Kirgisistan"

#: includes/bundles/countries-bundle.php:136
msgid "Laos"
msgstr "Laos"

#: includes/bundles/countries-bundle.php:137
msgid "Latvia"
msgstr "Lettland"

#: includes/bundles/countries-bundle.php:138
msgid "Lebanon"
msgstr "Libanon"

#: includes/bundles/countries-bundle.php:139
msgid "Lesotho"
msgstr "Lesotho"

#: includes/bundles/countries-bundle.php:140
msgid "Liberia"
msgstr "Liberia"

#: includes/bundles/countries-bundle.php:141
msgid "Libya"
msgstr "Libyen"

#: includes/bundles/countries-bundle.php:142
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: includes/bundles/countries-bundle.php:143
msgid "Lithuania"
msgstr "Litauen"

#: includes/bundles/countries-bundle.php:144
msgid "Luxembourg"
msgstr "Luxemburg"

#: includes/bundles/countries-bundle.php:145
msgid "Macao S.A.R., China"
msgstr "Macau"

#: includes/bundles/countries-bundle.php:146
msgid "Macedonia"
msgstr "Mazedonien"

#: includes/bundles/countries-bundle.php:147
msgid "Madagascar"
msgstr "Madagaskar"

#: includes/bundles/countries-bundle.php:148
msgid "Malawi"
msgstr "Malawi"

#: includes/bundles/countries-bundle.php:149
msgid "Malaysia"
msgstr "Malaysien"

#: includes/bundles/countries-bundle.php:150
msgid "Maldives"
msgstr "Malediven"

#: includes/bundles/countries-bundle.php:151
msgid "Mali"
msgstr "Mali"

#: includes/bundles/countries-bundle.php:152
msgid "Malta"
msgstr "Malta"

#: includes/bundles/countries-bundle.php:153
msgid "Marshall Islands"
msgstr "Marshallinseln"

#: includes/bundles/countries-bundle.php:154
msgid "Martinique"
msgstr "Martinique"

#: includes/bundles/countries-bundle.php:155
msgid "Mauritania"
msgstr "Mauretanien"

#: includes/bundles/countries-bundle.php:156
msgid "Mauritius"
msgstr "Mauritius"

#: includes/bundles/countries-bundle.php:157
msgid "Mayotte"
msgstr "Mayotte"

#: includes/bundles/countries-bundle.php:158
msgid "Mexico"
msgstr "Mexiko"

#: includes/bundles/countries-bundle.php:159
msgid "Micronesia"
msgstr "Mikronesien"

#: includes/bundles/countries-bundle.php:160
msgid "Moldova"
msgstr "Moldawien"

#: includes/bundles/countries-bundle.php:161
msgid "Monaco"
msgstr "Monaco"

#: includes/bundles/countries-bundle.php:162
msgid "Mongolia"
msgstr "Mongolei"

#: includes/bundles/countries-bundle.php:163
msgid "Montenegro"
msgstr "Montenegro"

#: includes/bundles/countries-bundle.php:164
msgid "Montserrat"
msgstr "Montserrat"

#: includes/bundles/countries-bundle.php:165
msgid "Morocco"
msgstr "Marokko"

#: includes/bundles/countries-bundle.php:166
msgid "Mozambique"
msgstr "Mosambik"

#: includes/bundles/countries-bundle.php:167
msgid "Myanmar"
msgstr "Myanmar"

#: includes/bundles/countries-bundle.php:168
msgid "Namibia"
msgstr "Namibia"

#: includes/bundles/countries-bundle.php:169
msgid "Nauru"
msgstr "Nauru"

#: includes/bundles/countries-bundle.php:170
msgid "Nepal"
msgstr "Nepal"

#: includes/bundles/countries-bundle.php:171
msgid "Netherlands"
msgstr "Niederlande"

#: includes/bundles/countries-bundle.php:172
msgid "New Caledonia"
msgstr "Neukaledonien"

#: includes/bundles/countries-bundle.php:173
msgid "New Zealand"
msgstr "Neuseeland"

#: includes/bundles/countries-bundle.php:174
msgid "Nicaragua"
msgstr "Nicaragua"

#: includes/bundles/countries-bundle.php:175
msgid "Niger"
msgstr "Niger"

#: includes/bundles/countries-bundle.php:176
msgid "Nigeria"
msgstr "Nigeria"

#: includes/bundles/countries-bundle.php:177
msgid "Niue"
msgstr "Niue"

#: includes/bundles/countries-bundle.php:178
msgid "Norfolk Island"
msgstr "Norfolkinsel"

#: includes/bundles/countries-bundle.php:179
msgid "Northern Mariana Islands"
msgstr "Nördliche Marianen"

#: includes/bundles/countries-bundle.php:180
msgid "North Korea"
msgstr "Nordkorea"

#: includes/bundles/countries-bundle.php:181
msgid "Norway"
msgstr "Norwegen"

#: includes/bundles/countries-bundle.php:182
msgid "Oman"
msgstr "Oman"

#: includes/bundles/countries-bundle.php:183
msgid "Pakistan"
msgstr "Pakistan"

#: includes/bundles/countries-bundle.php:184
msgid "Palestinian Territory"
msgstr "Palästinensische Autonomiegebiete"

#: includes/bundles/countries-bundle.php:185
msgid "Panama"
msgstr "Panama"

#: includes/bundles/countries-bundle.php:186
msgid "Papua New Guinea"
msgstr "Papua-Neuguinea"

#: includes/bundles/countries-bundle.php:187
msgid "Paraguay"
msgstr "Paraguay"

#: includes/bundles/countries-bundle.php:188
#: includes/settings/main-settings.php:37
msgid "Peru"
msgstr "Peru"

#: includes/bundles/countries-bundle.php:189
msgid "Philippines"
msgstr "Philippinen"

#: includes/bundles/countries-bundle.php:190
msgid "Pitcairn"
msgstr "Pitcairninseln"

#: includes/bundles/countries-bundle.php:191
msgid "Poland"
msgstr "Polen"

#: includes/bundles/countries-bundle.php:192
msgid "Portugal"
msgstr "Portugal"

#: includes/bundles/countries-bundle.php:193
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: includes/bundles/countries-bundle.php:194
msgid "Qatar"
msgstr "Katar"

#: includes/bundles/countries-bundle.php:195
msgid "Reunion"
msgstr "Réunion"

#: includes/bundles/countries-bundle.php:196
msgid "Romania"
msgstr "Rumänien"

#: includes/bundles/countries-bundle.php:197
msgid "Russia"
msgstr "Russland"

#: includes/bundles/countries-bundle.php:198
msgid "Rwanda"
msgstr "Ruanda"

#: includes/bundles/countries-bundle.php:199
msgid "Saint Barth&eacute;lemy"
msgstr "Saint-Barthélemy"

#: includes/bundles/countries-bundle.php:200
msgid "Saint Helena"
msgstr "St. Helena"

#: includes/bundles/countries-bundle.php:201
msgid "Saint Kitts and Nevis"
msgstr "St. Kitts und Nevis"

#: includes/bundles/countries-bundle.php:202
msgid "Saint Lucia"
msgstr "St. Lucia"

#: includes/bundles/countries-bundle.php:203
msgid "Saint Martin (French part)"
msgstr "St. Martin (französischer Teil)"

#: includes/bundles/countries-bundle.php:204
msgid "Saint Martin (Dutch part)"
msgstr "St. Martin (niederländischer Inselteil)"

#: includes/bundles/countries-bundle.php:205
msgid "Saint Pierre and Miquelon"
msgstr "Saint-Pierre und Miquelon"

#: includes/bundles/countries-bundle.php:206
msgid "Saint Vincent and the Grenadines"
msgstr "St. Vincent und die Grenadinen"

#: includes/bundles/countries-bundle.php:207
msgid "San Marino"
msgstr "San Marino"

#: includes/bundles/countries-bundle.php:208
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "São Tomé und Príncipe"

#: includes/bundles/countries-bundle.php:209
msgid "Saudi Arabia"
msgstr "Saudi-Arabien"

#: includes/bundles/countries-bundle.php:210
msgid "Senegal"
msgstr "Senegal"

#: includes/bundles/countries-bundle.php:211
msgid "Serbia"
msgstr "Serbien"

#: includes/bundles/countries-bundle.php:212
msgid "Seychelles"
msgstr "Seychellen"

#: includes/bundles/countries-bundle.php:213
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: includes/bundles/countries-bundle.php:214
msgid "Singapore"
msgstr "Singapur"

#: includes/bundles/countries-bundle.php:215
msgid "Slovakia"
msgstr "Slowakei"

#: includes/bundles/countries-bundle.php:216
msgid "Slovenia"
msgstr "Slowenien"

#: includes/bundles/countries-bundle.php:217
msgid "Solomon Islands"
msgstr "Salomon-Inseln"

#: includes/bundles/countries-bundle.php:218
msgid "Somalia"
msgstr "Somalia"

#: includes/bundles/countries-bundle.php:219
msgid "South Africa"
msgstr "Südafrika"

#: includes/bundles/countries-bundle.php:220
msgid "South Georgia/Sandwich Islands"
msgstr "Südgeorgien und die Südlichen Sandwichinseln"

#: includes/bundles/countries-bundle.php:221
msgid "South Korea"
msgstr "Südkorea"

#: includes/bundles/countries-bundle.php:222
msgid "South Sudan"
msgstr "Südsudan"

#: includes/bundles/countries-bundle.php:223
msgid "Spain"
msgstr "Spanien"

#: includes/bundles/countries-bundle.php:224
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: includes/bundles/countries-bundle.php:225
msgid "Sudan"
msgstr "Sudan"

#: includes/bundles/countries-bundle.php:226
msgid "Suriname"
msgstr "Surinam"

#: includes/bundles/countries-bundle.php:227
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard und Jan Mayen"

#: includes/bundles/countries-bundle.php:228
msgid "Swaziland"
msgstr "Swasiland"

#: includes/bundles/countries-bundle.php:229
msgid "Sweden"
msgstr "Schweden"

#: includes/bundles/countries-bundle.php:230
msgid "Switzerland"
msgstr "Schweiz"

#: includes/bundles/countries-bundle.php:231
msgid "Syria"
msgstr "Syrien"

#: includes/bundles/countries-bundle.php:232
msgid "Taiwan"
msgstr "Taiwan"

#: includes/bundles/countries-bundle.php:233
msgid "Tajikistan"
msgstr "Tadschikistan"

#: includes/bundles/countries-bundle.php:234
msgid "Tanzania"
msgstr "Tansania"

#: includes/bundles/countries-bundle.php:235
msgid "Thailand"
msgstr "Thailand"

#: includes/bundles/countries-bundle.php:236
msgid "Timor-Leste"
msgstr "Osttimor"

#: includes/bundles/countries-bundle.php:237
msgid "Togo"
msgstr "Togo"

#: includes/bundles/countries-bundle.php:238
msgid "Tokelau"
msgstr "Tokelau"

#: includes/bundles/countries-bundle.php:239
msgid "Tonga"
msgstr "Tonga"

#: includes/bundles/countries-bundle.php:240
msgid "Trinidad and Tobago"
msgstr "Trinidad und Tobago"

#: includes/bundles/countries-bundle.php:241
msgid "Tunisia"
msgstr "Tunesien"

#: includes/bundles/countries-bundle.php:242
msgid "Turkey"
msgstr "Türkei"

#: includes/bundles/countries-bundle.php:243
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: includes/bundles/countries-bundle.php:244
msgid "Turks and Caicos Islands"
msgstr "Turks- und Caicosinseln"

#: includes/bundles/countries-bundle.php:245
msgid "Tuvalu"
msgstr "Tuvalu"

#: includes/bundles/countries-bundle.php:246
msgid "Uganda"
msgstr "Uganda"

#: includes/bundles/countries-bundle.php:247
msgid "Ukraine"
msgstr "Ukraine"

#: includes/bundles/countries-bundle.php:248
msgid "United Arab Emirates"
msgstr "Vereinigte Arabische Emirate"

#: includes/bundles/countries-bundle.php:249
msgid "United Kingdom (UK)"
msgstr "Vereinigtes Königreich (UK)"

#: includes/bundles/countries-bundle.php:250
msgid "United States (US)"
msgstr "Vereinigte Staaten (USA)"

#: includes/bundles/countries-bundle.php:251
msgid "United States (US) Minor Outlying Islands"
msgstr "United States Minor Outlying Islands"

#: includes/bundles/countries-bundle.php:252
msgid "United States (US) Virgin Islands"
msgstr "Amerikanische Jungferninseln"

#: includes/bundles/countries-bundle.php:253
msgid "Uruguay"
msgstr "Uruguay"

#: includes/bundles/countries-bundle.php:254
msgid "Uzbekistan"
msgstr "Usbekistan"

#: includes/bundles/countries-bundle.php:255
msgid "Vanuatu"
msgstr "Vanuatu"

#: includes/bundles/countries-bundle.php:256
msgid "Vatican"
msgstr "Vatikan"

#: includes/bundles/countries-bundle.php:257
msgid "Venezuela"
msgstr "Venezuela"

#: includes/bundles/countries-bundle.php:258
msgid "Vietnam"
msgstr "Vietnam"

#: includes/bundles/countries-bundle.php:259
msgid "Wallis and Futuna"
msgstr "Wallis und Futuna"

#: includes/bundles/countries-bundle.php:260
msgid "Western Sahara"
msgstr "Westsahara"

#: includes/bundles/countries-bundle.php:261
msgid "Samoa"
msgstr "Samoa"

#: includes/bundles/countries-bundle.php:262
msgid "Yemen"
msgstr "Jemen"

#: includes/bundles/countries-bundle.php:263
msgid "Zambia"
msgstr "Sambia"

#: includes/bundles/countries-bundle.php:264
msgid "Zimbabwe"
msgstr "Simbabwe"

#: includes/bundles/currency-bundle.php:17
msgid "Euro"
msgstr "Euro"

#: includes/bundles/currency-bundle.php:18
msgid "United States (US) dollar"
msgstr "US-Dollar"

#: includes/bundles/currency-bundle.php:19
msgid "Pound sterling"
msgstr "Pfund Sterling"

#: includes/bundles/currency-bundle.php:20
msgid "United Arab Emirates dirham"
msgstr "Dirham der Vereinigten Arabischen Emirate"

#: includes/bundles/currency-bundle.php:21
msgid "Afghan afghani"
msgstr "Afghanischer Afghani"

#: includes/bundles/currency-bundle.php:22
msgid "Albanian lek"
msgstr "Albanischer Lek"

#: includes/bundles/currency-bundle.php:23
msgid "Armenian dram"
msgstr "Armenischer Dram"

#: includes/bundles/currency-bundle.php:24
msgid "Netherlands Antillean guilder"
msgstr "Niederländischer Antillen-Gulden"

#: includes/bundles/currency-bundle.php:25
msgid "Angolan kwanza"
msgstr "Angolischer Kwanza"

#: includes/bundles/currency-bundle.php:26
msgid "Argentine peso"
msgstr "Argentinischer Peso"

#: includes/bundles/currency-bundle.php:27
msgid "Australian dollar"
msgstr "Australischer Dollar"

#: includes/bundles/currency-bundle.php:28
msgid "Aruban florin"
msgstr "Aruba-Florin"

#: includes/bundles/currency-bundle.php:29
msgid "Azerbaijani manat"
msgstr "Aserbaidschan-Manat"

#: includes/bundles/currency-bundle.php:30
msgid "Bosnia and Herzegovina convertible mark"
msgstr "Konvertible Mark (BAM)"

#: includes/bundles/currency-bundle.php:31
msgid "Barbadian dollar"
msgstr "Barbados-Dollar"

#: includes/bundles/currency-bundle.php:32
msgid "Bangladeshi taka"
msgstr "Bangladeschischer Taka"

#: includes/bundles/currency-bundle.php:33
msgid "Bulgarian lev"
msgstr "Bulgarischer Lew"

#: includes/bundles/currency-bundle.php:34
msgid "Bahraini dinar"
msgstr "Bahrain-Dinar"

#: includes/bundles/currency-bundle.php:35
msgid "Burundian franc"
msgstr "Burundi-Franc"

#: includes/bundles/currency-bundle.php:36
msgid "Bermudian dollar"
msgstr "Bermuda-Dollar"

#: includes/bundles/currency-bundle.php:37
msgid "Brunei dollar"
msgstr "Brunei-Dollar"

#: includes/bundles/currency-bundle.php:38
msgid "Bolivian boliviano"
msgstr "Bolivianischer Boliviano"

#: includes/bundles/currency-bundle.php:39
msgid "Brazilian real"
msgstr "Brasilianischer Real"

#: includes/bundles/currency-bundle.php:40
msgid "Bahamian dollar"
msgstr "Bahama-Dollar"

#: includes/bundles/currency-bundle.php:41
msgid "Bitcoin"
msgstr "Bitcoin"

#: includes/bundles/currency-bundle.php:42
msgid "Bhutanese ngultrum"
msgstr "Bhutanischer Ngultrum"

#: includes/bundles/currency-bundle.php:43
msgid "Botswana pula"
msgstr "Botswanischer Pula"

#: includes/bundles/currency-bundle.php:44
msgid "Belarusian ruble (old)"
msgstr "Alter Weißrussischer Rubel"

#: includes/bundles/currency-bundle.php:45
msgid "Belarusian ruble"
msgstr "Weißrussischer Rubel"

#: includes/bundles/currency-bundle.php:46
msgid "Belize dollar"
msgstr "Belize-Dollar"

#: includes/bundles/currency-bundle.php:47
msgid "Canadian dollar"
msgstr "Kanadischer Dollar"

#: includes/bundles/currency-bundle.php:48
msgid "Congolese franc"
msgstr "Kongo-Franc"

#: includes/bundles/currency-bundle.php:49
msgid "Swiss franc"
msgstr "Schweizer Franken"

#: includes/bundles/currency-bundle.php:50
msgid "Chilean peso"
msgstr "Chilenischer Peso"

#: includes/bundles/currency-bundle.php:51
msgid "Chinese yuan"
msgstr "Chinesischer Yuan"

#: includes/bundles/currency-bundle.php:52
msgid "Colombian peso"
msgstr "Kolumbianischer Peso"

#: includes/bundles/currency-bundle.php:53
msgid "Costa Rican col&oacute;n"
msgstr "Costa-Rica-Colon"

#: includes/bundles/currency-bundle.php:54
msgid "Cuban convertible peso"
msgstr "Peso Convertible"

#: includes/bundles/currency-bundle.php:55
msgid "Cuban peso"
msgstr "Kubanischer Peso"

#: includes/bundles/currency-bundle.php:56
msgid "Cape Verdean escudo"
msgstr "Kap-Verde-Escudo"

#: includes/bundles/currency-bundle.php:57
msgid "Czech koruna"
msgstr "Tschechische Krone"

#: includes/bundles/currency-bundle.php:58
msgid "Djiboutian franc"
msgstr "Dschibuti-Franc"

#: includes/bundles/currency-bundle.php:59
msgid "Danish krone"
msgstr "Dänische Krone"

#: includes/bundles/currency-bundle.php:60
msgid "Dominican peso"
msgstr "Dominikanischer Peso"

#: includes/bundles/currency-bundle.php:61
msgid "Algerian dinar"
msgstr "Algerischer Dinar"

#: includes/bundles/currency-bundle.php:62
msgid "Egyptian pound"
msgstr "Ägyptisches Pfund"

#: includes/bundles/currency-bundle.php:63
msgid "Eritrean nakfa"
msgstr "Eritreischer Nakfa"

#: includes/bundles/currency-bundle.php:64
msgid "Ethiopian birr"
msgstr "Äthiopischer Birr"

#: includes/bundles/currency-bundle.php:65
msgid "Fijian dollar"
msgstr "Fidschi-Dollar"

#: includes/bundles/currency-bundle.php:66
msgid "Falkland Islands pound"
msgstr "Falkland-Pfund"

#: includes/bundles/currency-bundle.php:67
msgid "Georgian lari"
msgstr "Georgischer Lari"

#: includes/bundles/currency-bundle.php:68
msgid "Guernsey pound"
msgstr "Guernsey-Pfund"

#: includes/bundles/currency-bundle.php:69
msgid "Ghana cedi"
msgstr "Ghanaischer Cedi"

#: includes/bundles/currency-bundle.php:70
msgid "Gibraltar pound"
msgstr "Gibraltar-Pfund"

#: includes/bundles/currency-bundle.php:71
msgid "Gambian dalasi"
msgstr "Gambischer Dalasi"

#: includes/bundles/currency-bundle.php:72
msgid "Guinean franc"
msgstr "Guinea-Franc"

#: includes/bundles/currency-bundle.php:73
msgid "Guatemalan quetzal"
msgstr "Guatemaltekischer Quetzal"

#: includes/bundles/currency-bundle.php:74
msgid "Guyanese dollar"
msgstr "Guyana-Dollar"

#: includes/bundles/currency-bundle.php:75
msgid "Hong Kong dollar"
msgstr "Hongkong-Dollar"

#: includes/bundles/currency-bundle.php:76
msgid "Honduran lempira"
msgstr "Honduras-Lempira"

#: includes/bundles/currency-bundle.php:77
msgid "Croatian kuna"
msgstr "Kroatische Kuna"

#: includes/bundles/currency-bundle.php:78
msgid "Haitian gourde"
msgstr "Haitianische Gourde"

#: includes/bundles/currency-bundle.php:79
msgid "Hungarian forint"
msgstr "Ungarischer Forint"

#: includes/bundles/currency-bundle.php:80
msgid "Indonesian rupiah"
msgstr "Indonesische Rupiah"

#: includes/bundles/currency-bundle.php:81
msgid "Israeli new shekel"
msgstr "Israelischer Neuer Schekel"

#: includes/bundles/currency-bundle.php:82
msgid "Manx pound"
msgstr "Manx-Pfund"

#: includes/bundles/currency-bundle.php:83
msgid "Indian rupee"
msgstr "Indische Rupie"

#: includes/bundles/currency-bundle.php:84
msgid "Iraqi dinar"
msgstr "Irakischer Dinar"

#: includes/bundles/currency-bundle.php:85
msgid "Iranian rial"
msgstr "Iranischer Rial"

#: includes/bundles/currency-bundle.php:86
msgid "Iranian toman"
msgstr "Iranischer Toman"

#: includes/bundles/currency-bundle.php:87
msgid "Icelandic kr&oacute;na"
msgstr "Isländische Krone"

#: includes/bundles/currency-bundle.php:88
msgid "Jersey pound"
msgstr "Jersey-Pfund"

#: includes/bundles/currency-bundle.php:89
msgid "Jamaican dollar"
msgstr "Jamaika-Dollar"

#: includes/bundles/currency-bundle.php:90
msgid "Jordanian dinar"
msgstr "Jordanischer Dinar"

#: includes/bundles/currency-bundle.php:91
msgid "Japanese yen"
msgstr "Japanischer Yen"

#: includes/bundles/currency-bundle.php:92
msgid "Kenyan shilling"
msgstr "Kenianischer Shilling"

#: includes/bundles/currency-bundle.php:93
msgid "Kyrgyzstani som"
msgstr "Kirgisischer Som"

#: includes/bundles/currency-bundle.php:94
msgid "Cambodian riel"
msgstr "Kambodschanischer Riel"

#: includes/bundles/currency-bundle.php:95
msgid "Comorian franc"
msgstr "Komoren-Franc"

#: includes/bundles/currency-bundle.php:96
msgid "North Korean won"
msgstr "Nordkoreanischer Won"

#: includes/bundles/currency-bundle.php:97
msgid "South Korean won"
msgstr "Südkoreanischer Won"

#: includes/bundles/currency-bundle.php:98
msgid "Kuwaiti dinar"
msgstr "Kuwait-Dinar"

#: includes/bundles/currency-bundle.php:99
msgid "Cayman Islands dollar"
msgstr "Kaiman-Dollar"

#: includes/bundles/currency-bundle.php:100
msgid "Kazakhstani tenge"
msgstr "Kasachischer Tenge"

#: includes/bundles/currency-bundle.php:101
msgid "Lao kip"
msgstr "Laotischer Kip"

#: includes/bundles/currency-bundle.php:102
msgid "Lebanese pound"
msgstr "Libanesisches Pfund"

#: includes/bundles/currency-bundle.php:103
msgid "Sri Lankan rupee"
msgstr "Sri-Lanka-Rupie"

#: includes/bundles/currency-bundle.php:104
msgid "Liberian dollar"
msgstr "Liberianischer Dollar"

#: includes/bundles/currency-bundle.php:105
msgid "Lesotho loti"
msgstr "Lesothischer Loti"

#: includes/bundles/currency-bundle.php:106
msgid "Libyan dinar"
msgstr "Libyscher Dinar"

#: includes/bundles/currency-bundle.php:107
msgid "Moroccan dirham"
msgstr "Marokkanischer Dirham"

#: includes/bundles/currency-bundle.php:108
msgid "Moldovan leu"
msgstr "Moldauischer Leu"

#: includes/bundles/currency-bundle.php:109
msgid "Malagasy ariary"
msgstr "Madagascar-Ariary"

#: includes/bundles/currency-bundle.php:110
msgid "Macedonian denar"
msgstr "Mazedonischer Denar"

#: includes/bundles/currency-bundle.php:111
msgid "Burmese kyat"
msgstr "Myanmar-Kyat"

#: includes/bundles/currency-bundle.php:112
msgid "Mongolian t&ouml;gr&ouml;g"
msgstr "Mongolischer Tugrik"

#: includes/bundles/currency-bundle.php:113
msgid "Macanese pataca"
msgstr "Macau-Pataca"

#: includes/bundles/currency-bundle.php:114
msgid "Mauritanian ouguiya"
msgstr "Mauretanien Ouguiya"

#: includes/bundles/currency-bundle.php:115
msgid "Mauritian rupee"
msgstr "Mauritius-Rupie"

#: includes/bundles/currency-bundle.php:116
msgid "Maldivian rufiyaa"
msgstr "Malediven-Rupie"

#: includes/bundles/currency-bundle.php:117
msgid "Malawian kwacha"
msgstr "Malawi-Kwacha"

#: includes/bundles/currency-bundle.php:118
msgid "Mexican peso"
msgstr "Mexikanischer Peso"

#: includes/bundles/currency-bundle.php:119
msgid "Malaysian ringgit"
msgstr "Malaysischer Ringgit"

#: includes/bundles/currency-bundle.php:120
msgid "Mozambican metical"
msgstr "Mosambikanischer Metical"

#: includes/bundles/currency-bundle.php:121
msgid "Namibian dollar"
msgstr "Namibia-Dollar"

#: includes/bundles/currency-bundle.php:122
msgid "Nigerian naira"
msgstr "Nigerianischer Naira"

#: includes/bundles/currency-bundle.php:123
msgid "Nicaraguan c&oacute;rdoba"
msgstr "Nicaraguanischer Cordoba"

#: includes/bundles/currency-bundle.php:124
msgid "Norwegian krone"
msgstr "Norwegische Krone"

#: includes/bundles/currency-bundle.php:125
msgid "Nepalese rupee"
msgstr "Nepalesische Rupie"

#: includes/bundles/currency-bundle.php:126
msgid "New Zealand dollar"
msgstr "Neuseeland-Dollar"

#: includes/bundles/currency-bundle.php:127
msgid "Omani rial"
msgstr "Omanischer Rial"

#: includes/bundles/currency-bundle.php:128
msgid "Panamanian balboa"
msgstr "Panamaischer Balboa"

#: includes/bundles/currency-bundle.php:129
msgid "Sol"
msgstr "Sol"

#: includes/bundles/currency-bundle.php:130
msgid "Papua New Guinean kina"
msgstr "Papua Neuguinea-Kina"

#: includes/bundles/currency-bundle.php:131
msgid "Philippine peso"
msgstr "Philippinischer Peso"

#: includes/bundles/currency-bundle.php:132
msgid "Pakistani rupee"
msgstr "Pakistanische Rupie"

#: includes/bundles/currency-bundle.php:133
msgid "Polish z&#x142;oty"
msgstr "Polnischer Zloty"

#: includes/bundles/currency-bundle.php:134
msgid "Transnistrian ruble"
msgstr "Transnistrischer Rubel"

#: includes/bundles/currency-bundle.php:135
msgid "Paraguayan guaran&iacute;"
msgstr "Paraguayischer Guaraní"

#: includes/bundles/currency-bundle.php:136
msgid "Qatari riyal"
msgstr "Katar-Riyal"

#: includes/bundles/currency-bundle.php:137
msgid "Romanian leu"
msgstr "Rumänischer Leu"

#: includes/bundles/currency-bundle.php:138
msgid "Serbian dinar"
msgstr "Serbischer Dinar"

#: includes/bundles/currency-bundle.php:139
msgid "Russian ruble"
msgstr "Russischer Rubel"

#: includes/bundles/currency-bundle.php:140
msgid "Rwandan franc"
msgstr "Ruanda-Franc"

#: includes/bundles/currency-bundle.php:141
msgid "Saudi riyal"
msgstr "Saudi-Riyal"

#: includes/bundles/currency-bundle.php:142
msgid "Solomon Islands dollar"
msgstr "Salomonen-Dollar"

#: includes/bundles/currency-bundle.php:143
msgid "Seychellois rupee"
msgstr "Seychellen-Rupie"

#: includes/bundles/currency-bundle.php:144
msgid "Sudanese pound"
msgstr "Sudanesisches Pfund"

#: includes/bundles/currency-bundle.php:145
msgid "Swedish krona"
msgstr "Schwedische Krone"

#: includes/bundles/currency-bundle.php:146
msgid "Singapore dollar"
msgstr "Singapur-Dollar"

#: includes/bundles/currency-bundle.php:147
msgid "Saint Helena pound"
msgstr "St.-Helena-Pfund"

#: includes/bundles/currency-bundle.php:148
msgid "Sierra Leonean leone"
msgstr "Sierra-leonischer Leone"

#: includes/bundles/currency-bundle.php:149
msgid "Somali shilling"
msgstr "Somalia-Schilling"

#: includes/bundles/currency-bundle.php:150
msgid "Surinamese dollar"
msgstr "Suriname-Dollar"

#: includes/bundles/currency-bundle.php:151
msgid "South Sudanese pound"
msgstr "Südsudanesisches Pfund"

#: includes/bundles/currency-bundle.php:152
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe dobra"
msgstr "Sao Tomeischer Dobra"

#: includes/bundles/currency-bundle.php:153
msgid "Syrian pound"
msgstr "Syrisches Pfund"

#: includes/bundles/currency-bundle.php:154
msgid "Swazi lilangeni"
msgstr "Swasiländischer Lilangeni"

#: includes/bundles/currency-bundle.php:155
msgid "Thai baht"
msgstr "Thailändischer Baht"

#: includes/bundles/currency-bundle.php:156
msgid "Tajikistani somoni"
msgstr "Tajikistan Somoni"

#: includes/bundles/currency-bundle.php:157
msgid "Turkmenistan manat"
msgstr "Turkmenistan-Manat"

#: includes/bundles/currency-bundle.php:158
msgid "Tunisian dinar"
msgstr "Tunesischer Dinar"

#: includes/bundles/currency-bundle.php:159
msgid "Tongan pa&#x2bb;anga"
msgstr "Tongaische Pa’anga"

#: includes/bundles/currency-bundle.php:160
msgid "Turkish lira"
msgstr "Türkische Lira"

#: includes/bundles/currency-bundle.php:161
msgid "Trinidad and Tobago dollar"
msgstr "Trinidad-und-Tobago-Dollar"

#: includes/bundles/currency-bundle.php:162
msgid "New Taiwan dollar"
msgstr "Neuer Taiwan-Dollar"

#: includes/bundles/currency-bundle.php:163
msgid "Tanzanian shilling"
msgstr "Tansania-Schilling"

#: includes/bundles/currency-bundle.php:164
msgid "Ukrainian hryvnia"
msgstr "Ukrainische Hrywnja"

#: includes/bundles/currency-bundle.php:165
msgid "Ugandan shilling"
msgstr "Uganda-Schilling"

#: includes/bundles/currency-bundle.php:166
msgid "Uruguayan peso"
msgstr "Uruguayischer Peso"

#: includes/bundles/currency-bundle.php:167
msgid "Uzbekistani som"
msgstr "Usbekische Som"

#: includes/bundles/currency-bundle.php:168
msgid "Venezuelan bol&iacute;var"
msgstr "Venezolanischer Bolívar"

#: includes/bundles/currency-bundle.php:169
msgid "Bol&iacute;var soberano"
msgstr "Bolívar soberano"

#: includes/bundles/currency-bundle.php:170
msgid "Vietnamese &#x111;&#x1ed3;ng"
msgstr "Vietnamesischer Dong"

#: includes/bundles/currency-bundle.php:171
msgid "Vanuatu vatu"
msgstr "Vanuatischer Vatu"

#: includes/bundles/currency-bundle.php:172
msgid "Samoan t&#x101;l&#x101;"
msgstr "Samoanischer Tala"

#: includes/bundles/currency-bundle.php:173
msgid "Central African CFA franc"
msgstr "Zentralafrikanischer CFA-Franc"

#: includes/bundles/currency-bundle.php:174
msgid "East Caribbean dollar"
msgstr "Ostkaribischer Dollar"

#: includes/bundles/currency-bundle.php:175
msgid "West African CFA franc"
msgstr "Westafrikanischer CFA-Franc"

#: includes/bundles/currency-bundle.php:176
msgid "CFP franc"
msgstr "CFP-Franc"

#: includes/bundles/currency-bundle.php:177
msgid "Yemeni rial"
msgstr "Jemen-Rial"

#: includes/bundles/currency-bundle.php:178
msgid "South African rand"
msgstr "Südafrikanischer Rand"

#: includes/bundles/currency-bundle.php:179
msgid "Zambian kwacha"
msgstr "Sambischer Kwacha"

#: includes/bundles/currency-bundle.php:358
msgid "Before"
msgstr "Davor"

#: includes/bundles/currency-bundle.php:359
msgid "After"
msgstr "Danach"

#: includes/bundles/currency-bundle.php:360
msgid "Before with space"
msgstr "Davor mit Leerzeichen"

#: includes/bundles/currency-bundle.php:361
msgid "After with space"
msgstr "Danach mit Leerzeichen"

#: includes/bundles/customer-bundle.php:97
msgid "First name is required."
msgstr "Name ist erforderlich."

#: includes/bundles/customer-bundle.php:106
msgid "Last name is required."
msgstr "Nachname ist erforderlich."

#: includes/bundles/customer-bundle.php:115
msgid "Email is required."
msgstr "E-Mail ist erforderlich."

#: includes/bundles/customer-bundle.php:124
msgid "Phone is required."
msgstr "Telefon ist erforderlich."

#: includes/bundles/customer-bundle.php:128
#: includes/views/shortcodes/checkout-view.php:650
msgid "Country of residence"
msgstr "Wohnort"

#: includes/bundles/customer-bundle.php:133
msgid "Country is required."
msgstr "Land ist erforderlich."

#: includes/bundles/customer-bundle.php:142
msgid "Address is required."
msgstr "Adresse ist erforderlich."

#: includes/bundles/customer-bundle.php:151
msgid "City is required."
msgstr "Stadt ist erforderlich."

#: includes/bundles/customer-bundle.php:160
msgid "State is required."
msgstr "Bundesland ist erforderlich."

#: includes/bundles/customer-bundle.php:169
msgid "Postcode is required."
msgstr "Postleitzahl ist erforderlich."

#: includes/bundles/customer-bundle.php:178
msgid "Note is required."
msgstr "Notiz ist erforderlich."

#: includes/bundles/units-bundle.php:16
msgid "Square Meter"
msgstr "Quadratmeter"

#: includes/bundles/units-bundle.php:17
msgid "Square Foot"
msgstr "Quadratfuß"

#: includes/bundles/units-bundle.php:18
msgid "Square Yard"
msgstr "Quadratyard"

#: includes/bundles/units-bundle.php:21
msgid "m²"
msgstr "m²"

#: includes/bundles/units-bundle.php:22
msgid "ft²"
msgstr "ft²"

#: includes/bundles/units-bundle.php:23
msgid "yd²"
msgstr "yd²"

#: includes/core/helpers/price-helper.php:57
msgctxt "Zero price"
msgid "Free"
msgstr "Kostenlos"

#. translators: Price per one night. Example: $99 per night
#: includes/core/helpers/price-helper.php:144
msgctxt "Price per one night. Example: $99 per night"
msgid "per night"
msgstr "pro Nacht"

#. translators: Price for X nights. Example: $99 for 2 nights, $99 for 21 nights
#: includes/core/helpers/price-helper.php:156
msgctxt "Price for X nights. Example: $99 for 2 nights, $99 for 21 nights"
msgid "for %d nights"
msgid_plural "for %d nights"
msgstr[0] "pro Nacht"
msgstr[1] "für %d Nächte"

#: includes/crons/cron-manager.php:112
msgid "User Approval Time setted in Hotel Booking Settings"
msgstr "Benutzer-Genehmigungszeit in Hotelbuchungseinstellungen festgelegt"

#: includes/crons/cron-manager.php:117
msgid "Pending Payment Time set in Hotel Booking Settings"
msgstr "Ausstehende Zahlungszeit in Hotelbuchungseinstellungen festlegen"

#: includes/crons/cron-manager.php:122
msgid "Interval for automatic cleaning of synchronization logs."
msgstr "Intervall für automatische Bereinigung der Synchronisationsprotokolle."

#: includes/crons/cron-manager.php:127
msgid "Once a week"
msgstr "Einmal pro Woche"

#: includes/csv/bookings/bookings-exporter-helper.php:73
#: templates/account/bookings.php:19
#: templates/account/bookings.php:70
#: templates/create-booking/search/search-form.php:42
#: templates/edit-booking/edit-dates.php:29
#: templates/shortcodes/search/search-form.php:35
msgid "Check-in"
msgstr "Anreise"

#: includes/csv/bookings/bookings-exporter-helper.php:74
#: templates/account/bookings.php:20
#: templates/account/bookings.php:73
#: templates/create-booking/search/search-form.php:62
#: templates/edit-booking/edit-dates.php:38
#: templates/shortcodes/search/search-form.php:55
msgid "Check-out"
msgstr "Abreise"

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/csv/bookings/bookings-exporter-helper.php:78
#: includes/post-types/rate-cpt.php:104
msgid "Rate"
msgstr "Tarif"

#: includes/csv/bookings/bookings-exporter-helper.php:79
msgid "Adults/Guests"
msgstr "Erwachsene / Gäste"

#: includes/csv/bookings/bookings-exporter-helper.php:91
#: includes/emails/templaters/reserved-rooms-templater.php:223
#: includes/views/edit-booking/checkout-view.php:164
#: includes/views/shortcodes/checkout-view.php:291
msgid "Full Guest Name"
msgstr "Vollständiger Name des Gastes"

#: includes/csv/bookings/bookings-exporter-helper.php:92
#: includes/views/booking-view.php:141
msgid "Accommodation Subtotal"
msgstr "Unterkunft Zwischensumme"

#: includes/csv/bookings/bookings-exporter-helper.php:93
#: includes/post-types/coupon-cpt.php:72
#: includes/views/booking-view.php:150
msgid "Accommodation Discount"
msgstr "Übernachtungsrabatt"

#: includes/csv/bookings/bookings-exporter-helper.php:94
#: includes/views/booking-view.php:160
msgid "Accommodation Total"
msgstr "Unterkunft Summe"

#: includes/csv/bookings/bookings-exporter-helper.php:96
#: includes/views/booking-view.php:186
msgid "Accommodation Taxes Total"
msgstr "Unterkunft Steuern Summe"

#: includes/csv/bookings/bookings-exporter-helper.php:98
#: includes/views/booking-view.php:225
msgid "Services Subtotal"
msgstr "Services-Zwischensumme"

#: includes/csv/bookings/bookings-exporter-helper.php:99
#: includes/views/booking-view.php:236
msgid "Services Discount"
msgstr "Dienstleistungsrabatt"

#: includes/csv/bookings/bookings-exporter-helper.php:100
#: includes/views/booking-view.php:248
msgid "Services Total"
msgstr "Services gesamt"

#: includes/csv/bookings/bookings-exporter-helper.php:101
#: includes/views/booking-view.php:280
msgid "Service Taxes Total"
msgstr "Service-Steuern gesamt"

#: includes/csv/bookings/bookings-exporter-helper.php:103
#: includes/views/booking-view.php:312
msgid "Fees Subtotal"
msgstr "Gebühren Zwischensumme"

#: includes/csv/bookings/bookings-exporter-helper.php:104
#: includes/views/booking-view.php:321
msgid "Fees Discount"
msgstr "Gebührenrabatt"

#: includes/csv/bookings/bookings-exporter-helper.php:105
#: includes/views/booking-view.php:331
msgid "Fees Total"
msgstr "Gesamtkosten"

#: includes/csv/bookings/bookings-exporter-helper.php:106
#: includes/views/booking-view.php:364
msgid "Fee Taxes Total"
msgstr "Steuer Gesamtkosten"

#: includes/csv/bookings/bookings-exporter-helper.php:108
msgid "Discount"
msgstr "Rabatt"

#: includes/csv/bookings/bookings-exporter-helper.php:109
#: includes/views/booking-view.php:451
#: templates/account/bookings.php:21
#: templates/account/bookings.php:76
msgid "Total"
msgstr "Gesamt"

#: includes/csv/bookings/bookings-exporter-helper.php:110
msgid "Paid"
msgstr "Bezahlt"

#: includes/csv/bookings/bookings-exporter-helper.php:111
#: includes/post-types/payment-cpt.php:129
#: includes/shortcodes/booking-confirmation-shortcode.php:284
msgid "Payment Details"
msgstr "Zahlungsdetails"

#: includes/csv/bookings/bookings-query.php:92
msgid "Please select columns to export."
msgstr "Wählen Sie bitte Spateln zu exportieren aus."

#: includes/csv/csv-export-handler.php:32
#: includes/payments/gateways/stripe-gateway.php:559
msgid "Nonce verification failed."
msgstr "Die Nonce-Verifizierung ist fehlgeschlagen."

#: includes/csv/csv-export-handler.php:50
msgid "The file does not exist."
msgstr "Die Datei existiert nicht."

#: includes/emails/abstract-email.php:441
msgid "Disable this email notification"
msgstr "Diese E-Mail-Benachrichtigung deaktivieren"

#: includes/emails/abstract-email.php:449
msgid "Subject"
msgstr "Betreff"

#: includes/emails/abstract-email.php:461
msgid "Header"
msgstr "Kopfzeile"

#: includes/emails/abstract-email.php:473
msgid "Email Template"
msgstr "E-Mail Template"

#: includes/emails/abstract-email.php:570
msgid "\"%s\" email will not be sent: there is no customer email in the booking."
msgstr "Die E-Mail \"%s\" kann nicht gesendet werden: In der Buchung ist keine Kunden-E-Mail enthalten."

#: includes/emails/abstract-email.php:594
msgid "Deprecated tags in header of %s"
msgstr "Veraltete Tags in Header von %s"

#: includes/emails/abstract-email.php:597
msgid "Deprecated tags in subject of %s"
msgstr "Veraltete Tags in Thema von %s"

#: includes/emails/abstract-email.php:600
msgid "Deprecated tags in template of %s"
msgstr "Veraltete Tags in Template von %s"

#: includes/emails/booking/admin/base-email.php:37
msgid "Recipients"
msgstr "Empfänger"

#: includes/emails/booking/admin/base-email.php:40
msgid "You can use multiple comma-separated emails"
msgstr "Sie können mehrere Komma-getrennte E-Mails eingeben"

#: includes/emails/booking/admin/base-email.php:89
msgid "\"%s\" mail was sent to admin."
msgstr "\"%s\" Mail wurde dem Administrator gesendet."

#: includes/emails/booking/admin/base-email.php:93
msgid "\"%s\" mail sending to admin is failed."
msgstr "\"%s\" Mail-Sendung dem Administrator ist fehlgeschlagen."

#: includes/emails/booking/admin/cancelled-email.php:8
msgid "Booking Cancelled"
msgstr "Buchung wurde storniert"

#: includes/emails/booking/admin/cancelled-email.php:12
msgid "%site_title% - Booking #%booking_id% Cancelled"
msgstr "%site_title% - Buchung #%booking_id% storniert"

#: includes/emails/booking/admin/cancelled-email.php:16
msgid "Email that will be sent to Admin when customer cancels booking."
msgstr "E-Mail, die an Admin gesendet wird, wenn der Kunde die Buchung storniert."

#: includes/emails/booking/admin/cancelled-email.php:20
#: includes/emails/booking/customer/cancelled-email.php:20
msgid "Cancelled Booking Email"
msgstr "Stornierte Buchungs-E-Mail"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:8
#: includes/emails/booking/admin/confirmed-email.php:8
#: includes/wizard.php:134
msgid "Booking Confirmed"
msgstr "Buchung bestätigt"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:12
#: includes/emails/booking/admin/confirmed-email.php:12
msgid "%site_title% - Booking #%booking_id% Confirmed"
msgstr "%site_title% - Buchung #%booking_id% bestätigt"

#: includes/emails/booking/admin/confirmed-by-payment-email.php:16
msgid "Email that will be sent to Admin when payment is completed."
msgstr "E-Mail, die an den Administrator gesendet wird, wenn die Zahlung abgeschlossen ist."

#: includes/emails/booking/admin/confirmed-by-payment-email.php:20
msgid "Approved Booking Email (via payment)"
msgstr "Bestätigte Buchung-E-Mail  (via Zahlung)"

#: includes/emails/booking/admin/confirmed-email.php:16
msgid "Email that will be sent to Admin when customer confirms booking."
msgstr "E-Mail, die an den Administrator gesendet wird, wenn der Kunde die Buchung bestätigt."

#: includes/emails/booking/admin/confirmed-email.php:20
#: includes/emails/booking/customer/approved-email.php:20
msgid "Approved Booking Email"
msgstr "Genehmigte Buchungs-E-Mail"

#: includes/emails/booking/admin/pending-email.php:8
msgid "Confirm new booking"
msgstr "Neue Buchung bestätigen"

#: includes/emails/booking/admin/pending-email.php:12
msgid "%site_title% - New booking #%booking_id%"
msgstr "%site_title% - Neue Buchung #%booking_id%"

#: includes/emails/booking/admin/pending-email.php:16
msgid "Email that will be sent to administrator after booking is placed."
msgstr "E-Mail, die nach der Buchung an den Administrator geschickt wird."

#: includes/emails/booking/admin/pending-email.php:20
msgid "Pending Booking Email"
msgstr "Ausstehende Buchungs-E-Mail"

#: includes/emails/booking/customer/approved-email.php:8
msgid "Your booking is approved"
msgstr "Ihre Buchung ist akzeptiert"

#: includes/emails/booking/customer/approved-email.php:12
msgid "%site_title% - Your booking #%booking_id% is approved"
msgstr "%site_title% - Ihre Buchung #%booking_id% ist akzeptiert"

#: includes/emails/booking/customer/approved-email.php:16
msgid "Email that will be sent to customer when booking is approved."
msgstr "E-Mail, die bei der Buchung an den Kunden geschickt wird."

#: includes/emails/booking/customer/base-email.php:55
msgid "\"%s\" mail was sent to customer."
msgstr "\"%s\" Mail wurde an den Kunden geschickt."

#: includes/emails/booking/customer/base-email.php:59
msgid "\"%s\" mail sending is failed."
msgstr "\"%s\" Mail-Sendung ist fehlgeschlagen."

#: includes/emails/booking/customer/cancelled-email.php:8
msgid "Your booking is cancelled"
msgstr "Ihre Buchung wurde storniert"

#: includes/emails/booking/customer/cancelled-email.php:12
msgid "%site_title% - Your booking #%booking_id% is cancelled"
msgstr "%site_title% - Ihre Buchung #%booking_id% wurde storniert"

#: includes/emails/booking/customer/cancelled-email.php:16
msgid "Email that will be sent to customer when booking is cancelled."
msgstr "E-Mail, die an den Kunden gesendet wird, wenn die Buchung storniert wird."

#: includes/emails/booking/customer/confirmation-email.php:8
msgid "Confirm your booking"
msgstr "Bestätigen Sie Ihre Buchung"

#: includes/emails/booking/customer/confirmation-email.php:12
msgid "%site_title% - Confirm your booking #%booking_id%"
msgstr "%site_title% - Bestätigen Sie Ihre Buchung #%booking_id%"

#: includes/emails/booking/customer/confirmation-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Customer confirmation via email."
msgstr "Diese E-Mail wird gesendet, wenn „Buchungsbestätigungsmodus“ auf „Kunden-Bestätigung per E-Mail“ eingestellt ist."

#: includes/emails/booking/customer/confirmation-email.php:17
#: includes/emails/booking/customer/direct-bank-transfer-email.php:43
#: includes/emails/booking/customer/pending-email.php:17
msgid "Email that will be sent to customer after booking is placed."
msgstr "E-Mail, die dem Kunden nach der Buchung zugesandt wird."

#: includes/emails/booking/customer/confirmation-email.php:21
msgid "New Booking Email (Confirmation by User)"
msgstr "Neue Buchungs-E-Mail (Bestätigung durch Benutzer)"

#: includes/emails/booking/customer/direct-bank-transfer-email.php:35
msgid "Pay for your booking"
msgstr "Bezahlen für Ihre Buchung"

#: includes/emails/booking/customer/direct-bank-transfer-email.php:39
msgid "%site_title% - Pay for your booking #%booking_id%"
msgstr "%site_title% - Bezahlen Sie Ihre Buchung #%booking_id%"

#: includes/emails/booking/customer/direct-bank-transfer-email.php:47
msgid "Payment Instructions Email"
msgstr "Zahlungsanweisungen E-Mail"

#: includes/emails/booking/customer/pending-email.php:8
msgid "Your booking is placed"
msgstr "Ihre Buchung wurde empfangen"

#: includes/emails/booking/customer/pending-email.php:12
msgid "%site_title% - Booking #%booking_id% is placed"
msgstr "%site_title%  - Buchung #%booking_id% wird platziert"

#: includes/emails/booking/customer/pending-email.php:16
msgid "This email is sent when \"Booking Confirmation Mode\" is set to Admin confirmation."
msgstr "Diese E-Mail wird gesendet, wenn \"Buchungsbestätigungsmodus\" von Administrator-Bestätigung abhängig ist."

#: includes/emails/booking/customer/pending-email.php:21
msgid "New Booking Email (Confirmation by Admin)"
msgstr "Neue Buchungs-E-Mail (Bestätigung durch Administrator)"

#: includes/emails/booking/customer/registration-email.php:8
msgid "Welcome"
msgstr "Herzlich willkommen"

#: includes/emails/booking/customer/registration-email.php:12
msgid "%site_title% - account details"
msgstr "%site_title% – Kontodetails"

#: includes/emails/booking/customer/registration-email.php:16
msgid "Email that will be sent to a customer after they registered on your site."
msgstr "Diese E-Mail wird an die Kunden gesendet, nachdem sie sich auf Ihrer Website registriert haben."

#: includes/emails/booking/customer/registration-email.php:20
msgid "Customer Registration Email"
msgstr "E-Mail zur Kundenregistrierung"

#: includes/emails/templaters/abstract-templater.php:77
msgid "Email Tags"
msgstr "Email Tags"

#: includes/emails/templaters/abstract-templater.php:87
#: includes/emails/templaters/abstract-templater.php:89
msgid "Deprecated."
msgstr "Veraltet."

#: includes/emails/templaters/abstract-templater.php:101
msgid "none"
msgstr "Kein"

#: includes/emails/templaters/cancellation-booking-templater.php:50
msgid "User Cancellation Link"
msgstr "Stornierungslink des Benutzers"

#: includes/emails/templaters/email-templater.php:109
msgid "Site title (set in Settings > General)"
msgstr "Site-Titel (in Einstellungen> Allgemein)"

#: includes/emails/templaters/email-templater.php:124
#: includes/post-types/payment-cpt.php:232
msgid "Booking ID"
msgstr "Buchung-ID"

#: includes/emails/templaters/email-templater.php:128
msgid "Booking Edit Link"
msgstr "Buchung-Bearbeitungslink"

#: includes/emails/templaters/email-templater.php:132
msgid "Booking Total Price"
msgstr "Gesamtpreis der Buchung"

#: includes/emails/templaters/email-templater.php:153
#: includes/emails/templaters/email-templater.php:296
msgid "Customer First Name"
msgstr "Vorname des Kunden"

#: includes/emails/templaters/email-templater.php:157
#: includes/emails/templaters/email-templater.php:300
msgid "Customer Last Name"
msgstr "Nachname des Kunden"

#: includes/emails/templaters/email-templater.php:161
#: includes/emails/templaters/email-templater.php:304
msgid "Customer Email"
msgstr "E-Mail des Kunden"

#: includes/emails/templaters/email-templater.php:165
#: includes/emails/templaters/email-templater.php:308
msgid "Customer Phone"
msgstr "Telefon des Kunden"

#: includes/emails/templaters/email-templater.php:169
#: includes/emails/templaters/email-templater.php:312
msgid "Customer Country"
msgstr "Land (Kunde)"

#: includes/emails/templaters/email-templater.php:173
#: includes/emails/templaters/email-templater.php:316
msgid "Customer Address"
msgstr "Adresse (Kunde)"

#: includes/emails/templaters/email-templater.php:177
#: includes/emails/templaters/email-templater.php:320
msgid "Customer City"
msgstr "Stadt (Kunde)"

#: includes/emails/templaters/email-templater.php:181
#: includes/emails/templaters/email-templater.php:324
msgid "Customer State/County"
msgstr "Bundesland Kunde"

#: includes/emails/templaters/email-templater.php:185
#: includes/emails/templaters/email-templater.php:328
msgid "Customer Postcode"
msgstr "Postleitzahl (PLZ) Kunde"

#: includes/emails/templaters/email-templater.php:194
msgid "Reserved Accommodations Details"
msgstr "Details von reservierten Unterkünften"

#: includes/emails/templaters/email-templater.php:216
#: includes/views/create-booking/checkout-view.php:15
#: includes/views/shortcodes/checkout-view.php:164
#: templates/shortcodes/booking-details/booking-details.php:18
msgid "Booking Details"
msgstr "Buchungsdetails"

#: includes/emails/templaters/email-templater.php:230
msgid "Confirmation Link"
msgstr "Bestätigungslink"

#: includes/emails/templaters/email-templater.php:234
msgid "Confirmation Link Expiration Time ( UTC )"
msgstr "Bestätigungslink Verfallzeit (UTC)"

#: includes/emails/templaters/email-templater.php:248
msgid "Cancellation Details (if enabled)"
msgstr "Stornierungsdetails (falls aktiviert)"

#: includes/emails/templaters/email-templater.php:262
msgid "The total amount of payment"
msgstr "Der Gesamtpreis der Zahlung"

#: includes/emails/templaters/email-templater.php:266
msgid "The unique ID of payment"
msgstr "Persönliche Zahlung-ID"

#: includes/emails/templaters/email-templater.php:270
msgid "The method of payment"
msgstr "Zahlungsmethode"

#: includes/emails/templaters/email-templater.php:274
msgid "Payment instructions"
msgstr "Anleitung für Bezahlung"

#: includes/emails/templaters/email-templater.php:288
msgid "User login"
msgstr "Benutzername"

#: includes/emails/templaters/email-templater.php:292
msgid "User password"
msgstr "Benutzerpasswort"

#: includes/emails/templaters/email-templater.php:332
msgid "Link to My Account page"
msgstr "Link zur Seite Mein Konto"

#: includes/emails/templaters/email-templater.php:562
#: includes/upgrader.php:868
#: includes/wizard.php:213
msgid "My Account"
msgstr "Mein Konto"

#: includes/emails/templaters/reserved-rooms-templater.php:191
msgid "Accommodation Type Link"
msgstr "Unterkunftsart-Link"

#: includes/emails/templaters/reserved-rooms-templater.php:195
msgid "Accommodation Type Title"
msgstr "Unterkunftsart-Titel"

#: includes/emails/templaters/reserved-rooms-templater.php:199
msgid "Accommodation Title"
msgstr "Titel der Unterkunft"

#: includes/emails/templaters/reserved-rooms-templater.php:203
msgid "Accommodation Type Categories"
msgstr "Unterkunftsart-Kategorien"

#: includes/emails/templaters/reserved-rooms-templater.php:207
msgid "Accommodation Type Bed"
msgstr "Unterkunftsart-Bett"

#: includes/emails/templaters/reserved-rooms-templater.php:211
msgid "Accommodation Rate Title"
msgstr "Unterkunft-Rate-Titel"

#: includes/emails/templaters/reserved-rooms-templater.php:215
msgid "Accommodation Rate Description"
msgstr "Unterkunft-Rate-Beschreibung"

#: includes/emails/templaters/reserved-rooms-templater.php:219
msgid "Sequential Number of Accommodation"
msgstr "Sequentielle Anzahl der Unterkunft"

#: includes/entities/coupon.php:370
msgid "This coupon has expired."
msgstr "Dieser Gutschein ist abgelaufen."

#: includes/entities/coupon.php:374
msgid "Sorry, this coupon is not applicable to your booking contents."
msgstr "Entschuldigung, dieser Gutschein ist nicht auf Ihre Buchungsinhalte anwendbar."

#: includes/entities/coupon.php:378
msgid "Coupon usage limit has been reached."
msgstr "Der Nutzungslimit dieses Gutscheines wurde erreicht."

#: includes/entities/reserved-service.php:98
msgid " &#215; %d night"
msgid_plural " &#215; %d nights"
msgstr[0] " &#215; %d Nacht"
msgstr[1] " &#215; %d Nächte"

#: includes/entities/reserved-service.php:103
#: includes/shortcodes/search-results-shortcode.php:904
msgid "%d adult"
msgid_plural "%d adults"
msgstr[0] "%d Erwachsener"
msgstr[1] "%d Erwachsene"

#: includes/entities/reserved-service.php:105
#: includes/shortcodes/search-results-shortcode.php:896
#: includes/shortcodes/search-results-shortcode.php:900
msgid "%d guest"
msgid_plural "%d guests"
msgstr[0] "%d Gast"
msgstr[1] "%d Gäste"

#: includes/entities/reserved-service.php:110
msgid " &#215; %d time"
msgid_plural " &#215; %d times"
msgstr[0] " &#215; %d Mal"
msgstr[1] " &#215; %d Mal"

#: includes/entities/service.php:195
msgid "Per Instance"
msgstr "Pro Instanz"

#: includes/i-cal/background-processes/background-synchronizer.php:34
msgid "Maximum execution time is set to %d seconds."
msgstr "Die maximale Ausführungszeit ist auf %d Sekunden festgelegt."

#: includes/i-cal/background-processes/background-synchronizer.php:80
msgid "%d URL pulled for parsing."
msgid_plural "%d URLs pulled for parsing."
msgstr[0] "%d URL zum Parsen gewählt."
msgstr[1] "%d URLs zum Parsen gewählt."

#: includes/i-cal/background-processes/background-synchronizer.php:82
msgid "Skipped. No URLs found for parsing."
msgstr "Übersprungen. Keine URLs zum Parsen gefunden."

#: includes/i-cal/background-processes/background-uploader.php:64
msgid "Cannot read uploaded file"
msgstr "Hochgeladene Datei kann nicht gelesen werden"

#: includes/i-cal/background-processes/background-worker.php:327
msgctxt "%s - calendar URI or calendar filename"
msgid "%1$d event found in calendar %2$s"
msgid_plural "%1$d events found in calendar %2$s"
msgstr[0] "%1$d Ereignis im Kalender %2$s gefunden"
msgstr[1] "%1$d Ereignisse im Kalender %2$s gefunden"

#: includes/i-cal/background-processes/background-worker.php:357
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar source is empty (%s)"
msgstr "Die Kalenderquelle ist leer (%s)"

#: includes/i-cal/background-processes/background-worker.php:370
msgctxt "%s - calendar URI or calendar filename"
msgid "Calendar file is not empty, but there are no events in %s"
msgstr "Die Kalenderdatei ist nicht leer, aber es gibt keine Ereignisse in %s"

#: includes/i-cal/background-processes/background-worker.php:403
msgid "We will need to check %d previous booking after importing and remove it if the booking is outdated."
msgid_plural "We will need to check %d previous bookings after importing and remove the outdated ones."
msgstr[0] "Wir müssen %d vorherige Buchung nach dem Import überprüfen und löschen, wenn die Buchung veraltet ist."
msgstr[1] "Wir müssen %d vorherige Buchungen nach dem Import überprüfen und die veraltete löschen ."

#: includes/i-cal/background-processes/background-worker.php:425
msgid "Error while loading calendar (%1$s): %2$s"
msgstr "Fehler beim Laden des Kalenders (%1$s): %2$s"

#: includes/i-cal/background-processes/background-worker.php:427
msgctxt "%s - error description"
msgid "Parse error. %s"
msgstr "Fehler beim Parsen. %s"

#: includes/i-cal/background-processes/background-worker.php:468
msgid "Skipped. Outdated booking #%d already removed."
msgstr "Übersprungen. Veraltete Buchung #%d ist bereits gelöscht."

#: includes/i-cal/background-processes/background-worker.php:475
msgid "Skipped. Booking #%d updated with new data."
msgstr "Übersprungen. Buchung #%d ist mit neuen Daten aktualisiert."

#: includes/i-cal/background-processes/background-worker.php:497
msgid "The outdated booking #%d has been removed."
msgstr "Die veraltete Buchung #%d ist gelöscht."

#: includes/i-cal/importer.php:104
msgid "Skipped. Event from %1$s to %2$s has passed."
msgstr "Übersprungen. Ereignis von %1$s bis %2$s ist vorbei."

#: includes/i-cal/importer.php:120
msgid "New booking #%1$d. The dates from %2$s to %3$s are now blocked."
msgstr "Neue Buchung #%1$d. Die Daten von %2$s bis %3$s sind jetzt blockiert."

#: includes/i-cal/importer.php:140
msgid "Success. Booking #%d updated with new data."
msgstr "Erfolg. Buchung #%d ist mit neuen Daten aktualisiert."

#: includes/i-cal/importer.php:148
msgid "Skipped. The dates from %1$s to %2$s are already blocked."
msgstr "Übersprungen. Die Daten von %1$s bis %2$s sind bereits gesperrt."

#: includes/i-cal/importer.php:164
msgid "Success. Booking #%1$d updated with new data. Removed %2$d outdated booking."
msgid_plural "Success. Booking #%1$d updated with new data. Removed %2$d outdated bookings."
msgstr[0] "Erfolg. Buchung #%1$d ist mit neuen Daten aktualisiert. %2$d veraltete Buchung ist gelöscht."
msgstr[1] "Erfolg. Buchung #%1$d ist mit neuen Daten aktualisiert. %2$d veraltete Buchungen sind gelöscht."

#: includes/i-cal/importer.php:166
msgid "Success. Booking #%1$d updated with new data."
msgstr "Erfolg. Buchung #%1$d ist mit neuen Daten aktualisiert."

#: includes/i-cal/importer.php:177
msgid "Cannot import new event. Dates from %1$s to %2$s are partially blocked by booking %3$s."
msgid_plural "Cannot import new event. Dates from %1$s to %2$s are partially blocked by bookings %3$s."
msgstr[0] "Neues Ereignis kann nicht importiert werden. Daten von %1$s bis %2$s sind teilweise durch die Buchung von %3$s gesperrt."
msgstr[1] "Neues Ereignis kann nicht importiert werden. Daten von %1$s bis %2$s sind teilweise durch Buchungen %3$s gesperrt."

#: includes/i-cal/importer.php:233
msgid "Booking imported with UID %1$s.<br />Summary: %2$s.<br />Description: %3$s.<br />Source: %4$s."
msgstr "Buchung wurde mit UID importiert %1$s.<br />Überblick: %2$s.<br />Beschreibung: %3$s.<br />Quelle: %4$s."

#: includes/i-cal/logs-handler.php:25
msgid "Process Information"
msgstr "Prozessinformationen"

#: includes/i-cal/logs-handler.php:35
msgid "Total bookings: %s"
msgstr "Gesamte Buchungen: %s"

#: includes/i-cal/logs-handler.php:37
msgid "Success bookings: %s"
msgstr "Erfolgreiche Buchungen: %s"

#: includes/i-cal/logs-handler.php:39
msgid "Skipped bookings: %s"
msgstr "Übersprungene Buchungen: %s"

#: includes/i-cal/logs-handler.php:41
msgid "Failed bookings: %s"
msgstr "Fehlgeschlagene Buchungen: %s"

#: includes/i-cal/logs-handler.php:43
msgid "Removed bookings: %s"
msgstr "Gelöschte Buchungen: %s"

#: includes/i-cal/logs-handler.php:87
msgid "Expand All"
msgstr "Alles aufklappen"

#: includes/i-cal/logs-handler.php:91
msgid "Collapse All"
msgstr "Alles zuklappen"

#: includes/i-cal/logs-handler.php:138
msgid "All done! %1$d booking was successfully added."
msgid_plural "All done! %1$d bookings were successfully added."
msgstr[0] "Fertig! %1$d Buchung wurde erfolgreich hinzugefügt."
msgstr[1] "Fertig! %1$d Buchungen wurden erfolgreich hinzugefügt."

#: includes/i-cal/logs-handler.php:139
msgid " There was %2$d failure."
msgid_plural " There were %2$d failures."
msgstr[0] " Es gab %2$d Fehler."
msgstr[1] " Es gab %2$d Fehler."

#: includes/license-notice.php:87
#: includes/license-notice.php:160
msgid "Your License Key is not active. Please, <a href=\"%s\">activate your License Key</a> to get plugin updates."
msgstr "Ihr Lizenzschlüssel ist nicht aktiv. Bitte, <a href='%s'>aktivieren Sie Ihren Lizenzschlüssel</a>, um Plugin-Updates zu erhalten."

#: includes/license-notice.php:152
msgid "Dismiss "
msgstr "Verwerfen "

#: includes/linked-rooms.php:31
msgid "Blocked because the linked accommodation is booked"
msgstr "Gesperrt, weil die verlinkte Unterkunft gebucht wurde"

#: includes/notices.php:138
#: includes/notices.php:156
#: includes/wizard.php:33
msgid "Hotel Booking Plugin"
msgstr "Plugin für Hotelbuchung"

#: includes/notices.php:139
msgid "Your database is being updated in the background."
msgstr "Ihre Datenbank wird im Hintergrund aktualisiert."

#: includes/notices.php:141
msgid "Taking a while? Click here to run it now."
msgstr "Dauert es zu lange? Klicken Sie hier, um es jetzt zu starten."

#: includes/notices.php:157
msgid "Add \"Booking Confirmation\" shortcode to your \"Booking Confirmed\" and \"Reservation Received\" pages to show more details about booking or payment.<br/>Click \"Update Pages\" to apply all changes automatically or skip this notice and add \"Booking Confirmation\" shortcode manually.<br/><b><em>This action will replace the whole content of the pages.</em></b>"
msgstr "Fügen Sie den Shortcode \"Buchungsbestätigung\" zu Ihren Seiten \"Buchungsbestätigung\" und \"Reservierung erhalten\" hinzu, um weitere Details zu Buchung oder Zahlung anzuzeigen.<br/>Klicken Sie auf \"Seiten aktualisieren\", um alle Änderungen automatisch zu übernehmen oder überspringen Sie diesen Hinweis und fügen Sie den Shortcode \"Buchungsbestätigung\" manuell hinzu.<br/><b><em>Diese Aktion ersetzt den gesamten Inhalt der Seiten.</em></b>"

#: includes/notices.php:159
msgid "Update Pages"
msgstr "Seiten aktualisieren"

#: includes/notices.php:161
#: includes/wizard.php:36
msgid "Skip"
msgstr "Überspringen"

#: includes/payments/gateways/bank-gateway.php:82
#: includes/payments/gateways/bank-gateway.php:91
msgid "Direct Bank Transfer"
msgstr "Direktüberweisung"

#: includes/payments/gateways/bank-gateway.php:92
msgid "Make your payment directly into our bank account. Please use your Booking ID as the payment reference."
msgstr "Zahlen Sie direkt auf unser Bankkonto ein. Bitte verwenden Sie Ihre Buchungsnummer als Zahlungsreferenz."

#: includes/payments/gateways/bank-gateway.php:118
msgid "Enable Auto-Abandonment"
msgstr "Auto-Verzicht aktivieren"

#: includes/payments/gateways/bank-gateway.php:119
msgid "Automatically abandon bookings and release reserved slots if payment is not received within a specified time period. You need to manually set the status of paid payments to Completed to avoid automatic abandonment."
msgstr "Buchungen automatisch aufgeben und reservierte Slots freigeben, wenn die Zahlung innerhalb einer bestimmten Frist nicht eingegangen ist. Sie müssen den Status der bezahlten Zahlungen manuell auf Abgeschlossene festlegen, um eine automatische Aufgabe zu vermeiden."

#: includes/payments/gateways/bank-gateway.php:128
msgid "Period of time in hours a user has to pay for a booking. Unpaid bookings become abandoned, and accommodations become available for others."
msgstr "Zeitspanne in Stunden, die ein Nutzer für eine Buchung bezahlen muss, unbezahlte Buchungen werden aufgegeben und andere Unterkünfte stehen zur Verfügung."

#: includes/payments/gateways/beanstream-gateway.php:54
msgid "Beanstream/Bambora"
msgstr "Beanstream/Bambora"

#: includes/payments/gateways/beanstream-gateway.php:59
#: includes/payments/gateways/braintree-gateway.php:149
#: includes/payments/gateways/paypal-gateway.php:72
#: includes/payments/gateways/two-checkout-gateway.php:70
msgid "Use the card number %1$s with CVC %2$s and a valid expiration date to test a payment."
msgstr "Verwenden Sie die Kartennummer %1$s mit CVC %2$s und ein gültiges Ablaufdatum, um eine Zahlung zu testen."

#: includes/payments/gateways/beanstream-gateway.php:66
msgid "Pay by Card (Beanstream)"
msgstr "Bezahlen per Karte (Beanstream)"

#: includes/payments/gateways/beanstream-gateway.php:67
msgid "Pay with your credit card via Beanstream."
msgstr "Bezahlen Sie mit Ihrer Kreditkarte über Beanstream."

#: includes/payments/gateways/beanstream-gateway.php:85
#: includes/payments/gateways/braintree-gateway.php:194
#: includes/payments/gateways/stripe-gateway.php:226
msgid "%1$s is enabled, but the <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "%1$s ist aktiviert, aber die Option <a href=\"%2$s\">Sicherer Buchungsvorgang</a> ist deaktiviert. Bitte aktivieren Sie SSL und stellen Sie sicher, dass Ihr Server über ein gültiges SSL-Zertifikat verfügt. Andernfalls wird %1$s nur im Testmodus funktionieren."

#: includes/payments/gateways/beanstream-gateway.php:87
#: includes/payments/gateways/braintree-gateway.php:196
#: includes/payments/gateways/stripe-gateway.php:228
msgid "The <a href=\"%2$s\">Force Secure Checkout</a> option is disabled. Please enable SSL and ensure your server has a valid SSL certificate. Otherwise, %1$s will only work in Test Mode."
msgstr "Stripe ist aktiviert, aber die Option <a href=\"%2$s\">Sicherer Buchungsvorgang</a> ist deaktiviert. Bitte aktivieren Sie SSL und stellen Sie sicher, dass Ihr Server über ein gültiges SSL-Zertifikat verfügt. Andernfalls funktioniert %1$s nur im Testmodus."

#: includes/payments/gateways/beanstream-gateway.php:90
msgid "Beanstream"
msgstr "Beanstream"

#: includes/payments/gateways/beanstream-gateway.php:103
#: includes/payments/gateways/braintree-gateway.php:212
msgid "Merchant ID"
msgstr "Händler-ID"

#: includes/payments/gateways/beanstream-gateway.php:105
msgid "Your Merchant ID can be found in the top-right corner of the screen after logging in to the Beanstream Back Office"
msgstr "Ihre Händler-ID kann in der oberen rechten Ecke des Bildschirms nach der Anmeldung im Beanstream Back Office gefunden werden"

#: includes/payments/gateways/beanstream-gateway.php:112
msgid "Payments Passcode"
msgstr "Zahlungen-Passcode"

#: includes/payments/gateways/beanstream-gateway.php:114
msgid "To generate the passcode, navigate to Administration > Account Settings > Order Settings in the sidebar, then scroll to Payment Gateway > Security/Authentication"
msgstr "Um den Passcode zu generieren, navigieren Sie zu Administration> Kontoeinstellungen> Auftragseinstellungen in der Seitenleiste und scrollen Sie dann zu Payment Gateway> Sicherheit / Authentifizierung"

#: includes/payments/gateways/beanstream-gateway.php:163
msgid "Beanstream Payment Error: %s"
msgstr "Beanstream Zahlungsfehler: %s"

#: includes/payments/gateways/beanstream-gateway.php:201
msgid "Payment single use token is required."
msgstr "Token-Einzelverwendung Zahlung ist erforderlich."

#: includes/payments/gateways/braintree-gateway.php:142
#: includes/payments/gateways/braintree-gateway.php:199
msgid "Braintree"
msgstr "Braintree"

#: includes/payments/gateways/braintree-gateway.php:155
#: includes/payments/gateways/stripe-gateway.php:86
msgid "Webhooks Destination URL: %s"
msgstr "Webhooks URL-Ziel: %s"

#: includes/payments/gateways/braintree-gateway.php:168
msgid "Pay by Card (Braintree)"
msgstr "Bezahlen per Karte (Braintree)"

#: includes/payments/gateways/braintree-gateway.php:169
msgid "Pay with your credit card via Braintree."
msgstr "Bezahlen Sie mit Ihrer Kreditkarte über Braintree."

#: includes/payments/gateways/braintree-gateway.php:189
msgid "Braintree gateway cannot be enabled due to some problems: %s"
msgstr "Braintree Gateway kann aufgrund einiger Probleme nicht aktiviert werden: %s"

#: includes/payments/gateways/braintree-gateway.php:214
msgid "In your Braintree account select Account > My User > View Authorizations."
msgstr "Wählen Sie in Ihrem Braintree-Konto Konto> Mein Benutzer> Berechtigungen anzeigen."

#: includes/payments/gateways/braintree-gateway.php:221
#: includes/payments/gateways/stripe-gateway.php:284
msgid "Public Key"
msgstr "Öffentlicher Schlüssel"

#: includes/payments/gateways/braintree-gateway.php:229
msgid "Private Key"
msgstr "Privatschlüssel"

#: includes/payments/gateways/braintree-gateway.php:237
msgid "Merchant Account ID"
msgstr "Händler-Konto-ID"

#: includes/payments/gateways/braintree-gateway.php:238
msgid "In case the site currency differs from default currency in your Braintree account, you can set specific merchant account to avoid <a href=\"%s\">complications with currencty conversions</a>. Otherwise leave the field empty."
msgstr "Falls die Standortwährung von der Standardwährung in Ihrem Braintree-Konto abweicht, können Sie ein bestimmtes Händlerkonto festlegen, um <a href=\"%s\">Komplikationen mit Währungsumwandlungen zu vermeiden</a>. Andernfalls lassen Sie das Feld leer."

#: includes/payments/gateways/braintree-gateway.php:293
msgid "Braintree submitted for settlement (Transaction ID: %s)"
msgstr "Braintree zur Abrechnung eingereicht (Transaction ID: %s)"

#: includes/payments/gateways/braintree-gateway.php:303
msgid "Braintree Payment Error: %s"
msgstr "Braintree Zahlungsfehler: %s"

#: includes/payments/gateways/braintree-gateway.php:330
msgid "Payment method nonce is required."
msgstr "Zahlungsmethode Nonce ist erforderlich."

#: includes/payments/gateways/braintree/webhook-listener.php:116
msgid "Payment dispute opened"
msgstr "Zahlungsstreit eröffnet"

#: includes/payments/gateways/braintree/webhook-listener.php:121
msgid "Payment dispute lost"
msgstr "Zahlungsstreit verloren"

#: includes/payments/gateways/braintree/webhook-listener.php:126
msgid "Payment dispute won"
msgstr "Zahlungsstreit gewonnen"

#: includes/payments/gateways/braintree/webhook-listener.php:143
msgid "Payment refunded in Braintree"
msgstr "Zahlung zurückerstattet in Braintree"

#: includes/payments/gateways/braintree/webhook-listener.php:147
msgid "Braintree transaction voided"
msgstr "Braintree Transaktion für ungültig erklärt"

#: includes/payments/gateways/cash-gateway.php:45
#: includes/payments/gateways/cash-gateway.php:53
msgid "Pay on Arrival"
msgstr "Bei Ankunft bezahlen"

#: includes/payments/gateways/cash-gateway.php:54
msgid "Pay with cash on arrival."
msgstr "Bei Ankunft bar bezahlen."

#: includes/payments/gateways/gateway.php:301
msgid "%s is a required field."
msgstr "%s ist ein Pflichtfeld."

#: includes/payments/gateways/gateway.php:314
msgid "%s is not a valid email address."
msgstr "%s ist keine gültige E-Mail-Adresse."

#. translators: %s is the payment gateway title.
#: includes/payments/gateways/gateway.php:472
msgid "Enable \"%s\""
msgstr "\"%s\" aktivieren"

#: includes/payments/gateways/gateway.php:482
msgid "Test Mode"
msgstr "Test-Modus"

#: includes/payments/gateways/gateway.php:483
msgid "Enable Sandbox Mode"
msgstr "Sandbox-Modus aktivieren"

#: includes/payments/gateways/gateway.php:485
msgid "Sandbox can be used to test payments."
msgstr "Sandbox kann verwendet werden, um Zahlungen zu testen."

#: includes/payments/gateways/gateway.php:496
msgid "Payment method title that the customer will see on your website."
msgstr "Zahlungsmethode, die der Kunde auf Ihrer Website sehen wird."

#: includes/payments/gateways/gateway.php:506
msgid "Payment method description that the customer will see on your website."
msgstr "Zahlungsmethodenbeschreibung, die der Kunde auf Ihrer Website sehen wird."

#: includes/payments/gateways/gateway.php:516
msgid "Instructions"
msgstr "Anleitung"

#: includes/payments/gateways/gateway.php:518
msgid "Instructions for a customer on how to complete the payment."
msgstr "Anleitung für den Kunden um die Zahlung abzuschließen."

#: includes/payments/gateways/gateway.php:543
msgid "Reservation #%d"
msgstr "Reservierung #%d"

#: includes/payments/gateways/gateway.php:545
msgid "Accommodation(s) reservation"
msgstr "Reservierung von Unterkunft (Unterkünften)"

#: includes/payments/gateways/manual-gateway.php:14
#: includes/payments/gateways/manual-gateway.php:19
msgid "Manual Payment"
msgstr "Manuelle Zahlung"

#: includes/payments/gateways/paypal-gateway.php:67
#: includes/payments/gateways/paypal-gateway.php:80
msgid "PayPal"
msgstr "PayPal"

#: includes/payments/gateways/paypal-gateway.php:81
msgid "Pay via PayPal"
msgstr "Zahlen mit PayPal"

#: includes/payments/gateways/paypal-gateway.php:117
msgid "Paypal Business Email"
msgstr "Paypal Business E-Mail"

#: includes/payments/gateways/paypal-gateway.php:125
msgid "Disable IPN Verification"
msgstr "IPN-Überprüfung deaktivieren"

#: includes/payments/gateways/paypal-gateway.php:127
msgid "Specify an IPN listener for a specific payment instead of the listeners specified in your PayPal Profile."
msgstr "Geben Sie einen IPN-Listener für eine bestimmte Zahlung anstelle der in Ihrem PayPal-Profil angegebenen Zuhörer an."

#: includes/payments/gateways/paypal/ipn-listener.php:164
msgid "Payment %s via IPN."
msgstr "Zahlung %s über IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:183
msgid "Payment failed due to invalid PayPal business email."
msgstr "Die Zahlung fehlgeschlagen aufgrund der ungültigen PayPal-Business-E-Mail."

#: includes/payments/gateways/paypal/ipn-listener.php:200
msgid "Payment failed due to invalid currency in PayPal IPN."
msgstr "Zahlung fehlgeschlagen aufgrund ungültiger Währung in PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:215
msgid "Payment failed due to invalid amount in PayPal IPN."
msgstr "Zahlung fehlgeschlagen aufgrund eines ungültigen Betrags in PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:230
msgid "Payment failed due to invalid purchase key in PayPal IPN."
msgstr "Zahlung fehlgeschlagen aufgrund eines ungültigen Kaufschlüssels in PayPal IPN."

#: includes/payments/gateways/paypal/ipn-listener.php:302
msgid "Payment made via eCheck and will clear automatically in 5-8 days."
msgstr "Zahlung erfolgt per eCheck und wird in 5-8 Tagen automatisch gelöscht."

#: includes/payments/gateways/paypal/ipn-listener.php:307
msgid "Payment requires a confirmed customer address and must be accepted manually through PayPal."
msgstr "Zahlung erfordert eine bestätigte Kundenadresse und muss manuell über PayPal akzeptiert werden."

#: includes/payments/gateways/paypal/ipn-listener.php:312
msgid "Payment must be accepted manually through PayPal due to international account regulations."
msgstr "Zahlung muss manuell über PayPal akzeptiert werden."

#: includes/payments/gateways/paypal/ipn-listener.php:317
msgid "Payment received in non-shop currency and must be accepted manually through PayPal."
msgstr "Zahlung in Nicht-Ladenwährung eingegangen und muss manuell über PayPal akzeptiert werden."

#: includes/payments/gateways/paypal/ipn-listener.php:323
msgid "Payment is being reviewed by PayPal staff as high-risk or in possible violation of government regulations."
msgstr "Zahlung wird von PayPal-Mitarbeitern als Hochrisiko oder in einer möglichen Verletzung von Regierungsvorschriften verdächtigt und wird überprüft."

#: includes/payments/gateways/paypal/ipn-listener.php:328
msgid "Payment was sent to unconfirmed or non-registered email address."
msgstr "Zahlung wurde an nicht bestätigte oder nicht registrierte E-Mail-Adresse gesendet."

#: includes/payments/gateways/paypal/ipn-listener.php:333
msgid "PayPal account must be upgraded before this payment can be accepted."
msgstr "PayPal-Konto muss aktualisiert werden, bevor diese Zahlung akzeptiert werden kann."

#: includes/payments/gateways/paypal/ipn-listener.php:338
msgid "PayPal account is not verified. Verify account in order to accept this payment."
msgstr "PayPal-Konto wurde nicht überprüft. Überprüfen Sie das Konto, um diese Zahlung zu akzeptieren."

#: includes/payments/gateways/paypal/ipn-listener.php:343
msgid "Payment is pending for unknown reasons. Contact PayPal support for assistance."
msgstr "Zahlung ist aus unbekannten Gründen ausstehend. Wenden Sie sich an den PayPal-Support."

#: includes/payments/gateways/paypal/ipn-listener.php:363
msgid "Partial PayPal refund processed: %s"
msgstr "Teilweise PayPal-Rückerstattung verarbeitet: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:370
msgid "PayPal Payment #%s Refunded for reason: %s"
msgstr "PayPal Zahlung #%s Rückerstattung für Grund: %s"

#: includes/payments/gateways/paypal/ipn-listener.php:374
msgid "PayPal Refund Transaction ID: %s"
msgstr "PayPal Rückerstattung Transaktion-ID: %s"

#: includes/payments/gateways/stripe-gateway.php:149
#: includes/payments/gateways/stripe-gateway.php:231
msgid "Stripe"
msgstr "Stripe"

#: includes/payments/gateways/stripe-gateway.php:186
msgid "Use the card number %1$s with CVC %2$s, a valid expiration date and random 5-digit ZIP-code to test a payment."
msgstr "Verwenden Sie die Kartennummer %1$s mit CVC %2$s, ein gültiges Ablaufdatum und eine zufällige 5-stellige Postleitzahl, um eine Zahlung zu testen."

#: includes/payments/gateways/stripe-gateway.php:202
msgid "Pay by Card (Stripe)"
msgstr "Bezahlen mit Karte (Stripe)"

#: includes/payments/gateways/stripe-gateway.php:203
msgid "Pay with your credit card via Stripe."
msgstr "Bezahlen Sie mit Ihrer Kreditkarte über Stripe."

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:240
#: includes/payments/gateways/stripe-gateway.php:259
#: includes/payments/gateways/stripe-gateway.php:688
msgid "Bancontact"
msgstr "Bankverbindung"

#: includes/payments/gateways/stripe-gateway.php:241
#: includes/payments/gateways/stripe-gateway.php:260
#: includes/payments/gateways/stripe-gateway.php:689
msgid "iDEAL"
msgstr "iDEAL"

#: includes/payments/gateways/stripe-gateway.php:242
#: includes/payments/gateways/stripe-gateway.php:261
#: includes/payments/gateways/stripe-gateway.php:690
msgid "Giropay"
msgstr "Giropay"

#: includes/payments/gateways/stripe-gateway.php:243
#: includes/payments/gateways/stripe-gateway.php:262
#: includes/payments/gateways/stripe-gateway.php:691
msgid "SEPA Direct Debit"
msgstr "SEPA-Lastschrift"

#. translators: %1$s - name of payment method, %2$s - currency codes
#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:244
#: includes/payments/gateways/stripe-gateway.php:269
#: includes/payments/gateways/stripe-gateway.php:276
#: includes/payments/gateways/stripe-gateway.php:692
msgid "Klarna"
msgstr "Klarna"

#. translators: %s - currency codes
#: includes/payments/gateways/stripe-gateway.php:252
msgid "The %s currency is selected in the main settings."
msgstr "Die %s Währung wird in den Haupteinstellungen ausgewählt."

#. translators: %1$s - names of payment methods, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:258
msgid "%1$s support the following currencies: %2$s."
msgstr "%1$s Unterstützt folgende Währungen: %2$s."

#. translators: %1$s - name of payment method, %2$s - currency codes
#: includes/payments/gateways/stripe-gateway.php:268
msgid "%1$s supports: %2$s."
msgstr "%1$s Unterstützung: %2$s."

#. translators: %s - payment method name
#: includes/payments/gateways/stripe-gateway.php:275
msgid "%s special restrictions."
msgstr "%s Sonderbeschränkungen."

#: includes/payments/gateways/stripe-gateway.php:293
msgid "Secret Key"
msgstr "Geheimer Schlüssel"

#: includes/payments/gateways/stripe-gateway.php:301
msgid "Webhook Secret"
msgstr "Webhook geheim"

#: includes/payments/gateways/stripe-gateway.php:310
msgid "Payment Methods"
msgstr "Zahlungsmethoden"

#: includes/payments/gateways/stripe-gateway.php:311
msgid "Card Payments"
msgstr "Kartenzahlungen"

#: includes/payments/gateways/stripe-gateway.php:322
msgid "Checkout Locale"
msgstr "Checkout-Sprache"

#: includes/payments/gateways/stripe-gateway.php:325
msgid "Display Checkout in the user's preferred language, if available."
msgstr "Checkout in der bevorzugten Sprache des Benutzers zeigen, falls vorhanden."

#: includes/payments/gateways/stripe-gateway.php:395
msgid "The payment method is not selected."
msgstr "Die Zahlungsmethode wurde nicht ausgewählt."

#: includes/payments/gateways/stripe-gateway.php:401
msgid "PaymentIntent ID is not set."
msgstr "PaymentIntent ID ist nicht gesetzt."

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:430
msgid "Payment for PaymentIntent %s succeeded."
msgstr "Die Zahlung für PaymentIntent %s war erfolgreich."

#. translators: %s - Stripe PaymentIntent ID
#: includes/payments/gateways/stripe-gateway.php:436
msgid "Payment for PaymentIntent %s is processing."
msgstr "Die Zahlung für PaymentIntent %s wird ausgeführt."

#. translators: %1$s - Stripe PaymentIntent ID, %2$s - Stripe error message text
#: includes/payments/gateways/stripe-gateway.php:457
msgid "Failed to process PaymentIntent %1$s. %2$s"
msgstr "PaymentIntent %1$s konnte nicht verarbeitet werden. %2$s"

#: includes/payments/gateways/stripe-gateway.php:474
msgid "Can't charge the payment again: payment's flow already completed."
msgstr "Zahlungsmittel kann nicht erneut belastet werden: Zahlungsprozess bereits abgeschlossen."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:496
msgid "Charge %s succeeded."
msgstr "Belastung des Zahlungsmittels %s war erfolgreich."

#. translators: %1$s - Stripe Charge ID; %2$s - payment price
#: includes/payments/gateways/stripe-gateway.php:503
msgid "Charge %1$s for %2$s created."
msgstr "Belastung des Zahlungsmittels %1$s für %2$s erstellt."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe-gateway.php:508
msgid "Charge %s failed."
msgstr "Belastung des Zahlungsmittels %s fehlgeschlagen."

#: includes/payments/gateways/stripe-gateway.php:515
msgid "Charge error. %s"
msgstr "Fehler bei Belastung des Zahlungsmittels. %s"

#: includes/payments/gateways/stripe-gateway.php:529
msgid "Argentinean"
msgstr "Argentinisch"

#: includes/payments/gateways/stripe-gateway.php:530
msgid "Simplified Chinese"
msgstr "Vereinfachtes Chinesisch"

#: includes/payments/gateways/stripe-gateway.php:531
msgid "Danish"
msgstr "Dänisch"

#: includes/payments/gateways/stripe-gateway.php:532
msgid "Dutch"
msgstr "Niederländisch"

#: includes/payments/gateways/stripe-gateway.php:533
msgid "English"
msgstr "Englisch"

#: includes/payments/gateways/stripe-gateway.php:534
msgid "Finnish"
msgstr "Finnisch"

#: includes/payments/gateways/stripe-gateway.php:535
msgid "French"
msgstr "Französisch"

#: includes/payments/gateways/stripe-gateway.php:536
msgid "German"
msgstr "Deutsch"

#: includes/payments/gateways/stripe-gateway.php:537
msgid "Italian"
msgstr "Italienisch"

#: includes/payments/gateways/stripe-gateway.php:538
msgid "Japanese"
msgstr "Japanisch"

#: includes/payments/gateways/stripe-gateway.php:539
msgid "Norwegian"
msgstr "Norwegisch"

#: includes/payments/gateways/stripe-gateway.php:540
msgid "Polish"
msgstr "Polnisch"

#: includes/payments/gateways/stripe-gateway.php:541
msgid "Russian"
msgstr "Russisch"

#: includes/payments/gateways/stripe-gateway.php:542
msgid "Spanish"
msgstr "Spanisch"

#: includes/payments/gateways/stripe-gateway.php:543
msgid "Swedish"
msgstr "Schwedisch"

#: includes/payments/gateways/stripe-gateway.php:571
msgid "PaymentIntent ID is missing."
msgstr "PaymentIntent ID fehlt."

#: includes/payments/gateways/stripe-gateway.php:687
msgid "Card"
msgstr "Karte"

#: includes/payments/gateways/stripe-gateway.php:694
msgid "Credit or debit card"
msgstr "Kredit- oder EC-Karte"

#: includes/payments/gateways/stripe-gateway.php:695
msgid "IBAN"
msgstr "IBAN"

#: includes/payments/gateways/stripe-gateway.php:696
msgid "Select iDEAL Bank"
msgstr "iDEAL Bank aussuchen"

#: includes/payments/gateways/stripe-gateway.php:698
msgid "You will be redirected to a secure page to complete the payment."
msgstr "Sie werden auf eine sichere Seite weitergeleitet, um Ihre Zahlung abzuschließen."

#: includes/payments/gateways/stripe-gateway.php:699
msgid "By providing your IBAN and confirming this payment, you are authorizing this merchant and Stripe, our payment service provider, to send instructions to your bank to debit your account and your bank to debit your account in accordance with those instructions. You are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited."
msgstr "Durch die Angabe Ihrer IBAN und die Bestätigung dieser Zahlung ermächtigen Sie diesen Händler und unseren Zahlungsdienstleister Stripe, der Bank Anweisungen zur Belastung Ihres Kontos und der Bank Anweisungen zur Belastung Ihres Kontos gemäß diesen Anweisungen zu senden. Sie haben Anspruch auf eine Rückerstattung von Ihrer Bank gemäß den Bedingungen Ihrer Vereinbarung mit Ihrer Bank. Eine Rückerstattung muss innerhalb von 8 Wochen ab dem Datum, an dem Ihr Konto belastet wurde, beantragt werden."

#. translators: %s - payment method type code like: card
#: includes/payments/gateways/stripe/stripe-api.php:172
msgid "Could not create PaymentIntent for a not allowed payment type: %s"
msgstr "Konnte keinen PaymentIntent für eine nicht zulässige Zahlungsart erstellen: %s"

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:163
msgid "Webhook received. Payment %s was cancelled by the customer."
msgstr "Webhook erhalten. Zahlung %s wurde vom Kunden abgebrochen."

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:174
msgid "Webhook received. Payment %s failed and couldn't be processed."
msgstr "Webhook empfangen. Zahlung %s fehlgeschlagen und konnte nicht verarbeitet werden."

#. translators: %s - Stripe event object ID
#: includes/payments/gateways/stripe/webhook-listener.php:200
msgid "Webhook received. Payment %s was successfully processed."
msgstr "Webhook erhalten. Zahlung %s wurde erfolgreich bearbeitet."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:215
msgid "Webhook received. Charge %s succeeded."
msgstr "Webhook erhalten. Belastung des Zahlungsmittels %s war erfolgreich."

#. translators: %s - Stripe Charge ID
#: includes/payments/gateways/stripe/webhook-listener.php:227
msgid "Webhook received. Charge %s failed."
msgstr "Webhook erhalten. Belastung des Zahlungsmittels %s fehlgeschlagen."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:238
msgid "Webhook received. The source %s is chargeable."
msgstr "Webhook erhalten. Das Zahlungsmittels %s ist belastbar."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:252
msgid "Webhook received. Payment source %s was cancelled by customer."
msgstr "Webhook erhalten. Belastung des Zahlungsmittels %s wurde vom Kunden abgebrochen."

#. translators: %s - Stripe Source ID
#: includes/payments/gateways/stripe/webhook-listener.php:263
msgid "Webhook received. Payment source %s failed and couldn't be processed."
msgstr "Die Zahlungsquelle %s ist fehlgeschlagen und konnte nicht verarbeitet werden."

#: includes/payments/gateways/test-gateway.php:46
#: includes/payments/gateways/test-gateway.php:52
msgid "Test Payment"
msgstr "Testzahlung"

#: includes/payments/gateways/two-checkout-gateway.php:65
#: includes/payments/gateways/two-checkout-gateway.php:101
msgid "2Checkout"
msgstr "2Checkout"

#: includes/payments/gateways/two-checkout-gateway.php:88
msgid "To setup the callback process for 2Checkout to automatically mark payments completed, you will need to"
msgstr "Um den Rückrufprozess für 2Checkout einzurichten und die gezahlten Zahlungen automatisch zu markieren, müssen Sie"

#: includes/payments/gateways/two-checkout-gateway.php:90
msgid "Login to your 2Checkout account and click the Notifications tab"
msgstr "Melden Sie sich bei Ihrem 2Checkout-Konto an und klicken Sie auf die Registerkarte Benachrichtigungen"

#: includes/payments/gateways/two-checkout-gateway.php:91
msgid "Click Enable All Notifications"
msgstr "Klicken Sie auf Alle Benachrichtigungen aktivieren"

#: includes/payments/gateways/two-checkout-gateway.php:92
msgid "In the Global URL field, enter the url %s"
msgstr "Im globalen URL-Feld geben Sie die url %s ein"

#: includes/payments/gateways/two-checkout-gateway.php:93
msgid "Click Apply"
msgstr "Klicken Sie auf Übernehmen"

#: includes/payments/gateways/two-checkout-gateway.php:189
msgid "Account Number"
msgstr "Accountnummer"

#: includes/payments/gateways/two-checkout-gateway.php:197
msgid "Secret Word"
msgstr "Geheimwort"

#: includes/payments/gateways/two-checkout/ins-listener.php:68
msgid "2Checkout \"Order Created\" notification received."
msgstr "2Checkout \"Bestellung angefertigt\" Benachrichtigung erhalten."

#: includes/payments/gateways/two-checkout/ins-listener.php:73
msgid "Payment refunded in 2Checkout"
msgstr "Zahlung zurückerstattet in 2Checkout"

#: includes/payments/gateways/two-checkout/ins-listener.php:80
msgid "2Checkout fraud review passed"
msgstr "2Checkout Betrug-Überprüfung bestanden"

#: includes/payments/gateways/two-checkout/ins-listener.php:83
msgid "2Checkout fraud review failed"
msgstr "2Checkout Betrug-Überprüfung fehlgeschlagen"

#: includes/payments/gateways/two-checkout/ins-listener.php:86
msgid "2Checkout fraud review in progress"
msgstr "2Checkout Betrug-Überprüfung im Gange"

#: includes/post-types/attributes-cpt.php:55
msgid "Attribute"
msgstr "Attribut"

#: includes/post-types/attributes-cpt.php:56
msgctxt "Add New Attribute"
msgid "Add New"
msgstr "Neu hinzufügen"

#: includes/post-types/attributes-cpt.php:57
msgid "Add New Attribute"
msgstr "Neues Attribut hinzufügen"

#: includes/post-types/attributes-cpt.php:58
msgid "Edit Attribute"
msgstr "Attribut bearbeiten"

#: includes/post-types/attributes-cpt.php:59
msgid "New Attribute"
msgstr "Neues Attribut"

#: includes/post-types/attributes-cpt.php:60
msgid "View Attribute"
msgstr "Attribut ansehen"

#: includes/post-types/attributes-cpt.php:62
msgid "Search Attribute"
msgstr "Nach Attribut suchen"

#: includes/post-types/attributes-cpt.php:63
msgid "No Attributes found"
msgstr "Keine Attribute gefunden"

#: includes/post-types/attributes-cpt.php:64
msgid "No Attributes found in Trash"
msgstr "Keine Attribute im Papierkorb gefunden"

#: includes/post-types/attributes-cpt.php:66
msgid "Insert into attribute description"
msgstr "In Beschreibung vom Attribut einfügen"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:157
msgid "Search %s"
msgstr "Nach %s suchen"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:159
msgid "All %s"
msgstr "Alle %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:161
msgid "Edit %s"
msgstr "%s bearbeiten"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:163
msgid "Update %s"
msgstr "%s aktualisieren"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:165
msgid "Add new %s"
msgstr "%s neu hinzufügen"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:167
msgid "New %s"
msgstr "Neuer %s"

#. translators: %s: attribute name
#: includes/post-types/attributes-cpt.php:169
msgid "No &quot;%s&quot; found"
msgstr "&quot;%s&quot; nicht gefunden"

#: includes/post-types/attributes-cpt.php:302
msgid "Name (numeric)"
msgstr "Name (numerisch)"

#: includes/post-types/attributes-cpt.php:303
msgid "Term ID"
msgstr "ID des Begriffs"

#: includes/post-types/attributes-cpt.php:314
msgid "Enable Archives"
msgstr "Archive aktivieren"

#: includes/post-types/attributes-cpt.php:315
msgid "Link the attribute to an archive page with all accommodation types that have this attribute."
msgstr "Das Attribut mit einer Archivseite, die alle Unterkunftsarten mit diesem Attribut enthält, verknüpfen."

#: includes/post-types/attributes-cpt.php:324
msgid "Visible in Details"
msgstr "Im Detailbereich anzeigen"

#: includes/post-types/attributes-cpt.php:325
msgid "Display the attribute in details section of an accommodation type."
msgstr "Das Attribut im Detailbereich einer Unterkunftsart anzeigen."

#: includes/post-types/attributes-cpt.php:334
msgid "Default Sort Order"
msgstr "Standard-Sortierreihenfolge"

#: includes/post-types/attributes-cpt.php:344
msgid "Default Text"
msgstr "Standardtext"

#: includes/post-types/attributes-cpt.php:355
msgid "Select"
msgstr "Wählen"

#: includes/post-types/booking-cpt.php:75
#: templates/edit-booking/edit-dates.php:24
msgid "Edit Dates"
msgstr "Datum bearbeiten"

#: includes/post-types/booking-cpt.php:215
msgid "Note"
msgstr "Notiz"

#: includes/post-types/booking-cpt.php:243
msgctxt "Add New Booking"
msgid "Add New Booking"
msgstr "Neue Buchung hinzufügen"

#: includes/post-types/booking-cpt.php:247
#: templates/emails/admin-customer-cancelled-booking.php:16
#: templates/emails/admin-customer-confirmed-booking.php:16
#: templates/emails/admin-payment-confirmed-booking.php:16
#: templates/emails/admin-pending-booking.php:16
#: templates/emails/customer-approved-booking.php:24
#: templates/emails/customer-pending-booking.php:26
msgid "View Booking"
msgstr "Buchung ansehen"

#: includes/post-types/booking-cpt.php:248
msgid "Search Booking"
msgstr "Nach Buchung suchen"

#: includes/post-types/booking-cpt.php:249
msgid "No bookings found"
msgstr "Keine Buchungen gefunden"

#: includes/post-types/booking-cpt.php:250
msgid "No bookings found in Trash"
msgstr "Keine Buchungen im Papierkorb gefunden"

#: includes/post-types/booking-cpt.php:251
msgid "All Bookings"
msgstr "Alle Buchungen"

#: includes/post-types/booking-cpt.php:252
msgid "Insert into booking description"
msgstr "In Buchungsbeschreibung einfügen"

#: includes/post-types/booking-cpt.php:253
msgid "Uploaded to this booking"
msgstr "Hochgeladen in diese Buchung"

#: includes/post-types/booking-cpt/statuses.php:58
msgctxt "Booking status"
msgid "Pending User Confirmation"
msgstr "Ausstehende Benutzerbestätigung"

#: includes/post-types/booking-cpt/statuses.php:63
msgid "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgid_plural "Pending User Confirmation <span class=\"count\">(%s)</span>"
msgstr[0] "Ausstehende Benutzerbestätigung <span class=\"count\">(%s)</span>"
msgstr[1] "Ausstehende Benutzerbestätigung <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:69
msgctxt "Booking status"
msgid "Pending Payment"
msgstr "Ausstehende Zahlung"

#: includes/post-types/booking-cpt/statuses.php:74
msgid "Pending Payment <span class=\"count\">(%s)</span>"
msgid_plural "Pending Payment <span class=\"count\">(%s)</span>"
msgstr[0] "Ausstehende Zahlung <span class=\"count\">(%s)</span>"
msgstr[1] "Ausstehende Zahlung <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:80
msgctxt "Booking status"
msgid "Pending Admin"
msgstr "Ausstehender Administrator"

#: includes/post-types/booking-cpt/statuses.php:85
msgid "Pending Admin <span class=\"count\">(%s)</span>"
msgid_plural "Pending Admin <span class=\"count\">(%s)</span>"
msgstr[0] "Ausstehender Administrator <span class=\"count\">(%s)</span>"
msgstr[1] "Ausstehender Admin <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:91
#: includes/reports/data/report-earnings-by-dates-data.php:31
msgctxt "Booking status"
msgid "Abandoned"
msgstr "Aufgegeben"

#: includes/post-types/booking-cpt/statuses.php:96
#: includes/post-types/payment-cpt/statuses.php:83
msgid "Abandoned <span class=\"count\">(%s)</span>"
msgid_plural "Abandoned <span class=\"count\">(%s)</span>"
msgstr[0] "Aufgegeben <span class=\"count\">(%s)</span>"
msgstr[1] "Aufgegeben <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:107
msgid "Confirmed <span class=\"count\">(%s)</span>"
msgid_plural "Confirmed <span class=\"count\">(%s)</span>"
msgstr[0] "Bestätigt <span class=\"count\">(%s)</span>"
msgstr[1] "Bestätigt <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:113
#: includes/reports/data/report-earnings-by-dates-data.php:30
msgctxt "Booking status"
msgid "Cancelled"
msgstr "Storniert"

#: includes/post-types/booking-cpt/statuses.php:118
#: includes/post-types/payment-cpt/statuses.php:116
msgid "Cancelled <span class=\"count\">(%s)</span>"
msgid_plural "Cancelled <span class=\"count\">(%s)</span>"
msgstr[0] "Storniert <span class=\"count\">(%s)</span>"
msgstr[1] "Storniert <span class=\"count\">(%s)</span>"

#: includes/post-types/booking-cpt/statuses.php:180
#: includes/post-types/payment-cpt/statuses.php:213
msgid "Status changed from %s to %s."
msgstr "Status wurde von %s auf  %s geändert."

#: includes/post-types/coupon-cpt.php:45
msgid "A brief description to remind you what this code is for."
msgstr "Eine kurze Beschreibung, um Sie daran zu erinnern, wofür dieser Code ist."

#: includes/post-types/coupon-cpt.php:52
msgid "Conditions"
msgstr "Konditionen"

#: includes/post-types/coupon-cpt.php:60
msgid "Apply a coupon code to selected accommodations in a booking. Leave blank to apply to all accommodations."
msgstr "Wenden Sie einen Gutscheincode auf ausgewählte Unterkünfte in einer Buchung an. Lassen Sie das Feld leer um sich für alle Unterkünfte zu bewerben."

#: includes/post-types/coupon-cpt.php:82
msgid "Percentage discount on accommodation price"
msgstr "Prozentualer Rabatt auf den Übernachtungspreis"

#: includes/post-types/coupon-cpt.php:83
msgid "Fixed discount on accommodation price"
msgstr "Fester Rabatt auf den Übernachtungspreis"

#: includes/post-types/coupon-cpt.php:84
msgid "Fixed discount on daily/nightly price"
msgstr "Fester Rabatt auf täglich/nachts Preis"

#: includes/post-types/coupon-cpt.php:94
#: includes/post-types/coupon-cpt.php:125
#: includes/post-types/coupon-cpt.php:170
msgid "Enter percent or fixed amount according to selected type."
msgstr "Geben Sie einen Prozentsatz oder einen festen Betrag je nach ausgewähltem Typ ein."

#: includes/post-types/coupon-cpt.php:104
msgid "Service Discount"
msgstr "Dienstleistungsrabatt"

#: includes/post-types/coupon-cpt.php:137
msgid "Apply a coupon code to selected services in a booking. Leave blank to apply to all services."
msgstr "Wenden Sie einen Gutscheincode für ausgewählte Dienstleistungen in einer Buchung an. Lassen Sie das Feld leer um sich für alle Dienstleistungen zu bewerben."

#: includes/post-types/coupon-cpt.php:149
msgid "Fee Discount"
msgstr "Gebührrabatt"

#: includes/post-types/coupon-cpt.php:180
msgid "Usage Restrictions"
msgstr "Nutzungsbeschränkungen"

#: includes/post-types/coupon-cpt.php:195
msgid "Check-in After"
msgstr "Abreise nach"

#: includes/post-types/coupon-cpt.php:203
msgid "Check-out Before"
msgstr "Abreise vor"

#: includes/post-types/coupon-cpt.php:211
msgid "Min days before check-in"
msgstr "Min. Tage vor dem Check-in"

#: includes/post-types/coupon-cpt.php:212
msgid "For early bird discount. The coupon code applies if a booking is made in a minimum set number of days before the check-in date."
msgstr "Für Frühbucherrabatte gilt der Gutschein-Code, wenn eine Buchung in einer Mindestanzahl von Tagen vor dem Check-in vorgenommen wird."

#: includes/post-types/coupon-cpt.php:222
msgid "Max days before check-in"
msgstr "Max. Tage vor dem Check-in"

#: includes/post-types/coupon-cpt.php:223
msgid "For last minute discount. The coupon code applies if a booking is made in a maximum set number of days before the check-in date."
msgstr "Für Last-Minute-Rabatt, gilt der Gutschein-Code, wenn eine Buchung in einer maximal festgelegten Anzahl von Tagen vor dem Check-in vorgenommen wird."

#: includes/post-types/coupon-cpt.php:233
msgid "Minimum Days"
msgstr "Min. Tage"

#: includes/post-types/coupon-cpt.php:243
msgid "Maximum Days"
msgstr "Max. Tage"

#: includes/post-types/coupon-cpt.php:253
msgid "Usage Limit"
msgstr "Nutzungslimit"

#: includes/post-types/coupon-cpt.php:263
msgid "Usage Count"
msgstr "Verwendungsanzahl"

#: includes/post-types/coupon-cpt.php:290
msgctxt "Add New Coupon"
msgid "Add New"
msgstr "Neu hinzufügen"

#: includes/post-types/coupon-cpt.php:291
msgid "Add New Coupon"
msgstr "Neuen Gutschein hinzufügen"

#: includes/post-types/coupon-cpt.php:292
msgid "Edit Coupon"
msgstr "Gutschein bearbeiten"

#: includes/post-types/coupon-cpt.php:293
msgid "New Coupon"
msgstr "Neuer Gutschein"

#: includes/post-types/coupon-cpt.php:294
msgid "View Coupon"
msgstr "Gutschein ansehen"

#: includes/post-types/coupon-cpt.php:295
msgid "Search Coupon"
msgstr "Gutschein suchen"

#: includes/post-types/coupon-cpt.php:296
msgid "No coupons found"
msgstr "Keine Gutscheine gefunden"

#: includes/post-types/coupon-cpt.php:297
msgid "No coupons found in Trash"
msgstr "Keine Gutscheine im Papierkorb gefunden"

#: includes/post-types/coupon-cpt.php:298
msgid "All Coupons"
msgstr "Alle Gutscheine"

#: includes/post-types/payment-cpt.php:37
msgid "Payment History"
msgstr "Zahlungshistorie"

#: includes/post-types/payment-cpt.php:38
msgid "Payment"
msgstr "Zahlung"

#: includes/post-types/payment-cpt.php:39
msgctxt "Add New Payment"
msgid "Add New"
msgstr "Neu hinzufügen"

#: includes/post-types/payment-cpt.php:40
msgid "Add New Payment"
msgstr "Neue Zahlung hinzufügen"

#: includes/post-types/payment-cpt.php:41
msgid "Edit Payment"
msgstr "Zahlung bearbeiten"

#: includes/post-types/payment-cpt.php:42
msgid "New Payment"
msgstr "Neue Zahlung"

#: includes/post-types/payment-cpt.php:43
msgid "View Payment"
msgstr "Zahlung ansehen"

#: includes/post-types/payment-cpt.php:44
msgid "Search Payment"
msgstr "Nach Zahlung suchen"

#: includes/post-types/payment-cpt.php:45
msgid "No payments found"
msgstr "Keine Zahlungen gefunden"

#: includes/post-types/payment-cpt.php:46
msgid "No payments found in Trash"
msgstr "Keine Zahlungen im Papierkorb gefunden"

#: includes/post-types/payment-cpt.php:47
msgid "Payments"
msgstr "Zahlungen"

#: includes/post-types/payment-cpt.php:48
msgid "Insert into payment description"
msgstr "In Zahlungsbeschreibung einfügen"

#: includes/post-types/payment-cpt.php:49
msgid "Uploaded to this payment"
msgstr "Hochgeladen in diese Zahlung"

#: includes/post-types/payment-cpt.php:54
msgid "Payments."
msgstr "Zahlungen."

#: includes/post-types/payment-cpt.php:169
msgid "Gateway Mode"
msgstr "Gateway-Modus"

#: includes/post-types/payment-cpt.php:171
msgid "Sandbox"
msgstr "Sandbox"

#: includes/post-types/payment-cpt.php:172
msgid "Live"
msgstr "Live"

#: includes/post-types/payment-cpt.php:194
msgid "Fee"
msgstr "Gebühr"

#: includes/post-types/payment-cpt.php:215
msgid "Payment Type"
msgstr "Zahlungsart"

#: includes/post-types/payment-cpt.php:240
msgid "Billing Info"
msgstr "Rechnungsinfo"

#: includes/post-types/payment-cpt.php:287
msgid "Address 1"
msgstr "Adresse 1"

#: includes/post-types/payment-cpt.php:295
msgid "Address 2"
msgstr "Adresse 2"

#: includes/post-types/payment-cpt.php:319
msgid "Postal Code (ZIP)"
msgstr "Postleitzahl (PLZ)"

#: includes/post-types/payment-cpt/statuses.php:45
msgctxt "Payment status"
msgid "Pending"
msgstr "Ausstehend"

#: includes/post-types/payment-cpt/statuses.php:50
msgid "Pending <span class=\"count\">(%s)</span>"
msgid_plural "Pending <span class=\"count\">(%s)</span>"
msgstr[0] "Ausstehend <span class=\"count\">(%s)</span>"
msgstr[1] "Ausstehend <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:56
msgctxt "Payment status"
msgid "Completed"
msgstr "Abgeschlossen"

#: includes/post-types/payment-cpt/statuses.php:61
msgid "Completed <span class=\"count\">(%s)</span>"
msgid_plural "Completed <span class=\"count\">(%s)</span>"
msgstr[0] "Abgeschlossen <span class=\"count\">(%s)</span>"
msgstr[1] "Abgeschlossen <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:67
msgctxt "Payment status"
msgid "Failed"
msgstr "Fehlgeschlagen"

#: includes/post-types/payment-cpt/statuses.php:72
msgid "Failed <span class=\"count\">(%s)</span>"
msgid_plural "Failed <span class=\"count\">(%s)</span>"
msgstr[0] "Fehlgeschlagen <span class=\"count\">(%s)</span>"
msgstr[1] "Fehlgeschlagen <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:78
msgctxt "Payment status"
msgid "Abandoned"
msgstr "Aufgegeben"

#: includes/post-types/payment-cpt/statuses.php:89
msgctxt "Payment status"
msgid "On Hold"
msgstr "In der Warteschleife"

#: includes/post-types/payment-cpt/statuses.php:94
msgid "On Hold <span class=\"count\">(%s)</span>"
msgid_plural "On Hold <span class=\"count\">(%s)</span>"
msgstr[0] "In der Warteschleife <span class=\"count\">(%s)</span>"
msgstr[1] "In der Warteschleife <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:100
msgctxt "Payment status"
msgid "Refunded"
msgstr "Zurückerstattet"

#: includes/post-types/payment-cpt/statuses.php:105
msgid "Refunded <span class=\"count\">(%s)</span>"
msgid_plural "Refunded <span class=\"count\">(%s)</span>"
msgstr[0] "Zurückerstattet <span class=\"count\">(%s)</span>"
msgstr[1] "Zurückerstattet <span class=\"count\">(%s)</span>"

#: includes/post-types/payment-cpt/statuses.php:111
msgctxt "Payment status"
msgid "Cancelled"
msgstr "Storniert"

#: includes/post-types/payment-cpt/statuses.php:180
msgid "Payment (#%s) for this booking is on hold"
msgstr "Zahlung (#%s)  für diese Buchung ist in der Warteschleife"

#: includes/post-types/rate-cpt.php:23
msgid "Rate Info"
msgstr "Rate-Info"

#: includes/post-types/rate-cpt.php:49
#: includes/post-types/season-cpt.php:99
msgid "Season"
msgstr "Saison"

#: includes/post-types/rate-cpt.php:72
msgid "Move price to top to set higher priority."
msgstr "Verschieben Sie den Preis nach oben, um höhere Priorität zu setzen."

#: includes/post-types/rate-cpt.php:84
msgid "Will be displayed on the checkout page."
msgstr "Wird auf der Checkout-Seite angezeigt."

#. translators: The value a hotel wishes to sell their rooms. Also called the Cost, Value, Tariff or Room charge.
#: includes/post-types/rate-cpt.php:103
#: includes/post-types/rate-cpt.php:113
msgid "Rates"
msgstr "Raten"

#: includes/post-types/rate-cpt.php:105
msgctxt "Add New Rate"
msgid "Add New"
msgstr "Neu hinzufügen"

#: includes/post-types/rate-cpt.php:106
msgid "Add New Rate"
msgstr "Neue Rate hinzufügen"

#: includes/post-types/rate-cpt.php:107
msgid "Edit Rate"
msgstr "Rate bearbeiten"

#: includes/post-types/rate-cpt.php:108
msgid "New Rate"
msgstr "Neue Rate"

#: includes/post-types/rate-cpt.php:109
msgid "View Rate"
msgstr "Rate ansehen"

#: includes/post-types/rate-cpt.php:110
msgid "Search Rate"
msgstr "Nach Rate suchen"

#: includes/post-types/rate-cpt.php:111
msgid "No rates found"
msgstr "Keine Raten gefunden"

#: includes/post-types/rate-cpt.php:112
msgid "No rates found in Trash"
msgstr "Kein Raten im Papierkorb gefunden"

#: includes/post-types/rate-cpt.php:114
msgid "Insert into rate description"
msgstr "In die Rate-Beschreibung einfügen"

#: includes/post-types/rate-cpt.php:115
msgid "Uploaded to this rate"
msgstr "Zu dieser Rate hochgeladen"

#: includes/post-types/rate-cpt.php:120
msgid "This is where you can add new rates."
msgstr "Hier können Sie neue Raten hinzufügen."

#: includes/post-types/reserved-room-cpt.php:23
msgid "Reserved Accommodation"
msgstr "Reservierte Unterkunft"

#: includes/post-types/room-cpt.php:33
msgctxt "Add New Accommodation"
msgid "Add New"
msgstr "Neu hinzufügen"

#: includes/post-types/room-cpt.php:34
msgid "Add New Accommodation"
msgstr "Neue Unterkunft hinzufügen"

#: includes/post-types/room-cpt.php:35
msgid "Edit Accommodation"
msgstr "Unterkunft bearbeiten"

#: includes/post-types/room-cpt.php:36
msgid "New Accommodation"
msgstr "Neue Unterkunft"

#: includes/post-types/room-cpt.php:37
msgid "View Accommodation"
msgstr "Unterkunft ansehen"

#: includes/post-types/room-cpt.php:38
msgid "Search Accommodation"
msgstr "Nach Unterkunft suchen"

#: includes/post-types/room-cpt.php:39
#: templates/create-booking/results/rooms-found.php:21
#: templates/shortcodes/search-results/results-info.php:19
msgid "No accommodations found"
msgstr "Keine Unterkünfte gefunden"

#: includes/post-types/room-cpt.php:40
msgid "No accommodations found in Trash"
msgstr "Keine Unterkünfte im Papierkorb gefunden"

#: includes/post-types/room-cpt.php:42
msgid "Insert into accommodation description"
msgstr "In die Unterkunft-Beschreibung einfügen"

#: includes/post-types/room-cpt.php:43
msgid "Uploaded to this accommodation"
msgstr "Hochgeladen in diese Unterkunft"

#: includes/post-types/room-cpt.php:48
msgid "This is where you can add new accommodations to your hotel."
msgstr "Hier können Sie neue Unterkünfte  zu Ihrem Hotel hinzufügen."

#: includes/post-types/room-cpt.php:106
msgid "Automatically block current accommodation when the selected ones are booked"
msgstr "Aktuelle Unterkunft automatisch blockieren, wenn die ausgewählten gebucht werden"

#: includes/post-types/room-type-cpt.php:55
msgctxt "Add New Accommodation Type"
msgid "Add Accommodation Type"
msgstr "Unterkunftsart hinzufügen"

#: includes/post-types/room-type-cpt.php:56
msgid "Add New Accommodation Type"
msgstr "Neuen Unterkunftsart hinzufügen"

#: includes/post-types/room-type-cpt.php:57
msgid "Edit Accommodation Type"
msgstr "Unterkunftsart bearbeiten"

#: includes/post-types/room-type-cpt.php:58
msgid "New Accommodation Type"
msgstr "Neuer Unterkunftsart"

#: includes/post-types/room-type-cpt.php:59
msgid "View Accommodation Type"
msgstr "Unterkunftsart ansehen"

#: includes/post-types/room-type-cpt.php:61
msgid "Search Accommodation Type"
msgstr "Nach Unterkunftsart suchen"

#: includes/post-types/room-type-cpt.php:62
msgid "No Accommodation types found"
msgstr "Keine Unterkunftsarten gefunden"

#: includes/post-types/room-type-cpt.php:63
msgid "No Accommodation types found in Trash"
msgstr "Keine Unterkunftsart im Papierkorb gefunden"

#: includes/post-types/room-type-cpt.php:65
msgid "Insert into accommodation type description"
msgstr "In die Unterkunftsart-Beschreibung einfügen"

#: includes/post-types/room-type-cpt.php:66
msgid "Uploaded to this accommodation type"
msgstr "Hochgeladen in diese Unterkunftsart"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:83
msgctxt "slug"
msgid "accommodation"
msgstr "accommodation"

#: includes/post-types/room-type-cpt.php:108
msgid "Accommodation Categories"
msgstr "Unterkunftskategorien"

#: includes/post-types/room-type-cpt.php:109
msgid "Accommodation Category"
msgstr "Unterkunftskategorie"

#: includes/post-types/room-type-cpt.php:110
msgid "Search Accommodation Categories"
msgstr "Nach Unterkunftskategorien suchen"

#: includes/post-types/room-type-cpt.php:111
msgid "Popular Accommodation Categories"
msgstr "Beliebte Unterkunftskategorien"

#: includes/post-types/room-type-cpt.php:112
msgid "All Accommodation Categories"
msgstr "Alle Unterkunftskategorien"

#: includes/post-types/room-type-cpt.php:113
msgid "Parent Accommodation Category"
msgstr "Übergeordnete Unterkunftskategorie"

#: includes/post-types/room-type-cpt.php:114
msgid "Parent Accommodation Category:"
msgstr "Unterkunftskategorie bearbeiten:"

#: includes/post-types/room-type-cpt.php:115
msgid "Edit Accommodation Category"
msgstr "Unterkunftskategorie bearbeiten"

#: includes/post-types/room-type-cpt.php:116
msgid "Update Accommodation Category"
msgstr "Unterkunftskategorie aktualisieren"

#: includes/post-types/room-type-cpt.php:117
msgid "Add New Accommodation Category"
msgstr "Neue Unterkunftskategorie hinzufügen"

#: includes/post-types/room-type-cpt.php:118
msgid "New Accommodation Category Name"
msgstr "Neuer Unterkunftskategorie-Name"

#: includes/post-types/room-type-cpt.php:119
msgid "Separate categories with commas"
msgstr "Kategorien mit Kommas trennen"

#: includes/post-types/room-type-cpt.php:120
msgid "Add or remove categories"
msgstr "Kategorien hinzufügen oder entfernen"

#: includes/post-types/room-type-cpt.php:121
msgid "Choose from the most used categories"
msgstr "Wählen Sie aus den den meisten genutzten Kategorien"

#: includes/post-types/room-type-cpt.php:122
msgid "No categories found."
msgstr "Keine Kategorien gefunden."

#: includes/post-types/room-type-cpt.php:123
#: assets/blocks/blocks.js:816
msgid "Categories"
msgstr "Kategorien"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:138
msgctxt "slug"
msgid "accommodation-category"
msgstr "accommodation-category"

#: includes/post-types/room-type-cpt.php:163
msgid "Accommodation Tags"
msgstr "Unterkunfts-Tags"

#: includes/post-types/room-type-cpt.php:164
msgid "Accommodation Tag"
msgstr "Unterkunfts-Tag"

#: includes/post-types/room-type-cpt.php:165
msgid "Search Accommodation Tags"
msgstr "Nach Unterkunfts-Tags suchen"

#: includes/post-types/room-type-cpt.php:166
msgid "Popular Accommodation Tags"
msgstr "Beliebte Unterkunfts-Tags"

#: includes/post-types/room-type-cpt.php:167
msgid "All Accommodation Tags"
msgstr "Alle Unterkunfts-Tags"

#: includes/post-types/room-type-cpt.php:168
msgid "Parent Accommodation Tag"
msgstr "Parent Unterkunfts-Tag"

#: includes/post-types/room-type-cpt.php:169
msgid "Parent Accommodation Tag:"
msgstr "Parent Unterkunfts-Tag:"

#: includes/post-types/room-type-cpt.php:170
msgid "Edit Accommodation Tag"
msgstr "Unterkunfts-Tag bearbeiten"

#: includes/post-types/room-type-cpt.php:171
msgid "Update Accommodation Tag"
msgstr "Unterkunfts-Tag aktualisieren"

#: includes/post-types/room-type-cpt.php:172
msgid "Add New Accommodation Tag"
msgstr "Neues Unterkunfts-Tag hinzufügen"

#: includes/post-types/room-type-cpt.php:173
msgid "New Accommodation Tag Name"
msgstr "Neuer Name des Unterkunfts-Tags"

#: includes/post-types/room-type-cpt.php:174
msgid "Separate tags with commas"
msgstr "Tags durch Kommas trennen"

#: includes/post-types/room-type-cpt.php:175
msgid "Add or remove tags"
msgstr "Tags hinzufügen oder entfernen"

#: includes/post-types/room-type-cpt.php:176
msgid "Choose from the most used tags"
msgstr "Aus den am häufigsten verwendeten Tags wählen"

#: includes/post-types/room-type-cpt.php:177
msgid "No tags found."
msgstr "Keine Tags gefunden."

#: includes/post-types/room-type-cpt.php:178
#: assets/blocks/blocks.js:828
msgid "Tags"
msgstr "Tags"

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:192
msgctxt "slug"
msgid "accommodation-tag"
msgstr "accommodation-tag"

#: includes/post-types/room-type-cpt.php:217
#: includes/post-types/room-type-cpt.php:232
msgid "Amenities"
msgstr "Ausstattungen"

#: includes/post-types/room-type-cpt.php:218
msgid "Amenity"
msgstr "Ausstattung"

#: includes/post-types/room-type-cpt.php:219
msgid "Search Amenities"
msgstr "Nach Ausstattungen suchen"

#: includes/post-types/room-type-cpt.php:220
msgid "Popular Amenities"
msgstr "Beliebteste Ausstattungen"

#: includes/post-types/room-type-cpt.php:221
msgid "All Amenities"
msgstr "Alle Ausstattungen"

#: includes/post-types/room-type-cpt.php:222
msgid "Parent Amenity"
msgstr "Übergeordnete Ausstattung"

#: includes/post-types/room-type-cpt.php:223
msgid "Parent Amenity:"
msgstr "Übergeordnete Ausstattung:"

#: includes/post-types/room-type-cpt.php:224
msgid "Edit Amenity"
msgstr "Ausstattung bearbeiten"

#: includes/post-types/room-type-cpt.php:225
msgid "Update Amenity"
msgstr "Ausstattung aktualisieren"

#: includes/post-types/room-type-cpt.php:226
msgid "Add New Amenity"
msgstr "Neue Ausstattung hinzufügen"

#: includes/post-types/room-type-cpt.php:227
msgid "New Amenity Name"
msgstr "Neuer Ausstattungsname"

#: includes/post-types/room-type-cpt.php:228
msgid "Separate amenities with commas"
msgstr "Einrichtungen mit Kommas trennen"

#: includes/post-types/room-type-cpt.php:229
msgid "Add or remove amenities"
msgstr "Einrichtungen hinzufügen oder entfernen"

#: includes/post-types/room-type-cpt.php:230
msgid "Choose from the most used amenities"
msgstr "Wählen Sie aus den meisten genutzten Einrichtungen"

#: includes/post-types/room-type-cpt.php:231
msgid "No amenities found."
msgstr "Keine Einrichtungen gefunden."

#. translators: do not translate
#: includes/post-types/room-type-cpt.php:247
msgctxt "slug"
msgid "accommodation-facility"
msgstr "accommodation-facility"

#: includes/post-types/room-type-cpt.php:293
msgid "State the age or disable children in <a href=\"%s\">settings</a>."
msgstr "Geben Sie das Alter an oder deaktivieren Sie Kinder in den <a href=\"%s\">Einstellungen</a>."

#: includes/post-types/room-type-cpt.php:303
msgid "Leave this option empty to calculate total capacity automatically to meet the exact number of adults AND children set above. This is the default behavior. Configure this option to allow any variations of adults OR children set above at checkout so that in total it meets the limit of manually set \"Capacity\". For example, configuration \"adults:5\", \"children:4\", \"capacity:5\" means the property can accommodate up to 5 adults, up to 4 children, but up to 5 guests in total (not 9)."
msgstr "Lassen Sie diese Option leer, um die Gesamtkapazität automatisch zu berechnen, um die oben festgelegte genaue Anzahl von Erwachsenen UND Kindern zu erreichen. Dies ist das Standardverhalten. Konfigurieren Sie diese Option, um Variationen von Erwachsenen ODER Kindern zuzulassen, die oben beim Auschecken festgelegt wurden, sodass insgesamt das Limit der manuell festgelegten \"Kapazität\" erreicht wird. Beispiel: Konfiguration \"Erwachsene: 5\", \"Kinder: 4\", \"Kapazität: 5\" bedeutet, dass in der Unterkunft bis zu 5 Erwachsene, bis zu 4 Kinder, aber insgesamt bis zu 5 Gäste (nicht 9) untergebracht werden können."

#: includes/post-types/room-type-cpt.php:313
msgid "Base Adults Occupancy"
msgstr "Basis Erwachsene Belegung"

#: includes/post-types/room-type-cpt.php:314
#: includes/post-types/room-type-cpt.php:325
msgid "An optional starting value used when creating seasonal prices in the Rates menu."
msgstr "Ein optionaler Startwert, der bei der Erstellung von saisonalen Preisen im Preismenü verwendet wird."

#: includes/post-types/room-type-cpt.php:324
msgid "Base Children Occupancy"
msgstr "Basis Kinder Belegung"

#: includes/post-types/room-type-cpt.php:334
msgid "Other"
msgstr "Andere"

#: includes/post-types/room-type-cpt.php:340
msgid "Size, %s"
msgstr "Größe, %s"

#: includes/post-types/room-type-cpt.php:341
msgid "Leave blank to hide."
msgstr "Leer lassen, um es nicht anzeigen zu lassen."

#: includes/post-types/room-type-cpt.php:355
msgid "City view, seaside, swimming pool etc."
msgstr "Stadtansicht, Meer, Schwimmbad etc."

#: includes/post-types/room-type-cpt.php:366
msgid "Bed type"
msgstr "Bettentyp"

#: includes/post-types/room-type-cpt.php:369
msgid "Set bed types list in <a href=\"%link%\" target=\"_blank\">settings</a>."
msgstr "Bettentypen Liste in <a href=\"%link%\" target=\"_blank\">Einstellungen </a> festlegen."

#: includes/post-types/room-type-cpt.php:379
msgid "Photo Gallery"
msgstr "Fotogalerie"

#: includes/post-types/room-type-cpt.php:390
#: includes/post-types/room-type-cpt.php:395
msgid "Available Services"
msgstr "Verfügbare Dienstleistungen"

#: includes/post-types/season-cpt.php:25
msgid "Season Info"
msgstr "Saison-Info"

#: includes/post-types/season-cpt.php:32
#: includes/reports/earnings-report.php:332
msgid "Start date"
msgstr "Anfangsdatum"

#: includes/post-types/season-cpt.php:45
#: includes/reports/earnings-report.php:344
msgid "End date"
msgstr "Enddatum"

#: includes/post-types/season-cpt.php:55
msgid "Applied for days"
msgstr "Angewendet für Tage"

#: includes/post-types/season-cpt.php:59
msgid "Hold Ctrl / Cmd to select multiple."
msgstr "Halten Sie Ctrl/Cmd gedrückt, um mehrere Optionen zu wählen."

#: includes/post-types/season-cpt.php:68
msgid "Annual repeats begin on the Start date of the season, for one year from the current date."
msgstr "Jährliche Wiederholungen beginnen am Startdatum der Saison, für ein Jahr ab dem aktuellen Datum."

#: includes/post-types/season-cpt.php:70
msgid "Does not repeat"
msgstr "Wird nicht wiederholt"

#: includes/post-types/season-cpt.php:81
msgid "Repeat until date"
msgstr "Wiederholen bis Datum"

#: includes/post-types/season-cpt.php:100
msgctxt "Add New Season"
msgid "Add New"
msgstr "Neu hinzufügen"

#: includes/post-types/season-cpt.php:101
msgid "Add New Season"
msgstr "Neue Saison hinzufügen"

#: includes/post-types/season-cpt.php:102
msgid "Edit Season"
msgstr "Saison bearbeiten"

#: includes/post-types/season-cpt.php:103
msgid "New Season"
msgstr "Neue Saison"

#: includes/post-types/season-cpt.php:104
msgid "View Season"
msgstr "Saison ansehen"

#: includes/post-types/season-cpt.php:105
msgid "Search Season"
msgstr "Nach Saison suchen"

#: includes/post-types/season-cpt.php:106
msgid "No seasons found"
msgstr "Keine Saisons gefunden"

#: includes/post-types/season-cpt.php:107
msgid "No seasons found in Trash"
msgstr "Keine Saisons im Papierkorb gefunden"

#: includes/post-types/season-cpt.php:109
msgid "Insert into season description"
msgstr "In Saison-Beschreibung einfügen"

#: includes/post-types/season-cpt.php:110
msgid "Uploaded to this season"
msgstr "Hochgeladen in diese Saison"

#: includes/post-types/season-cpt.php:115
msgid "This is where you can add new seasons."
msgstr "Hier können Sie neue Saisons hinzufügen."

#: includes/post-types/service-cpt.php:92
#: includes/views/booking-view.php:206
msgid "Service"
msgstr "Dienstleistung"

#: includes/post-types/service-cpt.php:93
msgctxt "Add New Service"
msgid "Add New"
msgstr "Neu hinzufügen"

#: includes/post-types/service-cpt.php:94
msgid "Add New Service"
msgstr "Neuen Service hinzufügen"

#: includes/post-types/service-cpt.php:95
msgid "Edit Service"
msgstr "Service bearbeiten"

#: includes/post-types/service-cpt.php:96
msgid "New Service"
msgstr "Neuer Service"

#: includes/post-types/service-cpt.php:97
msgid "View Service"
msgstr "Service ansehen"

#: includes/post-types/service-cpt.php:98
msgid "Search Service"
msgstr "Nach Service suchen"

#: includes/post-types/service-cpt.php:99
msgid "No services found"
msgstr "Keine Services gefunden"

#: includes/post-types/service-cpt.php:100
msgid "No services found in Trash"
msgstr "Kein Service im Papierkorb gefunden"

#: includes/post-types/service-cpt.php:102
msgid "Insert into service description"
msgstr "In Beschreibung vom Service einfügen"

#: includes/post-types/service-cpt.php:103
msgid "Uploaded to this service"
msgstr "Hochgeladen in diesen Service"

#. translators: do not translate
#: includes/post-types/service-cpt.php:120
msgctxt "slug"
msgid "services"
msgstr "services"

#: includes/post-types/service-cpt.php:156
msgid "How many times the customer will be charged."
msgstr "Wie oft wird dem Kunden die Rechnung gestellt."

#: includes/post-types/service-cpt.php:167
msgid "Minimum"
msgstr "Minimal"

#: includes/post-types/service-cpt.php:181
msgid "Maximum"
msgstr "Maximal"

#: includes/post-types/service-cpt.php:193
msgid "Empty means unlimited"
msgstr "Leer lassen für unbegrenzte Anzahl von Malen"

#: includes/reports/data/report-earnings-by-dates-data.php:29
msgctxt "Booking status"
msgid "Pending"
msgstr "Ausstehend"

#: includes/reports/earnings-report.php:91
msgid "Total Sales"
msgstr "Umsatz gesamt"

#: includes/reports/earnings-report.php:94
msgid "Total Without Taxes"
msgstr "Gesamt ohne Steuern"

#: includes/reports/earnings-report.php:97
msgid "Total Fees"
msgstr "Gebühren gesamt"

#: includes/reports/earnings-report.php:100
msgid "Total Services"
msgstr "Services gesamt"

#: includes/reports/earnings-report.php:103
msgid "Total Discounts"
msgstr "Rabatte gesamt"

#: includes/reports/earnings-report.php:106
msgid "Total Bookings"
msgstr "Buchungen gesamt"

#: includes/reports/earnings-report.php:289
#: includes/reports/report-filters.php:38
msgid "Revenue"
msgstr "Einnahmen"

#: includes/reports/earnings-report.php:352
#: includes/views/create-booking/checkout-view.php:56
#: includes/views/shortcodes/checkout-view.php:90
msgid "Apply"
msgstr "Anwenden"

#: includes/reports/earnings-report.php:496
msgid "From %s to %s"
msgstr "Von %s bis %s"

#: includes/reports/report-filters.php:61
msgid "Today"
msgstr "Heute"

#: includes/reports/report-filters.php:64
msgid "Yesterday"
msgstr "Gestern"

#: includes/reports/report-filters.php:67
msgid "This week"
msgstr "Diese Woche"

#: includes/reports/report-filters.php:70
msgid "Last week"
msgstr "Letzte Woche"

#: includes/reports/report-filters.php:73
msgid "Last 30 days"
msgstr "Letzten 30 Tage"

#: includes/reports/report-filters.php:76
msgid "This month"
msgstr "Dieser Monat"

#: includes/reports/report-filters.php:79
msgid "Last month"
msgstr "Letzter Monat"

#: includes/reports/report-filters.php:82
msgid "This quarter"
msgstr "Dieses Quartal"

#: includes/reports/report-filters.php:85
msgid "Last quarter"
msgstr "Letztes Quartal"

#: includes/reports/report-filters.php:88
msgid "This year"
msgstr "Dieses Jahr"

#: includes/reports/report-filters.php:91
msgid "Last year"
msgstr "Letztes Jahr"

#. translators: %s - original Rate title
#: includes/repositories/rate-repository.php:195
msgid "%s - copy"
msgstr "%s - Kopie"

#: includes/script-managers/admin-script-manager.php:92
msgid "Accommodation Type Gallery"
msgstr "Unterkunftsart-Galerie"

#: includes/script-managers/admin-script-manager.php:93
msgid "Add Gallery To Accommodation Type"
msgstr "Galerie zu Unterkunftsart hinzufügen"

#: includes/script-managers/admin-script-manager.php:105
msgid "Display imported bookings."
msgstr "Importierte Buchungen anzeigen."

#: includes/script-managers/admin-script-manager.php:106
msgid "Processing..."
msgstr "Bearbeitung..."

#: includes/script-managers/admin-script-manager.php:107
msgid "Cancelling..."
msgstr "Stornierung..."

#: includes/script-managers/admin-script-manager.php:108
msgid "Want to delete?"
msgstr "Löschen?"

#: includes/script-managers/public-script-manager.php:204
msgid "Not available"
msgstr "Nicht verfügbar"

#: includes/script-managers/public-script-manager.php:205
msgid "This is earlier than allowed by our advance reservation rules."
msgstr "Das ist früher als in unseren Regeln für die Reservierung im Voraus erlaubt."

#: includes/script-managers/public-script-manager.php:206
msgid "This is later than allowed by our advance reservation rules."
msgstr "Das ist später als in unseren Regeln für die Reservierung im Voraus erlaubt."

#: includes/script-managers/public-script-manager.php:210
msgid "Day in the past"
msgstr "Tag in der Vergangenheit"

#: includes/script-managers/public-script-manager.php:211
msgid "Check-in date"
msgstr "Anreisedatum"

#: includes/script-managers/public-script-manager.php:212
msgid "Less than min days stay"
msgstr "Weniger als Minimalaufenthalt"

#: includes/script-managers/public-script-manager.php:213
msgid "More than max days stay"
msgstr "Mehr als Maximalaufenthalt"

#: includes/script-managers/public-script-manager.php:215
msgid "Later than max date for current check-in date"
msgstr "Später als max. Datum für das aktuelle Anreisedatum"

#: includes/script-managers/public-script-manager.php:216
msgid "Rules:"
msgstr "Regeln:"

#: includes/script-managers/public-script-manager.php:217
msgid "Tokenisation failed: %s"
msgstr "Tokenisierung fehlgeschlagen: %s"

#: includes/script-managers/public-script-manager.php:218
#: includes/script-managers/public-script-manager.php:219
msgid "%1$d &times; &ldquo;%2$s&rdquo; has been added to your reservation."
msgid_plural "%1$d &times; &ldquo;%2$s&rdquo; have been added to your reservation."
msgstr[0] "%1$d &times; &ldquo;%2$s&rdquo; wurde zu Ihrer Reservierung hinzugefügt."
msgstr[1] "%1$d &times; &ldquo;%2$s&rdquo; wurden zu Ihrer Reservierung hinzugefügt."

#: includes/script-managers/public-script-manager.php:220
#: includes/script-managers/public-script-manager.php:221
msgid "%s accommodation selected."
msgid_plural "%s accommodations selected."
msgstr[0] "%s Unterkunft ausgewählt."
msgstr[1] "%s Unterkünfte ausgewählt."

#: includes/script-managers/public-script-manager.php:222
msgid "Coupon code is empty."
msgstr "Der Gutscheincode ist leer."

#: includes/script-managers/public-script-manager.php:225
msgid "Select dates"
msgstr "Wähle einen Zeitraum aus"

#: includes/settings/main-settings.php:26
msgid "Dark Blue"
msgstr "Dunkelblau"

#: includes/settings/main-settings.php:27
msgid "Dark Green"
msgstr "Dunkelgrün"

#: includes/settings/main-settings.php:28
msgid "Dark Red"
msgstr "Dunkelrot"

#: includes/settings/main-settings.php:29
msgid "Grayscale"
msgstr "Graustufen"

#: includes/settings/main-settings.php:30
msgid "Light Blue"
msgstr "Hellblau"

#: includes/settings/main-settings.php:31
msgid "Light Coral"
msgstr "Hellkorallenrot"

#: includes/settings/main-settings.php:32
msgid "Light Green"
msgstr "Hellgrün"

#: includes/settings/main-settings.php:33
msgid "Light Yellow"
msgstr "Hellgelb"

#: includes/settings/main-settings.php:34
msgid "Minimal Blue"
msgstr "Minimal Blau"

#: includes/settings/main-settings.php:35
msgid "Minimal Orange"
msgstr "Minimal Orange"

#: includes/settings/main-settings.php:36
msgid "Minimal"
msgstr "Minimal"

#: includes/settings/main-settings.php:38
msgid "Sky Blue"
msgstr "Himmelblau"

#: includes/settings/main-settings.php:39
msgid "Slate Blue"
msgstr "Schieferblau"

#: includes/settings/main-settings.php:40
msgid "Turquoise"
msgstr "Türkis"

#: includes/shortcodes/account-shortcode.php:212
#: includes/views/shortcodes/checkout-view.php:22
msgid "Invalid login or password."
msgstr "Ungültiger Benutzername oder Passwort."

#: includes/shortcodes/account-shortcode.php:221
msgid "Account data updated."
msgstr "Kontodaten aktualisiert."

#: includes/shortcodes/account-shortcode.php:227
msgid "Password changed."
msgstr "Passwort geändert."

#: includes/shortcodes/account-shortcode.php:238
msgid "Dashboard"
msgstr "Dashboard"

#: includes/shortcodes/account-shortcode.php:240
msgid "Account"
msgstr "Konto"

#: includes/shortcodes/account-shortcode.php:241
msgid "Logout"
msgstr "Abmelden"

#: includes/shortcodes/account-shortcode.php:279
msgid "Passwords do not match."
msgstr "Die Passwörter stimmen nicht überein."

#: includes/shortcodes/account-shortcode.php:282
msgid "Please, provide a valid current password."
msgstr "Bitte geben Sie ein gültiges aktuelles Passwort ein."

#: includes/shortcodes/account-shortcode.php:301
#: includes/views/shortcodes/checkout-view.php:54
msgid "Lost your password?"
msgstr "Passwort vergessen?"

#: includes/shortcodes/booking-confirmation-shortcode.php:294
msgid "Payment:"
msgstr "Bezahlung:"

#: includes/shortcodes/booking-confirmation-shortcode.php:302
msgid "Payment Method:"
msgstr "Zahlungsmethode:"

#: includes/shortcodes/booking-confirmation-shortcode.php:315
#: templates/shortcodes/booking-details/booking-details.php:42
msgid "Status:"
msgstr "Status:"

#: includes/shortcodes/checkout-shortcode.php:196
msgid "Bookings are disabled in the settings."
msgstr "Buchungen sind in den Einstellungen deaktiviert."

#: includes/shortcodes/checkout-shortcode/step-booking.php:151
msgid "Checkout data is not valid."
msgstr "Checkout-Daten sind nicht gültig."

#: includes/shortcodes/checkout-shortcode/step-booking.php:449
msgid "Payment method is not valid."
msgstr "Zahlungsmethode ist nicht gültig."

#: includes/shortcodes/checkout-shortcode/step-checkout.php:193
msgid "Accommodation count is not valid."
msgstr "Unterkunft-Anzahl ist nicht gültig."

#: includes/shortcodes/checkout-shortcode/step.php:110
msgid "Accommodation is already booked."
msgstr "Die Unterkunft ist bereits gebucht."

#: includes/shortcodes/checkout-shortcode/step.php:120
#: includes/shortcodes/checkout-shortcode/step.php:129
#: includes/shortcodes/checkout-shortcode/step.php:138
msgid "Reservation submitted"
msgstr "Reservierung eingereicht"

#: includes/shortcodes/checkout-shortcode/step.php:121
msgid "Details of your reservation have just been sent to you in a confirmation email. Please check your inbox to complete booking."
msgstr "Details Ihrer Reservierung wurden Ihnen gerade in einer Bestätigungs-E-Mail zugesandt. Bitte überprüfen Sie Ihren Posteingang, um die Buchung abzuschließen."

#: includes/shortcodes/checkout-shortcode/step.php:130
#: includes/shortcodes/checkout-shortcode/step.php:139
msgid "We received your booking request. Once it is confirmed we will notify you via email."
msgstr "Wir haben Ihre Buchungsanfrage erhalten. Sobald sie bestätigt ist, werden wir Sie per E-Mail benachrichtigen."

#: includes/shortcodes/room-rates-shortcode.php:104
#: template-functions.php:31
msgid "Choose dates to see relevant prices"
msgstr "Wählen Sie Daten, um relevante Preise zu sehen"

#: includes/shortcodes/search-results-shortcode.php:766
msgid "Select from available accommodations."
msgstr "Wählen Sie aus verfügbaren Unterkünften."

#: includes/shortcodes/search-results-shortcode.php:775
#: includes/shortcodes/search-results-shortcode.php:1013
#: template-functions.php:843
msgid "Confirm Reservation"
msgstr "Reservierung bestätigen"

#: includes/shortcodes/search-results-shortcode.php:804
msgid "Recommended for %d adult"
msgid_plural "Recommended for %d adults"
msgstr[0] "Empfohlen für %d Erwachsenen"
msgstr[1] "Empfohlen für %d Erwachsene"

#: includes/shortcodes/search-results-shortcode.php:806
msgid " and %d child"
msgid_plural " and %d children"
msgstr[0] " und %d Kind"
msgstr[1] " und %d Kinder"

#: includes/shortcodes/search-results-shortcode.php:809
msgid "Recommended for %d guest"
msgid_plural "Recommended for %d guests"
msgstr[0] "Empfohlen für %d Gast"
msgstr[1] "Empfohlen für %d Gäste"

#: includes/shortcodes/search-results-shortcode.php:891
msgid "Max occupancy:"
msgstr "Max. Belegung:"

#: includes/shortcodes/search-results-shortcode.php:910
msgid "%d child"
msgid_plural "%d children"
msgstr[0] "%d Kind"
msgstr[1] "%d Kinder"

#: includes/shortcodes/search-results-shortcode.php:945
#: templates/create-booking/results/reserve-rooms.php:76
msgid "Reserve"
msgstr "Reservieren"

#: includes/shortcodes/search-results-shortcode.php:1002
msgid "of %d accommodation available."
msgid_plural "of %d accommodations available."
msgstr[0] "von %d Unterkunft verfügbar."
msgstr[1] "von %d Unterkünfte verfügbar."

#. translators: Verb. To book an accommodation.
#: includes/shortcodes/search-results-shortcode.php:1012
#: template-functions.php:531
#: template-functions.php:544
msgid "Book"
msgstr "Buchen"

#: includes/users-and-roles/customers.php:290
#: includes/users-and-roles/customers.php:383
msgid "Please, provide a valid email."
msgstr "Bitte geben Sie eine gültige E-Mail ein."

#: includes/users-and-roles/customers.php:318
msgid "Could not create a customer."
msgstr "Kunde konnte nicht erstellt werden."

#: includes/users-and-roles/customers.php:379
msgid "Could not retrieve a customer."
msgstr "Kunde konnte nicht abgerufen werden."

#: includes/users-and-roles/customers.php:531
#: includes/users-and-roles/customers.php:577
msgid "Please, provide a valid Customer ID."
msgstr "Bitte geben Sie eine gültige Kunden-ID ein."

#: includes/users-and-roles/customers.php:563
msgid "A database error."
msgstr "Datenbankfehler."

#: includes/users-and-roles/customers.php:583
msgid "No customer was deleted."
msgstr "Es wurde kein Kunde gelöscht."

#: includes/users-and-roles/customers.php:694
#: includes/views/shortcodes/checkout-view.php:31
msgid "An account with this email already exists. Please, log in."
msgstr "Es gibt bereits ein Konto mit dieser E-Mail-Adresse. Bitte melden Sie sich an."

#: includes/users-and-roles/roles.php:34
msgid "Hotel Manager"
msgstr "Hotelmanager"

#: includes/users-and-roles/roles.php:41
msgid "Hotel Worker"
msgstr "Hotelmitarbeiter"

#: includes/users-and-roles/roles.php:48
msgid "Hotel Customer"
msgstr "Hotelkunde"

#: includes/users-and-roles/user.php:54
msgid "Please provide a valid email address."
msgstr "Bitte geben Sie eine gültige E-Mail-Adresse ein."

#: includes/users-and-roles/user.php:69
msgid "Please enter a valid account username."
msgstr "Bitte geben Sie einen gültigen Konto-Benutzernamen ein."

#: includes/users-and-roles/user.php:73
msgid "An account is already registered with that username. Please choose another."
msgstr "Es ist bereits ein Konto mit diesem Benutzernamen registriert. Bitte wählen Sie einen anderen Benutzernamen."

#: includes/users-and-roles/user.php:81
msgid "Please enter an account password."
msgstr "Bitte geben Sie ein Kontopasswort ein."

#: includes/utils/date-utils.php:145
msgid "Sunday"
msgstr "Sonntag"

#: includes/utils/date-utils.php:146
msgid "Monday"
msgstr "Montag"

#: includes/utils/date-utils.php:147
msgid "Tuesday"
msgstr "Dienstag"

#: includes/utils/date-utils.php:148
msgid "Wednesday"
msgstr "Mittwoch"

#: includes/utils/date-utils.php:149
msgid "Thursday"
msgstr "Donnerstag"

#: includes/utils/date-utils.php:150
msgid "Friday"
msgstr "Freitag"

#: includes/utils/date-utils.php:151
msgid "Saturday"
msgstr "Samstag"

#: includes/utils/parse-utils.php:135
msgid "Check-out date cannot be earlier than check-in date."
msgstr "Das Auscheck-Datum kann nicht vor dem Eincheck-Datum liegen."

#: includes/utils/parse-utils.php:159
msgid "Adults number is not valid"
msgstr "Nummer für Erwachsene ist nicht gültig"

#: includes/utils/parse-utils.php:183
msgid "Children number is not valid"
msgstr "Kindernummer ist nicht gültig"

#: includes/utils/taxes-and-fees-utils.php:27
msgctxt "Text about taxes and fees below the price."
msgid " (+taxes and fees)"
msgstr " (+Steuern und Gebühren)"

#. translators: %s is a tax value
#: includes/utils/taxes-and-fees-utils.php:56
msgctxt "Text about taxes and fees below the price."
msgid " (+%s taxes and fees)"
msgstr " (+%s Steuern und Gebühren)"

#: includes/utils/taxes-and-fees-utils.php:84
msgctxt "Text about taxes and fees below the price."
msgid " (includes taxes and fees)"
msgstr " (einschließlich Steuern und Gebühren)"

#: includes/views/booking-view.php:79
msgctxt "Accommodation type in price breakdown table. Example: #1 Double Room"
msgid "#%d %s"
msgstr "#%d %s"

#: includes/views/booking-view.php:82
msgid "Expand"
msgstr "Erweitern"

#: includes/views/booking-view.php:91
#: includes/views/edit-booking/checkout-view.php:209
msgid "Rate: %s"
msgstr "Rate: %s"

#: includes/views/booking-view.php:125
msgid "Dates"
msgstr "Daten"

#: includes/views/booking-view.php:207
#: includes/views/loop-room-type-view.php:39
#: includes/views/single-room-type-view.php:131
#: includes/widgets/rooms-widget.php:197
#: assets/blocks/blocks.js:484
#: assets/blocks/blocks.js:734
#: assets/blocks/blocks.js:1263
msgid "Details"
msgstr "Details"

#: includes/views/booking-view.php:380
msgid "Subtotal"
msgstr "Zwischensumme"

#: includes/views/booking-view.php:393
msgid "Coupon: %s"
msgstr "Gutschein: %s"

#: includes/views/booking-view.php:412
msgid "Subtotal (excl. taxes)"
msgstr "Zwischensumme (exkl. Steuern)"

#: includes/views/booking-view.php:422
msgid "Taxes"
msgstr "Steuern"

#: includes/views/create-booking/checkout-view.php:55
#: includes/views/shortcodes/checkout-view.php:89
msgid "Coupon Code:"
msgstr "Gutscheincode:"

#: includes/views/edit-booking/checkout-view.php:25
msgid "New Booking Details"
msgstr "Neue Buchungsdetails"

#: includes/views/edit-booking/checkout-view.php:43
msgid "Original Booking Details"
msgstr "Original-Buchungsdetails"

#: includes/views/edit-booking/checkout-view.php:154
#: includes/views/shortcodes/checkout-view.php:269
#: template-functions.php:794
#: templates/create-booking/search/search-form.php:111
#: templates/shortcodes/search/search-form.php:105
msgid "Children %s"
msgstr "Kinder %s"

#: includes/views/edit-booking/checkout-view.php:232
#: templates/emails/reserved-room-details.php:30
msgid "Additional Services"
msgstr "Zusatzleistungen"

#: includes/views/edit-booking/checkout-view.php:249
#: includes/views/reserved-room-view.php:26
#: template-functions.php:937
msgid "x %d guest"
msgid_plural "x %d guests"
msgstr[0] "x %d Gast"
msgstr[1] "x %d Gäste"

#: includes/views/edit-booking/checkout-view.php:253
#: includes/views/reserved-room-view.php:31
#: template-functions.php:940
msgid "x %d time"
msgid_plural "x %d times"
msgstr[0] "x %d Mal"
msgstr[1] "x %d Mal"

#: includes/views/global-view.php:53
msgid "Accommodation pagination"
msgstr "Paginierung von Unterkunft"

#: includes/views/global-view.php:56
msgid "Services pagination"
msgstr "Paginierung von Dienstleistungen"

#: includes/views/loop-room-type-view.php:55
#: includes/views/single-room-type-view.php:147
#: templates/widgets/rooms/room-content.php:99
msgid "Categories:"
msgstr "Kategorien:"

#: includes/views/loop-room-type-view.php:67
#: includes/views/single-room-type-view.php:159
#: templates/widgets/rooms/room-content.php:129
msgid "Amenities:"
msgstr "Ausstattungen:"

#: includes/views/loop-room-type-view.php:97
#: includes/views/loop-room-type-view.php:115
#: includes/views/single-room-type-view.php:189
#: includes/views/single-room-type-view.php:207
#: templates/widgets/rooms/room-content.php:67
#: templates/widgets/rooms/room-content.php:79
#: templates/widgets/search-availability/search-form.php:80
msgid "Guests:"
msgstr "Gäste:"

#: includes/views/loop-room-type-view.php:140
#: includes/views/single-room-type-view.php:232
#: templates/widgets/rooms/room-content.php:175
msgid "Bed Type:"
msgstr "Bettentyp:"

#: includes/views/loop-room-type-view.php:164
#: includes/views/single-room-type-view.php:256
#: templates/widgets/rooms/room-content.php:159
msgid "View:"
msgstr "Ansicht:"

#: includes/views/loop-room-type-view.php:184
#: includes/views/single-room-type-view.php:276
#: template-functions.php:839
#: templates/widgets/rooms/room-content.php:227
msgid "Prices start at:"
msgstr "Preis von:"

#: includes/views/loop-service-view.php:46
msgid "Price:"
msgstr "Preis:"

#: includes/views/shortcodes/checkout-view.php:49
msgid "Returning customer?"
msgstr "Wiederkehrender Kunde?"

#: includes/views/shortcodes/checkout-view.php:50
msgid "Click here to log in"
msgstr "Zur Anmeldung hier klicken"

#. translators: 1 - username;
#: includes/views/shortcodes/checkout-view.php:70
#: templates/account/dashboard.php:29
msgid "Hello %1$s (not %1$s? <a href=\"%2$s\">Log out</a>)."
msgstr "Hallo %1$s (nicht %1$s? <a href=\"%2$s\">Abmelden</a>)."

#: includes/views/shortcodes/checkout-view.php:182
msgid "Accommodation #%d"
msgstr "Unterkunft #%d"

#: includes/views/shortcodes/checkout-view.php:186
msgid "Accommodation Type:"
msgstr "Unterkunftsart:"

#: includes/views/shortcodes/checkout-view.php:324
msgid "Choose Rate"
msgstr "Rate wählen"

#: includes/views/shortcodes/checkout-view.php:397
msgid "Choose Additional Services"
msgstr "Wählen Sie Zusatzleistungen"

#: includes/views/shortcodes/checkout-view.php:429
msgid "for "
msgstr "für "

#: includes/views/shortcodes/checkout-view.php:442
msgctxt "Example: Breakfast for X guest(s)"
msgid " guest(s)"
msgstr " Gast(Gäste)"

#: includes/views/shortcodes/checkout-view.php:464
msgid "time(s)"
msgstr "Mal"

#: includes/views/shortcodes/checkout-view.php:533
msgctxt "I've read and accept the terms & conditions"
msgid "terms & conditions"
msgstr "Allgemeine Geschäftsbedingungen"

#: includes/views/shortcodes/checkout-view.php:536
msgctxt "I've read and accept the <tag>terms & conditions</tag>"
msgid "I've read and accept the %s"
msgstr "Ich habe die %s gelesen und akzeptiere diese"

#: includes/views/shortcodes/checkout-view.php:579
msgid "Your Information"
msgstr "Ihre Informationen"

#: includes/views/shortcodes/checkout-view.php:582
#: template-functions.php:696
#: templates/widgets/search-availability/search-form.php:24
msgid "Required fields are followed by %s"
msgstr "Erforderliche Felder werden gefolgt von %s"

#: includes/views/shortcodes/checkout-view.php:758
msgid "Create an account"
msgstr "Ein Konto erstellen"

#: includes/views/shortcodes/checkout-view.php:775
msgid "Payment Method"
msgstr "Zahlungsmethode"

#: includes/views/shortcodes/checkout-view.php:780
msgid "Sorry, it seems that there are no available payment methods."
msgstr "Tut uns leid, es scheint, dass es keine verfügbaren Zahlungsmethoden gibt."

#: includes/views/shortcodes/checkout-view.php:874
#: templates/emails/admin-customer-cancelled-booking.php:32
#: templates/emails/admin-customer-confirmed-booking.php:32
#: templates/emails/admin-payment-confirmed-booking.php:39
#: templates/emails/admin-pending-booking.php:32
#: templates/emails/customer-approved-booking.php:28
#: templates/emails/customer-cancelled-booking.php:26
#: templates/emails/customer-confirmation-booking.php:32
#: templates/emails/customer-pending-booking.php:30
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:27
msgid "Total Price:"
msgstr "Gesamtsumme:"

#: includes/views/shortcodes/checkout-view.php:886
msgid "Deposit:"
msgstr "Anzahlung:"

#: includes/views/shortcodes/checkout-view.php:906
#: templates/shortcodes/booking-details/booking-details.php:25
#: templates/widgets/search-availability/search-form.php:35
msgid "Check-in:"
msgstr "Anreise:"

#: includes/views/shortcodes/checkout-view.php:913
msgctxt "from 10:00 am"
msgid "from"
msgstr "von"

#: includes/views/shortcodes/checkout-view.php:929
#: templates/shortcodes/booking-details/booking-details.php:29
#: templates/widgets/search-availability/search-form.php:54
msgid "Check-out:"
msgstr "Abreise:"

#: includes/views/shortcodes/checkout-view.php:936
msgctxt "until 10:00 am"
msgid "until"
msgstr "bis"

#: includes/views/shortcodes/checkout-view.php:1012
#: templates/create-booking/checkout/checkout-form.php:42
msgid "Book Now"
msgstr "Jetzt buchen"

#: includes/views/single-room-type-view.php:127
msgid "Availability"
msgstr "Verfügbarkeit"

#: includes/views/single-room-type-view.php:292
msgid "Reservation Form"
msgstr "Buchungsformular"

#: includes/widgets/rooms-widget.php:24
msgid "Display Accommodation Types"
msgstr "Alle Unterkunftsarten anzeigen"

#: includes/widgets/rooms-widget.php:169
#: includes/widgets/search-availability-widget.php:236
msgid "Title:"
msgstr "Titel:"

#: includes/widgets/rooms-widget.php:189
#: assets/blocks/blocks.js:448
#: assets/blocks/blocks.js:698
#: assets/blocks/blocks.js:1227
msgid "Featured Image"
msgstr "Beitragsbild"

#: includes/widgets/rooms-widget.php:193
#: assets/blocks/blocks.js:472
#: assets/blocks/blocks.js:722
#: assets/blocks/blocks.js:1251
msgid "Excerpt (short description)"
msgstr "Beitragsauszug (Kurzbeschreibung)"

#: includes/widgets/rooms-widget.php:205
#: assets/blocks/blocks.js:770
#: assets/blocks/blocks.js:1299
msgid "Book Button"
msgstr "Buchungsbutton"

#: includes/widgets/search-availability-widget.php:50
#: includes/wizard.php:84
msgid "Search Availability"
msgstr "Verfügbarkeitsuche"

#: includes/widgets/search-availability-widget.php:53
msgid "Search Availability Form"
msgstr "Verfügbarkeitssuche-Formular"

#: includes/widgets/search-availability-widget.php:240
msgid "Check-in Date:"
msgstr "Anreisedatum:"

#: includes/widgets/search-availability-widget.php:241
#: includes/widgets/search-availability-widget.php:246
msgctxt "Date format tip"
msgid "Preset date. Formatted as %s"
msgstr "Datumsvoreinstellung. Formatiert als %s"

#: includes/widgets/search-availability-widget.php:244
msgid "Check-out Date:"
msgstr "Abreisedatum:"

#: includes/widgets/search-availability-widget.php:249
msgid "Preset Adults:"
msgstr "Voreingestellte Parameter für Erwachsene:"

#: includes/widgets/search-availability-widget.php:257
msgid "Preset Children:"
msgstr "Voreingestellte Parameter für Kinder:"

#: includes/widgets/search-availability-widget.php:265
msgid "Attributes:"
msgstr "Attribute:"

#: includes/wizard.php:34
msgid "Booking Confirmation and Search Results pages are required to handle bookings. Press \"Install Pages\" button to create and set up these pages. Dismiss this notice if you already installed them."
msgstr "Checkout- und Suchergebnisseiten sind erforderlich, um Buchungen zu bearbeiten. Klicken Sie auf die Schaltfläche \"Seiten installieren\", um diese Seiten zu erstellen und einzurichten. Ignorieren Sie diese Benachrichtigung, wenn Sie sie bereits installiert haben."

#: includes/wizard.php:35
msgid "Install Pages"
msgstr "Seiten installieren"

#: includes/wizard.php:147
msgid "Booking Canceled"
msgstr "Buchung storniert"

#: includes/wizard.php:148
msgid "Your reservation is canceled."
msgstr "Ihre Reservierung wurde storniert."

#: includes/wizard.php:183
msgid "Reservation Received"
msgstr "Reservierung erhalten"

#: includes/wizard.php:196
msgid "Transaction Failed"
msgstr "Transaktion fehlgeschlagen"

#: includes/wizard.php:197
msgid "Unfortunately, your transaction cannot be completed at this time. Please try again or contact us."
msgstr "Leider kann Ihre Transaktion derzeit nicht abgeschlossen werden. Bitte versuchen Sie es noch einmal oder kontaktieren Sie uns direkt."

#: plugin.php:1100
msgid "Prices start at: %s"
msgstr "Preis von: %s"

#: template-functions.php:563
msgid "View Details"
msgstr "Details ansehen"

#: template-functions.php:593
#: template-functions.php:652
msgid "Accommodation %s not found."
msgstr "Unterkunft %s nicht gefunden."

#: template-functions.php:707
#: template-functions.php:716
#: templates/create-booking/search/search-form.php:43
#: templates/create-booking/search/search-form.php:63
#: templates/edit-booking/edit-dates.php:30
#: templates/edit-booking/edit-dates.php:39
#: templates/shortcodes/search/search-form.php:36
#: templates/shortcodes/search/search-form.php:56
#: templates/widgets/search-availability/search-form.php:36
#: templates/widgets/search-availability/search-form.php:55
msgctxt "Date format tip"
msgid "Formatted as %s"
msgstr "Formatiert als %s"

#: template-functions.php:831
msgid "Reserve %1$s of %2$s available accommodations."
msgstr "%1$s of %2$s verfügbare Unterkünfte reservieren."

#: template-functions.php:835
msgid "%s is available for selected dates."
msgstr "%s ist für ausgewählte Daten verfügbar."

#: template-functions.php:849
#: templates/edit-booking/edit-dates.php:46
msgid "Check Availability"
msgstr "Verfügbarkeit prüfen"

#: template-functions.php:910
msgid "Rate:"
msgstr "Rate:"

#: template-functions.php:930
msgid "Services:"
msgstr "Dienstleistungen:"

#: template-functions.php:953
msgid "Guest:"
msgstr "Gäste:"

#: template-functions.php:976
msgid "Payment ID"
msgstr "Zahlung-ID"

#: template-functions.php:1008
msgid "Total Paid"
msgstr "Bezahlte Gesamtsumme"

#: template-functions.php:1017
msgid "To Pay"
msgstr "Bezahlen"

#: template-functions.php:1042
msgid "Add Payment Manually"
msgstr "Zahlung manuell hinzufügen"

#: templates/account/account-details.php:78
msgid "Change Password"
msgstr "Passwort ändern"

#: templates/account/account-details.php:81
msgid "Old Password"
msgstr "Altes Passwort"

#: templates/account/account-details.php:85
msgid "New Password"
msgstr "Neues Passwort"

#: templates/account/account-details.php:89
msgid "Confirm New Password"
msgstr "Neues Passwort bestätigen"

#: templates/account/account-details.php:99
msgid "You are not allowed to access this page."
msgstr "Sie sind nicht berechtigt, auf diese Seite zuzugreifen."

#: templates/account/bookings.php:116
#: templates/account/bookings.php:121
msgid "No bookings found."
msgstr "Keine Buchungen gefunden."

#: templates/account/dashboard.php:41
msgid "From your account dashboard you can view <a href=\"%1$s\">your recent bookings</a> or edit your <a href=\"%2$s\">password and account details</a>."
msgstr "Sie können in Ihrem Konto-Dashboard <a href=\"%1$s\">Ihre letzten Buchungen</a> anzeigen oder Ihre <a href=\"%2$s\">Passwort- und Kontodetails</a> bearbeiten."

#: templates/create-booking/results/reserve-rooms.php:37
msgid "Base price"
msgstr "Basispreis"

#: templates/create-booking/results/rooms-found.php:19
#: templates/shortcodes/search-results/results-info.php:17
msgid "%s accommodation found"
msgid_plural "%s accommodations found"
msgstr[0] "%s Unterkunft gefunden"
msgstr[1] "%s Unterkünfte gefunden"

#: templates/create-booking/results/rooms-found.php:24
#: templates/shortcodes/search-results/results-info.php:21
msgid " from %s - till %s"
msgstr " von %s - bis %s"

#: templates/edit-booking/add-room-popup.php:24
#: templates/edit-booking/edit-reserved-rooms.php:36
msgid "Add Accommodation"
msgstr "Unterkunft hinzufügen"

#: templates/edit-booking/checkout-form.php:28
msgid "Save"
msgstr "Speichern"

#: templates/edit-booking/edit-dates.php:25
msgid "Choose new dates to check availability of reserved accommodations in the original booking."
msgstr "Wählen Sie neue Daten aus, um die Verfügbarkeit der reservierten Unterkünfte in der Originalbuchung zu überprüfen."

#: templates/edit-booking/edit-reserved-rooms.php:39
msgid "Add, remove or replace accommodations in the original booking."
msgstr "Hinzufügen, Entfernen oder Ersetzen von Unterkünften in der ursprünglichen Buchung."

#: templates/edit-booking/edit-reserved-rooms.php:67
msgid "Not Available"
msgstr "Nicht verfügbar"

#: templates/edit-booking/edit-reserved-rooms.php:79
#: templates/edit-booking/summary-table.php:65
msgid "Continue"
msgstr "Weiter"

#: templates/edit-booking/summary-table.php:26
msgid "Choose how to associate data"
msgstr "Wählen Sie aus, wie die Daten verknüpft werden"

#: templates/edit-booking/summary-table.php:27
msgid "Use Source Accommodation to assign pre-filled booking information available in the original booking, e.g., full guest name, selected rate, services, etc."
msgstr "Verwenden Sie die Funktion Quell-Unterkunft, um vorausgefüllte Buchungsinformationen zuzuordnen, die in der ursprünglichen Buchung verfügbar waren, z. B. vollständiger Gastname, ausgewählter Tarif, Dienstleistungen usw."

#: templates/edit-booking/summary-table.php:32
msgid "Source accommodation"
msgstr "Quellunterkunft"

#: templates/edit-booking/summary-table.php:34
msgid "Target accommodation"
msgstr "Zielunterbringung"

#: templates/emails/admin-customer-cancelled-booking.php:15
msgid "Booking #%s is cancelled by customer."
msgstr "Buchung #%s wurde vom Kunden storniert."

#: templates/emails/admin-customer-cancelled-booking.php:17
#: templates/emails/admin-customer-confirmed-booking.php:17
#: templates/emails/admin-payment-confirmed-booking.php:24
#: templates/emails/admin-pending-booking.php:17
#: templates/emails/customer-approved-booking.php:17
#: templates/emails/customer-cancelled-booking.php:18
#: templates/emails/customer-confirmation-booking.php:24
#: templates/emails/customer-pending-booking.php:19
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:19
msgid "Details of booking"
msgstr "Buchungsdetails"

#: templates/emails/admin-customer-cancelled-booking.php:18
#: templates/emails/admin-customer-confirmed-booking.php:18
#: templates/emails/admin-payment-confirmed-booking.php:25
#: templates/emails/admin-pending-booking.php:18
#: templates/emails/customer-approved-booking.php:20
#: templates/emails/customer-cancelled-booking.php:21
#: templates/emails/customer-confirmation-booking.php:27
#: templates/emails/customer-pending-booking.php:22
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:22
msgid "Check-in: %1$s, from %2$s"
msgstr "Anreise: %1$s, ab %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:20
#: templates/emails/admin-customer-confirmed-booking.php:20
#: templates/emails/admin-payment-confirmed-booking.php:27
#: templates/emails/admin-pending-booking.php:20
#: templates/emails/customer-approved-booking.php:22
#: templates/emails/customer-cancelled-booking.php:23
#: templates/emails/customer-confirmation-booking.php:29
#: templates/emails/customer-pending-booking.php:24
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:24
msgid "Check-out: %1$s, until %2$s"
msgstr "Abreise: %1$s, bis %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:24
#: templates/emails/admin-customer-confirmed-booking.php:24
#: templates/emails/admin-payment-confirmed-booking.php:31
#: templates/emails/admin-pending-booking.php:24
#: templates/emails/customer-approved-booking.php:32
#: templates/emails/customer-cancelled-booking.php:30
#: templates/emails/customer-confirmation-booking.php:36
#: templates/emails/customer-pending-booking.php:33
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:31
msgid "Name: %1$s %2$s"
msgstr "Name: %1$s %2$s"

#: templates/emails/admin-customer-cancelled-booking.php:26
#: templates/emails/admin-customer-confirmed-booking.php:26
#: templates/emails/admin-payment-confirmed-booking.php:33
#: templates/emails/admin-pending-booking.php:26
#: templates/emails/customer-approved-booking.php:34
#: templates/emails/customer-cancelled-booking.php:32
#: templates/emails/customer-confirmation-booking.php:38
#: templates/emails/customer-pending-booking.php:35
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:33
msgid "Email: %s"
msgstr "E-Mail: %s"

#: templates/emails/admin-customer-cancelled-booking.php:28
#: templates/emails/admin-customer-confirmed-booking.php:28
#: templates/emails/admin-payment-confirmed-booking.php:35
#: templates/emails/admin-pending-booking.php:28
#: templates/emails/customer-approved-booking.php:36
#: templates/emails/customer-cancelled-booking.php:34
#: templates/emails/customer-confirmation-booking.php:40
#: templates/emails/customer-pending-booking.php:37
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:35
msgid "Phone: %s"
msgstr "Telefon: %s"

#: templates/emails/admin-customer-cancelled-booking.php:30
#: templates/emails/admin-customer-confirmed-booking.php:30
#: templates/emails/admin-payment-confirmed-booking.php:37
#: templates/emails/admin-pending-booking.php:30
#: templates/emails/customer-approved-booking.php:38
#: templates/emails/customer-cancelled-booking.php:36
#: templates/emails/customer-confirmation-booking.php:42
#: templates/emails/customer-pending-booking.php:39
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:37
msgid "Note: %s"
msgstr "Notizen: %s"

#: templates/emails/admin-customer-confirmed-booking.php:15
msgid "Booking #%s is confirmed by customer."
msgstr "Buchung #%s wurde vom Kunden bestätigt."

#: templates/emails/admin-payment-confirmed-booking.php:15
msgid "Booking #%s is confirmed by payment."
msgstr "Buchung #%s wurde durch Zahlung bestätigt."

#: templates/emails/admin-payment-confirmed-booking.php:17
msgid "Details of payment"
msgstr "Zahlungsdetails"

#: templates/emails/admin-payment-confirmed-booking.php:18
msgid "Payment ID: #%s"
msgstr "Zahlung-ID: #%s"

#: templates/emails/admin-payment-confirmed-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:16
msgid "Amount: %s"
msgstr "Betrag: %s"

#: templates/emails/admin-payment-confirmed-booking.php:22
msgid "Method: %s"
msgstr "Methode: %s"

#: templates/emails/admin-pending-booking.php:15
msgid "Booking #%s is pending for Administrator approval."
msgstr "Buchung #%s wartet noch auf die Genehmigung des Administrators."

#: templates/emails/cancellation-details.php:14
msgid "Click the link below to cancel your booking."
msgstr "Klicken Sie auf den untenstehenden Link, um Ihre Buchung zu stornieren."

#: templates/emails/cancellation-details.php:16
msgid "Cancel your booking"
msgstr "Stornieren Sie Ihre Buchung"

#: templates/emails/customer-approved-booking.php:15
msgid "Dear %1$s %2$s, your reservation is approved!"
msgstr "Sehr geehrte(r) Frau/Herr %1$s %2$s, Ihre Reservierung wurde akzeptiert!"

#: templates/emails/customer-approved-booking.php:18
#: templates/emails/customer-cancelled-booking.php:19
#: templates/emails/customer-confirmation-booking.php:25
#: templates/emails/customer-pending-booking.php:20
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:20
msgid "ID: #%s"
msgstr "ID: #%s"

#: templates/emails/customer-approved-booking.php:41
#: templates/emails/customer-cancelled-booking.php:38
#: templates/emails/customer-confirmation-booking.php:44
#: templates/emails/customer-pending-booking.php:41
#: templates/emails/customer-registration.php:26
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:39
msgid "Thank you!"
msgstr "Vielen Dank!"

#: templates/emails/customer-cancelled-booking.php:15
msgid "Dear %1$s %2$s, your reservation is cancelled!"
msgstr "Sehr geehrte(r) Frau/Herr %1$s %2$s, Ihre Reservierung wurde storniert!"

#: templates/emails/customer-confirmation-booking.php:14
#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:12
msgid "Dear %1$s %2$s, we received your request for reservation."
msgstr "Sehr geehrte(r) Frau/Herr %1$s %2$s, wir erhielten Ihre Reservierungsanfrage."

#: templates/emails/customer-confirmation-booking.php:16
msgid "Click the link below to confirm your booking."
msgstr "Klicken Sie auf den Link unten, um Ihre Buchung zu bestätigen."

#: templates/emails/customer-confirmation-booking.php:18
msgid "Confirm"
msgstr "Bestätigen"

#: templates/emails/customer-confirmation-booking.php:20
msgid "Note: link expires on"
msgstr "Hinweis: Der Link wird inaktiv am"

#: templates/emails/customer-confirmation-booking.php:20
msgid "UTC"
msgstr "UTC"

#: templates/emails/customer-confirmation-booking.php:22
msgid "If you did not place this booking, please ignore this email."
msgstr "Wenn Sie diese Buchung nicht getätigt haben, ignorieren Sie bitte diese E-Mail."

#: templates/emails/customer-pending-booking.php:15
msgid "Dear %1$s %2$s, your reservation is pending."
msgstr "Sehr geehrte(r) Frau/Herr %1$s %2$s, Ihre Reservierung ist noch ausstehend."

#: templates/emails/customer-pending-booking.php:17
msgid "We will notify you by email once it is confirmed by our staff."
msgstr "Wir werden Sie per E-Mail benachrichtigen, sobald es von unseren Mitarbeitern bestätigt wird."

#: templates/emails/customer-registration.php:15
msgid "Hi %1$s %2$s,"
msgstr "Hallo %1$s %2$s,"

#: templates/emails/customer-registration.php:17
msgid "Thanks for creating an account on %1$s."
msgstr "Vielen Dank, dass Sie ein Konto bei %1$s erstellt haben."

#: templates/emails/customer-registration.php:19
msgid "You Account Details"
msgstr "Ihre Kontodetails"

#: templates/emails/customer-registration.php:20
msgid "Login: %s"
msgstr "Benutzername: %s"

#: templates/emails/customer-registration.php:21
msgid "Password: %s"
msgstr "Password: %s"

#: templates/emails/customer-registration.php:22
msgid "Log in here: %s"
msgstr "Hier anmelden: %s"

#: templates/emails/payment-gateways/direct-bank-transfer/customer-pending-booking.php:14
msgid "To confirm your booking, please follow the instructions below for payment."
msgstr "Um Ihre Buchung zu bestätigen, folgen Sie bitte den Anweisungen unten zur Zahlung."

#: templates/emails/reserved-room-details.php:14
msgid "Accommodation #%s"
msgstr "Unterkunft #%s"

#: templates/emails/reserved-room-details.php:21
msgid "Accommodation: <a href=\"%1$s\">%2$s</a>"
msgstr "Unterkunft: <a href=\"%1$s\">%2$s</a>"

#: templates/emails/reserved-room-details.php:24
msgid "Accommodation Rate: %s"
msgstr "Übernachtungspreis: %s"

#: templates/emails/reserved-room-details.php:28
msgid "Bed Type: %s"
msgstr "Bettentyp: %s"

#: templates/required-fields-tip.php:8
msgid "Required fields are followed by"
msgstr "Erforderliche Felder werden gefolgt von"

#: templates/shortcodes/booking-cancellation/already-cancelled.php:7
msgid "Booking is already canceled."
msgstr "Ihre Buchung wurde bereits storniert."

#: templates/shortcodes/booking-cancellation/booking-cancellation-button.php:15
msgid "Cancel Booking"
msgstr "Buchung stornieren"

#: templates/shortcodes/booking-cancellation/invalid-request.php:7
#: templates/shortcodes/booking-confirmation/invalid-request.php:7
msgid "Invalid request."
msgstr "Ungültige Anfrage."

#: templates/shortcodes/booking-cancellation/not-possible.php:7
msgid "Cancelation of your booking is not possible for some reason. Please contact the website administrator."
msgstr "Es ist nicht möglich, Ihre Buchung zu stornieren. Wenden Sie sich an den Administrator der Website."

#: templates/shortcodes/booking-confirmation/already-confirmed.php:7
msgid "Booking is already confirmed."
msgstr "Ihre Buchung ist bereits bestätigt."

#: templates/shortcodes/booking-confirmation/confirmed.php:7
msgid "Your booking is confirmed. Thank You!"
msgstr "Ihre Buchung ist bestätigt. Danke!"

#: templates/shortcodes/booking-confirmation/expired.php:7
msgid "Your booking request is expired. Please start a new booking request."
msgstr "Ihre Buchungsanfrage ist abgelaufen. Bitte starten Sie eine neue Buchungsanfrage."

#: templates/shortcodes/booking-confirmation/not-possible.php:7
msgid "Confirmation of your booking request is not possible for some reason. Please start a new booking request."
msgstr "Die Bestätigung Ihrer Buchungsanfrage ist aus irgendeinem Grund nicht möglich. Bitte starten Sie eine neue Buchungsanfrage."

#: templates/shortcodes/booking-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received and confirmed."
msgstr "Wir freuen uns, Ihnen mitteilen zu können, dass ihre Reservierungsanfrage erfolgreich bestätigt wurde."

#: templates/shortcodes/booking-details/booking-details.php:21
msgid "Booking:"
msgstr "Buchung:"

#: templates/shortcodes/booking-details/booking-details.php:47
msgid "Details:"
msgstr "Details:"

#: templates/shortcodes/payment-confirmation/completed.php:11
msgid "Thank you for your payment. Your transaction has been completed."
msgstr "Danke für Ihre Bezahlung. Ihre Transaktion wurde abgeschlossen."

#: templates/shortcodes/payment-confirmation/received.php:11
msgid "We are pleased to inform you that your reservation request has been received."
msgstr "Wir freuen uns Ihnen mitteilen zu dürfen, dass Ihre Reservierung zugestellt wurde."

#: templates/shortcodes/room-rates/rate-content.php:17
msgid "from %s"
msgstr "von %s"

#: templates/shortcodes/rooms/not-found.php:7
msgid "No accommodations matching criteria."
msgstr "Keine Unterkünfte entsprechen Ihren Kriterien."

#: templates/shortcodes/services/not-found.php:7
msgid "No services matched criteria."
msgstr "Keine Dienstleistung gefunden."

#: templates/widgets/rooms/not-found.php:6
msgid "Nothing found."
msgstr "Nichts gefunden."

#: templates/widgets/search-availability/search-form.php:105
msgid "Children %s:"
msgstr "Kinder %s:"

#: assets/blocks/blocks.js:178
#: assets/blocks/blocks.js:190
msgid "Preset date. Formatted as %s"
msgstr "Datumsvoreinstellung. Formatiert als %s"

#: assets/blocks/blocks.js:283
#: assets/blocks/blocks.js:1425
#: assets/blocks/blocks.js:1507
msgid "Select an accommodation type. Leave blank to use current."
msgstr "Wählen Sie einen Unterkunftstyp. Leer lassen, um aktuelle zu verwenden."

#: assets/blocks/blocks.js:284
#: assets/blocks/blocks.js:1426
#: assets/blocks/blocks.js:1508
msgid "ID of an accommodation type. Leave blank to use current."
msgstr "ID eines Unterkunftstyps. Leer lassen, um aktuell zu verwenden."

#: assets/blocks/blocks.js:460
#: assets/blocks/blocks.js:710
#: assets/blocks/blocks.js:1239
msgid "Gallery"
msgstr "Galerie"

#: assets/blocks/blocks.js:508
#: assets/blocks/blocks.js:758
#: assets/blocks/blocks.js:1287
msgid "View Button"
msgstr "\"Ansehen\"-Button"

#: assets/blocks/blocks.js:522
#: assets/blocks/blocks.js:558
#: assets/blocks/blocks.js:858
#: assets/blocks/blocks.js:894
#: assets/blocks/blocks.js:1042
#: assets/blocks/blocks.js:1078
msgid "Order"
msgstr "Reihenfolge"

#: assets/blocks/blocks.js:530
#: assets/blocks/blocks.js:866
#: assets/blocks/blocks.js:1050
msgid "Order By"
msgstr "Sortieren nach"

#: assets/blocks/blocks.js:533
#: assets/blocks/blocks.js:869
#: assets/blocks/blocks.js:1053
msgid "No order"
msgstr "Keine Reihenfolge"

#: assets/blocks/blocks.js:534
#: assets/blocks/blocks.js:870
#: assets/blocks/blocks.js:1054
msgid "Post ID"
msgstr "Beitrags-ID"

#: assets/blocks/blocks.js:535
#: assets/blocks/blocks.js:871
#: assets/blocks/blocks.js:1055
msgid "Post author"
msgstr "Beitragsautor"

#: assets/blocks/blocks.js:536
#: assets/blocks/blocks.js:872
#: assets/blocks/blocks.js:1056
msgid "Post title"
msgstr "Beitragstitel"

#: assets/blocks/blocks.js:537
#: assets/blocks/blocks.js:873
#: assets/blocks/blocks.js:1057
msgid "Post name (post slug)"
msgstr "Beitragsname (Beitrag-Slug)"

#: assets/blocks/blocks.js:538
#: assets/blocks/blocks.js:874
#: assets/blocks/blocks.js:1058
msgid "Post date"
msgstr "Veröffentlicht"

#: assets/blocks/blocks.js:539
#: assets/blocks/blocks.js:875
#: assets/blocks/blocks.js:1059
msgid "Last modified date"
msgstr "Zuletzt geändert"

#: assets/blocks/blocks.js:540
#: assets/blocks/blocks.js:876
#: assets/blocks/blocks.js:1060
msgid "Parent ID"
msgstr "ID der Oberkategorie"

#: assets/blocks/blocks.js:541
#: assets/blocks/blocks.js:877
#: assets/blocks/blocks.js:1061
msgid "Random order"
msgstr "Zufällige Reihenfolge"

#: assets/blocks/blocks.js:542
#: assets/blocks/blocks.js:878
#: assets/blocks/blocks.js:1062
msgid "Number of comments"
msgstr "Anzahl der Kommentare"

#: assets/blocks/blocks.js:543
#: assets/blocks/blocks.js:879
#: assets/blocks/blocks.js:1063
msgid "Relevance"
msgstr "Relevanz"

#: assets/blocks/blocks.js:544
#: assets/blocks/blocks.js:880
#: assets/blocks/blocks.js:1064
msgid "Page order"
msgstr "Seitenreihenfolge"

#: assets/blocks/blocks.js:545
#: assets/blocks/blocks.js:881
#: assets/blocks/blocks.js:1065
msgid "Meta value"
msgstr "Meta-Wert"

#: assets/blocks/blocks.js:546
#: assets/blocks/blocks.js:882
#: assets/blocks/blocks.js:1066
msgid "Numeric meta value"
msgstr "Numerischer Meta-Wert"

#: assets/blocks/blocks.js:561
#: assets/blocks/blocks.js:897
#: assets/blocks/blocks.js:1081
msgid "Ascending (1, 2, 3)"
msgstr "Aufsteigend (1, 2, 3)"

#: assets/blocks/blocks.js:562
#: assets/blocks/blocks.js:898
#: assets/blocks/blocks.js:1082
msgid "Descending (3, 2, 1)"
msgstr "Absteigend (3, 2, 1)"

#: assets/blocks/blocks.js:573
#: assets/blocks/blocks.js:909
#: assets/blocks/blocks.js:1093
msgid "Meta Name"
msgstr "Meta-Name"

#: assets/blocks/blocks.js:585
#: assets/blocks/blocks.js:921
#: assets/blocks/blocks.js:1105
msgid "Meta Type"
msgstr "Meta-Typ"

#: assets/blocks/blocks.js:586
#: assets/blocks/blocks.js:922
#: assets/blocks/blocks.js:1106
msgid "Specified type of the custom field. Can be used in conjunction with \"orderby\" = \"meta_value\"."
msgstr "Angegebener Typ des benutzerdefinierten Felds. Kann in Verbindung mit \"orderby\" = \"meta_value\" verwendet werden."

#: assets/blocks/blocks.js:589
#: assets/blocks/blocks.js:925
#: assets/blocks/blocks.js:1109
msgid "Any"
msgstr "Beliebig"

#: assets/blocks/blocks.js:590
#: assets/blocks/blocks.js:926
#: assets/blocks/blocks.js:1110
msgid "Numeric"
msgstr "Numerisch"

#: assets/blocks/blocks.js:591
#: assets/blocks/blocks.js:927
#: assets/blocks/blocks.js:1111
msgid "Binary"
msgstr "Binär"

#: assets/blocks/blocks.js:592
#: assets/blocks/blocks.js:928
#: assets/blocks/blocks.js:1112
msgid "String"
msgstr "Zeichenkette"

#: assets/blocks/blocks.js:594
#: assets/blocks/blocks.js:930
#: assets/blocks/blocks.js:1114
msgid "Time"
msgstr "Uhrzeit"

#: assets/blocks/blocks.js:595
#: assets/blocks/blocks.js:931
#: assets/blocks/blocks.js:1115
msgid "Date and time"
msgstr "Datum und Uhrzeit"

#: assets/blocks/blocks.js:596
#: assets/blocks/blocks.js:932
#: assets/blocks/blocks.js:1116
msgid "Decimal number"
msgstr "Dezimalzahl"

#: assets/blocks/blocks.js:597
#: assets/blocks/blocks.js:933
#: assets/blocks/blocks.js:1117
msgid "Signed number"
msgstr "Zahl mit Vorzeichen"

#: assets/blocks/blocks.js:598
#: assets/blocks/blocks.js:934
#: assets/blocks/blocks.js:1118
msgid "Unsigned number"
msgstr "Vorzeichenlose Zahl"

#: assets/blocks/blocks.js:784
#: assets/blocks/blocks.js:1009
msgid "Query Settings"
msgstr "Abfrageeinstellungen"

#: assets/blocks/blocks.js:840
msgid "Relation"
msgstr "Beziehung"

#: assets/blocks/blocks.js:1029
msgid "Values: integer, -1 to display all, default: \"Blog pages show at most\""
msgstr "Werte: Ganzzahl; -1 um alle anzuzeigen; Standard: 'Blogseiten zeigen maximal' (Einstellungen\\Lesen)"

#: assets/blocks/blocks.js:1203
msgid "Select an accommodation type."
msgstr "Wählen Sie einen Unterkunftstyp aus."

