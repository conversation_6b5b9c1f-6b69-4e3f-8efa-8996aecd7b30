
2025-07-27 14:36:19 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:19 MPHB_GB: Finished registering hooks
2025-07-27 14:36:19 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:36:19 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:19 MPHB_GB: Finished registering hooks
2025-07-27 14:36:26 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:26 MPHB_GB: Finished registering hooks
2025-07-27 14:36:33 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:33 MPHB_GB: Finished registering hooks
2025-07-27 14:36:34 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:36:34 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:36:34 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:36:34 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:36:34 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:36:34 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:34 MPHB_GB: Finished registering hooks
2025-07-27 14:37:27 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:37:27 MPHB_GB: Finished registering hooks
2025-07-27 14:39:14 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:14 MPHB_GB: Finished registering hooks
2025-07-27 14:39:14 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:39:15 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:15 MPHB_GB: Finished registering hooks
2025-07-27 14:39:27 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:27 MPHB_GB: Finished registering hooks
2025-07-27 14:39:29 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:29 MPHB_GB: Finished registering hooks
2025-07-27 14:39:29 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:39:29 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:39:29 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:39:29 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:39:29 MPHB_GB: Search dates found, modifying availability check
2025-07-27 14:39:29 MPHB_GB: Room type ID: 0
2025-07-27 14:39:29 MPHB_GB: No room type ID specified, checking all rooms
2025-07-27 14:39:30 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:30 MPHB_GB: Finished registering hooks
