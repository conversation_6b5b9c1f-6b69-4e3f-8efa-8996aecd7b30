
2025-07-27 14:36:19 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:19 MPHB_GB: Finished registering hooks
2025-07-27 14:36:19 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:36:19 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:19 MPHB_GB: Finished registering hooks
2025-07-27 14:36:26 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:26 MPHB_GB: Finished registering hooks
2025-07-27 14:36:33 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:33 MPHB_GB: Finished registering hooks
2025-07-27 14:36:34 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:36:34 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:36:34 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:36:34 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:36:34 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:36:34 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:36:34 MPHB_GB: Finished registering hooks
2025-07-27 14:37:27 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:37:27 MPHB_GB: Finished registering hooks
2025-07-27 14:39:14 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:14 MPHB_GB: Finished registering hooks
2025-07-27 14:39:14 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:39:15 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:15 MPHB_GB: Finished registering hooks
2025-07-27 14:39:27 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:27 MPHB_GB: Finished registering hooks
2025-07-27 14:39:29 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:29 MPHB_GB: Finished registering hooks
2025-07-27 14:39:29 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:39:29 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:39:29 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:39:29 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:39:29 MPHB_GB: Search dates found, modifying availability check
2025-07-27 14:39:29 MPHB_GB: Room type ID: 0
2025-07-27 14:39:29 MPHB_GB: No room type ID specified, checking all rooms
2025-07-27 14:39:30 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:39:30 MPHB_GB: Finished registering hooks
2025-07-27 14:41:27 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:41:27 MPHB_GB: Finished registering hooks
2025-07-27 14:41:41 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:41:41 MPHB_GB: Finished registering hooks
2025-07-27 14:41:41 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:41:41 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:41:41 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:41:41 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:41:41 MPHB_GB: Search dates found, modifying availability check
2025-07-27 14:41:41 MPHB_GB: Room type ID: 0
2025-07-27 14:41:41 MPHB_GB: No room type ID specified, checking all rooms
2025-07-27 14:41:41 MPHB_GB: Found 3 room types: 15,17,19
2025-07-27 14:41:41 MPHB_GB: Checking 1 rooms for room type 15
2025-07-27 14:41:41 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:41:41 MPHB_GB: Finished registering hooks
2025-07-27 14:41:43 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:41:43 MPHB_GB: Finished registering hooks
2025-07-27 14:41:43 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:41:48 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:41:48 MPHB_GB: Finished registering hooks
2025-07-27 14:41:48 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:41:48 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:41:48 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:41:48 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:41:48 MPHB_GB: Search dates found, modifying availability check
2025-07-27 14:41:48 MPHB_GB: Room type ID: 0
2025-07-27 14:41:48 MPHB_GB: No room type ID specified, checking all rooms
2025-07-27 14:41:48 MPHB_GB: Found 3 room types: 15,17,19
2025-07-27 14:41:48 MPHB_GB: Checking 1 rooms for room type 15
2025-07-27 14:41:48 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:41:48 MPHB_GB: Finished registering hooks
2025-07-27 14:43:27 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:43:27 MPHB_GB: Finished registering hooks
2025-07-27 14:45:27 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:45:27 MPHB_GB: Finished registering hooks
2025-07-27 14:46:20 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:46:20 MPHB_GB: Finished registering hooks
2025-07-27 14:46:20 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:46:21 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:46:21 MPHB_GB: Finished registering hooks
2025-07-27 14:46:22 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:46:22 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:46:22 MPHB_GB: Finished registering hooks
2025-07-27 14:46:24 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:46:24 MPHB_GB: Finished registering hooks
2025-07-27 14:46:25 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:46:25 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:46:25 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:46:25 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:46:25 MPHB_GB: Group code provided: 234kjm234lj, but skipping processing for now
2025-07-27 14:46:25 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:46:25 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:46:25 MPHB_GB: Finished registering hooks
2025-07-27 14:47:17 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:47:17 MPHB_GB: Finished registering hooks
2025-07-27 14:47:17 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:47:18 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:47:18 MPHB_GB: Finished registering hooks
2025-07-27 14:47:22 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:47:22 MPHB_GB: Finished registering hooks
2025-07-27 14:47:22 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:47:22 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:47:22 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:47:22 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:47:22 MPHB_GB: Processing group code: 234kjm234lj
2025-07-27 14:47:22 MPHB_GB: Room type ID: 0
2025-07-27 14:47:22 MPHB_GB: No specific room type ID, skipping group processing
2025-07-27 14:47:22 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:47:22 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:47:22 MPHB_GB: Finished registering hooks
2025-07-27 14:48:15 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:48:15 MPHB_GB: Finished registering hooks
2025-07-27 14:48:15 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:48:16 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:48:16 MPHB_GB: Finished registering hooks
2025-07-27 14:48:51 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:48:51 MPHB_GB: Finished registering hooks
2025-07-27 14:48:51 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:48:52 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:48:52 MPHB_GB: Finished registering hooks
2025-07-27 14:48:57 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:48:57 MPHB_GB: Finished registering hooks
2025-07-27 14:48:57 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:48:57 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:48:57 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:48:57 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:48:57 MPHB_GB: Processing group code: 234kjm234lj
2025-07-27 14:48:57 MPHB_GB: Room type ID: 0
2025-07-27 14:48:57 MPHB_GB: General search across all room types
2025-07-27 14:48:57 MPHB_GB: Found 3 room types: 15,17,19
2025-07-27 14:48:57 MPHB_GB: Checking 1 rooms for room type 15
2025-07-27 14:48:57 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:48:57 MPHB_GB: Finished registering hooks
2025-07-27 14:50:35 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:50:35 MPHB_GB: Finished registering hooks
2025-07-27 14:50:35 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:50:35 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:50:35 MPHB_GB: Finished registering hooks
2025-07-27 14:50:39 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:50:39 MPHB_GB: Finished registering hooks
2025-07-27 14:50:39 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:50:39 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:50:39 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:50:39 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:50:39 MPHB_GB: Processing group code: 234kjm234lj
2025-07-27 14:50:39 MPHB_GB: Room type ID: 0
2025-07-27 14:50:39 MPHB_GB: General search across all room types
2025-07-27 14:50:39 MPHB_GB: Found 3 room types: 15,17,19
2025-07-27 14:50:39 MPHB_GB: Checking 1 rooms for room type 15
2025-07-27 14:52:15 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:52:15 MPHB_GB: Finished registering hooks
2025-07-27 14:52:15 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:52:16 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:52:16 MPHB_GB: Finished registering hooks
2025-07-27 14:52:19 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:52:19 MPHB_GB: Finished registering hooks
2025-07-27 14:52:19 MPHB_GB: add_group_code_to_search_args called
2025-07-27 14:52:19 MPHB_GB: group_code: 234kjm234lj
2025-07-27 14:52:19 MPHB_GB: filter: mphb_search_available_rooms
2025-07-27 14:52:19 MPHB_GB: args: Array
(
    [availability] => locked
    [from_date] => DateTime Object
        (
            [date] => 2025-09-08 11:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [to_date] => DateTime Object
        (
            [date] => 2025-09-10 10:00:00.000000
            [timezone_type] => 1
            [timezone] => +00:00
        )

    [skip_buffer_rules] => 
    [group_code] => 234kjm234lj
    [include_rooms] => Array
        (
            [0] => 0
        )

)

2025-07-27 14:52:19 MPHB_GB: Processing group code: 234kjm234lj
2025-07-27 14:52:19 MPHB_GB: Room type ID: 0
2025-07-27 14:52:19 MPHB_GB: General search across all room types
2025-07-27 14:52:19 MPHB_GB: Found 3 room types: 15,17,19
2025-07-27 14:52:19 MPHB_GB: Checking 1 rooms for room type 15
2025-07-27 14:52:19 MPHB_GB: Checking 1 rooms for room type 17
2025-07-27 14:52:19 MPHB_GB: Checking 1 rooms for room type 19
2025-07-27 14:52:19 MPHB_GB: Found 3 total available rooms for group: 16,18,20
2025-07-27 14:52:19 MPHB_GB: Excluded 3 available rooms from locked search
2025-07-27 14:52:20 MPHB_GB: test_hook_working called - plugin is active
2025-07-27 14:54:05 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:54:05 MPHB_GB: Finished registering hooks
2025-07-27 14:55:05 MPHB_GB: Registering hooks in define_rule_hooks
2025-07-27 14:55:05 MPHB_GB: Finished registering hooks
